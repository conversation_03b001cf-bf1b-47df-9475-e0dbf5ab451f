(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[8554],{65573:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){"use strict";eval(`/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(29905);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(85893);


var FallbackComponent = function FallbackComponent() {
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .ZP, {
    status: "403",
    title: "Kh\\xF4ng th\\u1EC3 truy c\\u1EADp",
    subTitle: "Xin l\\u1ED7i, b\\u1EA1n kh\\xF4ng c\\xF3 quy\\u1EC1n xem n\\u1ED9i dung n\\xE0y."
    //   extra={
    //     <Button type="primary" href="/">
    //       Back Home
    //     </Button>
    //   }
  });
};
/* harmony default export */ __webpack_exports__.Z = (FallbackComponent);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNjU1NzMuanMiLCJtYXBwaW5ncyI6Ijs7QUFBOEI7QUFBQTtBQUU5QixJQUFNRyxpQkFBaUIsR0FBRyxTQUFwQkEsaUJBQWlCQSxDQUFBLEVBQVM7RUFDOUIsb0JBQ0VELHNEQUFBLENBQUNGLHNEQUFNO0lBQ0xJLE1BQU0sRUFBQyxLQUFLO0lBQ1pDLEtBQUssRUFBQyxpQ0FBb0I7SUFDMUJDLFFBQVEsRUFBQztJQUNUO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7RUFBQSxDQUNELENBQUM7QUFFTixDQUFDO0FBRUQsc0RBQWVILGlCQUFpQiIsInNvdXJjZXMiOlsid2VicGFjazovL2FudC1kZXNpZ24tcHJvLy4vc3JjL2NvbXBvbmVudHMvRmFsbGJhY2tDb250ZW50L2luZGV4LnRzeD9mZGRhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFJlc3VsdCB9IGZyb20gJ2FudGQnO1xyXG5cclxuY29uc3QgRmFsbGJhY2tDb21wb25lbnQgPSAoKSA9PiB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxSZXN1bHRcclxuICAgICAgc3RhdHVzPVwiNDAzXCJcclxuICAgICAgdGl0bGU9XCJLaMO0bmcgdGjhu4MgdHJ1eSBj4bqtcFwiXHJcbiAgICAgIHN1YlRpdGxlPVwiWGluIGzhu5dpLCBi4bqhbiBraMO0bmcgY8OzIHF1eeG7gW4geGVtIG7hu5lpIGR1bmcgbsOgeS5cIlxyXG4gICAgICAvLyAgIGV4dHJhPXtcclxuICAgICAgLy8gICAgIDxCdXR0b24gdHlwZT1cInByaW1hcnlcIiBocmVmPVwiL1wiPlxyXG4gICAgICAvLyAgICAgICBCYWNrIEhvbWVcclxuICAgICAgLy8gICAgIDwvQnV0dG9uPlxyXG4gICAgICAvLyAgIH1cclxuICAgIC8+XHJcbiAgKTtcclxufTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IEZhbGxiYWNrQ29tcG9uZW50O1xyXG4iXSwibmFtZXMiOlsiUmVzdWx0IiwianN4IiwiX2pzeCIsIkZhbGxiYWNrQ29tcG9uZW50Iiwic3RhdHVzIiwidGl0bGUiLCJzdWJUaXRsZSJdLCJzb3VyY2VSb290IjoiIn0=
//# sourceURL=webpack-internal:///65573
`)},24414:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){"use strict";eval(`/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(5574);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _services_customerUser__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(40063);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(51042);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(7837);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(98138);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(25514);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(14726);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(85576);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(45360);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(71230);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(15746);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(96365);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(84567);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(67294);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(85893);











var Item = antd__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Z.Item;
var Title = antd__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .Z.Title;
var CreateRoleForm = function CreateRoleForm(params) {
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false),
    _useState2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2___default()(_useState, 2),
    loading = _useState2[0],
    setLoading = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false),
    _useState4 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2___default()(_useState3, 2),
    isOpen = _useState4[0],
    setOpen = _useState4[1];
  var _Form$useForm = antd__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Z.useForm(),
    _Form$useForm2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2___default()(_Form$useForm, 1),
    form = _Form$useForm2[0];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([]),
    _useState6 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2___default()(_useState5, 2),
    sectionCategories = _useState6[0],
    setSectionCategories = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false),
    _useState8 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2___default()(_useState7, 2),
    showFieldLevelPermissions = _useState8[0],
    setShowFieldLevelPermissions = _useState8[1];
  (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(function () {
    fetchData();
  }, []);
  var fetchData = /*#__PURE__*/function () {
    var _ref = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee() {
      var data, sectionArray;
      return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _context.prev = 0;
            _context.next = 3;
            return (0,_services_customerUser__WEBPACK_IMPORTED_MODULE_3__/* .listDynamicRoleAllSection */ .Lf)();
          case 3:
            data = _context.sent;
            sectionArray = Object.entries(data).map(function (_ref2) {
              var _ref3 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2___default()(_ref2, 2),
                key = _ref3[0],
                value = _ref3[1];
              return {
                label: key,
                value: value
              };
            });
            categorizeActions(sectionArray);
            _context.next = 11;
            break;
          case 8:
            _context.prev = 8;
            _context.t0 = _context["catch"](0);
            console.error('Error fetching sections data:', _context.t0);
          case 11:
          case "end":
            return _context.stop();
        }
      }, _callee, null, [[0, 8]]);
    }));
    return function fetchData() {
      return _ref.apply(this, arguments);
    };
  }();
  var categorizeActions = function categorizeActions(data) {
    var categories = {
      System: [],
      Project: [],
      Zone: [],
      Plant: [],
      Crop: [],
      Task: [],
      Plan: [],
      State: [],
      Category: [],
      Storage: [],
      CategoryInventory: [],
      CategoryInventoryFieldLevel: [],
      Employee: [],
      DynamicRole: [],
      Timekeeping: [],
      Visitor: [],
      IoTDevice: []
    };
    Object.entries(data).forEach(function (_ref4) {
      var _ref5 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2___default()(_ref4, 2),
        index = _ref5[0],
        obj = _ref5[1];
      var _ref6 = obj,
        label = _ref6.label,
        value = _ref6.value;
      if (label.includes('PROJECT')) {
        categories.Project.push({
          label: value,
          value: label
        });
      } else if (label.includes('ZONE')) {
        categories.Zone.push({
          label: value,
          value: label
        });
      } else if (label.includes('PLANT')) {
        categories.Plant.push({
          label: value,
          value: label
        });
      } else if (label.includes('CROP')) {
        categories.Crop.push({
          label: value,
          value: label
        });
      } else if (label.includes('TASK')) {
        categories.Task.push({
          label: value,
          value: label
        });
      } else if (label.includes('STATE')) {
        categories.State.push({
          label: value,
          value: label
        });
      } else if (label.includes('CATEGORY') && !label.includes('CATEGORY_INVENTORY')) {
        categories.Category.push({
          label: value,
          value: label
        });
      } else if (label.includes('STORAGE')) {
        categories.Storage.push({
          label: value,
          value: label
        });
      } else if (label.includes('CATEGORY_INVENTORY') && !label.includes('CATEGORY_INVENTORY_FIELD_LEVEL')) {
        categories.CategoryInventory.push({
          label: value,
          value: label
        });
      } else if (label.includes('CATEGORY_INVENTORY_FIELD_LEVEL')) {
        categories.CategoryInventoryFieldLevel.push({
          label: value,
          value: label
        });
      } else if (label.includes('EMPLOYEE')) {
        categories.Employee.push({
          label: value,
          value: label
        });
      } else if (label.includes('DYNAMIC_ROLE')) {
        categories.DynamicRole.push({
          label: value,
          value: label
        });
      } else if (label.includes('TIMEKEEPING')) {
        categories.Timekeeping.push({
          label: value,
          value: label
        });
      } else if (label.includes('VISITOR')) {
        categories.Visitor.push({
          label: value,
          value: label
        });
      } else if (label.includes('SYSTEM')) {
        categories.System.push({
          label: value,
          value: label
        });
      } else if (label.includes('IOT_DEVICE')) {
        categories.IoTDevice.push({
          label: value,
          value: label
        });
      }
    });
    setSectionCategories(categories);
  };
  var showModal = function showModal() {
    setOpen(true);
  };
  var hideModal = function hideModal() {
    setOpen(false);
  };
  var handleOk = function handleOk() {
    form.submit();
  };
  var handleCancel = function handleCancel() {
    hideModal();
    form.resetFields();
  };
  var handleReset = function handleReset() {
    form.resetFields();
  };
  var _useIntl = (0,_umijs_max__WEBPACK_IMPORTED_MODULE_4__.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  var handleFieldLevelPermissionsChange = function handleFieldLevelPermissionsChange(e) {
    setShowFieldLevelPermissions(e.target.checked);
  };
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.Fragment, {
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .ZP, {
      type: "primary",
      onClick: showModal,
      style: {
        display: 'flex',
        alignItems: 'center'
      },
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .Z, {}), " ", formatMessage({
        id: 'common.add_new_role'
      })]
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(antd__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .Z, {
      title: formatMessage({
        id: 'common.add_new_role'
      }),
      open: isOpen,
      onOk: handleOk,
      onCancel: handleCancel,
      width: 800,
      confirmLoading: loading,
      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Z, {
        layout: "horizontal",
        labelCol: {
          span: 24
        },
        wrapperCol: {
          span: 24
        },
        labelAlign: "left",
        form: form,
        onFinish: ( /*#__PURE__*/function () {
          var _ref7 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee2(value) {
            var values, flattenedString, dynamicRole, req;
            return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee2$(_context2) {
              while (1) switch (_context2.prev = _context2.next) {
                case 0:
                  _context2.prev = 0;
                  values = Object.entries(value).filter(function (_ref8) {
                    var _ref9 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2___default()(_ref8, 2),
                      key = _ref9[0],
                      value = _ref9[1];
                    return key !== 'label' && Array.isArray(value);
                  }).map(function (_ref10) {
                    var _ref11 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2___default()(_ref10, 2),
                      key = _ref11[0],
                      value = _ref11[1];
                    return value;
                  }).flat();
                  flattenedString = values.join(',');
                  dynamicRole = {
                    label: value.label,
                    iot_customer: params.customer_id,
                    sections: flattenedString
                  };
                  _context2.next = 6;
                  return (0,_services_customerUser__WEBPACK_IMPORTED_MODULE_3__/* .createDynamicRole */ .f6)(dynamicRole);
                case 6:
                  req = _context2.sent;
                  hideModal();
                  if (!params.refreshFnc) {
                    _context2.next = 11;
                    break;
                  }
                  _context2.next = 11;
                  return params.refreshFnc();
                case 11:
                  antd__WEBPACK_IMPORTED_MODULE_12__/* ["default"] */ .ZP.success('Success!');
                  _context2.next = 17;
                  break;
                case 14:
                  _context2.prev = 14;
                  _context2.t0 = _context2["catch"](0);
                  antd__WEBPACK_IMPORTED_MODULE_12__/* ["default"] */ .ZP.error(_context2.t0.toString());
                case 17:
                  _context2.prev = 17;
                  setLoading(false);
                  return _context2.finish(17);
                case 20:
                case "end":
                  return _context2.stop();
              }
            }, _callee2, null, [[0, 14, 17, 20]]);
          }));
          return function (_x) {
            return _ref7.apply(this, arguments);
          };
        }()),
        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_13__/* ["default"] */ .Z, {
          gutter: 16,
          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(antd__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .Z, {
            md: 12,
            children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Item, {
              label: formatMessage({
                id: 'common.role_name'
              }),
              rules: [{
                required: true,
                message: 'B\u1EAFt bu\u1ED9c \u0111i\u1EC1n'
              }],
              name: "label",
              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(antd__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .Z, {})
            })
          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(antd__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .Z, {
            span: 24,
            style: {
              textAlign: 'left',
              marginBottom: '16px'
            },
            children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(antd__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .ZP, {
              size: "small",
              type: "default",
              onClick: handleReset,
              children: "T\\u1EA1o l\\u1EA1i"
            })
          })]
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_13__/* ["default"] */ .Z, {
          gutter: 16,
          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .Z, {
            md: 6,
            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Title, {
              level: 5,
              children: "Qu\\u1EA3n tr\\u1ECB vi\\xEAn"
            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Item, {
              name: "system_section",
              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(antd__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .Z.Group, {
                options: sectionCategories.System
              })
            })]
          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .Z, {
            md: 6,
            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Title, {
              level: 5,
              children: "Quy\\u1EC1n d\\u1EF1 \\xE1n"
            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Item, {
              name: "project_section",
              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(antd__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .Z.Group, {
                options: sectionCategories.Project
              })
            })]
          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .Z, {
            md: 6,
            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Title, {
              level: 5,
              children: "Quy\\u1EC1n khu v\\u1EF1c"
            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Item, {
              name: "zone_section",
              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(antd__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .Z.Group, {
                options: sectionCategories.Zone
              })
            })]
          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .Z, {
            md: 6,
            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Title, {
              level: 5,
              children: "Quy\\u1EC1n c\\xE2y tr\\u1ED3ng"
            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Item, {
              name: "plant_section",
              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(antd__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .Z.Group, {
                options: sectionCategories.Plant
              })
            })]
          })]
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_13__/* ["default"] */ .Z, {
          gutter: 16,
          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .Z, {
            md: 6,
            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Title, {
              level: 5,
              children: "Quy\\u1EC1n m\\xF9a v\\u1EE5"
            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Item, {
              name: "crop_section",
              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(antd__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .Z.Group, {
                options: sectionCategories.Crop
              })
            })]
          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .Z, {
            md: 6,
            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Title, {
              level: 5,
              children: "Quy\\u1EC1n giai \\u0111o\\u1EA1n"
            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Item, {
              name: "state_section",
              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(antd__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .Z.Group, {
                options: sectionCategories.State
              })
            })]
          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .Z, {
            md: 6,
            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Title, {
              level: 5,
              children: "Quy\\u1EC1n c\\xF4ng vi\\u1EC7c"
            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Item, {
              name: "task_section",
              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(antd__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .Z.Group, {
                options: sectionCategories.Task
              })
            })]
          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .Z, {
            md: 6,
            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Title, {
              level: 5,
              children: "Quy\\u1EC1n h\\xE0ng h\\xF3a"
            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Item, {
              name: "category_section",
              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(antd__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .Z.Group, {
                options: sectionCategories.Category
              })
            })]
          })]
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_13__/* ["default"] */ .Z, {
          gutter: 16,
          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .Z, {
            md: 6,
            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Title, {
              level: 5,
              children: "Quy\\u1EC1n kho"
            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Item, {
              name: "storage_section",
              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(antd__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .Z.Group, {
                options: sectionCategories.Storage
              })
            })]
          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .Z, {
            md: 6,
            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Title, {
              level: 5,
              children: "Quy\\u1EC1n t\\u1ED3n kho"
            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxs)("div", {
              style: {
                display: 'flex',
                flexDirection: 'column',
                gap: '0px'
              },
              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Item, {
                name: "category_inventory_section",
                style: {
                  marginBottom: '0px'
                },
                children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(antd__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .Z.Group, {
                  options: sectionCategories.CategoryInventory
                })
              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(antd__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .Z, {
                onChange: handleFieldLevelPermissionsChange,
                style: {
                  marginTop: '0px'
                },
                children: "Quy\\u1EC1n chi ti\\u1EBFt"
              }), showFieldLevelPermissions && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)("div", {
                style: {
                  paddingLeft: '24px',
                  marginTop: '8px'
                },
                children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Item, {
                  name: "category_inventory_field_level_section",
                  children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(antd__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .Z.Group, {
                    options: sectionCategories.CategoryInventoryFieldLevel
                  })
                })
              })]
            })]
          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .Z, {
            md: 6,
            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Title, {
              level: 5,
              children: "Qu\\u1EA3n l\\xFD nh\\xE2n vi\\xEAn"
            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Item, {
              name: "employee_section",
              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(antd__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .Z.Group, {
                options: sectionCategories.Employee
              })
            })]
          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .Z, {
            md: 6,
            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Title, {
              level: 5,
              children: "Qu\\u1EA3n l\\xFD vai tr\\xF2"
            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Item, {
              name: "dynamic_role_section",
              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(antd__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .Z.Group, {
                options: sectionCategories.DynamicRole
              })
            })]
          })]
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_13__/* ["default"] */ .Z, {
          gutter: 16,
          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .Z, {
            md: 6,
            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Title, {
              level: 5,
              children: "Qu\\u1EA3n l\\xFD ch\\u1EA5m c\\xF4ng"
            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Item, {
              name: "dynamic_timekeeping_section",
              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(antd__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .Z.Group, {
                options: sectionCategories.Timekeeping
              })
            })]
          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .Z, {
            md: 6,
            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Title, {
              level: 5,
              children: "Kh\\xE1ch tham quan"
            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Item, {
              name: "visitor_section",
              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(antd__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .Z.Group, {
                options: sectionCategories.Visitor
              })
            })]
          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .Z, {
            md: 6,
            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Title, {
              level: 5,
              children: "Qu\\u1EA3n l\\xFD thi\\u1EBFt b\\u1ECB IoT"
            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Item, {
              name: "iot_device_section",
              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(antd__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .Z.Group, {
                options: sectionCategories.IoTDevice
              })
            })]
          })]
        })]
      })
    })]
  });
};
/* harmony default export */ __webpack_exports__.Z = (CreateRoleForm);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///24414
`)},66321:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){"use strict";eval(`// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return /* binding */ UserList; }
});

// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/regeneratorRuntime.js
var regeneratorRuntime = __webpack_require__(15009);
var regeneratorRuntime_default = /*#__PURE__*/__webpack_require__.n(regeneratorRuntime);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(99289);
var asyncToGenerator_default = /*#__PURE__*/__webpack_require__.n(asyncToGenerator);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/slicedToArray.js
var slicedToArray = __webpack_require__(5574);
var slicedToArray_default = /*#__PURE__*/__webpack_require__.n(slicedToArray);
// EXTERNAL MODULE: ./src/components/FallbackContent/index.tsx
var FallbackContent = __webpack_require__(65573);
// EXTERNAL MODULE: ./src/services/customerUser.ts
var customerUser = __webpack_require__(40063);
// EXTERNAL MODULE: ./src/services/sscript.ts
var sscript = __webpack_require__(39750);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-layout/es/components/PageContainer/index.js + 18 modules
var PageContainer = __webpack_require__(6110);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-table/es/Table.js + 76 modules
var Table = __webpack_require__(4894);
// EXTERNAL MODULE: ./src/.umi-production/exports.ts + 34 modules
var _umi_production_exports = __webpack_require__(7837);
// EXTERNAL MODULE: ./node_modules/antd/es/switch/index.js + 2 modules
var es_switch = __webpack_require__(72269);
// EXTERNAL MODULE: ./node_modules/antd/es/config-provider/index.js + 7 modules
var config_provider = __webpack_require__(28459);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./src/pages/MyUser/DynamicRole/Components/CreateNewRole.tsx
var CreateNewRole = __webpack_require__(24414);
// EXTERNAL MODULE: ./src/pages/MyUser/UserList/Components/CreateCustomerUserForm.tsx
var CreateCustomerUserForm = __webpack_require__(4645);
// EXTERNAL MODULE: ./node_modules/antd/es/form/index.js + 18 modules
var es_form = __webpack_require__(98138);
// EXTERNAL MODULE: ./node_modules/antd/es/select/index.js + 30 modules
var es_select = __webpack_require__(83863);
// EXTERNAL MODULE: ./node_modules/antd/es/message/index.js
var message = __webpack_require__(45360);
// EXTERNAL MODULE: ./node_modules/antd/es/button/index.js + 9 modules
var es_button = __webpack_require__(14726);
// EXTERNAL MODULE: ./node_modules/antd/es/modal/index.js + 1 modules
var modal = __webpack_require__(85576);
// EXTERNAL MODULE: ./node_modules/antd/es/row/index.js
var row = __webpack_require__(71230);
// EXTERNAL MODULE: ./node_modules/antd/es/col/index.js
var col = __webpack_require__(15746);
// EXTERNAL MODULE: ./node_modules/antd/es/input/index.js + 4 modules
var input = __webpack_require__(96365);
// EXTERNAL MODULE: ./src/helpers/tree.json
var tree = __webpack_require__(98792);
// EXTERNAL MODULE: ./src/services/utils.ts
var utils = __webpack_require__(467);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/Select/index.js
var Select = __webpack_require__(64317);
// EXTERNAL MODULE: ./node_modules/antd/es/input/TextArea.js + 4 modules
var TextArea = __webpack_require__(70006);
// EXTERNAL MODULE: ./node_modules/bcryptjs/dist/bcrypt.js
var bcrypt = __webpack_require__(2418);
var bcrypt_default = /*#__PURE__*/__webpack_require__.n(bcrypt);
// EXTERNAL MODULE: ./node_modules/uuid/dist/esm-browser/v4.js + 3 modules
var v4 = __webpack_require__(57632);
// EXTERNAL MODULE: ./node_modules/react/jsx-runtime.js
var jsx_runtime = __webpack_require__(85893);
;// CONCATENATED MODULE: ./src/pages/MyUser/UserList/Components/UpdateCustomerUserForm.tsx





var Item = es_form/* default */.Z.Item;












var Option = es_select/* default */.Z.Option;
var UpdateCustomerUserForm = function UpdateCustomerUserForm(params) {
  var _useState = (0,react.useState)(false),
    _useState2 = slicedToArray_default()(_useState, 2),
    loading = _useState2[0],
    setLoading = _useState2[1];
  var _useState3 = (0,react.useState)(false),
    _useState4 = slicedToArray_default()(_useState3, 2),
    isOpen = _useState4[0],
    setOpen = _useState4[1];
  var _Form$useForm = es_form/* default */.Z.useForm(),
    _Form$useForm2 = slicedToArray_default()(_Form$useForm, 1),
    form = _Form$useForm2[0];
  var _useState5 = (0,react.useState)([]),
    _useState6 = slicedToArray_default()(_useState5, 2),
    stateOption = _useState6[0],
    setStateOption = _useState6[1];
  var _useState7 = (0,react.useState)([]),
    _useState8 = slicedToArray_default()(_useState7, 2),
    wardOption = _useState8[0],
    setWardOption = _useState8[1];
  var _useState9 = (0,react.useState)({}),
    _useState10 = slicedToArray_default()(_useState9, 2),
    userCredential = _useState10[0],
    setUserCredential = _useState10[1];
  var _useModel = (0,_umi_production_exports.useModel)('@@initialState'),
    initialState = _useModel.initialState;
  var currentUser = initialState === null || initialState === void 0 ? void 0 : initialState.currentUser;
  var isAdmin = currentUser === null || currentUser === void 0 ? void 0 : currentUser.sections.includes('SYSTEM_ADMIN');
  var showModal = function showModal() {
    if (params.customerUser) form.setFieldsValue(params.customerUser);
    setOpen(true);
    getUserCredentitals();
  };
  var hideModal = function hideModal() {
    setOpen(false);
  };
  var handleOk = function handleOk() {
    form.submit();
  };
  var handleCancel = function handleCancel() {
    hideModal();
    form.resetFields();
  };
  var handleChangeCity = function handleChangeCity(value) {
    if (value) {
      var new_state = Object.keys(tree[value]['quan-huyen']).map(function (key) {
        return /*#__PURE__*/(0,jsx_runtime.jsx)(Option, {
          value: key,
          children: tree[value]['quan-huyen'][key].name_with_type
        }, key);
      });
      form.setFieldValue('district', null);
      form.setFieldValue('ward', null);
      setStateOption(new_state);
    } else {
      form.setFieldValue('district', null);
      form.setFieldValue('ward', null);
    }
  };
  var handleChangeState = function handleChangeState(value) {
    if (value) {
      var city = form.getFieldValue('province');
      if (city) {
        var new_ward = Object.keys(tree[city]['quan-huyen'][value]['xa-phuong']).map(function (key) {
          return /*#__PURE__*/(0,jsx_runtime.jsx)(Option, {
            value: key,
            children: tree[city]['quan-huyen'][value]['xa-phuong'][key].name_with_type
          }, key);
        });
        form.setFieldValue('ward', null);
        setWardOption(new_ward);
      }
    } else {
      form.setFieldValue('ward', null);
    }
  };
  var getUserCredentitals = /*#__PURE__*/function () {
    var _ref = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
      var _params$customerUser, filterArr, result;
      return regeneratorRuntime_default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _context.prev = 0;
            setLoading(true);
            filterArr = [['iot_customer_user_credentials', 'user_id', 'like', (_params$customerUser = params.customerUser) === null || _params$customerUser === void 0 ? void 0 : _params$customerUser.name]];
            _context.next = 5;
            return (0,sscript/* sscriptGeneralList */.RB)({
              doc_name: 'iot_customer_user_credentials',
              filters: filterArr,
              page: 1,
              size: 1,
              fields: ['*']
            });
          case 5:
            result = _context.sent;
            if (result.data.length > 0) {
              setUserCredential(result.data[0]);
            }
            _context.next = 12;
            break;
          case 9:
            _context.prev = 9;
            _context.t0 = _context["catch"](0);
            message/* default */.ZP.error(_context.t0.toString());
          case 12:
            _context.prev = 12;
            setLoading(false);
            return _context.finish(12);
          case 15:
          case "end":
            return _context.stop();
        }
      }, _callee, null, [[0, 9, 12, 15]]);
    }));
    return function getUserCredentitals() {
      return _ref.apply(this, arguments);
    };
  }();
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  if (loading) return /*#__PURE__*/(0,jsx_runtime.jsx)(jsx_runtime.Fragment, {
    children: "loading..."
  });
  return /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
    children: [/*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
      type: "link",
      onClick: showModal,
      children: formatMessage({
        id: 'common.edit'
      })
    }), /*#__PURE__*/(0,jsx_runtime.jsxs)(modal/* default */.Z, {
      title: formatMessage({
        id: 'common.edit'
      }),
      open: isOpen,
      onOk: handleOk,
      onCancel: handleCancel,
      width: 800,
      confirmLoading: loading,
      children: [/*#__PURE__*/(0,jsx_runtime.jsxs)(es_form/* default */.Z, {
        layout: "horizontal",
        labelCol: {
          span: 24
        },
        labelAlign: "left",
        form: form,
        onFinish: ( /*#__PURE__*/function () {
          var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2(value) {
            var _vietnam_location$pro, _vietnam_location$pro2, _vietnam_location$pro3, _params$customerUser2, email, phone_number, province, district, ward, address, description, first_name, last_name, iot_dynamic_role, is_deactivated, province_str, district_str, ward_str;
            return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
              while (1) switch (_context2.prev = _context2.next) {
                case 0:
                  _context2.prev = 0;
                  email = value.email, phone_number = value.phone_number, province = value.province, district = value.district, ward = value.ward, address = value.address, description = value.description, first_name = value.first_name, last_name = value.last_name, iot_dynamic_role = value.iot_dynamic_role, is_deactivated = value.is_deactivated;
                  console.log('iot_dynamic_role', value);
                  ward_str = '';
                  province_str = ((_vietnam_location$pro = tree[province]) === null || _vietnam_location$pro === void 0 ? void 0 : _vietnam_location$pro.name_with_type) || null;
                  if (district) district_str = (_vietnam_location$pro2 = tree[province]) === null || _vietnam_location$pro2 === void 0 || (_vietnam_location$pro2 = _vietnam_location$pro2['quan-huyen']) === null || _vietnam_location$pro2 === void 0 ? void 0 : _vietnam_location$pro2[district]['name_with_type'];
                  if (ward) ward_str = (_vietnam_location$pro3 = tree[province]) === null || _vietnam_location$pro3 === void 0 || (_vietnam_location$pro3 = _vietnam_location$pro3['quan-huyen']) === null || _vietnam_location$pro3 === void 0 || (_vietnam_location$pro3 = _vietnam_location$pro3[district]) === null || _vietnam_location$pro3 === void 0 || (_vietnam_location$pro3 = _vietnam_location$pro3['xa-phuong']) === null || _vietnam_location$pro3 === void 0 || (_vietnam_location$pro3 = _vietnam_location$pro3[ward]) === null || _vietnam_location$pro3 === void 0 ? void 0 : _vietnam_location$pro3['name_with_type'];
                  province = province_str;
                  district = district_str;
                  ward = ward_str;

                  // await generalUpdate('iot_customer_user', params.customerUser?.name, {
                  //   data: {
                  //     first_name,
                  //     last_name,
                  //     email,
                  //     phone_number,
                  //     province,
                  //     district,
                  //     ward,
                  //     address,
                  //     description,
                  //     iot_dynamic_role,
                  //     is_deactivated,
                  //   },
                  // });
                  _context2.next = 12;
                  return (0,customerUser/* updateCustomerUser */.cb)({
                    name: (_params$customerUser2 = params.customerUser) === null || _params$customerUser2 === void 0 ? void 0 : _params$customerUser2.name,
                    first_name: first_name,
                    last_name: last_name,
                    email: email,
                    phone_number: phone_number,
                    province: province,
                    district: district,
                    ward: ward,
                    address: address,
                    description: description,
                    iot_dynamic_role: iot_dynamic_role,
                    is_deactivated: is_deactivated
                  });
                case 12:
                  message/* default */.ZP.success('Success!');
                  hideModal();
                  if (!params.refreshFnc) {
                    _context2.next = 17;
                    break;
                  }
                  _context2.next = 17;
                  return params.refreshFnc();
                case 17:
                  _context2.next = 22;
                  break;
                case 19:
                  _context2.prev = 19;
                  _context2.t0 = _context2["catch"](0);
                  message/* default */.ZP.error(_context2.t0.toString());
                case 22:
                  _context2.prev = 22;
                  setLoading(false);
                  return _context2.finish(22);
                case 25:
                case "end":
                  return _context2.stop();
              }
            }, _callee2, null, [[0, 19, 22, 25]]);
          }));
          return function (_x) {
            return _ref2.apply(this, arguments);
          };
        }()),
        children: [/*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
          gutter: 16,
          children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
            className: "gutter-row",
            md: 6,
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(Item, {
              label: formatMessage({
                id: 'common.last_name'
              }),
              labelCol: {
                span: 24
              },
              rules: [{
                required: true,
                message: 'B\u1EAFt bu\u1ED9c \u0111i\u1EC1n'
              }],
              name: "last_name",
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(input/* default */.Z, {})
            })
          }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
            className: "gutter-row",
            md: 6,
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(Item, {
              label: formatMessage({
                id: 'common.first_name'
              }),
              labelCol: {
                span: 24
              },
              rules: [{
                required: true,
                message: 'B\u1EAFt bu\u1ED9c \u0111i\u1EC1n'
              }],
              name: "first_name",
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(input/* default */.Z, {})
            })
          }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
            className: "gutter-row",
            md: 6,
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(Item, {
              label: "Email",
              labelCol: {
                span: 24
              },
              rules: [{
                required: true,
                message: 'B\u1EAFt bu\u1ED9c \u0111i\u1EC1n'
              }, {
                type: 'email',
                message: 'Vui l\xF2ng nh\u1EADp \u0111\xFAng \u0111\u1ECBnh d\u1EA1ng email'
              }],
              name: "email",
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(input/* default */.Z, {})
            })
          }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
            className: "gutter-row",
            md: 6,
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(Item, {
              label: "Phone",
              labelCol: {
                span: 24
              },
              rules: [{
                required: true,
                message: 'B\u1EAFt bu\u1ED9c \u0111i\u1EC1n'
              }],
              name: "phone_number",
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(input/* default */.Z, {})
            })
          }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
            className: "gutter-row",
            md: 6,
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(Item, {
              label: "Province",
              labelCol: {
                span: 24
              },
              name: "province",
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_select/* default */.Z, {
                allowClear: true,
                showSearch: true,
                style: {
                  width: '100%'
                },
                onChange: handleChangeCity,
                filterOption: function filterOption(input, option) {
                  return (0,utils/* toLowerCaseNonAccentVietnamese */.HO)(option.children).includes((0,utils/* toLowerCaseNonAccentVietnamese */.HO)(input));
                },
                children: Object.keys(tree).map(function (key) {
                  return /*#__PURE__*/(0,jsx_runtime.jsx)(Option, {
                    value: key,
                    children: tree[key].name
                  }, key);
                })
              })
            })
          }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
            className: "gutter-row",
            md: 6,
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(Item, {
              label: "District",
              labelCol: {
                span: 24
              },
              name: "district",
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_select/* default */.Z, {
                allowClear: true,
                showSearch: true,
                style: {
                  width: '100%'
                },
                onChange: handleChangeState,
                filterOption: function filterOption(input, option) {
                  return (0,utils/* toLowerCaseNonAccentVietnamese */.HO)(option.children).includes((0,utils/* toLowerCaseNonAccentVietnamese */.HO)(input));
                },
                children: stateOption
              })
            })
          }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
            className: "gutter-row",
            md: 6,
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(Item, {
              label: "Ward",
              labelCol: {
                span: 24
              },
              name: "ward",
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_select/* default */.Z, {
                allowClear: true,
                showSearch: true,
                style: {
                  width: '100%'
                },
                filterOption: function filterOption(input, option) {
                  return (0,utils/* toLowerCaseNonAccentVietnamese */.HO)(option.children).includes((0,utils/* toLowerCaseNonAccentVietnamese */.HO)(input));
                },
                children: wardOption
              })
            })
          }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
            className: "gutter-row",
            md: 6,
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(Item, {
              label: "Address",
              labelCol: {
                span: 24
              },
              name: "address",
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(input/* default */.Z, {})
            })
          }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
            className: "gutter-row",
            md: 24,
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(Item, {
              label: "Description",
              labelCol: {
                span: 24
              },
              name: "description",
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(TextArea/* default */.Z, {
                rows: 5,
                placeholder: "maxLength is 100",
                maxLength: 100
              })
            })
          })]
        }), /*#__PURE__*/(0,jsx_runtime.jsx)(row/* default */.Z, {
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
            className: "gutter-row",
            md: 24,
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(Item, {
              label: formatMessage({
                id: 'common.role'
              }),
              required: true,
              rules: [{
                required: true,
                message: 'Vui l\xF2ng ch\u1ECDn vai tr\xF2'
              }],
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
                initialValue: params.customerUser.iot_dynamic_role,
                rules: [{
                  required: true,
                  message: 'Vui l\xF2ng ch\u1ECDn vai tr\xF2'
                }],
                name: 'iot_dynamic_role',
                showSearch: true,
                request: ( /*#__PURE__*/function () {
                  var _ref3 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee3(option) {
                    var roleList;
                    return regeneratorRuntime_default()().wrap(function _callee3$(_context3) {
                      while (1) switch (_context3.prev = _context3.next) {
                        case 0:
                          _context3.next = 2;
                          return (0,customerUser/* getDynamicRole */.w)();
                        case 2:
                          roleList = _context3.sent;
                          return _context3.abrupt("return", roleList.map(function (item) {
                            return {
                              label: item.label,
                              value: item.name
                            };
                          }));
                        case 4:
                        case "end":
                          return _context3.stop();
                      }
                    }, _callee3);
                  }));
                  return function (_x2) {
                    return _ref3.apply(this, arguments);
                  };
                }())
              })
            })
          })
        }), isAdmin && /*#__PURE__*/(0,jsx_runtime.jsx)(jsx_runtime.Fragment, {})
        // <Row>
        //   <Col className="gutter-row" md={24}>
        //     <Item
        //       label={formatMessage({
        //         id: 'common.active',
        //       })}
        //     >
        //       <ProFormSwitch
        //         initialValue={!params.customerUser.is_deactivated}
        //         name={'is_deactivated'}
        //       />
        //       {/* <Switch /> */}
        //     </Item>
        //   </Col>
        // </Row>
        ]
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(es_form/* default */.Z, {
        layout: "horizontal",
        labelCol: {
          span: 24
        },
        labelAlign: "left",
        onFinish: ( /*#__PURE__*/function () {
          var _ref4 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee4(value) {
            var new_password, user_id, salt, hash;
            return regeneratorRuntime_default()().wrap(function _callee4$(_context4) {
              while (1) switch (_context4.prev = _context4.next) {
                case 0:
                  _context4.prev = 0;
                  new_password = value.new_password;
                  user_id = params.customerUser.name;
                  salt = bcrypt_default().genSaltSync(10);
                  hash = bcrypt_default().hashSync(new_password, salt);
                  if (!(userCredential !== null && userCredential !== void 0 && userCredential.name)) {
                    _context4.next = 10;
                    break;
                  }
                  _context4.next = 8;
                  return (0,sscript/* generalUpdate */.I6)('iot_customer_user_credentials', userCredential === null || userCredential === void 0 ? void 0 : userCredential.name, {
                    data: {
                      name: userCredential === null || userCredential === void 0 ? void 0 : userCredential.name,
                      user_id: user_id,
                      password: hash
                    }
                  });
                case 8:
                  _context4.next = 12;
                  break;
                case 10:
                  _context4.next = 12;
                  return (0,sscript/* generalCreate */.UD)('iot_customer_user_credentials', {
                    data: {
                      id: (0,v4/* default */.Z)(),
                      user_id: user_id,
                      password: hash
                    }
                  });
                case 12:
                  message/* default */.ZP.success('Success!');
                  hideModal();
                  if (!params.refreshFnc) {
                    _context4.next = 17;
                    break;
                  }
                  _context4.next = 17;
                  return params.refreshFnc();
                case 17:
                  _context4.next = 22;
                  break;
                case 19:
                  _context4.prev = 19;
                  _context4.t0 = _context4["catch"](0);
                  message/* default */.ZP.error(_context4.t0.toString());
                case 22:
                  _context4.prev = 22;
                  setLoading(false);
                  return _context4.finish(22);
                case 25:
                case "end":
                  return _context4.stop();
              }
            }, _callee4, null, [[0, 19, 22, 25]]);
          }));
          return function (_x3) {
            return _ref4.apply(this, arguments);
          };
        }()),
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(row/* default */.Z, {
          gutter: 5,
          children: Object.keys(userCredential).length ? /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
            children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
              className: "gutter-row",
              md: 12,
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(Item, {
                label: formatMessage({
                  id: 'common.reset_password'
                }),
                labelCol: {
                  span: 12
                },
                name: "new_password",
                rules: [{
                  required: true,
                  message: 'B\u1EAFt bu\u1ED9c \u0111i\u1EC1n'
                }],
                children: /*#__PURE__*/(0,jsx_runtime.jsx)(input/* default */.Z, {})
              })
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
              className: "gutter-row",
              md: 12,
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
                type: "primary",
                htmlType: "submit",
                children: formatMessage({
                  id: 'common.confirm'
                })
              })
            })]
          }) : /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
            children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
              className: "gutter-row",
              md: 24,
              children: /*#__PURE__*/(0,jsx_runtime.jsx)("h3", {
                children: formatMessage({
                  id: 'common.does_not_have_an_account_create_a_new_account'
                })
              })
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
              className: "gutter-row",
              md: 12,
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(Item, {
                label: "Password",
                labelCol: {
                  span: 12
                },
                name: "new_password",
                rules: [{
                  required: true,
                  message: 'B\u1EAFt bu\u1ED9c \u0111i\u1EC1n'
                }],
                children: /*#__PURE__*/(0,jsx_runtime.jsx)(input/* default */.Z, {})
              })
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
              className: "gutter-row",
              md: 12,
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
                type: "primary",
                htmlType: "submit",
                children: formatMessage({
                  id: 'common.create_an_account'
                })
              })
            })]
          })
        })
      })]
    })]
  });
};
/* harmony default export */ var Components_UpdateCustomerUserForm = (UpdateCustomerUserForm);
;// CONCATENATED MODULE: ./src/pages/MyUser/UserList/index.tsx















var CustomerUser = function CustomerUser() {
  var _initialState$current, _initialState$current2;
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  var tableRef = (0,react.useRef)();
  var _useSearchParams = (0,_umi_production_exports.useSearchParams)(),
    _useSearchParams2 = slicedToArray_default()(_useSearchParams, 2),
    searchParams = _useSearchParams2[0],
    setSearchParams = _useSearchParams2[1];
  var _useModel = (0,_umi_production_exports.useModel)('@@initialState'),
    initialState = _useModel.initialState;
  var customer_name = initialState === null || initialState === void 0 || (_initialState$current = initialState.currentUser) === null || _initialState$current === void 0 ? void 0 : _initialState$current.customer_id;
  var isAdmin = initialState === null || initialState === void 0 || (_initialState$current2 = initialState.currentUser) === null || _initialState$current2 === void 0 ? void 0 : _initialState$current2.sections.includes('SYSTEM_ADMIN');
  console.log('isAdmin', initialState === null || initialState === void 0 ? void 0 : initialState.currentUser);
  if (!customer_name) return /*#__PURE__*/(0,jsx_runtime.jsx)(jsx_runtime.Fragment, {});
  var access = (0,_umi_production_exports.useAccess)();
  var canRead = access.canAccessPageEmployeeNewManagement();
  var canUpdate = access.canUpdateInEmployeeNewManagement();
  var canCreateUser = access.canCreateInEmployeeNewManagement();
  var canCreateRole = access.canCreateInRoleManagement();
  var columns = [{
    title: formatMessage({
      id: 'common.action'
    }),
    dataIndex: 'name',
    render: function render(dom, entity) {
      if (canUpdate) {
        return /*#__PURE__*/(0,jsx_runtime.jsx)(Components_UpdateCustomerUserForm, {
          refreshFnc: reloadTable,
          customerUser: entity
        });
      }
    },
    fixed: 'left',
    hideInTable: !canUpdate,
    align: 'center',
    width: 40
  }, {
    title: 'Email',
    dataIndex: 'email',
    align: 'center',
    width: 80
  }, {
    title: formatMessage({
      id: 'common.role'
    }),
    dataIndex: 'iot_dynamic_role',
    render: function render(dom, entity) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)(jsx_runtime.Fragment, {
        children: entity.role_label
      });
    },
    align: 'center',
    width: 60
  }, {
    title: formatMessage({
      id: 'common.last_name'
    }),
    dataIndex: 'last_name',
    align: 'center',
    width: 40
  }, {
    title: formatMessage({
      id: 'common.first_name'
    }),
    dataIndex: 'first_name',
    align: 'center',
    width: 40
  }, {
    title: 'Phone',
    dataIndex: 'phone_number',
    align: 'center',
    width: 60
  }, {
    title: formatMessage({
      id: 'common.active'
    }),
    dataIndex: 'is_deactivated',
    align: 'center',
    width: 40,
    render: function render(dom, entity, index, action, schema) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)(es_switch/* default */.Z, {
        disabled: isAdmin ? false : true,
        checked: !entity.is_deactivated,
        onChange: ( /*#__PURE__*/function () {
          var _ref = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(checked) {
            var _tableRef$current;
            var newIsDeactivated;
            return regeneratorRuntime_default()().wrap(function _callee$(_context) {
              while (1) switch (_context.prev = _context.next) {
                case 0:
                  newIsDeactivated = checked ? 0 : 1;
                  _context.next = 3;
                  return (0,customerUser/* updateCustomerUser */.cb)({
                    name: entity.name,
                    is_deactivated: newIsDeactivated
                  });
                case 3:
                  (_tableRef$current = tableRef.current) === null || _tableRef$current === void 0 || _tableRef$current.reload();
                case 4:
                case "end":
                  return _context.stop();
              }
            }, _callee);
          }));
          return function (_x) {
            return _ref.apply(this, arguments);
          };
        }())
      });
    }
  }];
  var reloadTable = /*#__PURE__*/function () {
    var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2() {
      var _tableRef$current2;
      return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            (_tableRef$current2 = tableRef.current) === null || _tableRef$current2 === void 0 || _tableRef$current2.reload();
          case 1:
          case "end":
            return _context2.stop();
        }
      }, _callee2);
    }));
    return function reloadTable() {
      return _ref2.apply(this, arguments);
    };
  }();
  var renderButtons = [];
  if (canCreateUser) {
    renderButtons.push( /*#__PURE__*/(0,jsx_runtime.jsx)(CreateCustomerUserForm/* default */.Z, {
      refreshFnc: reloadTable,
      customer_id: customer_name
    }, "create_user"));
  }
  if (canCreateRole) {
    renderButtons.push( /*#__PURE__*/(0,jsx_runtime.jsx)(CreateNewRole/* default */.Z, {
      refreshFnc: reloadTable,
      customer_id: customer_name
    }, "create_role"));
  }
  return /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.Access, {
    accessible: canRead,
    fallback: /*#__PURE__*/(0,jsx_runtime.jsx)(FallbackContent/* default */.Z, {}),
    children: /*#__PURE__*/(0,jsx_runtime.jsx)(PageContainer/* PageContainer */._z, {
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(config_provider/* default */.ZP, {
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(Table/* default */.Z, {
          scroll: {
            x: 1200,
            y: 600
          },
          size: "small",
          actionRef: tableRef,
          rowKey: "name"
          // loading={loading}
          // dataSource={[...users]}
          ,
          request: ( /*#__PURE__*/function () {
            var _ref3 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee3(params, sort, filter) {
              var order_by, current, pageSize, searchFields, filterArr, result;
              return regeneratorRuntime_default()().wrap(function _callee3$(_context3) {
                while (1) switch (_context3.prev = _context3.next) {
                  case 0:
                    order_by = 'email, modified desc';
                    if (Object.keys(sort).length) {
                      order_by = "".concat(Object.keys(sort)[0], " ").concat(Object.values(sort)[0] === 'ascend' ? 'asc' : 'desc');
                    }
                    current = params.current, pageSize = params.pageSize;
                    searchFields = Object.keys(params).filter(function (field) {
                      var value = params[field];
                      return field !== 'current' && field !== 'pageSize' && value !== 'all';
                    });
                    filterArr = searchFields.map(function (field) {
                      var value = params[field];
                      return ['iot_customer_user', field, 'like', "%".concat(value, "%")];
                    });
                    if (customer_name) {
                      filterArr.push(['iot_customer_user', 'customer_id', 'like', customer_name]);
                    }
                    _context3.prev = 6;
                    _context3.next = 9;
                    return (0,sscript/* sscriptGeneralList */.RB)({
                      doc_name: 'iot_customer_user',
                      filters: filterArr,
                      page: current ? current : 0 + 1,
                      size: pageSize,
                      fields: ['*'],
                      order_by: order_by
                    });
                  case 9:
                    result = _context3.sent;
                    return _context3.abrupt("return", {
                      data: result.data,
                      success: true,
                      total: result.pagination.totalElements
                    });
                  case 13:
                    _context3.prev = 13;
                    _context3.t0 = _context3["catch"](6);
                    console.log(_context3.t0);
                  case 16:
                    _context3.prev = 16;
                    return _context3.finish(16);
                  case 18:
                  case "end":
                    return _context3.stop();
                }
              }, _callee3, null, [[6, 13, 16, 18]]);
            }));
            return function (_x2, _x3, _x4) {
              return _ref3.apply(this, arguments);
            };
          }()),
          bordered: true,
          columns: columns,
          search: false,
          toolBarRender: function toolBarRender() {
            return renderButtons;
          },
          pagination: {
            defaultPageSize: 20,
            showSizeChanger: true,
            pageSizeOptions: ['20', '50', '100']
          }
        })
      })
    })
  });
};
/* harmony default export */ var UserList = (CustomerUser);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///66321
`)},46601:function(){},89214:function(){},71922:function(){},2363:function(){},96419:function(){},56353:function(){},69386:function(){},94014:function(){},52361:function(){},94616:function(){}}]);
