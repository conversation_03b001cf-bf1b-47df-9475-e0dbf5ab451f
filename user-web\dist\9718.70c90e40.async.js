"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[9718],{22638:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(67294);
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(92770);
/* harmony import */ var _utils_isDev__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(31663);



function useMemoizedFn(fn) {
  if (_utils_isDev__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Z) {
    if (!(0,_utils__WEBPACK_IMPORTED_MODULE_2__/* .isFunction */ .mf)(fn)) {
      console.error("useMemoizedFn expected parameter is a function, got ".concat(typeof fn));
    }
  }
  var fnRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(fn);
  // why not write \`fnRef.current = fn\`?
  // https://github.com/alibaba/hooks/issues/728
  fnRef.current = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function () {
    return fn;
  }, [fn]);
  var memoizedFn = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();
  if (!memoizedFn.current) {
    memoizedFn.current = function () {
      var args = [];
      for (var _i = 0; _i < arguments.length; _i++) {
        args[_i] = arguments[_i];
      }
      return fnRef.current.apply(this, args);
    };
  }
  return memoizedFn.current;
}
/* harmony default export */ __webpack_exports__.Z = (useMemoizedFn);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiMjI2MzguanMiLCJtYXBwaW5ncyI6Ijs7O0FBQXdDO0FBQ0Y7QUFDSDtBQUNuQztBQUNBLE1BQU0sNkRBQUs7QUFDWCxTQUFTLDREQUFVO0FBQ25CO0FBQ0E7QUFDQTtBQUNBLGNBQWMsNkNBQU07QUFDcEI7QUFDQTtBQUNBLGtCQUFrQiw4Q0FBTztBQUN6QjtBQUNBLEdBQUc7QUFDSCxtQkFBbUIsNkNBQU07QUFDekI7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCLHVCQUF1QjtBQUM5QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNEQUFlLGFBQWEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hbnQtZGVzaWduLXByby8uL25vZGVfbW9kdWxlcy9haG9va3MvZXMvdXNlTWVtb2l6ZWRGbi9pbmRleC5qcz84MDRmIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZU1lbW8sIHVzZVJlZiB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IGlzRnVuY3Rpb24gfSBmcm9tICcuLi91dGlscyc7XG5pbXBvcnQgaXNEZXYgZnJvbSAnLi4vdXRpbHMvaXNEZXYnO1xuZnVuY3Rpb24gdXNlTWVtb2l6ZWRGbihmbikge1xuICBpZiAoaXNEZXYpIHtcbiAgICBpZiAoIWlzRnVuY3Rpb24oZm4pKSB7XG4gICAgICBjb25zb2xlLmVycm9yKFwidXNlTWVtb2l6ZWRGbiBleHBlY3RlZCBwYXJhbWV0ZXIgaXMgYSBmdW5jdGlvbiwgZ290IFwiLmNvbmNhdCh0eXBlb2YgZm4pKTtcbiAgICB9XG4gIH1cbiAgdmFyIGZuUmVmID0gdXNlUmVmKGZuKTtcbiAgLy8gd2h5IG5vdCB3cml0ZSBgZm5SZWYuY3VycmVudCA9IGZuYD9cbiAgLy8gaHR0cHM6Ly9naXRodWIuY29tL2FsaWJhYmEvaG9va3MvaXNzdWVzLzcyOFxuICBmblJlZi5jdXJyZW50ID0gdXNlTWVtbyhmdW5jdGlvbiAoKSB7XG4gICAgcmV0dXJuIGZuO1xuICB9LCBbZm5dKTtcbiAgdmFyIG1lbW9pemVkRm4gPSB1c2VSZWYoKTtcbiAgaWYgKCFtZW1vaXplZEZuLmN1cnJlbnQpIHtcbiAgICBtZW1vaXplZEZuLmN1cnJlbnQgPSBmdW5jdGlvbiAoKSB7XG4gICAgICB2YXIgYXJncyA9IFtdO1xuICAgICAgZm9yICh2YXIgX2kgPSAwOyBfaSA8IGFyZ3VtZW50cy5sZW5ndGg7IF9pKyspIHtcbiAgICAgICAgYXJnc1tfaV0gPSBhcmd1bWVudHNbX2ldO1xuICAgICAgfVxuICAgICAgcmV0dXJuIGZuUmVmLmN1cnJlbnQuYXBwbHkodGhpcywgYXJncyk7XG4gICAgfTtcbiAgfVxuICByZXR1cm4gbWVtb2l6ZWRGbi5jdXJyZW50O1xufVxuZXhwb3J0IGRlZmF1bHQgdXNlTWVtb2l6ZWRGbjsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=
//# sourceURL=webpack-internal:///22638
`)},79718:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`
// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  Z: function() { return /* binding */ es_useRequest; }
});

// UNUSED EXPORTS: clearCache

// EXTERNAL MODULE: ./node_modules/tslib/tslib.es6.mjs
var tslib_es6 = __webpack_require__(97582);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./node_modules/ahooks/es/useUpdateEffect/index.js + 1 modules
var useUpdateEffect = __webpack_require__(77598);
;// CONCATENATED MODULE: ./node_modules/ahooks/es/useRequest/src/plugins/useAutoRunPlugin.js



// support refreshDeps & ready
var useAutoRunPlugin = function (fetchInstance, _a) {
  var manual = _a.manual,
    _b = _a.ready,
    ready = _b === void 0 ? true : _b,
    _c = _a.defaultParams,
    defaultParams = _c === void 0 ? [] : _c,
    _d = _a.refreshDeps,
    refreshDeps = _d === void 0 ? [] : _d,
    refreshDepsAction = _a.refreshDepsAction;
  var hasAutoRun = (0,react.useRef)(false);
  hasAutoRun.current = false;
  (0,useUpdateEffect/* default */.Z)(function () {
    if (!manual && ready) {
      hasAutoRun.current = true;
      fetchInstance.run.apply(fetchInstance, (0,tslib_es6/* __spreadArray */.ev)([], (0,tslib_es6/* __read */.CR)(defaultParams), false));
    }
  }, [ready]);
  (0,useUpdateEffect/* default */.Z)(function () {
    if (hasAutoRun.current) {
      return;
    }
    if (!manual) {
      hasAutoRun.current = true;
      if (refreshDepsAction) {
        refreshDepsAction();
      } else {
        fetchInstance.refresh();
      }
    }
  }, (0,tslib_es6/* __spreadArray */.ev)([], (0,tslib_es6/* __read */.CR)(refreshDeps), false));
  return {
    onBefore: function () {
      if (!ready) {
        return {
          stopNow: true
        };
      }
    }
  };
};
useAutoRunPlugin.onInit = function (_a) {
  var _b = _a.ready,
    ready = _b === void 0 ? true : _b,
    manual = _a.manual;
  return {
    loading: !manual && ready
  };
};
/* harmony default export */ var plugins_useAutoRunPlugin = (useAutoRunPlugin);
;// CONCATENATED MODULE: ./node_modules/ahooks/es/utils/depsAreSame.js
function depsAreSame(oldDeps, deps) {
  if (oldDeps === deps) return true;
  for (var i = 0; i < oldDeps.length; i++) {
    if (!Object.is(oldDeps[i], deps[i])) return false;
  }
  return true;
}
;// CONCATENATED MODULE: ./node_modules/ahooks/es/useCreation/index.js


function useCreation(factory, deps) {
  var current = (0,react.useRef)({
    deps: deps,
    obj: undefined,
    initialized: false
  }).current;
  if (current.initialized === false || !depsAreSame(current.deps, deps)) {
    current.deps = deps;
    current.obj = factory();
    current.initialized = true;
  }
  return current.obj;
}
// EXTERNAL MODULE: ./node_modules/ahooks/es/useUnmount/index.js
var useUnmount = __webpack_require__(45210);
;// CONCATENATED MODULE: ./node_modules/ahooks/es/useRequest/src/utils/cache.js

var cache = new Map();
var setCache = function (key, cacheTime, cachedData) {
  var currentCache = cache.get(key);
  if (currentCache === null || currentCache === void 0 ? void 0 : currentCache.timer) {
    clearTimeout(currentCache.timer);
  }
  var timer = undefined;
  if (cacheTime > -1) {
    // if cache out, clear it
    timer = setTimeout(function () {
      cache.delete(key);
    }, cacheTime);
  }
  cache.set(key, (0,tslib_es6/* __assign */.pi)((0,tslib_es6/* __assign */.pi)({}, cachedData), {
    timer: timer
  }));
};
var getCache = function (key) {
  return cache.get(key);
};
var clearCache = function (key) {
  if (key) {
    var cacheKeys = Array.isArray(key) ? key : [key];
    cacheKeys.forEach(function (cacheKey) {
      return cache.delete(cacheKey);
    });
  } else {
    cache.clear();
  }
};

;// CONCATENATED MODULE: ./node_modules/ahooks/es/useRequest/src/utils/cachePromise.js
var cachePromise = new Map();
var getCachePromise = function (cacheKey) {
  return cachePromise.get(cacheKey);
};
var setCachePromise = function (cacheKey, promise) {
  // Should cache the same promise, cannot be promise.finally
  // Because the promise.finally will change the reference of the promise
  cachePromise.set(cacheKey, promise);
  // no use promise.finally for compatibility
  promise.then(function (res) {
    cachePromise.delete(cacheKey);
    return res;
  }).catch(function () {
    cachePromise.delete(cacheKey);
  });
};

;// CONCATENATED MODULE: ./node_modules/ahooks/es/useRequest/src/utils/cacheSubscribe.js
var listeners = {};
var trigger = function (key, data) {
  if (listeners[key]) {
    listeners[key].forEach(function (item) {
      return item(data);
    });
  }
};
var subscribe = function (key, listener) {
  if (!listeners[key]) {
    listeners[key] = [];
  }
  listeners[key].push(listener);
  return function unsubscribe() {
    var index = listeners[key].indexOf(listener);
    listeners[key].splice(index, 1);
  };
};

;// CONCATENATED MODULE: ./node_modules/ahooks/es/useRequest/src/plugins/useCachePlugin.js







var useCachePlugin = function (fetchInstance, _a) {
  var cacheKey = _a.cacheKey,
    _b = _a.cacheTime,
    cacheTime = _b === void 0 ? 5 * 60 * 1000 : _b,
    _c = _a.staleTime,
    staleTime = _c === void 0 ? 0 : _c,
    customSetCache = _a.setCache,
    customGetCache = _a.getCache;
  var unSubscribeRef = (0,react.useRef)();
  var currentPromiseRef = (0,react.useRef)();
  var _setCache = function (key, cachedData) {
    if (customSetCache) {
      customSetCache(cachedData);
    } else {
      setCache(key, cacheTime, cachedData);
    }
    trigger(key, cachedData.data);
  };
  var _getCache = function (key, params) {
    if (params === void 0) {
      params = [];
    }
    if (customGetCache) {
      return customGetCache(params);
    }
    return getCache(key);
  };
  useCreation(function () {
    if (!cacheKey) {
      return;
    }
    // get data from cache when init
    var cacheData = _getCache(cacheKey);
    if (cacheData && Object.hasOwnProperty.call(cacheData, 'data')) {
      fetchInstance.state.data = cacheData.data;
      fetchInstance.state.params = cacheData.params;
      if (staleTime === -1 || new Date().getTime() - cacheData.time <= staleTime) {
        fetchInstance.state.loading = false;
      }
    }
    // subscribe same cachekey update, trigger update
    unSubscribeRef.current = subscribe(cacheKey, function (data) {
      fetchInstance.setState({
        data: data
      });
    });
  }, []);
  (0,useUnmount/* default */.Z)(function () {
    var _a;
    (_a = unSubscribeRef.current) === null || _a === void 0 ? void 0 : _a.call(unSubscribeRef);
  });
  if (!cacheKey) {
    return {};
  }
  return {
    onBefore: function (params) {
      var cacheData = _getCache(cacheKey, params);
      if (!cacheData || !Object.hasOwnProperty.call(cacheData, 'data')) {
        return {};
      }
      // If the data is fresh, stop request
      if (staleTime === -1 || new Date().getTime() - cacheData.time <= staleTime) {
        return {
          loading: false,
          data: cacheData === null || cacheData === void 0 ? void 0 : cacheData.data,
          error: undefined,
          returnNow: true
        };
      } else {
        // If the data is stale, return data, and request continue
        return {
          data: cacheData === null || cacheData === void 0 ? void 0 : cacheData.data,
          error: undefined
        };
      }
    },
    onRequest: function (service, args) {
      var servicePromise = getCachePromise(cacheKey);
      // If has servicePromise, and is not trigger by self, then use it
      if (servicePromise && servicePromise !== currentPromiseRef.current) {
        return {
          servicePromise: servicePromise
        };
      }
      servicePromise = service.apply(void 0, (0,tslib_es6/* __spreadArray */.ev)([], (0,tslib_es6/* __read */.CR)(args), false));
      currentPromiseRef.current = servicePromise;
      setCachePromise(cacheKey, servicePromise);
      return {
        servicePromise: servicePromise
      };
    },
    onSuccess: function (data, params) {
      var _a;
      if (cacheKey) {
        // cancel subscribe, avoid trgger self
        (_a = unSubscribeRef.current) === null || _a === void 0 ? void 0 : _a.call(unSubscribeRef);
        _setCache(cacheKey, {
          data: data,
          params: params,
          time: new Date().getTime()
        });
        // resubscribe
        unSubscribeRef.current = subscribe(cacheKey, function (d) {
          fetchInstance.setState({
            data: d
          });
        });
      }
    },
    onMutate: function (data) {
      var _a;
      if (cacheKey) {
        // cancel subscribe, avoid trigger self
        (_a = unSubscribeRef.current) === null || _a === void 0 ? void 0 : _a.call(unSubscribeRef);
        _setCache(cacheKey, {
          data: data,
          params: fetchInstance.state.params,
          time: new Date().getTime()
        });
        // resubscribe
        unSubscribeRef.current = subscribe(cacheKey, function (d) {
          fetchInstance.setState({
            data: d
          });
        });
      }
    }
  };
};
/* harmony default export */ var plugins_useCachePlugin = (useCachePlugin);
// EXTERNAL MODULE: ./node_modules/lodash/debounce.js
var debounce = __webpack_require__(23279);
var debounce_default = /*#__PURE__*/__webpack_require__.n(debounce);
;// CONCATENATED MODULE: ./node_modules/ahooks/es/useRequest/src/plugins/useDebouncePlugin.js



var useDebouncePlugin = function (fetchInstance, _a) {
  var debounceWait = _a.debounceWait,
    debounceLeading = _a.debounceLeading,
    debounceTrailing = _a.debounceTrailing,
    debounceMaxWait = _a.debounceMaxWait;
  var debouncedRef = (0,react.useRef)();
  var options = (0,react.useMemo)(function () {
    var ret = {};
    if (debounceLeading !== undefined) {
      ret.leading = debounceLeading;
    }
    if (debounceTrailing !== undefined) {
      ret.trailing = debounceTrailing;
    }
    if (debounceMaxWait !== undefined) {
      ret.maxWait = debounceMaxWait;
    }
    return ret;
  }, [debounceLeading, debounceTrailing, debounceMaxWait]);
  (0,react.useEffect)(function () {
    if (debounceWait) {
      var _originRunAsync_1 = fetchInstance.runAsync.bind(fetchInstance);
      debouncedRef.current = debounce_default()(function (callback) {
        callback();
      }, debounceWait, options);
      // debounce runAsync should be promise
      // https://github.com/lodash/lodash/issues/4400#issuecomment-834800398
      fetchInstance.runAsync = function () {
        var args = [];
        for (var _i = 0; _i < arguments.length; _i++) {
          args[_i] = arguments[_i];
        }
        return new Promise(function (resolve, reject) {
          var _a;
          (_a = debouncedRef.current) === null || _a === void 0 ? void 0 : _a.call(debouncedRef, function () {
            _originRunAsync_1.apply(void 0, (0,tslib_es6/* __spreadArray */.ev)([], (0,tslib_es6/* __read */.CR)(args), false)).then(resolve).catch(reject);
          });
        });
      };
      return function () {
        var _a;
        (_a = debouncedRef.current) === null || _a === void 0 ? void 0 : _a.cancel();
        fetchInstance.runAsync = _originRunAsync_1;
      };
    }
  }, [debounceWait, options]);
  if (!debounceWait) {
    return {};
  }
  return {
    onCancel: function () {
      var _a;
      (_a = debouncedRef.current) === null || _a === void 0 ? void 0 : _a.cancel();
    }
  };
};
/* harmony default export */ var plugins_useDebouncePlugin = (useDebouncePlugin);
;// CONCATENATED MODULE: ./node_modules/ahooks/es/useRequest/src/plugins/useLoadingDelayPlugin.js

var useLoadingDelayPlugin = function (fetchInstance, _a) {
  var loadingDelay = _a.loadingDelay,
    ready = _a.ready;
  var timerRef = (0,react.useRef)();
  if (!loadingDelay) {
    return {};
  }
  var cancelTimeout = function () {
    if (timerRef.current) {
      clearTimeout(timerRef.current);
    }
  };
  return {
    onBefore: function () {
      cancelTimeout();
      // Two cases:
      // 1. ready === undefined
      // 2. ready === true
      if (ready !== false) {
        timerRef.current = setTimeout(function () {
          fetchInstance.setState({
            loading: true
          });
        }, loadingDelay);
      }
      return {
        loading: false
      };
    },
    onFinally: function () {
      cancelTimeout();
    },
    onCancel: function () {
      cancelTimeout();
    }
  };
};
/* harmony default export */ var plugins_useLoadingDelayPlugin = (useLoadingDelayPlugin);
// EXTERNAL MODULE: ./node_modules/ahooks/es/utils/isBrowser.js
var isBrowser = __webpack_require__(52982);
;// CONCATENATED MODULE: ./node_modules/ahooks/es/useRequest/src/utils/isDocumentVisible.js

function isDocumentVisible() {
  if (isBrowser/* default */.Z) {
    return document.visibilityState !== 'hidden';
  }
  return true;
}
;// CONCATENATED MODULE: ./node_modules/ahooks/es/useRequest/src/utils/subscribeReVisible.js


var subscribeReVisible_listeners = [];
function subscribeReVisible_subscribe(listener) {
  subscribeReVisible_listeners.push(listener);
  return function unsubscribe() {
    var index = subscribeReVisible_listeners.indexOf(listener);
    subscribeReVisible_listeners.splice(index, 1);
  };
}
if (isBrowser/* default */.Z) {
  var revalidate = function () {
    if (!isDocumentVisible()) return;
    for (var i = 0; i < subscribeReVisible_listeners.length; i++) {
      var listener = subscribeReVisible_listeners[i];
      listener();
    }
  };
  window.addEventListener('visibilitychange', revalidate, false);
}
/* harmony default export */ var subscribeReVisible = (subscribeReVisible_subscribe);
;// CONCATENATED MODULE: ./node_modules/ahooks/es/useRequest/src/plugins/usePollingPlugin.js




var usePollingPlugin = function (fetchInstance, _a) {
  var pollingInterval = _a.pollingInterval,
    _b = _a.pollingWhenHidden,
    pollingWhenHidden = _b === void 0 ? true : _b,
    _c = _a.pollingErrorRetryCount,
    pollingErrorRetryCount = _c === void 0 ? -1 : _c;
  var timerRef = (0,react.useRef)();
  var unsubscribeRef = (0,react.useRef)();
  var countRef = (0,react.useRef)(0);
  var stopPolling = function () {
    var _a;
    if (timerRef.current) {
      clearTimeout(timerRef.current);
    }
    (_a = unsubscribeRef.current) === null || _a === void 0 ? void 0 : _a.call(unsubscribeRef);
  };
  (0,useUpdateEffect/* default */.Z)(function () {
    if (!pollingInterval) {
      stopPolling();
    }
  }, [pollingInterval]);
  if (!pollingInterval) {
    return {};
  }
  return {
    onBefore: function () {
      stopPolling();
    },
    onError: function () {
      countRef.current += 1;
    },
    onSuccess: function () {
      countRef.current = 0;
    },
    onFinally: function () {
      if (pollingErrorRetryCount === -1 ||
      // When an error occurs, the request is not repeated after pollingErrorRetryCount retries
      pollingErrorRetryCount !== -1 && countRef.current <= pollingErrorRetryCount) {
        timerRef.current = setTimeout(function () {
          // if pollingWhenHidden = false && document is hidden, then stop polling and subscribe revisible
          if (!pollingWhenHidden && !isDocumentVisible()) {
            unsubscribeRef.current = subscribeReVisible(function () {
              fetchInstance.refresh();
            });
          } else {
            fetchInstance.refresh();
          }
        }, pollingInterval);
      } else {
        countRef.current = 0;
      }
    },
    onCancel: function () {
      stopPolling();
    }
  };
};
/* harmony default export */ var plugins_usePollingPlugin = (usePollingPlugin);
;// CONCATENATED MODULE: ./node_modules/ahooks/es/useRequest/src/utils/limit.js

function limit(fn, timespan) {
  var pending = false;
  return function () {
    var args = [];
    for (var _i = 0; _i < arguments.length; _i++) {
      args[_i] = arguments[_i];
    }
    if (pending) return;
    pending = true;
    fn.apply(void 0, (0,tslib_es6/* __spreadArray */.ev)([], (0,tslib_es6/* __read */.CR)(args), false));
    setTimeout(function () {
      pending = false;
    }, timespan);
  };
}
;// CONCATENATED MODULE: ./node_modules/ahooks/es/useRequest/src/utils/isOnline.js

function isOnline() {
  if (isBrowser/* default */.Z && typeof navigator.onLine !== 'undefined') {
    return navigator.onLine;
  }
  return true;
}
;// CONCATENATED MODULE: ./node_modules/ahooks/es/useRequest/src/utils/subscribeFocus.js
// from swr



var subscribeFocus_listeners = [];
function subscribeFocus_subscribe(listener) {
  subscribeFocus_listeners.push(listener);
  return function unsubscribe() {
    var index = subscribeFocus_listeners.indexOf(listener);
    if (index > -1) {
      subscribeFocus_listeners.splice(index, 1);
    }
  };
}
if (isBrowser/* default */.Z) {
  var subscribeFocus_revalidate = function () {
    if (!isDocumentVisible() || !isOnline()) return;
    for (var i = 0; i < subscribeFocus_listeners.length; i++) {
      var listener = subscribeFocus_listeners[i];
      listener();
    }
  };
  window.addEventListener('visibilitychange', subscribeFocus_revalidate, false);
  window.addEventListener('focus', subscribeFocus_revalidate, false);
}
/* harmony default export */ var subscribeFocus = (subscribeFocus_subscribe);
;// CONCATENATED MODULE: ./node_modules/ahooks/es/useRequest/src/plugins/useRefreshOnWindowFocusPlugin.js




var useRefreshOnWindowFocusPlugin = function (fetchInstance, _a) {
  var refreshOnWindowFocus = _a.refreshOnWindowFocus,
    _b = _a.focusTimespan,
    focusTimespan = _b === void 0 ? 5000 : _b;
  var unsubscribeRef = (0,react.useRef)();
  var stopSubscribe = function () {
    var _a;
    (_a = unsubscribeRef.current) === null || _a === void 0 ? void 0 : _a.call(unsubscribeRef);
  };
  (0,react.useEffect)(function () {
    if (refreshOnWindowFocus) {
      var limitRefresh_1 = limit(fetchInstance.refresh.bind(fetchInstance), focusTimespan);
      unsubscribeRef.current = subscribeFocus(function () {
        limitRefresh_1();
      });
    }
    return function () {
      stopSubscribe();
    };
  }, [refreshOnWindowFocus, focusTimespan]);
  (0,useUnmount/* default */.Z)(function () {
    stopSubscribe();
  });
  return {};
};
/* harmony default export */ var plugins_useRefreshOnWindowFocusPlugin = (useRefreshOnWindowFocusPlugin);
;// CONCATENATED MODULE: ./node_modules/ahooks/es/useRequest/src/plugins/useRetryPlugin.js

var useRetryPlugin = function (fetchInstance, _a) {
  var retryInterval = _a.retryInterval,
    retryCount = _a.retryCount;
  var timerRef = (0,react.useRef)();
  var countRef = (0,react.useRef)(0);
  var triggerByRetry = (0,react.useRef)(false);
  if (!retryCount) {
    return {};
  }
  return {
    onBefore: function () {
      if (!triggerByRetry.current) {
        countRef.current = 0;
      }
      triggerByRetry.current = false;
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    },
    onSuccess: function () {
      countRef.current = 0;
    },
    onError: function () {
      countRef.current += 1;
      if (retryCount === -1 || countRef.current <= retryCount) {
        // Exponential backoff
        var timeout = retryInterval !== null && retryInterval !== void 0 ? retryInterval : Math.min(1000 * Math.pow(2, countRef.current), 30000);
        timerRef.current = setTimeout(function () {
          triggerByRetry.current = true;
          fetchInstance.refresh();
        }, timeout);
      } else {
        countRef.current = 0;
      }
    },
    onCancel: function () {
      countRef.current = 0;
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    }
  };
};
/* harmony default export */ var plugins_useRetryPlugin = (useRetryPlugin);
// EXTERNAL MODULE: ./node_modules/lodash/throttle.js
var throttle = __webpack_require__(23493);
var throttle_default = /*#__PURE__*/__webpack_require__.n(throttle);
;// CONCATENATED MODULE: ./node_modules/ahooks/es/useRequest/src/plugins/useThrottlePlugin.js



var useThrottlePlugin = function (fetchInstance, _a) {
  var throttleWait = _a.throttleWait,
    throttleLeading = _a.throttleLeading,
    throttleTrailing = _a.throttleTrailing;
  var throttledRef = (0,react.useRef)();
  var options = {};
  if (throttleLeading !== undefined) {
    options.leading = throttleLeading;
  }
  if (throttleTrailing !== undefined) {
    options.trailing = throttleTrailing;
  }
  (0,react.useEffect)(function () {
    if (throttleWait) {
      var _originRunAsync_1 = fetchInstance.runAsync.bind(fetchInstance);
      throttledRef.current = throttle_default()(function (callback) {
        callback();
      }, throttleWait, options);
      // throttle runAsync should be promise
      // https://github.com/lodash/lodash/issues/4400#issuecomment-834800398
      fetchInstance.runAsync = function () {
        var args = [];
        for (var _i = 0; _i < arguments.length; _i++) {
          args[_i] = arguments[_i];
        }
        return new Promise(function (resolve, reject) {
          var _a;
          (_a = throttledRef.current) === null || _a === void 0 ? void 0 : _a.call(throttledRef, function () {
            _originRunAsync_1.apply(void 0, (0,tslib_es6/* __spreadArray */.ev)([], (0,tslib_es6/* __read */.CR)(args), false)).then(resolve).catch(reject);
          });
        });
      };
      return function () {
        var _a;
        fetchInstance.runAsync = _originRunAsync_1;
        (_a = throttledRef.current) === null || _a === void 0 ? void 0 : _a.cancel();
      };
    }
  }, [throttleWait, throttleLeading, throttleTrailing]);
  if (!throttleWait) {
    return {};
  }
  return {
    onCancel: function () {
      var _a;
      (_a = throttledRef.current) === null || _a === void 0 ? void 0 : _a.cancel();
    }
  };
};
/* harmony default export */ var plugins_useThrottlePlugin = (useThrottlePlugin);
// EXTERNAL MODULE: ./node_modules/ahooks/es/useLatest/index.js
var useLatest = __webpack_require__(3930);
// EXTERNAL MODULE: ./node_modules/ahooks/es/useMemoizedFn/index.js
var useMemoizedFn = __webpack_require__(22638);
// EXTERNAL MODULE: ./node_modules/ahooks/es/utils/index.js
var utils = __webpack_require__(92770);
// EXTERNAL MODULE: ./node_modules/ahooks/es/utils/isDev.js
var isDev = __webpack_require__(31663);
;// CONCATENATED MODULE: ./node_modules/ahooks/es/useMount/index.js



var useMount = function (fn) {
  if (isDev/* default */.Z) {
    if (!(0,utils/* isFunction */.mf)(fn)) {
      console.error("useMount: parameter \`fn\` expected to be a function, but got \\"".concat(typeof fn, "\\"."));
    }
  }
  (0,react.useEffect)(function () {
    fn === null || fn === void 0 ? void 0 : fn();
  }, []);
};
/* harmony default export */ var es_useMount = (useMount);
;// CONCATENATED MODULE: ./node_modules/ahooks/es/useUpdate/index.js


var useUpdate = function () {
  var _a = (0,tslib_es6/* __read */.CR)((0,react.useState)({}), 2),
    setState = _a[1];
  return (0,react.useCallback)(function () {
    return setState({});
  }, []);
};
/* harmony default export */ var es_useUpdate = (useUpdate);
;// CONCATENATED MODULE: ./node_modules/ahooks/es/useRequest/src/Fetch.js

/* eslint-disable @typescript-eslint/no-parameter-properties */

var Fetch = /** @class */function () {
  function Fetch(serviceRef, options, subscribe, initState) {
    if (initState === void 0) {
      initState = {};
    }
    this.serviceRef = serviceRef;
    this.options = options;
    this.subscribe = subscribe;
    this.initState = initState;
    this.count = 0;
    this.state = {
      loading: false,
      params: undefined,
      data: undefined,
      error: undefined
    };
    this.state = (0,tslib_es6/* __assign */.pi)((0,tslib_es6/* __assign */.pi)((0,tslib_es6/* __assign */.pi)({}, this.state), {
      loading: !options.manual
    }), initState);
  }
  Fetch.prototype.setState = function (s) {
    if (s === void 0) {
      s = {};
    }
    this.state = (0,tslib_es6/* __assign */.pi)((0,tslib_es6/* __assign */.pi)({}, this.state), s);
    this.subscribe();
  };
  Fetch.prototype.runPluginHandler = function (event) {
    var rest = [];
    for (var _i = 1; _i < arguments.length; _i++) {
      rest[_i - 1] = arguments[_i];
    }
    // @ts-ignore
    var r = this.pluginImpls.map(function (i) {
      var _a;
      return (_a = i[event]) === null || _a === void 0 ? void 0 : _a.call.apply(_a, (0,tslib_es6/* __spreadArray */.ev)([i], (0,tslib_es6/* __read */.CR)(rest), false));
    }).filter(Boolean);
    return Object.assign.apply(Object, (0,tslib_es6/* __spreadArray */.ev)([{}], (0,tslib_es6/* __read */.CR)(r), false));
  };
  Fetch.prototype.runAsync = function () {
    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k;
    var params = [];
    for (var _i = 0; _i < arguments.length; _i++) {
      params[_i] = arguments[_i];
    }
    return (0,tslib_es6/* __awaiter */.mG)(this, void 0, void 0, function () {
      var currentCount, _l, _m, stopNow, _o, returnNow, state, servicePromise, res, error_1;
      var _p;
      return (0,tslib_es6/* __generator */.Jh)(this, function (_q) {
        switch (_q.label) {
          case 0:
            this.count += 1;
            currentCount = this.count;
            _l = this.runPluginHandler('onBefore', params), _m = _l.stopNow, stopNow = _m === void 0 ? false : _m, _o = _l.returnNow, returnNow = _o === void 0 ? false : _o, state = (0,tslib_es6/* __rest */._T)(_l, ["stopNow", "returnNow"]);
            // stop request
            if (stopNow) {
              return [2 /*return*/, new Promise(function () {})];
            }
            this.setState((0,tslib_es6/* __assign */.pi)({
              loading: true,
              params: params
            }, state));
            // return now
            if (returnNow) {
              return [2 /*return*/, Promise.resolve(state.data)];
            }
            (_b = (_a = this.options).onBefore) === null || _b === void 0 ? void 0 : _b.call(_a, params);
            _q.label = 1;
          case 1:
            _q.trys.push([1, 3,, 4]);
            servicePromise = this.runPluginHandler('onRequest', this.serviceRef.current, params).servicePromise;
            if (!servicePromise) {
              servicePromise = (_p = this.serviceRef).current.apply(_p, (0,tslib_es6/* __spreadArray */.ev)([], (0,tslib_es6/* __read */.CR)(params), false));
            }
            return [4 /*yield*/, servicePromise];
          case 2:
            res = _q.sent();
            if (currentCount !== this.count) {
              // prevent run.then when request is canceled
              return [2 /*return*/, new Promise(function () {})];
            }
            // const formattedResult = this.options.formatResultRef.current ? this.options.formatResultRef.current(res) : res;
            this.setState({
              data: res,
              error: undefined,
              loading: false
            });
            (_d = (_c = this.options).onSuccess) === null || _d === void 0 ? void 0 : _d.call(_c, res, params);
            this.runPluginHandler('onSuccess', res, params);
            (_f = (_e = this.options).onFinally) === null || _f === void 0 ? void 0 : _f.call(_e, params, res, undefined);
            if (currentCount === this.count) {
              this.runPluginHandler('onFinally', params, res, undefined);
            }
            return [2 /*return*/, res];
          case 3:
            error_1 = _q.sent();
            if (currentCount !== this.count) {
              // prevent run.then when request is canceled
              return [2 /*return*/, new Promise(function () {})];
            }
            this.setState({
              error: error_1,
              loading: false
            });
            (_h = (_g = this.options).onError) === null || _h === void 0 ? void 0 : _h.call(_g, error_1, params);
            this.runPluginHandler('onError', error_1, params);
            (_k = (_j = this.options).onFinally) === null || _k === void 0 ? void 0 : _k.call(_j, params, undefined, error_1);
            if (currentCount === this.count) {
              this.runPluginHandler('onFinally', params, undefined, error_1);
            }
            throw error_1;
          case 4:
            return [2 /*return*/];
        }
      });
    });
  };

  Fetch.prototype.run = function () {
    var _this = this;
    var params = [];
    for (var _i = 0; _i < arguments.length; _i++) {
      params[_i] = arguments[_i];
    }
    this.runAsync.apply(this, (0,tslib_es6/* __spreadArray */.ev)([], (0,tslib_es6/* __read */.CR)(params), false)).catch(function (error) {
      if (!_this.options.onError) {
        console.error(error);
      }
    });
  };
  Fetch.prototype.cancel = function () {
    this.count += 1;
    this.setState({
      loading: false
    });
    this.runPluginHandler('onCancel');
  };
  Fetch.prototype.refresh = function () {
    // @ts-ignore
    this.run.apply(this, (0,tslib_es6/* __spreadArray */.ev)([], (0,tslib_es6/* __read */.CR)(this.state.params || []), false));
  };
  Fetch.prototype.refreshAsync = function () {
    // @ts-ignore
    return this.runAsync.apply(this, (0,tslib_es6/* __spreadArray */.ev)([], (0,tslib_es6/* __read */.CR)(this.state.params || []), false));
  };
  Fetch.prototype.mutate = function (data) {
    var targetData = (0,utils/* isFunction */.mf)(data) ? data(this.state.data) : data;
    this.runPluginHandler('onMutate', targetData);
    this.setState({
      data: targetData
    });
  };
  return Fetch;
}();
/* harmony default export */ var src_Fetch = (Fetch);
;// CONCATENATED MODULE: ./node_modules/ahooks/es/useRequest/src/useRequestImplement.js









function useRequestImplement(service, options, plugins) {
  if (options === void 0) {
    options = {};
  }
  if (plugins === void 0) {
    plugins = [];
  }
  var _a = options.manual,
    manual = _a === void 0 ? false : _a,
    rest = (0,tslib_es6/* __rest */._T)(options, ["manual"]);
  if (isDev/* default */.Z) {
    if (options.defaultParams && !Array.isArray(options.defaultParams)) {
      console.warn("expected defaultParams is array, got ".concat(typeof options.defaultParams));
    }
  }
  var fetchOptions = (0,tslib_es6/* __assign */.pi)({
    manual: manual
  }, rest);
  var serviceRef = (0,useLatest/* default */.Z)(service);
  var update = es_useUpdate();
  var fetchInstance = useCreation(function () {
    var initState = plugins.map(function (p) {
      var _a;
      return (_a = p === null || p === void 0 ? void 0 : p.onInit) === null || _a === void 0 ? void 0 : _a.call(p, fetchOptions);
    }).filter(Boolean);
    return new src_Fetch(serviceRef, fetchOptions, update, Object.assign.apply(Object, (0,tslib_es6/* __spreadArray */.ev)([{}], (0,tslib_es6/* __read */.CR)(initState), false)));
  }, []);
  fetchInstance.options = fetchOptions;
  // run all plugins hooks
  fetchInstance.pluginImpls = plugins.map(function (p) {
    return p(fetchInstance, fetchOptions);
  });
  es_useMount(function () {
    if (!manual) {
      // useCachePlugin can set fetchInstance.state.params from cache when init
      var params = fetchInstance.state.params || options.defaultParams || [];
      // @ts-ignore
      fetchInstance.run.apply(fetchInstance, (0,tslib_es6/* __spreadArray */.ev)([], (0,tslib_es6/* __read */.CR)(params), false));
    }
  });
  (0,useUnmount/* default */.Z)(function () {
    fetchInstance.cancel();
  });
  return {
    loading: fetchInstance.state.loading,
    data: fetchInstance.state.data,
    error: fetchInstance.state.error,
    params: fetchInstance.state.params || [],
    cancel: (0,useMemoizedFn/* default */.Z)(fetchInstance.cancel.bind(fetchInstance)),
    refresh: (0,useMemoizedFn/* default */.Z)(fetchInstance.refresh.bind(fetchInstance)),
    refreshAsync: (0,useMemoizedFn/* default */.Z)(fetchInstance.refreshAsync.bind(fetchInstance)),
    run: (0,useMemoizedFn/* default */.Z)(fetchInstance.run.bind(fetchInstance)),
    runAsync: (0,useMemoizedFn/* default */.Z)(fetchInstance.runAsync.bind(fetchInstance)),
    mutate: (0,useMemoizedFn/* default */.Z)(fetchInstance.mutate.bind(fetchInstance))
  };
}
/* harmony default export */ var src_useRequestImplement = (useRequestImplement);
;// CONCATENATED MODULE: ./node_modules/ahooks/es/useRequest/src/useRequest.js










// function useRequest<TData, TParams extends any[], TFormated, TTFormated extends TFormated = any>(
//   service: Service<TData, TParams>,
//   options: OptionsWithFormat<TData, TParams, TFormated, TTFormated>,
//   plugins?: Plugin<TData, TParams>[],
// ): Result<TFormated, TParams>
// function useRequest<TData, TParams extends any[]>(
//   service: Service<TData, TParams>,
//   options?: OptionsWithoutFormat<TData, TParams>,
//   plugins?: Plugin<TData, TParams>[],
// ): Result<TData, TParams>
function useRequest(service, options, plugins) {
  return src_useRequestImplement(service, options, (0,tslib_es6/* __spreadArray */.ev)((0,tslib_es6/* __spreadArray */.ev)([], (0,tslib_es6/* __read */.CR)(plugins || []), false), [plugins_useDebouncePlugin, plugins_useLoadingDelayPlugin, plugins_usePollingPlugin, plugins_useRefreshOnWindowFocusPlugin, plugins_useThrottlePlugin, plugins_useAutoRunPlugin, plugins_useCachePlugin, plugins_useRetryPlugin], false));
}
/* harmony default export */ var src_useRequest = (useRequest);
;// CONCATENATED MODULE: ./node_modules/ahooks/es/useRequest/index.js



/* harmony default export */ var es_useRequest = (src_useRequest);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNzk3MTguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBQThDO0FBQ2Y7QUFDd0I7QUFDdkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsZ0JBQU07QUFDekI7QUFDQSxFQUFFLGtDQUFlO0FBQ2pCO0FBQ0E7QUFDQSw2Q0FBNkMsbUNBQWEsS0FBSyw0QkFBTTtBQUNyRTtBQUNBLEdBQUc7QUFDSCxFQUFFLGtDQUFlO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBLEdBQUcsRUFBRSxtQ0FBYSxLQUFLLDRCQUFNO0FBQzdCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZEQUFlLGdCQUFnQixFOztBQ3BEaEI7QUFDZjtBQUNBLGtCQUFrQixvQkFBb0I7QUFDdEM7QUFDQTtBQUNBO0FBQ0EsQzs7QUNOK0I7QUFDZ0I7QUFDaEM7QUFDZixnQkFBZ0IsZ0JBQU07QUFDdEI7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNILHdDQUF3QyxXQUFXO0FBQ25EO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDOzs7O0FDZGlDO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQSxpQkFBaUIsOEJBQVEsQ0FBQyw4QkFBUSxHQUFHO0FBQ3JDO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCxJQUFJO0FBQ0o7QUFDQTtBQUNBOzs7QUM5QkE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0EsR0FBRztBQUNIOzs7QUNmQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7O0FDakI4QztBQUNmO0FBQ2dCO0FBQ0Y7QUFDTztBQUNxQjtBQUNaO0FBQzdEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1QkFBdUIsZ0JBQU07QUFDN0IsMEJBQTBCLGdCQUFNO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTixNQUFNLFFBQVE7QUFDZDtBQUNBLElBQUksT0FBTztBQUNYO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkI7QUFDQSxFQUFFLFdBQVc7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZCQUE2QixTQUFTO0FBQ3RDO0FBQ0E7QUFDQSxPQUFPO0FBQ1AsS0FBSztBQUNMLEdBQUc7QUFDSCxFQUFFLDZCQUFVO0FBQ1o7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBLDJCQUEyQixlQUFlO0FBQzFDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZDQUE2QyxtQ0FBYSxLQUFLLDRCQUFNO0FBQ3JFO0FBQ0EsTUFBTSxlQUFlO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQSxpQ0FBaUMsU0FBUztBQUMxQztBQUNBO0FBQ0EsV0FBVztBQUNYLFNBQVM7QUFDVDtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQSxpQ0FBaUMsU0FBUztBQUMxQztBQUNBO0FBQ0EsV0FBVztBQUNYLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJEQUFlLGNBQWMsRTs7Ozs7QUN4SWlCO0FBQ1A7QUFDWTtBQUNuRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCLGdCQUFNO0FBQzNCLGdCQUFnQixpQkFBTztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNILEVBQUUsbUJBQVM7QUFDWDtBQUNBO0FBQ0EsNkJBQTZCLGtCQUFRO0FBQ3JDO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUJBQXlCLHVCQUF1QjtBQUNoRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNENBQTRDLG1DQUFhLEtBQUssNEJBQU07QUFDcEUsV0FBVztBQUNYLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhEQUFlLGlCQUFpQixFOztBQzNERDtBQUMvQjtBQUNBO0FBQ0E7QUFDQSxpQkFBaUIsZ0JBQU07QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVztBQUNYLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrRUFBZSxxQkFBcUIsRTs7OztBQ3RDYTtBQUNsQztBQUNmLE1BQU0sd0JBQVM7QUFDZjtBQUNBO0FBQ0E7QUFDQSxDOztBQ05pRDtBQUNHO0FBQ3BELElBQUksNEJBQVM7QUFDYixTQUFTLDRCQUFTO0FBQ2xCLEVBQUUsNEJBQVM7QUFDWDtBQUNBLGdCQUFnQiw0QkFBUztBQUN6QixJQUFJLDRCQUFTO0FBQ2I7QUFDQTtBQUNBLElBQUksd0JBQVM7QUFDYjtBQUNBLFNBQVMsaUJBQWlCO0FBQzFCLG9CQUFvQixJQUFJLDRCQUFTLFNBQVM7QUFDMUMscUJBQXFCLDRCQUFTO0FBQzlCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1REFBZSw0QkFBUyxFOztBQ3BCTztBQUN3QjtBQUNJO0FBQ0U7QUFDN0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCLGdCQUFNO0FBQ3ZCLHVCQUF1QixnQkFBTTtBQUM3QixpQkFBaUIsZ0JBQU07QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxFQUFFLGtDQUFlO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFDQUFxQyxpQkFBaUI7QUFDdEQscUNBQXFDLGtCQUFrQjtBQUN2RDtBQUNBLGFBQWE7QUFDYixZQUFZO0FBQ1o7QUFDQTtBQUNBLFNBQVM7QUFDVCxRQUFRO0FBQ1I7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkRBQWUsZ0JBQWdCLEU7O0FDN0RlO0FBQy9CO0FBQ2Y7QUFDQTtBQUNBO0FBQ0EscUJBQXFCLHVCQUF1QjtBQUM1QztBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQixtQ0FBYSxLQUFLLDRCQUFNO0FBQzdDO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQSxDOztBQ2ZpRDtBQUNsQztBQUNmLE1BQU0sd0JBQVM7QUFDZjtBQUNBO0FBQ0E7QUFDQSxDOztBQ05BO0FBQ2lEO0FBQ0c7QUFDbEI7QUFDbEMsSUFBSSx3QkFBUztBQUNiLFNBQVMsd0JBQVM7QUFDbEIsRUFBRSx3QkFBUztBQUNYO0FBQ0EsZ0JBQWdCLHdCQUFTO0FBQ3pCO0FBQ0EsTUFBTSx3QkFBUztBQUNmO0FBQ0E7QUFDQTtBQUNBLElBQUksd0JBQVM7QUFDYixNQUFNLHlCQUFVO0FBQ2hCLFNBQVMsaUJBQWlCLE9BQU8sUUFBUTtBQUN6QyxvQkFBb0IsSUFBSSx3QkFBUyxTQUFTO0FBQzFDLHFCQUFxQix3QkFBUztBQUM5QjtBQUNBO0FBQ0E7QUFDQSw4Q0FBOEMseUJBQVU7QUFDeEQsbUNBQW1DLHlCQUFVO0FBQzdDO0FBQ0EsbURBQWUsd0JBQVMsRTs7QUN6QmtCO0FBQ0c7QUFDVjtBQUNrQjtBQUNyRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1QixnQkFBTTtBQUM3QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEVBQUUsbUJBQVM7QUFDWDtBQUNBLDJCQUEyQixLQUFLO0FBQ2hDLCtCQUErQixjQUFjO0FBQzdDO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNILEVBQUUsNkJBQVU7QUFDWjtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0EsMEVBQWUsNkJBQTZCLEU7O0FDN0JiO0FBQy9CO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQixnQkFBTTtBQUN2QixpQkFBaUIsZ0JBQU07QUFDdkIsdUJBQXVCLGdCQUFNO0FBQzdCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsUUFBUTtBQUNSO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJEQUFlLGNBQWMsRTs7Ozs7QUM1Q2lCO0FBQ1A7QUFDRztBQUMxQztBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQixnQkFBTTtBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEVBQUUsbUJBQVM7QUFDWDtBQUNBO0FBQ0EsNkJBQTZCLGtCQUFRO0FBQ3JDO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUJBQXlCLHVCQUF1QjtBQUNoRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNENBQTRDLG1DQUFhLEtBQUssNEJBQU07QUFDcEUsV0FBVztBQUNYLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhEQUFlLGlCQUFpQixFOzs7Ozs7Ozs7O0FDcERFO0FBQ0k7QUFDSDtBQUNuQztBQUNBLE1BQU0sb0JBQUs7QUFDWCxTQUFTLDRCQUFVO0FBQ25CO0FBQ0E7QUFDQTtBQUNBLEVBQUUsbUJBQVM7QUFDWDtBQUNBLEdBQUc7QUFDSDtBQUNBLGdEQUFlLFFBQVEsRTs7QUNiUTtBQUNlO0FBQzlDO0FBQ0EsV0FBVyw0QkFBTSxDQUFDLGtCQUFRLEdBQUc7QUFDN0I7QUFDQSxTQUFTLHFCQUFXO0FBQ3BCLHNCQUFzQjtBQUN0QixHQUFHO0FBQ0g7QUFDQSxpREFBZSxTQUFTLEU7O0FDVGdFO0FBQ3hGO0FBQ3lDO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCLDhCQUFRLENBQUMsOEJBQVEsQ0FBQyw4QkFBUSxHQUFHO0FBQzlDO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUIsOEJBQVEsQ0FBQyw4QkFBUSxHQUFHO0FBQ3JDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCLHVCQUF1QjtBQUM1QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0ZBQW9GLG1DQUFhLE1BQU0sNEJBQU07QUFDN0csS0FBSztBQUNMLHVDQUF1QyxtQ0FBYSxJQUFJLEdBQUcsNEJBQU07QUFDakU7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUIsdUJBQXVCO0FBQzVDO0FBQ0E7QUFDQSxXQUFXLCtCQUFTO0FBQ3BCO0FBQ0E7QUFDQSxhQUFhLGlDQUFXO0FBQ3hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0xBQXNMLDRCQUFNO0FBQzVMO0FBQ0E7QUFDQSw4REFBOEQ7QUFDOUQ7QUFDQSwwQkFBMEIsOEJBQVE7QUFDbEM7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdFQUF3RSxtQ0FBYSxLQUFLLDRCQUFNO0FBQ2hHO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhEQUE4RDtBQUM5RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhEQUE4RDtBQUM5RDtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUCxLQUFLO0FBQ0w7O0FBRUE7QUFDQTtBQUNBO0FBQ0EscUJBQXFCLHVCQUF1QjtBQUM1QztBQUNBO0FBQ0EsOEJBQThCLG1DQUFhLEtBQUssNEJBQU07QUFDdEQ7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUIsbUNBQWEsS0FBSyw0QkFBTTtBQUNqRDtBQUNBO0FBQ0E7QUFDQSxxQ0FBcUMsbUNBQWEsS0FBSyw0QkFBTTtBQUM3RDtBQUNBO0FBQ0EscUJBQXFCLDRCQUFVO0FBQy9CO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsQ0FBQztBQUNELDhDQUFlLEtBQUssRTs7QUM3SjRDO0FBQ3BCO0FBQ0o7QUFDUTtBQUNWO0FBQ0k7QUFDRjtBQUNGO0FBQ1Y7QUFDNUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyw0QkFBTTtBQUNqQixNQUFNLG9CQUFLO0FBQ1g7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUIsOEJBQVE7QUFDN0I7QUFDQSxHQUFHO0FBQ0gsbUJBQW1CLDRCQUFTO0FBQzVCLGVBQWUsWUFBUztBQUN4QixzQkFBc0IsV0FBVztBQUNqQztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsZUFBZSxTQUFLLCtEQUErRCxtQ0FBYSxJQUFJLEdBQUcsNEJBQU07QUFDN0csR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNILEVBQUUsV0FBUTtBQUNWO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkNBQTZDLG1DQUFhLEtBQUssNEJBQU07QUFDckU7QUFDQSxHQUFHO0FBQ0gsRUFBRSw2QkFBVTtBQUNaO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLGdDQUFhO0FBQ3pCLGFBQWEsZ0NBQWE7QUFDMUIsa0JBQWtCLGdDQUFhO0FBQy9CLFNBQVMsZ0NBQWE7QUFDdEIsY0FBYyxnQ0FBYTtBQUMzQixZQUFZLGdDQUFhO0FBQ3pCO0FBQ0E7QUFDQSw0REFBZSxtQkFBbUIsRTs7QUNqRVk7QUFDWTtBQUNKO0FBQ007QUFDUTtBQUNWO0FBQzBCO0FBQzlCO0FBQ007QUFDSjtBQUN4RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUyx1QkFBbUIsbUJBQW1CLG1DQUFhLENBQUMsbUNBQWEsS0FBSyw0QkFBTSwwQkFBMEIseUJBQWlCLEVBQUUsNkJBQXFCLEVBQUUsd0JBQWdCLEVBQUUscUNBQTZCLEVBQUUseUJBQWlCLEVBQUUsd0JBQWdCLEVBQUUsc0JBQWMsRUFBRSxzQkFBYztBQUM3UTtBQUNBLG1EQUFlLFVBQVUsRTs7QUN2QmlCO0FBQ0s7QUFDekI7QUFDdEIsa0RBQWUsY0FBVSIsInNvdXJjZXMiOlsid2VicGFjazovL2FudC1kZXNpZ24tcHJvLy4vbm9kZV9tb2R1bGVzL2Fob29rcy9lcy91c2VSZXF1ZXN0L3NyYy9wbHVnaW5zL3VzZUF1dG9SdW5QbHVnaW4uanM/OGFlOSIsIndlYnBhY2s6Ly9hbnQtZGVzaWduLXByby8uL25vZGVfbW9kdWxlcy9haG9va3MvZXMvdXRpbHMvZGVwc0FyZVNhbWUuanM/NGEyNiIsIndlYnBhY2s6Ly9hbnQtZGVzaWduLXByby8uL25vZGVfbW9kdWxlcy9haG9va3MvZXMvdXNlQ3JlYXRpb24vaW5kZXguanM/MmVjMyIsIndlYnBhY2s6Ly9hbnQtZGVzaWduLXByby8uL25vZGVfbW9kdWxlcy9haG9va3MvZXMvdXNlUmVxdWVzdC9zcmMvdXRpbHMvY2FjaGUuanM/NTQzNCIsIndlYnBhY2s6Ly9hbnQtZGVzaWduLXByby8uL25vZGVfbW9kdWxlcy9haG9va3MvZXMvdXNlUmVxdWVzdC9zcmMvdXRpbHMvY2FjaGVQcm9taXNlLmpzP2U1YzYiLCJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvYWhvb2tzL2VzL3VzZVJlcXVlc3Qvc3JjL3V0aWxzL2NhY2hlU3Vic2NyaWJlLmpzPzViZjAiLCJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvYWhvb2tzL2VzL3VzZVJlcXVlc3Qvc3JjL3BsdWdpbnMvdXNlQ2FjaGVQbHVnaW4uanM/ODFmYiIsIndlYnBhY2s6Ly9hbnQtZGVzaWduLXByby8uL25vZGVfbW9kdWxlcy9haG9va3MvZXMvdXNlUmVxdWVzdC9zcmMvcGx1Z2lucy91c2VEZWJvdW5jZVBsdWdpbi5qcz9mMjA1Iiwid2VicGFjazovL2FudC1kZXNpZ24tcHJvLy4vbm9kZV9tb2R1bGVzL2Fob29rcy9lcy91c2VSZXF1ZXN0L3NyYy9wbHVnaW5zL3VzZUxvYWRpbmdEZWxheVBsdWdpbi5qcz8xZWZlIiwid2VicGFjazovL2FudC1kZXNpZ24tcHJvLy4vbm9kZV9tb2R1bGVzL2Fob29rcy9lcy91c2VSZXF1ZXN0L3NyYy91dGlscy9pc0RvY3VtZW50VmlzaWJsZS5qcz80MTkzIiwid2VicGFjazovL2FudC1kZXNpZ24tcHJvLy4vbm9kZV9tb2R1bGVzL2Fob29rcy9lcy91c2VSZXF1ZXN0L3NyYy91dGlscy9zdWJzY3JpYmVSZVZpc2libGUuanM/NDM5YSIsIndlYnBhY2s6Ly9hbnQtZGVzaWduLXByby8uL25vZGVfbW9kdWxlcy9haG9va3MvZXMvdXNlUmVxdWVzdC9zcmMvcGx1Z2lucy91c2VQb2xsaW5nUGx1Z2luLmpzPzgxZDciLCJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvYWhvb2tzL2VzL3VzZVJlcXVlc3Qvc3JjL3V0aWxzL2xpbWl0LmpzPzYwOTQiLCJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvYWhvb2tzL2VzL3VzZVJlcXVlc3Qvc3JjL3V0aWxzL2lzT25saW5lLmpzP2ViYTAiLCJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvYWhvb2tzL2VzL3VzZVJlcXVlc3Qvc3JjL3V0aWxzL3N1YnNjcmliZUZvY3VzLmpzP2MxMTUiLCJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvYWhvb2tzL2VzL3VzZVJlcXVlc3Qvc3JjL3BsdWdpbnMvdXNlUmVmcmVzaE9uV2luZG93Rm9jdXNQbHVnaW4uanM/ZmM0NSIsIndlYnBhY2s6Ly9hbnQtZGVzaWduLXByby8uL25vZGVfbW9kdWxlcy9haG9va3MvZXMvdXNlUmVxdWVzdC9zcmMvcGx1Z2lucy91c2VSZXRyeVBsdWdpbi5qcz85YWNiIiwid2VicGFjazovL2FudC1kZXNpZ24tcHJvLy4vbm9kZV9tb2R1bGVzL2Fob29rcy9lcy91c2VSZXF1ZXN0L3NyYy9wbHVnaW5zL3VzZVRocm90dGxlUGx1Z2luLmpzPzk4YzMiLCJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvYWhvb2tzL2VzL3VzZU1vdW50L2luZGV4LmpzPzYzNTMiLCJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvYWhvb2tzL2VzL3VzZVVwZGF0ZS9pbmRleC5qcz8zODMzIiwid2VicGFjazovL2FudC1kZXNpZ24tcHJvLy4vbm9kZV9tb2R1bGVzL2Fob29rcy9lcy91c2VSZXF1ZXN0L3NyYy9GZXRjaC5qcz8yZjc1Iiwid2VicGFjazovL2FudC1kZXNpZ24tcHJvLy4vbm9kZV9tb2R1bGVzL2Fob29rcy9lcy91c2VSZXF1ZXN0L3NyYy91c2VSZXF1ZXN0SW1wbGVtZW50LmpzP2EwODIiLCJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvYWhvb2tzL2VzL3VzZVJlcXVlc3Qvc3JjL3VzZVJlcXVlc3QuanM/OTNlMiIsIndlYnBhY2s6Ly9hbnQtZGVzaWduLXByby8uL25vZGVfbW9kdWxlcy9haG9va3MvZXMvdXNlUmVxdWVzdC9pbmRleC5qcz83YzI3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IF9fcmVhZCwgX19zcHJlYWRBcnJheSB9IGZyb20gXCJ0c2xpYlwiO1xuaW1wb3J0IHsgdXNlUmVmIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHVzZVVwZGF0ZUVmZmVjdCBmcm9tICcuLi8uLi8uLi91c2VVcGRhdGVFZmZlY3QnO1xuLy8gc3VwcG9ydCByZWZyZXNoRGVwcyAmIHJlYWR5XG52YXIgdXNlQXV0b1J1blBsdWdpbiA9IGZ1bmN0aW9uIChmZXRjaEluc3RhbmNlLCBfYSkge1xuICB2YXIgbWFudWFsID0gX2EubWFudWFsLFxuICAgIF9iID0gX2EucmVhZHksXG4gICAgcmVhZHkgPSBfYiA9PT0gdm9pZCAwID8gdHJ1ZSA6IF9iLFxuICAgIF9jID0gX2EuZGVmYXVsdFBhcmFtcyxcbiAgICBkZWZhdWx0UGFyYW1zID0gX2MgPT09IHZvaWQgMCA/IFtdIDogX2MsXG4gICAgX2QgPSBfYS5yZWZyZXNoRGVwcyxcbiAgICByZWZyZXNoRGVwcyA9IF9kID09PSB2b2lkIDAgPyBbXSA6IF9kLFxuICAgIHJlZnJlc2hEZXBzQWN0aW9uID0gX2EucmVmcmVzaERlcHNBY3Rpb247XG4gIHZhciBoYXNBdXRvUnVuID0gdXNlUmVmKGZhbHNlKTtcbiAgaGFzQXV0b1J1bi5jdXJyZW50ID0gZmFsc2U7XG4gIHVzZVVwZGF0ZUVmZmVjdChmdW5jdGlvbiAoKSB7XG4gICAgaWYgKCFtYW51YWwgJiYgcmVhZHkpIHtcbiAgICAgIGhhc0F1dG9SdW4uY3VycmVudCA9IHRydWU7XG4gICAgICBmZXRjaEluc3RhbmNlLnJ1bi5hcHBseShmZXRjaEluc3RhbmNlLCBfX3NwcmVhZEFycmF5KFtdLCBfX3JlYWQoZGVmYXVsdFBhcmFtcyksIGZhbHNlKSk7XG4gICAgfVxuICB9LCBbcmVhZHldKTtcbiAgdXNlVXBkYXRlRWZmZWN0KGZ1bmN0aW9uICgpIHtcbiAgICBpZiAoaGFzQXV0b1J1bi5jdXJyZW50KSB7XG4gICAgICByZXR1cm47XG4gICAgfVxuICAgIGlmICghbWFudWFsKSB7XG4gICAgICBoYXNBdXRvUnVuLmN1cnJlbnQgPSB0cnVlO1xuICAgICAgaWYgKHJlZnJlc2hEZXBzQWN0aW9uKSB7XG4gICAgICAgIHJlZnJlc2hEZXBzQWN0aW9uKCk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBmZXRjaEluc3RhbmNlLnJlZnJlc2goKTtcbiAgICAgIH1cbiAgICB9XG4gIH0sIF9fc3ByZWFkQXJyYXkoW10sIF9fcmVhZChyZWZyZXNoRGVwcyksIGZhbHNlKSk7XG4gIHJldHVybiB7XG4gICAgb25CZWZvcmU6IGZ1bmN0aW9uICgpIHtcbiAgICAgIGlmICghcmVhZHkpIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICBzdG9wTm93OiB0cnVlXG4gICAgICAgIH07XG4gICAgICB9XG4gICAgfVxuICB9O1xufTtcbnVzZUF1dG9SdW5QbHVnaW4ub25Jbml0ID0gZnVuY3Rpb24gKF9hKSB7XG4gIHZhciBfYiA9IF9hLnJlYWR5LFxuICAgIHJlYWR5ID0gX2IgPT09IHZvaWQgMCA/IHRydWUgOiBfYixcbiAgICBtYW51YWwgPSBfYS5tYW51YWw7XG4gIHJldHVybiB7XG4gICAgbG9hZGluZzogIW1hbnVhbCAmJiByZWFkeVxuICB9O1xufTtcbmV4cG9ydCBkZWZhdWx0IHVzZUF1dG9SdW5QbHVnaW47IiwiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gZGVwc0FyZVNhbWUob2xkRGVwcywgZGVwcykge1xuICBpZiAob2xkRGVwcyA9PT0gZGVwcykgcmV0dXJuIHRydWU7XG4gIGZvciAodmFyIGkgPSAwOyBpIDwgb2xkRGVwcy5sZW5ndGg7IGkrKykge1xuICAgIGlmICghT2JqZWN0LmlzKG9sZERlcHNbaV0sIGRlcHNbaV0pKSByZXR1cm4gZmFsc2U7XG4gIH1cbiAgcmV0dXJuIHRydWU7XG59IiwiaW1wb3J0IHsgdXNlUmVmIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IGRlcHNBcmVTYW1lIGZyb20gJy4uL3V0aWxzL2RlcHNBcmVTYW1lJztcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHVzZUNyZWF0aW9uKGZhY3RvcnksIGRlcHMpIHtcbiAgdmFyIGN1cnJlbnQgPSB1c2VSZWYoe1xuICAgIGRlcHM6IGRlcHMsXG4gICAgb2JqOiB1bmRlZmluZWQsXG4gICAgaW5pdGlhbGl6ZWQ6IGZhbHNlXG4gIH0pLmN1cnJlbnQ7XG4gIGlmIChjdXJyZW50LmluaXRpYWxpemVkID09PSBmYWxzZSB8fCAhZGVwc0FyZVNhbWUoY3VycmVudC5kZXBzLCBkZXBzKSkge1xuICAgIGN1cnJlbnQuZGVwcyA9IGRlcHM7XG4gICAgY3VycmVudC5vYmogPSBmYWN0b3J5KCk7XG4gICAgY3VycmVudC5pbml0aWFsaXplZCA9IHRydWU7XG4gIH1cbiAgcmV0dXJuIGN1cnJlbnQub2JqO1xufSIsImltcG9ydCB7IF9fYXNzaWduIH0gZnJvbSBcInRzbGliXCI7XG52YXIgY2FjaGUgPSBuZXcgTWFwKCk7XG52YXIgc2V0Q2FjaGUgPSBmdW5jdGlvbiAoa2V5LCBjYWNoZVRpbWUsIGNhY2hlZERhdGEpIHtcbiAgdmFyIGN1cnJlbnRDYWNoZSA9IGNhY2hlLmdldChrZXkpO1xuICBpZiAoY3VycmVudENhY2hlID09PSBudWxsIHx8IGN1cnJlbnRDYWNoZSA9PT0gdm9pZCAwID8gdm9pZCAwIDogY3VycmVudENhY2hlLnRpbWVyKSB7XG4gICAgY2xlYXJUaW1lb3V0KGN1cnJlbnRDYWNoZS50aW1lcik7XG4gIH1cbiAgdmFyIHRpbWVyID0gdW5kZWZpbmVkO1xuICBpZiAoY2FjaGVUaW1lID4gLTEpIHtcbiAgICAvLyBpZiBjYWNoZSBvdXQsIGNsZWFyIGl0XG4gICAgdGltZXIgPSBzZXRUaW1lb3V0KGZ1bmN0aW9uICgpIHtcbiAgICAgIGNhY2hlLmRlbGV0ZShrZXkpO1xuICAgIH0sIGNhY2hlVGltZSk7XG4gIH1cbiAgY2FjaGUuc2V0KGtleSwgX19hc3NpZ24oX19hc3NpZ24oe30sIGNhY2hlZERhdGEpLCB7XG4gICAgdGltZXI6IHRpbWVyXG4gIH0pKTtcbn07XG52YXIgZ2V0Q2FjaGUgPSBmdW5jdGlvbiAoa2V5KSB7XG4gIHJldHVybiBjYWNoZS5nZXQoa2V5KTtcbn07XG52YXIgY2xlYXJDYWNoZSA9IGZ1bmN0aW9uIChrZXkpIHtcbiAgaWYgKGtleSkge1xuICAgIHZhciBjYWNoZUtleXMgPSBBcnJheS5pc0FycmF5KGtleSkgPyBrZXkgOiBba2V5XTtcbiAgICBjYWNoZUtleXMuZm9yRWFjaChmdW5jdGlvbiAoY2FjaGVLZXkpIHtcbiAgICAgIHJldHVybiBjYWNoZS5kZWxldGUoY2FjaGVLZXkpO1xuICAgIH0pO1xuICB9IGVsc2Uge1xuICAgIGNhY2hlLmNsZWFyKCk7XG4gIH1cbn07XG5leHBvcnQgeyBnZXRDYWNoZSwgc2V0Q2FjaGUsIGNsZWFyQ2FjaGUgfTsiLCJ2YXIgY2FjaGVQcm9taXNlID0gbmV3IE1hcCgpO1xudmFyIGdldENhY2hlUHJvbWlzZSA9IGZ1bmN0aW9uIChjYWNoZUtleSkge1xuICByZXR1cm4gY2FjaGVQcm9taXNlLmdldChjYWNoZUtleSk7XG59O1xudmFyIHNldENhY2hlUHJvbWlzZSA9IGZ1bmN0aW9uIChjYWNoZUtleSwgcHJvbWlzZSkge1xuICAvLyBTaG91bGQgY2FjaGUgdGhlIHNhbWUgcHJvbWlzZSwgY2Fubm90IGJlIHByb21pc2UuZmluYWxseVxuICAvLyBCZWNhdXNlIHRoZSBwcm9taXNlLmZpbmFsbHkgd2lsbCBjaGFuZ2UgdGhlIHJlZmVyZW5jZSBvZiB0aGUgcHJvbWlzZVxuICBjYWNoZVByb21pc2Uuc2V0KGNhY2hlS2V5LCBwcm9taXNlKTtcbiAgLy8gbm8gdXNlIHByb21pc2UuZmluYWxseSBmb3IgY29tcGF0aWJpbGl0eVxuICBwcm9taXNlLnRoZW4oZnVuY3Rpb24gKHJlcykge1xuICAgIGNhY2hlUHJvbWlzZS5kZWxldGUoY2FjaGVLZXkpO1xuICAgIHJldHVybiByZXM7XG4gIH0pLmNhdGNoKGZ1bmN0aW9uICgpIHtcbiAgICBjYWNoZVByb21pc2UuZGVsZXRlKGNhY2hlS2V5KTtcbiAgfSk7XG59O1xuZXhwb3J0IHsgZ2V0Q2FjaGVQcm9taXNlLCBzZXRDYWNoZVByb21pc2UgfTsiLCJ2YXIgbGlzdGVuZXJzID0ge307XG52YXIgdHJpZ2dlciA9IGZ1bmN0aW9uIChrZXksIGRhdGEpIHtcbiAgaWYgKGxpc3RlbmVyc1trZXldKSB7XG4gICAgbGlzdGVuZXJzW2tleV0uZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkge1xuICAgICAgcmV0dXJuIGl0ZW0oZGF0YSk7XG4gICAgfSk7XG4gIH1cbn07XG52YXIgc3Vic2NyaWJlID0gZnVuY3Rpb24gKGtleSwgbGlzdGVuZXIpIHtcbiAgaWYgKCFsaXN0ZW5lcnNba2V5XSkge1xuICAgIGxpc3RlbmVyc1trZXldID0gW107XG4gIH1cbiAgbGlzdGVuZXJzW2tleV0ucHVzaChsaXN0ZW5lcik7XG4gIHJldHVybiBmdW5jdGlvbiB1bnN1YnNjcmliZSgpIHtcbiAgICB2YXIgaW5kZXggPSBsaXN0ZW5lcnNba2V5XS5pbmRleE9mKGxpc3RlbmVyKTtcbiAgICBsaXN0ZW5lcnNba2V5XS5zcGxpY2UoaW5kZXgsIDEpO1xuICB9O1xufTtcbmV4cG9ydCB7IHRyaWdnZXIsIHN1YnNjcmliZSB9OyIsImltcG9ydCB7IF9fcmVhZCwgX19zcHJlYWRBcnJheSB9IGZyb20gXCJ0c2xpYlwiO1xuaW1wb3J0IHsgdXNlUmVmIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHVzZUNyZWF0aW9uIGZyb20gJy4uLy4uLy4uL3VzZUNyZWF0aW9uJztcbmltcG9ydCB1c2VVbm1vdW50IGZyb20gJy4uLy4uLy4uL3VzZVVubW91bnQnO1xuaW1wb3J0IHsgc2V0Q2FjaGUsIGdldENhY2hlIH0gZnJvbSAnLi4vdXRpbHMvY2FjaGUnO1xuaW1wb3J0IHsgc2V0Q2FjaGVQcm9taXNlLCBnZXRDYWNoZVByb21pc2UgfSBmcm9tICcuLi91dGlscy9jYWNoZVByb21pc2UnO1xuaW1wb3J0IHsgdHJpZ2dlciwgc3Vic2NyaWJlIH0gZnJvbSAnLi4vdXRpbHMvY2FjaGVTdWJzY3JpYmUnO1xudmFyIHVzZUNhY2hlUGx1Z2luID0gZnVuY3Rpb24gKGZldGNoSW5zdGFuY2UsIF9hKSB7XG4gIHZhciBjYWNoZUtleSA9IF9hLmNhY2hlS2V5LFxuICAgIF9iID0gX2EuY2FjaGVUaW1lLFxuICAgIGNhY2hlVGltZSA9IF9iID09PSB2b2lkIDAgPyA1ICogNjAgKiAxMDAwIDogX2IsXG4gICAgX2MgPSBfYS5zdGFsZVRpbWUsXG4gICAgc3RhbGVUaW1lID0gX2MgPT09IHZvaWQgMCA/IDAgOiBfYyxcbiAgICBjdXN0b21TZXRDYWNoZSA9IF9hLnNldENhY2hlLFxuICAgIGN1c3RvbUdldENhY2hlID0gX2EuZ2V0Q2FjaGU7XG4gIHZhciB1blN1YnNjcmliZVJlZiA9IHVzZVJlZigpO1xuICB2YXIgY3VycmVudFByb21pc2VSZWYgPSB1c2VSZWYoKTtcbiAgdmFyIF9zZXRDYWNoZSA9IGZ1bmN0aW9uIChrZXksIGNhY2hlZERhdGEpIHtcbiAgICBpZiAoY3VzdG9tU2V0Q2FjaGUpIHtcbiAgICAgIGN1c3RvbVNldENhY2hlKGNhY2hlZERhdGEpO1xuICAgIH0gZWxzZSB7XG4gICAgICBzZXRDYWNoZShrZXksIGNhY2hlVGltZSwgY2FjaGVkRGF0YSk7XG4gICAgfVxuICAgIHRyaWdnZXIoa2V5LCBjYWNoZWREYXRhLmRhdGEpO1xuICB9O1xuICB2YXIgX2dldENhY2hlID0gZnVuY3Rpb24gKGtleSwgcGFyYW1zKSB7XG4gICAgaWYgKHBhcmFtcyA9PT0gdm9pZCAwKSB7XG4gICAgICBwYXJhbXMgPSBbXTtcbiAgICB9XG4gICAgaWYgKGN1c3RvbUdldENhY2hlKSB7XG4gICAgICByZXR1cm4gY3VzdG9tR2V0Q2FjaGUocGFyYW1zKTtcbiAgICB9XG4gICAgcmV0dXJuIGdldENhY2hlKGtleSk7XG4gIH07XG4gIHVzZUNyZWF0aW9uKGZ1bmN0aW9uICgpIHtcbiAgICBpZiAoIWNhY2hlS2V5KSB7XG4gICAgICByZXR1cm47XG4gICAgfVxuICAgIC8vIGdldCBkYXRhIGZyb20gY2FjaGUgd2hlbiBpbml0XG4gICAgdmFyIGNhY2hlRGF0YSA9IF9nZXRDYWNoZShjYWNoZUtleSk7XG4gICAgaWYgKGNhY2hlRGF0YSAmJiBPYmplY3QuaGFzT3duUHJvcGVydHkuY2FsbChjYWNoZURhdGEsICdkYXRhJykpIHtcbiAgICAgIGZldGNoSW5zdGFuY2Uuc3RhdGUuZGF0YSA9IGNhY2hlRGF0YS5kYXRhO1xuICAgICAgZmV0Y2hJbnN0YW5jZS5zdGF0ZS5wYXJhbXMgPSBjYWNoZURhdGEucGFyYW1zO1xuICAgICAgaWYgKHN0YWxlVGltZSA9PT0gLTEgfHwgbmV3IERhdGUoKS5nZXRUaW1lKCkgLSBjYWNoZURhdGEudGltZSA8PSBzdGFsZVRpbWUpIHtcbiAgICAgICAgZmV0Y2hJbnN0YW5jZS5zdGF0ZS5sb2FkaW5nID0gZmFsc2U7XG4gICAgICB9XG4gICAgfVxuICAgIC8vIHN1YnNjcmliZSBzYW1lIGNhY2hla2V5IHVwZGF0ZSwgdHJpZ2dlciB1cGRhdGVcbiAgICB1blN1YnNjcmliZVJlZi5jdXJyZW50ID0gc3Vic2NyaWJlKGNhY2hlS2V5LCBmdW5jdGlvbiAoZGF0YSkge1xuICAgICAgZmV0Y2hJbnN0YW5jZS5zZXRTdGF0ZSh7XG4gICAgICAgIGRhdGE6IGRhdGFcbiAgICAgIH0pO1xuICAgIH0pO1xuICB9LCBbXSk7XG4gIHVzZVVubW91bnQoZnVuY3Rpb24gKCkge1xuICAgIHZhciBfYTtcbiAgICAoX2EgPSB1blN1YnNjcmliZVJlZi5jdXJyZW50KSA9PT0gbnVsbCB8fCBfYSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2EuY2FsbCh1blN1YnNjcmliZVJlZik7XG4gIH0pO1xuICBpZiAoIWNhY2hlS2V5KSB7XG4gICAgcmV0dXJuIHt9O1xuICB9XG4gIHJldHVybiB7XG4gICAgb25CZWZvcmU6IGZ1bmN0aW9uIChwYXJhbXMpIHtcbiAgICAgIHZhciBjYWNoZURhdGEgPSBfZ2V0Q2FjaGUoY2FjaGVLZXksIHBhcmFtcyk7XG4gICAgICBpZiAoIWNhY2hlRGF0YSB8fCAhT2JqZWN0Lmhhc093blByb3BlcnR5LmNhbGwoY2FjaGVEYXRhLCAnZGF0YScpKSB7XG4gICAgICAgIHJldHVybiB7fTtcbiAgICAgIH1cbiAgICAgIC8vIElmIHRoZSBkYXRhIGlzIGZyZXNoLCBzdG9wIHJlcXVlc3RcbiAgICAgIGlmIChzdGFsZVRpbWUgPT09IC0xIHx8IG5ldyBEYXRlKCkuZ2V0VGltZSgpIC0gY2FjaGVEYXRhLnRpbWUgPD0gc3RhbGVUaW1lKSB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgbG9hZGluZzogZmFsc2UsXG4gICAgICAgICAgZGF0YTogY2FjaGVEYXRhID09PSBudWxsIHx8IGNhY2hlRGF0YSA9PT0gdm9pZCAwID8gdm9pZCAwIDogY2FjaGVEYXRhLmRhdGEsXG4gICAgICAgICAgZXJyb3I6IHVuZGVmaW5lZCxcbiAgICAgICAgICByZXR1cm5Ob3c6IHRydWVcbiAgICAgICAgfTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIC8vIElmIHRoZSBkYXRhIGlzIHN0YWxlLCByZXR1cm4gZGF0YSwgYW5kIHJlcXVlc3QgY29udGludWVcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICBkYXRhOiBjYWNoZURhdGEgPT09IG51bGwgfHwgY2FjaGVEYXRhID09PSB2b2lkIDAgPyB2b2lkIDAgOiBjYWNoZURhdGEuZGF0YSxcbiAgICAgICAgICBlcnJvcjogdW5kZWZpbmVkXG4gICAgICAgIH07XG4gICAgICB9XG4gICAgfSxcbiAgICBvblJlcXVlc3Q6IGZ1bmN0aW9uIChzZXJ2aWNlLCBhcmdzKSB7XG4gICAgICB2YXIgc2VydmljZVByb21pc2UgPSBnZXRDYWNoZVByb21pc2UoY2FjaGVLZXkpO1xuICAgICAgLy8gSWYgaGFzIHNlcnZpY2VQcm9taXNlLCBhbmQgaXMgbm90IHRyaWdnZXIgYnkgc2VsZiwgdGhlbiB1c2UgaXRcbiAgICAgIGlmIChzZXJ2aWNlUHJvbWlzZSAmJiBzZXJ2aWNlUHJvbWlzZSAhPT0gY3VycmVudFByb21pc2VSZWYuY3VycmVudCkge1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIHNlcnZpY2VQcm9taXNlOiBzZXJ2aWNlUHJvbWlzZVxuICAgICAgICB9O1xuICAgICAgfVxuICAgICAgc2VydmljZVByb21pc2UgPSBzZXJ2aWNlLmFwcGx5KHZvaWQgMCwgX19zcHJlYWRBcnJheShbXSwgX19yZWFkKGFyZ3MpLCBmYWxzZSkpO1xuICAgICAgY3VycmVudFByb21pc2VSZWYuY3VycmVudCA9IHNlcnZpY2VQcm9taXNlO1xuICAgICAgc2V0Q2FjaGVQcm9taXNlKGNhY2hlS2V5LCBzZXJ2aWNlUHJvbWlzZSk7XG4gICAgICByZXR1cm4ge1xuICAgICAgICBzZXJ2aWNlUHJvbWlzZTogc2VydmljZVByb21pc2VcbiAgICAgIH07XG4gICAgfSxcbiAgICBvblN1Y2Nlc3M6IGZ1bmN0aW9uIChkYXRhLCBwYXJhbXMpIHtcbiAgICAgIHZhciBfYTtcbiAgICAgIGlmIChjYWNoZUtleSkge1xuICAgICAgICAvLyBjYW5jZWwgc3Vic2NyaWJlLCBhdm9pZCB0cmdnZXIgc2VsZlxuICAgICAgICAoX2EgPSB1blN1YnNjcmliZVJlZi5jdXJyZW50KSA9PT0gbnVsbCB8fCBfYSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2EuY2FsbCh1blN1YnNjcmliZVJlZik7XG4gICAgICAgIF9zZXRDYWNoZShjYWNoZUtleSwge1xuICAgICAgICAgIGRhdGE6IGRhdGEsXG4gICAgICAgICAgcGFyYW1zOiBwYXJhbXMsXG4gICAgICAgICAgdGltZTogbmV3IERhdGUoKS5nZXRUaW1lKClcbiAgICAgICAgfSk7XG4gICAgICAgIC8vIHJlc3Vic2NyaWJlXG4gICAgICAgIHVuU3Vic2NyaWJlUmVmLmN1cnJlbnQgPSBzdWJzY3JpYmUoY2FjaGVLZXksIGZ1bmN0aW9uIChkKSB7XG4gICAgICAgICAgZmV0Y2hJbnN0YW5jZS5zZXRTdGF0ZSh7XG4gICAgICAgICAgICBkYXRhOiBkXG4gICAgICAgICAgfSk7XG4gICAgICAgIH0pO1xuICAgICAgfVxuICAgIH0sXG4gICAgb25NdXRhdGU6IGZ1bmN0aW9uIChkYXRhKSB7XG4gICAgICB2YXIgX2E7XG4gICAgICBpZiAoY2FjaGVLZXkpIHtcbiAgICAgICAgLy8gY2FuY2VsIHN1YnNjcmliZSwgYXZvaWQgdHJpZ2dlciBzZWxmXG4gICAgICAgIChfYSA9IHVuU3Vic2NyaWJlUmVmLmN1cnJlbnQpID09PSBudWxsIHx8IF9hID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYS5jYWxsKHVuU3Vic2NyaWJlUmVmKTtcbiAgICAgICAgX3NldENhY2hlKGNhY2hlS2V5LCB7XG4gICAgICAgICAgZGF0YTogZGF0YSxcbiAgICAgICAgICBwYXJhbXM6IGZldGNoSW5zdGFuY2Uuc3RhdGUucGFyYW1zLFxuICAgICAgICAgIHRpbWU6IG5ldyBEYXRlKCkuZ2V0VGltZSgpXG4gICAgICAgIH0pO1xuICAgICAgICAvLyByZXN1YnNjcmliZVxuICAgICAgICB1blN1YnNjcmliZVJlZi5jdXJyZW50ID0gc3Vic2NyaWJlKGNhY2hlS2V5LCBmdW5jdGlvbiAoZCkge1xuICAgICAgICAgIGZldGNoSW5zdGFuY2Uuc2V0U3RhdGUoe1xuICAgICAgICAgICAgZGF0YTogZFxuICAgICAgICAgIH0pO1xuICAgICAgICB9KTtcbiAgICAgIH1cbiAgICB9XG4gIH07XG59O1xuZXhwb3J0IGRlZmF1bHQgdXNlQ2FjaGVQbHVnaW47IiwiaW1wb3J0IHsgX19yZWFkLCBfX3NwcmVhZEFycmF5IH0gZnJvbSBcInRzbGliXCI7XG5pbXBvcnQgZGVib3VuY2UgZnJvbSAnbG9kYXNoL2RlYm91bmNlJztcbmltcG9ydCB7IHVzZUVmZmVjdCwgdXNlTWVtbywgdXNlUmVmIH0gZnJvbSAncmVhY3QnO1xudmFyIHVzZURlYm91bmNlUGx1Z2luID0gZnVuY3Rpb24gKGZldGNoSW5zdGFuY2UsIF9hKSB7XG4gIHZhciBkZWJvdW5jZVdhaXQgPSBfYS5kZWJvdW5jZVdhaXQsXG4gICAgZGVib3VuY2VMZWFkaW5nID0gX2EuZGVib3VuY2VMZWFkaW5nLFxuICAgIGRlYm91bmNlVHJhaWxpbmcgPSBfYS5kZWJvdW5jZVRyYWlsaW5nLFxuICAgIGRlYm91bmNlTWF4V2FpdCA9IF9hLmRlYm91bmNlTWF4V2FpdDtcbiAgdmFyIGRlYm91bmNlZFJlZiA9IHVzZVJlZigpO1xuICB2YXIgb3B0aW9ucyA9IHVzZU1lbW8oZnVuY3Rpb24gKCkge1xuICAgIHZhciByZXQgPSB7fTtcbiAgICBpZiAoZGVib3VuY2VMZWFkaW5nICE9PSB1bmRlZmluZWQpIHtcbiAgICAgIHJldC5sZWFkaW5nID0gZGVib3VuY2VMZWFkaW5nO1xuICAgIH1cbiAgICBpZiAoZGVib3VuY2VUcmFpbGluZyAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICByZXQudHJhaWxpbmcgPSBkZWJvdW5jZVRyYWlsaW5nO1xuICAgIH1cbiAgICBpZiAoZGVib3VuY2VNYXhXYWl0ICE9PSB1bmRlZmluZWQpIHtcbiAgICAgIHJldC5tYXhXYWl0ID0gZGVib3VuY2VNYXhXYWl0O1xuICAgIH1cbiAgICByZXR1cm4gcmV0O1xuICB9LCBbZGVib3VuY2VMZWFkaW5nLCBkZWJvdW5jZVRyYWlsaW5nLCBkZWJvdW5jZU1heFdhaXRdKTtcbiAgdXNlRWZmZWN0KGZ1bmN0aW9uICgpIHtcbiAgICBpZiAoZGVib3VuY2VXYWl0KSB7XG4gICAgICB2YXIgX29yaWdpblJ1bkFzeW5jXzEgPSBmZXRjaEluc3RhbmNlLnJ1bkFzeW5jLmJpbmQoZmV0Y2hJbnN0YW5jZSk7XG4gICAgICBkZWJvdW5jZWRSZWYuY3VycmVudCA9IGRlYm91bmNlKGZ1bmN0aW9uIChjYWxsYmFjaykge1xuICAgICAgICBjYWxsYmFjaygpO1xuICAgICAgfSwgZGVib3VuY2VXYWl0LCBvcHRpb25zKTtcbiAgICAgIC8vIGRlYm91bmNlIHJ1bkFzeW5jIHNob3VsZCBiZSBwcm9taXNlXG4gICAgICAvLyBodHRwczovL2dpdGh1Yi5jb20vbG9kYXNoL2xvZGFzaC9pc3N1ZXMvNDQwMCNpc3N1ZWNvbW1lbnQtODM0ODAwMzk4XG4gICAgICBmZXRjaEluc3RhbmNlLnJ1bkFzeW5jID0gZnVuY3Rpb24gKCkge1xuICAgICAgICB2YXIgYXJncyA9IFtdO1xuICAgICAgICBmb3IgKHZhciBfaSA9IDA7IF9pIDwgYXJndW1lbnRzLmxlbmd0aDsgX2krKykge1xuICAgICAgICAgIGFyZ3NbX2ldID0gYXJndW1lbnRzW19pXTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gbmV3IFByb21pc2UoZnVuY3Rpb24gKHJlc29sdmUsIHJlamVjdCkge1xuICAgICAgICAgIHZhciBfYTtcbiAgICAgICAgICAoX2EgPSBkZWJvdW5jZWRSZWYuY3VycmVudCkgPT09IG51bGwgfHwgX2EgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hLmNhbGwoZGVib3VuY2VkUmVmLCBmdW5jdGlvbiAoKSB7XG4gICAgICAgICAgICBfb3JpZ2luUnVuQXN5bmNfMS5hcHBseSh2b2lkIDAsIF9fc3ByZWFkQXJyYXkoW10sIF9fcmVhZChhcmdzKSwgZmFsc2UpKS50aGVuKHJlc29sdmUpLmNhdGNoKHJlamVjdCk7XG4gICAgICAgICAgfSk7XG4gICAgICAgIH0pO1xuICAgICAgfTtcbiAgICAgIHJldHVybiBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHZhciBfYTtcbiAgICAgICAgKF9hID0gZGVib3VuY2VkUmVmLmN1cnJlbnQpID09PSBudWxsIHx8IF9hID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYS5jYW5jZWwoKTtcbiAgICAgICAgZmV0Y2hJbnN0YW5jZS5ydW5Bc3luYyA9IF9vcmlnaW5SdW5Bc3luY18xO1xuICAgICAgfTtcbiAgICB9XG4gIH0sIFtkZWJvdW5jZVdhaXQsIG9wdGlvbnNdKTtcbiAgaWYgKCFkZWJvdW5jZVdhaXQpIHtcbiAgICByZXR1cm4ge307XG4gIH1cbiAgcmV0dXJuIHtcbiAgICBvbkNhbmNlbDogZnVuY3Rpb24gKCkge1xuICAgICAgdmFyIF9hO1xuICAgICAgKF9hID0gZGVib3VuY2VkUmVmLmN1cnJlbnQpID09PSBudWxsIHx8IF9hID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYS5jYW5jZWwoKTtcbiAgICB9XG4gIH07XG59O1xuZXhwb3J0IGRlZmF1bHQgdXNlRGVib3VuY2VQbHVnaW47IiwiaW1wb3J0IHsgdXNlUmVmIH0gZnJvbSAncmVhY3QnO1xudmFyIHVzZUxvYWRpbmdEZWxheVBsdWdpbiA9IGZ1bmN0aW9uIChmZXRjaEluc3RhbmNlLCBfYSkge1xuICB2YXIgbG9hZGluZ0RlbGF5ID0gX2EubG9hZGluZ0RlbGF5LFxuICAgIHJlYWR5ID0gX2EucmVhZHk7XG4gIHZhciB0aW1lclJlZiA9IHVzZVJlZigpO1xuICBpZiAoIWxvYWRpbmdEZWxheSkge1xuICAgIHJldHVybiB7fTtcbiAgfVxuICB2YXIgY2FuY2VsVGltZW91dCA9IGZ1bmN0aW9uICgpIHtcbiAgICBpZiAodGltZXJSZWYuY3VycmVudCkge1xuICAgICAgY2xlYXJUaW1lb3V0KHRpbWVyUmVmLmN1cnJlbnQpO1xuICAgIH1cbiAgfTtcbiAgcmV0dXJuIHtcbiAgICBvbkJlZm9yZTogZnVuY3Rpb24gKCkge1xuICAgICAgY2FuY2VsVGltZW91dCgpO1xuICAgICAgLy8gVHdvIGNhc2VzOlxuICAgICAgLy8gMS4gcmVhZHkgPT09IHVuZGVmaW5lZFxuICAgICAgLy8gMi4gcmVhZHkgPT09IHRydWVcbiAgICAgIGlmIChyZWFkeSAhPT0gZmFsc2UpIHtcbiAgICAgICAgdGltZXJSZWYuY3VycmVudCA9IHNldFRpbWVvdXQoZnVuY3Rpb24gKCkge1xuICAgICAgICAgIGZldGNoSW5zdGFuY2Uuc2V0U3RhdGUoe1xuICAgICAgICAgICAgbG9hZGluZzogdHJ1ZVxuICAgICAgICAgIH0pO1xuICAgICAgICB9LCBsb2FkaW5nRGVsYXkpO1xuICAgICAgfVxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgbG9hZGluZzogZmFsc2VcbiAgICAgIH07XG4gICAgfSxcbiAgICBvbkZpbmFsbHk6IGZ1bmN0aW9uICgpIHtcbiAgICAgIGNhbmNlbFRpbWVvdXQoKTtcbiAgICB9LFxuICAgIG9uQ2FuY2VsOiBmdW5jdGlvbiAoKSB7XG4gICAgICBjYW5jZWxUaW1lb3V0KCk7XG4gICAgfVxuICB9O1xufTtcbmV4cG9ydCBkZWZhdWx0IHVzZUxvYWRpbmdEZWxheVBsdWdpbjsiLCJpbXBvcnQgaXNCcm93c2VyIGZyb20gJy4uLy4uLy4uL3V0aWxzL2lzQnJvd3Nlcic7XG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBpc0RvY3VtZW50VmlzaWJsZSgpIHtcbiAgaWYgKGlzQnJvd3Nlcikge1xuICAgIHJldHVybiBkb2N1bWVudC52aXNpYmlsaXR5U3RhdGUgIT09ICdoaWRkZW4nO1xuICB9XG4gIHJldHVybiB0cnVlO1xufSIsImltcG9ydCBpc0Jyb3dzZXIgZnJvbSAnLi4vLi4vLi4vdXRpbHMvaXNCcm93c2VyJztcbmltcG9ydCBpc0RvY3VtZW50VmlzaWJsZSBmcm9tICcuL2lzRG9jdW1lbnRWaXNpYmxlJztcbnZhciBsaXN0ZW5lcnMgPSBbXTtcbmZ1bmN0aW9uIHN1YnNjcmliZShsaXN0ZW5lcikge1xuICBsaXN0ZW5lcnMucHVzaChsaXN0ZW5lcik7XG4gIHJldHVybiBmdW5jdGlvbiB1bnN1YnNjcmliZSgpIHtcbiAgICB2YXIgaW5kZXggPSBsaXN0ZW5lcnMuaW5kZXhPZihsaXN0ZW5lcik7XG4gICAgbGlzdGVuZXJzLnNwbGljZShpbmRleCwgMSk7XG4gIH07XG59XG5pZiAoaXNCcm93c2VyKSB7XG4gIHZhciByZXZhbGlkYXRlID0gZnVuY3Rpb24gKCkge1xuICAgIGlmICghaXNEb2N1bWVudFZpc2libGUoKSkgcmV0dXJuO1xuICAgIGZvciAodmFyIGkgPSAwOyBpIDwgbGlzdGVuZXJzLmxlbmd0aDsgaSsrKSB7XG4gICAgICB2YXIgbGlzdGVuZXIgPSBsaXN0ZW5lcnNbaV07XG4gICAgICBsaXN0ZW5lcigpO1xuICAgIH1cbiAgfTtcbiAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ3Zpc2liaWxpdHljaGFuZ2UnLCByZXZhbGlkYXRlLCBmYWxzZSk7XG59XG5leHBvcnQgZGVmYXVsdCBzdWJzY3JpYmU7IiwiaW1wb3J0IHsgdXNlUmVmIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHVzZVVwZGF0ZUVmZmVjdCBmcm9tICcuLi8uLi8uLi91c2VVcGRhdGVFZmZlY3QnO1xuaW1wb3J0IGlzRG9jdW1lbnRWaXNpYmxlIGZyb20gJy4uL3V0aWxzL2lzRG9jdW1lbnRWaXNpYmxlJztcbmltcG9ydCBzdWJzY3JpYmVSZVZpc2libGUgZnJvbSAnLi4vdXRpbHMvc3Vic2NyaWJlUmVWaXNpYmxlJztcbnZhciB1c2VQb2xsaW5nUGx1Z2luID0gZnVuY3Rpb24gKGZldGNoSW5zdGFuY2UsIF9hKSB7XG4gIHZhciBwb2xsaW5nSW50ZXJ2YWwgPSBfYS5wb2xsaW5nSW50ZXJ2YWwsXG4gICAgX2IgPSBfYS5wb2xsaW5nV2hlbkhpZGRlbixcbiAgICBwb2xsaW5nV2hlbkhpZGRlbiA9IF9iID09PSB2b2lkIDAgPyB0cnVlIDogX2IsXG4gICAgX2MgPSBfYS5wb2xsaW5nRXJyb3JSZXRyeUNvdW50LFxuICAgIHBvbGxpbmdFcnJvclJldHJ5Q291bnQgPSBfYyA9PT0gdm9pZCAwID8gLTEgOiBfYztcbiAgdmFyIHRpbWVyUmVmID0gdXNlUmVmKCk7XG4gIHZhciB1bnN1YnNjcmliZVJlZiA9IHVzZVJlZigpO1xuICB2YXIgY291bnRSZWYgPSB1c2VSZWYoMCk7XG4gIHZhciBzdG9wUG9sbGluZyA9IGZ1bmN0aW9uICgpIHtcbiAgICB2YXIgX2E7XG4gICAgaWYgKHRpbWVyUmVmLmN1cnJlbnQpIHtcbiAgICAgIGNsZWFyVGltZW91dCh0aW1lclJlZi5jdXJyZW50KTtcbiAgICB9XG4gICAgKF9hID0gdW5zdWJzY3JpYmVSZWYuY3VycmVudCkgPT09IG51bGwgfHwgX2EgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hLmNhbGwodW5zdWJzY3JpYmVSZWYpO1xuICB9O1xuICB1c2VVcGRhdGVFZmZlY3QoZnVuY3Rpb24gKCkge1xuICAgIGlmICghcG9sbGluZ0ludGVydmFsKSB7XG4gICAgICBzdG9wUG9sbGluZygpO1xuICAgIH1cbiAgfSwgW3BvbGxpbmdJbnRlcnZhbF0pO1xuICBpZiAoIXBvbGxpbmdJbnRlcnZhbCkge1xuICAgIHJldHVybiB7fTtcbiAgfVxuICByZXR1cm4ge1xuICAgIG9uQmVmb3JlOiBmdW5jdGlvbiAoKSB7XG4gICAgICBzdG9wUG9sbGluZygpO1xuICAgIH0sXG4gICAgb25FcnJvcjogZnVuY3Rpb24gKCkge1xuICAgICAgY291bnRSZWYuY3VycmVudCArPSAxO1xuICAgIH0sXG4gICAgb25TdWNjZXNzOiBmdW5jdGlvbiAoKSB7XG4gICAgICBjb3VudFJlZi5jdXJyZW50ID0gMDtcbiAgICB9LFxuICAgIG9uRmluYWxseTogZnVuY3Rpb24gKCkge1xuICAgICAgaWYgKHBvbGxpbmdFcnJvclJldHJ5Q291bnQgPT09IC0xIHx8XG4gICAgICAvLyBXaGVuIGFuIGVycm9yIG9jY3VycywgdGhlIHJlcXVlc3QgaXMgbm90IHJlcGVhdGVkIGFmdGVyIHBvbGxpbmdFcnJvclJldHJ5Q291bnQgcmV0cmllc1xuICAgICAgcG9sbGluZ0Vycm9yUmV0cnlDb3VudCAhPT0gLTEgJiYgY291bnRSZWYuY3VycmVudCA8PSBwb2xsaW5nRXJyb3JSZXRyeUNvdW50KSB7XG4gICAgICAgIHRpbWVyUmVmLmN1cnJlbnQgPSBzZXRUaW1lb3V0KGZ1bmN0aW9uICgpIHtcbiAgICAgICAgICAvLyBpZiBwb2xsaW5nV2hlbkhpZGRlbiA9IGZhbHNlICYmIGRvY3VtZW50IGlzIGhpZGRlbiwgdGhlbiBzdG9wIHBvbGxpbmcgYW5kIHN1YnNjcmliZSByZXZpc2libGVcbiAgICAgICAgICBpZiAoIXBvbGxpbmdXaGVuSGlkZGVuICYmICFpc0RvY3VtZW50VmlzaWJsZSgpKSB7XG4gICAgICAgICAgICB1bnN1YnNjcmliZVJlZi5jdXJyZW50ID0gc3Vic2NyaWJlUmVWaXNpYmxlKGZ1bmN0aW9uICgpIHtcbiAgICAgICAgICAgICAgZmV0Y2hJbnN0YW5jZS5yZWZyZXNoKCk7XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgZmV0Y2hJbnN0YW5jZS5yZWZyZXNoKCk7XG4gICAgICAgICAgfVxuICAgICAgICB9LCBwb2xsaW5nSW50ZXJ2YWwpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY291bnRSZWYuY3VycmVudCA9IDA7XG4gICAgICB9XG4gICAgfSxcbiAgICBvbkNhbmNlbDogZnVuY3Rpb24gKCkge1xuICAgICAgc3RvcFBvbGxpbmcoKTtcbiAgICB9XG4gIH07XG59O1xuZXhwb3J0IGRlZmF1bHQgdXNlUG9sbGluZ1BsdWdpbjsiLCJpbXBvcnQgeyBfX3JlYWQsIF9fc3ByZWFkQXJyYXkgfSBmcm9tIFwidHNsaWJcIjtcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGxpbWl0KGZuLCB0aW1lc3Bhbikge1xuICB2YXIgcGVuZGluZyA9IGZhbHNlO1xuICByZXR1cm4gZnVuY3Rpb24gKCkge1xuICAgIHZhciBhcmdzID0gW107XG4gICAgZm9yICh2YXIgX2kgPSAwOyBfaSA8IGFyZ3VtZW50cy5sZW5ndGg7IF9pKyspIHtcbiAgICAgIGFyZ3NbX2ldID0gYXJndW1lbnRzW19pXTtcbiAgICB9XG4gICAgaWYgKHBlbmRpbmcpIHJldHVybjtcbiAgICBwZW5kaW5nID0gdHJ1ZTtcbiAgICBmbi5hcHBseSh2b2lkIDAsIF9fc3ByZWFkQXJyYXkoW10sIF9fcmVhZChhcmdzKSwgZmFsc2UpKTtcbiAgICBzZXRUaW1lb3V0KGZ1bmN0aW9uICgpIHtcbiAgICAgIHBlbmRpbmcgPSBmYWxzZTtcbiAgICB9LCB0aW1lc3Bhbik7XG4gIH07XG59IiwiaW1wb3J0IGlzQnJvd3NlciBmcm9tICcuLi8uLi8uLi91dGlscy9pc0Jyb3dzZXInO1xuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gaXNPbmxpbmUoKSB7XG4gIGlmIChpc0Jyb3dzZXIgJiYgdHlwZW9mIG5hdmlnYXRvci5vbkxpbmUgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgcmV0dXJuIG5hdmlnYXRvci5vbkxpbmU7XG4gIH1cbiAgcmV0dXJuIHRydWU7XG59IiwiLy8gZnJvbSBzd3JcbmltcG9ydCBpc0Jyb3dzZXIgZnJvbSAnLi4vLi4vLi4vdXRpbHMvaXNCcm93c2VyJztcbmltcG9ydCBpc0RvY3VtZW50VmlzaWJsZSBmcm9tICcuL2lzRG9jdW1lbnRWaXNpYmxlJztcbmltcG9ydCBpc09ubGluZSBmcm9tICcuL2lzT25saW5lJztcbnZhciBsaXN0ZW5lcnMgPSBbXTtcbmZ1bmN0aW9uIHN1YnNjcmliZShsaXN0ZW5lcikge1xuICBsaXN0ZW5lcnMucHVzaChsaXN0ZW5lcik7XG4gIHJldHVybiBmdW5jdGlvbiB1bnN1YnNjcmliZSgpIHtcbiAgICB2YXIgaW5kZXggPSBsaXN0ZW5lcnMuaW5kZXhPZihsaXN0ZW5lcik7XG4gICAgaWYgKGluZGV4ID4gLTEpIHtcbiAgICAgIGxpc3RlbmVycy5zcGxpY2UoaW5kZXgsIDEpO1xuICAgIH1cbiAgfTtcbn1cbmlmIChpc0Jyb3dzZXIpIHtcbiAgdmFyIHJldmFsaWRhdGUgPSBmdW5jdGlvbiAoKSB7XG4gICAgaWYgKCFpc0RvY3VtZW50VmlzaWJsZSgpIHx8ICFpc09ubGluZSgpKSByZXR1cm47XG4gICAgZm9yICh2YXIgaSA9IDA7IGkgPCBsaXN0ZW5lcnMubGVuZ3RoOyBpKyspIHtcbiAgICAgIHZhciBsaXN0ZW5lciA9IGxpc3RlbmVyc1tpXTtcbiAgICAgIGxpc3RlbmVyKCk7XG4gICAgfVxuICB9O1xuICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcigndmlzaWJpbGl0eWNoYW5nZScsIHJldmFsaWRhdGUsIGZhbHNlKTtcbiAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ2ZvY3VzJywgcmV2YWxpZGF0ZSwgZmFsc2UpO1xufVxuZXhwb3J0IGRlZmF1bHQgc3Vic2NyaWJlOyIsImltcG9ydCB7IHVzZUVmZmVjdCwgdXNlUmVmIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHVzZVVubW91bnQgZnJvbSAnLi4vLi4vLi4vdXNlVW5tb3VudCc7XG5pbXBvcnQgbGltaXQgZnJvbSAnLi4vdXRpbHMvbGltaXQnO1xuaW1wb3J0IHN1YnNjcmliZUZvY3VzIGZyb20gJy4uL3V0aWxzL3N1YnNjcmliZUZvY3VzJztcbnZhciB1c2VSZWZyZXNoT25XaW5kb3dGb2N1c1BsdWdpbiA9IGZ1bmN0aW9uIChmZXRjaEluc3RhbmNlLCBfYSkge1xuICB2YXIgcmVmcmVzaE9uV2luZG93Rm9jdXMgPSBfYS5yZWZyZXNoT25XaW5kb3dGb2N1cyxcbiAgICBfYiA9IF9hLmZvY3VzVGltZXNwYW4sXG4gICAgZm9jdXNUaW1lc3BhbiA9IF9iID09PSB2b2lkIDAgPyA1MDAwIDogX2I7XG4gIHZhciB1bnN1YnNjcmliZVJlZiA9IHVzZVJlZigpO1xuICB2YXIgc3RvcFN1YnNjcmliZSA9IGZ1bmN0aW9uICgpIHtcbiAgICB2YXIgX2E7XG4gICAgKF9hID0gdW5zdWJzY3JpYmVSZWYuY3VycmVudCkgPT09IG51bGwgfHwgX2EgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hLmNhbGwodW5zdWJzY3JpYmVSZWYpO1xuICB9O1xuICB1c2VFZmZlY3QoZnVuY3Rpb24gKCkge1xuICAgIGlmIChyZWZyZXNoT25XaW5kb3dGb2N1cykge1xuICAgICAgdmFyIGxpbWl0UmVmcmVzaF8xID0gbGltaXQoZmV0Y2hJbnN0YW5jZS5yZWZyZXNoLmJpbmQoZmV0Y2hJbnN0YW5jZSksIGZvY3VzVGltZXNwYW4pO1xuICAgICAgdW5zdWJzY3JpYmVSZWYuY3VycmVudCA9IHN1YnNjcmliZUZvY3VzKGZ1bmN0aW9uICgpIHtcbiAgICAgICAgbGltaXRSZWZyZXNoXzEoKTtcbiAgICAgIH0pO1xuICAgIH1cbiAgICByZXR1cm4gZnVuY3Rpb24gKCkge1xuICAgICAgc3RvcFN1YnNjcmliZSgpO1xuICAgIH07XG4gIH0sIFtyZWZyZXNoT25XaW5kb3dGb2N1cywgZm9jdXNUaW1lc3Bhbl0pO1xuICB1c2VVbm1vdW50KGZ1bmN0aW9uICgpIHtcbiAgICBzdG9wU3Vic2NyaWJlKCk7XG4gIH0pO1xuICByZXR1cm4ge307XG59O1xuZXhwb3J0IGRlZmF1bHQgdXNlUmVmcmVzaE9uV2luZG93Rm9jdXNQbHVnaW47IiwiaW1wb3J0IHsgdXNlUmVmIH0gZnJvbSAncmVhY3QnO1xudmFyIHVzZVJldHJ5UGx1Z2luID0gZnVuY3Rpb24gKGZldGNoSW5zdGFuY2UsIF9hKSB7XG4gIHZhciByZXRyeUludGVydmFsID0gX2EucmV0cnlJbnRlcnZhbCxcbiAgICByZXRyeUNvdW50ID0gX2EucmV0cnlDb3VudDtcbiAgdmFyIHRpbWVyUmVmID0gdXNlUmVmKCk7XG4gIHZhciBjb3VudFJlZiA9IHVzZVJlZigwKTtcbiAgdmFyIHRyaWdnZXJCeVJldHJ5ID0gdXNlUmVmKGZhbHNlKTtcbiAgaWYgKCFyZXRyeUNvdW50KSB7XG4gICAgcmV0dXJuIHt9O1xuICB9XG4gIHJldHVybiB7XG4gICAgb25CZWZvcmU6IGZ1bmN0aW9uICgpIHtcbiAgICAgIGlmICghdHJpZ2dlckJ5UmV0cnkuY3VycmVudCkge1xuICAgICAgICBjb3VudFJlZi5jdXJyZW50ID0gMDtcbiAgICAgIH1cbiAgICAgIHRyaWdnZXJCeVJldHJ5LmN1cnJlbnQgPSBmYWxzZTtcbiAgICAgIGlmICh0aW1lclJlZi5jdXJyZW50KSB7XG4gICAgICAgIGNsZWFyVGltZW91dCh0aW1lclJlZi5jdXJyZW50KTtcbiAgICAgIH1cbiAgICB9LFxuICAgIG9uU3VjY2VzczogZnVuY3Rpb24gKCkge1xuICAgICAgY291bnRSZWYuY3VycmVudCA9IDA7XG4gICAgfSxcbiAgICBvbkVycm9yOiBmdW5jdGlvbiAoKSB7XG4gICAgICBjb3VudFJlZi5jdXJyZW50ICs9IDE7XG4gICAgICBpZiAocmV0cnlDb3VudCA9PT0gLTEgfHwgY291bnRSZWYuY3VycmVudCA8PSByZXRyeUNvdW50KSB7XG4gICAgICAgIC8vIEV4cG9uZW50aWFsIGJhY2tvZmZcbiAgICAgICAgdmFyIHRpbWVvdXQgPSByZXRyeUludGVydmFsICE9PSBudWxsICYmIHJldHJ5SW50ZXJ2YWwgIT09IHZvaWQgMCA/IHJldHJ5SW50ZXJ2YWwgOiBNYXRoLm1pbigxMDAwICogTWF0aC5wb3coMiwgY291bnRSZWYuY3VycmVudCksIDMwMDAwKTtcbiAgICAgICAgdGltZXJSZWYuY3VycmVudCA9IHNldFRpbWVvdXQoZnVuY3Rpb24gKCkge1xuICAgICAgICAgIHRyaWdnZXJCeVJldHJ5LmN1cnJlbnQgPSB0cnVlO1xuICAgICAgICAgIGZldGNoSW5zdGFuY2UucmVmcmVzaCgpO1xuICAgICAgICB9LCB0aW1lb3V0KTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGNvdW50UmVmLmN1cnJlbnQgPSAwO1xuICAgICAgfVxuICAgIH0sXG4gICAgb25DYW5jZWw6IGZ1bmN0aW9uICgpIHtcbiAgICAgIGNvdW50UmVmLmN1cnJlbnQgPSAwO1xuICAgICAgaWYgKHRpbWVyUmVmLmN1cnJlbnQpIHtcbiAgICAgICAgY2xlYXJUaW1lb3V0KHRpbWVyUmVmLmN1cnJlbnQpO1xuICAgICAgfVxuICAgIH1cbiAgfTtcbn07XG5leHBvcnQgZGVmYXVsdCB1c2VSZXRyeVBsdWdpbjsiLCJpbXBvcnQgeyBfX3JlYWQsIF9fc3ByZWFkQXJyYXkgfSBmcm9tIFwidHNsaWJcIjtcbmltcG9ydCB0aHJvdHRsZSBmcm9tICdsb2Rhc2gvdGhyb3R0bGUnO1xuaW1wb3J0IHsgdXNlRWZmZWN0LCB1c2VSZWYgfSBmcm9tICdyZWFjdCc7XG52YXIgdXNlVGhyb3R0bGVQbHVnaW4gPSBmdW5jdGlvbiAoZmV0Y2hJbnN0YW5jZSwgX2EpIHtcbiAgdmFyIHRocm90dGxlV2FpdCA9IF9hLnRocm90dGxlV2FpdCxcbiAgICB0aHJvdHRsZUxlYWRpbmcgPSBfYS50aHJvdHRsZUxlYWRpbmcsXG4gICAgdGhyb3R0bGVUcmFpbGluZyA9IF9hLnRocm90dGxlVHJhaWxpbmc7XG4gIHZhciB0aHJvdHRsZWRSZWYgPSB1c2VSZWYoKTtcbiAgdmFyIG9wdGlvbnMgPSB7fTtcbiAgaWYgKHRocm90dGxlTGVhZGluZyAhPT0gdW5kZWZpbmVkKSB7XG4gICAgb3B0aW9ucy5sZWFkaW5nID0gdGhyb3R0bGVMZWFkaW5nO1xuICB9XG4gIGlmICh0aHJvdHRsZVRyYWlsaW5nICE9PSB1bmRlZmluZWQpIHtcbiAgICBvcHRpb25zLnRyYWlsaW5nID0gdGhyb3R0bGVUcmFpbGluZztcbiAgfVxuICB1c2VFZmZlY3QoZnVuY3Rpb24gKCkge1xuICAgIGlmICh0aHJvdHRsZVdhaXQpIHtcbiAgICAgIHZhciBfb3JpZ2luUnVuQXN5bmNfMSA9IGZldGNoSW5zdGFuY2UucnVuQXN5bmMuYmluZChmZXRjaEluc3RhbmNlKTtcbiAgICAgIHRocm90dGxlZFJlZi5jdXJyZW50ID0gdGhyb3R0bGUoZnVuY3Rpb24gKGNhbGxiYWNrKSB7XG4gICAgICAgIGNhbGxiYWNrKCk7XG4gICAgICB9LCB0aHJvdHRsZVdhaXQsIG9wdGlvbnMpO1xuICAgICAgLy8gdGhyb3R0bGUgcnVuQXN5bmMgc2hvdWxkIGJlIHByb21pc2VcbiAgICAgIC8vIGh0dHBzOi8vZ2l0aHViLmNvbS9sb2Rhc2gvbG9kYXNoL2lzc3Vlcy80NDAwI2lzc3VlY29tbWVudC04MzQ4MDAzOThcbiAgICAgIGZldGNoSW5zdGFuY2UucnVuQXN5bmMgPSBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHZhciBhcmdzID0gW107XG4gICAgICAgIGZvciAodmFyIF9pID0gMDsgX2kgPCBhcmd1bWVudHMubGVuZ3RoOyBfaSsrKSB7XG4gICAgICAgICAgYXJnc1tfaV0gPSBhcmd1bWVudHNbX2ldO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBuZXcgUHJvbWlzZShmdW5jdGlvbiAocmVzb2x2ZSwgcmVqZWN0KSB7XG4gICAgICAgICAgdmFyIF9hO1xuICAgICAgICAgIChfYSA9IHRocm90dGxlZFJlZi5jdXJyZW50KSA9PT0gbnVsbCB8fCBfYSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2EuY2FsbCh0aHJvdHRsZWRSZWYsIGZ1bmN0aW9uICgpIHtcbiAgICAgICAgICAgIF9vcmlnaW5SdW5Bc3luY18xLmFwcGx5KHZvaWQgMCwgX19zcHJlYWRBcnJheShbXSwgX19yZWFkKGFyZ3MpLCBmYWxzZSkpLnRoZW4ocmVzb2x2ZSkuY2F0Y2gocmVqZWN0KTtcbiAgICAgICAgICB9KTtcbiAgICAgICAgfSk7XG4gICAgICB9O1xuICAgICAgcmV0dXJuIGZ1bmN0aW9uICgpIHtcbiAgICAgICAgdmFyIF9hO1xuICAgICAgICBmZXRjaEluc3RhbmNlLnJ1bkFzeW5jID0gX29yaWdpblJ1bkFzeW5jXzE7XG4gICAgICAgIChfYSA9IHRocm90dGxlZFJlZi5jdXJyZW50KSA9PT0gbnVsbCB8fCBfYSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2EuY2FuY2VsKCk7XG4gICAgICB9O1xuICAgIH1cbiAgfSwgW3Rocm90dGxlV2FpdCwgdGhyb3R0bGVMZWFkaW5nLCB0aHJvdHRsZVRyYWlsaW5nXSk7XG4gIGlmICghdGhyb3R0bGVXYWl0KSB7XG4gICAgcmV0dXJuIHt9O1xuICB9XG4gIHJldHVybiB7XG4gICAgb25DYW5jZWw6IGZ1bmN0aW9uICgpIHtcbiAgICAgIHZhciBfYTtcbiAgICAgIChfYSA9IHRocm90dGxlZFJlZi5jdXJyZW50KSA9PT0gbnVsbCB8fCBfYSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2EuY2FuY2VsKCk7XG4gICAgfVxuICB9O1xufTtcbmV4cG9ydCBkZWZhdWx0IHVzZVRocm90dGxlUGx1Z2luOyIsImltcG9ydCB7IHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IGlzRnVuY3Rpb24gfSBmcm9tICcuLi91dGlscyc7XG5pbXBvcnQgaXNEZXYgZnJvbSAnLi4vdXRpbHMvaXNEZXYnO1xudmFyIHVzZU1vdW50ID0gZnVuY3Rpb24gKGZuKSB7XG4gIGlmIChpc0Rldikge1xuICAgIGlmICghaXNGdW5jdGlvbihmbikpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJ1c2VNb3VudDogcGFyYW1ldGVyIGBmbmAgZXhwZWN0ZWQgdG8gYmUgYSBmdW5jdGlvbiwgYnV0IGdvdCBcXFwiXCIuY29uY2F0KHR5cGVvZiBmbiwgXCJcXFwiLlwiKSk7XG4gICAgfVxuICB9XG4gIHVzZUVmZmVjdChmdW5jdGlvbiAoKSB7XG4gICAgZm4gPT09IG51bGwgfHwgZm4gPT09IHZvaWQgMCA/IHZvaWQgMCA6IGZuKCk7XG4gIH0sIFtdKTtcbn07XG5leHBvcnQgZGVmYXVsdCB1c2VNb3VudDsiLCJpbXBvcnQgeyBfX3JlYWQgfSBmcm9tIFwidHNsaWJcIjtcbmltcG9ydCB7IHVzZUNhbGxiYWNrLCB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcbnZhciB1c2VVcGRhdGUgPSBmdW5jdGlvbiAoKSB7XG4gIHZhciBfYSA9IF9fcmVhZCh1c2VTdGF0ZSh7fSksIDIpLFxuICAgIHNldFN0YXRlID0gX2FbMV07XG4gIHJldHVybiB1c2VDYWxsYmFjayhmdW5jdGlvbiAoKSB7XG4gICAgcmV0dXJuIHNldFN0YXRlKHt9KTtcbiAgfSwgW10pO1xufTtcbmV4cG9ydCBkZWZhdWx0IHVzZVVwZGF0ZTsiLCJpbXBvcnQgeyBfX2Fzc2lnbiwgX19hd2FpdGVyLCBfX2dlbmVyYXRvciwgX19yZWFkLCBfX3Jlc3QsIF9fc3ByZWFkQXJyYXkgfSBmcm9tIFwidHNsaWJcIjtcbi8qIGVzbGludC1kaXNhYmxlIEB0eXBlc2NyaXB0LWVzbGludC9uby1wYXJhbWV0ZXItcHJvcGVydGllcyAqL1xuaW1wb3J0IHsgaXNGdW5jdGlvbiB9IGZyb20gJy4uLy4uL3V0aWxzJztcbnZhciBGZXRjaCA9IC8qKiBAY2xhc3MgKi9mdW5jdGlvbiAoKSB7XG4gIGZ1bmN0aW9uIEZldGNoKHNlcnZpY2VSZWYsIG9wdGlvbnMsIHN1YnNjcmliZSwgaW5pdFN0YXRlKSB7XG4gICAgaWYgKGluaXRTdGF0ZSA9PT0gdm9pZCAwKSB7XG4gICAgICBpbml0U3RhdGUgPSB7fTtcbiAgICB9XG4gICAgdGhpcy5zZXJ2aWNlUmVmID0gc2VydmljZVJlZjtcbiAgICB0aGlzLm9wdGlvbnMgPSBvcHRpb25zO1xuICAgIHRoaXMuc3Vic2NyaWJlID0gc3Vic2NyaWJlO1xuICAgIHRoaXMuaW5pdFN0YXRlID0gaW5pdFN0YXRlO1xuICAgIHRoaXMuY291bnQgPSAwO1xuICAgIHRoaXMuc3RhdGUgPSB7XG4gICAgICBsb2FkaW5nOiBmYWxzZSxcbiAgICAgIHBhcmFtczogdW5kZWZpbmVkLFxuICAgICAgZGF0YTogdW5kZWZpbmVkLFxuICAgICAgZXJyb3I6IHVuZGVmaW5lZFxuICAgIH07XG4gICAgdGhpcy5zdGF0ZSA9IF9fYXNzaWduKF9fYXNzaWduKF9fYXNzaWduKHt9LCB0aGlzLnN0YXRlKSwge1xuICAgICAgbG9hZGluZzogIW9wdGlvbnMubWFudWFsXG4gICAgfSksIGluaXRTdGF0ZSk7XG4gIH1cbiAgRmV0Y2gucHJvdG90eXBlLnNldFN0YXRlID0gZnVuY3Rpb24gKHMpIHtcbiAgICBpZiAocyA9PT0gdm9pZCAwKSB7XG4gICAgICBzID0ge307XG4gICAgfVxuICAgIHRoaXMuc3RhdGUgPSBfX2Fzc2lnbihfX2Fzc2lnbih7fSwgdGhpcy5zdGF0ZSksIHMpO1xuICAgIHRoaXMuc3Vic2NyaWJlKCk7XG4gIH07XG4gIEZldGNoLnByb3RvdHlwZS5ydW5QbHVnaW5IYW5kbGVyID0gZnVuY3Rpb24gKGV2ZW50KSB7XG4gICAgdmFyIHJlc3QgPSBbXTtcbiAgICBmb3IgKHZhciBfaSA9IDE7IF9pIDwgYXJndW1lbnRzLmxlbmd0aDsgX2krKykge1xuICAgICAgcmVzdFtfaSAtIDFdID0gYXJndW1lbnRzW19pXTtcbiAgICB9XG4gICAgLy8gQHRzLWlnbm9yZVxuICAgIHZhciByID0gdGhpcy5wbHVnaW5JbXBscy5tYXAoZnVuY3Rpb24gKGkpIHtcbiAgICAgIHZhciBfYTtcbiAgICAgIHJldHVybiAoX2EgPSBpW2V2ZW50XSkgPT09IG51bGwgfHwgX2EgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hLmNhbGwuYXBwbHkoX2EsIF9fc3ByZWFkQXJyYXkoW2ldLCBfX3JlYWQocmVzdCksIGZhbHNlKSk7XG4gICAgfSkuZmlsdGVyKEJvb2xlYW4pO1xuICAgIHJldHVybiBPYmplY3QuYXNzaWduLmFwcGx5KE9iamVjdCwgX19zcHJlYWRBcnJheShbe31dLCBfX3JlYWQociksIGZhbHNlKSk7XG4gIH07XG4gIEZldGNoLnByb3RvdHlwZS5ydW5Bc3luYyA9IGZ1bmN0aW9uICgpIHtcbiAgICB2YXIgX2EsIF9iLCBfYywgX2QsIF9lLCBfZiwgX2csIF9oLCBfaiwgX2s7XG4gICAgdmFyIHBhcmFtcyA9IFtdO1xuICAgIGZvciAodmFyIF9pID0gMDsgX2kgPCBhcmd1bWVudHMubGVuZ3RoOyBfaSsrKSB7XG4gICAgICBwYXJhbXNbX2ldID0gYXJndW1lbnRzW19pXTtcbiAgICB9XG4gICAgcmV0dXJuIF9fYXdhaXRlcih0aGlzLCB2b2lkIDAsIHZvaWQgMCwgZnVuY3Rpb24gKCkge1xuICAgICAgdmFyIGN1cnJlbnRDb3VudCwgX2wsIF9tLCBzdG9wTm93LCBfbywgcmV0dXJuTm93LCBzdGF0ZSwgc2VydmljZVByb21pc2UsIHJlcywgZXJyb3JfMTtcbiAgICAgIHZhciBfcDtcbiAgICAgIHJldHVybiBfX2dlbmVyYXRvcih0aGlzLCBmdW5jdGlvbiAoX3EpIHtcbiAgICAgICAgc3dpdGNoIChfcS5sYWJlbCkge1xuICAgICAgICAgIGNhc2UgMDpcbiAgICAgICAgICAgIHRoaXMuY291bnQgKz0gMTtcbiAgICAgICAgICAgIGN1cnJlbnRDb3VudCA9IHRoaXMuY291bnQ7XG4gICAgICAgICAgICBfbCA9IHRoaXMucnVuUGx1Z2luSGFuZGxlcignb25CZWZvcmUnLCBwYXJhbXMpLCBfbSA9IF9sLnN0b3BOb3csIHN0b3BOb3cgPSBfbSA9PT0gdm9pZCAwID8gZmFsc2UgOiBfbSwgX28gPSBfbC5yZXR1cm5Ob3csIHJldHVybk5vdyA9IF9vID09PSB2b2lkIDAgPyBmYWxzZSA6IF9vLCBzdGF0ZSA9IF9fcmVzdChfbCwgW1wic3RvcE5vd1wiLCBcInJldHVybk5vd1wiXSk7XG4gICAgICAgICAgICAvLyBzdG9wIHJlcXVlc3RcbiAgICAgICAgICAgIGlmIChzdG9wTm93KSB7XG4gICAgICAgICAgICAgIHJldHVybiBbMiAvKnJldHVybiovLCBuZXcgUHJvbWlzZShmdW5jdGlvbiAoKSB7fSldO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgdGhpcy5zZXRTdGF0ZShfX2Fzc2lnbih7XG4gICAgICAgICAgICAgIGxvYWRpbmc6IHRydWUsXG4gICAgICAgICAgICAgIHBhcmFtczogcGFyYW1zXG4gICAgICAgICAgICB9LCBzdGF0ZSkpO1xuICAgICAgICAgICAgLy8gcmV0dXJuIG5vd1xuICAgICAgICAgICAgaWYgKHJldHVybk5vdykge1xuICAgICAgICAgICAgICByZXR1cm4gWzIgLypyZXR1cm4qLywgUHJvbWlzZS5yZXNvbHZlKHN0YXRlLmRhdGEpXTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIChfYiA9IChfYSA9IHRoaXMub3B0aW9ucykub25CZWZvcmUpID09PSBudWxsIHx8IF9iID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYi5jYWxsKF9hLCBwYXJhbXMpO1xuICAgICAgICAgICAgX3EubGFiZWwgPSAxO1xuICAgICAgICAgIGNhc2UgMTpcbiAgICAgICAgICAgIF9xLnRyeXMucHVzaChbMSwgMywsIDRdKTtcbiAgICAgICAgICAgIHNlcnZpY2VQcm9taXNlID0gdGhpcy5ydW5QbHVnaW5IYW5kbGVyKCdvblJlcXVlc3QnLCB0aGlzLnNlcnZpY2VSZWYuY3VycmVudCwgcGFyYW1zKS5zZXJ2aWNlUHJvbWlzZTtcbiAgICAgICAgICAgIGlmICghc2VydmljZVByb21pc2UpIHtcbiAgICAgICAgICAgICAgc2VydmljZVByb21pc2UgPSAoX3AgPSB0aGlzLnNlcnZpY2VSZWYpLmN1cnJlbnQuYXBwbHkoX3AsIF9fc3ByZWFkQXJyYXkoW10sIF9fcmVhZChwYXJhbXMpLCBmYWxzZSkpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmV0dXJuIFs0IC8qeWllbGQqLywgc2VydmljZVByb21pc2VdO1xuICAgICAgICAgIGNhc2UgMjpcbiAgICAgICAgICAgIHJlcyA9IF9xLnNlbnQoKTtcbiAgICAgICAgICAgIGlmIChjdXJyZW50Q291bnQgIT09IHRoaXMuY291bnQpIHtcbiAgICAgICAgICAgICAgLy8gcHJldmVudCBydW4udGhlbiB3aGVuIHJlcXVlc3QgaXMgY2FuY2VsZWRcbiAgICAgICAgICAgICAgcmV0dXJuIFsyIC8qcmV0dXJuKi8sIG5ldyBQcm9taXNlKGZ1bmN0aW9uICgpIHt9KV07XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvLyBjb25zdCBmb3JtYXR0ZWRSZXN1bHQgPSB0aGlzLm9wdGlvbnMuZm9ybWF0UmVzdWx0UmVmLmN1cnJlbnQgPyB0aGlzLm9wdGlvbnMuZm9ybWF0UmVzdWx0UmVmLmN1cnJlbnQocmVzKSA6IHJlcztcbiAgICAgICAgICAgIHRoaXMuc2V0U3RhdGUoe1xuICAgICAgICAgICAgICBkYXRhOiByZXMsXG4gICAgICAgICAgICAgIGVycm9yOiB1bmRlZmluZWQsXG4gICAgICAgICAgICAgIGxvYWRpbmc6IGZhbHNlXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIChfZCA9IChfYyA9IHRoaXMub3B0aW9ucykub25TdWNjZXNzKSA9PT0gbnVsbCB8fCBfZCA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2QuY2FsbChfYywgcmVzLCBwYXJhbXMpO1xuICAgICAgICAgICAgdGhpcy5ydW5QbHVnaW5IYW5kbGVyKCdvblN1Y2Nlc3MnLCByZXMsIHBhcmFtcyk7XG4gICAgICAgICAgICAoX2YgPSAoX2UgPSB0aGlzLm9wdGlvbnMpLm9uRmluYWxseSkgPT09IG51bGwgfHwgX2YgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9mLmNhbGwoX2UsIHBhcmFtcywgcmVzLCB1bmRlZmluZWQpO1xuICAgICAgICAgICAgaWYgKGN1cnJlbnRDb3VudCA9PT0gdGhpcy5jb3VudCkge1xuICAgICAgICAgICAgICB0aGlzLnJ1blBsdWdpbkhhbmRsZXIoJ29uRmluYWxseScsIHBhcmFtcywgcmVzLCB1bmRlZmluZWQpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmV0dXJuIFsyIC8qcmV0dXJuKi8sIHJlc107XG4gICAgICAgICAgY2FzZSAzOlxuICAgICAgICAgICAgZXJyb3JfMSA9IF9xLnNlbnQoKTtcbiAgICAgICAgICAgIGlmIChjdXJyZW50Q291bnQgIT09IHRoaXMuY291bnQpIHtcbiAgICAgICAgICAgICAgLy8gcHJldmVudCBydW4udGhlbiB3aGVuIHJlcXVlc3QgaXMgY2FuY2VsZWRcbiAgICAgICAgICAgICAgcmV0dXJuIFsyIC8qcmV0dXJuKi8sIG5ldyBQcm9taXNlKGZ1bmN0aW9uICgpIHt9KV07XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICB0aGlzLnNldFN0YXRlKHtcbiAgICAgICAgICAgICAgZXJyb3I6IGVycm9yXzEsXG4gICAgICAgICAgICAgIGxvYWRpbmc6IGZhbHNlXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIChfaCA9IChfZyA9IHRoaXMub3B0aW9ucykub25FcnJvcikgPT09IG51bGwgfHwgX2ggPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9oLmNhbGwoX2csIGVycm9yXzEsIHBhcmFtcyk7XG4gICAgICAgICAgICB0aGlzLnJ1blBsdWdpbkhhbmRsZXIoJ29uRXJyb3InLCBlcnJvcl8xLCBwYXJhbXMpO1xuICAgICAgICAgICAgKF9rID0gKF9qID0gdGhpcy5vcHRpb25zKS5vbkZpbmFsbHkpID09PSBudWxsIHx8IF9rID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfay5jYWxsKF9qLCBwYXJhbXMsIHVuZGVmaW5lZCwgZXJyb3JfMSk7XG4gICAgICAgICAgICBpZiAoY3VycmVudENvdW50ID09PSB0aGlzLmNvdW50KSB7XG4gICAgICAgICAgICAgIHRoaXMucnVuUGx1Z2luSGFuZGxlcignb25GaW5hbGx5JywgcGFyYW1zLCB1bmRlZmluZWQsIGVycm9yXzEpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgdGhyb3cgZXJyb3JfMTtcbiAgICAgICAgICBjYXNlIDQ6XG4gICAgICAgICAgICByZXR1cm4gWzIgLypyZXR1cm4qL107XG4gICAgICAgIH1cbiAgICAgIH0pO1xuICAgIH0pO1xuICB9O1xuXG4gIEZldGNoLnByb3RvdHlwZS5ydW4gPSBmdW5jdGlvbiAoKSB7XG4gICAgdmFyIF90aGlzID0gdGhpcztcbiAgICB2YXIgcGFyYW1zID0gW107XG4gICAgZm9yICh2YXIgX2kgPSAwOyBfaSA8IGFyZ3VtZW50cy5sZW5ndGg7IF9pKyspIHtcbiAgICAgIHBhcmFtc1tfaV0gPSBhcmd1bWVudHNbX2ldO1xuICAgIH1cbiAgICB0aGlzLnJ1bkFzeW5jLmFwcGx5KHRoaXMsIF9fc3ByZWFkQXJyYXkoW10sIF9fcmVhZChwYXJhbXMpLCBmYWxzZSkpLmNhdGNoKGZ1bmN0aW9uIChlcnJvcikge1xuICAgICAgaWYgKCFfdGhpcy5vcHRpb25zLm9uRXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcihlcnJvcik7XG4gICAgICB9XG4gICAgfSk7XG4gIH07XG4gIEZldGNoLnByb3RvdHlwZS5jYW5jZWwgPSBmdW5jdGlvbiAoKSB7XG4gICAgdGhpcy5jb3VudCArPSAxO1xuICAgIHRoaXMuc2V0U3RhdGUoe1xuICAgICAgbG9hZGluZzogZmFsc2VcbiAgICB9KTtcbiAgICB0aGlzLnJ1blBsdWdpbkhhbmRsZXIoJ29uQ2FuY2VsJyk7XG4gIH07XG4gIEZldGNoLnByb3RvdHlwZS5yZWZyZXNoID0gZnVuY3Rpb24gKCkge1xuICAgIC8vIEB0cy1pZ25vcmVcbiAgICB0aGlzLnJ1bi5hcHBseSh0aGlzLCBfX3NwcmVhZEFycmF5KFtdLCBfX3JlYWQodGhpcy5zdGF0ZS5wYXJhbXMgfHwgW10pLCBmYWxzZSkpO1xuICB9O1xuICBGZXRjaC5wcm90b3R5cGUucmVmcmVzaEFzeW5jID0gZnVuY3Rpb24gKCkge1xuICAgIC8vIEB0cy1pZ25vcmVcbiAgICByZXR1cm4gdGhpcy5ydW5Bc3luYy5hcHBseSh0aGlzLCBfX3NwcmVhZEFycmF5KFtdLCBfX3JlYWQodGhpcy5zdGF0ZS5wYXJhbXMgfHwgW10pLCBmYWxzZSkpO1xuICB9O1xuICBGZXRjaC5wcm90b3R5cGUubXV0YXRlID0gZnVuY3Rpb24gKGRhdGEpIHtcbiAgICB2YXIgdGFyZ2V0RGF0YSA9IGlzRnVuY3Rpb24oZGF0YSkgPyBkYXRhKHRoaXMuc3RhdGUuZGF0YSkgOiBkYXRhO1xuICAgIHRoaXMucnVuUGx1Z2luSGFuZGxlcignb25NdXRhdGUnLCB0YXJnZXREYXRhKTtcbiAgICB0aGlzLnNldFN0YXRlKHtcbiAgICAgIGRhdGE6IHRhcmdldERhdGFcbiAgICB9KTtcbiAgfTtcbiAgcmV0dXJuIEZldGNoO1xufSgpO1xuZXhwb3J0IGRlZmF1bHQgRmV0Y2g7IiwiaW1wb3J0IHsgX19hc3NpZ24sIF9fcmVhZCwgX19yZXN0LCBfX3NwcmVhZEFycmF5IH0gZnJvbSBcInRzbGliXCI7XG5pbXBvcnQgdXNlQ3JlYXRpb24gZnJvbSAnLi4vLi4vdXNlQ3JlYXRpb24nO1xuaW1wb3J0IHVzZUxhdGVzdCBmcm9tICcuLi8uLi91c2VMYXRlc3QnO1xuaW1wb3J0IHVzZU1lbW9pemVkRm4gZnJvbSAnLi4vLi4vdXNlTWVtb2l6ZWRGbic7XG5pbXBvcnQgdXNlTW91bnQgZnJvbSAnLi4vLi4vdXNlTW91bnQnO1xuaW1wb3J0IHVzZVVubW91bnQgZnJvbSAnLi4vLi4vdXNlVW5tb3VudCc7XG5pbXBvcnQgdXNlVXBkYXRlIGZyb20gJy4uLy4uL3VzZVVwZGF0ZSc7XG5pbXBvcnQgaXNEZXYgZnJvbSAnLi4vLi4vdXRpbHMvaXNEZXYnO1xuaW1wb3J0IEZldGNoIGZyb20gJy4vRmV0Y2gnO1xuZnVuY3Rpb24gdXNlUmVxdWVzdEltcGxlbWVudChzZXJ2aWNlLCBvcHRpb25zLCBwbHVnaW5zKSB7XG4gIGlmIChvcHRpb25zID09PSB2b2lkIDApIHtcbiAgICBvcHRpb25zID0ge307XG4gIH1cbiAgaWYgKHBsdWdpbnMgPT09IHZvaWQgMCkge1xuICAgIHBsdWdpbnMgPSBbXTtcbiAgfVxuICB2YXIgX2EgPSBvcHRpb25zLm1hbnVhbCxcbiAgICBtYW51YWwgPSBfYSA9PT0gdm9pZCAwID8gZmFsc2UgOiBfYSxcbiAgICByZXN0ID0gX19yZXN0KG9wdGlvbnMsIFtcIm1hbnVhbFwiXSk7XG4gIGlmIChpc0Rldikge1xuICAgIGlmIChvcHRpb25zLmRlZmF1bHRQYXJhbXMgJiYgIUFycmF5LmlzQXJyYXkob3B0aW9ucy5kZWZhdWx0UGFyYW1zKSkge1xuICAgICAgY29uc29sZS53YXJuKFwiZXhwZWN0ZWQgZGVmYXVsdFBhcmFtcyBpcyBhcnJheSwgZ290IFwiLmNvbmNhdCh0eXBlb2Ygb3B0aW9ucy5kZWZhdWx0UGFyYW1zKSk7XG4gICAgfVxuICB9XG4gIHZhciBmZXRjaE9wdGlvbnMgPSBfX2Fzc2lnbih7XG4gICAgbWFudWFsOiBtYW51YWxcbiAgfSwgcmVzdCk7XG4gIHZhciBzZXJ2aWNlUmVmID0gdXNlTGF0ZXN0KHNlcnZpY2UpO1xuICB2YXIgdXBkYXRlID0gdXNlVXBkYXRlKCk7XG4gIHZhciBmZXRjaEluc3RhbmNlID0gdXNlQ3JlYXRpb24oZnVuY3Rpb24gKCkge1xuICAgIHZhciBpbml0U3RhdGUgPSBwbHVnaW5zLm1hcChmdW5jdGlvbiAocCkge1xuICAgICAgdmFyIF9hO1xuICAgICAgcmV0dXJuIChfYSA9IHAgPT09IG51bGwgfHwgcCA9PT0gdm9pZCAwID8gdm9pZCAwIDogcC5vbkluaXQpID09PSBudWxsIHx8IF9hID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYS5jYWxsKHAsIGZldGNoT3B0aW9ucyk7XG4gICAgfSkuZmlsdGVyKEJvb2xlYW4pO1xuICAgIHJldHVybiBuZXcgRmV0Y2goc2VydmljZVJlZiwgZmV0Y2hPcHRpb25zLCB1cGRhdGUsIE9iamVjdC5hc3NpZ24uYXBwbHkoT2JqZWN0LCBfX3NwcmVhZEFycmF5KFt7fV0sIF9fcmVhZChpbml0U3RhdGUpLCBmYWxzZSkpKTtcbiAgfSwgW10pO1xuICBmZXRjaEluc3RhbmNlLm9wdGlvbnMgPSBmZXRjaE9wdGlvbnM7XG4gIC8vIHJ1biBhbGwgcGx1Z2lucyBob29rc1xuICBmZXRjaEluc3RhbmNlLnBsdWdpbkltcGxzID0gcGx1Z2lucy5tYXAoZnVuY3Rpb24gKHApIHtcbiAgICByZXR1cm4gcChmZXRjaEluc3RhbmNlLCBmZXRjaE9wdGlvbnMpO1xuICB9KTtcbiAgdXNlTW91bnQoZnVuY3Rpb24gKCkge1xuICAgIGlmICghbWFudWFsKSB7XG4gICAgICAvLyB1c2VDYWNoZVBsdWdpbiBjYW4gc2V0IGZldGNoSW5zdGFuY2Uuc3RhdGUucGFyYW1zIGZyb20gY2FjaGUgd2hlbiBpbml0XG4gICAgICB2YXIgcGFyYW1zID0gZmV0Y2hJbnN0YW5jZS5zdGF0ZS5wYXJhbXMgfHwgb3B0aW9ucy5kZWZhdWx0UGFyYW1zIHx8IFtdO1xuICAgICAgLy8gQHRzLWlnbm9yZVxuICAgICAgZmV0Y2hJbnN0YW5jZS5ydW4uYXBwbHkoZmV0Y2hJbnN0YW5jZSwgX19zcHJlYWRBcnJheShbXSwgX19yZWFkKHBhcmFtcyksIGZhbHNlKSk7XG4gICAgfVxuICB9KTtcbiAgdXNlVW5tb3VudChmdW5jdGlvbiAoKSB7XG4gICAgZmV0Y2hJbnN0YW5jZS5jYW5jZWwoKTtcbiAgfSk7XG4gIHJldHVybiB7XG4gICAgbG9hZGluZzogZmV0Y2hJbnN0YW5jZS5zdGF0ZS5sb2FkaW5nLFxuICAgIGRhdGE6IGZldGNoSW5zdGFuY2Uuc3RhdGUuZGF0YSxcbiAgICBlcnJvcjogZmV0Y2hJbnN0YW5jZS5zdGF0ZS5lcnJvcixcbiAgICBwYXJhbXM6IGZldGNoSW5zdGFuY2Uuc3RhdGUucGFyYW1zIHx8IFtdLFxuICAgIGNhbmNlbDogdXNlTWVtb2l6ZWRGbihmZXRjaEluc3RhbmNlLmNhbmNlbC5iaW5kKGZldGNoSW5zdGFuY2UpKSxcbiAgICByZWZyZXNoOiB1c2VNZW1vaXplZEZuKGZldGNoSW5zdGFuY2UucmVmcmVzaC5iaW5kKGZldGNoSW5zdGFuY2UpKSxcbiAgICByZWZyZXNoQXN5bmM6IHVzZU1lbW9pemVkRm4oZmV0Y2hJbnN0YW5jZS5yZWZyZXNoQXN5bmMuYmluZChmZXRjaEluc3RhbmNlKSksXG4gICAgcnVuOiB1c2VNZW1vaXplZEZuKGZldGNoSW5zdGFuY2UucnVuLmJpbmQoZmV0Y2hJbnN0YW5jZSkpLFxuICAgIHJ1bkFzeW5jOiB1c2VNZW1vaXplZEZuKGZldGNoSW5zdGFuY2UucnVuQXN5bmMuYmluZChmZXRjaEluc3RhbmNlKSksXG4gICAgbXV0YXRlOiB1c2VNZW1vaXplZEZuKGZldGNoSW5zdGFuY2UubXV0YXRlLmJpbmQoZmV0Y2hJbnN0YW5jZSkpXG4gIH07XG59XG5leHBvcnQgZGVmYXVsdCB1c2VSZXF1ZXN0SW1wbGVtZW50OyIsImltcG9ydCB7IF9fcmVhZCwgX19zcHJlYWRBcnJheSB9IGZyb20gXCJ0c2xpYlwiO1xuaW1wb3J0IHVzZUF1dG9SdW5QbHVnaW4gZnJvbSAnLi9wbHVnaW5zL3VzZUF1dG9SdW5QbHVnaW4nO1xuaW1wb3J0IHVzZUNhY2hlUGx1Z2luIGZyb20gJy4vcGx1Z2lucy91c2VDYWNoZVBsdWdpbic7XG5pbXBvcnQgdXNlRGVib3VuY2VQbHVnaW4gZnJvbSAnLi9wbHVnaW5zL3VzZURlYm91bmNlUGx1Z2luJztcbmltcG9ydCB1c2VMb2FkaW5nRGVsYXlQbHVnaW4gZnJvbSAnLi9wbHVnaW5zL3VzZUxvYWRpbmdEZWxheVBsdWdpbic7XG5pbXBvcnQgdXNlUG9sbGluZ1BsdWdpbiBmcm9tICcuL3BsdWdpbnMvdXNlUG9sbGluZ1BsdWdpbic7XG5pbXBvcnQgdXNlUmVmcmVzaE9uV2luZG93Rm9jdXNQbHVnaW4gZnJvbSAnLi9wbHVnaW5zL3VzZVJlZnJlc2hPbldpbmRvd0ZvY3VzUGx1Z2luJztcbmltcG9ydCB1c2VSZXRyeVBsdWdpbiBmcm9tICcuL3BsdWdpbnMvdXNlUmV0cnlQbHVnaW4nO1xuaW1wb3J0IHVzZVRocm90dGxlUGx1Z2luIGZyb20gJy4vcGx1Z2lucy91c2VUaHJvdHRsZVBsdWdpbic7XG5pbXBvcnQgdXNlUmVxdWVzdEltcGxlbWVudCBmcm9tICcuL3VzZVJlcXVlc3RJbXBsZW1lbnQnO1xuLy8gZnVuY3Rpb24gdXNlUmVxdWVzdDxURGF0YSwgVFBhcmFtcyBleHRlbmRzIGFueVtdLCBURm9ybWF0ZWQsIFRURm9ybWF0ZWQgZXh0ZW5kcyBURm9ybWF0ZWQgPSBhbnk+KFxuLy8gICBzZXJ2aWNlOiBTZXJ2aWNlPFREYXRhLCBUUGFyYW1zPixcbi8vICAgb3B0aW9uczogT3B0aW9uc1dpdGhGb3JtYXQ8VERhdGEsIFRQYXJhbXMsIFRGb3JtYXRlZCwgVFRGb3JtYXRlZD4sXG4vLyAgIHBsdWdpbnM/OiBQbHVnaW48VERhdGEsIFRQYXJhbXM+W10sXG4vLyApOiBSZXN1bHQ8VEZvcm1hdGVkLCBUUGFyYW1zPlxuLy8gZnVuY3Rpb24gdXNlUmVxdWVzdDxURGF0YSwgVFBhcmFtcyBleHRlbmRzIGFueVtdPihcbi8vICAgc2VydmljZTogU2VydmljZTxURGF0YSwgVFBhcmFtcz4sXG4vLyAgIG9wdGlvbnM/OiBPcHRpb25zV2l0aG91dEZvcm1hdDxURGF0YSwgVFBhcmFtcz4sXG4vLyAgIHBsdWdpbnM/OiBQbHVnaW48VERhdGEsIFRQYXJhbXM+W10sXG4vLyApOiBSZXN1bHQ8VERhdGEsIFRQYXJhbXM+XG5mdW5jdGlvbiB1c2VSZXF1ZXN0KHNlcnZpY2UsIG9wdGlvbnMsIHBsdWdpbnMpIHtcbiAgcmV0dXJuIHVzZVJlcXVlc3RJbXBsZW1lbnQoc2VydmljZSwgb3B0aW9ucywgX19zcHJlYWRBcnJheShfX3NwcmVhZEFycmF5KFtdLCBfX3JlYWQocGx1Z2lucyB8fCBbXSksIGZhbHNlKSwgW3VzZURlYm91bmNlUGx1Z2luLCB1c2VMb2FkaW5nRGVsYXlQbHVnaW4sIHVzZVBvbGxpbmdQbHVnaW4sIHVzZVJlZnJlc2hPbldpbmRvd0ZvY3VzUGx1Z2luLCB1c2VUaHJvdHRsZVBsdWdpbiwgdXNlQXV0b1J1blBsdWdpbiwgdXNlQ2FjaGVQbHVnaW4sIHVzZVJldHJ5UGx1Z2luXSwgZmFsc2UpKTtcbn1cbmV4cG9ydCBkZWZhdWx0IHVzZVJlcXVlc3Q7IiwiaW1wb3J0IHVzZVJlcXVlc3QgZnJvbSAnLi9zcmMvdXNlUmVxdWVzdCc7XG5pbXBvcnQgeyBjbGVhckNhY2hlIH0gZnJvbSAnLi9zcmMvdXRpbHMvY2FjaGUnO1xuZXhwb3J0IHsgY2xlYXJDYWNoZSB9O1xuZXhwb3J0IGRlZmF1bHQgdXNlUmVxdWVzdDsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=
//# sourceURL=webpack-internal:///79718
`)},77598:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`
// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  Z: function() { return /* binding */ useUpdateEffect; }
});

// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
;// CONCATENATED MODULE: ./node_modules/ahooks/es/createUpdateEffect/index.js

var createUpdateEffect = function (hook) {
  return function (effect, deps) {
    var isMounted = (0,react.useRef)(false);
    // for react-refresh
    hook(function () {
      return function () {
        isMounted.current = false;
      };
    }, []);
    hook(function () {
      if (!isMounted.current) {
        isMounted.current = true;
      } else {
        return effect();
      }
    }, deps);
  };
};
/* harmony default export */ var es_createUpdateEffect = ((/* unused pure expression or super */ null && (createUpdateEffect)));
;// CONCATENATED MODULE: ./node_modules/ahooks/es/useUpdateEffect/index.js


/* harmony default export */ var useUpdateEffect = (createUpdateEffect(react.useEffect));//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNzc1OTguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQStCO0FBQ3hCO0FBQ1A7QUFDQSxvQkFBb0IsZ0JBQU07QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsMERBQWUsa0VBQWtCLEk7O0FDbkJDO0FBQ3lCO0FBQzNELG9EQUFlLGtCQUFrQixDQUFDLGVBQVMsQ0FBQyIsInNvdXJjZXMiOlsid2VicGFjazovL2FudC1kZXNpZ24tcHJvLy4vbm9kZV9tb2R1bGVzL2Fob29rcy9lcy9jcmVhdGVVcGRhdGVFZmZlY3QvaW5kZXguanM/M2Y2NCIsIndlYnBhY2s6Ly9hbnQtZGVzaWduLXByby8uL25vZGVfbW9kdWxlcy9haG9va3MvZXMvdXNlVXBkYXRlRWZmZWN0L2luZGV4LmpzP2E2NTMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlUmVmIH0gZnJvbSAncmVhY3QnO1xuZXhwb3J0IHZhciBjcmVhdGVVcGRhdGVFZmZlY3QgPSBmdW5jdGlvbiAoaG9vaykge1xuICByZXR1cm4gZnVuY3Rpb24gKGVmZmVjdCwgZGVwcykge1xuICAgIHZhciBpc01vdW50ZWQgPSB1c2VSZWYoZmFsc2UpO1xuICAgIC8vIGZvciByZWFjdC1yZWZyZXNoXG4gICAgaG9vayhmdW5jdGlvbiAoKSB7XG4gICAgICByZXR1cm4gZnVuY3Rpb24gKCkge1xuICAgICAgICBpc01vdW50ZWQuY3VycmVudCA9IGZhbHNlO1xuICAgICAgfTtcbiAgICB9LCBbXSk7XG4gICAgaG9vayhmdW5jdGlvbiAoKSB7XG4gICAgICBpZiAoIWlzTW91bnRlZC5jdXJyZW50KSB7XG4gICAgICAgIGlzTW91bnRlZC5jdXJyZW50ID0gdHJ1ZTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHJldHVybiBlZmZlY3QoKTtcbiAgICAgIH1cbiAgICB9LCBkZXBzKTtcbiAgfTtcbn07XG5leHBvcnQgZGVmYXVsdCBjcmVhdGVVcGRhdGVFZmZlY3Q7IiwiaW1wb3J0IHsgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgY3JlYXRlVXBkYXRlRWZmZWN0IH0gZnJvbSAnLi4vY3JlYXRlVXBkYXRlRWZmZWN0JztcbmV4cG9ydCBkZWZhdWx0IGNyZWF0ZVVwZGF0ZUVmZmVjdCh1c2VFZmZlY3QpOyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==
//# sourceURL=webpack-internal:///77598
`)},52982:function(__unused_webpack_module,__webpack_exports__){eval(`var isBrowser = !!(typeof window !== 'undefined' && window.document && window.document.createElement);
/* harmony default export */ __webpack_exports__.Z = (isBrowser);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNTI5ODIuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQSxzREFBZSxTQUFTIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvYWhvb2tzL2VzL3V0aWxzL2lzQnJvd3Nlci5qcz82Yjg2Il0sInNvdXJjZXNDb250ZW50IjpbInZhciBpc0Jyb3dzZXIgPSAhISh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJyAmJiB3aW5kb3cuZG9jdW1lbnQgJiYgd2luZG93LmRvY3VtZW50LmNyZWF0ZUVsZW1lbnQpO1xuZXhwb3J0IGRlZmF1bHQgaXNCcm93c2VyOyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==
//# sourceURL=webpack-internal:///52982
`)}}]);
