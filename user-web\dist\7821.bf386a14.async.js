"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[7821],{28591:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(5574);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(67294);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(85893);





var withTriggerFormModal = function withTriggerFormModal(_ref) {
  var DefaultTrigger = _ref.defaultTrigger,
    contentRender = _ref.contentRender;
  var Component = function Component(_ref2) {
    var open = _ref2.open,
      trigger = _ref2.trigger,
      triggerRender = _ref2.triggerRender,
      onOpenChange = _ref2.onOpenChange,
      onSuccess = _ref2.onSuccess,
      modalProps = _ref2.modalProps,
      disabled = _ref2.disabled,
      buttonType = _ref2.buttonType;
    var _useState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false),
      _useState2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_0___default()(_useState, 2),
      _open = _useState2[0],
      _setOpen = _useState2[1];
    var openActive = typeof open === 'boolean' ? open : _open;
    var onOpenChangeActive = typeof onOpenChange === 'function' ? onOpenChange : _setOpen;
    var TriggerRender = triggerRender;
    var ContentRender = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(function () {
      return contentRender;
    }, [contentRender]);
    if (!ContentRender) return null;
    return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {
      children: [TriggerRender ? /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(TriggerRender, {
        changeOpen: _setOpen,
        open: open
      }) : trigger || (DefaultTrigger ? /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(DefaultTrigger, {
        disabled: disabled,
        changeOpen: _setOpen,
        buttonType: buttonType
      }) : null), openActive && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ContentRender, {
        open: openActive,
        trigger: trigger,
        onOpenChange: onOpenChangeActive,
        onSuccess: onSuccess,
        modalProps: modalProps
      })]
    });
  };
  return Component;
};
/* harmony default export */ __webpack_exports__.Z = (withTriggerFormModal);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///28591
`)},10592:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(5574);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _utils_common__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(89575);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(69753);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(7837);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(14726);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(67294);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(85893);









var ExportSheets = function ExportSheets(_ref) {
  var handleExport = _ref.handleExport,
    disabled = _ref.disabled;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false),
    _useState2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2___default()(_useState, 2),
    loading = _useState2[0],
    setLoading = _useState2[1];
  var onExport = /*#__PURE__*/function () {
    var _ref2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee() {
      return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            setLoading(true);
            _context.next = 3;
            return Promise.all([handleExport === null || handleExport === void 0 ? void 0 : handleExport(), (0,_utils_common__WEBPACK_IMPORTED_MODULE_3__/* .sleep */ ._v)(500)]);
          case 3:
            setLoading(false);
          case 4:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }));
    return function onExport() {
      return _ref2.apply(this, arguments);
    };
  }();
  var _useIntl = (0,_umijs_max__WEBPACK_IMPORTED_MODULE_4__.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(antd__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .ZP, {
    disabled: disabled,
    loading: loading,
    icon: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .Z, {}),
    onClick: onExport,
    children: formatMessage({
      id: 'common.export_excel'
    })
  });
};
/* harmony default export */ __webpack_exports__.Z = (ExportSheets);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///10592
`)},47821:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`
// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  Z: function() { return /* binding */ TimeSheetTask_TimesheetTaskTable; }
});

// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/slicedToArray.js
var slicedToArray = __webpack_require__(5574);
var slicedToArray_default = /*#__PURE__*/__webpack_require__.n(slicedToArray);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/regeneratorRuntime.js
var regeneratorRuntime = __webpack_require__(15009);
var regeneratorRuntime_default = /*#__PURE__*/__webpack_require__.n(regeneratorRuntime);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/defineProperty.js
var defineProperty = __webpack_require__(9783);
var defineProperty_default = /*#__PURE__*/__webpack_require__.n(defineProperty);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(99289);
var asyncToGenerator_default = /*#__PURE__*/__webpack_require__.n(asyncToGenerator);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/toConsumableArray.js
var toConsumableArray = __webpack_require__(19632);
var toConsumableArray_default = /*#__PURE__*/__webpack_require__.n(toConsumableArray);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/objectSpread2.js
var objectSpread2 = __webpack_require__(97857);
var objectSpread2_default = /*#__PURE__*/__webpack_require__.n(objectSpread2);
// EXTERNAL MODULE: ./src/services/timesheetsV2.ts
var timesheetsV2 = __webpack_require__(62872);
// EXTERNAL MODULE: ./src/services/utils.ts
var utils = __webpack_require__(467);
// EXTERNAL MODULE: ./src/utils/common.ts
var common = __webpack_require__(89575);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/ArrowLeftOutlined.js + 1 modules
var ArrowLeftOutlined = __webpack_require__(82826);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/ArrowRightOutlined.js + 1 modules
var ArrowRightOutlined = __webpack_require__(87603);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/EditOutlined.js
var EditOutlined = __webpack_require__(47389);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-table/es/components/EditableTable/index.js + 1 modules
var EditableTable = __webpack_require__(88280);
// EXTERNAL MODULE: ./src/.umi-production/exports.ts + 34 modules
var _umi_production_exports = __webpack_require__(7837);
// EXTERNAL MODULE: ./node_modules/antd/es/button/index.js + 9 modules
var es_button = __webpack_require__(14726);
// EXTERNAL MODULE: ./node_modules/antd/es/app/index.js + 1 modules
var app = __webpack_require__(31418);
// EXTERNAL MODULE: ./node_modules/antd/es/space/index.js + 2 modules
var space = __webpack_require__(42075);
// EXTERNAL MODULE: ./node_modules/lodash/fp.js
var fp = __webpack_require__(78230);
// EXTERNAL MODULE: ./node_modules/nanoid/index.browser.js
var index_browser = __webpack_require__(53416);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./node_modules/dayjs/dayjs.min.js
var dayjs_min = __webpack_require__(27484);
var dayjs_min_default = /*#__PURE__*/__webpack_require__.n(dayjs_min);
// EXTERNAL MODULE: ./node_modules/dayjs/locale/en.js
var en = __webpack_require__(25054);
var en_default = /*#__PURE__*/__webpack_require__.n(en);
// EXTERNAL MODULE: ./node_modules/dayjs/locale/vi.js
var vi = __webpack_require__(37553);
var vi_default = /*#__PURE__*/__webpack_require__.n(vi);
;// CONCATENATED MODULE: ./src/pages/MyUser/TimekeepingV2/utils/date.ts





var getDaysOfWeekNextOrPrevious = function getDaysOfWeekNextOrPrevious() {
  var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},
    dateString = _ref.dateString,
    weeksToAdd = _ref.weeksToAdd;
  var dayjsLocal = (0,_umi_production_exports.getLocale)() === 'vi-VN' ? (vi_default()) : (en_default());
  dayjs_min_default().locale(objectSpread2_default()(objectSpread2_default()({}, dayjsLocal), {}, {
    weekStart: 1 // start monday
  }));
  dayjs_min_default().extend(dayjs_min.localeData);
  var weekDays = dayjs_min_default().weekdays(true);
  // Chuy\u1EC3n \u0111\u1ED5i chu\u1ED7i ng\xE0y th\xE0nh \u0111\u1ED1i t\u01B0\u1EE3ng dayjs
  var date = dayjs_min_default()(dateString);

  // L\u1EA5y ng\xE0y b\u1EAFt \u0111\u1EA7u c\u1EE7a tu\u1EA7n (m\u1EB7c \u0111\u1ECBnh l\xE0 ch\u1EE7 nh\u1EADt)
  var startOfWeekDate = date.startOf('week');

  // T\xEDnh to\xE1n ng\xE0y c\u1EE7a tu\u1EA7n ti\u1EBFp theo ho\u1EB7c tu\u1EA7n tr\u01B0\u1EDBc
  var targetWeekDate = startOfWeekDate.add(weeksToAdd || 0, 'week');

  // T\u1EA1o m\u1EA3ng ch\u1EE9a t\u1EA5t c\u1EA3 c\xE1c ng\xE0y trong tu\u1EA7n
  var daysOfWeek = Array.from({
    length: 7
  }, function (_, index) {
    var day = targetWeekDate.add(index, 'day');
    return {
      dateFormat: day.format('YYYY-MM-DD'),
      dateOfWeek: weekDays[index],
      timeIOS: day.toISOString(),
      date: day.format('DD/MM')
    };
  });
  return daysOfWeek;
};
;// CONCATENATED MODULE: ./src/pages/MyUser/TimekeepingV2/hooks/useTimeSheet.ts



var useTimeSheet = function useTimeSheet() {
  var _useState = (0,react.useState)(null),
    _useState2 = slicedToArray_default()(_useState, 2),
    employee = _useState2[0],
    setEmployee = _useState2[1];
  var onChangeEmployee = (0,react.useCallback)(function (newEmployee) {
    setEmployee(newEmployee);
  }, [setEmployee]);
  var _useState3 = (0,react.useState)(getDaysOfWeekNextOrPrevious()),
    _useState4 = slicedToArray_default()(_useState3, 2),
    currentWeek = _useState4[0],
    setCurrentWeek = _useState4[1];
  var startOfWeek = (0,react.useMemo)(function () {
    return currentWeek[0];
  }, [currentWeek]);
  var endOfWeek = (0,react.useMemo)(function () {
    return currentWeek[6];
  }, [currentWeek]);
  var changeToWeek = (0,react.useCallback)(function (weeksToAdd) {
    setCurrentWeek(getDaysOfWeekNextOrPrevious({
      dateString: startOfWeek.timeIOS,
      weeksToAdd: weeksToAdd
    }));
  }, [startOfWeek, setCurrentWeek]);
  return {
    employee: employee,
    onChangeEmployee: onChangeEmployee,
    currentWeek: currentWeek,
    startOfWeek: startOfWeek,
    endOfWeek: endOfWeek,
    changeToWeek: changeToWeek
  };
};
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/objectWithoutProperties.js
var objectWithoutProperties = __webpack_require__(13769);
var objectWithoutProperties_default = /*#__PURE__*/__webpack_require__.n(objectWithoutProperties);
// EXTERNAL MODULE: ./src/common/contanst/constanst.ts
var constanst = __webpack_require__(86604);
// EXTERNAL MODULE: ./src/services/cropManager.ts
var cropManager = __webpack_require__(77890);
// EXTERNAL MODULE: ./src/services/farming-plan.ts
var services_farming_plan = __webpack_require__(74459);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/PlusOutlined.js
var PlusOutlined = __webpack_require__(51042);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/layouts/ProForm/index.js + 3 modules
var ProForm = __webpack_require__(34994);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/Digit/index.js
var Digit = __webpack_require__(31199);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/Switch/index.js
var Switch = __webpack_require__(52688);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/Dependency/index.js
var Dependency = __webpack_require__(97462);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/Text/index.js
var Text = __webpack_require__(5966);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/Select/index.js
var Select = __webpack_require__(64317);
// EXTERNAL MODULE: ./src/HOC/withTriggerFormModal/index.tsx
var withTriggerFormModal = __webpack_require__(28591);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/layouts/ModalForm/index.js
var ModalForm = __webpack_require__(37476);
// EXTERNAL MODULE: ./node_modules/react/jsx-runtime.js
var jsx_runtime = __webpack_require__(85893);
;// CONCATENATED MODULE: ./src/pages/MyUser/TimekeepingV2/components/DetailTimesheet/TimeSheetTask/AddTask.tsx




var _excluded = ["farming_plan", "crop"];












var AddTask_space = '########++++++!!!!!%%%';
var ContentForm = function ContentForm(_ref) {
  var open = _ref.open,
    onOpenChange = _ref.onOpenChange,
    onSuccess = _ref.onSuccess,
    modalProps = _ref.modalProps,
    trigger = _ref.trigger;
  // const { formatMessage } = useIntl();
  var _ProForm$useForm = ProForm/* ProForm */.A.useForm(),
    _ProForm$useForm2 = slicedToArray_default()(_ProForm$useForm, 1),
    form = _ProForm$useForm2[0];
  // const { message } = App.useApp();

  return /*#__PURE__*/(0,jsx_runtime.jsxs)(ModalForm/* ModalForm */.Y, {
    title: "Add task",
    name: "add:task",
    form: form,
    width: 500,
    onFinish: ( /*#__PURE__*/function () {
      var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(values) {
        var _modalProps$onSelectT, _values$task;
        return regeneratorRuntime_default()().wrap(function _callee$(_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              // if (!values.task) {
              //   message.error(\`Task is required\`);
              //   return false;
              // }
              // const [start_date, end_date] = values.rangeDate;
              modalProps === null || modalProps === void 0 || (_modalProps$onSelectT = modalProps.onSelectTask) === null || _modalProps$onSelectT === void 0 || _modalProps$onSelectT.call(modalProps, {
                label: !values.isSelectTask ? values.label : (_values$task = values.task) === null || _values$task === void 0 || (_values$task = _values$task.split(AddTask_space)) === null || _values$task === void 0 ? void 0 : _values$task[1],
                start_date: '',
                end_date: '',
                completion_percentage: values.completion_percentage
              });
              return _context.abrupt("return", true);
            case 2:
            case "end":
              return _context.stop();
          }
        }, _callee);
      }));
      return function (_x) {
        return _ref2.apply(this, arguments);
      };
    }()),
    open: open,
    onOpenChange: onOpenChange,
    trigger: trigger,
    initialValues: {
      isSelectTask: true,
      completion_percentage: 0
    },
    children: [/*#__PURE__*/(0,jsx_runtime.jsx)(Digit/* default */.Z, {
      min: 0,
      max: 100,
      rules: [{
        required: true
      }],
      label: "Ti\\u1EBFn \\u0111\\u1ED9 ho\\xE0n th\\xE0nh",
      name: "completion_percentage"
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(Switch/* default */.Z, {
      name: "isSelectTask",
      label: "Ch\\u1ECDn c\\xF4ng vi\\u1EC7c t\\u1EEB v\\u1EE5 m\\xF9a"
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(Dependency/* default */.Z, {
      name: ['isSelectTask'],
      children: function children(_ref3) {
        var isSelectTask = _ref3.isSelectTask;
        if (!isSelectTask) {
          return /*#__PURE__*/(0,jsx_runtime.jsx)(Text/* default */.Z, {
            rules: [{
              required: true
            }],
            label: "T\\xEAn",
            name: "label"
          });
        }
        return /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
          children: [/*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
            showSearch: true,
            label: "Ch\\u1ECDn v\\u1EE5 m\\xF9a",
            rules: [{
              required: true
            }],
            onChange: function onChange() {
              form.setFieldsValue({
                farming_plan: undefined,
                farming_plan_state: undefined,
                task: undefined
              });
            },
            request: /*#__PURE__*/asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2() {
              var res;
              return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
                while (1) switch (_context2.prev = _context2.next) {
                  case 0:
                    _context2.next = 2;
                    return (0,cropManager/* getCropManagementInfoList */.Gz)({
                      page: 1,
                      size: constanst/* DEFAULT_PAGE_SIZE_ALL */.YY
                    });
                  case 2:
                    res = _context2.sent;
                    return _context2.abrupt("return", res.data.map(function (item) {
                      return {
                        label: item.label,
                        value: item.name
                      };
                    }));
                  case 4:
                  case "end":
                    return _context2.stop();
                }
              }, _callee2);
            })),
            name: "crop",
            colProps: {
              md: 8,
              sm: 24
            }
          }), /*#__PURE__*/(0,jsx_runtime.jsx)(Dependency/* default */.Z, {
            name: ['crop'],
            children: function children(_ref5) {
              var crop = _ref5.crop;
              return /*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
                showSearch: true,
                dependencies: ['crop'],
                label: "Ch\\u1ECDn k\\u1EBF ho\\u1EA1ch",
                rules: [{
                  required: true
                }],
                onChange: function onChange(v) {
                  form.setFieldsValue({
                    //  farming_plan: undefined,
                    farming_plan_state: undefined,
                    task: undefined
                  });
                },
                disabled: !crop,
                request: /*#__PURE__*/asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee3() {
                  var res;
                  return regeneratorRuntime_default()().wrap(function _callee3$(_context3) {
                    while (1) switch (_context3.prev = _context3.next) {
                      case 0:
                        if (crop) {
                          _context3.next = 2;
                          break;
                        }
                        return _context3.abrupt("return", []);
                      case 2:
                        _context3.next = 4;
                        return (0,services_farming_plan/* getFarmingPlanList */.Qo)({
                          page: 1,
                          size: constanst/* DEFAULT_PAGE_SIZE_ALL */.YY,
                          filters: [[constanst/* DOCTYPE_ERP */.lH.iotFarmingPlan, 'crop', '=', crop]]
                        });
                      case 4:
                        res = _context3.sent;
                        return _context3.abrupt("return", res.data.map(function (item) {
                          return {
                            label: item.label,
                            value: item.name
                          };
                        }));
                      case 6:
                      case "end":
                        return _context3.stop();
                    }
                  }, _callee3);
                })),
                name: "farming_plan"
              });
            }
          }), /*#__PURE__*/(0,jsx_runtime.jsx)(Dependency/* default */.Z, {
            name: ['farming_plan'],
            ignoreFormListField: true,
            children: function children(_ref7) {
              var farming_plan = _ref7.farming_plan,
                crop = _ref7.crop,
                rest = objectWithoutProperties_default()(_ref7, _excluded);
              return /*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
                showSearch: true,
                dependencies: ['farming_plan'],
                label: "Ch\\u1ECDn giai \\u0111o\\u1EA1n",
                rules: [{
                  required: true
                }],
                disabled: !farming_plan,
                onChange: function onChange(v) {
                  form.setFieldsValue({
                    //  farming_plan: undefined,
                    // farming_plan_state: undefined,
                    task: undefined
                  });
                },
                name: "farming_plan_state",
                request: /*#__PURE__*/asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee4() {
                  var res;
                  return regeneratorRuntime_default()().wrap(function _callee4$(_context4) {
                    while (1) switch (_context4.prev = _context4.next) {
                      case 0:
                        if (farming_plan) {
                          _context4.next = 2;
                          break;
                        }
                        return _context4.abrupt("return", []);
                      case 2:
                        _context4.next = 4;
                        return (0,services_farming_plan/* getFarmingPlanState */.jY)({
                          page: 1,
                          size: constanst/* DEFAULT_PAGE_SIZE_ALL */.YY,
                          filters: [[constanst/* DOCTYPE_ERP */.lH.iotFarmingPlanState, 'farming_plan', '=', farming_plan]]
                        });
                      case 4:
                        res = _context4.sent;
                        return _context4.abrupt("return", res.data.map(function (item) {
                          return {
                            label: item.label,
                            value: item.name
                          };
                        }));
                      case 6:
                      case "end":
                        return _context4.stop();
                    }
                  }, _callee4);
                }))
              });
            }
          }), /*#__PURE__*/(0,jsx_runtime.jsx)(Dependency/* default */.Z, {
            name: ['farming_plan_state'],
            children: function children(_ref9) {
              var farming_plan_state = _ref9.farming_plan_state;
              return /*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
                showSearch: true,
                dependencies: ['farming_plan_state'],
                label: "Ch\\u1ECDn c\\xF4ng vi\\u1EC7c",
                rules: [{
                  required: true
                }],
                disabled: !farming_plan_state,
                name: "task",
                request: /*#__PURE__*/asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee5() {
                  var res;
                  return regeneratorRuntime_default()().wrap(function _callee5$(_context5) {
                    while (1) switch (_context5.prev = _context5.next) {
                      case 0:
                        if (farming_plan_state) {
                          _context5.next = 2;
                          break;
                        }
                        return _context5.abrupt("return", []);
                      case 2:
                        _context5.next = 4;
                        return (0,services_farming_plan/* getTaskManagerList */.UM)({
                          page: 1,
                          size: constanst/* DEFAULT_PAGE_SIZE_ALL */.YY,
                          filters: [[constanst/* DOCTYPE_ERP */.lH.iotFarmingPlanTask, 'farming_plan_state', 'like', farming_plan_state]]
                        });
                      case 4:
                        res = _context5.sent;
                        return _context5.abrupt("return", res.data.map(function (item) {
                          return {
                            label: item.label,
                            value: "".concat(item.name).concat(AddTask_space).concat(item.label) // trick
                          };
                        }));
                      case 6:
                      case "end":
                        return _context5.stop();
                    }
                  }, _callee5);
                }))
              });
            }
          })]
        });
      }
    })]
  });
};
var AddNewZone = (0,withTriggerFormModal/* default */.Z)({
  defaultTrigger: function defaultTrigger(_ref11) {
    var changeOpen = _ref11.changeOpen,
      disabled = _ref11.disabled;
    return /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
      disabled: disabled,
      type: "primary",
      icon: /*#__PURE__*/(0,jsx_runtime.jsx)(PlusOutlined/* default */.Z, {}),
      onClick: function onClick() {
        return changeOpen(true);
      },
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
        id: "common.add_new_task"
      })
    });
  },
  contentRender: ContentForm
});
/* harmony default export */ var AddTask = (AddNewZone);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/DeleteOutlined.js
var DeleteOutlined = __webpack_require__(82061);
;// CONCATENATED MODULE: ./src/pages/MyUser/TimekeepingV2/hooks/useDeleteTimeSheetTask.ts



function useDeleteTimeSheetTask() {
  var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},
    _onSuccess = _ref.onSuccess;
  var _App$useApp = app/* default */.Z.useApp(),
    message = _App$useApp.message;
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  return (0,_umi_production_exports.useRequest)(timesheetsV2/* deleteTimesheetTask */.Mr, {
    manual: true,
    onError: function onError(err) {
      message.error(err.message);
    },
    onSuccess: function onSuccess() {
      message.success(formatMessage({
        id: 'common.success'
      }));
      _onSuccess === null || _onSuccess === void 0 || _onSuccess();
    }
  });
}
;// CONCATENATED MODULE: ./src/pages/MyUser/TimekeepingV2/components/DetailTimesheet/TimeSheetTask/DeleteTask.tsx







var DeleteTask = function DeleteTask(_ref) {
  var id = _ref.id,
    handleReload = _ref.handleReload;
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  var _useDeleteTimeSheetTa = useDeleteTimeSheetTask({
      onSuccess: function onSuccess() {
        handleReload === null || handleReload === void 0 || handleReload();
      }
    }),
    run = _useDeleteTimeSheetTa.run,
    loading = _useDeleteTimeSheetTa.loading;
  var _App$useApp = app/* default */.Z.useApp(),
    modal = _App$useApp.modal;
  return /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
    loading: loading,
    icon: /*#__PURE__*/(0,jsx_runtime.jsx)(DeleteOutlined/* default */.Z, {}),
    danger: true,
    onClick: function onClick() {
      modal.confirm({
        title: formatMessage({
          id: 'common.sentences.confirm-delete'
        }),
        onOk: function onOk() {
          return asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
            return regeneratorRuntime_default()().wrap(function _callee$(_context) {
              while (1) switch (_context.prev = _context.next) {
                case 0:
                  _context.next = 2;
                  return run(id);
                case 2:
                case "end":
                  return _context.stop();
              }
            }, _callee);
          }))();
        },
        okButtonProps: {
          danger: true
        }
      });
    }
  });
};
/* harmony default export */ var TimeSheetTask_DeleteTask = (DeleteTask);
// EXTERNAL MODULE: ./src/pages/MyUser/TimekeepingV2/components/DetailTimesheet/TimeSheetTask/ExportSheets.tsx
var ExportSheets = __webpack_require__(10592);
;// CONCATENATED MODULE: ./src/pages/MyUser/TimekeepingV2/components/DetailTimesheet/TimeSheetTask/TimesheetTaskTable.tsx























var isClientKey = Symbol["for"]((0,index_browser/* nanoid */.x0)());
var isClientTotalKey = Symbol["for"]((0,index_browser/* nanoid */.x0)());
var TimesheetTaskTable = function TimesheetTaskTable(_ref) {
  var children = _ref.children,
    timeSheetId = _ref.timeSheetId,
    readonly = _ref.readonly,
    onChange = _ref.onChange,
    approvalId = _ref.approvalId;
  var _useModel = (0,_umi_production_exports.useModel)('@@initialState'),
    setInitialState = _useModel.setInitialState,
    initialState = _useModel.initialState;
  (0,react.useEffect)(function () {
    setInitialState(objectSpread2_default()(objectSpread2_default()({}, initialState), {}, {
      collapsed: true
    }));
  }, []);
  var _useTimeSheet = useTimeSheet(),
    currentWeek = _useTimeSheet.currentWeek,
    changeToWeek = _useTimeSheet.changeToWeek,
    startOfWeek = _useTimeSheet.startOfWeek,
    endOfWeek = _useTimeSheet.endOfWeek;
  var actionRef = (0,react.useRef)(null);
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  var reload = function reload() {
    var _actionRef$current;
    (_actionRef$current = actionRef.current) === null || _actionRef$current === void 0 || _actionRef$current.reload();
    onChange === null || onChange === void 0 || onChange();
  };
  var columns = (0,react.useMemo)(function () {
    return [{
      title: formatMessage({
        id: 'common.task_name'
      }),
      dataIndex: 'label',
      render: function render(dom, entity, index, action, schema) {
        if (entity[isClientTotalKey]) {
          return /*#__PURE__*/(0,jsx_runtime.jsx)("span", {
            style: {
              fontWeight: 600
              // fontSize: 18,
            },
            children: dom
          });
        }
        return dom;
      },
      formItemProps: {
        rules: [{
          required: true
        }]
      },
      width: 'auto'
    }, {
      title: /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
        icon: /*#__PURE__*/(0,jsx_runtime.jsx)(ArrowLeftOutlined/* default */.Z, {}),
        onClick: function onClick() {
          changeToWeek(-1);
        },
        type: "link"
      }),
      search: false,
      width: 30,
      formItemProps: {
        hidden: true
      }
    }].concat(toConsumableArray_default()(currentWeek.map(function (item) {
      return {
        title: /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
          style: {
            textTransform: 'capitalize'
          },
          children: [/*#__PURE__*/(0,jsx_runtime.jsx)("span", {
            children: item.dateOfWeek
          }), /*#__PURE__*/(0,jsx_runtime.jsx)("br", {}), /*#__PURE__*/(0,jsx_runtime.jsx)("span", {
            children: item.date
          })]
        }),
        // title: \`\${item.dateOfWeek} - \\n \${item.date}\`,
        search: false,
        dataIndex: ['task', item.dateFormat],
        width: 80,
        valueType: 'digit',
        render: function render(text, record, index, action) {
          var _record$cells;
          if (record[isClientTotalKey]) {
            var _total;
            var total = (record === null || record === void 0 || (_total = record.total) === null || _total === void 0 || (_total = _total[item.dateFormat]) === null || _total === void 0 ? void 0 : _total.reduce(function (acc, item) {
              return acc + item.work_hour;
            }, 0)) || 0;
            return /*#__PURE__*/(0,jsx_runtime.jsx)("span", {
              style: {
                fontWeight: 600
              },
              children: total
            });
          }
          return record === null || record === void 0 || (_record$cells = record.cells) === null || _record$cells === void 0 || (_record$cells = _record$cells.find(function (c) {
            return c.work_date === item.dateFormat;
          })) === null || _record$cells === void 0 ? void 0 : _record$cells.work_hour;
        },
        fieldProps: function fieldProps(form, config) {
          return objectSpread2_default()(objectSpread2_default()({}, config), {}, {
            style: {
              width: 70
            }
          });
        },
        formItemProps: {
          // name: ['task', item.dateFormat],

          rules: [{
            message: 'H\xE3y nh\u1EADp th\xF4ng tin ',
            required: true
          }]
        }
      };
    })), [{
      title: /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
        icon: /*#__PURE__*/(0,jsx_runtime.jsx)(ArrowRightOutlined/* default */.Z, {}),
        type: "link",
        onClick: function onClick() {
          changeToWeek(1);
        }
      }),
      search: false,
      width: 40,
      formItemProps: {
        hidden: true
      }
    }, {
      title: formatMessage({
        id: 'common.completion_schedule'
      }),
      dataIndex: 'completion_percentage',
      valueType: 'progress',
      // render(dom, entity, index, action, schema) {
      //   if ((entity as any)[isClientTotalKey]) {
      //     return (entity as any)?.['totalCompletionSchedule'];
      //   }
      //   return formatNumber(Number((entity as any).completion_percentage), {
      //     digits: 2,
      //   });
      // },
      width: 116
    }, {
      title: formatMessage({
        id: 'common.total'
      }),
      fieldProps: {
        disabled: true
      },
      dataIndex: 'total',
      render: function render(dom, entity, index, action, schema) {
        var _entity$cells;
        if (entity[isClientTotalKey]) {
          return /*#__PURE__*/(0,jsx_runtime.jsx)("span", {
            style: {
              fontWeight: 600
            },
            children: entity['totalAllCells']
          });
        }
        return (entity === null || entity === void 0 || (_entity$cells = entity.cells) === null || _entity$cells === void 0 ? void 0 : _entity$cells.reduce(function (prev, c) {
          return prev + c.work_hour || 0;
        }, 0)) || 0;
      },
      width: 80
    }, {
      width: 90,
      valueType: 'option',
      render: function render(dom, entity, index, action, schema) {
        if (entity[isClientTotalKey] || readonly) {
          return [];
        }
        return [/*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
          icon: /*#__PURE__*/(0,jsx_runtime.jsx)(EditOutlined/* default */.Z, {}),
          onClick: function onClick() {
            var _actionRef$current2;
            (_actionRef$current2 = actionRef.current) === null || _actionRef$current2 === void 0 || _actionRef$current2.startEditable(entity.name);
          }
        }, "edit"), /*#__PURE__*/(0,jsx_runtime.jsx)(TimeSheetTask_DeleteTask, {
          handleReload: reload,
          id: entity.name
        }, "delete")];
      }
    }]);
  }, [currentWeek]);
  (0,react.useEffect)(function () {
    if (timeSheetId && startOfWeek) {
      reload();
    }
  }, [timeSheetId, startOfWeek]);
  var _App$useApp = app/* default */.Z.useApp(),
    message = _App$useApp.message;
  var dataRef = (0,react.useRef)(null);
  var handleExportData = /*#__PURE__*/function () {
    var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
      var _dataRef$current, _dataRef$current2;
      var data;
      return regeneratorRuntime_default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            if ((_dataRef$current = dataRef.current) !== null && _dataRef$current !== void 0 && _dataRef$current.length) {
              _context.next = 3;
              break;
            }
            message.error('B\u1EA3ng ch\u01B0a c\xF3 data');
            return _context.abrupt("return");
          case 3:
            data = dataRef === null || dataRef === void 0 || (_dataRef$current2 = dataRef.current) === null || _dataRef$current2 === void 0 ? void 0 : _dataRef$current2.map(function (dataItem) {
              var _dataItem$cells2;
              return objectSpread2_default()(objectSpread2_default()(defineProperty_default()({}, formatMessage({
                id: 'common.task_name'
              }), dataItem.label), currentWeek.reduce(function (prev, item) {
                var _dataItem$cells;
                return objectSpread2_default()(objectSpread2_default()({}, prev), {}, defineProperty_default()({}, "".concat(item.dateOfWeek, " \\n ").concat(item.dateFormat), (dataItem === null || dataItem === void 0 || (_dataItem$cells = dataItem.cells) === null || _dataItem$cells === void 0 || (_dataItem$cells = _dataItem$cells.find(function (c) {
                  return c.work_date === item.dateFormat;
                })) === null || _dataItem$cells === void 0 ? void 0 : _dataItem$cells.work_hour) || 0));
              }, {})), {}, defineProperty_default()(defineProperty_default()({}, formatMessage({
                id: 'common.completion_schedule'
              }), (0,common/* formatNumber */.uf)(Number(dataItem.completion_percentage), {
                defaultValue: 0,
                digits: 2
              })), formatMessage({
                id: 'common.total'
              }), (dataItem === null || dataItem === void 0 || (_dataItem$cells2 = dataItem.cells) === null || _dataItem$cells2 === void 0 ? void 0 : _dataItem$cells2.reduce(function (prev, c) {
                return prev + c.work_hour || 0;
              }, 0)) || 0));
            });
            _context.next = 6;
            return (0,utils/* downloadExcelData */.bF)(data);
          case 6:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }));
    return function handleExportData() {
      return _ref2.apply(this, arguments);
    };
  }();
  var _useState = (0,react.useState)([]),
    _useState2 = slicedToArray_default()(_useState, 2),
    editableKeys = _useState2[0],
    setEditableRowKeys = _useState2[1];
  var isDisabledAction = !timeSheetId;
  return /*#__PURE__*/(0,jsx_runtime.jsx)(EditableTable/* default */.Z, {
    scroll: {
      x: 1100
    },
    recordCreatorProps: {
      record: function record() {
        return defineProperty_default()({
          name: (0,index_browser/* nanoid */.x0)(),
          // [startOfWeek.dateFormat]: 0,
          label: 'Task',
          task: objectSpread2_default()({}, currentWeek.reduce(function (prev, item) {
            return objectSpread2_default()(objectSpread2_default()({}, prev), {}, defineProperty_default()({}, item.dateFormat, 0));
          }, {}))
        }, isClientKey, true);
      },
      // https://github.com/ant-design/pro-components/issues/1726#issuecomment-765814817
      // th\xEAm nhi\u1EC1u d\xF2ng 1 l\xFAc
      // newRecordType: 'dataSource',
      // th\xEAm 1 dong 1 l\xFAc
      newRecordType: 'cache',
      disabled: isDisabledAction,
      hidden: true
    }
    // pagination={{
    //   pageSize: 200,
    // }}
    ,
    actionRef: actionRef,
    columns: columns,
    rowKey: 'name',
    size: "small",
    request: ( /*#__PURE__*/function () {
      var _ref4 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2(params, sort, filter) {
        var res, dataFormat, allCells, totalAllCells, total, totalCompletionSchedule;
        return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
          while (1) switch (_context2.prev = _context2.next) {
            case 0:
              if (timeSheetId) {
                _context2.next = 2;
                break;
              }
              return _context2.abrupt("return", {
                data: []
              });
            case 2:
              if (!approvalId) {
                _context2.next = 8;
                break;
              }
              _context2.next = 5;
              return (0,timesheetsV2/* getTimeSheetTaskRecords */.ZS)({
                page: params.current || 1,
                size: params.pageSize || 50,
                approval_id: approvalId,
                work_date_from: startOfWeek.dateFormat,
                work_date_to: endOfWeek.dateFormat
              });
            case 5:
              _context2.t0 = _context2.sent;
              _context2.next = 11;
              break;
            case 8:
              _context2.next = 10;
              return (0,timesheetsV2/* getTimeSheetTasks */.Qf)({
                page: params.current || 1,
                size: params.pageSize || 50,
                timesheet_id: timeSheetId,
                work_date_from: startOfWeek.dateFormat,
                work_date_to: endOfWeek.dateFormat
              });
            case 10:
              _context2.t0 = _context2.sent;
            case 11:
              res = _context2.t0;
              dataFormat = res.data.map(function (item) {
                var _item$cells;
                return objectSpread2_default()(objectSpread2_default()({}, item), {}, {
                  // for form
                  task: objectSpread2_default()(objectSpread2_default()({}, currentWeek.reduce(function (acc, item) {
                    return objectSpread2_default()(objectSpread2_default()({}, acc), {}, defineProperty_default()({}, item.dateFormat, 0));
                  }, {})), (_item$cells = item.cells) === null || _item$cells === void 0 ? void 0 : _item$cells.reduce(function (acc, cell) {
                    return objectSpread2_default()(objectSpread2_default()({}, acc), {}, defineProperty_default()({}, cell.work_date, cell.work_hour));
                  }, {}))
                });
              });
              dataRef.current = dataFormat;
              if (dataFormat.length) {
                _context2.next = 16;
                break;
              }
              return _context2.abrupt("return", {});
            case 16:
              allCells = dataFormat.map(function (item) {
                return item.cells;
              }).flat(1);
              totalAllCells = allCells.reduce(function (acc, c) {
                return acc + c.work_hour;
              }, 0);
              total = (0,fp.groupBy)(function (i) {
                return i.work_date;
              }, allCells);
              totalCompletionSchedule = dataFormat.reduce(function (acc, c) {
                return [acc[0] + ((0,common/* formatNumber */.uf)(Number(c === null || c === void 0 ? void 0 : c.completion_percentage) || '0', {
                  defaultValue: 0,
                  digits: 2
                }) || 0), acc[1] + 100];
              },
              // current, total
              [0, 0]).reduce(function (acc, c, index, arr) {
                return arr[0] / arr[1] * 100;
              }, 0);
              return _context2.abrupt("return", {
                data: [].concat(toConsumableArray_default()(dataFormat), [defineProperty_default()(defineProperty_default()(defineProperty_default()(defineProperty_default()({
                  // cho t\xEDnh t\u1ED5ng
                  label: formatMessage({
                    id: 'common.total'
                  }),
                  cells: []
                }, isClientTotalKey, true), "total", total), "totalAllCells", totalAllCells), "completion_percentage", (0,common/* formatNumber */.uf)(Number(totalCompletionSchedule)))]),
                success: true
              });
            case 21:
            case "end":
              return _context2.stop();
          }
        }, _callee2);
      }));
      return function (_x, _x2, _x3) {
        return _ref4.apply(this, arguments);
      };
    }()),
    options: {
      // search: true,
      density: false,
      fullScreen: false,
      densityIcon: false,
      setting: false
    },
    editable: {
      editableKeys: editableKeys,
      onChange: setEditableRowKeys,
      type: 'multiple',
      saveText: 'L\u01B0u',
      // editableKeys,
      //  \u0111\u1ED5i ch\u1EEF trung qu\u1ED1c khi validate th\xEAm 1 ho\u1EB7c nhi\u1EC1u d\xF2ng
      onlyAddOneLineAlertMessage: 'Ch\u1EC9 c\xF3 th\u1EC3 ch\u1EC9nh s\u1EEDa m\u1ED9t h\xE0ng c\xF9ng m\u1ED9t l\xFAc',
      onlyOneLineEditorAlertMessage: 'Ch\u1EC9 c\xF3 th\u1EC3 ch\u1EC9nh s\u1EEDa m\u1ED9t h\xE0ng c\xF9ng m\u1ED9t l\xFAc',
      actionRender: function actionRender(row, config, defaultDom) {
        return [/*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
          children: [defaultDom.save, defaultDom.cancel]
        })];
      },
      onSave: function () {
        var _onSave = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee3(rowKey, data, row) {
          var dataFormCreate, cells, dataReq, res, _error$response, mess, dataEdit, _cells, _dataReq;
          return regeneratorRuntime_default()().wrap(function _callee3$(_context3) {
            while (1) switch (_context3.prev = _context3.next) {
              case 0:
                if (!row[isClientKey]) {
                  _context3.next = 19;
                  break;
                }
                _context3.prev = 1;
                dataFormCreate = data;
                cells = Object.entries(dataFormCreate.task).map(function (_ref6) {
                  var _ref7 = slicedToArray_default()(_ref6, 2),
                    work_date = _ref7[0],
                    work_hour = _ref7[1];
                  return {
                    work_date: work_date,
                    work_hour: work_hour
                  };
                });
                dataReq = {
                  label: dataFormCreate.label,
                  start_date: dataFormCreate.start_date,
                  end_date: dataFormCreate.end_date,
                  completion_percentage: dataFormCreate.completion_percentage,
                  timesheet_id: timeSheetId,
                  cells: cells
                };
                _context3.next = 7;
                return (0,timesheetsV2/* createTimeSeedTask */.Kv)(dataReq);
              case 7:
                res = _context3.sent;
                if (res.result.status === 'success') {
                  message.success(formatMessage({
                    id: 'common.success'
                  }));
                } else {
                  message.warning(res.result.message);
                }
                reload();
                return _context3.abrupt("return", true);
              case 13:
                _context3.prev = 13;
                _context3.t0 = _context3["catch"](1);
                mess = (_context3.t0 === null || _context3.t0 === void 0 || (_error$response = _context3.t0.response) === null || _error$response === void 0 || (_error$response = _error$response.data) === null || _error$response === void 0 ? void 0 : _error$response.message) || formatMessage({
                  id: 'common.error'
                });
                console.log('mess: ', mess);
                message.error(mess);
                throw new Error(_context3.t0 === null || _context3.t0 === void 0 ? void 0 : _context3.t0.message);
              case 19:
                _context3.prev = 19;
                dataEdit = data;
                _cells = Object.entries(dataEdit.task).map(function (_ref8) {
                  var _dataEdit$cells;
                  var _ref9 = slicedToArray_default()(_ref8, 2),
                    work_date = _ref9[0],
                    work_hour = _ref9[1];
                  return {
                    work_date: work_date,
                    work_hour: work_hour,
                    name: dataEdit === null || dataEdit === void 0 || (_dataEdit$cells = dataEdit.cells) === null || _dataEdit$cells === void 0 || (_dataEdit$cells = _dataEdit$cells.find(function (item) {
                      return item.work_date === work_date;
                    })) === null || _dataEdit$cells === void 0 ? void 0 : _dataEdit$cells.name
                  };
                });
                _dataReq = {
                  name: dataEdit.name,
                  label: dataEdit.label,
                  start_date: dataEdit.start_date,
                  end_date: dataEdit.end_date,
                  completion_percentage: dataEdit.completion_percentage,
                  timesheet_id: timeSheetId,
                  cells: _cells
                };
                _context3.next = 25;
                return (0,timesheetsV2/* editTimesheet */.v6)(_dataReq);
              case 25:
                message.success(formatMessage({
                  id: 'common.success'
                }));
                reload();
                return _context3.abrupt("return", true);
              case 30:
                _context3.prev = 30;
                _context3.t1 = _context3["catch"](19);
                console.log('error: ', _context3.t1);
                message.error(formatMessage({
                  id: 'common.error'
                }));
                throw new Error(_context3.t1 === null || _context3.t1 === void 0 ? void 0 : _context3.t1.message);
              case 35:
              case "end":
                return _context3.stop();
            }
          }, _callee3, null, [[1, 13], [19, 30]]);
        }));
        function onSave(_x4, _x5, _x6) {
          return _onSave.apply(this, arguments);
        }
        return onSave;
      }()
    },
    toolBarRender: function toolBarRender() {
      return readonly ? [] : [] || 0;
    },
    headerTitle: readonly ? null : /*#__PURE__*/(0,jsx_runtime.jsx)(space/* default */.Z, {
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(AddTask, {
        modalProps: {
          onSelectTask: function onSelectTask(task) {
            var _actionRef$current3;
            (_actionRef$current3 = actionRef.current) === null || _actionRef$current3 === void 0 || _actionRef$current3.addEditRecord(defineProperty_default()({
              name: (0,index_browser/* nanoid */.x0)(),
              // [startOfWeek.dateFormat]: 0,
              label: task === null || task === void 0 ? void 0 : task.label,
              start_date: task === null || task === void 0 ? void 0 : task.start_date,
              end_date: task === null || task === void 0 ? void 0 : task.end_date,
              completion_percentage: task === null || task === void 0 ? void 0 : task.completion_percentage,
              task: objectSpread2_default()({}, currentWeek.reduce(function (prev, item) {
                return objectSpread2_default()(objectSpread2_default()({}, prev), {}, defineProperty_default()({}, item.dateFormat, 0));
              }, {}))
            }, isClientKey, true));
          }
        },
        disabled: isDisabledAction
      })
    })
  });
};
/* harmony default export */ var TimeSheetTask_TimesheetTaskTable = (TimesheetTaskTable);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///47821
`)},77890:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Ak: function() { return /* binding */ updateCrop; },
/* harmony export */   Gz: function() { return /* binding */ getCropManagementInfoList; },
/* harmony export */   Ir: function() { return /* binding */ deleteCropNote; },
/* harmony export */   JB: function() { return /* binding */ addParticipantInCrop; },
/* harmony export */   LY: function() { return /* binding */ getTemplateCropList; },
/* harmony export */   No: function() { return /* binding */ getParticipantsInCrop; },
/* harmony export */   TQ: function() { return /* binding */ getCropList; },
/* harmony export */   Tq: function() { return /* binding */ deleteParticipantsInCrop; },
/* harmony export */   WP: function() { return /* binding */ getStatisticNoteList; },
/* harmony export */   bx: function() { return /* binding */ updateCropNote; },
/* harmony export */   mP: function() { return /* binding */ createCrop; },
/* harmony export */   rC: function() { return /* binding */ createCropNote; },
/* harmony export */   vW: function() { return /* binding */ getCurrentStateOfCrop; },
/* harmony export */   xu: function() { return /* binding */ getCropNoteList; }
/* harmony export */ });
/* unused harmony exports updateParticipantsInCrop, getStatisticPestList */
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(7837);
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(467);




var getCropList = /*#__PURE__*/function () {
  var _ref = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/crop'), {
            method: 'GET',
            params: (0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .getParamsReqList */ .vj)(params)
          });
        case 2:
          res = _context.sent;
          return _context.abrupt("return", res.result);
        case 4:
        case "end":
          return _context.stop();
      }
    }, _callee);
  }));
  return function getCropList(_x) {
    return _ref.apply(this, arguments);
  };
}();
var getTemplateCropList = /*#__PURE__*/function () {
  var _ref2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee2(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          _context2.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/crop/template'), {
            method: 'GET',
            params: (0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .getParamsReqList */ .vj)(params)
          });
        case 2:
          res = _context2.sent;
          return _context2.abrupt("return", res.result);
        case 4:
        case "end":
          return _context2.stop();
      }
    }, _callee2);
  }));
  return function getTemplateCropList(_x2) {
    return _ref2.apply(this, arguments);
  };
}();
var getCropManagementInfoList = /*#__PURE__*/function () {
  var _ref3 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee3(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee3$(_context3) {
      while (1) switch (_context3.prev = _context3.next) {
        case 0:
          _context3.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/crop-management-info'), {
            method: 'GET',
            params: params
          });
        case 2:
          res = _context3.sent;
          return _context3.abrupt("return", res.result);
        case 4:
        case "end":
          return _context3.stop();
      }
    }, _callee3);
  }));
  return function getCropManagementInfoList(_x3) {
    return _ref3.apply(this, arguments);
  };
}();
var getCurrentStateOfCrop = /*#__PURE__*/function () {
  var _ref4 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee4(cropId) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee4$(_context4) {
      while (1) switch (_context4.prev = _context4.next) {
        case 0:
          _context4.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/crop-current-state'), {
            method: 'GET',
            params: {
              crop_id: cropId
            }
          });
        case 2:
          res = _context4.sent;
          return _context4.abrupt("return", res.result);
        case 4:
        case "end":
          return _context4.stop();
      }
    }, _callee4);
  }));
  return function getCurrentStateOfCrop(_x4) {
    return _ref4.apply(this, arguments);
  };
}();
var createCrop = /*#__PURE__*/function () {
  var _ref5 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee5(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee5$(_context5) {
      while (1) switch (_context5.prev = _context5.next) {
        case 0:
          _context5.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/crop'), {
            method: 'POST',
            data: data
          });
        case 2:
          res = _context5.sent;
          return _context5.abrupt("return", res.result);
        case 4:
        case "end":
          return _context5.stop();
      }
    }, _callee5);
  }));
  return function createCrop(_x5) {
    return _ref5.apply(this, arguments);
  };
}();
var updateCrop = /*#__PURE__*/function () {
  var _ref6 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee6(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee6$(_context6) {
      while (1) switch (_context6.prev = _context6.next) {
        case 0:
          _context6.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/crop'), {
            method: 'PUT',
            data: data
          });
        case 2:
          res = _context6.sent;
          return _context6.abrupt("return", res.result);
        case 4:
        case "end":
          return _context6.stop();
      }
    }, _callee6);
  }));
  return function updateCrop(_x6) {
    return _ref6.apply(this, arguments);
  };
}();
var getCropNoteList = /*#__PURE__*/function () {
  var _ref7 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee7(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee7$(_context7) {
      while (1) switch (_context7.prev = _context7.next) {
        case 0:
          _context7.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/note'), {
            method: 'GET',
            params: (0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .getParamsReqList */ .vj)(params)
          });
        case 2:
          res = _context7.sent;
          return _context7.abrupt("return", res.result);
        case 4:
        case "end":
          return _context7.stop();
      }
    }, _callee7);
  }));
  return function getCropNoteList(_x7) {
    return _ref7.apply(this, arguments);
  };
}();
var createCropNote = /*#__PURE__*/function () {
  var _ref8 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee8(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee8$(_context8) {
      while (1) switch (_context8.prev = _context8.next) {
        case 0:
          _context8.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/note'), {
            method: 'POST',
            data: data
          });
        case 2:
          res = _context8.sent;
          return _context8.abrupt("return", res.result);
        case 4:
        case "end":
          return _context8.stop();
      }
    }, _callee8);
  }));
  return function createCropNote(_x8) {
    return _ref8.apply(this, arguments);
  };
}();
var updateCropNote = /*#__PURE__*/function () {
  var _ref9 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee9(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee9$(_context9) {
      while (1) switch (_context9.prev = _context9.next) {
        case 0:
          _context9.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/note'), {
            method: 'PUT',
            data: data
          });
        case 2:
          res = _context9.sent;
          return _context9.abrupt("return", res.result);
        case 4:
        case "end":
          return _context9.stop();
      }
    }, _callee9);
  }));
  return function updateCropNote(_x9) {
    return _ref9.apply(this, arguments);
  };
}();
var deleteCropNote = /*#__PURE__*/function () {
  var _ref11 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee10(_ref10) {
    var name, res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee10$(_context10) {
      while (1) switch (_context10.prev = _context10.next) {
        case 0:
          name = _ref10.name;
          _context10.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)("api/v2/cropManage/note?name=".concat(name)), {
            method: 'DELETE'
          });
        case 3:
          res = _context10.sent;
          return _context10.abrupt("return", res.result);
        case 5:
        case "end":
          return _context10.stop();
      }
    }, _callee10);
  }));
  return function deleteCropNote(_x10) {
    return _ref11.apply(this, arguments);
  };
}();
// Participants
var getParticipantsInCrop = /*#__PURE__*/function () {
  var _ref12 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee11(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee11$(_context11) {
      while (1) switch (_context11.prev = _context11.next) {
        case 0:
          _context11.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/iot_employee_in_crop'), {
            method: 'GET',
            params: (0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .getParamsReqList */ .vj)(params)
          });
        case 2:
          res = _context11.sent;
          return _context11.abrupt("return", res.result);
        case 4:
        case "end":
          return _context11.stop();
      }
    }, _callee11);
  }));
  return function getParticipantsInCrop(_x11) {
    return _ref12.apply(this, arguments);
  };
}();
var addParticipantInCrop = /*#__PURE__*/function () {
  var _ref13 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee12(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee12$(_context12) {
      while (1) switch (_context12.prev = _context12.next) {
        case 0:
          _context12.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/iot_employee_in_crop'), {
            method: 'POST',
            data: data
          });
        case 2:
          res = _context12.sent;
          return _context12.abrupt("return", res);
        case 4:
        case "end":
          return _context12.stop();
      }
    }, _callee12);
  }));
  return function addParticipantInCrop(_x12) {
    return _ref13.apply(this, arguments);
  };
}();
var updateParticipantsInCrop = /*#__PURE__*/(/* unused pure expression or super */ null && (function () {
  var _ref14 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee13(data) {
    var res;
    return _regeneratorRuntime().wrap(function _callee13$(_context13) {
      while (1) switch (_context13.prev = _context13.next) {
        case 0:
          _context13.next = 2;
          return request(generateAPIPath('api/v2/cropManage/iot_employee_in_crop'), {
            method: 'PUT',
            data: data
          });
        case 2:
          res = _context13.sent;
          return _context13.abrupt("return", res);
        case 4:
        case "end":
          return _context13.stop();
      }
    }, _callee13);
  }));
  return function updateParticipantsInCrop(_x13) {
    return _ref14.apply(this, arguments);
  };
}()));
var deleteParticipantsInCrop = /*#__PURE__*/function () {
  var _ref15 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee14(name) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee14$(_context14) {
      while (1) switch (_context14.prev = _context14.next) {
        case 0:
          _context14.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/iot_employee_in_crop'), {
            method: 'DELETE',
            params: {
              name: name
            }
          });
        case 2:
          res = _context14.sent;
          return _context14.abrupt("return", res);
        case 4:
        case "end":
          return _context14.stop();
      }
    }, _callee14);
  }));
  return function deleteParticipantsInCrop(_x14) {
    return _ref15.apply(this, arguments);
  };
}();
var getStatisticPestList = /*#__PURE__*/(/* unused pure expression or super */ null && (function () {
  var _ref16 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee15(params) {
    var res;
    return _regeneratorRuntime().wrap(function _callee15$(_context15) {
      while (1) switch (_context15.prev = _context15.next) {
        case 0:
          _context15.next = 2;
          return request(generateAPIPath("api/v2/cropManage/statisticPestList?crop_id=".concat(params === null || params === void 0 ? void 0 : params.cropId)), {
            method: 'GET',
            params: getParamsReqList(params)
          });
        case 2:
          res = _context15.sent;
          return _context15.abrupt("return", res.result);
        case 4:
        case "end":
          return _context15.stop();
      }
    }, _callee15);
  }));
  return function getStatisticPestList(_x15) {
    return _ref16.apply(this, arguments);
  };
}()));
var getStatisticNoteList = /*#__PURE__*/function () {
  var _ref17 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee16(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee16$(_context16) {
      while (1) switch (_context16.prev = _context16.next) {
        case 0:
          _context16.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)("api/v2/cropManage/statisticNoteList?crop_id=".concat(params === null || params === void 0 ? void 0 : params.cropId)), {
            method: 'GET',
            params: (0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .getParamsReqList */ .vj)(params)
          });
        case 2:
          res = _context16.sent;
          return _context16.abrupt("return", res.result);
        case 4:
        case "end":
          return _context16.stop();
      }
    }, _callee16);
  }));
  return function getStatisticNoteList(_x16) {
    return _ref17.apply(this, arguments);
  };
}();//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNzc4OTAuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFxQztBQUN1QjtBQWdDckQsSUFBTUcsV0FBVztFQUFBLElBQUFDLElBQUEsR0FBQUMsK0tBQUEsZUFBQUMsaUxBQUEsR0FBQUMsSUFBQSxDQUFHLFNBQUFDLFFBQU9DLE1BQTBCO0lBQUEsSUFBQUMsR0FBQTtJQUFBLE9BQUFKLGlMQUFBLEdBQUFLLElBQUEsVUFBQUMsU0FBQUMsUUFBQTtNQUFBLGtCQUFBQSxRQUFBLENBQUFDLElBQUEsR0FBQUQsUUFBQSxDQUFBRSxJQUFBO1FBQUE7VUFBQUYsUUFBQSxDQUFBRSxJQUFBO1VBQUEsT0FDeENmLG1EQUFPLENBSXZCQyxpRUFBZSxDQUFDLHdCQUF3QixDQUFDLEVBQUU7WUFDM0NlLE1BQU0sRUFBRSxLQUFLO1lBQ2JQLE1BQU0sRUFBRVAsa0VBQWdCLENBQUNPLE1BQU07VUFDakMsQ0FBQyxDQUFDO1FBQUE7VUFQSUMsR0FBRyxHQUFBRyxRQUFBLENBQUFJLElBQUE7VUFBQSxPQUFBSixRQUFBLENBQUFLLE1BQUEsV0FRRlIsR0FBRyxDQUFDUyxNQUFNO1FBQUE7UUFBQTtVQUFBLE9BQUFOLFFBQUEsQ0FBQU8sSUFBQTtNQUFBO0lBQUEsR0FBQVosT0FBQTtFQUFBLENBQ2xCO0VBQUEsZ0JBVllMLFdBQVdBLENBQUFrQixFQUFBO0lBQUEsT0FBQWpCLElBQUEsQ0FBQWtCLEtBQUEsT0FBQUMsU0FBQTtFQUFBO0FBQUEsR0FVdkI7QUFFTSxJQUFNQyxtQkFBbUI7RUFBQSxJQUFBQyxLQUFBLEdBQUFwQiwrS0FBQSxlQUFBQyxpTEFBQSxHQUFBQyxJQUFBLENBQUcsU0FBQW1CLFNBQU9qQixNQUEwQjtJQUFBLElBQUFDLEdBQUE7SUFBQSxPQUFBSixpTEFBQSxHQUFBSyxJQUFBLFVBQUFnQixVQUFBQyxTQUFBO01BQUEsa0JBQUFBLFNBQUEsQ0FBQWQsSUFBQSxHQUFBYyxTQUFBLENBQUFiLElBQUE7UUFBQTtVQUFBYSxTQUFBLENBQUFiLElBQUE7VUFBQSxPQUNoRGYsbURBQU8sQ0FJdkJDLGlFQUFlLENBQUMsaUNBQWlDLENBQUMsRUFBRTtZQUNwRGUsTUFBTSxFQUFFLEtBQUs7WUFDYlAsTUFBTSxFQUFFUCxrRUFBZ0IsQ0FBQ08sTUFBTTtVQUNqQyxDQUFDLENBQUM7UUFBQTtVQVBJQyxHQUFHLEdBQUFrQixTQUFBLENBQUFYLElBQUE7VUFBQSxPQUFBVyxTQUFBLENBQUFWLE1BQUEsV0FRRlIsR0FBRyxDQUFDUyxNQUFNO1FBQUE7UUFBQTtVQUFBLE9BQUFTLFNBQUEsQ0FBQVIsSUFBQTtNQUFBO0lBQUEsR0FBQU0sUUFBQTtFQUFBLENBQ2xCO0VBQUEsZ0JBVllGLG1CQUFtQkEsQ0FBQUssR0FBQTtJQUFBLE9BQUFKLEtBQUEsQ0FBQUgsS0FBQSxPQUFBQyxTQUFBO0VBQUE7QUFBQSxHQVUvQjtBQWtCTSxJQUFNTyx5QkFBeUI7RUFBQSxJQUFBQyxLQUFBLEdBQUExQiwrS0FBQSxlQUFBQyxpTEFBQSxHQUFBQyxJQUFBLENBQUcsU0FBQXlCLFNBQU92QixNQUEwQjtJQUFBLElBQUFDLEdBQUE7SUFBQSxPQUFBSixpTEFBQSxHQUFBSyxJQUFBLFVBQUFzQixVQUFBQyxTQUFBO01BQUEsa0JBQUFBLFNBQUEsQ0FBQXBCLElBQUEsR0FBQW9CLFNBQUEsQ0FBQW5CLElBQUE7UUFBQTtVQUFBbUIsU0FBQSxDQUFBbkIsSUFBQTtVQUFBLE9BQ3REZixtREFBTyxDQUN2QkMsaUVBQWUsQ0FBQyx3Q0FBd0MsQ0FBQyxFQUN6RDtZQUNFZSxNQUFNLEVBQUUsS0FBSztZQUNiUCxNQUFNLEVBQUVBO1VBQ1YsQ0FDRixDQUFDO1FBQUE7VUFOS0MsR0FBRyxHQUFBd0IsU0FBQSxDQUFBakIsSUFBQTtVQUFBLE9BQUFpQixTQUFBLENBQUFoQixNQUFBLFdBT0ZSLEdBQUcsQ0FBQ1MsTUFBTTtRQUFBO1FBQUE7VUFBQSxPQUFBZSxTQUFBLENBQUFkLElBQUE7TUFBQTtJQUFBLEdBQUFZLFFBQUE7RUFBQSxDQUNsQjtFQUFBLGdCQVRZRix5QkFBeUJBLENBQUFLLEdBQUE7SUFBQSxPQUFBSixLQUFBLENBQUFULEtBQUEsT0FBQUMsU0FBQTtFQUFBO0FBQUEsR0FTckM7QUFDTSxJQUFNYSxxQkFBcUI7RUFBQSxJQUFBQyxLQUFBLEdBQUFoQywrS0FBQSxlQUFBQyxpTEFBQSxHQUFBQyxJQUFBLENBQUcsU0FBQStCLFNBQU9DLE1BQWM7SUFBQSxJQUFBN0IsR0FBQTtJQUFBLE9BQUFKLGlMQUFBLEdBQUFLLElBQUEsVUFBQTZCLFVBQUFDLFNBQUE7TUFBQSxrQkFBQUEsU0FBQSxDQUFBM0IsSUFBQSxHQUFBMkIsU0FBQSxDQUFBMUIsSUFBQTtRQUFBO1VBQUEwQixTQUFBLENBQUExQixJQUFBO1VBQUEsT0FDdENmLG1EQUFPLENBU3ZCQyxpRUFBZSxDQUFDLHNDQUFzQyxDQUFDLEVBQUU7WUFDekRlLE1BQU0sRUFBRSxLQUFLO1lBQ2JQLE1BQU0sRUFBRTtjQUNOaUMsT0FBTyxFQUFFSDtZQUNYO1VBQ0YsQ0FBQyxDQUFDO1FBQUE7VUFkSTdCLEdBQUcsR0FBQStCLFNBQUEsQ0FBQXhCLElBQUE7VUFBQSxPQUFBd0IsU0FBQSxDQUFBdkIsTUFBQSxXQWVGUixHQUFHLENBQUNTLE1BQU07UUFBQTtRQUFBO1VBQUEsT0FBQXNCLFNBQUEsQ0FBQXJCLElBQUE7TUFBQTtJQUFBLEdBQUFrQixRQUFBO0VBQUEsQ0FDbEI7RUFBQSxnQkFqQllGLHFCQUFxQkEsQ0FBQU8sR0FBQTtJQUFBLE9BQUFOLEtBQUEsQ0FBQWYsS0FBQSxPQUFBQyxTQUFBO0VBQUE7QUFBQSxHQWlCakM7QUFxQ00sSUFBTXFCLFVBQVU7RUFBQSxJQUFBQyxLQUFBLEdBQUF4QywrS0FBQSxlQUFBQyxpTEFBQSxHQUFBQyxJQUFBLENBQUcsU0FBQXVDLFNBQU9DLElBQW9CO0lBQUEsSUFBQXJDLEdBQUE7SUFBQSxPQUFBSixpTEFBQSxHQUFBSyxJQUFBLFVBQUFxQyxVQUFBQyxTQUFBO01BQUEsa0JBQUFBLFNBQUEsQ0FBQW5DLElBQUEsR0FBQW1DLFNBQUEsQ0FBQWxDLElBQUE7UUFBQTtVQUFBa0MsU0FBQSxDQUFBbEMsSUFBQTtVQUFBLE9BQ2pDZixtREFBTyxDQUl2QkMsaUVBQWUsQ0FBQyx3QkFBd0IsQ0FBQyxFQUFFO1lBQzNDZSxNQUFNLEVBQUUsTUFBTTtZQUNkK0IsSUFBSSxFQUFKQTtVQUNGLENBQUMsQ0FBQztRQUFBO1VBUElyQyxHQUFHLEdBQUF1QyxTQUFBLENBQUFoQyxJQUFBO1VBQUEsT0FBQWdDLFNBQUEsQ0FBQS9CLE1BQUEsV0FRRlIsR0FBRyxDQUFDUyxNQUFNO1FBQUE7UUFBQTtVQUFBLE9BQUE4QixTQUFBLENBQUE3QixJQUFBO01BQUE7SUFBQSxHQUFBMEIsUUFBQTtFQUFBLENBQ2xCO0VBQUEsZ0JBVllGLFVBQVVBLENBQUFNLEdBQUE7SUFBQSxPQUFBTCxLQUFBLENBQUF2QixLQUFBLE9BQUFDLFNBQUE7RUFBQTtBQUFBLEdBVXRCO0FBZ0JNLElBQU00QixVQUFVO0VBQUEsSUFBQUMsS0FBQSxHQUFBL0MsK0tBQUEsZUFBQUMsaUxBQUEsR0FBQUMsSUFBQSxDQUFHLFNBQUE4QyxTQUFPTixJQUFvQjtJQUFBLElBQUFyQyxHQUFBO0lBQUEsT0FBQUosaUxBQUEsR0FBQUssSUFBQSxVQUFBMkMsVUFBQUMsU0FBQTtNQUFBLGtCQUFBQSxTQUFBLENBQUF6QyxJQUFBLEdBQUF5QyxTQUFBLENBQUF4QyxJQUFBO1FBQUE7VUFBQXdDLFNBQUEsQ0FBQXhDLElBQUE7VUFBQSxPQUNqQ2YsbURBQU8sQ0FJdkJDLGlFQUFlLENBQUMsd0JBQXdCLENBQUMsRUFBRTtZQUMzQ2UsTUFBTSxFQUFFLEtBQUs7WUFDYitCLElBQUksRUFBSkE7VUFDRixDQUFDLENBQUM7UUFBQTtVQVBJckMsR0FBRyxHQUFBNkMsU0FBQSxDQUFBdEMsSUFBQTtVQUFBLE9BQUFzQyxTQUFBLENBQUFyQyxNQUFBLFdBUUZSLEdBQUcsQ0FBQ1MsTUFBTTtRQUFBO1FBQUE7VUFBQSxPQUFBb0MsU0FBQSxDQUFBbkMsSUFBQTtNQUFBO0lBQUEsR0FBQWlDLFFBQUE7RUFBQSxDQUNsQjtFQUFBLGdCQVZZRixVQUFVQSxDQUFBSyxHQUFBO0lBQUEsT0FBQUosS0FBQSxDQUFBOUIsS0FBQSxPQUFBQyxTQUFBO0VBQUE7QUFBQSxHQVV0QjtBQXNCTSxJQUFNa0MsZUFBZTtFQUFBLElBQUFDLEtBQUEsR0FBQXJELCtLQUFBLGVBQUFDLGlMQUFBLEdBQUFDLElBQUEsQ0FBRyxTQUFBb0QsU0FBT2xELE1BQTBCO0lBQUEsSUFBQUMsR0FBQTtJQUFBLE9BQUFKLGlMQUFBLEdBQUFLLElBQUEsVUFBQWlELFVBQUFDLFNBQUE7TUFBQSxrQkFBQUEsU0FBQSxDQUFBL0MsSUFBQSxHQUFBK0MsU0FBQSxDQUFBOUMsSUFBQTtRQUFBO1VBQUE4QyxTQUFBLENBQUE5QyxJQUFBO1VBQUEsT0FDNUNmLG1EQUFPLENBSXZCQyxpRUFBZSxDQUFDLHdCQUF3QixDQUFDLEVBQUU7WUFDM0NlLE1BQU0sRUFBRSxLQUFLO1lBQ2JQLE1BQU0sRUFBRVAsa0VBQWdCLENBQUNPLE1BQU07VUFDakMsQ0FBQyxDQUFDO1FBQUE7VUFQSUMsR0FBRyxHQUFBbUQsU0FBQSxDQUFBNUMsSUFBQTtVQUFBLE9BQUE0QyxTQUFBLENBQUEzQyxNQUFBLFdBUUZSLEdBQUcsQ0FBQ1MsTUFBTTtRQUFBO1FBQUE7VUFBQSxPQUFBMEMsU0FBQSxDQUFBekMsSUFBQTtNQUFBO0lBQUEsR0FBQXVDLFFBQUE7RUFBQSxDQUNsQjtFQUFBLGdCQVZZRixlQUFlQSxDQUFBSyxHQUFBO0lBQUEsT0FBQUosS0FBQSxDQUFBcEMsS0FBQSxPQUFBQyxTQUFBO0VBQUE7QUFBQSxHQVUzQjtBQXdCTSxJQUFNd0MsY0FBYztFQUFBLElBQUFDLEtBQUEsR0FBQTNELCtLQUFBLGVBQUFDLGlMQUFBLEdBQUFDLElBQUEsQ0FBRyxTQUFBMEQsU0FBT2xCLElBQXdCO0lBQUEsSUFBQXJDLEdBQUE7SUFBQSxPQUFBSixpTEFBQSxHQUFBSyxJQUFBLFVBQUF1RCxVQUFBQyxTQUFBO01BQUEsa0JBQUFBLFNBQUEsQ0FBQXJELElBQUEsR0FBQXFELFNBQUEsQ0FBQXBELElBQUE7UUFBQTtVQUFBb0QsU0FBQSxDQUFBcEQsSUFBQTtVQUFBLE9BQ3pDZixtREFBTyxDQUl2QkMsaUVBQWUsQ0FBQyx3QkFBd0IsQ0FBQyxFQUFFO1lBQzNDZSxNQUFNLEVBQUUsTUFBTTtZQUNkK0IsSUFBSSxFQUFKQTtVQUNGLENBQUMsQ0FBQztRQUFBO1VBUElyQyxHQUFHLEdBQUF5RCxTQUFBLENBQUFsRCxJQUFBO1VBQUEsT0FBQWtELFNBQUEsQ0FBQWpELE1BQUEsV0FRRlIsR0FBRyxDQUFDUyxNQUFNO1FBQUE7UUFBQTtVQUFBLE9BQUFnRCxTQUFBLENBQUEvQyxJQUFBO01BQUE7SUFBQSxHQUFBNkMsUUFBQTtFQUFBLENBQ2xCO0VBQUEsZ0JBVllGLGNBQWNBLENBQUFLLEdBQUE7SUFBQSxPQUFBSixLQUFBLENBQUExQyxLQUFBLE9BQUFDLFNBQUE7RUFBQTtBQUFBLEdBVTFCO0FBVU0sSUFBTThDLGNBQWM7RUFBQSxJQUFBQyxLQUFBLEdBQUFqRSwrS0FBQSxlQUFBQyxpTEFBQSxHQUFBQyxJQUFBLENBQUcsU0FBQWdFLFNBQU94QixJQUF3QjtJQUFBLElBQUFyQyxHQUFBO0lBQUEsT0FBQUosaUxBQUEsR0FBQUssSUFBQSxVQUFBNkQsVUFBQUMsU0FBQTtNQUFBLGtCQUFBQSxTQUFBLENBQUEzRCxJQUFBLEdBQUEyRCxTQUFBLENBQUExRCxJQUFBO1FBQUE7VUFBQTBELFNBQUEsQ0FBQTFELElBQUE7VUFBQSxPQUN6Q2YsbURBQU8sQ0FJdkJDLGlFQUFlLENBQUMsd0JBQXdCLENBQUMsRUFBRTtZQUMzQ2UsTUFBTSxFQUFFLEtBQUs7WUFDYitCLElBQUksRUFBSkE7VUFDRixDQUFDLENBQUM7UUFBQTtVQVBJckMsR0FBRyxHQUFBK0QsU0FBQSxDQUFBeEQsSUFBQTtVQUFBLE9BQUF3RCxTQUFBLENBQUF2RCxNQUFBLFdBUUZSLEdBQUcsQ0FBQ1MsTUFBTTtRQUFBO1FBQUE7VUFBQSxPQUFBc0QsU0FBQSxDQUFBckQsSUFBQTtNQUFBO0lBQUEsR0FBQW1ELFFBQUE7RUFBQSxDQUNsQjtFQUFBLGdCQVZZRixjQUFjQSxDQUFBSyxHQUFBO0lBQUEsT0FBQUosS0FBQSxDQUFBaEQsS0FBQSxPQUFBQyxTQUFBO0VBQUE7QUFBQSxHQVUxQjtBQUVNLElBQU1vRCxjQUFjO0VBQUEsSUFBQUMsTUFBQSxHQUFBdkUsK0tBQUEsZUFBQUMsaUxBQUEsR0FBQUMsSUFBQSxDQUFHLFNBQUFzRSxVQUFBQyxNQUFBO0lBQUEsSUFBQUMsSUFBQSxFQUFBckUsR0FBQTtJQUFBLE9BQUFKLGlMQUFBLEdBQUFLLElBQUEsVUFBQXFFLFdBQUFDLFVBQUE7TUFBQSxrQkFBQUEsVUFBQSxDQUFBbkUsSUFBQSxHQUFBbUUsVUFBQSxDQUFBbEUsSUFBQTtRQUFBO1VBQVNnRSxJQUFJLEdBQUFELE1BQUEsQ0FBSkMsSUFBSTtVQUFBRSxVQUFBLENBQUFsRSxJQUFBO1VBQUEsT0FDdkJmLG1EQUFPLENBS3ZCQyxpRUFBZSxnQ0FBQWlGLE1BQUEsQ0FBZ0NILElBQUksQ0FBRSxDQUFDLEVBQUU7WUFDeEQvRCxNQUFNLEVBQUU7VUFDVixDQUFDLENBQUM7UUFBQTtVQVBJTixHQUFHLEdBQUF1RSxVQUFBLENBQUFoRSxJQUFBO1VBQUEsT0FBQWdFLFVBQUEsQ0FBQS9ELE1BQUEsV0FRRlIsR0FBRyxDQUFDUyxNQUFNO1FBQUE7UUFBQTtVQUFBLE9BQUE4RCxVQUFBLENBQUE3RCxJQUFBO01BQUE7SUFBQSxHQUFBeUQsU0FBQTtFQUFBLENBQ2xCO0VBQUEsZ0JBVllGLGNBQWNBLENBQUFRLElBQUE7SUFBQSxPQUFBUCxNQUFBLENBQUF0RCxLQUFBLE9BQUFDLFNBQUE7RUFBQTtBQUFBLEdBVTFCO0FBQ0Q7QUFDTyxJQUFNNkQscUJBQXFCO0VBQUEsSUFBQUMsTUFBQSxHQUFBaEYsK0tBQUEsZUFBQUMsaUxBQUEsR0FBQUMsSUFBQSxDQUFHLFNBQUErRSxVQUFPN0UsTUFBMEI7SUFBQSxJQUFBQyxHQUFBO0lBQUEsT0FBQUosaUxBQUEsR0FBQUssSUFBQSxVQUFBNEUsV0FBQUMsVUFBQTtNQUFBLGtCQUFBQSxVQUFBLENBQUExRSxJQUFBLEdBQUEwRSxVQUFBLENBQUF6RSxJQUFBO1FBQUE7VUFBQXlFLFVBQUEsQ0FBQXpFLElBQUE7VUFBQSxPQUNsRGYsbURBQU8sQ0FJdkJDLGlFQUFlLENBQUMsd0NBQXdDLENBQUMsRUFBRTtZQUMzRGUsTUFBTSxFQUFFLEtBQUs7WUFDYlAsTUFBTSxFQUFFUCxrRUFBZ0IsQ0FBQ08sTUFBTTtVQUNqQyxDQUFDLENBQUM7UUFBQTtVQVBJQyxHQUFHLEdBQUE4RSxVQUFBLENBQUF2RSxJQUFBO1VBQUEsT0FBQXVFLFVBQUEsQ0FBQXRFLE1BQUEsV0FRRlIsR0FBRyxDQUFDUyxNQUFNO1FBQUE7UUFBQTtVQUFBLE9BQUFxRSxVQUFBLENBQUFwRSxJQUFBO01BQUE7SUFBQSxHQUFBa0UsU0FBQTtFQUFBLENBQ2xCO0VBQUEsZ0JBVllGLHFCQUFxQkEsQ0FBQUssSUFBQTtJQUFBLE9BQUFKLE1BQUEsQ0FBQS9ELEtBQUEsT0FBQUMsU0FBQTtFQUFBO0FBQUEsR0FVakM7QUFFTSxJQUFNbUUsb0JBQW9CO0VBQUEsSUFBQUMsTUFBQSxHQUFBdEYsK0tBQUEsZUFBQUMsaUxBQUEsR0FBQUMsSUFBQSxDQUFHLFNBQUFxRixVQUFPN0MsSUFHMUM7SUFBQSxJQUFBckMsR0FBQTtJQUFBLE9BQUFKLGlMQUFBLEdBQUFLLElBQUEsVUFBQWtGLFdBQUFDLFVBQUE7TUFBQSxrQkFBQUEsVUFBQSxDQUFBaEYsSUFBQSxHQUFBZ0YsVUFBQSxDQUFBL0UsSUFBQTtRQUFBO1VBQUErRSxVQUFBLENBQUEvRSxJQUFBO1VBQUEsT0FDbUJmLG1EQUFPLENBSXZCQyxpRUFBZSxDQUFDLHdDQUF3QyxDQUFDLEVBQUU7WUFDM0RlLE1BQU0sRUFBRSxNQUFNO1lBQ2QrQixJQUFJLEVBQUpBO1VBQ0YsQ0FBQyxDQUFDO1FBQUE7VUFQSXJDLEdBQUcsR0FBQW9GLFVBQUEsQ0FBQTdFLElBQUE7VUFBQSxPQUFBNkUsVUFBQSxDQUFBNUUsTUFBQSxXQVFGUixHQUFHO1FBQUE7UUFBQTtVQUFBLE9BQUFvRixVQUFBLENBQUExRSxJQUFBO01BQUE7SUFBQSxHQUFBd0UsU0FBQTtFQUFBLENBQ1g7RUFBQSxnQkFiWUYsb0JBQW9CQSxDQUFBSyxJQUFBO0lBQUEsT0FBQUosTUFBQSxDQUFBckUsS0FBQSxPQUFBQyxTQUFBO0VBQUE7QUFBQSxHQWFoQztBQUVNLElBQU15RSx3QkFBd0I7RUFBQSxJQUFBQyxNQUFBLEdBQUE1RixpQkFBQSxlQUFBQyxtQkFBQSxHQUFBQyxJQUFBLENBQUcsU0FBQTJGLFVBQU9uRCxJQUk5QztJQUFBLElBQUFyQyxHQUFBO0lBQUEsT0FBQUosbUJBQUEsR0FBQUssSUFBQSxVQUFBd0YsV0FBQUMsVUFBQTtNQUFBLGtCQUFBQSxVQUFBLENBQUF0RixJQUFBLEdBQUFzRixVQUFBLENBQUFyRixJQUFBO1FBQUE7VUFBQXFGLFVBQUEsQ0FBQXJGLElBQUE7VUFBQSxPQUNtQmYsT0FBTyxDQUl2QkMsZUFBZSxDQUFDLHdDQUF3QyxDQUFDLEVBQUU7WUFDM0RlLE1BQU0sRUFBRSxLQUFLO1lBQ2IrQixJQUFJLEVBQUpBO1VBQ0YsQ0FBQyxDQUFDO1FBQUE7VUFQSXJDLEdBQUcsR0FBQTBGLFVBQUEsQ0FBQW5GLElBQUE7VUFBQSxPQUFBbUYsVUFBQSxDQUFBbEYsTUFBQSxXQVFGUixHQUFHO1FBQUE7UUFBQTtVQUFBLE9BQUEwRixVQUFBLENBQUFoRixJQUFBO01BQUE7SUFBQSxHQUFBOEUsU0FBQTtFQUFBLENBQ1g7RUFBQSxnQkFkWUYsd0JBQXdCQSxDQUFBSyxJQUFBO0lBQUEsT0FBQUosTUFBQSxDQUFBM0UsS0FBQSxPQUFBQyxTQUFBO0VBQUE7QUFBQSxHQWNwQztBQUVNLElBQU0rRSx3QkFBd0I7RUFBQSxJQUFBQyxNQUFBLEdBQUFsRywrS0FBQSxlQUFBQyxpTEFBQSxHQUFBQyxJQUFBLENBQUcsU0FBQWlHLFVBQU96QixJQUFZO0lBQUEsSUFBQXJFLEdBQUE7SUFBQSxPQUFBSixpTEFBQSxHQUFBSyxJQUFBLFVBQUE4RixXQUFBQyxVQUFBO01BQUEsa0JBQUFBLFVBQUEsQ0FBQTVGLElBQUEsR0FBQTRGLFVBQUEsQ0FBQTNGLElBQUE7UUFBQTtVQUFBMkYsVUFBQSxDQUFBM0YsSUFBQTtVQUFBLE9BQ3ZDZixtREFBTyxDQUFDQyxpRUFBZSxDQUFDLHdDQUF3QyxDQUFDLEVBQUU7WUFDbkZlLE1BQU0sRUFBRSxRQUFRO1lBQ2hCUCxNQUFNLEVBQUU7Y0FDTnNFLElBQUksRUFBSkE7WUFDRjtVQUNGLENBQUMsQ0FBQztRQUFBO1VBTElyRSxHQUFHLEdBQUFnRyxVQUFBLENBQUF6RixJQUFBO1VBQUEsT0FBQXlGLFVBQUEsQ0FBQXhGLE1BQUEsV0FNRlIsR0FBRztRQUFBO1FBQUE7VUFBQSxPQUFBZ0csVUFBQSxDQUFBdEYsSUFBQTtNQUFBO0lBQUEsR0FBQW9GLFNBQUE7RUFBQSxDQUNYO0VBQUEsZ0JBUllGLHdCQUF3QkEsQ0FBQUssSUFBQTtJQUFBLE9BQUFKLE1BQUEsQ0FBQWpGLEtBQUEsT0FBQUMsU0FBQTtFQUFBO0FBQUEsR0FRcEM7QUFjTSxJQUFNcUYsb0JBQW9CO0VBQUEsSUFBQUMsTUFBQSxHQUFBeEcsaUJBQUEsZUFBQUMsbUJBQUEsR0FBQUMsSUFBQSxDQUFHLFNBQUF1RyxVQUFPckcsTUFBZ0Q7SUFBQSxJQUFBQyxHQUFBO0lBQUEsT0FBQUosbUJBQUEsR0FBQUssSUFBQSxVQUFBb0csV0FBQUMsVUFBQTtNQUFBLGtCQUFBQSxVQUFBLENBQUFsRyxJQUFBLEdBQUFrRyxVQUFBLENBQUFqRyxJQUFBO1FBQUE7VUFBQWlHLFVBQUEsQ0FBQWpHLElBQUE7VUFBQSxPQUN2RWYsT0FBTyxDQUN2QkMsZUFBZSxnREFBQWlGLE1BQUEsQ0FBZ0R6RSxNQUFNLGFBQU5BLE1BQU0sdUJBQU5BLE1BQU0sQ0FBRThCLE1BQU0sQ0FBRSxDQUFDLEVBQ2hGO1lBQ0V2QixNQUFNLEVBQUUsS0FBSztZQUNiUCxNQUFNLEVBQUVQLGdCQUFnQixDQUFDTyxNQUFNO1VBQ2pDLENBQ0YsQ0FBQztRQUFBO1VBTktDLEdBQUcsR0FBQXNHLFVBQUEsQ0FBQS9GLElBQUE7VUFBQSxPQUFBK0YsVUFBQSxDQUFBOUYsTUFBQSxXQU9GUixHQUFHLENBQUNTLE1BQU07UUFBQTtRQUFBO1VBQUEsT0FBQTZGLFVBQUEsQ0FBQTVGLElBQUE7TUFBQTtJQUFBLEdBQUEwRixTQUFBO0VBQUEsQ0FDbEI7RUFBQSxnQkFUWUYsb0JBQW9CQSxDQUFBSyxJQUFBO0lBQUEsT0FBQUosTUFBQSxDQUFBdkYsS0FBQSxPQUFBQyxTQUFBO0VBQUE7QUFBQSxHQVNoQztBQVVNLElBQU0yRixvQkFBb0I7RUFBQSxJQUFBQyxNQUFBLEdBQUE5RywrS0FBQSxlQUFBQyxpTEFBQSxHQUFBQyxJQUFBLENBQUcsU0FBQTZHLFVBQU8zRyxNQUFnRDtJQUFBLElBQUFDLEdBQUE7SUFBQSxPQUFBSixpTEFBQSxHQUFBSyxJQUFBLFVBQUEwRyxXQUFBQyxVQUFBO01BQUEsa0JBQUFBLFVBQUEsQ0FBQXhHLElBQUEsR0FBQXdHLFVBQUEsQ0FBQXZHLElBQUE7UUFBQTtVQUFBdUcsVUFBQSxDQUFBdkcsSUFBQTtVQUFBLE9BQ3ZFZixtREFBTyxDQUN2QkMsaUVBQWUsZ0RBQUFpRixNQUFBLENBQWdEekUsTUFBTSxhQUFOQSxNQUFNLHVCQUFOQSxNQUFNLENBQUU4QixNQUFNLENBQUUsQ0FBQyxFQUNoRjtZQUNFdkIsTUFBTSxFQUFFLEtBQUs7WUFDYlAsTUFBTSxFQUFFUCxrRUFBZ0IsQ0FBQ08sTUFBTTtVQUNqQyxDQUNGLENBQUM7UUFBQTtVQU5LQyxHQUFHLEdBQUE0RyxVQUFBLENBQUFyRyxJQUFBO1VBQUEsT0FBQXFHLFVBQUEsQ0FBQXBHLE1BQUEsV0FPRlIsR0FBRyxDQUFDUyxNQUFNO1FBQUE7UUFBQTtVQUFBLE9BQUFtRyxVQUFBLENBQUFsRyxJQUFBO01BQUE7SUFBQSxHQUFBZ0csU0FBQTtFQUFBLENBQ2xCO0VBQUEsZ0JBVFlGLG9CQUFvQkEsQ0FBQUssSUFBQTtJQUFBLE9BQUFKLE1BQUEsQ0FBQTdGLEtBQUEsT0FBQUMsU0FBQTtFQUFBO0FBQUEsR0FTaEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hbnQtZGVzaWduLXByby8uL3NyYy9zZXJ2aWNlcy9jcm9wTWFuYWdlci50cz84YzJmIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHJlcXVlc3QgfSBmcm9tICdAdW1panMvbWF4JztcclxuaW1wb3J0IHsgZ2VuZXJhdGVBUElQYXRoLCBnZXRQYXJhbXNSZXFMaXN0IH0gZnJvbSAnLi91dGlscyc7XHJcblxyXG5leHBvcnQgdHlwZSBJQ3JvcFJlcyA9IHtcclxuICBuYW1lOiBzdHJpbmc7XHJcbiAgY3JlYXRpb246IHN0cmluZztcclxuICBtb2RpZmllZDogc3RyaW5nO1xyXG4gIG1vZGlmaWVkX2J5OiBzdHJpbmc7XHJcbiAgb3duZXI6IHN0cmluZztcclxuICBkb2NzdGF0dXM6IG51bWJlcjtcclxuICBpZHg6IG51bWJlcjtcclxuICBmYXJtaW5nX3BsYW46IGFueTtcclxuICBsYWJlbDogc3RyaW5nO1xyXG4gIGRlc2NyaXB0aW9uOiBzdHJpbmc7XHJcbiAgem9uZTogYW55O1xyXG4gIF91c2VyX3RhZ3M6IGFueTtcclxuICBfY29tbWVudHM6IGFueTtcclxuICBfYXNzaWduOiBhbnk7XHJcbiAgX2xpa2VkX2J5OiBhbnk7XHJcbiAgY3JvcHM6IGFueTtcclxuICBwbGFudF9pZDogc3RyaW5nO1xyXG4gIHBsYW50OiBhbnk7XHJcbiAgem9uZV9pZDogc3RyaW5nO1xyXG4gIHNxdWFyZTogc3RyaW5nO1xyXG4gIHN0YXJ0X2RhdGU6IHN0cmluZztcclxuICBlbmRfZGF0ZTogc3RyaW5nO1xyXG4gIGltYWdlOiBzdHJpbmc7XHJcbiAgYXZhdGFyOiBzdHJpbmc7XHJcbiAgZXN0aW1hdGVfcXVhbnRpdHk6IGFueTtcclxuICBxdWFudGl0eV9lc3RpbWF0ZTogbnVtYmVyO1xyXG4gIHN0YXR1czogc3RyaW5nO1xyXG4gIHBsYW50X2xhYmVsOiBzdHJpbmc7XHJcbn07XHJcbmV4cG9ydCBjb25zdCBnZXRDcm9wTGlzdCA9IGFzeW5jIChwYXJhbXM/OiBBUEkuTGlzdFBhcmFtc1JlcSkgPT4ge1xyXG4gIGNvbnN0IHJlcyA9IGF3YWl0IHJlcXVlc3Q8XHJcbiAgICBBUEkuUmVzcG9uc2VSZXN1bHQ8e1xyXG4gICAgICBkYXRhOiBJQ3JvcFJlc1tdO1xyXG4gICAgfT5cclxuICA+KGdlbmVyYXRlQVBJUGF0aCgnYXBpL3YyL2Nyb3BNYW5hZ2UvY3JvcCcpLCB7XHJcbiAgICBtZXRob2Q6ICdHRVQnLFxyXG4gICAgcGFyYW1zOiBnZXRQYXJhbXNSZXFMaXN0KHBhcmFtcyksXHJcbiAgfSk7XHJcbiAgcmV0dXJuIHJlcy5yZXN1bHQ7XHJcbn07XHJcblxyXG5leHBvcnQgY29uc3QgZ2V0VGVtcGxhdGVDcm9wTGlzdCA9IGFzeW5jIChwYXJhbXM/OiBBUEkuTGlzdFBhcmFtc1JlcSkgPT4ge1xyXG4gIGNvbnN0IHJlcyA9IGF3YWl0IHJlcXVlc3Q8XHJcbiAgICBBUEkuUmVzcG9uc2VSZXN1bHQ8e1xyXG4gICAgICBkYXRhOiBJQ3JvcFJlc1tdO1xyXG4gICAgfT5cclxuICA+KGdlbmVyYXRlQVBJUGF0aCgnYXBpL3YyL2Nyb3BNYW5hZ2UvY3JvcC90ZW1wbGF0ZScpLCB7XHJcbiAgICBtZXRob2Q6ICdHRVQnLFxyXG4gICAgcGFyYW1zOiBnZXRQYXJhbXNSZXFMaXN0KHBhcmFtcyksXHJcbiAgfSk7XHJcbiAgcmV0dXJuIHJlcy5yZXN1bHQ7XHJcbn07XHJcblxyXG5leHBvcnQgdHlwZSBJQ3JvcE1hbmFnZXJJbmZvID0ge1xyXG4gIGF2YXRhcjogc3RyaW5nIHwgbnVsbDtcclxuICBuYW1lOiBzdHJpbmc7XHJcbiAgbGFiZWw6IHN0cmluZztcclxuICBwbGFudF9pZDogc3RyaW5nO1xyXG4gIHBsYW50X25hbWU6IHN0cmluZztcclxuICB6b25lX2lkOiBzdHJpbmc7XHJcbiAgem9uZV9uYW1lOiBzdHJpbmc7XHJcbiAgcHJvamVjdF9pZDogc3RyaW5nO1xyXG4gIHByb2plY3RfbmFtZTogc3RyaW5nO1xyXG4gIHN0YXJ0X2RhdGU6IHN0cmluZyB8IG51bGw7XHJcbiAgZW5kX2RhdGU6IHN0cmluZyB8IG51bGw7XHJcbiAgc3RhdHVzOiBzdHJpbmcgfCBudWxsO1xyXG4gIGRhdGVGaWx0ZXI6ICd0aGlzX3dlZWsnIHwgJ3RoaXNfbW9udGgnIHwgJ3RoaXNfeWVhcicgfCAnbmV4dF8zX21vbnRocycgfCBudWxsO1xyXG4gIGlzX3RlbXBsYXRlOiBib29sZWFuO1xyXG59O1xyXG5leHBvcnQgY29uc3QgZ2V0Q3JvcE1hbmFnZW1lbnRJbmZvTGlzdCA9IGFzeW5jIChwYXJhbXM/OiBBUEkuTGlzdFBhcmFtc1JlcSkgPT4ge1xyXG4gIGNvbnN0IHJlcyA9IGF3YWl0IHJlcXVlc3Q8QVBJLlBhZ2luYXRpb25SZXNwb25zZVJlc3VsdDxJQ3JvcE1hbmFnZXJJbmZvW10+PihcclxuICAgIGdlbmVyYXRlQVBJUGF0aCgnYXBpL3YyL2Nyb3BNYW5hZ2UvY3JvcC1tYW5hZ2VtZW50LWluZm8nKSxcclxuICAgIHtcclxuICAgICAgbWV0aG9kOiAnR0VUJyxcclxuICAgICAgcGFyYW1zOiBwYXJhbXMsXHJcbiAgICB9LFxyXG4gICk7XHJcbiAgcmV0dXJuIHJlcy5yZXN1bHQ7XHJcbn07XHJcbmV4cG9ydCBjb25zdCBnZXRDdXJyZW50U3RhdGVPZkNyb3AgPSBhc3luYyAoY3JvcElkOiBzdHJpbmcpID0+IHtcclxuICBjb25zdCByZXMgPSBhd2FpdCByZXF1ZXN0PFxyXG4gICAgQVBJLlJlc3BvbnNlUmVzdWx0PFxyXG4gICAgICB7XHJcbiAgICAgICAgbmFtZTogc3RyaW5nO1xyXG4gICAgICAgIGxhYmVsOiBzdHJpbmc7XHJcbiAgICAgICAgc3RhcnRfZGF0ZTogc3RyaW5nO1xyXG4gICAgICAgIGVuZF9kYXRlOiBzdHJpbmc7XHJcbiAgICAgIH1bXVxyXG4gICAgPlxyXG4gID4oZ2VuZXJhdGVBUElQYXRoKCdhcGkvdjIvY3JvcE1hbmFnZS9jcm9wLWN1cnJlbnQtc3RhdGUnKSwge1xyXG4gICAgbWV0aG9kOiAnR0VUJyxcclxuICAgIHBhcmFtczoge1xyXG4gICAgICBjcm9wX2lkOiBjcm9wSWQsXHJcbiAgICB9LFxyXG4gIH0pO1xyXG4gIHJldHVybiByZXMucmVzdWx0O1xyXG59O1xyXG5leHBvcnQgdHlwZSBJQ3JlYXRlQ3JvcFJlcSA9IHtcclxuICBsYWJlbDogc3RyaW5nO1xyXG4gIHpvbmVfaWQ6IHN0cmluZztcclxuICBzdGFydF9kYXRlOiBzdHJpbmc7XHJcbiAgZW5kX2RhdGU6IHN0cmluZztcclxuICBzcXVhcmU6IG51bWJlcjtcclxuICBwbGFudF9pZDogc3RyaW5nO1xyXG4gIGltYWdlPzogc3RyaW5nO1xyXG4gIGRlc2NyaXB0aW9uPzogc3RyaW5nO1xyXG4gIHN0YXR1cz86IHN0cmluZztcclxuICBpc190ZW1wbGF0ZT86IGJvb2xlYW47XHJcbiAgcXVhbnRpdHlfZXN0aW1hdGU/OiBudW1iZXI7XHJcbn07XHJcbmV4cG9ydCBpbnRlcmZhY2UgSUNyZWF0ZUNyb3BSZXMge1xyXG4gIG5hbWU6IHN0cmluZztcclxuICBvd25lcjogc3RyaW5nO1xyXG4gIGNyZWF0aW9uOiBzdHJpbmc7XHJcbiAgbW9kaWZpZWQ6IHN0cmluZztcclxuICBtb2RpZmllZF9ieTogc3RyaW5nO1xyXG4gIGRvY3N0YXR1czogbnVtYmVyO1xyXG4gIGlkeDogbnVtYmVyO1xyXG4gIHBsYW50X2lkOiBhbnk7XHJcbiAgcGxhbnRfbGFiZWw6IGFueTtcclxuICB6b25lX2lkOiBzdHJpbmc7XHJcbiAgem9uZTogYW55O1xyXG4gIGltYWdlOiBhbnk7XHJcbiAgbGFiZWw6IHN0cmluZztcclxuICBkZXNjcmlwdGlvbjogYW55O1xyXG4gIHNxdWFyZTogYW55O1xyXG4gIHN0YXJ0X2RhdGU6IGFueTtcclxuICBlbmRfZGF0ZTogYW55O1xyXG4gIHF1YW50aXR5X2VzdGltYXRlOiBudW1iZXI7XHJcbiAgc3RhdHVzOiBzdHJpbmc7XHJcbiAgZG9jdHlwZTogc3RyaW5nO1xyXG59XHJcblxyXG5leHBvcnQgY29uc3QgY3JlYXRlQ3JvcCA9IGFzeW5jIChkYXRhOiBJQ3JlYXRlQ3JvcFJlcSkgPT4ge1xyXG4gIGNvbnN0IHJlcyA9IGF3YWl0IHJlcXVlc3Q8XHJcbiAgICBBUEkuUmVzcG9uc2VSZXN1bHQ8e1xyXG4gICAgICBkYXRhOiBJQ3JlYXRlQ3JvcFJlcztcclxuICAgIH0+XHJcbiAgPihnZW5lcmF0ZUFQSVBhdGgoJ2FwaS92Mi9jcm9wTWFuYWdlL2Nyb3AnKSwge1xyXG4gICAgbWV0aG9kOiAnUE9TVCcsXHJcbiAgICBkYXRhLFxyXG4gIH0pO1xyXG4gIHJldHVybiByZXMucmVzdWx0O1xyXG59O1xyXG5leHBvcnQgdHlwZSBJVXBkYXRlQ3JvcFJlcSA9IHtcclxuICBuYW1lOiBzdHJpbmc7XHJcbiAgbGFiZWw/OiBzdHJpbmc7XHJcbiAgem9uZV9pZD86IHN0cmluZztcclxuICBzdGFydF9kYXRlPzogc3RyaW5nO1xyXG4gIGVuZF9kYXRlPzogc3RyaW5nO1xyXG4gIHNxdWFyZT86IG51bWJlcjtcclxuICBwbGFudF9pZD86IHN0cmluZztcclxuICBpbWFnZT86IHN0cmluZyB8IG51bGw7XHJcbiAgYXZhdGFyPzogc3RyaW5nIHwgbnVsbDtcclxuICBkZXNjcmlwdGlvbj86IHN0cmluZztcclxuICBzdGF0dXM/OiBzdHJpbmc7XHJcbiAgcXVhbnRpdHlfZXN0aW1hdGU/OiBudW1iZXI7XHJcbiAgaXNfZGVsZXRlZD86IG51bWJlcjtcclxufTtcclxuZXhwb3J0IGNvbnN0IHVwZGF0ZUNyb3AgPSBhc3luYyAoZGF0YTogSVVwZGF0ZUNyb3BSZXEpID0+IHtcclxuICBjb25zdCByZXMgPSBhd2FpdCByZXF1ZXN0PFxyXG4gICAgQVBJLlJlc3BvbnNlUmVzdWx0PHtcclxuICAgICAgZGF0YTogSUNyZWF0ZUNyb3BSZXM7XHJcbiAgICB9PlxyXG4gID4oZ2VuZXJhdGVBUElQYXRoKCdhcGkvdjIvY3JvcE1hbmFnZS9jcm9wJyksIHtcclxuICAgIG1ldGhvZDogJ1BVVCcsXHJcbiAgICBkYXRhLFxyXG4gIH0pO1xyXG4gIHJldHVybiByZXMucmVzdWx0O1xyXG59O1xyXG5leHBvcnQgaW50ZXJmYWNlIElDcm9wTm90ZVJlcyB7XHJcbiAgbmFtZTogc3RyaW5nO1xyXG4gIGNyZWF0aW9uOiBzdHJpbmc7XHJcbiAgbW9kaWZpZWQ6IHN0cmluZztcclxuICBtb2RpZmllZF9ieTogc3RyaW5nO1xyXG4gIG93bmVyOiBzdHJpbmc7XHJcbiAgZG9jc3RhdHVzOiBudW1iZXI7XHJcbiAgaWR4OiBudW1iZXI7XHJcbiAgY3JvcDogc3RyaW5nO1xyXG4gIGxhYmVsOiBzdHJpbmc7XHJcbiAgbm90ZTogc3RyaW5nO1xyXG4gIF91c2VyX3RhZ3M6IGFueTtcclxuICBfY29tbWVudHM6IGFueTtcclxuICBfYXNzaWduOiBhbnk7XHJcbiAgX2xpa2VkX2J5OiBhbnk7XHJcbiAgaW1hZ2U6IGFueTtcclxuICBjcm9wX25hbWU6IHN0cmluZztcclxuICBmaXJzdF9uYW1lOiBzdHJpbmc7XHJcbiAgbGFzdF9uYW1lOiBzdHJpbmc7XHJcbn1cclxuXHJcbmV4cG9ydCBjb25zdCBnZXRDcm9wTm90ZUxpc3QgPSBhc3luYyAocGFyYW1zPzogQVBJLkxpc3RQYXJhbXNSZXEpID0+IHtcclxuICBjb25zdCByZXMgPSBhd2FpdCByZXF1ZXN0PFxyXG4gICAgQVBJLlJlc3BvbnNlUmVzdWx0PHtcclxuICAgICAgZGF0YTogSUNyb3BOb3RlUmVzW107XHJcbiAgICB9PlxyXG4gID4oZ2VuZXJhdGVBUElQYXRoKCdhcGkvdjIvY3JvcE1hbmFnZS9ub3RlJyksIHtcclxuICAgIG1ldGhvZDogJ0dFVCcsXHJcbiAgICBwYXJhbXM6IGdldFBhcmFtc1JlcUxpc3QocGFyYW1zKSxcclxuICB9KTtcclxuICByZXR1cm4gcmVzLnJlc3VsdDtcclxufTtcclxuXHJcbmV4cG9ydCB0eXBlIElDcmVhdGVDcm9wTm90ZVJlcSA9IHtcclxuICBjcm9wOiBzdHJpbmc7XHJcbiAgbGFiZWw6IHN0cmluZztcclxuICBub3RlPzogc3RyaW5nO1xyXG4gIGltYWdlPzogc3RyaW5nO1xyXG59O1xyXG5leHBvcnQgaW50ZXJmYWNlIElDcmVhdGVDcm9wTm90ZVJlcyB7XHJcbiAgbmFtZTogc3RyaW5nO1xyXG4gIG93bmVyOiBzdHJpbmc7XHJcbiAgY3JlYXRpb246IHN0cmluZztcclxuICBtb2RpZmllZDogc3RyaW5nO1xyXG4gIG1vZGlmaWVkX2J5OiBzdHJpbmc7XHJcbiAgZG9jc3RhdHVzOiBudW1iZXI7XHJcbiAgaWR4OiBudW1iZXI7XHJcbiAgaW90X2Nyb3A6IHN0cmluZztcclxuICBjcm9wX25hbWU6IHN0cmluZztcclxuICBsYWJlbDogc3RyaW5nO1xyXG4gIGRlc2NyaXB0aW9uOiBzdHJpbmc7XHJcbiAgaW1hZ2U6IHN0cmluZztcclxuICBkb2N0eXBlOiBzdHJpbmc7XHJcbn1cclxuXHJcbmV4cG9ydCBjb25zdCBjcmVhdGVDcm9wTm90ZSA9IGFzeW5jIChkYXRhOiBJQ3JlYXRlQ3JvcE5vdGVSZXEpID0+IHtcclxuICBjb25zdCByZXMgPSBhd2FpdCByZXF1ZXN0PFxyXG4gICAgQVBJLlJlc3BvbnNlUmVzdWx0PHtcclxuICAgICAgZGF0YTogSUNyZWF0ZUNyb3BOb3RlUmVzO1xyXG4gICAgfT5cclxuICA+KGdlbmVyYXRlQVBJUGF0aCgnYXBpL3YyL2Nyb3BNYW5hZ2Uvbm90ZScpLCB7XHJcbiAgICBtZXRob2Q6ICdQT1NUJyxcclxuICAgIGRhdGEsXHJcbiAgfSk7XHJcbiAgcmV0dXJuIHJlcy5yZXN1bHQ7XHJcbn07XHJcblxyXG5leHBvcnQgaW50ZXJmYWNlIElVcGRhdGVDcm9wTm90ZVJlcSB7XHJcbiAgbmFtZTogc3RyaW5nO1xyXG4gIGxhYmVsPzogc3RyaW5nO1xyXG4gIG5vdGU/OiBzdHJpbmc7XHJcbiAgaW1hZ2U/OiBzdHJpbmc7XHJcbiAgY3JvcDogc3RyaW5nO1xyXG59XHJcblxyXG5leHBvcnQgY29uc3QgdXBkYXRlQ3JvcE5vdGUgPSBhc3luYyAoZGF0YTogSVVwZGF0ZUNyb3BOb3RlUmVxKSA9PiB7XHJcbiAgY29uc3QgcmVzID0gYXdhaXQgcmVxdWVzdDxcclxuICAgIEFQSS5SZXNwb25zZVJlc3VsdDx7XHJcbiAgICAgIGRhdGE6IElDcm9wTm90ZVJlcztcclxuICAgIH0+XHJcbiAgPihnZW5lcmF0ZUFQSVBhdGgoJ2FwaS92Mi9jcm9wTWFuYWdlL25vdGUnKSwge1xyXG4gICAgbWV0aG9kOiAnUFVUJyxcclxuICAgIGRhdGEsXHJcbiAgfSk7XHJcbiAgcmV0dXJuIHJlcy5yZXN1bHQ7XHJcbn07XHJcblxyXG5leHBvcnQgY29uc3QgZGVsZXRlQ3JvcE5vdGUgPSBhc3luYyAoeyBuYW1lIH06IHsgbmFtZTogc3RyaW5nIH0pID0+IHtcclxuICBjb25zdCByZXMgPSBhd2FpdCByZXF1ZXN0PFxyXG4gICAgQVBJLlJlc3BvbnNlUmVzdWx0PHtcclxuICAgICAgZXhjX3R5cGU6ICdEb2VzTm90RXhpc3RFcnJvcic7XHJcbiAgICAgIG1lc3NhZ2U6ICdvayc7XHJcbiAgICB9PlxyXG4gID4oZ2VuZXJhdGVBUElQYXRoKGBhcGkvdjIvY3JvcE1hbmFnZS9ub3RlP25hbWU9JHtuYW1lfWApLCB7XHJcbiAgICBtZXRob2Q6ICdERUxFVEUnLFxyXG4gIH0pO1xyXG4gIHJldHVybiByZXMucmVzdWx0O1xyXG59O1xyXG4vLyBQYXJ0aWNpcGFudHNcclxuZXhwb3J0IGNvbnN0IGdldFBhcnRpY2lwYW50c0luQ3JvcCA9IGFzeW5jIChwYXJhbXM/OiBBUEkuTGlzdFBhcmFtc1JlcSkgPT4ge1xyXG4gIGNvbnN0IHJlcyA9IGF3YWl0IHJlcXVlc3Q8XHJcbiAgICBBUEkuUmVzcG9uc2VSZXN1bHQ8e1xyXG4gICAgICBkYXRhOiBJQ3JvcE5vdGVSZXNbXTtcclxuICAgIH0+XHJcbiAgPihnZW5lcmF0ZUFQSVBhdGgoJ2FwaS92Mi9jcm9wTWFuYWdlL2lvdF9lbXBsb3llZV9pbl9jcm9wJyksIHtcclxuICAgIG1ldGhvZDogJ0dFVCcsXHJcbiAgICBwYXJhbXM6IGdldFBhcmFtc1JlcUxpc3QocGFyYW1zKSxcclxuICB9KTtcclxuICByZXR1cm4gcmVzLnJlc3VsdDtcclxufTtcclxuXHJcbmV4cG9ydCBjb25zdCBhZGRQYXJ0aWNpcGFudEluQ3JvcCA9IGFzeW5jIChkYXRhOiB7XHJcbiAgaW90X2Nyb3A6IHN0cmluZztcclxuICBpb3RfY3VzdG9tZXJfdXNlcjogc3RyaW5nO1xyXG59KSA9PiB7XHJcbiAgY29uc3QgcmVzID0gYXdhaXQgcmVxdWVzdDxcclxuICAgIEFQSS5SZXNwb25zZVJlc3VsdDx7XHJcbiAgICAgIGRhdGE6IElDcm9wTm90ZVJlc1tdO1xyXG4gICAgfT5cclxuICA+KGdlbmVyYXRlQVBJUGF0aCgnYXBpL3YyL2Nyb3BNYW5hZ2UvaW90X2VtcGxveWVlX2luX2Nyb3AnKSwge1xyXG4gICAgbWV0aG9kOiAnUE9TVCcsXHJcbiAgICBkYXRhLFxyXG4gIH0pO1xyXG4gIHJldHVybiByZXM7XHJcbn07XHJcblxyXG5leHBvcnQgY29uc3QgdXBkYXRlUGFydGljaXBhbnRzSW5Dcm9wID0gYXN5bmMgKGRhdGE6IHtcclxuICBuYW1lOiBzdHJpbmc7XHJcbiAgaW90X2Nyb3A6IHN0cmluZztcclxuICBpb3RfY3VzdG9tZXJfdXNlcjogc3RyaW5nO1xyXG59KSA9PiB7XHJcbiAgY29uc3QgcmVzID0gYXdhaXQgcmVxdWVzdDxcclxuICAgIEFQSS5SZXNwb25zZVJlc3VsdDx7XHJcbiAgICAgIGRhdGE6IElDcm9wTm90ZVJlc1tdO1xyXG4gICAgfT5cclxuICA+KGdlbmVyYXRlQVBJUGF0aCgnYXBpL3YyL2Nyb3BNYW5hZ2UvaW90X2VtcGxveWVlX2luX2Nyb3AnKSwge1xyXG4gICAgbWV0aG9kOiAnUFVUJyxcclxuICAgIGRhdGEsXHJcbiAgfSk7XHJcbiAgcmV0dXJuIHJlcztcclxufTtcclxuXHJcbmV4cG9ydCBjb25zdCBkZWxldGVQYXJ0aWNpcGFudHNJbkNyb3AgPSBhc3luYyAobmFtZTogc3RyaW5nKSA9PiB7XHJcbiAgY29uc3QgcmVzID0gYXdhaXQgcmVxdWVzdChnZW5lcmF0ZUFQSVBhdGgoJ2FwaS92Mi9jcm9wTWFuYWdlL2lvdF9lbXBsb3llZV9pbl9jcm9wJyksIHtcclxuICAgIG1ldGhvZDogJ0RFTEVURScsXHJcbiAgICBwYXJhbXM6IHtcclxuICAgICAgbmFtZSxcclxuICAgIH0sXHJcbiAgfSk7XHJcbiAgcmV0dXJuIHJlcztcclxufTtcclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgSVN0YXRpc3RpY1Blc3RSZXMge1xyXG4gIHBlc3RfaWQ6IHN0cmluZztcclxuICBwZXN0X2xhYmVsOiBzdHJpbmc7XHJcbiAgY2F0ZWdvcnlfaWQ/OiBzdHJpbmc7XHJcbiAgY2F0ZWdvcnlfbGFiZWw/OiBzdHJpbmc7XHJcbiAgc3RhdGVfaWQ/OiBzdHJpbmc7XHJcbiAgc3RhdGVfbGFiZWw/OiBzdHJpbmc7XHJcbiAgY3VzdG9tZXJfdXNlcl9pZD86IHN0cmluZztcclxuICBmaXJzdF9uYW1lPzogc3RyaW5nO1xyXG4gIGxhc3RfbmFtZT86IHN0cmluZztcclxuICBpbWFnZTogc3RyaW5nO1xyXG59XHJcbmV4cG9ydCBjb25zdCBnZXRTdGF0aXN0aWNQZXN0TGlzdCA9IGFzeW5jIChwYXJhbXM/OiBBUEkuTGlzdFBhcmFtc1JlcSAmIHsgY3JvcElkPzogc3RyaW5nIH0pID0+IHtcclxuICBjb25zdCByZXMgPSBhd2FpdCByZXF1ZXN0PEFQSS5QYWdpbmF0aW9uUmVzcG9uc2VSZXN1bHQ8SVN0YXRpc3RpY1Blc3RSZXNbXT4+KFxyXG4gICAgZ2VuZXJhdGVBUElQYXRoKGBhcGkvdjIvY3JvcE1hbmFnZS9zdGF0aXN0aWNQZXN0TGlzdD9jcm9wX2lkPSR7cGFyYW1zPy5jcm9wSWR9YCksXHJcbiAgICB7XHJcbiAgICAgIG1ldGhvZDogJ0dFVCcsXHJcbiAgICAgIHBhcmFtczogZ2V0UGFyYW1zUmVxTGlzdChwYXJhbXMpLFxyXG4gICAgfSxcclxuICApO1xyXG4gIHJldHVybiByZXMucmVzdWx0O1xyXG59O1xyXG5cclxuZXhwb3J0IGludGVyZmFjZSBJU3RhdGlzdGljTm90ZVJlcyB7XHJcbiAgbm90ZV9pZDogc3RyaW5nO1xyXG4gIG5vdGVfbGFiZWw/OiBzdHJpbmc7XHJcbiAgaW1hZ2U/OiBzdHJpbmc7XHJcbiAgbm90ZTogc3RyaW5nO1xyXG4gIGNyZWF0aW9uOiBzdHJpbmc7XHJcbiAgbW9kaWZpZWQ6IHN0cmluZztcclxufVxyXG5leHBvcnQgY29uc3QgZ2V0U3RhdGlzdGljTm90ZUxpc3QgPSBhc3luYyAocGFyYW1zPzogQVBJLkxpc3RQYXJhbXNSZXEgJiB7IGNyb3BJZD86IHN0cmluZyB9KSA9PiB7XHJcbiAgY29uc3QgcmVzID0gYXdhaXQgcmVxdWVzdDxBUEkuUGFnaW5hdGlvblJlc3BvbnNlUmVzdWx0PElTdGF0aXN0aWNOb3RlUmVzW10+PihcclxuICAgIGdlbmVyYXRlQVBJUGF0aChgYXBpL3YyL2Nyb3BNYW5hZ2Uvc3RhdGlzdGljTm90ZUxpc3Q/Y3JvcF9pZD0ke3BhcmFtcz8uY3JvcElkfWApLFxyXG4gICAge1xyXG4gICAgICBtZXRob2Q6ICdHRVQnLFxyXG4gICAgICBwYXJhbXM6IGdldFBhcmFtc1JlcUxpc3QocGFyYW1zKSxcclxuICAgIH0sXHJcbiAgKTtcclxuICByZXR1cm4gcmVzLnJlc3VsdDtcclxufTtcclxuIl0sIm5hbWVzIjpbInJlcXVlc3QiLCJnZW5lcmF0ZUFQSVBhdGgiLCJnZXRQYXJhbXNSZXFMaXN0IiwiZ2V0Q3JvcExpc3QiLCJfcmVmIiwiX2FzeW5jVG9HZW5lcmF0b3IiLCJfcmVnZW5lcmF0b3JSdW50aW1lIiwibWFyayIsIl9jYWxsZWUiLCJwYXJhbXMiLCJyZXMiLCJ3cmFwIiwiX2NhbGxlZSQiLCJfY29udGV4dCIsInByZXYiLCJuZXh0IiwibWV0aG9kIiwic2VudCIsImFicnVwdCIsInJlc3VsdCIsInN0b3AiLCJfeCIsImFwcGx5IiwiYXJndW1lbnRzIiwiZ2V0VGVtcGxhdGVDcm9wTGlzdCIsIl9yZWYyIiwiX2NhbGxlZTIiLCJfY2FsbGVlMiQiLCJfY29udGV4dDIiLCJfeDIiLCJnZXRDcm9wTWFuYWdlbWVudEluZm9MaXN0IiwiX3JlZjMiLCJfY2FsbGVlMyIsIl9jYWxsZWUzJCIsIl9jb250ZXh0MyIsIl94MyIsImdldEN1cnJlbnRTdGF0ZU9mQ3JvcCIsIl9yZWY0IiwiX2NhbGxlZTQiLCJjcm9wSWQiLCJfY2FsbGVlNCQiLCJfY29udGV4dDQiLCJjcm9wX2lkIiwiX3g0IiwiY3JlYXRlQ3JvcCIsIl9yZWY1IiwiX2NhbGxlZTUiLCJkYXRhIiwiX2NhbGxlZTUkIiwiX2NvbnRleHQ1IiwiX3g1IiwidXBkYXRlQ3JvcCIsIl9yZWY2IiwiX2NhbGxlZTYiLCJfY2FsbGVlNiQiLCJfY29udGV4dDYiLCJfeDYiLCJnZXRDcm9wTm90ZUxpc3QiLCJfcmVmNyIsIl9jYWxsZWU3IiwiX2NhbGxlZTckIiwiX2NvbnRleHQ3IiwiX3g3IiwiY3JlYXRlQ3JvcE5vdGUiLCJfcmVmOCIsIl9jYWxsZWU4IiwiX2NhbGxlZTgkIiwiX2NvbnRleHQ4IiwiX3g4IiwidXBkYXRlQ3JvcE5vdGUiLCJfcmVmOSIsIl9jYWxsZWU5IiwiX2NhbGxlZTkkIiwiX2NvbnRleHQ5IiwiX3g5IiwiZGVsZXRlQ3JvcE5vdGUiLCJfcmVmMTEiLCJfY2FsbGVlMTAiLCJfcmVmMTAiLCJuYW1lIiwiX2NhbGxlZTEwJCIsIl9jb250ZXh0MTAiLCJjb25jYXQiLCJfeDEwIiwiZ2V0UGFydGljaXBhbnRzSW5Dcm9wIiwiX3JlZjEyIiwiX2NhbGxlZTExIiwiX2NhbGxlZTExJCIsIl9jb250ZXh0MTEiLCJfeDExIiwiYWRkUGFydGljaXBhbnRJbkNyb3AiLCJfcmVmMTMiLCJfY2FsbGVlMTIiLCJfY2FsbGVlMTIkIiwiX2NvbnRleHQxMiIsIl94MTIiLCJ1cGRhdGVQYXJ0aWNpcGFudHNJbkNyb3AiLCJfcmVmMTQiLCJfY2FsbGVlMTMiLCJfY2FsbGVlMTMkIiwiX2NvbnRleHQxMyIsIl94MTMiLCJkZWxldGVQYXJ0aWNpcGFudHNJbkNyb3AiLCJfcmVmMTUiLCJfY2FsbGVlMTQiLCJfY2FsbGVlMTQkIiwiX2NvbnRleHQxNCIsIl94MTQiLCJnZXRTdGF0aXN0aWNQZXN0TGlzdCIsIl9yZWYxNiIsIl9jYWxsZWUxNSIsIl9jYWxsZWUxNSQiLCJfY29udGV4dDE1IiwiX3gxNSIsImdldFN0YXRpc3RpY05vdGVMaXN0IiwiX3JlZjE3IiwiX2NhbGxlZTE2IiwiX2NhbGxlZTE2JCIsIl9jb250ZXh0MTYiLCJfeDE2Il0sInNvdXJjZVJvb3QiOiIifQ==
//# sourceURL=webpack-internal:///77890
`)}}]);
