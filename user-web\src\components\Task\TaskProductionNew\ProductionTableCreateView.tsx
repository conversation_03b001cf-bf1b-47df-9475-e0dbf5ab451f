import { TaskProduction, useTaskProductionCreateStore } from '@/stores/TaskProductionCreateStore';
import { DeleteOutlined } from '@ant-design/icons';
import type { ProColumns } from '@ant-design/pro-components';
import { EditableProTable } from '@ant-design/pro-components';
import { FormattedMessage, useIntl } from '@umijs/max';
import { useDebounceEffect } from 'ahooks';
import { Button, Select } from 'antd';
import React, { useState } from 'react';
import CreateProductionCreateView from './CreateProductionCreateView';

const TaskProductionTable = () => {
  const { taskProduction, setTaskProduction } = useTaskProductionCreateStore();
  const [version, setVersion] = useState(0); // State variable to force re-render
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  const intl = useIntl();

  const columns: ProColumns<TaskProduction>[] = [
    {
      title: <FormattedMessage id="production.material_management.production_code" />,
      dataIndex: 'product_id',
      editable: false,
      render: (dom: any, entity: any) => {
        return <>{entity?.item_name}</>;
      },
    },
    {
      title: <FormattedMessage id="production.material_management.production_name" />,
      dataIndex: 'label',
      editable: false,
      render: (dom: any, entity: any) => {
        return <>{entity?.label}</>;
      },
    },
    {
      title: (
        <FormattedMessage id="production_management.production_management.expected_quantity" />
      ),
      dataIndex: 'exp_quantity',
      valueType: 'digit',
      editable: false,
    },
    {
      title: <FormattedMessage id="common.unit" />,
      dataIndex: 'uom_label',
      editable: false,
      renderFormItem: (_, { record }) => {
        const uoms = record?.uoms || [];
        return (
          <Select
            value={record?.active_uom || record?.uom}
            onChange={(value) => {
              const selectedUom = uoms.find((uom: any) => uom.uom === value);
              if (selectedUom) {
                // Cập nhật active_uom và active_conversion_factor
                const updatedRecord = {
                  ...record,
                  active_uom: selectedUom.uom,
                  active_conversion_factor: selectedUom.conversion_factor,
                  uom_label: selectedUom.uom_label,
                };

                // Cập nhật trong store
                const updatedTaskProduction = taskProduction.map((item) =>
                  item.product_id === record?.product_id ? updatedRecord : item,
                );
                setTaskProduction(updatedTaskProduction);
              }
            }}
            options={uoms.map((uom: any) => ({
              label: uom.uom_label,
              value: uom.uom,
            }))}
          />
        );
      },
      render: (_, record) => {
        return record?.uom_label || record?.active_uom;
      },
    },
    {
      title: <FormattedMessage id="common.action" />,
      editable: false,
      render: (dom: any, entity: any) => {
        return (
          <Button
            danger
            size="small"
            style={{ display: 'flex', alignItems: 'center' }}
            onClick={() => handlerRemove(entity.product_id)}
          >
            <DeleteOutlined />
          </Button>
        );
      },
    },
  ];

  const handlerRemove = async (id: any) => {
    try {
      const updatedTaskProduction = taskProduction.filter((item) => item.product_id !== id);
      setTaskProduction(updatedTaskProduction);
    } catch (error) {
      console.log(error);
    }
  };

  useDebounceEffect(() => {
    console.log('task production', taskProduction);
    setEditableRowKeys(taskProduction.map((item) => item.product_id));
    setVersion((prev) => prev + 1); // Force re-render by updating version
  }, [taskProduction]);

  return (
    <>
      <EditableProTable
        key={version} // Use version to force re-render
        headerTitle={intl.formatMessage({ id: 'common.production' })}
        columns={columns}
        rowKey="product_id"
        dataSource={taskProduction}
        value={taskProduction}
        editable={{
          type: 'multiple',
          editableKeys,
          actionRender: (row, config, defaultDoms) => {
            return [defaultDoms.delete];
          },
          onValuesChange: (record, recordList) => {
            setTaskProduction(recordList);
          },
          onChange: (key) => {
            setEditableRowKeys(key);
          },
        }}
        pagination={{
          defaultPageSize: 20,
          pageSizeOptions: ['20', '50', '100'],
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        recordCreatorProps={false}
        search={false}
      />
    </>
  );
};

const TaskProductionManagement = () => {
  return (
    <>
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '16px',
        }}
      >
        <h2></h2>
        <CreateProductionCreateView />
      </div>
      <TaskProductionTable />
    </>
  );
};

export default TaskProductionManagement;
