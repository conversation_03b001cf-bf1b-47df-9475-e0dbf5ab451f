"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[6089],{47944:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return /* binding */ CropLog_DetailCropNote; }
});

// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/slicedToArray.js
var slicedToArray = __webpack_require__(5574);
var slicedToArray_default = /*#__PURE__*/__webpack_require__.n(slicedToArray);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-skeleton/es/components/List/index.js
var List = __webpack_require__(56517);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-layout/es/components/PageContainer/index.js + 18 modules
var PageContainer = __webpack_require__(6110);
// EXTERNAL MODULE: ./src/.umi-production/exports.ts + 34 modules
var _umi_production_exports = __webpack_require__(7837);
// EXTERNAL MODULE: ./node_modules/antd/es/form/index.js + 18 modules
var es_form = __webpack_require__(98138);
// EXTERNAL MODULE: ./node_modules/antd/es/tabs/index.js + 30 modules
var tabs = __webpack_require__(48096);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/regeneratorRuntime.js
var regeneratorRuntime = __webpack_require__(15009);
var regeneratorRuntime_default = /*#__PURE__*/__webpack_require__.n(regeneratorRuntime);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(99289);
var asyncToGenerator_default = /*#__PURE__*/__webpack_require__.n(asyncToGenerator);
// EXTERNAL MODULE: ./src/services/crop.ts
var crop = __webpack_require__(52662);
// EXTERNAL MODULE: ./node_modules/antd/es/row/index.js
var row = __webpack_require__(71230);
// EXTERNAL MODULE: ./node_modules/antd/es/col/index.js
var col = __webpack_require__(15746);
// EXTERNAL MODULE: ./node_modules/antd/es/card/index.js + 3 modules
var card = __webpack_require__(76216);
// EXTERNAL MODULE: ./node_modules/antd/es/input/index.js + 4 modules
var input = __webpack_require__(96365);
// EXTERNAL MODULE: ./node_modules/antd/es/image/index.js + 37 modules
var es_image = __webpack_require__(11499);
// EXTERNAL MODULE: ./node_modules/react/jsx-runtime.js
var jsx_runtime = __webpack_require__(85893);
;// CONCATENATED MODULE: ./src/pages/FarmingManagement/CropLog/DetailCropNote/components/Image.tsx




var ImageCropNote = function ImageCropNote(_ref) {
  var imageLink = _ref.imageLink,
    index = _ref.index,
    callbackFunc = _ref.callbackFunc;
  return /*#__PURE__*/(0,jsx_runtime.jsx)(jsx_runtime.Fragment, {
    children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_image/* default */.Z, {
      width: 200,
      src: 'https://iot.viis.tech/api/v2/file/download?file_url=' + imageLink
    })
  });
};
/* harmony default export */ var Image = (ImageCropNote);
;// CONCATENATED MODULE: ./src/pages/FarmingManagement/CropLog/DetailCropNote/components/DetailCropNoteTab.tsx










var Item = es_form/* default */.Z.Item;
var DetailCropNoteTab = function DetailCropNoteTab(props) {
  var _useState = (0,react.useState)(false),
    _useState2 = slicedToArray_default()(_useState, 2),
    loading = _useState2[0],
    setLoading = _useState2[1];
  var _useState3 = (0,react.useState)({}),
    _useState4 = slicedToArray_default()(_useState3, 2),
    cropNote = _useState4[0],
    setCropNote = _useState4[1];
  var _useState5 = (0,react.useState)([]),
    _useState6 = slicedToArray_default()(_useState5, 2),
    cropList = _useState6[0],
    setCropList = _useState6[1];
  var _useState7 = (0,react.useState)([]),
    _useState8 = slicedToArray_default()(_useState7, 2),
    imageLinks = _useState8[0],
    setImageLinks = _useState8[1];
  var _Form$useForm = es_form/* default */.Z.useForm(),
    _Form$useForm2 = slicedToArray_default()(_Form$useForm, 1),
    form = _Form$useForm2[0];
  var cropNoteName = props.cropNoteID;
  var renderImageCropNoteLayout = function renderImageCropNoteLayout() {
    var ImageCropNoteComponents = [];
    var rowImages = [];
    imageLinks.forEach(function (imageLink, index) {
      rowImages.push(imageLink);
      if ((index + 1) % 4 === 0 || index === imageLinks.length - 1) {
        // When we have 4 images in the row or we have reached the last image
        var ImageCropNoteRow = /*#__PURE__*/(0,jsx_runtime.jsx)(row/* default */.Z, {
          className: "gutter-row",
          gutter: 4,
          children: rowImages.map(function (image, idx) {
            return /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
              className: "gutter-row",
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(Image, {
                imageLink: image,
                index: idx
              })
            }, "col_".concat(index, "_").concat(idx));
          })
        }, "row_".concat(index));
        ImageCropNoteComponents.push(ImageCropNoteRow);
        rowImages = [];
      }
    });
    return ImageCropNoteComponents;
  };
  var initCropNote = /*#__PURE__*/function () {
    var _ref = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
      var params, sort, filters, res, data;
      return regeneratorRuntime_default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _context.prev = 0;
            setLoading(true);
            params = '', sort = '', filters = [['iot_Crop_note', 'name', 'like', cropNoteName]];
            _context.next = 5;
            return (0,crop/* getCropNote */.dK)({
              page: 1,
              size: 1000,
              fields: ['*'],
              filters: JSON.stringify(filters),
              or_filters: [],
              order_by: '',
              group_by: ''
            });
          case 5:
            res = _context.sent;
            data = res.data.data;
            console.log('receive data', data[0]);
            setCropNote(data[0]);
            if (data[0].image) {
              setImageLinks(data[0].image.split(','));
            }
            form.setFieldsValue(data[0]);
            _context.next = 16;
            break;
          case 13:
            _context.prev = 13;
            _context.t0 = _context["catch"](0);
            console.log(_context.t0);
          case 16:
            _context.prev = 16;
            setLoading(false);
            return _context.finish(16);
          case 19:
          case "end":
            return _context.stop();
        }
      }, _callee, null, [[0, 13, 16, 19]]);
    }));
    return function initCropNote() {
      return _ref.apply(this, arguments);
    };
  }();
  var initCropList = /*#__PURE__*/function () {
    var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2() {
      var res, data;
      return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            _context2.prev = 0;
            setLoading(true);
            // let [params, sort, filters]: any[] = ['', '', [["iot_Crop_note", "name", "like", cropNoteName]]];
            _context2.next = 4;
            return (0,crop/* getCrop */.EH)({
              page: 1,
              size: 1000,
              fields: ['*'],
              filters: JSON.stringify([]),
              or_filters: [],
              order_by: '',
              group_by: ''
            });
          case 4:
            res = _context2.sent;
            data = res.data.data;
            console.log('receive data', data);
            setCropList(data);
            _context2.next = 13;
            break;
          case 10:
            _context2.prev = 10;
            _context2.t0 = _context2["catch"](0);
            console.log(_context2.t0);
          case 13:
            _context2.prev = 13;
            setLoading(false);
            return _context2.finish(13);
          case 16:
          case "end":
            return _context2.stop();
        }
      }, _callee2, null, [[0, 10, 13, 16]]);
    }));
    return function initCropList() {
      return _ref2.apply(this, arguments);
    };
  }();
  (0,react.useEffect)(function () {
    initCropNote();
    initCropList();
  }, [cropNoteName]);
  return /*#__PURE__*/(0,jsx_runtime.jsx)(card/* default */.Z, {
    title: cropNote ? cropNote.name : 'Loading...' // Ho\u1EB7c "N/A" ho\u1EB7c gi\xE1 tr\u1ECB m\u1EB7c \u0111\u1ECBnh kh\xE1c tu\u1EF3 \xFD
    ,
    loading: loading,
    children: /*#__PURE__*/(0,jsx_runtime.jsxs)(es_form/* default */.Z, {
      size: "small",
      layout: "horizontal",
      labelCol: {
        span: 24
      },
      labelAlign: "left",
      form: form,
      children: [/*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
        gutter: 5,
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
          className: "gutter-row",
          md: 8,
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(Item, {
            label: "T\\xEAn ghi ch\\xFA",
            labelCol: {
              span: 24
            },
            name: "label",
            style: {
              marginBottom: '16px',
              fontWeight: 'bold'
            } // Add the fontWeight property here
            ,
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(input/* default */.Z, {
              readOnly: true
            })
          })
        }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
          className: "gutter-row",
          md: 8,
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(Item, {
            label: "T\\xEAn v\\u1EE5 m\\xF9a",
            labelCol: {
              span: 24
            },
            name: "crop_name",
            style: {
              marginBottom: '16px',
              fontWeight: 'bold'
            } // Add the fontWeight property here
            ,
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(input/* default */.Z, {
              readOnly: true
            })
          })
        })]
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(row/* default */.Z, {
        gutter: 5,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
          className: "gutter-row",
          md: 24,
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(Item, {
            label: "N\\u1ED9i dung ghi ch\\xFA",
            labelCol: {
              span: 24
            },
            name: "note",
            style: {
              marginBottom: '16px',
              fontWeight: 'bold'
            } // Add the fontWeight property here
            ,
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(input/* default */.Z.TextArea, {
              readOnly: true
            })
          })
        })
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        className: "gutter-row",
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(Item, {
          label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
            id: 'common.form.image'
          }),
          labelCol: {
            span: 24
          },
          name: "image",
          style: {
            marginBottom: '16px',
            fontWeight: 'bold'
          } // Add the fontWeight property here
          ,
          children: renderImageCropNoteLayout()
        })
      })]
    })
  });
};
/* harmony default export */ var components_DetailCropNoteTab = (DetailCropNoteTab);
;// CONCATENATED MODULE: ./src/pages/FarmingManagement/CropLog/DetailCropNote/index.tsx








var DetailCropNote_Item = es_form/* default */.Z.Item;
var DetailCropNote = function DetailCropNote() {
  var _searchParams$get;
  var _useState = (0,react.useState)("1"),
    _useState2 = slicedToArray_default()(_useState, 2),
    tabActive = _useState2[0],
    setTabActive = _useState2[1];
  var _useSearchParams = (0,_umi_production_exports.useSearchParams)(),
    _useSearchParams2 = slicedToArray_default()(_useSearchParams, 2),
    searchParams = _useSearchParams2[0],
    setSearchParams = _useSearchParams2[1];
  var _useState3 = (0,react.useState)(false),
    _useState4 = slicedToArray_default()(_useState3, 2),
    loading = _useState4[0],
    setLoading = _useState4[1];
  var _useState5 = (0,react.useState)({}),
    _useState6 = slicedToArray_default()(_useState5, 2),
    cropNote = _useState6[0],
    setCropNote = _useState6[1];
  var _useState7 = (0,react.useState)([]),
    _useState8 = slicedToArray_default()(_useState7, 2),
    cropList = _useState8[0],
    setCropList = _useState8[1];
  var _useState9 = (0,react.useState)([]),
    _useState10 = slicedToArray_default()(_useState9, 2),
    imageLinks = _useState10[0],
    setImageLinks = _useState10[1];
  var _Form$useForm = es_form/* default */.Z.useForm(),
    _Form$useForm2 = slicedToArray_default()(_Form$useForm, 1),
    form = _Form$useForm2[0];
  var cropNoteName = (_searchParams$get = searchParams.get("crop_note_id")) !== null && _searchParams$get !== void 0 ? _searchParams$get : '';

  // const deleteImageLink = (indexToDelete: number) => {
  //   // Create a new array without the image link at the specified index
  //   const newImageLinks = imageLinks.filter((_, index) => index !== indexToDelete);
  //   setImageLinks(newImageLinks);
  // };

  // const renderImageCropNoteLayout = () => {
  //   const ImageCropNoteComponents: JSX.Element[] = [];
  //   let rowImages: string[] = [];

  //   imageLinks.forEach((imageLink, index) => {
  //     rowImages.push(imageLink);

  //     if ((index + 1) % 4 === 0 || index === imageLinks.length - 1) {
  //       // When we have 4 images in the row or we have reached the last image
  //       const ImageCropNoteRow = (
  //         <Row className="gutter-row" gutter={4} key={\`row_\${index}\`}>
  //           {rowImages.map((image, idx) => (
  //             <Col className="gutter-row" key={\`col_\${index}_\${idx}\`}>
  //               <ImageCropNote imageLink={image} index={idx} callbackFunc={deleteImageLink} />
  //             </Col>
  //           ))}
  //         </Row>
  //       );
  //       ImageCropNoteComponents.push(ImageCropNoteRow);
  //       rowImages = [];
  //     }
  //   });

  //   return ImageCropNoteComponents;
  // };

  // const initCropNote = async () => {
  //   try {
  //     setLoading(true);
  //     let [params, sort, filters]: any[] = ['', '', [["iot_Crop_note", "name", "like", cropNoteName]]];
  //     const res: any = await getCropNote({
  //       page: 1,
  //       size: 1000,
  //       fields: ['*'],
  //       filters: JSON.stringify(filters),
  //       or_filters: [],
  //       order_by: '',
  //       group_by: '',
  //     })
  //     let data: any = res.data.data
  //     console.log("receive data", data[0])
  //     setCropNote(data[0])
  //     if(data[0].image)
  // {
  //   setImageLinks(data[0].image.split(','));
  // }
  //     form.setFieldsValue(data[0])
  //   } catch (error) {
  //     console.log(error)
  //   } finally {
  //     setLoading(false);
  //   }
  // }

  // const initCropList = async () => {
  //   try {
  //     setLoading(true);
  //     // let [params, sort, filters]: any[] = ['', '', [["iot_Crop_note", "name", "like", cropNoteName]]];
  //     const res: any = await getCrop({
  //       page: 1,
  //       size: 1000,
  //       fields: ['*'],
  //       filters: JSON.stringify([]),
  //       or_filters: [],
  //       order_by: '',
  //       group_by: '',
  //     })
  //     let data: any = res.data.data
  //     console.log("receive data", data)
  //     setCropList(data)
  //   } catch (error) {
  //     console.log(error)
  //   } finally {
  //     setLoading(false);
  //   }
  // }

  // useEffect(() => {
  //   initCropNote();
  //   initCropList();
  // }, [cropNoteName])

  return /*#__PURE__*/(0,jsx_runtime.jsx)(react.Suspense, {
    fallback: /*#__PURE__*/(0,jsx_runtime.jsx)(List/* default */.ZP, {}),
    children: /*#__PURE__*/(0,jsx_runtime.jsx)(jsx_runtime.Fragment, {
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(PageContainer/* PageContainer */._z, {
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(tabs/* default */.Z, {
          activeKey: tabActive,
          onChange: function onChange(e) {
            setTabActive(e);
          },
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(tabs/* default */.Z.TabPane, {
            tab: "Th\\xF4ng tin chi ti\\u1EBFt ghi ch\\xFA",
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(components_DetailCropNoteTab, {
              cropNoteID: cropNoteName
            })
          }, "1")
        })
      })
    })
  });
};
/* harmony default export */ var CropLog_DetailCropNote = (DetailCropNote);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///47944
`)},52662:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   EH: function() { return /* binding */ getCrop; },
/* harmony export */   Gz: function() { return /* binding */ getCropManagementInfoList; },
/* harmony export */   Kw: function() { return /* binding */ getCropParticipantsTaskList; },
/* harmony export */   NQ: function() { return /* binding */ getCropWorksheetStatistic; },
/* harmony export */   _R: function() { return /* binding */ getCropItemStatistic; },
/* harmony export */   dK: function() { return /* binding */ getCropNote; },
/* harmony export */   e4: function() { return /* binding */ getCropByTask; },
/* harmony export */   hD: function() { return /* binding */ getCropParticipantsStatistic; },
/* harmony export */   qQ: function() { return /* binding */ getCropProductionStatisticDetailTask; },
/* harmony export */   su: function() { return /* binding */ getCropProductionQuantityStatistic; },
/* harmony export */   vx: function() { return /* binding */ getCropItemStatisticDetailTask; },
/* harmony export */   ym: function() { return /* binding */ getCropPest; }
/* harmony export */ });
/* unused harmony export cropList */
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(97857);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(7837);
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(467);





var CRUD_PATH = {
  CREATE: 'crop',
  READ: 'crop',
  UPDATE: 'crop',
  DELETE: 'crop'
};
var getCropManagementInfoList = /*#__PURE__*/function () {
  var _ref = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/cropManage/crop-management-info'), {
            method: 'GET',
            params: params
          });
        case 2:
          res = _context.sent;
          return _context.abrupt("return", {
            data: res.result
          });
        case 4:
        case "end":
          return _context.stop();
      }
    }, _callee);
  }));
  return function getCropManagementInfoList(_x) {
    return _ref.apply(this, arguments);
  };
}();
var getCropByTask = /*#__PURE__*/function () {
  var _ref2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee2(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          _context2.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/cropManage/crop-by-task'), {
            method: 'GET',
            params: params
          });
        case 2:
          res = _context2.sent;
          return _context2.abrupt("return", {
            data: res.result
          });
        case 4:
        case "end":
          return _context2.stop();
      }
    }, _callee2);
  }));
  return function getCropByTask(_x2) {
    return _ref2.apply(this, arguments);
  };
}();
function cropList(_x3) {
  return _cropList.apply(this, arguments);
}
function _cropList() {
  _cropList = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee13(_ref3) {
    var _ref3$page, page, _ref3$size, size, _ref3$fields, fields, _ref3$filters, filters, _ref3$or_filters, or_filters, _ref3$order_by, order_by, _ref3$group_by, group_by, params, result;
    return _regeneratorRuntime().wrap(function _callee13$(_context13) {
      while (1) switch (_context13.prev = _context13.next) {
        case 0:
          _ref3$page = _ref3.page, page = _ref3$page === void 0 ? 0 : _ref3$page, _ref3$size = _ref3.size, size = _ref3$size === void 0 ? 20 : _ref3$size, _ref3$fields = _ref3.fields, fields = _ref3$fields === void 0 ? ['*'] : _ref3$fields, _ref3$filters = _ref3.filters, filters = _ref3$filters === void 0 ? [] : _ref3$filters, _ref3$or_filters = _ref3.or_filters, or_filters = _ref3$or_filters === void 0 ? [] : _ref3$or_filters, _ref3$order_by = _ref3.order_by, order_by = _ref3$order_by === void 0 ? '' : _ref3$order_by, _ref3$group_by = _ref3.group_by, group_by = _ref3$group_by === void 0 ? '' : _ref3$group_by;
          _context13.prev = 1;
          params = {
            page: page,
            size: size,
            fields: JSON.stringify(fields),
            filters: JSON.stringify(filters),
            or_filters: JSON.stringify(or_filters)
            // order_by,
            // group_by
          };
          _context13.next = 5;
          return request(generateAPIPath("api/v2/cropManage/".concat(CRUD_PATH.READ)), {
            method: 'GET',
            params: params,
            queryParams: params
          });
        case 5:
          result = _context13.sent;
          return _context13.abrupt("return", result.result);
        case 9:
          _context13.prev = 9;
          _context13.t0 = _context13["catch"](1);
          console.log(_context13.t0);
          throw _context13.t0;
        case 13:
        case "end":
          return _context13.stop();
      }
    }, _callee13, null, [[1, 9]]);
  }));
  return _cropList.apply(this, arguments);
}
var getCropNote = /*#__PURE__*/function () {
  var _ref4 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee3(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee3$(_context3) {
      while (1) switch (_context3.prev = _context3.next) {
        case 0:
          _context3.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)("api/v2/cropManage/note"), {
            method: 'GET',
            params: params
          });
        case 2:
          res = _context3.sent;
          return _context3.abrupt("return", {
            data: res.result
          });
        case 4:
        case "end":
          return _context3.stop();
      }
    }, _callee3);
  }));
  return function getCropNote(_x4) {
    return _ref4.apply(this, arguments);
  };
}();
var getCropPest = /*#__PURE__*/function () {
  var _ref5 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee4(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee4$(_context4) {
      while (1) switch (_context4.prev = _context4.next) {
        case 0:
          _context4.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)("api/v2/cropManage/pest"), {
            method: 'GET',
            params: params
          });
        case 2:
          res = _context4.sent;
          console.log(' res.result', res.result);
          return _context4.abrupt("return", {
            data: res.result
          });
        case 5:
        case "end":
          return _context4.stop();
      }
    }, _callee4);
  }));
  return function getCropPest(_x5) {
    return _ref5.apply(this, arguments);
  };
}();
var getCrop = /*#__PURE__*/function () {
  var _ref6 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee5(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee5$(_context5) {
      while (1) switch (_context5.prev = _context5.next) {
        case 0:
          _context5.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/cropManage/crop'), {
            method: 'GET',
            params: params
          });
        case 2:
          res = _context5.sent;
          return _context5.abrupt("return", {
            data: res.result
          });
        case 4:
        case "end":
          return _context5.stop();
      }
    }, _callee5);
  }));
  return function getCrop(_x6) {
    return _ref6.apply(this, arguments);
  };
}();
var getCropItemStatistic = /*#__PURE__*/function () {
  var _ref7 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee6(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee6$(_context6) {
      while (1) switch (_context6.prev = _context6.next) {
        case 0:
          _context6.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/cropManage/statisticItem'), {
            method: 'GET',
            params: params
          });
        case 2:
          res = _context6.sent;
          return _context6.abrupt("return", {
            data: res.result
          });
        case 4:
        case "end":
          return _context6.stop();
      }
    }, _callee6);
  }));
  return function getCropItemStatistic(_x7) {
    return _ref7.apply(this, arguments);
  };
}();
var getCropItemStatisticDetailTask = /*#__PURE__*/function () {
  var _ref8 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee7(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee7$(_context7) {
      while (1) switch (_context7.prev = _context7.next) {
        case 0:
          _context7.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/cropManage/statisticItem/detail-in-crop'), {
            method: 'GET',
            params: params
          });
        case 2:
          res = _context7.sent;
          return _context7.abrupt("return", {
            data: res.result.data,
            pagination: res.result.pagination
          });
        case 4:
        case "end":
          return _context7.stop();
      }
    }, _callee7);
  }));
  return function getCropItemStatisticDetailTask(_x8) {
    return _ref8.apply(this, arguments);
  };
}();
var getCropProductionStatisticDetailTask = /*#__PURE__*/function () {
  var _ref9 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee8(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee8$(_context8) {
      while (1) switch (_context8.prev = _context8.next) {
        case 0:
          _context8.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/cropManage/statisticProductQuantity/detail-in-crop'), {
            method: 'GET',
            params: params
          });
        case 2:
          res = _context8.sent;
          return _context8.abrupt("return", {
            data: res.result.data,
            pagination: res.result.pagination
          });
        case 4:
        case "end":
          return _context8.stop();
      }
    }, _callee8);
  }));
  return function getCropProductionStatisticDetailTask(_x9) {
    return _ref9.apply(this, arguments);
  };
}();
var getCropParticipantsStatistic = /*#__PURE__*/function () {
  var _ref10 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee9(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee9$(_context9) {
      while (1) switch (_context9.prev = _context9.next) {
        case 0:
          _context9.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/cropManage/statisticParticipant'), {
            method: 'GET',
            params: params
          });
        case 2:
          res = _context9.sent;
          return _context9.abrupt("return", {
            data: res.result.data,
            pagination: res.result.pagination
          });
        case 4:
        case "end":
          return _context9.stop();
      }
    }, _callee9);
  }));
  return function getCropParticipantsStatistic(_x10) {
    return _ref10.apply(this, arguments);
  };
}();
var getCropParticipantsTaskList = /*#__PURE__*/function () {
  var _ref11 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee10(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee10$(_context10) {
      while (1) switch (_context10.prev = _context10.next) {
        case 0:
          _context10.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/cropManage/statisticParticipant/detail-task-list'), {
            method: 'GET',
            params: params
          });
        case 2:
          res = _context10.sent;
          return _context10.abrupt("return", {
            data: res.result.data,
            pagination: res.result.pagination
          });
        case 4:
        case "end":
          return _context10.stop();
      }
    }, _callee10);
  }));
  return function getCropParticipantsTaskList(_x11) {
    return _ref11.apply(this, arguments);
  };
}();
var getCropProductionQuantityStatistic = /*#__PURE__*/function () {
  var _ref12 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee11(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee11$(_context11) {
      while (1) switch (_context11.prev = _context11.next) {
        case 0:
          _context11.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/cropManage/statisticProductQuantity'), {
            method: 'GET',
            params: params
          });
        case 2:
          res = _context11.sent;
          return _context11.abrupt("return", {
            data: res.result
          });
        case 4:
        case "end":
          return _context11.stop();
      }
    }, _callee11);
  }));
  return function getCropProductionQuantityStatistic(_x12) {
    return _ref12.apply(this, arguments);
  };
}();
var getCropWorksheetStatistic = /*#__PURE__*/function () {
  var _ref13 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee12(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee12$(_context12) {
      while (1) switch (_context12.prev = _context12.next) {
        case 0:
          _context12.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/cropManage/statisticWorksheet'), {
            method: 'GET',
            params: params
          });
        case 2:
          res = _context12.sent;
          return _context12.abrupt("return", {
            data: res.result.map(function (stat) {
              return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default()({}, stat), {}, {
                type: stat.type.toLowerCase() === 'hour' ? 'Gi\u1EDD' : 'C\xF4ng'
              });
            })
          });
        case 4:
        case "end":
          return _context12.stop();
      }
    }, _callee12);
  }));
  return function getCropWorksheetStatistic(_x13) {
    return _ref13.apply(this, arguments);
  };
}();//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///52662
`)}}]);
