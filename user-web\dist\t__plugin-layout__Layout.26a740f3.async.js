"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[6301],{58763:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return /* binding */ Layout; }
});

// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/slicedToArray.js
var slicedToArray = __webpack_require__(5574);
var slicedToArray_default = /*#__PURE__*/__webpack_require__.n(slicedToArray);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/toConsumableArray.js
var toConsumableArray = __webpack_require__(19632);
var toConsumableArray_default = /*#__PURE__*/__webpack_require__.n(toConsumableArray);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/objectSpread2.js
var objectSpread2 = __webpack_require__(97857);
var objectSpread2_default = /*#__PURE__*/__webpack_require__.n(objectSpread2);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/createForOfIteratorHelper.js
var createForOfIteratorHelper = __webpack_require__(64599);
var createForOfIteratorHelper_default = /*#__PURE__*/__webpack_require__.n(createForOfIteratorHelper);
// EXTERNAL MODULE: ./src/.umi-production/exports.ts + 34 modules
var _umi_production_exports = __webpack_require__(7837);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-layout/es/ProLayout.js + 61 modules
var ProLayout = __webpack_require__(7574);
;// CONCATENATED MODULE: ./src/.umi-production/plugin-layout/Layout.css
// extracted by mini-css-extract-plugin

// EXTERNAL MODULE: ./node_modules/react/jsx-runtime.js
var jsx_runtime = __webpack_require__(85893);
;// CONCATENATED MODULE: ./src/.umi-production/plugin-layout/Logo.tsx
// @ts-nocheck
// This file is generated by Umi automatically
// DO NOT CHANGE IT MANUALLY!



var LogoIcon = function LogoIcon() {
  return /*#__PURE__*/(0,jsx_runtime.jsxs)("svg", {
    xmlns: "http://www.w3.org/2000/svg",
    width: "32",
    height: "32",
    viewBox: "0 0 200 200",
    children: [/*#__PURE__*/(0,jsx_runtime.jsxs)("defs", {
      children: [/*#__PURE__*/(0,jsx_runtime.jsxs)("linearGradient", {
        id: "linearGradient-1",
        x1: "62.102%",
        x2: "108.197%",
        y1: "0%",
        y2: "37.864%",
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)("stop", {
          offset: "0%",
          stopColor: "#4285EB"
        }), /*#__PURE__*/(0,jsx_runtime.jsx)("stop", {
          offset: "100%",
          stopColor: "#2EC7FF"
        })]
      }), /*#__PURE__*/(0,jsx_runtime.jsxs)("linearGradient", {
        id: "linearGradient-2",
        x1: "69.644%",
        x2: "54.043%",
        y1: "0%",
        y2: "108.457%",
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)("stop", {
          offset: "0%",
          stopColor: "#29CDFF"
        }), /*#__PURE__*/(0,jsx_runtime.jsx)("stop", {
          offset: "37.86%",
          stopColor: "#148EFF"
        }), /*#__PURE__*/(0,jsx_runtime.jsx)("stop", {
          offset: "100%",
          stopColor: "#0A60FF"
        })]
      }), /*#__PURE__*/(0,jsx_runtime.jsxs)("linearGradient", {
        id: "linearGradient-3",
        x1: "69.691%",
        x2: "16.723%",
        y1: "-12.974%",
        y2: "117.391%",
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)("stop", {
          offset: "0%",
          stopColor: "#FA816E"
        }), /*#__PURE__*/(0,jsx_runtime.jsx)("stop", {
          offset: "41.473%",
          stopColor: "#F74A5C"
        }), /*#__PURE__*/(0,jsx_runtime.jsx)("stop", {
          offset: "100%",
          stopColor: "#F51D2C"
        })]
      }), /*#__PURE__*/(0,jsx_runtime.jsxs)("linearGradient", {
        id: "linearGradient-4",
        x1: "68.128%",
        x2: "30.44%",
        y1: "-35.691%",
        y2: "114.943%",
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)("stop", {
          offset: "0%",
          stopColor: "#FA8E7D"
        }), /*#__PURE__*/(0,jsx_runtime.jsx)("stop", {
          offset: "51.264%",
          stopColor: "#F74A5C"
        }), /*#__PURE__*/(0,jsx_runtime.jsx)("stop", {
          offset: "100%",
          stopColor: "#F51D2C"
        })]
      })]
    }), /*#__PURE__*/(0,jsx_runtime.jsx)("g", {
      fill: "none",
      fillRule: "evenodd",
      stroke: "none",
      strokeWidth: "1",
      children: /*#__PURE__*/(0,jsx_runtime.jsx)("g", {
        transform: "translate(-20 -20)",
        children: /*#__PURE__*/(0,jsx_runtime.jsx)("g", {
          transform: "translate(20 20)",
          children: /*#__PURE__*/(0,jsx_runtime.jsxs)("g", {
            children: [/*#__PURE__*/(0,jsx_runtime.jsxs)("g", {
              fillRule: "nonzero",
              children: [/*#__PURE__*/(0,jsx_runtime.jsxs)("g", {
                children: [/*#__PURE__*/(0,jsx_runtime.jsx)("path", {
                  fill: "url(#linearGradient-1)",
                  d: "M91.588 4.177L4.18 91.513a11.981 11.981 0 000 16.974l87.408 87.336a12.005 12.005 0 0016.989 0l36.648-36.618c4.209-4.205 4.209-11.023 0-15.228-4.208-4.205-11.031-4.205-15.24 0l-27.783 27.76c-1.17 1.169-2.945 1.169-4.114 0l-69.802-69.744c-1.17-1.169-1.17-2.942 0-4.11l69.802-69.745c1.17-1.169 2.944-1.169 4.114 0l27.783 27.76c4.209 4.205 11.032 4.205 15.24 0 4.209-4.205 4.209-11.022 0-15.227L108.581 4.056c-4.719-4.594-12.312-4.557-16.993.12z"
                }), /*#__PURE__*/(0,jsx_runtime.jsx)("path", {
                  fill: "url(#linearGradient-2)",
                  d: "M91.588 4.177L4.18 91.513a11.981 11.981 0 000 16.974l87.408 87.336a12.005 12.005 0 0016.989 0l36.648-36.618c4.209-4.205 4.209-11.023 0-15.228-4.208-4.205-11.031-4.205-15.24 0l-27.783 27.76c-1.17 1.169-2.945 1.169-4.114 0l-69.802-69.744c-1.17-1.169-1.17-2.942 0-4.11l69.802-69.745c2.912-2.51 7.664-7.596 14.642-8.786 5.186-.883 10.855 1.062 17.009 5.837L108.58 4.056c-4.719-4.594-12.312-4.557-16.993.12z"
                })]
              }), /*#__PURE__*/(0,jsx_runtime.jsx)("path", {
                fill: "url(#linearGradient-3)",
                d: "M153.686 135.855c4.208 4.205 11.031 4.205 15.24 0l27.034-27.012c4.7-4.696 4.7-12.28 0-16.974l-27.27-27.15c-4.218-4.2-11.043-4.195-15.254.013-4.209 4.205-4.209 11.022 0 15.227l18.418 18.403c1.17 1.169 1.17 2.943 0 4.111l-18.168 18.154c-4.209 4.205-4.209 11.023 0 15.228z"
              })]
            }), /*#__PURE__*/(0,jsx_runtime.jsx)("ellipse", {
              cx: "100.519",
              cy: "100.437",
              fill: "url(#linearGradient-4)",
              rx: "23.6",
              ry: "23.581"
            })]
          })
        })
      })
    })]
  });
};
/* harmony default export */ var Logo = (LogoIcon);
// EXTERNAL MODULE: ./node_modules/antd/es/result/index.js + 6 modules
var result = __webpack_require__(29905);
// EXTERNAL MODULE: ./node_modules/antd/es/button/index.js + 9 modules
var es_button = __webpack_require__(14726);
;// CONCATENATED MODULE: ./src/.umi-production/plugin-layout/Exception.tsx
// @ts-nocheck
// This file is generated by Umi automatically
// DO NOT CHANGE IT MANUALLY!




var Exception = function Exception(props) {
  var _props$route, _props$route2;
  return (
    // render custom 404
    !props.route && (props.noFound || props.notFound) ||
    // render custom 403
    ((_props$route = props.route) === null || _props$route === void 0 ? void 0 : _props$route.unaccessible) && (props.unAccessible || props.noAccessible) ||
    // render default exception
    (!props.route || ((_props$route2 = props.route) === null || _props$route2 === void 0 ? void 0 : _props$route2.unaccessible)) && /*#__PURE__*/(0,jsx_runtime.jsx)(result/* default */.ZP, {
      status: props.route ? '403' : '404',
      title: props.route ? '403' : '404',
      subTitle: props.route ? '\u62B1\u6B49\uFF0C\u4F60\u65E0\u6743\u8BBF\u95EE\u8BE5\u9875\u9762' : '\u62B1\u6B49\uFF0C\u4F60\u8BBF\u95EE\u7684\u9875\u9762\u4E0D\u5B58\u5728',
      extra: /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
        type: "primary",
        onClick: function onClick() {
          return _umi_production_exports.history.push('/');
        },
        children: "\\u8FD4\\u56DE\\u9996\\u9875"
      })
    }) ||
    // normal render
    props.children
  );
};
/* harmony default export */ var plugin_layout_Exception = (Exception);
// EXTERNAL MODULE: ./node_modules/antd/es/avatar/index.js + 4 modules
var es_avatar = __webpack_require__(7134);
// EXTERNAL MODULE: ./node_modules/antd/es/spin/index.js + 2 modules
var spin = __webpack_require__(75081);
// EXTERNAL MODULE: ./node_modules/antd/es/version/index.js + 1 modules
var version = __webpack_require__(67159);
// EXTERNAL MODULE: ./node_modules/antd/es/menu/index.js + 11 modules
var menu = __webpack_require__(68508);
// EXTERNAL MODULE: ./node_modules/antd/es/dropdown/index.js + 1 modules
var dropdown = __webpack_require__(85418);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/LogoutOutlined.js + 1 modules
var LogoutOutlined = __webpack_require__(92443);
// EXTERNAL MODULE: ./src/.umi-production/plugin-locale/index.ts + 1 modules
var plugin_locale = __webpack_require__(66999);
;// CONCATENATED MODULE: ./src/.umi-production/plugin-layout/rightRender.tsx

// @ts-nocheck
// This file is generated by Umi automatically
// DO NOT CHANGE IT MANUALLY!







function getRightRenderContent(opts) {
  var _opts$initialState, _opts$initialState2, _opts$initialState3, _opts$initialState4, _opts$initialState5;
  if (opts.runtimeConfig.rightRender) {
    return opts.runtimeConfig.rightRender(opts.initialState, opts.setInitialState, opts.runtimeConfig);
  }
  var showAvatar = ((_opts$initialState = opts.initialState) === null || _opts$initialState === void 0 ? void 0 : _opts$initialState.avatar) || ((_opts$initialState2 = opts.initialState) === null || _opts$initialState2 === void 0 ? void 0 : _opts$initialState2.name) || opts.runtimeConfig.logout;
  var disableAvatarImg = ((_opts$initialState3 = opts.initialState) === null || _opts$initialState3 === void 0 ? void 0 : _opts$initialState3.avatar) === false;
  var nameClassName = disableAvatarImg ? 'umi-plugin-layout-name umi-plugin-layout-hide-avatar-img' : 'umi-plugin-layout-name';
  var avatar = showAvatar ? /*#__PURE__*/(0,jsx_runtime.jsxs)("span", {
    className: "umi-plugin-layout-action",
    children: [!disableAvatarImg ? /*#__PURE__*/(0,jsx_runtime.jsx)(es_avatar/* default */.C, {
      size: "small",
      className: "umi-plugin-layout-avatar",
      src: ((_opts$initialState4 = opts.initialState) === null || _opts$initialState4 === void 0 ? void 0 : _opts$initialState4.avatar) || "https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png",
      alt: "avatar"
    }) : null, /*#__PURE__*/(0,jsx_runtime.jsx)("span", {
      className: nameClassName,
      children: (_opts$initialState5 = opts.initialState) === null || _opts$initialState5 === void 0 ? void 0 : _opts$initialState5.name
    })]
  }) : null;
  if (opts.loading) {
    return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
      className: "umi-plugin-layout-right",
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(spin/* default */.Z, {
        size: "small",
        style: {
          marginLeft: 8,
          marginRight: 8
        }
      })
    });
  }

  // \u5982\u679C\u6CA1\u6709\u6253\u5F00Locale\uFF0C\u5E76\u4E14\u5934\u50CF\u4E3A\u7A7A\u5C31\u53D6\u6D88\u6389\u8FD9\u4E2A\u8FD4\u56DE\u7684\u5185\u5BB9

  var langMenu = {
    className: "umi-plugin-layout-menu",
    selectedKeys: [],
    items: [{
      key: "logout",
      label: /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)(LogoutOutlined/* default */.Z, {}), "\\u9000\\u51FA\\u767B\\u5F55"]
      }),
      onClick: function onClick() {
        var _opts$runtimeConfig, _opts$runtimeConfig$l;
        opts === null || opts === void 0 || (_opts$runtimeConfig = opts.runtimeConfig) === null || _opts$runtimeConfig === void 0 || (_opts$runtimeConfig$l = _opts$runtimeConfig.logout) === null || _opts$runtimeConfig$l === void 0 || _opts$runtimeConfig$l.call(_opts$runtimeConfig, opts.initialState);
      }
    }]
  };
  // antd@5 \u548C  4.24 \u4E4B\u540E\u63A8\u8350\u4F7F\u7528 menu\uFF0C\u6027\u80FD\u66F4\u597D
  var dropdownProps;
  if (version/* default */.Z.startsWith("5.") || version/* default */.Z.startsWith("4.24.")) {
    dropdownProps = {
      menu: langMenu
    };
  } else if (version/* default */.Z.startsWith("3.")) {
    dropdownProps = {
      overlay: /*#__PURE__*/(0,jsx_runtime.jsx)(menu/* default */.Z, {
        children: langMenu.items.map(function (item) {
          return /*#__PURE__*/(0,jsx_runtime.jsx)(menu/* default */.Z.Item, {
            onClick: item.onClick,
            children: item.label
          }, item.key);
        })
      })
    };
  } else {
    // \u9700\u8981 antd 4.20.0 \u4EE5\u4E0A\u7248\u672C
    dropdownProps = {
      overlay: /*#__PURE__*/(0,jsx_runtime.jsx)(menu/* default */.Z, objectSpread2_default()({}, langMenu))
    };
  }
  return /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
    className: "umi-plugin-layout-right anticon",
    children: [opts.runtimeConfig.logout ? /*#__PURE__*/(0,jsx_runtime.jsx)(dropdown/* default */.Z, objectSpread2_default()(objectSpread2_default()({}, dropdownProps), {}, {
      overlayClassName: "umi-plugin-layout-container",
      children: avatar
    })) : avatar, /*#__PURE__*/(0,jsx_runtime.jsx)(plugin_locale/* SelectLang */.pD, {})]
  });
}
// EXTERNAL MODULE: ./src/.umi-production/plugin-model/index.tsx
var plugin_model = __webpack_require__(44886);
// EXTERNAL MODULE: ./src/.umi-production/plugin-access/index.tsx
var plugin_access = __webpack_require__(83228);
;// CONCATENATED MODULE: ./src/.umi-production/plugin-layout/Layout.tsx




// @ts-nocheck
// This file is generated by Umi automatically
// DO NOT CHANGE IT MANUALLY!
/// <reference types="@ant-design/pro-components" />
/// <reference types="antd" />












// \u8FC7\u6EE4\u51FA\u9700\u8981\u663E\u793A\u7684\u8DEF\u7531, \u8FD9\u91CC\u7684filterFn \u6307 \u4E0D\u5E0C\u671B\u663E\u793A\u7684\u5C42\u7EA7

var filterRoutes = function filterRoutes(routes, filterFn) {
  if (routes.length === 0) {
    return [];
  }
  var newRoutes = [];
  var _iterator = createForOfIteratorHelper_default()(routes),
    _step;
  try {
    for (_iterator.s(); !(_step = _iterator.n()).done;) {
      var _route = _step.value;
      var newRoute = objectSpread2_default()({}, _route);
      if (filterFn(_route)) {
        if (Array.isArray(newRoute.routes)) {
          newRoutes.push.apply(newRoutes, toConsumableArray_default()(filterRoutes(newRoute.routes, filterFn)));
        }
      } else {
        if (Array.isArray(newRoute.children)) {
          newRoute.children = filterRoutes(newRoute.children, filterFn);
          newRoute.routes = newRoute.children;
        }
        newRoutes.push(newRoute);
      }
    }
  } catch (err) {
    _iterator.e(err);
  } finally {
    _iterator.f();
  }
  return newRoutes;
};

// \u683C\u5F0F\u5316\u8DEF\u7531 \u5904\u7406\u56E0 wrapper \u5BFC\u81F4\u7684 \u83DC\u5355 path \u4E0D\u4E00\u81F4
var mapRoutes = function mapRoutes(routes) {
  if (routes.length === 0) {
    return [];
  }
  return routes.map(function (route) {
    // \u9700\u8981 copy \u4E00\u4EFD, \u5426\u5219\u4F1A\u6C61\u67D3\u539F\u59CB\u6570\u636E
    var newRoute = objectSpread2_default()({}, route);
    if (route.originPath) {
      newRoute.path = route.originPath;
    }
    if (Array.isArray(route.routes)) {
      newRoute.routes = mapRoutes(route.routes);
    }
    if (Array.isArray(route.children)) {
      newRoute.children = mapRoutes(route.children);
    }
    return newRoute;
  });
};
/* harmony default export */ var Layout = (function (props) {
  var location = (0,_umi_production_exports.useLocation)();
  var navigate = (0,_umi_production_exports.useNavigate)();
  var _useAppData = (0,_umi_production_exports.useAppData)(),
    clientRoutes = _useAppData.clientRoutes,
    pluginManager = _useAppData.pluginManager;
  var initialInfo = plugin_model/* useModel */.t && (0,plugin_model/* useModel */.t)('@@initialState') || {
    initialState: undefined,
    loading: false,
    setInitialState: null
  };
  var initialState = initialInfo.initialState,
    loading = initialInfo.loading,
    setInitialState = initialInfo.setInitialState;
  var userConfig = {
    "locale": true,
    "siderWidth": 208,
    "navTheme": "light",
    "colorPrimary": "#44c4a1",
    "color": "#44c4a1",
    "layout": "mix",
    "contentWidth": "Fluid",
    "fixedHeader": false,
    "fixSiderbar": true,
    "title": "Trung t\xE2m d\u1EEF li\u1EC7u",
    "pwa": false,
    "logo": "/viis_logo.svg",
    "iconfontUrl": "",
    "colorWeak": false,
    "token": {
      "sider": {
        "colorMenuBackground": "#FFFFFF",
        "colorTextMenuSelected": "#444444",
        "colorTextMenuSecondary": "#44c4a1",
        "colorBgMenuItemHover": "#44c4a1",
        "colorBgMenuItemSelected": "#44c4a1",
        "colorBgCollapsedButton": "#ffffff",
        "colorBgMenuItemCollapsedElevated": "#44c4a1"
      },
      "pageContainer": {
        "paddingBlockPageContainerContent": 10,
        "colorBgPageContainer": "#fcfcfc"
      },
      "header": {
        "colorBgHeader": "#503C3C",
        "colorTextMenu": "#FFFFFF",
        "colorTextMenuSecondary": "#FFFFFF",
        "colorTextMenuSelected": "#FFFFFF",
        "colorBgMenuItemHover": "#FFFFFF",
        "colorBgMenuItemSelected": "#FFFFFF",
        "colorHeaderTitle": "#FFFFFF",
        "colorTextRightActionsItem": "#FFFFFF",
        "colorBgRightActionsItemHover": "#FFFFFF"
      }
    }
  };
  var _useIntl = (0,plugin_locale/* useIntl */.YB)(),
    formatMessage = _useIntl.formatMessage;
  var runtimeConfig = pluginManager.applyPlugins({
    key: 'layout',
    type: 'modify',
    initialValue: objectSpread2_default()({}, initialInfo)
  });

  // \u73B0\u5728\u7684 layout \u53CA wrapper \u5B9E\u73B0\u662F\u901A\u8FC7\u7236\u8DEF\u7531\u7684\u5F62\u5F0F\u5B9E\u73B0\u7684, \u4F1A\u5BFC\u81F4\u8DEF\u7531\u6570\u636E\u591A\u4E86\u5197\u4F59\u5C42\u7EA7, proLayout \u6D88\u8D39\u65F6, \u65E0\u6CD5\u6B63\u786E\u5C55\u793A\u83DC\u5355, \u8FD9\u91CC\u5BF9\u5197\u4F59\u6570\u636E\u8FDB\u884C\u8FC7\u6EE4\u64CD\u4F5C
  var newRoutes = filterRoutes(clientRoutes.filter(function (route) {
    return route.id === 'ant-design-pro-layout';
  }), function (route) {
    return !!route.isLayout && route.id !== 'ant-design-pro-layout' || !!route.isWrapper;
  });
  var _useAccessMarkedRoute = (0,plugin_access/* useAccessMarkedRoutes */.Mf)(mapRoutes(newRoutes)),
    _useAccessMarkedRoute2 = slicedToArray_default()(_useAccessMarkedRoute, 1),
    route = _useAccessMarkedRoute2[0];
  var matchedRoute = (0,react.useMemo)(function () {
    var _matchRoutes, _matchRoutes$pop;
    return (_matchRoutes = (0,_umi_production_exports.matchRoutes)(route.children, location.pathname)) === null || _matchRoutes === void 0 || (_matchRoutes$pop = _matchRoutes.pop) === null || _matchRoutes$pop === void 0 || (_matchRoutes$pop = _matchRoutes$pop.call(_matchRoutes)) === null || _matchRoutes$pop === void 0 ? void 0 : _matchRoutes$pop.route;
  }, [location.pathname]);
  return /*#__PURE__*/(0,jsx_runtime.jsx)(ProLayout/* ProLayout */.f, objectSpread2_default()(objectSpread2_default()({
    route: route,
    location: location,
    title: userConfig.title || 'ant-design-pro',
    navTheme: "dark",
    siderWidth: 256,
    onMenuHeaderClick: function onMenuHeaderClick(e) {
      e.stopPropagation();
      e.preventDefault();
      navigate('/');
    },
    formatMessage: userConfig.formatMessage || formatMessage,
    menu: {
      locale: userConfig.locale
    },
    logo: Logo,
    menuItemRender: function menuItemRender(menuItemProps, defaultDom) {
      if (menuItemProps.isUrl || menuItemProps.children) {
        return defaultDom;
      }
      if (menuItemProps.path && location.pathname !== menuItemProps.path) {
        return (
          /*#__PURE__*/
          // handle wildcard route path, for example /slave/* from qiankun
          (0,jsx_runtime.jsx)(_umi_production_exports.Link, {
            to: menuItemProps.path.replace('/*', ''),
            target: menuItemProps.target,
            children: defaultDom
          })
        );
      }
      return defaultDom;
    },
    itemRender: function itemRender(route, _, routes) {
      var breadcrumbName = route.breadcrumbName,
        title = route.title,
        path = route.path;
      var label = title || breadcrumbName;
      var last = routes[routes.length - 1];
      if (last) {
        if (last.path === path || last.linkPath === path) {
          return /*#__PURE__*/(0,jsx_runtime.jsx)("span", {
            children: label
          });
        }
      }
      return /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.Link, {
        to: path,
        children: label
      });
    },
    disableContentMargin: true,
    fixSiderbar: true,
    fixedHeader: true
  }, runtimeConfig), {}, {
    rightContentRender: runtimeConfig.rightContentRender !== false && function (layoutProps) {
      var dom = getRightRenderContent({
        runtimeConfig: runtimeConfig,
        loading: loading,
        initialState: initialState,
        setInitialState: setInitialState
      });
      if (runtimeConfig.rightContentRender) {
        return runtimeConfig.rightContentRender(layoutProps, dom, {
          // BREAK CHANGE userConfig > runtimeConfig
          userConfig: userConfig,
          runtimeConfig: runtimeConfig,
          loading: loading,
          initialState: initialState,
          setInitialState: setInitialState
        });
      }
      return dom;
    },
    children: /*#__PURE__*/(0,jsx_runtime.jsx)(plugin_layout_Exception, {
      route: matchedRoute,
      noFound: runtimeConfig === null || runtimeConfig === void 0 ? void 0 : runtimeConfig.noFound,
      notFound: runtimeConfig === null || runtimeConfig === void 0 ? void 0 : runtimeConfig.notFound,
      unAccessible: runtimeConfig === null || runtimeConfig === void 0 ? void 0 : runtimeConfig.unAccessible,
      noAccessible: runtimeConfig === null || runtimeConfig === void 0 ? void 0 : runtimeConfig.noAccessible,
      children: runtimeConfig.childrenRender ? runtimeConfig.childrenRender( /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.Outlet, {}), props) : /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.Outlet, {})
    })
  }));
});//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///58763
`)}}]);
