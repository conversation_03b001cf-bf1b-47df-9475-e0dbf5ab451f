"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[9920],{65573:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(29905);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(85893);


var FallbackComponent = function FallbackComponent() {
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .ZP, {
    status: "403",
    title: "Kh\\xF4ng th\\u1EC3 truy c\\u1EADp",
    subTitle: "Xin l\\u1ED7i, b\\u1EA1n kh\\xF4ng c\\xF3 quy\\u1EC1n xem n\\u1ED9i dung n\\xE0y."
    //   extra={
    //     <Button type="primary" href="/">
    //       Back Home
    //     </Button>
    //   }
  });
};
/* harmony default export */ __webpack_exports__.Z = (FallbackComponent);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNjU1NzMuanMiLCJtYXBwaW5ncyI6Ijs7QUFBOEI7QUFBQTtBQUU5QixJQUFNRyxpQkFBaUIsR0FBRyxTQUFwQkEsaUJBQWlCQSxDQUFBLEVBQVM7RUFDOUIsb0JBQ0VELHNEQUFBLENBQUNGLHNEQUFNO0lBQ0xJLE1BQU0sRUFBQyxLQUFLO0lBQ1pDLEtBQUssRUFBQyxpQ0FBb0I7SUFDMUJDLFFBQVEsRUFBQztJQUNUO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7RUFBQSxDQUNELENBQUM7QUFFTixDQUFDO0FBRUQsc0RBQWVILGlCQUFpQiIsInNvdXJjZXMiOlsid2VicGFjazovL2FudC1kZXNpZ24tcHJvLy4vc3JjL2NvbXBvbmVudHMvRmFsbGJhY2tDb250ZW50L2luZGV4LnRzeD9mZGRhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFJlc3VsdCB9IGZyb20gJ2FudGQnO1xyXG5cclxuY29uc3QgRmFsbGJhY2tDb21wb25lbnQgPSAoKSA9PiB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxSZXN1bHRcclxuICAgICAgc3RhdHVzPVwiNDAzXCJcclxuICAgICAgdGl0bGU9XCJLaMO0bmcgdGjhu4MgdHJ1eSBj4bqtcFwiXHJcbiAgICAgIHN1YlRpdGxlPVwiWGluIGzhu5dpLCBi4bqhbiBraMO0bmcgY8OzIHF1eeG7gW4geGVtIG7hu5lpIGR1bmcgbsOgeS5cIlxyXG4gICAgICAvLyAgIGV4dHJhPXtcclxuICAgICAgLy8gICAgIDxCdXR0b24gdHlwZT1cInByaW1hcnlcIiBocmVmPVwiL1wiPlxyXG4gICAgICAvLyAgICAgICBCYWNrIEhvbWVcclxuICAgICAgLy8gICAgIDwvQnV0dG9uPlxyXG4gICAgICAvLyAgIH1cclxuICAgIC8+XHJcbiAgKTtcclxufTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IEZhbGxiYWNrQ29tcG9uZW50O1xyXG4iXSwibmFtZXMiOlsiUmVzdWx0IiwianN4IiwiX2pzeCIsIkZhbGxiYWNrQ29tcG9uZW50Iiwic3RhdHVzIiwidGl0bGUiLCJzdWJUaXRsZSJdLCJzb3VyY2VSb290IjoiIn0=
//# sourceURL=webpack-internal:///65573
`)},64665:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return /* binding */ Visitor_Dashboard; }
});

// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/regeneratorRuntime.js
var regeneratorRuntime = __webpack_require__(15009);
var regeneratorRuntime_default = /*#__PURE__*/__webpack_require__.n(regeneratorRuntime);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(99289);
var asyncToGenerator_default = /*#__PURE__*/__webpack_require__.n(asyncToGenerator);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/slicedToArray.js
var slicedToArray = __webpack_require__(5574);
var slicedToArray_default = /*#__PURE__*/__webpack_require__.n(slicedToArray);
// EXTERNAL MODULE: ./src/components/FallbackContent/index.tsx
var FallbackContent = __webpack_require__(65573);
// EXTERNAL MODULE: ./src/services/visitor.ts
var visitor = __webpack_require__(98465);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/UsergroupAddOutlined.js + 1 modules
var UsergroupAddOutlined = __webpack_require__(42207);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/UserSwitchOutlined.js + 1 modules
var UserSwitchOutlined = __webpack_require__(80275);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/UserOutlined.js + 1 modules
var UserOutlined = __webpack_require__(87547);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/UserDeleteOutlined.js + 1 modules
var UserDeleteOutlined = __webpack_require__(20345);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/IdcardOutlined.js + 1 modules
var IdcardOutlined = __webpack_require__(75642);
// EXTERNAL MODULE: ./src/.umi-production/exports.ts + 34 modules
var _umi_production_exports = __webpack_require__(7837);
// EXTERNAL MODULE: ./node_modules/antd/es/typography/index.js + 18 modules
var typography = __webpack_require__(25514);
// EXTERNAL MODULE: ./node_modules/antd/es/tabs/index.js + 30 modules
var tabs = __webpack_require__(48096);
// EXTERNAL MODULE: ./node_modules/antd/es/row/index.js
var row = __webpack_require__(71230);
// EXTERNAL MODULE: ./node_modules/antd/es/col/index.js
var col = __webpack_require__(15746);
// EXTERNAL MODULE: ./node_modules/antd/es/card/index.js + 3 modules
var card = __webpack_require__(76216);
// EXTERNAL MODULE: ./node_modules/antd/es/statistic/index.js + 5 modules
var statistic = __webpack_require__(55054);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./node_modules/react-apexcharts/dist/react-apexcharts.min.js
var react_apexcharts_min = __webpack_require__(47229);
// EXTERNAL MODULE: ./node_modules/react/jsx-runtime.js
var jsx_runtime = __webpack_require__(85893);
;// CONCATENATED MODULE: ./src/pages/Visitor/Dashboard/Components/ColumnSingleChart.tsx




var createDataX = function createDataX(data) {
  var result = [];
  for (var i = 1; i <= data.length; i++) {
    result.push(i);
  }
  return result;
};
var ColumnSingleChart = function ColumnSingleChart(params) {
  var dataX = createDataX(params.dataIn);
  // console.log(params.dataIn);
  var _useState = (0,react.useState)({
      series: [{
        name: 'L\u01B0\u1EE3t',
        data: params.dataIn
      }],
      options: {
        chart: {
          height: 350,
          type: 'bar'
        },
        plotOptions: {
          bar: {
            borderRadius: 10,
            dataLabels: {
              position: 'top' // top, center, bottom
            }
          }
        },
        dataLabels: {
          enabled: true,
          formatter: function formatter(val) {
            return val + '';
          },
          offsetY: -20,
          style: {
            fontSize: '12px',
            colors: ['#304758']
          }
        },
        xaxis: {
          categories: dataX,
          position: 'top',
          axisBorder: {
            show: false
          },
          axisTicks: {
            show: false
          },
          crosshairs: {
            fill: {
              type: 'gradient',
              gradient: {
                colorFrom: '#D8E3F0',
                colorTo: '#BED1E6',
                stops: [0, 100],
                opacityFrom: 0.4,
                opacityTo: 0.5
              }
            }
          },
          tooltip: {
            enabled: true
          }
        },
        yaxis: {
          axisBorder: {
            show: false
          },
          axisTicks: {
            show: false
          },
          labels: {
            show: false,
            formatter: function formatter(val) {
              return val + '';
            }
          }
        }
      }
    } // object
    ),
    _useState2 = slicedToArray_default()(_useState, 2),
    state = _useState2[0],
    setState = _useState2[1]; // state

  (0,react.useEffect)(function () {
    setState({
      series: [{
        name: 'L\u01B0\u1EE3t',
        data: params.dataIn
      }],
      options: {
        chart: {
          height: 350,
          type: 'bar'
        },
        plotOptions: {
          bar: {
            borderRadius: 10,
            dataLabels: {
              position: 'top' // top, center, bottom
            }
          }
        },
        dataLabels: {
          enabled: true,
          formatter: function formatter(val) {
            return val + '';
          },
          offsetY: -20,
          style: {
            fontSize: '12px',
            colors: ['#304758']
          }
        },
        xaxis: {
          categories: dataX,
          position: 'top',
          axisBorder: {
            show: false
          },
          axisTicks: {
            show: false
          },
          crosshairs: {
            fill: {
              type: 'gradient',
              gradient: {
                colorFrom: '#D8E3F0',
                colorTo: '#BED1E6',
                stops: [0, 100],
                opacityFrom: 0.4,
                opacityTo: 0.5
              }
            }
          },
          tooltip: {
            enabled: true
          }
        },
        yaxis: {
          axisBorder: {
            show: false
          },
          axisTicks: {
            show: false
          },
          labels: {
            show: false,
            formatter: function formatter(val) {
              return val + '';
            }
          }
        }
        // title: {
        //   text: '',
        //   floating: true,
        //   offsetY: 330,
        //   align: 'center',
        //   style: {
        //     color: '#444',
        //   },
        // },
      }
    } // object
    ); // set State
  }, [params.dataIn]);
  return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
    id: params.id,
    children: /*#__PURE__*/(0,jsx_runtime.jsx)(react_apexcharts_min/* default */.Z, {
      options: state.options,
      series: state.series,
      type: "bar",
      height: 350
    })
  });
};
/* harmony default export */ var Components_ColumnSingleChart = (ColumnSingleChart);
// EXTERNAL MODULE: ./node_modules/dayjs/dayjs.min.js
var dayjs_min = __webpack_require__(27484);
var dayjs_min_default = /*#__PURE__*/__webpack_require__.n(dayjs_min);
;// CONCATENATED MODULE: ./src/pages/Visitor/Dashboard/Components/dataAnalysis.ts

var getSessionOfToDay = function getSessionOfToDay(data, time_key) {
  var today = dayjs_min_default()().startOf('day'); // B\u1EAFt \u0111\u1EA7u c\u1EE7a ng\xE0y hi\u1EC7n t\u1EA1i
  var tomorrow = dayjs_min_default()(today).endOf('day'); // K\u1EBFt th\xFAc c\u1EE7a ng\xE0y hi\u1EC7n t\u1EA1i

  var filteredData = data.filter(function (val) {
    var sessionTime = dayjs_min_default()(val[time_key], 'YYYY-MM-DD HH:mm:ss');
    var isSameOrAfterToday = sessionTime.isSameOrAfter(today, 'second');
    var isBeforeTomorrow = sessionTime.isBefore(tomorrow, 'second');
    return isSameOrAfterToday && isBeforeTomorrow;
  });
  return filteredData.length;
};
var getAllSession = function getAllSession(data) {
  return data.length;
};
var getUserExpired = function getUserExpired(data, key_time) {
  var nowDate = dayjs_min_default()(new Date().toISOString().substring(0, 10)).unix();
  var result = [];
  data.map(function (val) {
    var expiredDate = dayjs_min_default()(val[key_time]).unix();
    if (nowDate > expiredDate) {
      result.push(val);
    }
  });
  return result.length;
};
var getAllUser = function getAllUser(data) {
  return data.length;
};
var getSessionOfDay = function getSessionOfDay(data, day, time_key) {
  // YYYY-MM-DD
  var start_time = dayjs_min_default()("".concat(day, " 00:01:00")).unix();
  var end_time = dayjs_min_default()("".concat(day, " 23:59:00")).unix();
  var result = [];
  data.map(function (val) {
    var now = dayjs_min_default()(val[time_key]).unix();
    if (now >= start_time && now <= end_time) {
      result.push(val);
    }
  });
  return result.length;
};
var getSessionOfDayInThisMonth = function getSessionOfDayInThisMonth(data) {
  var now = dayjs_min_default()();
  var daysInMonth = now.daysInMonth();
  var year = now.year();
  var month = now.month() + 1; // Months are zero-based in Moment.js (January is 0)

  var result = [];
  var _loop = function _loop() {
    var currentDay = dayjs_min_default()("".concat(year, "-").concat(month.toString().padStart(2, '0'), "-").concat(i.toString().padStart(2, '0')));
    var sessionOfDay = data.filter(function (session) {
      return dayjs_min_default()(session.check_time).isSame(currentDay, 'day');
    }).length;
    result.push(sessionOfDay);
  };
  for (var i = 1; i <= daysInMonth; i++) {
    _loop();
  }
  console.log('result', result);
  return result;
};
var getSessionOfCustomMonth = function getSessionOfCustomMonth(data, customMonth) {
  var year = dayjs_min_default()().year(); // Current year
  var month = customMonth < 10 ? "0".concat(customMonth) : "".concat(customMonth);
  var daysInMonth = dayjs_min_default()("".concat(year, "-").concat(month), 'YYYY-MM').daysInMonth();
  var sum = 0;
  for (var i = 1; i <= daysInMonth; i++) {
    var currentDay = i < 10 ? "0".concat(i) : "".concat(i);
    var date = "".concat(year, "-").concat(month, "-").concat(currentDay);
    var sessionOfDay = getSessionOfDay(data, date, 'check_time');
    sum += sessionOfDay;
  }
  return sum;
};
var getSessionMonthOfThisYear = function getSessionMonthOfThisYear(data) {
  var result = [];
  for (var i = 1; i <= 12; i++) {
    var num = getSessionOfCustomMonth(data, i);
    result.push(num);
  }
  return result;
};
;// CONCATENATED MODULE: ./src/pages/Visitor/Dashboard/index.tsx













var Text = typography/* default */.Z.Text;
var TabPane = tabs/* default */.Z.TabPane;
var Dashboard = function Dashboard() {
  var _useState = (0,react.useState)(false),
    _useState2 = slicedToArray_default()(_useState, 2),
    loading = _useState2[0],
    setLoading = _useState2[1];
  var _useState3 = (0,react.useState)([]),
    _useState4 = slicedToArray_default()(_useState3, 2),
    dataChartMonth = _useState4[0],
    setDataChartMonth = _useState4[1];
  var _useState5 = (0,react.useState)([]),
    _useState6 = slicedToArray_default()(_useState5, 2),
    dataChartYear = _useState6[0],
    setDataChartYear = _useState6[1];
  var _useState7 = (0,react.useState)({
      alluser: 0,
      user: 0,
      card: 0,
      expired: 0,
      sessionDay: 0,
      sessionMonth: 0
    }),
    _useState8 = slicedToArray_default()(_useState7, 2),
    statisticsData = _useState8[0],
    setStatisticsData = _useState8[1];
  var fetchData = /*#__PURE__*/function () {
    var _ref = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
      var _yield$Promise$all, _yield$Promise$all2, sessionResult, userResult, cardResult, allSession, numSessionOfDay, dataOfDayInMonth, sumMonth, dataOfYear, allUser, numExpired;
      return regeneratorRuntime_default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _context.prev = 0;
            setLoading(true);
            _context.next = 4;
            return Promise.all([visitor/* visitorSessionService */.Zz.getListStatistic(), visitor/* visitorInforService */.tO.getList(), visitor/* visitorCardService */.N_.getList()]);
          case 4:
            _yield$Promise$all = _context.sent;
            _yield$Promise$all2 = slicedToArray_default()(_yield$Promise$all, 3);
            sessionResult = _yield$Promise$all2[0];
            userResult = _yield$Promise$all2[1];
            cardResult = _yield$Promise$all2[2];
            allSession = getAllSession(sessionResult.data);
            numSessionOfDay = getSessionOfToDay(sessionResult.data, 'check_time');
            dataOfDayInMonth = getSessionOfDayInThisMonth(sessionResult.data);
            sumMonth = dataOfDayInMonth.reduce(function (acc, val) {
              return acc + val;
            }, 0);
            dataOfYear = getSessionMonthOfThisYear(sessionResult.data);
            allUser = getAllUser(userResult.data);
            numExpired = getUserExpired(userResult.data, 'expired_day');
            setStatisticsData({
              alluser: allSession,
              sessionMonth: sumMonth,
              sessionDay: numSessionOfDay,
              user: allUser,
              expired: numExpired,
              card: cardResult.data.length
            });
            setDataChartMonth(dataOfDayInMonth);
            setDataChartYear(dataOfYear);
            _context.next = 24;
            break;
          case 21:
            _context.prev = 21;
            _context.t0 = _context["catch"](0);
            console.error(_context.t0);
          case 24:
            _context.prev = 24;
            setLoading(false);
            return _context.finish(24);
          case 27:
          case "end":
            return _context.stop();
        }
      }, _callee, null, [[0, 21, 24, 27]]);
    }));
    return function fetchData() {
      return _ref.apply(this, arguments);
    };
  }();
  (0,react.useEffect)(function () {
    fetchData();
  }, []);
  var access = (0,_umi_production_exports.useAccess)();
  var canRead = access.canAccessPageVisitorManagement();
  return /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.Access, {
    accessible: canRead,
    fallback: /*#__PURE__*/(0,jsx_runtime.jsx)(FallbackContent/* default */.Z, {}),
    children: /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
      gutter: [5, 5],
      children: [Object.entries(statisticsData).map(function (_ref2, index) {
        var _ref3 = slicedToArray_default()(_ref2, 2),
          title = _ref3[0],
          value = _ref3[1];
        return /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
          className: "gutter-row",
          md: 8,
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(card/* default */.Z, {
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(statistic/* default */.Z, {
              title: "S\\u1ED1 ".concat(getLabel(title)),
              value: value,
              loading: loading,
              prefix: getIcon(title),
              valueStyle: {
                color: getColor(title)
              }
            })
          })
        }, index);
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        className: "gutter-row",
        md: 24,
        children: /*#__PURE__*/(0,jsx_runtime.jsxs)(tabs/* default */.Z, {
          defaultActiveKey: "1",
          children: [/*#__PURE__*/(0,jsx_runtime.jsx)(TabPane, {
            tab: "Th\\xE1ng",
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(card/* default */.Z, {
              loading: loading,
              title: /*#__PURE__*/(0,jsx_runtime.jsx)(Text, {
                children: " L\\u01B0\\u1EE3t vi\\u1EBFng th\\u0103m trong th\\xE1ng"
              }),
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(Components_ColumnSingleChart, {
                dataIn: dataChartMonth,
                id: 'columnMonth'
              })
            })
          }, "1"), /*#__PURE__*/(0,jsx_runtime.jsx)(TabPane, {
            tab: "N\\u0103m",
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(card/* default */.Z, {
              loading: loading,
              title: /*#__PURE__*/(0,jsx_runtime.jsx)(Text, {
                children: " L\\u01B0\\u1EE3t vi\\u1EBFng th\\u0103m trong n\\u0103m"
              }),
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(Components_ColumnSingleChart, {
                dataIn: dataChartYear,
                id: 'columnYear'
              })
            })
          }, "2")]
        })
      })]
    })
  });
};
var getIcon = function getIcon(title) {
  switch (title) {
    case 'alluser':
      return /*#__PURE__*/(0,jsx_runtime.jsx)(UsergroupAddOutlined/* default */.Z, {});
    case 'sessionMonth':
    case 'sessionDay':
      return /*#__PURE__*/(0,jsx_runtime.jsx)(UserSwitchOutlined/* default */.Z, {});
    case 'user':
      return /*#__PURE__*/(0,jsx_runtime.jsx)(UserOutlined/* default */.Z, {});
    case 'expired':
      return /*#__PURE__*/(0,jsx_runtime.jsx)(UserDeleteOutlined/* default */.Z, {});
    case 'card':
      return /*#__PURE__*/(0,jsx_runtime.jsx)(IdcardOutlined/* default */.Z, {});
    default:
      return null;
  }
};
var getLabel = function getLabel(title) {
  switch (title) {
    case 'alluser':
      return 'l\u01B0\u1EE3t vi\u1EBFng th\u0103m';
    case 'sessionMonth':
      return 'l\u01B0\u1EE3t vi\u1EBFng th\u0103m th\xE1ng n\xE0y';
    case 'sessionDay':
      return 'l\u01B0\u1EE3t vi\u1EBFng th\u0103m h\xF4m nay';
    case 'user':
      return 't\u1ED5ng s\u1ED1 ng\u01B0\u1EDDi d\xF9ng';
    case 'expired':
      return 'ng\u01B0\u1EDDi d\xF9ng h\u1EBFt h\u1EA1n';
    case 'card':
      return 'l\u01B0\u1EE3ng th\u1EBB';
    default:
      return '';
  }
};
var getColor = function getColor(title) {
  switch (title) {
    case 'alluser':
      return '#f39c12';
    case 'sessionMonth':
    case 'sessionDay':
      return '#2980b9';
    case 'user':
      return '#16a085';
    case 'expired':
      return '#8e44ad';
    case 'card':
      return '#d35400';
    default:
      return '#000';
  }
};
/* harmony default export */ var Visitor_Dashboard = (Dashboard);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///64665
`)}}]);
