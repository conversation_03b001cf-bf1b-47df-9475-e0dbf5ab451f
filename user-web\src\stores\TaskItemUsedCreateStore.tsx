import { create } from 'zustand';

export type TaskItemUsed = {
  name?: string;
  quantity?: number;
  description?: string;
  iot_category_id: string;
  item_name?: string;
  label?: string;
  uom: string;
  uom_label: string;
  conversion_factor?: number;
  exp_quantity?: number;
  loss_quantity?: number;
  is_deleted?: boolean;
  draft_quantity?: number;
  ratio?: number;
  isFlattened?: boolean;
  parentId?: string;
  uoms: UOM[];
  bom?: BomItem[]; // Optional for items without a bom
  active_uom?: string; // Active UOM ID
  active_conversion_factor?: number; // Active conversion factor
};

interface BomItemAgg {
  name?: string;
  item_code: string;
  item_name: string;
  item_label: string;
  uom: string;
  uom_label: string;
  conversion_factor: number;
  uoms: UOM[];
  stock_qty: number;
  ratio: number;
  exp_quantity: number;
}

interface BomItem {
  bom_no: string;
  root_item_code: string;
  root_item_name: string;
  root_item_label: string;
  quantity: number;
  bom_items: BomItemAgg[];
}

interface UOM {
  uom: string;
  uom_label: string;
  conversion_factor: number;
}

type TaskItemUsedStore = {
  taskItemUsed: TaskItemUsed[];
  setTaskItemUsed: (state: TaskItemUsed[]) => void;
};

export const useTaskItemUsedCreateStore = create<TaskItemUsedStore>((set, get) => ({
  taskItemUsed: [],
  setTaskItemUsed: (taskItemUsed: TaskItemUsed[]) => {
    console.log('Setting taskItemUsed:', taskItemUsed); // Log the new state
    set({ taskItemUsed });
  },
}));
