import { create } from 'zustand';

export type TaskProduction = {
  name?: string;
  quantity?: number;
  description?: string;
  product_id: string;
  item_name?: string;
  label?: string;
  uom: string;
  uom_label: string;
  conversion_factor?: number;
  exp_quantity?: number;
  lost_quantity?: number;
  is_deleted?: boolean;
  draft_quantity?: number;
  ratio?: number;
  isFlattened?: boolean;
  parentId?: string;
  bom?: BomItem[]; // Optional for items without a bom
  uoms?: UOM[];
  active_uom?: string; // Active UOM ID
  active_conversion_factor?: number; // Active conversion factor
};
interface UOM {
  uom: string;
  uom_label: string;
  conversion_factor: number;
}
interface BomItemAgg {
  name?: string;
  item_code: string;
  item_name: string;
  item_label: string;
  uom: string;
  uom_label: string;
  conversion_factor: number;
  uoms: UOM[];
  stock_qty: number;
  ratio: number;
  exp_quantity: number;
}

interface BomItem {
  bom_no: string;
  root_item_code: string;
  root_item_name: string;
  root_item_label: string;
  quantity: number;
  bom_items: BomItemAgg[];
}

type TaskProductionStore = {
  taskProduction: TaskProduction[];
  setTaskProduction: (state: TaskProduction[]) => void;
};

export const useTaskProductionCreateStore = create<TaskProductionStore>((set, get) => ({
  taskProduction: [],
  setTaskProduction: (taskProduction: TaskProduction[]) => {
    console.log('Setting taskProduction:', taskProduction); // Log the new state
    set({ taskProduction });
  },
}));
