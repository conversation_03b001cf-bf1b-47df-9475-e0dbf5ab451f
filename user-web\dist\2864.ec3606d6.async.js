"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[2864],{97679:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(97857);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(19632);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(5574);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var _common_contanst_constanst__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(86604);
/* harmony import */ var _services_fileUpload__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(96876);
/* harmony import */ var _services_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(467);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(51042);
/* harmony import */ var _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(27068);
/* harmony import */ var _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(34994);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(98138);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(45360);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(78367);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(85576);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(67294);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(85893);















var getBase64 = function getBase64(file) {
  return new Promise(function (resolve, reject) {
    var reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = function () {
      return resolve(reader.result);
    };
    reader.onerror = function (error) {
      return reject(error);
    };
  });
};
var FormUploadsPreviewable = function FormUploadsPreviewable(_ref) {
  var formItemName = _ref.formItemName,
    fileLimit = _ref.fileLimit,
    label = _ref.label,
    initialImages = _ref.initialImages,
    docType = _ref.docType,
    isRequired = _ref.isRequired,
    hideUploadButton = _ref.hideUploadButton,
    hideDeleteIcon = _ref.hideDeleteIcon,
    hidePreviewIcon = _ref.hidePreviewIcon,
    _ref$uploadButtonLayo = _ref.uploadButtonLayout,
    uploadButtonLayout = _ref$uploadButtonLayo === void 0 ? 'vertical' : _ref$uploadButtonLayo,
    _ref$uploadButtonIcon = _ref.uploadButtonIcon,
    uploadButtonIcon = _ref$uploadButtonIcon === void 0 ? /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .Z, {}) : _ref$uploadButtonIcon,
    _ref$uploadButtonText = _ref.uploadButtonText,
    uploadButtonText = _ref$uploadButtonText === void 0 ? 'T\u1EA3i l\xEAn' : _ref$uploadButtonText,
    uploadButtonStyle = _ref.uploadButtonStyle,
    readOnly = _ref.readOnly;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(false),
    _useState2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_4___default()(_useState, 2),
    previewOpen = _useState2[0],
    setPreviewOpen = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(''),
    _useState4 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_4___default()(_useState3, 2),
    previewImage = _useState4[0],
    setPreviewImage = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(''),
    _useState6 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_4___default()(_useState5, 2),
    previewTitle = _useState6[0],
    setPreviewTitle = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(initialImages),
    _useState8 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_4___default()(_useState7, 2),
    imageList = _useState8[0],
    setImageList = _useState8[1];
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)([]),
    _useState10 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_4___default()(_useState9, 2),
    fileList = _useState10[0],
    setFileList = _useState10[1];
  var form = antd__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .Z.useFormInstance();
  (0,_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_12__/* .useDeepCompareEffect */ .KW)(function () {
    var listImg = (0,_services_utils__WEBPACK_IMPORTED_MODULE_7__/* .getListFileUrlFromStringV2 */ .JJ)({
      arrUrlString: initialImages
    }).map(function (url, index) {
      return {
        name: "\\u1EA2nh ".concat((index + 1).toString()),
        url: url || '',
        uid: (-index).toString(),
        status: url ? 'done' : 'error'
      };
    });
    setFileList(listImg);
  }, [initialImages]);
  var handlePreview = /*#__PURE__*/function () {
    var _ref2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee(file) {
      return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            if (!(!file.url && !file.preview)) {
              _context.next = 4;
              break;
            }
            _context.next = 3;
            return getBase64(file.originFileObj);
          case 3:
            file.preview = _context.sent;
          case 4:
            setPreviewImage(file.url || file.preview);
            setPreviewOpen(true);
            setPreviewTitle(file.name || file.url.substring(file.url.lastIndexOf('/') + 1));
          case 7:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }));
    return function handlePreview(_x) {
      return _ref2.apply(this, arguments);
    };
  }();
  var handleChange = /*#__PURE__*/function () {
    var _ref4 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee3(_ref3) {
      var newFileList, oldFileList, uploadListRes, checkUploadFailed, arrFileUrl;
      return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee3$(_context3) {
        while (1) switch (_context3.prev = _context3.next) {
          case 0:
            newFileList = _ref3.fileList;
            oldFileList = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_1___default()(fileList);
            if (!readOnly) {
              _context3.next = 4;
              break;
            }
            return _context3.abrupt("return");
          case 4:
            setFileList(newFileList);
            _context3.next = 7;
            return Promise.allSettled(newFileList.map( /*#__PURE__*/function () {
              var _ref5 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee2(item) {
                var _item$lastModified;
                return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee2$(_context2) {
                  while (1) switch (_context2.prev = _context2.next) {
                    case 0:
                      if (!item.url) {
                        _context2.next = 2;
                        break;
                      }
                      return _context2.abrupt("return", {
                        data: {
                          message: {
                            file_url: item.url.split('file_url=').at(-1)
                          }
                        }
                      });
                    case 2:
                      _context2.next = 4;
                      return (0,_services_fileUpload__WEBPACK_IMPORTED_MODULE_6__/* .uploadFile */ .cT)({
                        docType: docType || _common_contanst_constanst__WEBPACK_IMPORTED_MODULE_5__/* .DOCTYPE_ERP */ .lH.iotPlant,
                        docName: item.name + Math.random().toString(4) + ((_item$lastModified = item.lastModified) === null || _item$lastModified === void 0 ? void 0 : _item$lastModified.toString(4)),
                        file: item.originFileObj
                      });
                    case 4:
                      return _context2.abrupt("return", _context2.sent);
                    case 5:
                    case "end":
                      return _context2.stop();
                  }
                }, _callee2);
              }));
              return function (_x3) {
                return _ref5.apply(this, arguments);
              };
            }()));
          case 7:
            uploadListRes = _context3.sent;
            checkUploadFailed = uploadListRes.find(function (item) {
              return item.status === 'rejected';
            });
            if (checkUploadFailed) {
              antd__WEBPACK_IMPORTED_MODULE_13__/* ["default"] */ .ZP.error({
                content: "upload \\u1EA3nh kh\\xF4ng th\\xE0nh c\\xF4ng"
              });
              setFileList(oldFileList);
            }
            arrFileUrl = uploadListRes.reduce(function (prev, item) {
              var _item$value, _item$value2;
              return item.status === 'fulfilled' && item !== null && item !== void 0 && (_item$value = item.value) !== null && _item$value !== void 0 && (_item$value = _item$value.data) !== null && _item$value !== void 0 && (_item$value = _item$value.message) !== null && _item$value !== void 0 && _item$value.file_url ? [].concat(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_1___default()(prev), [item === null || item === void 0 || (_item$value2 = item.value) === null || _item$value2 === void 0 || (_item$value2 = _item$value2.data) === null || _item$value2 === void 0 || (_item$value2 = _item$value2.message) === null || _item$value2 === void 0 ? void 0 : _item$value2.file_url]) : prev;
            }, []).filter(function (item) {
              return typeof item === 'string';
            });
            if (arrFileUrl) {
              setImageList(arrFileUrl.join(','));
              form.setFieldValue(formItemName, arrFileUrl.join(','));
            }
          case 12:
          case "end":
            return _context3.stop();
        }
      }, _callee3);
    }));
    return function handleChange(_x2) {
      return _ref4.apply(this, arguments);
    };
  }();
  var uploadButton = /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxs)("div", {
    style: D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default()({
      display: 'flex',
      flexDirection: uploadButtonLayout === 'vertical' ? 'column' : 'row',
      alignItems: 'center',
      gap: uploadButtonLayout === 'vertical' ? '8px' : '4px'
    }, uploadButtonStyle),
    children: [uploadButtonIcon, /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)("div", {
      children: uploadButtonText
    })]
  });
  var handleCancel = function handleCancel() {
    return setPreviewOpen(false);
  };
  var normFile = function normFile(e) {
    if (Array.isArray(e)) {
      return e;
    }
    return e && e.fileList;
  };
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.Fragment, {
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_14__/* .ProForm */ .A.Item, {
      name: formItemName,
      initialValue: imageList,
      label: label,
      rules: D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_1___default()(isRequired ? [{
        required: isRequired
      }] : []),
      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(antd__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .Z, {
        listType: "picture-card",
        fileList: fileList,
        onPreview: handlePreview,
        maxCount: fileLimit,
        onChange: handleChange,
        multiple: true,
        showUploadList: {
          showRemoveIcon: !hideDeleteIcon,
          showPreviewIcon: !hidePreviewIcon
        },
        children: !hideUploadButton && (fileList.length >= fileLimit ? null : uploadButton)
      })
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(antd__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .Z, {
      open: previewOpen,
      title: previewTitle,
      footer: null,
      onCancel: handleCancel,
      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)("img", {
        alt: "example",
        style: {
          width: '100%'
        },
        src: previewImage
      })
    })]
  });
};
/* harmony default export */ __webpack_exports__.Z = (FormUploadsPreviewable);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///97679
`)},22864:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return /* binding */ Create; }
});

// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/objectSpread2.js
var objectSpread2 = __webpack_require__(97857);
var objectSpread2_default = /*#__PURE__*/__webpack_require__.n(objectSpread2);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/regeneratorRuntime.js
var regeneratorRuntime = __webpack_require__(15009);
var regeneratorRuntime_default = /*#__PURE__*/__webpack_require__.n(regeneratorRuntime);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(99289);
var asyncToGenerator_default = /*#__PURE__*/__webpack_require__.n(asyncToGenerator);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/slicedToArray.js
var slicedToArray = __webpack_require__(5574);
var slicedToArray_default = /*#__PURE__*/__webpack_require__.n(slicedToArray);
// EXTERNAL MODULE: ./src/common/contanst/constanst.ts
var constanst = __webpack_require__(86604);
// EXTERNAL MODULE: ./src/components/Task/TaskItemUsed/ItemUsedTableCreateView.tsx + 1 modules
var ItemUsedTableCreateView = __webpack_require__(60325);
// EXTERNAL MODULE: ./src/components/Task/TaskProductionNew/ProductionTableCreateView.tsx + 1 modules
var ProductionTableCreateView = __webpack_require__(1316);
// EXTERNAL MODULE: ./src/components/Task/TaskTodo/CreateTodoTableEditer.tsx + 2 modules
var CreateTodoTableEditer = __webpack_require__(84726);
// EXTERNAL MODULE: ./src/services/customerUser.ts
var customerUser = __webpack_require__(40063);
// EXTERNAL MODULE: ./src/services/farming-plan.ts
var farming_plan = __webpack_require__(74459);
// EXTERNAL MODULE: ./src/stores/TaskItemUsedCreateStore.tsx
var TaskItemUsedCreateStore = __webpack_require__(14682);
// EXTERNAL MODULE: ./src/stores/TaskProductionCreateStore.tsx
var TaskProductionCreateStore = __webpack_require__(55059);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/layouts/ProForm/index.js + 3 modules
var ProForm = __webpack_require__(34994);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-utils/es/hooks/useDeepCompareEffect/index.js
var useDeepCompareEffect = __webpack_require__(27068);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-layout/es/components/PageContainer/index.js + 18 modules
var PageContainer = __webpack_require__(6110);
// EXTERNAL MODULE: ./src/.umi-production/exports.ts + 34 modules
var _umi_production_exports = __webpack_require__(7837);
// EXTERNAL MODULE: ./node_modules/antd/es/app/index.js + 1 modules
var app = __webpack_require__(31418);
// EXTERNAL MODULE: ./node_modules/antd/es/space/index.js + 2 modules
var space = __webpack_require__(42075);
// EXTERNAL MODULE: ./node_modules/antd/es/button/index.js + 9 modules
var es_button = __webpack_require__(14726);
// EXTERNAL MODULE: ./node_modules/antd/es/modal/index.js + 1 modules
var modal = __webpack_require__(85576);
// EXTERNAL MODULE: ./node_modules/dayjs/dayjs.min.js
var dayjs_min = __webpack_require__(27484);
var dayjs_min_default = /*#__PURE__*/__webpack_require__.n(dayjs_min);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./src/components/FormUploadsPreviewable/index.tsx
var FormUploadsPreviewable = __webpack_require__(97679);
// EXTERNAL MODULE: ./src/services/cropManager.ts
var cropManager = __webpack_require__(77890);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/Select/index.js
var Select = __webpack_require__(64317);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/Checkbox/index.js
var Checkbox = __webpack_require__(63434);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/Text/index.js
var Text = __webpack_require__(5966);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/DateRangePicker/index.js
var DateRangePicker = __webpack_require__(34540);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/TextArea/index.js
var TextArea = __webpack_require__(90672);
// EXTERNAL MODULE: ./node_modules/antd/es/message/index.js
var message = __webpack_require__(45360);
// EXTERNAL MODULE: ./node_modules/antd/es/spin/index.js + 2 modules
var spin = __webpack_require__(75081);
// EXTERNAL MODULE: ./node_modules/antd/es/row/index.js
var row = __webpack_require__(71230);
// EXTERNAL MODULE: ./node_modules/antd/es/col/index.js
var col = __webpack_require__(15746);
// EXTERNAL MODULE: ./node_modules/antd/es/card/index.js + 3 modules
var card = __webpack_require__(76216);
// EXTERNAL MODULE: ./node_modules/antd/es/form/index.js + 18 modules
var es_form = __webpack_require__(98138);
// EXTERNAL MODULE: ./node_modules/antd/es/date-picker/index.js + 78 modules
var date_picker = __webpack_require__(47676);
// EXTERNAL MODULE: ./node_modules/antd/es/checkbox/index.js + 3 modules
var es_checkbox = __webpack_require__(84567);
// EXTERNAL MODULE: ./node_modules/antd/es/input-number/index.js + 16 modules
var input_number = __webpack_require__(9735);
// EXTERNAL MODULE: ./node_modules/lodash/fp.js
var fp = __webpack_require__(78230);
// EXTERNAL MODULE: ./src/pages/FarmingManagement/WorkflowManagement/TagManager/ProFormTagSelect.tsx + 2 modules
var ProFormTagSelect = __webpack_require__(97035);
// EXTERNAL MODULE: ./node_modules/react/jsx-runtime.js
var jsx_runtime = __webpack_require__(85893);
;// CONCATENATED MODULE: ./src/pages/FarmingManagement/WorkflowManagement/Create/DetailedInfo.tsx



















var PAGE_SIZE = 20;
var DetailedInfo = function DetailedInfo(_ref) {
  var children = _ref.children,
    onEditTagSuccess = _ref.onEditTagSuccess,
    currentPlanParam = _ref.currentPlanParam,
    _ref$onFileListChange = _ref.onFileListChange,
    onFileListChange = _ref$onFileListChange === void 0 ? function () {} : _ref$onFileListChange,
    setTodoList = _ref.setTodoList,
    setTaskItems = _ref.setTaskItems,
    setWorkTimes = _ref.setWorkTimes,
    setProductions = _ref.setProductions,
    _ref$isTemplateTask = _ref.isTemplateTask,
    isTemplateTask = _ref$isTemplateTask === void 0 ? false : _ref$isTemplateTask,
    _ref$openFromModal = _ref.openFromModal,
    openFromModal = _ref$openFromModal === void 0 ? false : _ref$openFromModal;
  var _useState = (0,react.useState)(false),
    _useState2 = slicedToArray_default()(_useState, 2),
    isInterval = _useState2[0],
    setIsInterval = _useState2[1];
  var _useState3 = (0,react.useState)({}),
    _useState4 = slicedToArray_default()(_useState3, 2),
    currentPlan = _useState4[0],
    setCurrentPlan = _useState4[1];
  var _useState5 = (0,react.useState)(''),
    _useState6 = slicedToArray_default()(_useState5, 2),
    selectedPlan = _useState6[0],
    setSelectedPlan = _useState6[1];
  var _useState7 = (0,react.useState)([]),
    _useState8 = slicedToArray_default()(_useState7, 2),
    planStateOptions = _useState8[0],
    setPlanStateOptions = _useState8[1];
  var _useState9 = (0,react.useState)(false),
    _useState10 = slicedToArray_default()(_useState9, 2),
    loading = _useState10[0],
    setLoading = _useState10[1];
  var _useState11 = (0,react.useState)([]),
    _useState12 = slicedToArray_default()(_useState11, 2),
    fileList = _useState12[0],
    setFileList = _useState12[1];
  var _useState13 = (0,react.useState)(1),
    _useState14 = slicedToArray_default()(_useState13, 2),
    page = _useState14[0],
    setPage = _useState14[1];
  var _useState15 = (0,react.useState)(0),
    _useState16 = slicedToArray_default()(_useState15, 2),
    total = _useState16[0],
    setTotal = _useState16[1];
  var _useState17 = (0,react.useState)([]),
    _useState18 = slicedToArray_default()(_useState17, 2),
    taskOptions = _useState18[0],
    setTaskOptions = _useState18[1];
  var form = ProForm/* ProForm */.A.useFormInstance();
  var cropId = ProForm/* ProForm */.A.useWatch('crop', form);
  var _useState19 = (0,react.useState)(isTemplateTask),
    _useState20 = slicedToArray_default()(_useState19, 2),
    isTemplate = _useState20[0],
    setIsTemplate = _useState20[1];
  var _useState21 = (0,react.useState)([]),
    _useState22 = slicedToArray_default()(_useState21, 2),
    cropList = _useState22[0],
    setCropList = _useState22[1];
  var handleTaskSelect = /*#__PURE__*/function () {
    var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(taskId) {
      var filters, res, task, currentValues, todoList, taskItems, productions;
      return regeneratorRuntime_default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            setLoading(true);
            _context.prev = 1;
            filters = [['iot_farming_plan_task', 'name', 'like', taskId]];
            _context.next = 5;
            return (0,farming_plan/* getTemplateTaskManagerList */.dQ)({
              filters: filters,
              page: 1,
              size: 1
            });
          case 5:
            res = _context.sent;
            task = res.data[0];
            currentValues = form.getFieldsValue(); // Get current form values
            form.setFieldsValue(objectSpread2_default()(objectSpread2_default()({}, currentValues), {}, {
              // Retain existing fields
              // crop: task.crop_id,
              label: task.label,
              status: task.status,
              // farming_plan_state: task.farming_plan_state,
              description: task.description,
              assigned_to: task.assigned_to,
              involved_in_users: task.involve_in_users ? task.involve_in_users.map(function (item) {
                return {
                  label: item.first_name || item.last_name ? "".concat(item.first_name || '', " ").concat(item.last_name || '') : "".concat(item.email),
                  customer_user: item.customer_user,
                  value: item.name
                };
              }) : [],
              tag: task.tag
            }));
            todoList = task.todo_list ? task.todo_list.map(function (item) {
              return {
                name: item.name,
                label: item.label,
                status: item.status,
                description: item.description,
                is_completed: 0,
                customer_user_id: item.customer_user_id,
                customer_user_name: item.customer_user_name
              };
            }) : [];
            setTodoList(todoList);
            taskItems = task.item_list ? task.item_list.map(function (item) {
              return {
                iot_category_id: item.iot_category_id,
                item_name: item.item_name,
                label: item.label,
                uom_name: item.uom_name,
                description: item.description,
                exp_quantity: item.exp_quantity
              };
            }) : [];
            setTaskItems(taskItems);
            productions = task.prod_quantity_list ? task.prod_quantity_list.map(function (item) {
              return {
                product_id: item.product_id,
                label: item.label,
                item_name: item.item_name,
                uom_name: item.uom_name,
                description: item.description,
                exp_quantity: item.exp_quantity
              };
            }) : [];
            setProductions(productions);
            _context.next = 21;
            break;
          case 17:
            _context.prev = 17;
            _context.t0 = _context["catch"](1);
            console.log(_context.t0);
            message/* default */.ZP.error('Failed to fetch task details.');
          case 21:
            _context.prev = 21;
            setLoading(false);
            return _context.finish(21);
          case 24:
          case "end":
            return _context.stop();
        }
      }, _callee, null, [[1, 17, 21, 24]]);
    }));
    return function handleTaskSelect(_x) {
      return _ref2.apply(this, arguments);
    };
  }();
  var _useModel = (0,_umi_production_exports.useModel)("@@initialState"),
    initialState = _useModel.initialState;
  var currentUser = initialState === null || initialState === void 0 ? void 0 : initialState.currentUser;
  (0,react.useEffect)(function () {
    onFileListChange(fileList);
  }, [fileList]);
  (0,react.useEffect)(function () {
    form.setFieldValue('farming_plan', currentPlanParam);
    setSelectedPlan(currentPlanParam.name);
    form.setFieldValue('is_template', isTemplateTask);
    setIsTemplate(isTemplateTask);
  }, [currentPlanParam]);
  (0,react.useEffect)(function () {
    form.setFieldValue('assigned_to', currentUser === null || currentUser === void 0 ? void 0 : currentUser.user_id);
  }, [currentUser]);
  (0,react.useEffect)(function () {
    var fetchData = /*#__PURE__*/function () {
      var _ref3 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2() {
        var res, today, todayState, _todayState$data, _res$data$at;
        return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
          while (1) switch (_context2.prev = _context2.next) {
            case 0:
              _context2.prev = 0;
              if (selectedPlan) {
                _context2.next = 3;
                break;
              }
              return _context2.abrupt("return");
            case 3:
              _context2.next = 5;
              return (0,farming_plan/* getFarmingPlanState */.jY)({
                page: 1,
                size: constanst/* DEFAULT_PAGE_SIZE_ALL */.YY,
                filters: [['iot_farming_plan_state', 'farming_plan', 'like', selectedPlan]]
              });
            case 5:
              res = _context2.sent;
              setPlanStateOptions(res.data.map(function (item) {
                return {
                  label: item.label,
                  value: item.name
                };
              }));
              today = new Date();
              _context2.next = 10;
              return (0,farming_plan/* getFarmingPlanState */.jY)({
                page: 1,
                size: constanst/* DEFAULT_PAGE_SIZE_ALL */.YY,
                filters: [['iot_farming_plan_state', 'farming_plan', 'like', selectedPlan], ['iot_farming_plan_state', 'start_date', '<=', today], ['iot_farming_plan_state', 'end_date', '>=', today]]
              });
            case 10:
              todayState = _context2.sent;
              if (todayState.data.length !== 0) {
                form.setFieldValue('farming_plan_state', todayState === null || todayState === void 0 || (_todayState$data = todayState.data) === null || _todayState$data === void 0 || (_todayState$data = _todayState$data.at(0)) === null || _todayState$data === void 0 ? void 0 : _todayState$data.name);
                form.setFieldValue('start_date', dayjs_min_default()(today.toISOString()));
              } else {
                form.setFieldValue('farming_plan_state', (_res$data$at = res.data.at(0)) === null || _res$data$at === void 0 ? void 0 : _res$data$at.name);
              }
              _context2.next = 17;
              break;
            case 14:
              _context2.prev = 14;
              _context2.t0 = _context2["catch"](0);
              message/* default */.ZP.error(_context2.t0.toString());
            case 17:
            case "end":
              return _context2.stop();
          }
        }, _callee2, null, [[0, 14]]);
      }));
      return function fetchData() {
        return _ref3.apply(this, arguments);
      };
    }();
    fetchData();
    if (currentPlanParam) {
      setCurrentPlan(currentPlanParam);
    }
  }, [selectedPlan]);
  var _useRequest = (0,_umi_production_exports.useRequest)(function (_ref4) {
      var cropId = _ref4.cropId;
      return (0,farming_plan/* getFarmingPlanList */.Qo)({
        page: 1,
        size: 1,
        filters: [[constanst/* DOCTYPE_ERP */.lH.iotFarmingPlan, 'crop', '=', cropId]]
      });
    }, {
      manual: true
    }),
    loadingFarmingPlan = _useRequest.loading,
    getFarmingPlanByCrop = _useRequest.run,
    data = _useRequest.data;
  var isDisableSelectCrop = (0,react.useMemo)(function () {
    return form.getFieldValue('crop');
  }, []);
  var handleChangeCrop = /*#__PURE__*/function () {
    var _ref5 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee3(v) {
      var _res$;
      var res, farmingPlan;
      return regeneratorRuntime_default()().wrap(function _callee3$(_context3) {
        while (1) switch (_context3.prev = _context3.next) {
          case 0:
            _context3.next = 2;
            return getFarmingPlanByCrop({
              cropId: v
            });
          case 2:
            res = _context3.sent;
            farmingPlan = res === null || res === void 0 || (_res$ = res[0]) === null || _res$ === void 0 ? void 0 : _res$.name;
            form.setFieldValue('farming_plan', farmingPlan);
            setSelectedPlan(farmingPlan);
          case 6:
          case "end":
            return _context3.stop();
        }
      }, _callee3);
    }));
    return function handleChangeCrop(_x2) {
      return _ref5.apply(this, arguments);
    };
  }();
  (0,react.useEffect)(function () {
    handleChangeCrop(cropId);
  }, [cropId]);
  var intl = (0,_umi_production_exports.useIntl)();
  (0,react.useEffect)(function () {
    var fetchCropList = /*#__PURE__*/function () {
      var _ref6 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee4() {
        var res;
        return regeneratorRuntime_default()().wrap(function _callee4$(_context4) {
          while (1) switch (_context4.prev = _context4.next) {
            case 0:
              if (!isTemplate) {
                _context4.next = 6;
                break;
              }
              _context4.next = 3;
              return (0,cropManager/* getTemplateCropList */.LY)({
                page: 1,
                size: constanst/* DEFAULT_PAGE_SIZE_ALL */.YY
              });
            case 3:
              _context4.t0 = _context4.sent;
              _context4.next = 9;
              break;
            case 6:
              _context4.next = 8;
              return (0,cropManager/* getCropList */.TQ)({
                page: 1,
                size: constanst/* DEFAULT_PAGE_SIZE_ALL */.YY
              });
            case 8:
              _context4.t0 = _context4.sent;
            case 9:
              res = _context4.t0;
              // form.setFieldValue(
              //   'crop',
              //   uniqBy('name', res.data).map((item: any) => ({
              //     label: item.label,
              //     value: item.name,
              //   })),
              // );
              setCropList((0,fp.uniqBy)('name', res.data).map(function (item) {
                return {
                  label: item.label,
                  value: item.name
                };
              }));
            case 11:
            case "end":
              return _context4.stop();
          }
        }, _callee4);
      }));
      return function fetchCropList() {
        return _ref6.apply(this, arguments);
      };
    }();
    fetchCropList();
  }, [isTemplate]);
  return /*#__PURE__*/(0,jsx_runtime.jsx)(spin/* default */.Z, {
    spinning: loading,
    children: /*#__PURE__*/(0,jsx_runtime.jsx)(row/* default */.Z, {
      gutter: [5, 5],
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        md: 24,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(card/* default */.Z, {
          title: intl.formatMessage({
            id: 'common.detail'
          }),
          children: /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
            gutter: [5, 5],
            children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
              className: "gutter-row",
              span: 12,
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(FormUploadsPreviewable/* default */.Z, {
                fileLimit: 20,
                label: intl.formatMessage({
                  id: 'common.image'
                }),
                formItemName: 'upload-image'
              })
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
              className: "gutter-row",
              span: 12,
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
                name: "copy_from_task",
                label: intl.formatMessage({
                  id: 'common.copy_from_task'
                }),
                showSearch: true,
                request: ( /*#__PURE__*/function () {
                  var _ref7 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee5(searchKeys) {
                    var filters, res;
                    return regeneratorRuntime_default()().wrap(function _callee5$(_context5) {
                      while (1) switch (_context5.prev = _context5.next) {
                        case 0:
                          filters = searchKeys.keyWords ? [['iot_farming_plan_task', 'label', 'like', searchKeys.keyWords]] : undefined;
                          _context5.next = 3;
                          return (0,farming_plan/* getTemplateTaskManagerList */.dQ)({
                            filters: filters,
                            page: page,
                            size: PAGE_SIZE
                          });
                        case 3:
                          res = _context5.sent;
                          return _context5.abrupt("return", res.data.map(function (item) {
                            return {
                              label: item.label,
                              value: item.name,
                              cropName: item.crop_name,
                              stateName: item.state_name
                            };
                          }));
                        case 5:
                        case "end":
                          return _context5.stop();
                      }
                    }, _callee5);
                  }));
                  return function (_x3) {
                    return _ref7.apply(this, arguments);
                  };
                }()),
                onChange: handleTaskSelect,
                fieldProps: {
                  options: taskOptions,
                  optionLabelProp: 'label',
                  optionRender: function optionRender(option) {
                    return /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
                      children: [/*#__PURE__*/(0,jsx_runtime.jsx)("div", {
                        children: option.label
                      }), /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
                        style: {
                          fontSize: '12px',
                          color: '#888'
                        },
                        children: "VM: ".concat(option.data.cropName, " - G\\u0110: ").concat(option.data.stateName)
                      })]
                    });
                  }
                }
              })
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
              span: 24,
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(Checkbox/* default */.Z, {
                name: "is_template",
                label: intl.formatMessage({
                  id: 'common.template_task'
                }),
                fieldProps: {
                  onChange: function onChange(event) {
                    setIsTemplate(event.target.checked);
                  }
                },
                disabled: openFromModal
              })
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
              span: 12,
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(Text/* default */.Z, {
                label: intl.formatMessage({
                  id: 'common.name'
                }),
                rules: [{
                  required: true
                }],
                name: "label"
              })
            }), /*#__PURE__*/(0,jsx_runtime.jsxs)(col/* default */.Z, {
              span: 12,
              children: [/*#__PURE__*/(0,jsx_runtime.jsx)(Text/* default */.Z, {
                hidden: true,
                name: 'farming_plan'
              }), /*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
                name: "crop",
                label: intl.formatMessage({
                  id: isTemplate ? 'common.template-crop' : 'common.crop'
                }),
                onChange: handleChangeCrop,
                disabled: isDisableSelectCrop,
                showSearch: true
                // request={async () => {
                //   return form.getFieldValue('crop');
                // }}
                ,
                options: cropList,
                rules: [{
                  required: true
                }]
              })]
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
              span: 12,
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
                label: intl.formatMessage({
                  id: 'common.state'
                }),
                name: "farming_plan_state",
                options: planStateOptions,
                disabled: planStateOptions.length === 0,
                fieldProps: {
                  loading: loadingFarmingPlan
                },
                rules: [{
                  required: true
                }]
              })
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
              span: 12,
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(ProFormTagSelect/* default */.Z, {
                onEditTagSuccess: onEditTagSuccess
              })
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
              span: 6,
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_form/* default */.Z.Item, {
                label: intl.formatMessage({
                  id: 'common.start_date'
                }),
                rules: [{
                  required: true
                }],
                name: "start_date",
                children: /*#__PURE__*/(0,jsx_runtime.jsx)(date_picker["default"], {
                  style: {
                    width: '100%'
                  },
                  showTime: true,
                  format: 'HH:mm DD/MM/YYYY'
                })
              })
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
              span: 6,
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_form/* default */.Z.Item, {
                label: intl.formatMessage({
                  id: 'common.end_date'
                }),
                rules: [{
                  required: true
                }],
                name: "end_date",
                children: /*#__PURE__*/(0,jsx_runtime.jsx)(date_picker["default"], {
                  style: {
                    width: '100%'
                  },
                  showTime: true,
                  format: 'HH:mm DD/MM/YYYY'
                })
              })
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
              span: 12,
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
                label: intl.formatMessage({
                  id: 'common.assigned_to'
                }),
                name: "assigned_to",
                request: /*#__PURE__*/asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee6() {
                  var res;
                  return regeneratorRuntime_default()().wrap(function _callee6$(_context6) {
                    while (1) switch (_context6.prev = _context6.next) {
                      case 0:
                        _context6.next = 2;
                        return (0,customerUser/* getCustomerUserList */.J9)({
                          page: 1,
                          size: constanst/* DEFAULT_PAGE_SIZE_ALL */.YY,
                          fields: ['name', 'first_name', 'last_name', 'email']
                        });
                      case 2:
                        res = _context6.sent;
                        return _context6.abrupt("return", res.data.map(function (item) {
                          return {
                            label: item.first_name || item.last_name ? "".concat(item.first_name || '', " ").concat(item.last_name || '') : "".concat(item.email),
                            value: item.name
                          };
                        }));
                      case 4:
                      case "end":
                        return _context6.stop();
                    }
                  }, _callee6);
                }))
              })
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
              span: 12,
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
                label: intl.formatMessage({
                  id: 'common.related_members'
                }),
                name: "involved_in_users",
                request: /*#__PURE__*/asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee7() {
                  var res;
                  return regeneratorRuntime_default()().wrap(function _callee7$(_context7) {
                    while (1) switch (_context7.prev = _context7.next) {
                      case 0:
                        _context7.next = 2;
                        return (0,customerUser/* getCustomerUserList */.J9)({
                          page: 1,
                          size: constanst/* DEFAULT_PAGE_SIZE_ALL */.YY,
                          fields: ['name', 'first_name', 'last_name', 'email']
                        });
                      case 2:
                        res = _context7.sent;
                        return _context7.abrupt("return", res.data.map(function (item) {
                          return {
                            label: item.first_name || item.last_name ? "".concat(item.first_name || '', " ").concat(item.last_name || '') : "".concat(item.email),
                            value: item.name
                          };
                        }));
                      case 4:
                      case "end":
                        return _context7.stop();
                    }
                  }, _callee7);
                })),
                mode: "multiple"
              })
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
              span: 12,
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
                rules: [{
                  required: true
                }],
                label: intl.formatMessage({
                  id: 'common.status'
                }),
                name: "status",
                options: [{
                  label: 'L\xEAn k\u1EBF ho\u1EA1ch',
                  value: 'Plan'
                }, {
                  label: '\u0110ang x\u1EED l\xFD',
                  value: 'In progress'
                }, {
                  label: 'Ho\xE0n t\u1EA5t',
                  value: 'Done'
                }, {
                  label: 'Tr\xEC ho\xE3n',
                  value: 'Pending'
                }],
                initialValue: 'Plan'
              })
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
              span: 24,
              children: /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
                gutter: 16,
                children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
                  span: 6,
                  children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_form/* default */.Z.Item, {
                    label: intl.formatMessage({
                      id: 'common.repeat_task'
                    }),
                    name: "is_interval",
                    valuePropName: "checked",
                    children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_checkbox/* default */.Z, {
                      value: isInterval,
                      onChange: function onChange(v) {
                        setIsInterval(v.target.checked);
                      }
                    })
                  })
                }), isInterval && /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
                  children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
                    span: 4,
                    children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_form/* default */.Z.Item, {
                      label: intl.formatMessage({
                        id: 'common.each'
                      }),
                      name: "interval_value",
                      initialValue: 1,
                      rules: [{
                        required: true
                      }],
                      children: /*#__PURE__*/(0,jsx_runtime.jsx)(input_number/* default */.Z, {
                        style: {
                          width: '100%'
                        },
                        min: 1
                      })
                    })
                  }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
                    span: 6,
                    children: /*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
                      style: {
                        width: '100%'
                      },
                      name: "interval_type",
                      label: intl.formatMessage({
                        id: 'common.time_type'
                      }),
                      options: [{
                        value: 'd',
                        label: 'Ng\xE0y'
                      }, {
                        value: 'w',
                        label: 'Tu\u1EA7n'
                      }, {
                        value: 'M',
                        label: 'Th\xE1ng'
                      }],
                      rules: [{
                        required: true
                      }]
                    })
                  }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
                    span: 8,
                    children: /*#__PURE__*/(0,jsx_runtime.jsx)(DateRangePicker/* default */.Z, {
                      style: {
                        width: '100%'
                      },
                      label: intl.formatMessage({
                        id: 'common.interval_range'
                      }),
                      width: 'lg',
                      rules: [{
                        required: true
                      }],
                      fieldProps: {
                        format: constanst/* DEFAULT_DATE_FORMAT_WITHOUT_TIME */.ug
                      },
                      name: "intervalRange"
                    })
                  })]
                })]
              })
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
              md: 24,
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(TextArea/* default */.Z, {
                label: intl.formatMessage({
                  id: 'common.note'
                }),
                name: "description"
              })
            })]
          })
        })
      })
    })
  });
};
/* harmony default export */ var Create_DetailedInfo = (DetailedInfo);
;// CONCATENATED MODULE: ./src/pages/FarmingManagement/WorkflowManagement/Create/index.tsx





















var CreateWorkflow = function CreateWorkflow(_ref) {
  var _ref$mode = _ref.mode,
    mode = _ref$mode === void 0 ? 'normal' : _ref$mode,
    onCreateSuccess = _ref.onCreateSuccess,
    open = _ref.open,
    onOpenChange = _ref.onOpenChange,
    farmingPlanStateId = _ref.farmingPlanStateId,
    planId = _ref.planId,
    defaultValue = _ref.defaultValue,
    cropId = _ref.cropId,
    isTemplateTask = _ref.isTemplateTask;
  var _useState = (0,react.useState)([]),
    _useState2 = slicedToArray_default()(_useState, 2),
    todoList = _useState2[0],
    setTodoList = _useState2[1];
  // const [taskItems, setTaskItems] = useState<any>([]);
  // const [productions, setProductions] = useState<any>([]);

  var _useTaskItemUsedCreat = (0,TaskItemUsedCreateStore/* useTaskItemUsedCreateStore */.W)(),
    taskItems = _useTaskItemUsedCreat.taskItemUsed,
    setTaskItems = _useTaskItemUsedCreat.setTaskItemUsed;
  var _useTaskProductionCre = (0,TaskProductionCreateStore/* useTaskProductionCreateStore */.N)(),
    productions = _useTaskProductionCre.taskProduction,
    setProductions = _useTaskProductionCre.setTaskProduction;
  var _useState3 = (0,react.useState)([]),
    _useState4 = slicedToArray_default()(_useState3, 2),
    workTimes = _useState4[0],
    setWorkTimes = _useState4[1];
  var _useState5 = (0,react.useState)(false),
    _useState6 = slicedToArray_default()(_useState5, 2),
    loading = _useState6[0],
    setLoading = _useState6[1];
  var _useState7 = (0,react.useState)([]),
    _useState8 = slicedToArray_default()(_useState7, 2),
    customerUserOptions = _useState8[0],
    setCustomerUserOptions = _useState8[1];
  var _useState9 = (0,react.useState)([]),
    _useState10 = slicedToArray_default()(_useState9, 2),
    fileList = _useState10[0],
    setFileList = _useState10[1];
  var _useState11 = (0,react.useState)({}),
    _useState12 = slicedToArray_default()(_useState11, 2),
    currentPlan = _useState12[0],
    setCurrentPlan = _useState12[1];

  //set taskItems and productions when taskItemUsed and taskProduction change
  // useEffect(() => {
  //   setTaskItems(taskItemUsed);
  //   setProductions(taskProduction);
  // }, [taskItemUsed, taskProduction]);

  var onFileListChange = function onFileListChange(fileList) {
    setFileList(fileList);
  };
  var getCustomerUser = /*#__PURE__*/function () {
    var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
      var _result$data, result;
      return regeneratorRuntime_default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _context.prev = 0;
            setLoading(true);
            //call api
            _context.next = 4;
            return (0,customerUser/* customerUserListAll */.jt)();
          case 4:
            result = _context.sent;
            console.log('result', result);
            setCustomerUserOptions(result === null || result === void 0 || (_result$data = result.data) === null || _result$data === void 0 ? void 0 : _result$data.map(function (d) {
              return {
                value: d.name,
                label: "".concat(d.full_name, " ").concat(d.email)
              };
            }));
            _context.next = 11;
            break;
          case 9:
            _context.prev = 9;
            _context.t0 = _context["catch"](0);
          case 11:
            _context.prev = 11;
            setLoading(false);
            return _context.finish(11);
          case 14:
          case "end":
            return _context.stop();
        }
      }, _callee, null, [[0, 9, 11, 14]]);
    }));
    return function getCustomerUser() {
      return _ref2.apply(this, arguments);
    };
  }();
  var getCurrentFarmingPlan = /*#__PURE__*/function () {
    var _ref3 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2() {
      var _farmingPlan, filters, farmingPlanState, farmingPlanId, farmingPlan;
      return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            if (!planId) {
              _context2.next = 6;
              break;
            }
            _context2.next = 3;
            return (0,farming_plan/* getFarmingPlan */.j1)(planId);
          case 3:
            _farmingPlan = _context2.sent;
            setCurrentPlan(_farmingPlan.data);
            return _context2.abrupt("return");
          case 6:
            if (farmingPlanStateId) {
              _context2.next = 8;
              break;
            }
            return _context2.abrupt("return");
          case 8:
            filters = [[constanst/* DOCTYPE_ERP */.lH.iotFarmingPlanState, 'name', 'like', farmingPlanStateId]];
            console.log('filters', filters);
            _context2.next = 12;
            return (0,farming_plan/* getFarmingPlanState */.jY)({
              filters: filters
            });
          case 12:
            farmingPlanState = _context2.sent;
            console.log('farming plan state is', farmingPlanState);
            farmingPlanId = farmingPlanState.data[0].farming_plan;
            _context2.next = 17;
            return (0,farming_plan/* getFarmingPlan */.j1)(farmingPlanId);
          case 17:
            farmingPlan = _context2.sent;
            setCurrentPlan(farmingPlan.data);
          case 19:
          case "end":
            return _context2.stop();
        }
      }, _callee2);
    }));
    return function getCurrentFarmingPlan() {
      return _ref3.apply(this, arguments);
    };
  }();
  (0,react.useEffect)(function () {
    getCustomerUser();
    getCurrentFarmingPlan();
  }, []);
  var _App$useApp = app/* default */.Z.useApp(),
    message = _App$useApp.message;
  var _useState13 = (0,react.useState)(false),
    _useState14 = slicedToArray_default()(_useState13, 2),
    submitting = _useState14[0],
    setSubmitting = _useState14[1];
  var _ProForm$useForm = ProForm/* ProForm */.A.useForm(),
    _ProForm$useForm2 = slicedToArray_default()(_ProForm$useForm, 1),
    form = _ProForm$useForm2[0];
  // const formRef = useRef<ProFormInstance<any>>();
  var onFinish = /*#__PURE__*/function () {
    var _ref4 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee3(values) {
      var imagePath, is_interval, interval_type, interval_value, intervalRange, start_date, end_date, enable_origin_tracing, involved_in_users, requestArr, start_check, counter, task;
      return regeneratorRuntime_default()().wrap(function _callee3$(_context3) {
        while (1) switch (_context3.prev = _context3.next) {
          case 0:
            console.log('start on finish');
            setSubmitting(true);
            imagePath = (values === null || values === void 0 ? void 0 : values['upload-image']) || '';
            _context3.prev = 3;
            is_interval = values.is_interval, interval_type = values.interval_type, interval_value = values.interval_value, intervalRange = values.intervalRange, start_date = values.start_date, end_date = values.end_date, enable_origin_tracing = values.enable_origin_tracing, involved_in_users = values.involved_in_users;
            requestArr = []; // Ensure intervalRange is defined and valid before mapping
            if (intervalRange && Array.isArray(intervalRange)) {
              values.intervalRange = intervalRange.map(function (d) {
                return dayjs_min_default()(d, 'DD-MM-YYYY').format('YYYY-MM-DD');
              });
            } else {
              values.intervalRange = [];
            }
            if (is_interval && interval_type && interval_value && values.intervalRange.length === 2 && dayjs_min_default()(values.intervalRange[0]).isValid() && dayjs_min_default()(values.intervalRange[1]).isValid()) {
              start_check = dayjs_min_default()(values.intervalRange[0]);
              counter = 1;
              while (start_check.isBefore(values.intervalRange[1])) {
                task = {
                  label: values.label,
                  farming_plan_state: values.farming_plan_state,
                  start_date: dayjs_min_default()(start_date).add(interval_value * counter, interval_type).format('YYYY-MM-DD HH:mm:ss'),
                  end_date: dayjs_min_default()(end_date).add(interval_value * counter, interval_type).format('YYYY-MM-DD HH:mm:ss'),
                  description: values.description,
                  assigned_to: values.assigned_to,
                  image: imagePath,
                  status: values.status,
                  enable_origin_tracing: enable_origin_tracing ? 1 : 0,
                  involve_in_users: (involved_in_users === null || involved_in_users === void 0 ? void 0 : involved_in_users.map(function (d) {
                    var id = typeof d === 'string' ? d : d.customer_user;
                    return {
                      customer_user: id
                    };
                  })) || [],
                  worksheet_list: workTimes.map(function (d) {
                    var _d$work_type_id = d.work_type_id,
                      work_type_id = _d$work_type_id === void 0 ? null : _d$work_type_id,
                      _d$exp_quantity = d.exp_quantity,
                      exp_quantity = _d$exp_quantity === void 0 ? 0 : _d$exp_quantity,
                      _d$quantity = d.quantity,
                      quantity = _d$quantity === void 0 ? 0 : _d$quantity,
                      _d$type = d.type,
                      type = _d$type === void 0 ? null : _d$type,
                      _d$description = d.description,
                      description = _d$description === void 0 ? null : _d$description,
                      _d$cost = d.cost,
                      cost = _d$cost === void 0 ? 0 : _d$cost;
                    return {
                      cost: cost,
                      work_type_id: work_type_id,
                      exp_quantity: exp_quantity,
                      quantity: quantity,
                      type: type,
                      description: description
                    };
                  }),
                  item_list: taskItems.map(function (d) {
                    var _d$quantity2 = d.quantity,
                      quantity = _d$quantity2 === void 0 ? 0 : _d$quantity2,
                      _d$description2 = d.description,
                      description = _d$description2 === void 0 ? null : _d$description2,
                      _d$iot_category_id = d.iot_category_id,
                      iot_category_id = _d$iot_category_id === void 0 ? null : _d$iot_category_id,
                      exp_quantity = d.exp_quantity,
                      _d$conversion_factor = d.conversion_factor,
                      conversion_factor = _d$conversion_factor === void 0 ? 1 : _d$conversion_factor,
                      _d$loss_quantity = d.loss_quantity,
                      loss_quantity = _d$loss_quantity === void 0 ? 0 : _d$loss_quantity,
                      active_uom = d.active_uom,
                      active_conversion_factor = d.active_conversion_factor,
                      uom = d.uom;

                    // T\xEDnh to\xE1n l\u1EA1i exp_quantity n\u1EBFu c\u1EA7n thi\u1EBFt
                    var calculatedExpQuantity = exp_quantity !== undefined && conversion_factor !== undefined ? exp_quantity * conversion_factor : exp_quantity;
                    return {
                      quantity: quantity,
                      description: description,
                      iot_category_id: iot_category_id,
                      exp_quantity: calculatedExpQuantity,
                      loss_quantity: loss_quantity,
                      active_uom: active_uom || uom,
                      // Fallback to default uom if active_uom is not set
                      active_conversion_factor: active_conversion_factor || conversion_factor // Fallback to default conversion_factor
                    };
                  }),
                  todo_list: todoList.map(function (d) {
                    delete d['name'];
                    var label = d.label,
                      _d$description3 = d.description,
                      description = _d$description3 === void 0 ? null : _d$description3,
                      customer_user_id = d.customer_user_id;
                    return {
                      label: label,
                      description: description,
                      customer_user_id: customer_user_id
                    };
                  }),
                  prod_quantity_list: productions.map(function (d) {
                    var _d$quantity3 = d.quantity,
                      quantity = _d$quantity3 === void 0 ? 0 : _d$quantity3,
                      _d$description4 = d.description,
                      description = _d$description4 === void 0 ? null : _d$description4,
                      _d$product_id = d.product_id,
                      product_id = _d$product_id === void 0 ? null : _d$product_id,
                      exp_quantity = d.exp_quantity,
                      _d$conversion_factor2 = d.conversion_factor,
                      conversion_factor = _d$conversion_factor2 === void 0 ? 1 : _d$conversion_factor2,
                      _d$lost_quantity = d.lost_quantity,
                      lost_quantity = _d$lost_quantity === void 0 ? 0 : _d$lost_quantity,
                      active_uom = d.active_uom,
                      active_conversion_factor = d.active_conversion_factor,
                      uom = d.uom;

                    // T\xEDnh to\xE1n l\u1EA1i exp_quantity n\u1EBFu c\u1EA7n thi\u1EBFt
                    var calculatedExpQuantity = exp_quantity !== undefined && conversion_factor !== undefined ? exp_quantity * conversion_factor : exp_quantity;
                    return {
                      quantity: quantity,
                      description: description,
                      product_id: product_id,
                      exp_quantity: calculatedExpQuantity,
                      lost_quantity: lost_quantity,
                      active_uom: active_uom || uom,
                      // Fallback to default uom if active_uom is not set
                      active_conversion_factor: active_conversion_factor || conversion_factor // Fallback to default conversion_factor
                    };
                  })
                };
                requestArr.push(task);
                start_check = start_check.add(interval_value, interval_type);
                counter++;
                console.log('counter is ', counter);
              }
              requestArr = requestArr.filter(function (d) {
                return d.start_date !== dayjs_min_default()(values.start_date[0]).format('YYYY-MM-DD HH:mm:ss');
              });
              requestArr.push({
                label: values.label,
                farming_plan_state: values.farming_plan_state,
                start_date: dayjs_min_default()(start_date).format('YYYY-MM-DD HH:mm:ss'),
                end_date: dayjs_min_default()(end_date).format('YYYY-MM-DD HH:mm:ss'),
                description: values.description,
                assigned_to: values.assigned_to,
                status: values.status,
                image: imagePath,
                enable_origin_tracing: enable_origin_tracing ? 1 : 0,
                involve_in_users: (involved_in_users === null || involved_in_users === void 0 ? void 0 : involved_in_users.map(function (d) {
                  var id = typeof d === 'string' ? d : d.customer_user;
                  return {
                    customer_user: id
                  };
                })) || [],
                worksheet_list: workTimes.map(function (d) {
                  var _d$cost2 = d.cost,
                    cost = _d$cost2 === void 0 ? 0 : _d$cost2,
                    _d$work_type_id2 = d.work_type_id,
                    work_type_id = _d$work_type_id2 === void 0 ? null : _d$work_type_id2,
                    _d$exp_quantity2 = d.exp_quantity,
                    exp_quantity = _d$exp_quantity2 === void 0 ? 0 : _d$exp_quantity2,
                    _d$quantity4 = d.quantity,
                    quantity = _d$quantity4 === void 0 ? 0 : _d$quantity4,
                    _d$type2 = d.type,
                    type = _d$type2 === void 0 ? null : _d$type2,
                    _d$description5 = d.description,
                    description = _d$description5 === void 0 ? null : _d$description5;
                  return {
                    cost: cost,
                    work_type_id: work_type_id,
                    exp_quantity: exp_quantity,
                    quantity: quantity,
                    type: type,
                    description: description
                  };
                }),
                item_list: taskItems.map(function (d) {
                  var _d$quantity5 = d.quantity,
                    quantity = _d$quantity5 === void 0 ? 0 : _d$quantity5,
                    _d$description6 = d.description,
                    description = _d$description6 === void 0 ? null : _d$description6,
                    _d$iot_category_id2 = d.iot_category_id,
                    iot_category_id = _d$iot_category_id2 === void 0 ? null : _d$iot_category_id2,
                    exp_quantity = d.exp_quantity,
                    _d$conversion_factor3 = d.conversion_factor,
                    conversion_factor = _d$conversion_factor3 === void 0 ? 1 : _d$conversion_factor3,
                    _d$loss_quantity2 = d.loss_quantity,
                    loss_quantity = _d$loss_quantity2 === void 0 ? 0 : _d$loss_quantity2,
                    active_uom = d.active_uom,
                    active_conversion_factor = d.active_conversion_factor,
                    uom = d.uom;

                  // T\xEDnh to\xE1n l\u1EA1i exp_quantity n\u1EBFu c\u1EA7n thi\u1EBFt
                  var calculatedExpQuantity = exp_quantity !== undefined && conversion_factor !== undefined ? exp_quantity * conversion_factor : exp_quantity;
                  return {
                    quantity: quantity,
                    description: description,
                    iot_category_id: iot_category_id,
                    exp_quantity: calculatedExpQuantity,
                    loss_quantity: loss_quantity,
                    active_uom: active_uom || uom,
                    // Fallback to default uom if active_uom is not set
                    active_conversion_factor: active_conversion_factor || conversion_factor // Fallback to default conversion_factor
                  };
                }),
                todo_list: todoList.map(function (d) {
                  delete d['name'];
                  var label = d.label,
                    _d$description7 = d.description,
                    description = _d$description7 === void 0 ? null : _d$description7,
                    customer_user_id = d.customer_user_id;
                  return {
                    label: label,
                    description: description,
                    customer_user_id: customer_user_id
                  };
                }),
                prod_quantity_list: productions.map(function (d) {
                  var _d$quantity6 = d.quantity,
                    quantity = _d$quantity6 === void 0 ? 0 : _d$quantity6,
                    _d$description8 = d.description,
                    description = _d$description8 === void 0 ? null : _d$description8,
                    _d$product_id2 = d.product_id,
                    product_id = _d$product_id2 === void 0 ? null : _d$product_id2,
                    exp_quantity = d.exp_quantity,
                    _d$conversion_factor4 = d.conversion_factor,
                    conversion_factor = _d$conversion_factor4 === void 0 ? 1 : _d$conversion_factor4,
                    _d$lost_quantity2 = d.lost_quantity,
                    lost_quantity = _d$lost_quantity2 === void 0 ? 0 : _d$lost_quantity2,
                    active_uom = d.active_uom,
                    active_conversion_factor = d.active_conversion_factor,
                    uom = d.uom;

                  // T\xEDnh to\xE1n l\u1EA1i exp_quantity n\u1EBFu c\u1EA7n thi\u1EBFt
                  var calculatedExpQuantity = exp_quantity !== undefined && conversion_factor !== undefined ? exp_quantity * conversion_factor : exp_quantity;
                  return {
                    quantity: quantity,
                    description: description,
                    product_id: product_id,
                    exp_quantity: calculatedExpQuantity,
                    lost_quantity: lost_quantity,
                    active_uom: active_uom || uom,
                    // Fallback to default uom if active_uom is not set
                    active_conversion_factor: active_conversion_factor || conversion_factor // Fallback to default conversion_factor
                  };
                })
              });
            } else {
              requestArr.push({
                label: values.label,
                farming_plan_state: values.farming_plan_state,
                start_date: dayjs_min_default()(start_date).format('YYYY-MM-DD HH:mm:ss'),
                end_date: dayjs_min_default()(end_date).format('YYYY-MM-DD HH:mm:ss'),
                description: values.description,
                assigned_to: values.assigned_to,
                status: values.status,
                image: imagePath,
                enable_origin_tracing: enable_origin_tracing ? 1 : 0,
                involve_in_users: (involved_in_users === null || involved_in_users === void 0 ? void 0 : involved_in_users.map(function (d) {
                  var id = typeof d === 'string' ? d : d.customer_user;
                  return {
                    customer_user: id
                  };
                })) || [],
                worksheet_list: workTimes.map(function (d) {
                  var _d$cost3 = d.cost,
                    cost = _d$cost3 === void 0 ? 0 : _d$cost3,
                    _d$work_type_id3 = d.work_type_id,
                    work_type_id = _d$work_type_id3 === void 0 ? null : _d$work_type_id3,
                    _d$exp_quantity3 = d.exp_quantity,
                    exp_quantity = _d$exp_quantity3 === void 0 ? 0 : _d$exp_quantity3,
                    _d$quantity7 = d.quantity,
                    quantity = _d$quantity7 === void 0 ? 0 : _d$quantity7,
                    _d$type3 = d.type,
                    type = _d$type3 === void 0 ? null : _d$type3,
                    _d$description9 = d.description,
                    description = _d$description9 === void 0 ? null : _d$description9;
                  return {
                    cost: cost,
                    work_type_id: work_type_id,
                    exp_quantity: exp_quantity,
                    quantity: quantity,
                    type: type,
                    description: description
                  };
                }),
                item_list: taskItems.map(function (d) {
                  var _d$quantity8 = d.quantity,
                    quantity = _d$quantity8 === void 0 ? 0 : _d$quantity8,
                    _d$description10 = d.description,
                    description = _d$description10 === void 0 ? null : _d$description10,
                    _d$iot_category_id3 = d.iot_category_id,
                    iot_category_id = _d$iot_category_id3 === void 0 ? null : _d$iot_category_id3,
                    exp_quantity = d.exp_quantity,
                    _d$conversion_factor5 = d.conversion_factor,
                    conversion_factor = _d$conversion_factor5 === void 0 ? 1 : _d$conversion_factor5,
                    _d$loss_quantity3 = d.loss_quantity,
                    loss_quantity = _d$loss_quantity3 === void 0 ? 0 : _d$loss_quantity3,
                    active_uom = d.active_uom,
                    active_conversion_factor = d.active_conversion_factor,
                    uom = d.uom;

                  // T\xEDnh to\xE1n l\u1EA1i exp_quantity n\u1EBFu c\u1EA7n thi\u1EBFt
                  var calculatedExpQuantity = exp_quantity !== undefined && conversion_factor !== undefined ? exp_quantity * conversion_factor : exp_quantity;
                  return {
                    quantity: quantity,
                    description: description,
                    iot_category_id: iot_category_id,
                    exp_quantity: calculatedExpQuantity,
                    loss_quantity: loss_quantity,
                    active_uom: active_uom || uom,
                    // Fallback to default uom if active_uom is not set
                    active_conversion_factor: active_conversion_factor || conversion_factor // Fallback to default conversion_factor
                  };
                }),
                todo_list: todoList.map(function (d) {
                  delete d['name'];
                  var label = d.label,
                    _d$description11 = d.description,
                    description = _d$description11 === void 0 ? null : _d$description11,
                    customer_user_id = d.customer_user_id;
                  return {
                    label: label,
                    description: description,
                    customer_user_id: customer_user_id
                  };
                }),
                prod_quantity_list: productions.map(function (d) {
                  var _d$quantity9 = d.quantity,
                    quantity = _d$quantity9 === void 0 ? 0 : _d$quantity9,
                    _d$description12 = d.description,
                    description = _d$description12 === void 0 ? null : _d$description12,
                    _d$product_id3 = d.product_id,
                    product_id = _d$product_id3 === void 0 ? null : _d$product_id3,
                    exp_quantity = d.exp_quantity,
                    _d$conversion_factor6 = d.conversion_factor,
                    conversion_factor = _d$conversion_factor6 === void 0 ? 1 : _d$conversion_factor6,
                    _d$lost_quantity3 = d.lost_quantity,
                    lost_quantity = _d$lost_quantity3 === void 0 ? 0 : _d$lost_quantity3,
                    active_uom = d.active_uom,
                    active_conversion_factor = d.active_conversion_factor,
                    uom = d.uom;

                  // T\xEDnh to\xE1n l\u1EA1i exp_quantity n\u1EBFu c\u1EA7n thi\u1EBFt
                  var calculatedExpQuantity = exp_quantity !== undefined && conversion_factor !== undefined ? exp_quantity * conversion_factor : exp_quantity;
                  return {
                    quantity: quantity,
                    description: description,
                    product_id: product_id,
                    exp_quantity: calculatedExpQuantity,
                    lost_quantity: lost_quantity,
                    active_uom: active_uom || uom,
                    // Fallback to default uom if active_uom is not set
                    active_conversion_factor: active_conversion_factor || conversion_factor // Fallback to default conversion_factor
                  };
                })
              });
            }
            requestArr = requestArr.map(function (d) {
              return objectSpread2_default()(objectSpread2_default()({}, d), {}, {
                task_progress: 0,
                tag: values.tag
              });
            });
            _context3.next = 11;
            return (0,farming_plan/* createFarmingPlanTask */.qM)(requestArr);
          case 11:
            message.success({
              content: 'Created successfully'
            });
            onOpenChange === null || onOpenChange === void 0 || onOpenChange(false);
            if (onCreateSuccess) {
              onCreateSuccess === null || onCreateSuccess === void 0 || onCreateSuccess();
            } else {
              _umi_production_exports.history.push('/farming-management/workflow-management');
            }
            return _context3.abrupt("return", true);
          case 17:
            _context3.prev = 17;
            _context3.t0 = _context3["catch"](3);
            console.log('error', _context3.t0);
            return _context3.abrupt("return", false);
          case 21:
            _context3.prev = 21;
            setSubmitting(false);
            return _context3.finish(21);
          case 24:
          case "end":
            return _context3.stop();
        }
      }, _callee3, null, [[3, 17, 21, 24]]);
    }));
    return function onFinish(_x) {
      return _ref4.apply(this, arguments);
    };
  }();
  var _useSearchParams = (0,_umi_production_exports.useSearchParams)(),
    _useSearchParams2 = slicedToArray_default()(_useSearchParams, 2),
    searchParams = _useSearchParams2[0],
    setSearchParams = _useSearchParams2[1];
  var farmingPlanState = searchParams.get('farming_plan_state');
  var intl = (0,_umi_production_exports.useIntl)();
  /// default value for form
  (0,useDeepCompareEffect/* useDeepCompareEffect */.KW)(function () {
    if (defaultValue) {
      form.setFieldsValue(defaultValue);
    }
  }, [defaultValue]);
  var content = /*#__PURE__*/(0,jsx_runtime.jsx)(ProForm/* ProForm */.A, {
    onFinish: onFinish,
    submitter: false,
    initialValues: {
      farming_plan_state: farmingPlanStateId || farmingPlanState,
      start_date: dayjs_min_default()(),
      crop: cropId
    },
    form: form
    // formRef={formRef}
    ,
    children: /*#__PURE__*/(0,jsx_runtime.jsxs)(space/* default */.Z, {
      size: 'large',
      direction: "vertical",
      style: {
        width: '100%'
      },
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)(Create_DetailedInfo, {
        openFromModal: mode === 'modal',
        isTemplateTask: isTemplateTask,
        currentPlanParam: currentPlan,
        onEditTagSuccess: onCreateSuccess,
        onFileListChange: onFileListChange,
        setTaskItems: setTaskItems,
        setTodoList: setTodoList,
        setWorkTimes: setWorkTimes,
        setProductions: setProductions
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(CreateTodoTableEditer/* default */.Z, {
        dataSource: todoList,
        setDataSource: setTodoList,
        customerUserOptions: customerUserOptions
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(ItemUsedTableCreateView/* default */.Z, {}), /*#__PURE__*/(0,jsx_runtime.jsx)(ProductionTableCreateView/* default */.Z, {})]
    })
  });
  var footer = [/*#__PURE__*/(0,jsx_runtime.jsxs)(space/* default */.Z, {
    children: [/*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
      onClick: function onClick() {
        if (mode === 'modal') {
          onOpenChange === null || onOpenChange === void 0 || onOpenChange(false);
          return;
        }
        _umi_production_exports.history.back();
      },
      children: intl.formatMessage({
        id: 'common.cancel'
      })
    }, 'cancel'), /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
      onClick: ( /*#__PURE__*/function () {
        var _ref5 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee4(event) {
          var valid;
          return regeneratorRuntime_default()().wrap(function _callee4$(_context4) {
            while (1) switch (_context4.prev = _context4.next) {
              case 0:
                console.log('submitting', event);
                _context4.prev = 1;
                _context4.next = 4;
                return form.validateFields();
              case 4:
                valid = _context4.sent;
                // Th\xEAm validateFields \u0111\u1EC3 ki\u1EC3m tra l\u1ED7i
                console.log('valid', valid);
                onFinish(form.getFieldsValue());
                // form.submit(); // Call form.submit() to trigger onFinish
                _context4.next = 12;
                break;
              case 9:
                _context4.prev = 9;
                _context4.t0 = _context4["catch"](1);
                console.error('Validation failed:', _context4.t0);
              case 12:
              case "end":
                return _context4.stop();
            }
          }, _callee4, null, [[1, 9]]);
        }));
        return function (_x2) {
          return _ref5.apply(this, arguments);
        };
      }()),
      loading: submitting,
      type: "primary",
      children: intl.formatMessage({
        id: 'common.save'
      })
    }, "save")]
  }, "footer")];
  if (mode === 'modal') return /*#__PURE__*/(0,jsx_runtime.jsx)(modal/* default */.Z, {
    open: open,
    onCancel: function onCancel() {
      onOpenChange === null || onOpenChange === void 0 || onOpenChange(false);
    },
    confirmLoading: loading,
    width: 800,
    title: intl.formatMessage({
      id: 'common.create'
    }),
    footer: footer,
    children: content
  });
  return /*#__PURE__*/(0,jsx_runtime.jsx)(PageContainer/* PageContainer */._z, {
    fixedHeader: true
    // extra={[
    //   <Button
    //     key={'cancel'}
    //     onClick={() => {
    //       history.back();
    //     }}
    //   >
    //     H\u1EE7y
    //   </Button>,
    //   <Button key="save" type="primary">
    //     L\u01B0u
    //   </Button>,
    // ]}
    ,
    footer: footer,
    children: content
  });
};
/* harmony default export */ var Create = (CreateWorkflow);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///22864
`)}}]);
