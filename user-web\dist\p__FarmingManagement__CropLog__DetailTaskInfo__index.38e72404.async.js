(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[7468],{47046:function(__unused_webpack_module,__webpack_exports__){"use strict";eval(`// This icon file is generated automatically.
var DeleteOutlined = { "icon": { "tag": "svg", "attrs": { "viewBox": "64 64 896 896", "focusable": "false" }, "children": [{ "tag": "path", "attrs": { "d": "M360 184h-8c4.4 0 8-3.6 8-8v8h304v-8c0 4.4 3.6 8 8 8h-8v72h72v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80h72v-72zm504 72H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM731.3 840H292.7l-24.2-512h487l-24.2 512z" } }] }, "name": "delete", "theme": "outlined" };
/* harmony default export */ __webpack_exports__.Z = (DeleteOutlined);
//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNDcwNDYuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQSx1QkFBdUIsVUFBVSx5QkFBeUIsa0RBQWtELGlCQUFpQiwwQkFBMEIsOFZBQThWLEdBQUc7QUFDeGYsc0RBQWUsY0FBYyxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvQGFudC1kZXNpZ24vaWNvbnMtc3ZnL2VzL2Fzbi9EZWxldGVPdXRsaW5lZC5qcz85ODlhIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIFRoaXMgaWNvbiBmaWxlIGlzIGdlbmVyYXRlZCBhdXRvbWF0aWNhbGx5LlxudmFyIERlbGV0ZU91dGxpbmVkID0geyBcImljb25cIjogeyBcInRhZ1wiOiBcInN2Z1wiLCBcImF0dHJzXCI6IHsgXCJ2aWV3Qm94XCI6IFwiNjQgNjQgODk2IDg5NlwiLCBcImZvY3VzYWJsZVwiOiBcImZhbHNlXCIgfSwgXCJjaGlsZHJlblwiOiBbeyBcInRhZ1wiOiBcInBhdGhcIiwgXCJhdHRyc1wiOiB7IFwiZFwiOiBcIk0zNjAgMTg0aC04YzQuNCAwIDgtMy42IDgtOHY4aDMwNHYtOGMwIDQuNCAzLjYgOCA4IDhoLTh2NzJoNzJ2LTgwYzAtMzUuMy0yOC43LTY0LTY0LTY0SDM1MmMtMzUuMyAwLTY0IDI4LjctNjQgNjR2ODBoNzJ2LTcyem01MDQgNzJIMTYwYy0xNy43IDAtMzIgMTQuMy0zMiAzMnYzMmMwIDQuNCAzLjYgOCA4IDhoNjAuNGwyNC43IDUyM2MxLjYgMzQuMSAyOS44IDYxIDYzLjkgNjFoNDU0YzM0LjIgMCA2Mi4zLTI2LjggNjMuOS02MWwyNC43LTUyM0g4ODhjNC40IDAgOC0zLjYgOC04di0zMmMwLTE3LjctMTQuMy0zMi0zMi0zMnpNNzMxLjMgODQwSDI5Mi43bC0yNC4yLTUxMmg0ODdsLTI0LjIgNTEyelwiIH0gfV0gfSwgXCJuYW1lXCI6IFwiZGVsZXRlXCIsIFwidGhlbWVcIjogXCJvdXRsaW5lZFwiIH07XG5leHBvcnQgZGVmYXVsdCBEZWxldGVPdXRsaW5lZDtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==
//# sourceURL=webpack-internal:///47046
`)},22452:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){"use strict";eval(`/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(1413);
/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(91);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(67294);
/* harmony import */ var _FieldContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(66758);
/* harmony import */ var _Field__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(265);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(85893);


var _excluded = ["fieldProps", "proFieldProps"];




var valueType = 'dateTime';

/**
 * \u65F6\u95F4\u65E5\u671F\u9009\u62E9\u7EC4\u4EF6
 *
 * @param
 */
var ProFormDateTimePicker = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function (_ref, ref) {
  var fieldProps = _ref.fieldProps,
    proFieldProps = _ref.proFieldProps,
    rest = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z)(_ref, _excluded);
  var context = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_FieldContext__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z);
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_Field__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z, (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z)({
    ref: ref,
    fieldProps: (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z)({
      getPopupContainer: context.getPopupContainer
    }, fieldProps),
    valueType: valueType,
    proFieldProps: proFieldProps,
    filedConfig: {
      valueType: valueType,
      customLightMode: true
    }
  }, rest));
});
/* harmony default export */ __webpack_exports__.Z = (ProFormDateTimePicker);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///22452
`)},56517:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){"use strict";eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SM: function() { return /* binding */ PageHeaderSkeleton; },
/* harmony export */   cg: function() { return /* binding */ ListSkeleton; },
/* harmony export */   x1: function() { return /* binding */ Line; }
/* harmony export */ });
/* unused harmony exports MediaQueryKeyEnum, ListSkeletonItem, ListToolbarSkeleton */
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(96074);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(75302);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(76216);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(99559);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(42075);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(67294);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(85893);



/** \u4E00\u6761\u5206\u5272\u7EBF */



var Line = function Line(_ref) {
  var padding = _ref.padding;
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)("div", {
    style: {
      padding: padding || '0 24px'
    },
    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z, {
      style: {
        margin: 0
      }
    })
  });
};
var MediaQueryKeyEnum = {
  xs: 2,
  sm: 2,
  md: 4,
  lg: 4,
  xl: 6,
  xxl: 6
};
var StatisticSkeleton = function StatisticSkeleton(_ref2) {
  var size = _ref2.size,
    active = _ref2.active;
  var defaultCol = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function () {
    return {
      lg: true,
      md: true,
      sm: false,
      xl: false,
      xs: false,
      xxl: false
    };
  }, []);
  var col = antd__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .ZP.useBreakpoint() || defaultCol;
  var colSize = Object.keys(col).filter(function (key) {
    return col[key] === true;
  })[0] || 'md';
  var arraySize = size === undefined ? MediaQueryKeyEnum[colSize] || 6 : size;
  var firstWidth = function firstWidth(index) {
    if (index === 0) {
      return 0;
    }
    if (arraySize > 2) {
      return 42;
    }
    return 16;
  };
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z, {
    bordered: false,
    style: {
      marginBlockEnd: 16
    },
    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)("div", {
      style: {
        width: '100%',
        justifyContent: 'space-between',
        display: 'flex'
      },
      children: new Array(arraySize).fill(null).map(function (_, index) {
        return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)("div", {
          style: {
            borderInlineStart: arraySize > 2 && index === 1 ? '1px solid rgba(0,0,0,0.06)' : undefined,
            paddingInlineStart: firstWidth(index),
            flex: 1,
            marginInlineEnd: index === 0 ? 16 : 0
          },
          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z, {
            active: active,
            paragraph: false,
            title: {
              width: 100,
              style: {
                marginBlockStart: 0
              }
            }
          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z.Button, {
            active: active,
            style: {
              height: 48
            }
          })]
        }, index);
      })
    })
  });
};

/** \u5217\u8868\u5B50\u9879\u76EE\u9AA8\u67B6\u5C4F */
var ListSkeletonItem = function ListSkeletonItem(_ref3) {
  var active = _ref3.active;
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z, {
      bordered: false
      // eslint-disable-next-line react/no-array-index-key
      ,
      style: {
        borderRadius: 0
      },
      bodyStyle: {
        padding: 24
      },
      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)("div", {
        style: {
          width: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        },
        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)("div", {
          style: {
            maxWidth: '100%',
            flex: 1
          },
          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z, {
            active: active,
            title: {
              width: 100,
              style: {
                marginBlockStart: 0
              }
            },
            paragraph: {
              rows: 1,
              style: {
                margin: 0
              }
            }
          })
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z.Button, {
          active: active,
          size: "small",
          style: {
            width: 165,
            marginBlockStart: 12
          }
        })]
      })
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Line, {})]
  });
};

/** \u5217\u8868\u9AA8\u67B6\u5C4F */
var ListSkeleton = function ListSkeleton(_ref4) {
  var size = _ref4.size,
    _ref4$active = _ref4.active,
    active = _ref4$active === void 0 ? true : _ref4$active,
    actionButton = _ref4.actionButton;
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z, {
    bordered: false,
    bodyStyle: {
      padding: 0
    },
    children: [new Array(size).fill(null).map(function (_, index) {
      return (
        /*#__PURE__*/
        // eslint-disable-next-line react/no-array-index-key
        (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ListSkeletonItem, {
          active: !!active
        }, index)
      );
    }), actionButton !== false && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z, {
      bordered: false,
      style: {
        borderStartEndRadius: 0,
        borderTopLeftRadius: 0
      },
      bodyStyle: {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      },
      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z.Button, {
        style: {
          width: 102
        },
        active: active,
        size: "small"
      })
    })]
  });
};

/**
 * \u9762\u5305\u5C51\u7684 \u9AA8\u67B6\u5C4F
 *
 * @param param0
 */
var PageHeaderSkeleton = function PageHeaderSkeleton(_ref5) {
  var active = _ref5.active;
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)("div", {
    style: {
      marginBlockEnd: 16
    },
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z, {
      paragraph: false,
      title: {
        width: 185
      }
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z.Button, {
      active: active,
      size: "small"
    })]
  });
};
/**
 * \u5217\u8868\u64CD\u4F5C\u680F\u7684\u9AA8\u67B6\u5C4F
 *
 * @param param0
 */
var ListToolbarSkeleton = function ListToolbarSkeleton(_ref6) {
  var active = _ref6.active;
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z, {
    bordered: false,
    style: {
      borderBottomRightRadius: 0,
      borderBottomLeftRadius: 0
    },
    bodyStyle: {
      paddingBlockEnd: 8
    },
    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Z, {
      style: {
        width: '100%',
        justifyContent: 'space-between'
      },
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z.Button, {
        active: active,
        style: {
          width: 200
        },
        size: "small"
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Z, {
        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z.Button, {
          active: active,
          size: "small",
          style: {
            width: 120
          }
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z.Button, {
          active: active,
          size: "small",
          style: {
            width: 80
          }
        })]
      })]
    })
  });
};
var ListPageSkeleton = function ListPageSkeleton(_ref7) {
  var _ref7$active = _ref7.active,
    active = _ref7$active === void 0 ? true : _ref7$active,
    statistic = _ref7.statistic,
    actionButton = _ref7.actionButton,
    toolbar = _ref7.toolbar,
    pageHeader = _ref7.pageHeader,
    _ref7$list = _ref7.list,
    list = _ref7$list === void 0 ? 5 : _ref7$list;
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)("div", {
    style: {
      width: '100%'
    },
    children: [pageHeader !== false && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PageHeaderSkeleton, {
      active: active
    }), statistic !== false && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(StatisticSkeleton, {
      size: statistic,
      active: active
    }), (toolbar !== false || list !== false) && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z, {
      bordered: false,
      bodyStyle: {
        padding: 0
      },
      children: [toolbar !== false && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ListToolbarSkeleton, {
        active: active
      }), list !== false && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ListSkeleton, {
        size: list,
        active: active,
        actionButton: actionButton
      })]
    })]
  });
};
/* harmony default export */ __webpack_exports__.ZP = (ListPageSkeleton);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///56517
`)},57404:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){"use strict";eval(`// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return /* binding */ CropLog_DetailTaskInfo; }
});

// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/slicedToArray.js
var slicedToArray = __webpack_require__(5574);
var slicedToArray_default = /*#__PURE__*/__webpack_require__.n(slicedToArray);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-skeleton/es/components/List/index.js
var List = __webpack_require__(56517);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-layout/es/components/PageContainer/index.js + 18 modules
var PageContainer = __webpack_require__(6110);
// EXTERNAL MODULE: ./src/.umi-production/exports.ts + 34 modules
var _umi_production_exports = __webpack_require__(7837);
// EXTERNAL MODULE: ./node_modules/antd/es/form/index.js + 18 modules
var es_form = __webpack_require__(98138);
// EXTERNAL MODULE: ./node_modules/antd/es/tabs/index.js + 30 modules
var tabs = __webpack_require__(48096);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/regeneratorRuntime.js
var regeneratorRuntime = __webpack_require__(15009);
var regeneratorRuntime_default = /*#__PURE__*/__webpack_require__.n(regeneratorRuntime);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(99289);
var asyncToGenerator_default = /*#__PURE__*/__webpack_require__.n(asyncToGenerator);
// EXTERNAL MODULE: ./src/services/crop.ts
var crop = __webpack_require__(52662);
// EXTERNAL MODULE: ./src/services/farming-plan.ts
var farming_plan = __webpack_require__(74459);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/DateTimePicker/index.js
var DateTimePicker = __webpack_require__(22452);
// EXTERNAL MODULE: ./node_modules/antd/es/row/index.js
var row = __webpack_require__(71230);
// EXTERNAL MODULE: ./node_modules/antd/es/col/index.js
var col = __webpack_require__(15746);
// EXTERNAL MODULE: ./node_modules/antd/es/card/index.js + 3 modules
var card = __webpack_require__(76216);
// EXTERNAL MODULE: ./node_modules/antd/es/input/index.js + 4 modules
var input = __webpack_require__(96365);
// EXTERNAL MODULE: ./node_modules/antd/es/image/index.js + 37 modules
var es_image = __webpack_require__(11499);
// EXTERNAL MODULE: ./node_modules/react/jsx-runtime.js
var jsx_runtime = __webpack_require__(85893);
;// CONCATENATED MODULE: ./src/pages/FarmingManagement/CropLog/DetailTaskInfo/components/Image.tsx




var ImageCropNote = function ImageCropNote(_ref) {
  var imageLink = _ref.imageLink,
    index = _ref.index,
    callbackFunc = _ref.callbackFunc;
  return /*#__PURE__*/(0,jsx_runtime.jsx)(jsx_runtime.Fragment, {
    children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_image/* default */.Z, {
      width: 200,
      src: 'https://iot.viis.tech/api/v2/file/download?file_url=' + imageLink
    })
  });
};
/* harmony default export */ var Image = (ImageCropNote);
;// CONCATENATED MODULE: ./src/pages/FarmingManagement/CropLog/DetailTaskInfo/components/DetailTaskInfoTab.tsx












var Item = es_form/* default */.Z.Item;
var DetailTaskInfotab = function DetailTaskInfotab(props) {
  var _useState = (0,react.useState)(false),
    _useState2 = slicedToArray_default()(_useState, 2),
    loading = _useState2[0],
    setLoading = _useState2[1];
  var _useState3 = (0,react.useState)({}),
    _useState4 = slicedToArray_default()(_useState3, 2),
    task = _useState4[0],
    setTask = _useState4[1];
  var _useState5 = (0,react.useState)([]),
    _useState6 = slicedToArray_default()(_useState5, 2),
    cropList = _useState6[0],
    setCropList = _useState6[1];
  var _useState7 = (0,react.useState)([]),
    _useState8 = slicedToArray_default()(_useState7, 2),
    imageLinks = _useState8[0],
    setImageLinks = _useState8[1];
  var _Form$useForm = es_form/* default */.Z.useForm(),
    _Form$useForm2 = slicedToArray_default()(_Form$useForm, 1),
    form = _Form$useForm2[0];
  var taskID = props.taskID;
  var renderImageCropNoteLayout = function renderImageCropNoteLayout() {
    var ImageCropNoteComponents = [];
    var rowImages = [];
    imageLinks.forEach(function (imageLink, index) {
      rowImages.push(imageLink);
      if ((index + 1) % 4 === 0 || index === imageLinks.length - 1) {
        // When we have 4 images in the row or we have reached the last image
        var ImageCropNoteRow = /*#__PURE__*/(0,jsx_runtime.jsx)(row/* default */.Z, {
          className: "gutter-row",
          gutter: 4,
          children: rowImages.map(function (image, idx) {
            return /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
              className: "gutter-row",
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(Image, {
                imageLink: image,
                index: idx
              })
            }, "col_".concat(index, "_").concat(idx));
          })
        }, "row_".concat(index));
        ImageCropNoteComponents.push(ImageCropNoteRow);
        rowImages = [];
      }
    });
    return ImageCropNoteComponents;
  };
  var initTaskInfo = /*#__PURE__*/function () {
    var _ref = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
      var res, data;
      return regeneratorRuntime_default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _context.prev = 0;
            setLoading(true);
            _context.next = 4;
            return (0,farming_plan/* getTaskManageTracingList */.Ae)({
              page: 1,
              size: 1000,
              fields: ['*'],
              filters: [],
              or_filters: [],
              order_by: '',
              group_by: ''
            });
          case 4:
            res = _context.sent;
            data = res.data.data;
            console.log('receive task info', data[0]);
            setTask(data[0]);
            if (data[0].image) {
              setImageLinks(data[0].image.split(','));
            }
            form.setFieldsValue(data[0]);
            _context.next = 15;
            break;
          case 12:
            _context.prev = 12;
            _context.t0 = _context["catch"](0);
            console.log(_context.t0);
          case 15:
            _context.prev = 15;
            setLoading(false);
            return _context.finish(15);
          case 18:
          case "end":
            return _context.stop();
        }
      }, _callee, null, [[0, 12, 15, 18]]);
    }));
    return function initTaskInfo() {
      return _ref.apply(this, arguments);
    };
  }();
  var initCropList = /*#__PURE__*/function () {
    var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2() {
      var res, data;
      return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            _context2.prev = 0;
            setLoading(true);
            // let [params, sort, filters]: any[] = ['', '', [["iot_Crop_note", "name", "like", taskID]]];
            _context2.next = 4;
            return (0,crop/* getCrop */.EH)({
              page: 1,
              size: 1000,
              fields: ['*'],
              filters: JSON.stringify([]),
              or_filters: [],
              order_by: '',
              group_by: ''
            });
          case 4:
            res = _context2.sent;
            data = res.data.data;
            console.log('receive data', data);
            setCropList(data);
            _context2.next = 13;
            break;
          case 10:
            _context2.prev = 10;
            _context2.t0 = _context2["catch"](0);
            console.log(_context2.t0);
          case 13:
            _context2.prev = 13;
            setLoading(false);
            return _context2.finish(13);
          case 16:
          case "end":
            return _context2.stop();
        }
      }, _callee2, null, [[0, 10, 13, 16]]);
    }));
    return function initCropList() {
      return _ref2.apply(this, arguments);
    };
  }();
  (0,react.useEffect)(function () {
    initTaskInfo();
    initCropList();
  }, [taskID]);
  var assignedToInfo = form.getFieldValue('assigned_to_info');
  var involveInUsers = form.getFieldValue('involve_in_users');
  console.log('assignedToInfo', assignedToInfo);
  return /*#__PURE__*/(0,jsx_runtime.jsx)(card/* default */.Z, {
    title: task ? task.name : 'Loading...' // Ho\u1EB7c "N/A" ho\u1EB7c gi\xE1 tr\u1ECB m\u1EB7c \u0111\u1ECBnh kh\xE1c tu\u1EF3 \xFD
    ,
    loading: loading,
    children: /*#__PURE__*/(0,jsx_runtime.jsxs)(es_form/* default */.Z, {
      size: "small",
      layout: "horizontal",
      labelCol: {
        span: 24
      },
      labelAlign: "left",
      form: form,
      children: [/*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
        gutter: 5,
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
          md: 6,
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(Item, {
            label: "C\\xF4ng vi\\u1EC7c",
            labelCol: {
              span: 24
            },
            name: "label",
            style: {
              marginBottom: '16px',
              fontWeight: 'bold'
            } // Add the fontWeight property here
            ,
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(input/* default */.Z, {
              readOnly: true
            })
          })
        }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
          md: 6,
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(Item, {
            label: "V\\u1EE5 m\\xF9a",
            labelCol: {
              span: 24
            },
            name: "crop_name",
            style: {
              marginBottom: '16px',
              fontWeight: 'bold'
            } // Add the fontWeight property here
            ,
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(input/* default */.Z, {
              readOnly: true
            })
          })
        }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
          md: 6,
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(Item, {
            label: "Khu v\\u1EF1c",
            labelCol: {
              span: 24
            },
            name: "zone_name",
            style: {
              marginBottom: '16px',
              fontWeight: 'bold'
            } // Add the fontWeight property here
            ,
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(input/* default */.Z, {
              readOnly: true
            })
          })
        }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
          md: 6,
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(Item, {
            label: "D\\u1EF1 \\xE1n",
            labelCol: {
              span: 24
            },
            name: "project_name",
            style: {
              marginBottom: '16px',
              fontWeight: 'bold'
            } // Add the fontWeight property here
            ,
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(input/* default */.Z, {
              readOnly: true
            })
          })
        })]
      }), /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
        gutter: 5,
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
          md: 6,
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(Item, {
            label: "K\\u1EBF ho\\u1EA1ch",
            labelCol: {
              span: 24
            },
            name: "pl_name",
            style: {
              marginBottom: '16px',
              fontWeight: 'bold'
            } // Add the fontWeight property here
            ,
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(input/* default */.Z, {
              readOnly: true
            })
          })
        }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
          md: 6,
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(Item, {
            label: "Giai \\u0111o\\u1EA1n",
            labelCol: {
              span: 24
            },
            name: "crop_name",
            style: {
              marginBottom: '16px',
              fontWeight: 'bold'
            } // Add the fontWeight property here
            ,
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(input/* default */.Z, {
              readOnly: true
            })
          })
        }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
          md: 6,
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(Item, {
            label: "Tr\\u1EA1ng th\\xE1i",
            labelCol: {
              span: 24
            },
            name: "status",
            style: {
              marginBottom: '16px',
              fontWeight: 'bold'
            } // Add the fontWeight property here
            ,
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(input/* default */.Z, {
              readOnly: true
            })
          })
        }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
          md: 6,
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(Item, {
            label: "T\\xEAn d\\u1EF1 \\xE1n",
            labelCol: {
              span: 24
            },
            name: "project_name",
            style: {
              marginBottom: '16px',
              fontWeight: 'bold'
            } // Add the fontWeight property here
            ,
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(input/* default */.Z, {
              readOnly: true
            })
          })
        })]
      }), /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
        gutter: 5,
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
          className: "gutter-row",
          md: 12,
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(Item, {
            label: "Ng\\xE0y b\\u1EAFt \\u0111\\u1EA7u",
            labelCol: {
              span: 24
            },
            name: "start_date",
            style: {
              marginBottom: '16px',
              fontWeight: 'bold'
            } // Add the fontWeight property here
            ,
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(DateTimePicker/* default */.Z, {
              disabled: true
            })
          })
        }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
          className: "gutter-row",
          md: 12,
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(Item, {
            label: "Ng\\xE0y k\\u1EBFt th\\xFAc",
            labelCol: {
              span: 24
            },
            name: "end_date",
            style: {
              marginBottom: '16px',
              fontWeight: 'bold'
            } // Add the fontWeight property here
            ,
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(DateTimePicker/* default */.Z, {
              disabled: true
            })
          })
        })]
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(row/* default */.Z, {
        gutter: 5,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
          className: "gutter-row",
          md: 24,
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(Item, {
            label: "N\\u1ED9i dung c\\xF4ng vi\\u1EC7c",
            labelCol: {
              span: 24
            },
            name: "description",
            style: {
              marginBottom: '16px',
              fontWeight: 'bold'
            } // Add the fontWeight property here
            ,
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(input/* default */.Z.TextArea, {
              readOnly: true
            })
          })
        })
      }), /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
        gutter: 5,
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
          className: "gutter-row",
          md: 8,
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(Item, {
            label: "Ng\\u01B0\\u1EDDi th\\u1EF1c hi\\u1EC7n",
            labelCol: {
              span: 24
            }
            // name="assigned_to_info"
            ,
            style: {
              marginBottom: '16px',
              fontWeight: 'bold'
            } // Add the fontWeight property here
            ,
            children: assignedToInfo ? /*#__PURE__*/(0,jsx_runtime.jsx)(input/* default */.Z, {
              readOnly: true,
              value: assignedToInfo.map(function (user) {
                return "".concat(user.first_name, " ").concat(user.last_name);
              }).join(', ')
            }) : /*#__PURE__*/(0,jsx_runtime.jsx)(input/* default */.Z, {
              readOnly: true,
              value: ""
            })
          })
        }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
          className: "gutter-row",
          md: 16,
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(Item, {
            label: "Ng\\u01B0\\u1EDDi li\\xEAn quan",
            labelCol: {
              span: 24
            },
            style: {
              marginBottom: '16px',
              fontWeight: 'bold'
            } // Add the fontWeight property here
            ,
            children: assignedToInfo ? /*#__PURE__*/(0,jsx_runtime.jsx)(input/* default */.Z, {
              readOnly: true,
              value: involveInUsers.map(function (user) {
                return "".concat(user.first_name, " ").concat(user.last_name);
              }).join(', ')
            }) : /*#__PURE__*/(0,jsx_runtime.jsx)(input/* default */.Z, {
              readOnly: true,
              value: ""
            })
          })
        })]
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        className: "gutter-row",
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(Item, {
          label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
            id: 'common.form.image'
          }),
          labelCol: {
            span: 24
          },
          name: "image",
          style: {
            marginBottom: '16px',
            fontWeight: 'bold'
          } // Add the fontWeight property here
          ,
          children: renderImageCropNoteLayout()
        })
      })]
    })
  });
};
/* harmony default export */ var DetailTaskInfoTab = (DetailTaskInfotab);
;// CONCATENATED MODULE: ./src/pages/FarmingManagement/CropLog/DetailTaskInfo/index.tsx








var DetailTaskInfo_Item = es_form/* default */.Z.Item;
var DetailTaskInfo = function DetailTaskInfo() {
  var _searchParams$get;
  var _useState = (0,react.useState)('1'),
    _useState2 = slicedToArray_default()(_useState, 2),
    tabActive = _useState2[0],
    setTabActive = _useState2[1];
  var _useSearchParams = (0,_umi_production_exports.useSearchParams)(),
    _useSearchParams2 = slicedToArray_default()(_useSearchParams, 2),
    searchParams = _useSearchParams2[0],
    setSearchParams = _useSearchParams2[1];
  var _useState3 = (0,react.useState)(false),
    _useState4 = slicedToArray_default()(_useState3, 2),
    loading = _useState4[0],
    setLoading = _useState4[1];
  var _useState5 = (0,react.useState)({}),
    _useState6 = slicedToArray_default()(_useState5, 2),
    cropNote = _useState6[0],
    setCropNote = _useState6[1];
  var _useState7 = (0,react.useState)([]),
    _useState8 = slicedToArray_default()(_useState7, 2),
    cropList = _useState8[0],
    setCropList = _useState8[1];
  var _useState9 = (0,react.useState)([]),
    _useState10 = slicedToArray_default()(_useState9, 2),
    imageLinks = _useState10[0],
    setImageLinks = _useState10[1];
  var _Form$useForm = es_form/* default */.Z.useForm(),
    _Form$useForm2 = slicedToArray_default()(_Form$useForm, 1),
    form = _Form$useForm2[0];
  var taskInfoName = (_searchParams$get = searchParams.get('task_info_id')) !== null && _searchParams$get !== void 0 ? _searchParams$get : '';
  return /*#__PURE__*/(0,jsx_runtime.jsx)(react.Suspense, {
    fallback: /*#__PURE__*/(0,jsx_runtime.jsx)(List/* default */.ZP, {}),
    children: /*#__PURE__*/(0,jsx_runtime.jsx)(jsx_runtime.Fragment, {
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(PageContainer/* PageContainer */._z, {
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(tabs/* default */.Z, {
          activeKey: tabActive,
          onChange: function onChange(e) {
            setTabActive(e);
          },
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(tabs/* default */.Z.TabPane, {
            tab: "Th\\xF4ng tin chi ti\\u1EBFt ghi ch\\xFA",
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(DetailTaskInfoTab, {
              taskID: taskInfoName
            })
          }, "1")
        })
      })
    })
  });
};
/* harmony default export */ var CropLog_DetailTaskInfo = (DetailTaskInfo);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///57404
`)},52662:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){"use strict";eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   EH: function() { return /* binding */ getCrop; },
/* harmony export */   Gz: function() { return /* binding */ getCropManagementInfoList; },
/* harmony export */   Kw: function() { return /* binding */ getCropParticipantsTaskList; },
/* harmony export */   NQ: function() { return /* binding */ getCropWorksheetStatistic; },
/* harmony export */   _R: function() { return /* binding */ getCropItemStatistic; },
/* harmony export */   dK: function() { return /* binding */ getCropNote; },
/* harmony export */   e4: function() { return /* binding */ getCropByTask; },
/* harmony export */   hD: function() { return /* binding */ getCropParticipantsStatistic; },
/* harmony export */   qQ: function() { return /* binding */ getCropProductionStatisticDetailTask; },
/* harmony export */   su: function() { return /* binding */ getCropProductionQuantityStatistic; },
/* harmony export */   vx: function() { return /* binding */ getCropItemStatisticDetailTask; },
/* harmony export */   ym: function() { return /* binding */ getCropPest; }
/* harmony export */ });
/* unused harmony export cropList */
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(97857);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(7837);
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(467);





var CRUD_PATH = {
  CREATE: 'crop',
  READ: 'crop',
  UPDATE: 'crop',
  DELETE: 'crop'
};
var getCropManagementInfoList = /*#__PURE__*/function () {
  var _ref = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/cropManage/crop-management-info'), {
            method: 'GET',
            params: params
          });
        case 2:
          res = _context.sent;
          return _context.abrupt("return", {
            data: res.result
          });
        case 4:
        case "end":
          return _context.stop();
      }
    }, _callee);
  }));
  return function getCropManagementInfoList(_x) {
    return _ref.apply(this, arguments);
  };
}();
var getCropByTask = /*#__PURE__*/function () {
  var _ref2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee2(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          _context2.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/cropManage/crop-by-task'), {
            method: 'GET',
            params: params
          });
        case 2:
          res = _context2.sent;
          return _context2.abrupt("return", {
            data: res.result
          });
        case 4:
        case "end":
          return _context2.stop();
      }
    }, _callee2);
  }));
  return function getCropByTask(_x2) {
    return _ref2.apply(this, arguments);
  };
}();
function cropList(_x3) {
  return _cropList.apply(this, arguments);
}
function _cropList() {
  _cropList = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee13(_ref3) {
    var _ref3$page, page, _ref3$size, size, _ref3$fields, fields, _ref3$filters, filters, _ref3$or_filters, or_filters, _ref3$order_by, order_by, _ref3$group_by, group_by, params, result;
    return _regeneratorRuntime().wrap(function _callee13$(_context13) {
      while (1) switch (_context13.prev = _context13.next) {
        case 0:
          _ref3$page = _ref3.page, page = _ref3$page === void 0 ? 0 : _ref3$page, _ref3$size = _ref3.size, size = _ref3$size === void 0 ? 20 : _ref3$size, _ref3$fields = _ref3.fields, fields = _ref3$fields === void 0 ? ['*'] : _ref3$fields, _ref3$filters = _ref3.filters, filters = _ref3$filters === void 0 ? [] : _ref3$filters, _ref3$or_filters = _ref3.or_filters, or_filters = _ref3$or_filters === void 0 ? [] : _ref3$or_filters, _ref3$order_by = _ref3.order_by, order_by = _ref3$order_by === void 0 ? '' : _ref3$order_by, _ref3$group_by = _ref3.group_by, group_by = _ref3$group_by === void 0 ? '' : _ref3$group_by;
          _context13.prev = 1;
          params = {
            page: page,
            size: size,
            fields: JSON.stringify(fields),
            filters: JSON.stringify(filters),
            or_filters: JSON.stringify(or_filters)
            // order_by,
            // group_by
          };
          _context13.next = 5;
          return request(generateAPIPath("api/v2/cropManage/".concat(CRUD_PATH.READ)), {
            method: 'GET',
            params: params,
            queryParams: params
          });
        case 5:
          result = _context13.sent;
          return _context13.abrupt("return", result.result);
        case 9:
          _context13.prev = 9;
          _context13.t0 = _context13["catch"](1);
          console.log(_context13.t0);
          throw _context13.t0;
        case 13:
        case "end":
          return _context13.stop();
      }
    }, _callee13, null, [[1, 9]]);
  }));
  return _cropList.apply(this, arguments);
}
var getCropNote = /*#__PURE__*/function () {
  var _ref4 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee3(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee3$(_context3) {
      while (1) switch (_context3.prev = _context3.next) {
        case 0:
          _context3.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)("api/v2/cropManage/note"), {
            method: 'GET',
            params: params
          });
        case 2:
          res = _context3.sent;
          return _context3.abrupt("return", {
            data: res.result
          });
        case 4:
        case "end":
          return _context3.stop();
      }
    }, _callee3);
  }));
  return function getCropNote(_x4) {
    return _ref4.apply(this, arguments);
  };
}();
var getCropPest = /*#__PURE__*/function () {
  var _ref5 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee4(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee4$(_context4) {
      while (1) switch (_context4.prev = _context4.next) {
        case 0:
          _context4.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)("api/v2/cropManage/pest"), {
            method: 'GET',
            params: params
          });
        case 2:
          res = _context4.sent;
          console.log(' res.result', res.result);
          return _context4.abrupt("return", {
            data: res.result
          });
        case 5:
        case "end":
          return _context4.stop();
      }
    }, _callee4);
  }));
  return function getCropPest(_x5) {
    return _ref5.apply(this, arguments);
  };
}();
var getCrop = /*#__PURE__*/function () {
  var _ref6 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee5(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee5$(_context5) {
      while (1) switch (_context5.prev = _context5.next) {
        case 0:
          _context5.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/cropManage/crop'), {
            method: 'GET',
            params: params
          });
        case 2:
          res = _context5.sent;
          return _context5.abrupt("return", {
            data: res.result
          });
        case 4:
        case "end":
          return _context5.stop();
      }
    }, _callee5);
  }));
  return function getCrop(_x6) {
    return _ref6.apply(this, arguments);
  };
}();
var getCropItemStatistic = /*#__PURE__*/function () {
  var _ref7 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee6(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee6$(_context6) {
      while (1) switch (_context6.prev = _context6.next) {
        case 0:
          _context6.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/cropManage/statisticItem'), {
            method: 'GET',
            params: params
          });
        case 2:
          res = _context6.sent;
          return _context6.abrupt("return", {
            data: res.result
          });
        case 4:
        case "end":
          return _context6.stop();
      }
    }, _callee6);
  }));
  return function getCropItemStatistic(_x7) {
    return _ref7.apply(this, arguments);
  };
}();
var getCropItemStatisticDetailTask = /*#__PURE__*/function () {
  var _ref8 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee7(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee7$(_context7) {
      while (1) switch (_context7.prev = _context7.next) {
        case 0:
          _context7.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/cropManage/statisticItem/detail-in-crop'), {
            method: 'GET',
            params: params
          });
        case 2:
          res = _context7.sent;
          return _context7.abrupt("return", {
            data: res.result.data,
            pagination: res.result.pagination
          });
        case 4:
        case "end":
          return _context7.stop();
      }
    }, _callee7);
  }));
  return function getCropItemStatisticDetailTask(_x8) {
    return _ref8.apply(this, arguments);
  };
}();
var getCropProductionStatisticDetailTask = /*#__PURE__*/function () {
  var _ref9 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee8(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee8$(_context8) {
      while (1) switch (_context8.prev = _context8.next) {
        case 0:
          _context8.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/cropManage/statisticProductQuantity/detail-in-crop'), {
            method: 'GET',
            params: params
          });
        case 2:
          res = _context8.sent;
          return _context8.abrupt("return", {
            data: res.result.data,
            pagination: res.result.pagination
          });
        case 4:
        case "end":
          return _context8.stop();
      }
    }, _callee8);
  }));
  return function getCropProductionStatisticDetailTask(_x9) {
    return _ref9.apply(this, arguments);
  };
}();
var getCropParticipantsStatistic = /*#__PURE__*/function () {
  var _ref10 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee9(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee9$(_context9) {
      while (1) switch (_context9.prev = _context9.next) {
        case 0:
          _context9.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/cropManage/statisticParticipant'), {
            method: 'GET',
            params: params
          });
        case 2:
          res = _context9.sent;
          return _context9.abrupt("return", {
            data: res.result.data,
            pagination: res.result.pagination
          });
        case 4:
        case "end":
          return _context9.stop();
      }
    }, _callee9);
  }));
  return function getCropParticipantsStatistic(_x10) {
    return _ref10.apply(this, arguments);
  };
}();
var getCropParticipantsTaskList = /*#__PURE__*/function () {
  var _ref11 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee10(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee10$(_context10) {
      while (1) switch (_context10.prev = _context10.next) {
        case 0:
          _context10.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/cropManage/statisticParticipant/detail-task-list'), {
            method: 'GET',
            params: params
          });
        case 2:
          res = _context10.sent;
          return _context10.abrupt("return", {
            data: res.result.data,
            pagination: res.result.pagination
          });
        case 4:
        case "end":
          return _context10.stop();
      }
    }, _callee10);
  }));
  return function getCropParticipantsTaskList(_x11) {
    return _ref11.apply(this, arguments);
  };
}();
var getCropProductionQuantityStatistic = /*#__PURE__*/function () {
  var _ref12 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee11(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee11$(_context11) {
      while (1) switch (_context11.prev = _context11.next) {
        case 0:
          _context11.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/cropManage/statisticProductQuantity'), {
            method: 'GET',
            params: params
          });
        case 2:
          res = _context11.sent;
          return _context11.abrupt("return", {
            data: res.result
          });
        case 4:
        case "end":
          return _context11.stop();
      }
    }, _callee11);
  }));
  return function getCropProductionQuantityStatistic(_x12) {
    return _ref12.apply(this, arguments);
  };
}();
var getCropWorksheetStatistic = /*#__PURE__*/function () {
  var _ref13 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee12(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee12$(_context12) {
      while (1) switch (_context12.prev = _context12.next) {
        case 0:
          _context12.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/cropManage/statisticWorksheet'), {
            method: 'GET',
            params: params
          });
        case 2:
          res = _context12.sent;
          return _context12.abrupt("return", {
            data: res.result.map(function (stat) {
              return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default()({}, stat), {}, {
                type: stat.type.toLowerCase() === 'hour' ? 'Gi\u1EDD' : 'C\xF4ng'
              });
            })
          });
        case 4:
        case "end":
          return _context12.stop();
      }
    }, _callee12);
  }));
  return function getCropWorksheetStatistic(_x13) {
    return _ref13.apply(this, arguments);
  };
}();//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///52662
`)},75302:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){"use strict";eval(`/* harmony import */ var _hooks_useBreakpoint__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(25378);
"use client";




// Do not export params
function useBreakpoint() {
  return (0,_hooks_useBreakpoint__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .Z)();
}

/* harmony default export */ __webpack_exports__.ZP = ({
  useBreakpoint
});//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNzUzMDIuanMiLCJtYXBwaW5ncyI6IjtBQUFBOztBQUV3QjtBQUNrQztBQUNsQztBQUN4QjtBQUNBO0FBQ0EsU0FBUyx5RUFBcUI7QUFDOUI7QUFDb0I7QUFDcEIsdURBQWU7QUFDZjtBQUNBLENBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hbnQtZGVzaWduLXByby8uL25vZGVfbW9kdWxlcy9hbnRkL2VzL2dyaWQvaW5kZXguanM/YTQxNyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcblxuaW1wb3J0IENvbCBmcm9tICcuL2NvbCc7XG5pbXBvcnQgdXNlSW50ZXJuYWxCcmVha3BvaW50IGZyb20gJy4vaG9va3MvdXNlQnJlYWtwb2ludCc7XG5pbXBvcnQgUm93IGZyb20gJy4vcm93Jztcbi8vIERvIG5vdCBleHBvcnQgcGFyYW1zXG5mdW5jdGlvbiB1c2VCcmVha3BvaW50KCkge1xuICByZXR1cm4gdXNlSW50ZXJuYWxCcmVha3BvaW50KCk7XG59XG5leHBvcnQgeyBDb2wsIFJvdyB9O1xuZXhwb3J0IGRlZmF1bHQge1xuICB1c2VCcmVha3BvaW50XG59OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==
//# sourceURL=webpack-internal:///75302
`)},59542:function(module){eval(`!function(e,t){ true?module.exports=t():0}(this,(function(){"use strict";var e="day";return function(t,i,s){var a=function(t){return t.add(4-t.isoWeekday(),e)},d=i.prototype;d.isoWeekYear=function(){return a(this).year()},d.isoWeek=function(t){if(!this.$utils().u(t))return this.add(7*(t-this.isoWeek()),e);var i,d,n,o,r=a(this),u=(i=this.isoWeekYear(),d=this.$u,n=(d?s.utc:s)().year(i).startOf("year"),o=4-n.isoWeekday(),n.isoWeekday()>4&&(o+=7),n.add(o,e));return r.diff(u,"week")+1},d.isoWeekday=function(e){return this.$utils().u(e)?this.day()||7:this.day(this.day()%7?e:e-7)};var n=d.startOf;d.startOf=function(e,t){var i=this.$utils(),s=!!i.u(t)||t;return"isoweek"===i.p(e)?s?this.date(this.date()-(this.isoWeekday()-1)).startOf("day"):this.date(this.date()-1-(this.isoWeekday()-1)+7).endOf("day"):n.bind(this)(e,t)}}}));//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNTk1NDIuanMiLCJtYXBwaW5ncyI6IkFBQUEsZUFBZSxLQUFvRCxvQkFBb0IsQ0FBOEgsQ0FBQyxrQkFBa0IsYUFBYSxZQUFZLHVCQUF1QixrQkFBa0IsaUNBQWlDLGVBQWUseUJBQXlCLHNCQUFzQix1QkFBdUIsK0RBQStELHdKQUF3SiwwQkFBMEIsMEJBQTBCLHNFQUFzRSxnQkFBZ0Isd0JBQXdCLGtDQUFrQyx5S0FBeUsiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hbnQtZGVzaWduLXByby8uL25vZGVfbW9kdWxlcy9kYXlqcy9wbHVnaW4vaXNvV2Vlay5qcz80NGI0Il0sInNvdXJjZXNDb250ZW50IjpbIiFmdW5jdGlvbihlLHQpe1wib2JqZWN0XCI9PXR5cGVvZiBleHBvcnRzJiZcInVuZGVmaW5lZFwiIT10eXBlb2YgbW9kdWxlP21vZHVsZS5leHBvcnRzPXQoKTpcImZ1bmN0aW9uXCI9PXR5cGVvZiBkZWZpbmUmJmRlZmluZS5hbWQ/ZGVmaW5lKHQpOihlPVwidW5kZWZpbmVkXCIhPXR5cGVvZiBnbG9iYWxUaGlzP2dsb2JhbFRoaXM6ZXx8c2VsZikuZGF5anNfcGx1Z2luX2lzb1dlZWs9dCgpfSh0aGlzLChmdW5jdGlvbigpe1widXNlIHN0cmljdFwiO3ZhciBlPVwiZGF5XCI7cmV0dXJuIGZ1bmN0aW9uKHQsaSxzKXt2YXIgYT1mdW5jdGlvbih0KXtyZXR1cm4gdC5hZGQoNC10Lmlzb1dlZWtkYXkoKSxlKX0sZD1pLnByb3RvdHlwZTtkLmlzb1dlZWtZZWFyPWZ1bmN0aW9uKCl7cmV0dXJuIGEodGhpcykueWVhcigpfSxkLmlzb1dlZWs9ZnVuY3Rpb24odCl7aWYoIXRoaXMuJHV0aWxzKCkudSh0KSlyZXR1cm4gdGhpcy5hZGQoNyoodC10aGlzLmlzb1dlZWsoKSksZSk7dmFyIGksZCxuLG8scj1hKHRoaXMpLHU9KGk9dGhpcy5pc29XZWVrWWVhcigpLGQ9dGhpcy4kdSxuPShkP3MudXRjOnMpKCkueWVhcihpKS5zdGFydE9mKFwieWVhclwiKSxvPTQtbi5pc29XZWVrZGF5KCksbi5pc29XZWVrZGF5KCk+NCYmKG8rPTcpLG4uYWRkKG8sZSkpO3JldHVybiByLmRpZmYodSxcIndlZWtcIikrMX0sZC5pc29XZWVrZGF5PWZ1bmN0aW9uKGUpe3JldHVybiB0aGlzLiR1dGlscygpLnUoZSk/dGhpcy5kYXkoKXx8Nzp0aGlzLmRheSh0aGlzLmRheSgpJTc/ZTplLTcpfTt2YXIgbj1kLnN0YXJ0T2Y7ZC5zdGFydE9mPWZ1bmN0aW9uKGUsdCl7dmFyIGk9dGhpcy4kdXRpbHMoKSxzPSEhaS51KHQpfHx0O3JldHVyblwiaXNvd2Vla1wiPT09aS5wKGUpP3M/dGhpcy5kYXRlKHRoaXMuZGF0ZSgpLSh0aGlzLmlzb1dlZWtkYXkoKS0xKSkuc3RhcnRPZihcImRheVwiKTp0aGlzLmRhdGUodGhpcy5kYXRlKCktMS0odGhpcy5pc29XZWVrZGF5KCktMSkrNykuZW5kT2YoXCJkYXlcIik6bi5iaW5kKHRoaXMpKGUsdCl9fX0pKTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=
//# sourceURL=webpack-internal:///59542
`)}}]);
