"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[8903],{27363:function(__unused_webpack_module,__webpack_exports__){eval(`// This icon file is generated automatically.
var EditOutlined = { "icon": { "tag": "svg", "attrs": { "viewBox": "64 64 896 896", "focusable": "false" }, "children": [{ "tag": "path", "attrs": { "d": "M257.7 752c2 0 4-.2 6-.5L431.9 722c2-.4 3.9-1.3 5.3-2.8l423.9-423.9a9.96 9.96 0 000-14.1L694.9 114.9c-1.9-1.9-4.4-2.9-7.1-2.9s-5.2 1-7.1 2.9L256.8 538.8c-1.5 1.5-2.4 3.3-2.8 5.3l-29.5 168.2a33.5 33.5 0 009.4 29.8c6.6 6.4 14.9 9.9 23.8 9.9zm67.4-174.4L687.8 215l73.3 73.3-362.7 362.6-88.9 15.7 15.6-89zM880 836H144c-17.7 0-32 14.3-32 32v36c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-36c0-17.7-14.3-32-32-32z" } }] }, "name": "edit", "theme": "outlined" };
/* harmony default export */ __webpack_exports__.Z = (EditOutlined);
//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiMjczNjMuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQSxxQkFBcUIsVUFBVSx5QkFBeUIsa0RBQWtELGlCQUFpQiwwQkFBMEIsMlpBQTJaLEdBQUc7QUFDbmpCLHNEQUFlLFlBQVksRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL2FudC1kZXNpZ24tcHJvLy4vbm9kZV9tb2R1bGVzL0BhbnQtZGVzaWduL2ljb25zLXN2Zy9lcy9hc24vRWRpdE91dGxpbmVkLmpzP2YxMjYiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gVGhpcyBpY29uIGZpbGUgaXMgZ2VuZXJhdGVkIGF1dG9tYXRpY2FsbHkuXG52YXIgRWRpdE91dGxpbmVkID0geyBcImljb25cIjogeyBcInRhZ1wiOiBcInN2Z1wiLCBcImF0dHJzXCI6IHsgXCJ2aWV3Qm94XCI6IFwiNjQgNjQgODk2IDg5NlwiLCBcImZvY3VzYWJsZVwiOiBcImZhbHNlXCIgfSwgXCJjaGlsZHJlblwiOiBbeyBcInRhZ1wiOiBcInBhdGhcIiwgXCJhdHRyc1wiOiB7IFwiZFwiOiBcIk0yNTcuNyA3NTJjMiAwIDQtLjIgNi0uNUw0MzEuOSA3MjJjMi0uNCAzLjktMS4zIDUuMy0yLjhsNDIzLjktNDIzLjlhOS45NiA5Ljk2IDAgMDAwLTE0LjFMNjk0LjkgMTE0LjljLTEuOS0xLjktNC40LTIuOS03LjEtMi45cy01LjIgMS03LjEgMi45TDI1Ni44IDUzOC44Yy0xLjUgMS41LTIuNCAzLjMtMi44IDUuM2wtMjkuNSAxNjguMmEzMy41IDMzLjUgMCAwMDkuNCAyOS44YzYuNiA2LjQgMTQuOSA5LjkgMjMuOCA5Ljl6bTY3LjQtMTc0LjRMNjg3LjggMjE1bDczLjMgNzMuMy0zNjIuNyAzNjIuNi04OC45IDE1LjcgMTUuNi04OXpNODgwIDgzNkgxNDRjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjM2YzAgNC40IDMuNiA4IDggOGg3ODRjNC40IDAgOC0zLjYgOC04di0zNmMwLTE3LjctMTQuMy0zMi0zMi0zMnpcIiB9IH1dIH0sIFwibmFtZVwiOiBcImVkaXRcIiwgXCJ0aGVtZVwiOiBcIm91dGxpbmVkXCIgfTtcbmV4cG9ydCBkZWZhdWx0IEVkaXRPdXRsaW5lZDtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==
//# sourceURL=webpack-internal:///27363
`)},82061:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(1413);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(67294);
/* harmony import */ var _ant_design_icons_svg_es_asn_DeleteOutlined__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(47046);
/* harmony import */ var _components_AntdIcon__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(89099);

// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY



var DeleteOutlined = function DeleteOutlined(props, ref) {
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(_components_AntdIcon__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Z, (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z)((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z)({}, props), {}, {
    ref: ref,
    icon: _ant_design_icons_svg_es_asn_DeleteOutlined__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z
  }));
};
DeleteOutlined.displayName = 'DeleteOutlined';
/* harmony default export */ __webpack_exports__.Z = (/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(DeleteOutlined));//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiODIwNjEuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFxRTtBQUNyRTtBQUNBO0FBQytCO0FBQzZDO0FBQzlCO0FBQzlDO0FBQ0Esc0JBQXNCLGdEQUFtQixDQUFDLHFFQUFRLEVBQUUsNkZBQWEsQ0FBQyw2RkFBYSxHQUFHLFlBQVk7QUFDOUY7QUFDQSxVQUFVLDRGQUFpQjtBQUMzQixHQUFHO0FBQ0g7QUFDQTtBQUNBLG1FQUE0Qiw2Q0FBZ0IsZ0JBQWdCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvQGFudC1kZXNpZ24vaWNvbnMvZXMvaWNvbnMvRGVsZXRlT3V0bGluZWQuanM/NGRkNyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgX29iamVjdFNwcmVhZCBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vb2JqZWN0U3ByZWFkMlwiO1xuLy8gR0VORVJBVEUgQlkgLi9zY3JpcHRzL2dlbmVyYXRlLnRzXG4vLyBET04gTk9UIEVESVQgSVQgTUFOVUFMTFlcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCBEZWxldGVPdXRsaW5lZFN2ZyBmcm9tIFwiQGFudC1kZXNpZ24vaWNvbnMtc3ZnL2VzL2Fzbi9EZWxldGVPdXRsaW5lZFwiO1xuaW1wb3J0IEFudGRJY29uIGZyb20gJy4uL2NvbXBvbmVudHMvQW50ZEljb24nO1xudmFyIERlbGV0ZU91dGxpbmVkID0gZnVuY3Rpb24gRGVsZXRlT3V0bGluZWQocHJvcHMsIHJlZikge1xuICByZXR1cm4gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoQW50ZEljb24sIF9vYmplY3RTcHJlYWQoX29iamVjdFNwcmVhZCh7fSwgcHJvcHMpLCB7fSwge1xuICAgIHJlZjogcmVmLFxuICAgIGljb246IERlbGV0ZU91dGxpbmVkU3ZnXG4gIH0pKTtcbn07XG5EZWxldGVPdXRsaW5lZC5kaXNwbGF5TmFtZSA9ICdEZWxldGVPdXRsaW5lZCc7XG5leHBvcnQgZGVmYXVsdCAvKiNfX1BVUkVfXyovUmVhY3QuZm9yd2FyZFJlZihEZWxldGVPdXRsaW5lZCk7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9
//# sourceURL=webpack-internal:///82061
`)},47033:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(93967);
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(85893);



var ActionHover = function ActionHover(_ref) {
  var children = _ref.children,
    actions = _ref.actions;
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)("div", {
    className: "relative ",
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)("div", {
      children: children
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)("div", {
      className: classnames__WEBPACK_IMPORTED_MODULE_0___default()('absolute bg-white bg-opacity-80 backdrop:blur-sm opacity-0 inset-y-0 -right-1/2 invisible  group-hover/action:visible group-hover/action:right-0 group-hover/action:opacity-100 duration-100 ease-out'),
      children: actions === null || actions === void 0 ? void 0 : actions()
    })]
  });
};
/* harmony default export */ __webpack_exports__.Z = (ActionHover);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNDcwMzMuanMiLCJtYXBwaW5ncyI6Ijs7O0FBQW9DO0FBQUE7QUFBQTtBQU9wQyxJQUFNSyxXQUFpQyxHQUFHLFNBQXBDQSxXQUFpQ0EsQ0FBQUMsSUFBQSxFQUE4QjtFQUFBLElBQXhCQyxRQUFRLEdBQUFELElBQUEsQ0FBUkMsUUFBUTtJQUFFQyxPQUFPLEdBQUFGLElBQUEsQ0FBUEUsT0FBTztFQUM1RCxvQkFDRUosdURBQUE7SUFBS0ssU0FBUyxFQUFDLFdBQVc7SUFBQUYsUUFBQSxnQkFDeEJMLHNEQUFBO01BQUFLLFFBQUEsRUFBTUE7SUFBUSxDQUFNLENBQUMsZUFDckJMLHNEQUFBO01BQ0VPLFNBQVMsRUFBRVQsaURBQVUsQ0FDbkIsdU1BQ0YsQ0FBRTtNQUFBTyxRQUFBLEVBRURDLE9BQU8sYUFBUEEsT0FBTyx1QkFBUEEsT0FBTyxDQUFHO0lBQUMsQ0FDVCxDQUFDO0VBQUEsQ0FDSCxDQUFDO0FBRVYsQ0FBQztBQUVELHNEQUFlSCxXQUFXIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9zcmMvY29tcG9uZW50cy9BY3Rpb25Ib3Zlci9pbmRleC50c3g/OTU2ZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY2xhc3NOYW1lcyBmcm9tICdjbGFzc25hbWVzJztcclxuaW1wb3J0IHsgRkMsIFJlYWN0Tm9kZSB9IGZyb20gJ3JlYWN0JztcclxuXHJcbmludGVyZmFjZSBBY3Rpb25Ib3ZlclByb3BzIHtcclxuICBjaGlsZHJlbj86IFJlYWN0Tm9kZTtcclxuICBhY3Rpb25zPzogKCkgPT4gUmVhY3ROb2RlO1xyXG59XHJcbmNvbnN0IEFjdGlvbkhvdmVyOiBGQzxBY3Rpb25Ib3ZlclByb3BzPiA9ICh7IGNoaWxkcmVuLCBhY3Rpb25zIH0pID0+IHtcclxuICByZXR1cm4gKFxyXG4gICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSBcIj5cclxuICAgICAgPGRpdj57Y2hpbGRyZW59PC9kaXY+XHJcbiAgICAgIDxkaXZcclxuICAgICAgICBjbGFzc05hbWU9e2NsYXNzTmFtZXMoXHJcbiAgICAgICAgICAnYWJzb2x1dGUgYmctd2hpdGUgYmctb3BhY2l0eS04MCBiYWNrZHJvcDpibHVyLXNtIG9wYWNpdHktMCBpbnNldC15LTAgLXJpZ2h0LTEvMiBpbnZpc2libGUgIGdyb3VwLWhvdmVyL2FjdGlvbjp2aXNpYmxlIGdyb3VwLWhvdmVyL2FjdGlvbjpyaWdodC0wIGdyb3VwLWhvdmVyL2FjdGlvbjpvcGFjaXR5LTEwMCBkdXJhdGlvbi0xMDAgZWFzZS1vdXQnLFxyXG4gICAgICAgICl9XHJcbiAgICAgID5cclxuICAgICAgICB7YWN0aW9ucz8uKCl9XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgPC9kaXY+XHJcbiAgKTtcclxufTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IEFjdGlvbkhvdmVyO1xyXG4iXSwibmFtZXMiOlsiY2xhc3NOYW1lcyIsImpzeCIsIl9qc3giLCJqc3hzIiwiX2pzeHMiLCJBY3Rpb25Ib3ZlciIsIl9yZWYiLCJjaGlsZHJlbiIsImFjdGlvbnMiLCJjbGFzc05hbWUiXSwic291cmNlUm9vdCI6IiJ9
//# sourceURL=webpack-internal:///47033
`)},76020:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(97857);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(82061);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(7837);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(31418);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(14726);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(67294);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(85893);






var ActionModalConfirm = function ActionModalConfirm(_ref) {
  var modalProps = _ref.modalProps,
    btnProps = _ref.btnProps,
    isDelete = _ref.isDelete;
  var _App$useApp = antd__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z.useApp(),
    modal = _App$useApp.modal;
  var _useIntl = (0,_umijs_max__WEBPACK_IMPORTED_MODULE_1__.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  var onClick = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    modal.confirm(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default()({}, modalProps), {}, {
      title: isDelete ? formatMessage({
        id: 'common.sentences.confirm-delete'
      }) : formatMessage({
        id: 'action.confirm'
      }),
      okButtonProps: D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default()({
        danger: true
      }, modalProps === null || modalProps === void 0 ? void 0 : modalProps.okButtonProps)
    }));
  }, [modal, modalProps, btnProps]);
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(antd__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .ZP, D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default()({
    danger: true,
    icon: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Z, {}),
    size: "small",
    onClick: onClick
  }, btnProps));
};
/* harmony default export */ __webpack_exports__.Z = (ActionModalConfirm);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///76020
`)},81169:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(7837);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(31418);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(67294);



var useUnsavedChangesModal = function useUnsavedChangesModal(isFormDirty) {
  var _useIntl = (0,_umijs_max__WEBPACK_IMPORTED_MODULE_0__.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  var _App$useApp = antd__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z.useApp(),
    modal = _App$useApp.modal;
  var confirmNavigation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (callback) {
    if (isFormDirty) {
      modal.confirm({
        title: formatMessage({
          id: 'common.unsaved_changes'
        }),
        content: formatMessage({
          id: 'common.confirm_leave'
        }),
        onOk: callback,
        okButtonProps: {
          danger: true
        }
      });
    } else {
      callback();
    }
  }, [isFormDirty, formatMessage]);
  return confirmNavigation;
};
/* harmony default export */ __webpack_exports__.Z = (useUnsavedChangesModal);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///81169
`)},67152:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return /* binding */ StageOfCrop; }
});

// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/slicedToArray.js
var slicedToArray = __webpack_require__(5574);
var slicedToArray_default = /*#__PURE__*/__webpack_require__.n(slicedToArray);
// EXTERNAL MODULE: ./src/components/UnsavedChangesModal/index.tsx
var UnsavedChangesModal = __webpack_require__(81169);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/PlusOutlined.js
var PlusOutlined = __webpack_require__(51042);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-layout/es/components/PageContainer/index.js + 18 modules
var PageContainer = __webpack_require__(6110);
// EXTERNAL MODULE: ./src/.umi-production/exports.ts + 34 modules
var _umi_production_exports = __webpack_require__(7837);
// EXTERNAL MODULE: ./node_modules/antd/es/button/index.js + 9 modules
var es_button = __webpack_require__(14726);
// EXTERNAL MODULE: ./node_modules/antd/es/row/index.js
var row = __webpack_require__(71230);
// EXTERNAL MODULE: ./node_modules/antd/es/col/index.js
var col = __webpack_require__(15746);
// EXTERNAL MODULE: ./node_modules/nanoid/index.browser.js
var index_browser = __webpack_require__(53416);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./src/pages/FarmingDiaryStatic/StageOfCrop/components/Create/index.tsx + 3 modules
var Create = __webpack_require__(47296);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/regeneratorRuntime.js
var regeneratorRuntime = __webpack_require__(15009);
var regeneratorRuntime_default = /*#__PURE__*/__webpack_require__.n(regeneratorRuntime);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(99289);
var asyncToGenerator_default = /*#__PURE__*/__webpack_require__.n(asyncToGenerator);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/objectSpread2.js
var objectSpread2 = __webpack_require__(97857);
var objectSpread2_default = /*#__PURE__*/__webpack_require__.n(objectSpread2);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/layouts/ProForm/index.js + 3 modules
var ProForm = __webpack_require__(34994);
// EXTERNAL MODULE: ./node_modules/antd/es/spin/index.js + 2 modules
var spin = __webpack_require__(75081);
// EXTERNAL MODULE: ./src/common/contanst/constanst.ts
var constanst = __webpack_require__(86604);
// EXTERNAL MODULE: ./src/services/diary-2/stage.ts
var stage = __webpack_require__(82865);
;// CONCATENATED MODULE: ./src/pages/FarmingDiaryStatic/StageOfCrop/hooks/useDetail.ts





function useDetail() {
  var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},
    id = _ref.id,
    _onSuccess = _ref.onSuccess;
  return (0,_umi_production_exports.useRequest)( /*#__PURE__*/asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
    var _res$data;
    var res, data;
    return regeneratorRuntime_default()().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          if (id) {
            _context.next = 2;
            break;
          }
          return _context.abrupt("return", {
            data: null
          });
        case 2:
          _context.next = 4;
          return (0,stage/* getStageList */.bp)({
            filters: [[constanst/* DOCTYPE_ERP */.lH.iot_diary_v2_state, 'name', '=', id]],
            order_by: 'name asc',
            page: 1,
            size: 1
          });
        case 4:
          res = _context.sent;
          data = res === null || res === void 0 || (_res$data = res.data) === null || _res$data === void 0 ? void 0 : _res$data[0];
          if (data) {
            _context.next = 8;
            break;
          }
          throw new Error('Not found');
        case 8:
          return _context.abrupt("return", {
            data: data
          });
        case 9:
        case "end":
          return _context.stop();
      }
    }, _callee);
  })), {
    onSuccess: function onSuccess(data) {
      if (data) _onSuccess === null || _onSuccess === void 0 || _onSuccess(data);
    },
    refreshDeps: [id]
  });
}
// EXTERNAL MODULE: ./node_modules/antd/es/app/index.js + 1 modules
var app = __webpack_require__(31418);
;// CONCATENATED MODULE: ./src/pages/FarmingDiaryStatic/StageOfCrop/hooks/useUpdate.ts



function useUpdate() {
  var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},
    _onSuccess = _ref.onSuccess;
  var _App$useApp = app/* default */.Z.useApp(),
    message = _App$useApp.message;
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  return (0,_umi_production_exports.useRequest)(stage/* updateStage */.xn, {
    manual: true,
    onSuccess: function onSuccess(data, params) {
      message.success(formatMessage({
        id: 'common.success'
      }));
      _onSuccess === null || _onSuccess === void 0 || _onSuccess();
    },
    onError: function onError(error) {
      message.error(error.message || formatMessage({
        id: 'common.error'
      }));
    }
  });
}
// EXTERNAL MODULE: ./src/components/FormUploadsPreviewable/index.tsx
var FormUploadsPreviewable = __webpack_require__(97679);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/Text/index.js
var Text = __webpack_require__(5966);
// EXTERNAL MODULE: ./node_modules/antd/es/card/index.js + 3 modules
var card = __webpack_require__(76216);
// EXTERNAL MODULE: ./node_modules/react/jsx-runtime.js
var jsx_runtime = __webpack_require__(85893);
;// CONCATENATED MODULE: ./src/pages/FarmingDiaryStatic/StageOfCrop/components/Edit/DetailedInfo.tsx






var w = 'md';
var DetailedInfo = function DetailedInfo(_ref) {
  var children = _ref.children,
    initialImage = _ref.initialImage;
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  return /*#__PURE__*/(0,jsx_runtime.jsxs)(card/* default */.Z, {
    title: formatMessage({
      id: 'task.detailed_info'
    }),
    bordered: false,
    style: {
      boxShadow: 'none'
    },
    children: [/*#__PURE__*/(0,jsx_runtime.jsx)(FormUploadsPreviewable/* default */.Z, {
      label: formatMessage({
        id: 'common.image'
      }),
      fileLimit: 10,
      formItemName: 'image',
      initialImages: initialImage
    }), /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
      gutter: 24,
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 12,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(Text/* default */.Z, {
          width: w,
          name: 'label',
          label: formatMessage({
            id: 'common.stage_name'
          }),
          rules: [{
            required: true
          }]
        })
      }), /*#__PURE__*/(0,jsx_runtime.jsxs)(col/* default */.Z, {
        span: 12,
        children: [' ', /*#__PURE__*/(0,jsx_runtime.jsx)(Text/* default */.Z, {
          width: w,
          name: 'description',
          label: formatMessage({
            id: 'common.note'
          })
        })]
      }), /*#__PURE__*/(0,jsx_runtime.jsxs)(col/* default */.Z, {
        span: 12,
        children: [' ', /*#__PURE__*/(0,jsx_runtime.jsx)(Text/* default */.Z, {
          width: w,
          name: 'expire_time_in_days',
          label: formatMessage({
            id: 'common.expire_time_in_days'
          }),
          disabled: true
        })]
      })]
    })]
  });
};
/* harmony default export */ var Edit_DetailedInfo = (DetailedInfo);
// EXTERNAL MODULE: ./src/components/Form/Config/pro-form-list.tsx
var pro_form_list = __webpack_require__(23281);
// EXTERNAL MODULE: ./src/services/diary-2/task.ts
var task = __webpack_require__(1018);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/List/index.js + 6 modules
var List = __webpack_require__(55895);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/Select/index.js
var Select = __webpack_require__(64317);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/Digit/index.js
var Digit = __webpack_require__(31199);
// EXTERNAL MODULE: ./node_modules/antd/es/tag/index.js + 5 modules
var tag = __webpack_require__(66309);
;// CONCATENATED MODULE: ./src/pages/FarmingDiaryStatic/StageOfCrop/components/Edit/Task.tsx













var getTagColor = function getTagColor(item) {
  switch (item) {
    case 'Priority':
      return constanst/* TAG_COLOR */.Q2.PRIORITY;
    case 'Important':
      return constanst/* TAG_COLOR */.Q2.IMPORTANT;
    case 'Common':
      return constanst/* TAG_COLOR */.Q2.COMMON;
    default:
      return 'default';
  }
};
var Task = function Task() {
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  var _useRequest = (0,_umi_production_exports.useRequest)(task/* getTaskList */.ZS),
    tasks = _useRequest.data,
    tasksLoading = _useRequest.loading;
  var form = ProForm/* ProForm */.A.useFormInstance();
  var formListProps = (0,pro_form_list/* useProFormList */.r)();
  var _useParams = (0,_umi_production_exports.useParams)(),
    id = _useParams.id;
  var handleTaskChange = (0,react.useCallback)(function (selectedTaskName) {
    var selectedTask = tasks === null || tasks === void 0 ? void 0 : tasks.find(function (task) {
      return task.name === selectedTaskName;
    });
    if (!selectedTask) return;
    var currentTasks = form.getFieldValue('tasks') || [];
    var updatedTasks = currentTasks.map(function (task, taskIndex) {
      return task.name === selectedTaskName ? objectSpread2_default()(objectSpread2_default()({}, task), {}, {
        idx: taskIndex + 1,
        // B\u1ED5 sung idx n\u1EBFu ch\u01B0a c\xF3
        level: selectedTask.level,
        expire_time_in_days: selectedTask.expire_time_in_days,
        execution_day: selectedTask.execution_day,
        supplies: selectedTask.related_items.map(function (related_item) {
          return " ".concat(related_item.quantity, " ").concat(related_item.uom_name, " ").concat(related_item.label);
        }).join(',')
      }) : task;
    });
    form.setFieldsValue({
      tasks: updatedTasks
    });
  }, [tasks, form]);
  return /*#__PURE__*/(0,jsx_runtime.jsx)(card/* default */.Z, {
    title: formatMessage({
      id: 'common.task'
    }),
    bordered: false,
    style: {
      boxShadow: 'none'
    },
    extra: /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
      onClick: function onClick() {
        _umi_production_exports.history.push('/farming-diary-static/task/create', {
          fromStageEdit: true,
          id: id
        });
      },
      icon: /*#__PURE__*/(0,jsx_runtime.jsx)(PlusOutlined/* default */.Z, {}),
      type: "default",
      children: formatMessage({
        id: 'common.create-task'
      })
    }),
    children: /*#__PURE__*/(0,jsx_runtime.jsx)(List/* ProFormList */.u, objectSpread2_default()(objectSpread2_default()({
      name: "tasks"
    }, formListProps), {}, {
      creatorButtonProps: {
        style: {
          width: '100%'
        },
        // Set the width to 100%
        className: 'mx-auto mt-4' // Center align the button and add margin-top
      },
      children: function children(d, index) {
        return /*#__PURE__*/(0,jsx_runtime.jsx)(card/* default */.Z, {
          style: {
            margin: 6
          },
          children: /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
            gutter: 24,
            children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
              span: 8,
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
                required: true,
                onChange: handleTaskChange,
                fieldProps: {
                  loading: tasksLoading
                },
                options: tasks === null || tasks === void 0 ? void 0 : tasks.map(function (task) {
                  return {
                    label: task.label,
                    value: task.name
                  };
                }),
                name: "name",
                label: "".concat(index + 1, ". ").concat(formatMessage({
                  id: 'common.task_name'
                })),
                showSearch: true
              })
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
              span: 8,
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
                disabled: true,
                label: formatMessage({
                  id: 'common.level'
                }),
                name: "level",
                request: /*#__PURE__*/asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
                  return regeneratorRuntime_default()().wrap(function _callee$(_context) {
                    while (1) switch (_context.prev = _context.next) {
                      case 0:
                        return _context.abrupt("return", ['Common', 'Important', 'Priority'].map(function (item) {
                          return {
                            label: /*#__PURE__*/(0,jsx_runtime.jsx)(tag/* default */.Z, {
                              color: getTagColor(item),
                              children: item
                            }),
                            value: item
                          };
                        }));
                      case 1:
                      case "end":
                        return _context.stop();
                    }
                  }, _callee);
                })),
                placeholder: ""
              })
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
              span: 8,
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(Digit/* default */.Z, {
                disabled: true,
                label: formatMessage({
                  id: 'common.execution_time'
                }),
                name: "execution_day",
                fieldProps: {
                  prefix: formatMessage({
                    id: 'common.days'
                  })
                },
                placeholder: ""
              })
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
              span: 8,
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(Digit/* default */.Z, {
                disabled: true,
                label: formatMessage({
                  id: 'common.expire_time_in_days'
                }),
                name: "expire_time_in_days",
                fieldProps: {
                  suffix: formatMessage({
                    id: 'common.days'
                  })
                },
                placeholder: ""
              })
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
              span: 16,
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(Text/* default */.Z, {
                disabled: true,
                label: formatMessage({
                  id: 'common.supplies'
                }),
                name: "supplies",
                placeholder: ""
              })
            })]
          })
        }, index);
      }
    }))
  });
};
/* harmony default export */ var Edit_Task = (Task);
;// CONCATENATED MODULE: ./src/pages/FarmingDiaryStatic/StageOfCrop/components/Edit/index.tsx














var StageOfCountEdit = function StageOfCountEdit(_ref) {
  var children = _ref.children,
    id = _ref.id,
    _onSuccess = _ref.onSuccess,
    _ref$setIsFormDirty = _ref.setIsFormDirty,
    setIsFormDirty = _ref$setIsFormDirty === void 0 ? function () {} : _ref$setIsFormDirty;
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  var _ProForm$useForm = ProForm/* ProForm */.A.useForm(),
    _ProForm$useForm2 = slicedToArray_default()(_ProForm$useForm, 1),
    form = _ProForm$useForm2[0];
  var _useDetail = useDetail({
      id: id,
      onSuccess: function onSuccess(data) {
        var _data$tasks;
        form.setFieldsValue(objectSpread2_default()(objectSpread2_default()({}, data), {}, {
          tasks: (_data$tasks = data.tasks) === null || _data$tasks === void 0 ? void 0 : _data$tasks.map(function (task) {
            var _task$related_items;
            return objectSpread2_default()(objectSpread2_default()({}, task), {}, {
              supplies: task === null || task === void 0 || (_task$related_items = task.related_items) === null || _task$related_items === void 0 ? void 0 : _task$related_items.map(function (related_item) {
                return " ".concat(related_item.quantity, " ").concat(related_item.label);
              }).join(',')
            });
          })
        }));
      }
    }),
    data = _useDetail.data,
    loading = _useDetail.loading;
  var _useUpdate = useUpdate({
      onSuccess: function onSuccess() {
        if (_onSuccess) {
          _onSuccess();
        }
      }
    }),
    run = _useUpdate.run;
  (0,react.useEffect)(function () {
    setIsFormDirty(false);
  }, [data]);
  return /*#__PURE__*/(0,jsx_runtime.jsx)(spin/* default */.Z, {
    spinning: loading,
    children: /*#__PURE__*/(0,jsx_runtime.jsx)(ProForm/* ProForm */.A, {
      form: form,
      onValuesChange: function onValuesChange() {
        return setIsFormDirty(true);
      },
      onFinish: ( /*#__PURE__*/function () {
        var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(values) {
          var _values$tasks;
          return regeneratorRuntime_default()().wrap(function _callee$(_context) {
            while (1) switch (_context.prev = _context.next) {
              case 0:
                _context.next = 2;
                return run({
                  name: id,
                  label: values.label,
                  description: values.description,
                  expire_time_in_days: values.expire_time_in_days || 0,
                  image: values.image,
                  task_count: values.tasks.length,
                  is_deleted: 0,
                  tasks: (_values$tasks = values.tasks) === null || _values$tasks === void 0 ? void 0 : _values$tasks.map(function (task) {
                    return {
                      name: task.name,
                      idx: task.idx || 0
                    };
                  })
                });
              case 2:
                setIsFormDirty(false);
                return _context.abrupt("return", true);
              case 4:
              case "end":
                return _context.stop();
            }
          }, _callee);
        }));
        return function (_x) {
          return _ref2.apply(this, arguments);
        };
      }()),
      submitter: {
        render: function render(_, dom) {
          return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
            style: {
              textAlign: 'right',
              margin: 24
            },
            children: dom.map(function (item, index) {
              return /*#__PURE__*/(0,jsx_runtime.jsx)("span", {
                style: {
                  marginRight: index === 0 ? 8 : 0
                },
                children: item
              }, index);
            })
          });
        }
      },
      children: /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
        className: "space-y-4 mb-4",
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)(Edit_DetailedInfo, {
          initialImage: data === null || data === void 0 ? void 0 : data.image
        }), /*#__PURE__*/(0,jsx_runtime.jsx)(Edit_Task, {})]
      })
    })
  });
};
/* harmony default export */ var Edit = (StageOfCountEdit);
// EXTERNAL MODULE: ./src/components/ActionHover/index.tsx
var ActionHover = __webpack_require__(47033);
// EXTERNAL MODULE: ./src/services/utils.ts
var utils = __webpack_require__(467);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-table/es/Table.js + 76 modules
var Table = __webpack_require__(4894);
// EXTERNAL MODULE: ./node_modules/antd/es/space/index.js + 2 modules
var space = __webpack_require__(42075);
// EXTERNAL MODULE: ./src/components/ActionModalConfirm/index.tsx
var ActionModalConfirm = __webpack_require__(76020);
;// CONCATENATED MODULE: ./src/pages/FarmingDiaryStatic/StageOfCrop/hooks/useDelete.ts



function useDelete() {
  var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},
    _onError = _ref.onError,
    _onSuccess = _ref.onSuccess;
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  var _App$useApp = app/* default */.Z.useApp(),
    message = _App$useApp.message;
  return (0,_umi_production_exports.useRequest)(stage/* deleteStage */._V, {
    manual: true,
    onSuccess: function onSuccess(data, params) {
      message.success(formatMessage({
        id: 'common.success'
      }));
      _onSuccess === null || _onSuccess === void 0 || _onSuccess();
    },
    onError: function onError(error) {
      //message.error(error.message);
      _onError === null || _onError === void 0 || _onError();
    }
  });
}
;// CONCATENATED MODULE: ./src/pages/FarmingDiaryStatic/StageOfCrop/components/DeleteStage.tsx





var DeleteStage = function DeleteStage(_ref) {
  var id = _ref.id,
    onSuccess = _ref.onSuccess;
  var _useDelete = useDelete(),
    run = _useDelete.run,
    loading = _useDelete.loading;
  return /*#__PURE__*/(0,jsx_runtime.jsx)(ActionModalConfirm/* default */.Z, {
    modalProps: {
      onOk: function onOk() {
        return asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
          return regeneratorRuntime_default()().wrap(function _callee$(_context) {
            while (1) switch (_context.prev = _context.next) {
              case 0:
                _context.next = 2;
                return run(id);
              case 2:
                onSuccess === null || onSuccess === void 0 || onSuccess();
                return _context.abrupt("return", true);
              case 4:
              case "end":
                return _context.stop();
            }
          }, _callee);
        }))();
      }
    }
  });
};
/* harmony default export */ var components_DeleteStage = (DeleteStage);
;// CONCATENATED MODULE: ./src/pages/FarmingDiaryStatic/StageOfCrop/components/StageCropList.tsx













var StageCropList = function StageCropList(_ref) {
  var children = _ref.children,
    onSelect = _ref.onSelect,
    reloadKey = _ref.reloadKey;
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  var actionRef = (0,react.useRef)();
  var _useState = (0,react.useState)(null),
    _useState2 = slicedToArray_default()(_useState, 2),
    selectedRowKey = _useState2[0],
    setSelectedRowKey = _useState2[1];
  var _useState3 = (0,react.useState)([]),
    _useState4 = slicedToArray_default()(_useState3, 2),
    data = _useState4[0],
    setData = _useState4[1];
  var handleReload = function handleReload() {
    var _actionRef$current, _actionRef$current$re;
    (_actionRef$current = actionRef.current) === null || _actionRef$current === void 0 || (_actionRef$current$re = _actionRef$current.reload) === null || _actionRef$current$re === void 0 || _actionRef$current$re.call(_actionRef$current);
  };
  (0,react.useEffect)(function () {
    if (reloadKey) {
      handleReload();
    }
  }, [reloadKey]);
  (0,react.useEffect)(function () {
    if (data.length > 0 && !selectedRowKey) {
      var firstRowKey = data[0].name;
      setSelectedRowKey(firstRowKey);
      onSelect === null || onSelect === void 0 || onSelect(firstRowKey);
    }
  }, [data]);
  return /*#__PURE__*/(0,jsx_runtime.jsx)(Table/* default */.Z, {
    actionRef: actionRef,
    search: false,
    toolBarRender: function toolBarRender() {
      return [];
    },
    rowKey: 'name',
    options: false // Hide the entire toolbar
    ,
    form: {
      labelWidth: 'auto'
    },
    scroll: {
      x: 'max-content'
    },
    pagination: {
      pageSize: 10,
      showSizeChanger: true,
      defaultPageSize: 10
    },
    request: ( /*#__PURE__*/function () {
      var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(params, sort, filter) {
        var paramsReq, res;
        return regeneratorRuntime_default()().wrap(function _callee$(_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              paramsReq = (0,utils/* getParamsReqTable */.wh)({
                doc_name: constanst/* DOCTYPE_ERP */.lH.iot_diary_v2_state,
                tableReqParams: {
                  params: params,
                  sort: sort,
                  filter: filter
                },
                defaultSort: 'name asc'
              });
              _context.next = 3;
              return (0,stage/* getStageList */.bp)(paramsReq);
            case 3:
              res = _context.sent;
              setData(res.data);
              return _context.abrupt("return", {
                data: res.data,
                total: res.pagination.totalElements
              });
            case 6:
            case "end":
              return _context.stop();
          }
        }, _callee);
      }));
      return function (_x, _x2, _x3) {
        return _ref2.apply(this, arguments);
      };
    }()),
    rowClassName: function rowClassName(record) {
      return record.name === selectedRowKey ? 'bg-emerald-100 group/action' : 'group/action';
    },
    columns: [{
      title: formatMessage({
        id: 'common.stage_name'
      }),
      dataIndex: 'label',
      render: function render(dom, entity, index, action, schema) {
        return /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
          type: "link",
          onClick: function onClick() {
            setSelectedRowKey(entity.name);
            onSelect === null || onSelect === void 0 || onSelect(entity.name);
          },
          children: dom
        });
      }
    }, {
      title: formatMessage({
        id: 'common.task_count'
      }),
      dataIndex: 'task_count'
    }, {
      title: formatMessage({
        id: 'common.time'
      }),
      dataIndex: 'expire_time_in_days',
      render: function render(dom, entity) {
        return /*#__PURE__*/(0,jsx_runtime.jsx)(ActionHover/* default */.Z, {
          actions: function actions() {
            return /*#__PURE__*/(0,jsx_runtime.jsx)(space/* default */.Z, {
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(components_DeleteStage, {
                id: entity.name,
                onSuccess: handleReload
              })
            });
          },
          children: "".concat(dom, " ").concat(formatMessage({
            id: 'common.days'
          }))
        });
      }
    }]
  });
};
/* harmony default export */ var components_StageCropList = (StageCropList);
;// CONCATENATED MODULE: ./src/pages/FarmingDiaryStatic/StageOfCrop/index.tsx













var Index = function Index(_ref) {
  var children = _ref.children;
  var _useState = (0,react.useState)(null),
    _useState2 = slicedToArray_default()(_useState, 2),
    tableReloadKey = _useState2[0],
    setTableReloadKey = _useState2[1];
  var _useState3 = (0,react.useState)(null),
    _useState4 = slicedToArray_default()(_useState3, 2),
    selectItem = _useState4[0],
    setSelectItemId = _useState4[1];
  var _useState5 = (0,react.useState)(false),
    _useState6 = slicedToArray_default()(_useState5, 2),
    isCreate = _useState6[0],
    setIsCreate = _useState6[1];
  var _useState7 = (0,react.useState)(false),
    _useState8 = slicedToArray_default()(_useState7, 2),
    isFormDirty = _useState8[0],
    setIsFormDirty = _useState8[1];
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  var handleReload = function handleReload() {
    setTableReloadKey((0,index_browser/* nanoid */.x0)());
  };
  var confirmNavigation = (0,UnsavedChangesModal/* default */.Z)(isFormDirty);
  var handleCreateClick = function handleCreateClick() {
    confirmNavigation(function () {
      setIsCreate(true);
      setSelectItemId(null);
    });
  };
  var handleSelectItemClick = function handleSelectItemClick(stageId) {
    confirmNavigation(function () {
      setSelectItemId(stageId);
      setIsCreate(false);
    });
  };
  return /*#__PURE__*/(0,jsx_runtime.jsx)(PageContainer/* PageContainer */._z, {
    extra: /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
      type: "primary",
      icon: /*#__PURE__*/(0,jsx_runtime.jsx)(PlusOutlined/* default */.Z, {}),
      onClick: handleCreateClick,
      children: formatMessage({
        id: 'common.create_stage'
      })
    }),
    children: /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
      className: "bg-white",
      children: /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
        gutter: [16, 16],
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
          span: 9,
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(components_StageCropList, {
            onSelect: handleSelectItemClick,
            reloadKey: tableReloadKey
          })
        }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
          span: 15,
          children: isCreate || !selectItem ? /*#__PURE__*/(0,jsx_runtime.jsx)(Create/* default */.Z, {
            onSuccess: handleReload,
            setIsFormDirty: setIsFormDirty
          }, tableReloadKey) : /*#__PURE__*/(0,jsx_runtime.jsx)(Edit, {
            id: selectItem,
            onSuccess: handleReload,
            setIsFormDirty: setIsFormDirty
          }, tableReloadKey)
        })]
      })
    })
  });
};
/* harmony default export */ var StageOfCrop = (Index);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///67152
`)},75302:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var _hooks_useBreakpoint__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(25378);
"use client";




// Do not export params
function useBreakpoint() {
  return (0,_hooks_useBreakpoint__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .Z)();
}

/* harmony default export */ __webpack_exports__.ZP = ({
  useBreakpoint
});//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNzUzMDIuanMiLCJtYXBwaW5ncyI6IjtBQUFBOztBQUV3QjtBQUNrQztBQUNsQztBQUN4QjtBQUNBO0FBQ0EsU0FBUyx5RUFBcUI7QUFDOUI7QUFDb0I7QUFDcEIsdURBQWU7QUFDZjtBQUNBLENBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hbnQtZGVzaWduLXByby8uL25vZGVfbW9kdWxlcy9hbnRkL2VzL2dyaWQvaW5kZXguanM/YTQxNyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcblxuaW1wb3J0IENvbCBmcm9tICcuL2NvbCc7XG5pbXBvcnQgdXNlSW50ZXJuYWxCcmVha3BvaW50IGZyb20gJy4vaG9va3MvdXNlQnJlYWtwb2ludCc7XG5pbXBvcnQgUm93IGZyb20gJy4vcm93Jztcbi8vIERvIG5vdCBleHBvcnQgcGFyYW1zXG5mdW5jdGlvbiB1c2VCcmVha3BvaW50KCkge1xuICByZXR1cm4gdXNlSW50ZXJuYWxCcmVha3BvaW50KCk7XG59XG5leHBvcnQgeyBDb2wsIFJvdyB9O1xuZXhwb3J0IGRlZmF1bHQge1xuICB1c2VCcmVha3BvaW50XG59OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==
//# sourceURL=webpack-internal:///75302
`)},49867:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   N: function() { return /* binding */ operationUnit; }
/* harmony export */ });
// eslint-disable-next-line import/prefer-default-export
const operationUnit = token => ({
  // FIXME: This use link but is a operation unit. Seems should be a colorPrimary.
  // And Typography use this to generate link style which should not do this.
  color: token.colorLink,
  textDecoration: 'none',
  outline: 'none',
  cursor: 'pointer',
  transition: \`color \${token.motionDurationSlow}\`,
  '&:focus, &:hover': {
    color: token.colorLinkHover
  },
  '&:active': {
    color: token.colorLinkActive
  }
});//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNDk4NjcuanMiLCJtYXBwaW5ncyI6Ijs7O0FBQUE7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1Qix5QkFBeUI7QUFDaEQ7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQSxDQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvYW50ZC9lcy9zdHlsZS9vcGVyYXRpb25Vbml0LmpzPzNjODIiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIGltcG9ydC9wcmVmZXItZGVmYXVsdC1leHBvcnRcbmV4cG9ydCBjb25zdCBvcGVyYXRpb25Vbml0ID0gdG9rZW4gPT4gKHtcbiAgLy8gRklYTUU6IFRoaXMgdXNlIGxpbmsgYnV0IGlzIGEgb3BlcmF0aW9uIHVuaXQuIFNlZW1zIHNob3VsZCBiZSBhIGNvbG9yUHJpbWFyeS5cbiAgLy8gQW5kIFR5cG9ncmFwaHkgdXNlIHRoaXMgdG8gZ2VuZXJhdGUgbGluayBzdHlsZSB3aGljaCBzaG91bGQgbm90IGRvIHRoaXMuXG4gIGNvbG9yOiB0b2tlbi5jb2xvckxpbmssXG4gIHRleHREZWNvcmF0aW9uOiAnbm9uZScsXG4gIG91dGxpbmU6ICdub25lJyxcbiAgY3Vyc29yOiAncG9pbnRlcicsXG4gIHRyYW5zaXRpb246IGBjb2xvciAke3Rva2VuLm1vdGlvbkR1cmF0aW9uU2xvd31gLFxuICAnJjpmb2N1cywgJjpob3Zlcic6IHtcbiAgICBjb2xvcjogdG9rZW4uY29sb3JMaW5rSG92ZXJcbiAgfSxcbiAgJyY6YWN0aXZlJzoge1xuICAgIGNvbG9yOiB0b2tlbi5jb2xvckxpbmtBY3RpdmVcbiAgfVxufSk7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9
//# sourceURL=webpack-internal:///49867
`)},79370:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   G: function() { return /* binding */ isStyleSupport; }
/* harmony export */ });
/* harmony import */ var _canUseDom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(98924);

var isStyleNameSupport = function isStyleNameSupport(styleName) {
  if ((0,_canUseDom__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .Z)() && window.document.documentElement) {
    var styleNameList = Array.isArray(styleName) ? styleName : [styleName];
    var documentElement = window.document.documentElement;
    return styleNameList.some(function (name) {
      return name in documentElement.style;
    });
  }
  return false;
};
var isStyleValueSupport = function isStyleValueSupport(styleName, value) {
  if (!isStyleNameSupport(styleName)) {
    return false;
  }
  var ele = document.createElement('div');
  var origin = ele.style[styleName];
  ele.style[styleName] = value;
  return ele.style[styleName] !== origin;
};
function isStyleSupport(styleName, styleValue) {
  if (!Array.isArray(styleName) && styleValue !== undefined) {
    return isStyleValueSupport(styleName, styleValue);
  }
  return isStyleNameSupport(styleName);
}//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNzkzNzAuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFvQztBQUNwQztBQUNBLE1BQU0sK0RBQVM7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hbnQtZGVzaWduLXByby8uL25vZGVfbW9kdWxlcy9yYy11dGlsL2VzL0RvbS9zdHlsZUNoZWNrZXIuanM/YWU2YyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY2FuVXNlRG9tIGZyb20gXCIuL2NhblVzZURvbVwiO1xudmFyIGlzU3R5bGVOYW1lU3VwcG9ydCA9IGZ1bmN0aW9uIGlzU3R5bGVOYW1lU3VwcG9ydChzdHlsZU5hbWUpIHtcbiAgaWYgKGNhblVzZURvbSgpICYmIHdpbmRvdy5kb2N1bWVudC5kb2N1bWVudEVsZW1lbnQpIHtcbiAgICB2YXIgc3R5bGVOYW1lTGlzdCA9IEFycmF5LmlzQXJyYXkoc3R5bGVOYW1lKSA/IHN0eWxlTmFtZSA6IFtzdHlsZU5hbWVdO1xuICAgIHZhciBkb2N1bWVudEVsZW1lbnQgPSB3aW5kb3cuZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50O1xuICAgIHJldHVybiBzdHlsZU5hbWVMaXN0LnNvbWUoZnVuY3Rpb24gKG5hbWUpIHtcbiAgICAgIHJldHVybiBuYW1lIGluIGRvY3VtZW50RWxlbWVudC5zdHlsZTtcbiAgICB9KTtcbiAgfVxuICByZXR1cm4gZmFsc2U7XG59O1xudmFyIGlzU3R5bGVWYWx1ZVN1cHBvcnQgPSBmdW5jdGlvbiBpc1N0eWxlVmFsdWVTdXBwb3J0KHN0eWxlTmFtZSwgdmFsdWUpIHtcbiAgaWYgKCFpc1N0eWxlTmFtZVN1cHBvcnQoc3R5bGVOYW1lKSkge1xuICAgIHJldHVybiBmYWxzZTtcbiAgfVxuICB2YXIgZWxlID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnZGl2Jyk7XG4gIHZhciBvcmlnaW4gPSBlbGUuc3R5bGVbc3R5bGVOYW1lXTtcbiAgZWxlLnN0eWxlW3N0eWxlTmFtZV0gPSB2YWx1ZTtcbiAgcmV0dXJuIGVsZS5zdHlsZVtzdHlsZU5hbWVdICE9PSBvcmlnaW47XG59O1xuZXhwb3J0IGZ1bmN0aW9uIGlzU3R5bGVTdXBwb3J0KHN0eWxlTmFtZSwgc3R5bGVWYWx1ZSkge1xuICBpZiAoIUFycmF5LmlzQXJyYXkoc3R5bGVOYW1lKSAmJiBzdHlsZVZhbHVlICE9PSB1bmRlZmluZWQpIHtcbiAgICByZXR1cm4gaXNTdHlsZVZhbHVlU3VwcG9ydChzdHlsZU5hbWUsIHN0eWxlVmFsdWUpO1xuICB9XG4gIHJldHVybiBpc1N0eWxlTmFtZVN1cHBvcnQoc3R5bGVOYW1lKTtcbn0iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=
//# sourceURL=webpack-internal:///79370
`)},53416:function(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   x0: function() { return /* binding */ nanoid; }
/* harmony export */ });
/* unused harmony exports random, customRandom, customAlphabet */

let random = bytes => crypto.getRandomValues(new Uint8Array(bytes))
let customRandom = (alphabet, defaultSize, getRandom) => {
  let mask = (2 << (Math.log(alphabet.length - 1) / Math.LN2)) - 1
  let step = -~((1.6 * mask * defaultSize) / alphabet.length)
  return (size = defaultSize) => {
    let id = ''
    while (true) {
      let bytes = getRandom(step)
      let j = step
      while (j--) {
        id += alphabet[bytes[j] & mask] || ''
        if (id.length === size) return id
      }
    }
  }
}
let customAlphabet = (alphabet, size = 21) =>
  customRandom(alphabet, size, random)
let nanoid = (size = 21) =>
  crypto.getRandomValues(new Uint8Array(size)).reduce((id, byte) => {
    byte &= 63
    if (byte < 36) {
      id += byte.toString(36)
    } else if (byte < 62) {
      id += (byte - 26).toString(36).toUpperCase()
    } else if (byte > 62) {
      id += '-'
    } else {
      id += '_'
    }
    return id
  }, '')
//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNTM0MTYuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFxRDtBQUM5QztBQUNBO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0EsTUFBTTtBQUNOO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBLEdBQUciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hbnQtZGVzaWduLXByby8uL25vZGVfbW9kdWxlcy9uYW5vaWQvaW5kZXguYnJvd3Nlci5qcz9hZjJmIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7IHVybEFscGhhYmV0IH0gZnJvbSAnLi91cmwtYWxwaGFiZXQvaW5kZXguanMnXG5leHBvcnQgbGV0IHJhbmRvbSA9IGJ5dGVzID0+IGNyeXB0by5nZXRSYW5kb21WYWx1ZXMobmV3IFVpbnQ4QXJyYXkoYnl0ZXMpKVxuZXhwb3J0IGxldCBjdXN0b21SYW5kb20gPSAoYWxwaGFiZXQsIGRlZmF1bHRTaXplLCBnZXRSYW5kb20pID0+IHtcbiAgbGV0IG1hc2sgPSAoMiA8PCAoTWF0aC5sb2coYWxwaGFiZXQubGVuZ3RoIC0gMSkgLyBNYXRoLkxOMikpIC0gMVxuICBsZXQgc3RlcCA9IC1+KCgxLjYgKiBtYXNrICogZGVmYXVsdFNpemUpIC8gYWxwaGFiZXQubGVuZ3RoKVxuICByZXR1cm4gKHNpemUgPSBkZWZhdWx0U2l6ZSkgPT4ge1xuICAgIGxldCBpZCA9ICcnXG4gICAgd2hpbGUgKHRydWUpIHtcbiAgICAgIGxldCBieXRlcyA9IGdldFJhbmRvbShzdGVwKVxuICAgICAgbGV0IGogPSBzdGVwXG4gICAgICB3aGlsZSAoai0tKSB7XG4gICAgICAgIGlkICs9IGFscGhhYmV0W2J5dGVzW2pdICYgbWFza10gfHwgJydcbiAgICAgICAgaWYgKGlkLmxlbmd0aCA9PT0gc2l6ZSkgcmV0dXJuIGlkXG4gICAgICB9XG4gICAgfVxuICB9XG59XG5leHBvcnQgbGV0IGN1c3RvbUFscGhhYmV0ID0gKGFscGhhYmV0LCBzaXplID0gMjEpID0+XG4gIGN1c3RvbVJhbmRvbShhbHBoYWJldCwgc2l6ZSwgcmFuZG9tKVxuZXhwb3J0IGxldCBuYW5vaWQgPSAoc2l6ZSA9IDIxKSA9PlxuICBjcnlwdG8uZ2V0UmFuZG9tVmFsdWVzKG5ldyBVaW50OEFycmF5KHNpemUpKS5yZWR1Y2UoKGlkLCBieXRlKSA9PiB7XG4gICAgYnl0ZSAmPSA2M1xuICAgIGlmIChieXRlIDwgMzYpIHtcbiAgICAgIGlkICs9IGJ5dGUudG9TdHJpbmcoMzYpXG4gICAgfSBlbHNlIGlmIChieXRlIDwgNjIpIHtcbiAgICAgIGlkICs9IChieXRlIC0gMjYpLnRvU3RyaW5nKDM2KS50b1VwcGVyQ2FzZSgpXG4gICAgfSBlbHNlIGlmIChieXRlID4gNjIpIHtcbiAgICAgIGlkICs9ICctJ1xuICAgIH0gZWxzZSB7XG4gICAgICBpZCArPSAnXydcbiAgICB9XG4gICAgcmV0dXJuIGlkXG4gIH0sICcnKVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9
//# sourceURL=webpack-internal:///53416
`)}}]);
