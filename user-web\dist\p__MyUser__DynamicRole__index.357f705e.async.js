(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[5512],{47046:function(__unused_webpack_module,__webpack_exports__){"use strict";eval(`// This icon file is generated automatically.
var DeleteOutlined = { "icon": { "tag": "svg", "attrs": { "viewBox": "64 64 896 896", "focusable": "false" }, "children": [{ "tag": "path", "attrs": { "d": "M360 184h-8c4.4 0 8-3.6 8-8v8h304v-8c0 4.4 3.6 8 8 8h-8v72h72v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80h72v-72zm504 72H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM731.3 840H292.7l-24.2-512h487l-24.2 512z" } }] }, "name": "delete", "theme": "outlined" };
/* harmony default export */ __webpack_exports__.Z = (DeleteOutlined);
//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNDcwNDYuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQSx1QkFBdUIsVUFBVSx5QkFBeUIsa0RBQWtELGlCQUFpQiwwQkFBMEIsOFZBQThWLEdBQUc7QUFDeGYsc0RBQWUsY0FBYyxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvQGFudC1kZXNpZ24vaWNvbnMtc3ZnL2VzL2Fzbi9EZWxldGVPdXRsaW5lZC5qcz85ODlhIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIFRoaXMgaWNvbiBmaWxlIGlzIGdlbmVyYXRlZCBhdXRvbWF0aWNhbGx5LlxudmFyIERlbGV0ZU91dGxpbmVkID0geyBcImljb25cIjogeyBcInRhZ1wiOiBcInN2Z1wiLCBcImF0dHJzXCI6IHsgXCJ2aWV3Qm94XCI6IFwiNjQgNjQgODk2IDg5NlwiLCBcImZvY3VzYWJsZVwiOiBcImZhbHNlXCIgfSwgXCJjaGlsZHJlblwiOiBbeyBcInRhZ1wiOiBcInBhdGhcIiwgXCJhdHRyc1wiOiB7IFwiZFwiOiBcIk0zNjAgMTg0aC04YzQuNCAwIDgtMy42IDgtOHY4aDMwNHYtOGMwIDQuNCAzLjYgOCA4IDhoLTh2NzJoNzJ2LTgwYzAtMzUuMy0yOC43LTY0LTY0LTY0SDM1MmMtMzUuMyAwLTY0IDI4LjctNjQgNjR2ODBoNzJ2LTcyem01MDQgNzJIMTYwYy0xNy43IDAtMzIgMTQuMy0zMiAzMnYzMmMwIDQuNCAzLjYgOCA4IDhoNjAuNGwyNC43IDUyM2MxLjYgMzQuMSAyOS44IDYxIDYzLjkgNjFoNDU0YzM0LjIgMCA2Mi4zLTI2LjggNjMuOS02MWwyNC43LTUyM0g4ODhjNC40IDAgOC0zLjYgOC04di0zMmMwLTE3LjctMTQuMy0zMi0zMi0zMnpNNzMxLjMgODQwSDI5Mi43bC0yNC4yLTUxMmg0ODdsLTI0LjIgNTEyelwiIH0gfV0gfSwgXCJuYW1lXCI6IFwiZGVsZXRlXCIsIFwidGhlbWVcIjogXCJvdXRsaW5lZFwiIH07XG5leHBvcnQgZGVmYXVsdCBEZWxldGVPdXRsaW5lZDtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==
//# sourceURL=webpack-internal:///47046
`)},27363:function(__unused_webpack_module,__webpack_exports__){"use strict";eval(`// This icon file is generated automatically.
var EditOutlined = { "icon": { "tag": "svg", "attrs": { "viewBox": "64 64 896 896", "focusable": "false" }, "children": [{ "tag": "path", "attrs": { "d": "M257.7 752c2 0 4-.2 6-.5L431.9 722c2-.4 3.9-1.3 5.3-2.8l423.9-423.9a9.96 9.96 0 000-14.1L694.9 114.9c-1.9-1.9-4.4-2.9-7.1-2.9s-5.2 1-7.1 2.9L256.8 538.8c-1.5 1.5-2.4 3.3-2.8 5.3l-29.5 168.2a33.5 33.5 0 009.4 29.8c6.6 6.4 14.9 9.9 23.8 9.9zm67.4-174.4L687.8 215l73.3 73.3-362.7 362.6-88.9 15.7 15.6-89zM880 836H144c-17.7 0-32 14.3-32 32v36c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-36c0-17.7-14.3-32-32-32z" } }] }, "name": "edit", "theme": "outlined" };
/* harmony default export */ __webpack_exports__.Z = (EditOutlined);
//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiMjczNjMuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQSxxQkFBcUIsVUFBVSx5QkFBeUIsa0RBQWtELGlCQUFpQiwwQkFBMEIsMlpBQTJaLEdBQUc7QUFDbmpCLHNEQUFlLFlBQVksRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL2FudC1kZXNpZ24tcHJvLy4vbm9kZV9tb2R1bGVzL0BhbnQtZGVzaWduL2ljb25zLXN2Zy9lcy9hc24vRWRpdE91dGxpbmVkLmpzP2YxMjYiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gVGhpcyBpY29uIGZpbGUgaXMgZ2VuZXJhdGVkIGF1dG9tYXRpY2FsbHkuXG52YXIgRWRpdE91dGxpbmVkID0geyBcImljb25cIjogeyBcInRhZ1wiOiBcInN2Z1wiLCBcImF0dHJzXCI6IHsgXCJ2aWV3Qm94XCI6IFwiNjQgNjQgODk2IDg5NlwiLCBcImZvY3VzYWJsZVwiOiBcImZhbHNlXCIgfSwgXCJjaGlsZHJlblwiOiBbeyBcInRhZ1wiOiBcInBhdGhcIiwgXCJhdHRyc1wiOiB7IFwiZFwiOiBcIk0yNTcuNyA3NTJjMiAwIDQtLjIgNi0uNUw0MzEuOSA3MjJjMi0uNCAzLjktMS4zIDUuMy0yLjhsNDIzLjktNDIzLjlhOS45NiA5Ljk2IDAgMDAwLTE0LjFMNjk0LjkgMTE0LjljLTEuOS0xLjktNC40LTIuOS03LjEtMi45cy01LjIgMS03LjEgMi45TDI1Ni44IDUzOC44Yy0xLjUgMS41LTIuNCAzLjMtMi44IDUuM2wtMjkuNSAxNjguMmEzMy41IDMzLjUgMCAwMDkuNCAyOS44YzYuNiA2LjQgMTQuOSA5LjkgMjMuOCA5Ljl6bTY3LjQtMTc0LjRMNjg3LjggMjE1bDczLjMgNzMuMy0zNjIuNyAzNjIuNi04OC45IDE1LjcgMTUuNi04OXpNODgwIDgzNkgxNDRjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjM2YzAgNC40IDMuNiA4IDggOGg3ODRjNC40IDAgOC0zLjYgOC04di0zNmMwLTE3LjctMTQuMy0zMi0zMi0zMnpcIiB9IH1dIH0sIFwibmFtZVwiOiBcImVkaXRcIiwgXCJ0aGVtZVwiOiBcIm91dGxpbmVkXCIgfTtcbmV4cG9ydCBkZWZhdWx0IEVkaXRPdXRsaW5lZDtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==
//# sourceURL=webpack-internal:///27363
`)},27704:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){"use strict";eval(`
// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  Z: function() { return /* binding */ icons_DeleteFilled; }
});

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectSpread2.js
var objectSpread2 = __webpack_require__(1413);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
;// CONCATENATED MODULE: ./node_modules/@ant-design/icons-svg/es/asn/DeleteFilled.js
// This icon file is generated automatically.
var DeleteFilled = { "icon": { "tag": "svg", "attrs": { "viewBox": "64 64 896 896", "focusable": "false" }, "children": [{ "tag": "path", "attrs": { "d": "M864 256H736v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zm-200 0H360v-72h304v72z" } }] }, "name": "delete", "theme": "filled" };
/* harmony default export */ var asn_DeleteFilled = (DeleteFilled);

// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/components/AntdIcon.js + 5 modules
var AntdIcon = __webpack_require__(89099);
;// CONCATENATED MODULE: ./node_modules/@ant-design/icons/es/icons/DeleteFilled.js

// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY



var DeleteFilled_DeleteFilled = function DeleteFilled(props, ref) {
  return /*#__PURE__*/react.createElement(AntdIcon/* default */.Z, (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, props), {}, {
    ref: ref,
    icon: asn_DeleteFilled
  }));
};
DeleteFilled_DeleteFilled.displayName = 'DeleteFilled';
/* harmony default export */ var icons_DeleteFilled = (/*#__PURE__*/react.forwardRef(DeleteFilled_DeleteFilled));//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///27704
`)},51042:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){"use strict";eval(`/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(1413);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(67294);
/* harmony import */ var _ant_design_icons_svg_es_asn_PlusOutlined__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(42110);
/* harmony import */ var _components_AntdIcon__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(89099);

// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY



var PlusOutlined = function PlusOutlined(props, ref) {
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(_components_AntdIcon__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Z, (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z)((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z)({}, props), {}, {
    ref: ref,
    icon: _ant_design_icons_svg_es_asn_PlusOutlined__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z
  }));
};
PlusOutlined.displayName = 'PlusOutlined';
/* harmony default export */ __webpack_exports__.Z = (/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(PlusOutlined));//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNTEwNDIuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFxRTtBQUNyRTtBQUNBO0FBQytCO0FBQ3lDO0FBQzFCO0FBQzlDO0FBQ0Esc0JBQXNCLGdEQUFtQixDQUFDLHFFQUFRLEVBQUUsNkZBQWEsQ0FBQyw2RkFBYSxHQUFHLFlBQVk7QUFDOUY7QUFDQSxVQUFVLDBGQUFlO0FBQ3pCLEdBQUc7QUFDSDtBQUNBO0FBQ0EsbUVBQTRCLDZDQUFnQixjQUFjIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvQGFudC1kZXNpZ24vaWNvbnMvZXMvaWNvbnMvUGx1c091dGxpbmVkLmpzPzNhMjIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL29iamVjdFNwcmVhZDJcIjtcbi8vIEdFTkVSQVRFIEJZIC4vc2NyaXB0cy9nZW5lcmF0ZS50c1xuLy8gRE9OIE5PVCBFRElUIElUIE1BTlVBTExZXG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgUGx1c091dGxpbmVkU3ZnIGZyb20gXCJAYW50LWRlc2lnbi9pY29ucy1zdmcvZXMvYXNuL1BsdXNPdXRsaW5lZFwiO1xuaW1wb3J0IEFudGRJY29uIGZyb20gJy4uL2NvbXBvbmVudHMvQW50ZEljb24nO1xudmFyIFBsdXNPdXRsaW5lZCA9IGZ1bmN0aW9uIFBsdXNPdXRsaW5lZChwcm9wcywgcmVmKSB7XG4gIHJldHVybiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChBbnRkSWNvbiwgX29iamVjdFNwcmVhZChfb2JqZWN0U3ByZWFkKHt9LCBwcm9wcyksIHt9LCB7XG4gICAgcmVmOiByZWYsXG4gICAgaWNvbjogUGx1c091dGxpbmVkU3ZnXG4gIH0pKTtcbn07XG5QbHVzT3V0bGluZWQuZGlzcGxheU5hbWUgPSAnUGx1c091dGxpbmVkJztcbmV4cG9ydCBkZWZhdWx0IC8qI19fUFVSRV9fKi9SZWFjdC5mb3J3YXJkUmVmKFBsdXNPdXRsaW5lZCk7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9
//# sourceURL=webpack-internal:///51042
`)},81568:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){"use strict";eval(`/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(5574);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(7837);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(45360);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(86738);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(14726);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(67294);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(85893);







var ActionPopConfirm = function ActionPopConfirm(_ref) {
  var actionCall = _ref.actionCall,
    refreshData = _ref.refreshData,
    buttonType = _ref.buttonType,
    text = _ref.text,
    danger = _ref.danger,
    size = _ref.size;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false),
    _useState2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2___default()(_useState, 2),
    loading = _useState2[0],
    setLoading = _useState2[1];
  var onConfirm = /*#__PURE__*/function () {
    var _ref2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee() {
      return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _context.prev = 0;
            setLoading(true);
            if (!actionCall) {
              _context.next = 5;
              break;
            }
            _context.next = 5;
            return actionCall();
          case 5:
            if (!refreshData) {
              _context.next = 8;
              break;
            }
            _context.next = 8;
            return refreshData();
          case 8:
            _context.next = 14;
            break;
          case 10:
            _context.prev = 10;
            _context.t0 = _context["catch"](0);
            antd__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .ZP.error(_context.t0.toString());
            console.log(_context.t0);
          case 14:
            _context.prev = 14;
            setLoading(false);
            return _context.finish(14);
          case 17:
          case "end":
            return _context.stop();
        }
      }, _callee, null, [[0, 10, 14, 17]]);
    }));
    return function onConfirm() {
      return _ref2.apply(this, arguments);
    };
  }();
  var onCancel = /*#__PURE__*/function () {
    var _ref3 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee2() {
      return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee2$(_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
          case "end":
            return _context2.stop();
        }
      }, _callee2);
    }));
    return function onCancel() {
      return _ref3.apply(this, arguments);
    };
  }();
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(antd__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Z, {
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_3__.FormattedMessage, {
      id: "action.confirm"
    }),
    description: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_3__.FormattedMessage, {
      id: "action.verify"
    }),
    onConfirm: onConfirm,
    onCancel: onCancel,
    okText: "\\u0110\\u1ED3ng \\xFD",
    cancelText: "Hu\\u1EF7",
    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(antd__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .ZP, {
      style: {
        display: 'flex',
        alignItems: 'center',
        borderRadius: 0 // Thi\u1EBFt l\u1EADp g\xF3c vu\xF4ng cho button
      },
      size: size || 'middle',
      danger: danger,
      type: buttonType,
      loading: loading,
      children: text
    })
  });
};
/* harmony default export */ __webpack_exports__.Z = (ActionPopConfirm);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///81568
`)},65573:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){"use strict";eval(`/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(29905);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(85893);


var FallbackComponent = function FallbackComponent() {
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .ZP, {
    status: "403",
    title: "Kh\\xF4ng th\\u1EC3 truy c\\u1EADp",
    subTitle: "Xin l\\u1ED7i, b\\u1EA1n kh\\xF4ng c\\xF3 quy\\u1EC1n xem n\\u1ED9i dung n\\xE0y."
    //   extra={
    //     <Button type="primary" href="/">
    //       Back Home
    //     </Button>
    //   }
  });
};
/* harmony default export */ __webpack_exports__.Z = (FallbackComponent);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNjU1NzMuanMiLCJtYXBwaW5ncyI6Ijs7QUFBOEI7QUFBQTtBQUU5QixJQUFNRyxpQkFBaUIsR0FBRyxTQUFwQkEsaUJBQWlCQSxDQUFBLEVBQVM7RUFDOUIsb0JBQ0VELHNEQUFBLENBQUNGLHNEQUFNO0lBQ0xJLE1BQU0sRUFBQyxLQUFLO0lBQ1pDLEtBQUssRUFBQyxpQ0FBb0I7SUFDMUJDLFFBQVEsRUFBQztJQUNUO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7RUFBQSxDQUNELENBQUM7QUFFTixDQUFDO0FBRUQsc0RBQWVILGlCQUFpQiIsInNvdXJjZXMiOlsid2VicGFjazovL2FudC1kZXNpZ24tcHJvLy4vc3JjL2NvbXBvbmVudHMvRmFsbGJhY2tDb250ZW50L2luZGV4LnRzeD9mZGRhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFJlc3VsdCB9IGZyb20gJ2FudGQnO1xyXG5cclxuY29uc3QgRmFsbGJhY2tDb21wb25lbnQgPSAoKSA9PiB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxSZXN1bHRcclxuICAgICAgc3RhdHVzPVwiNDAzXCJcclxuICAgICAgdGl0bGU9XCJLaMO0bmcgdGjhu4MgdHJ1eSBj4bqtcFwiXHJcbiAgICAgIHN1YlRpdGxlPVwiWGluIGzhu5dpLCBi4bqhbiBraMO0bmcgY8OzIHF1eeG7gW4geGVtIG7hu5lpIGR1bmcgbsOgeS5cIlxyXG4gICAgICAvLyAgIGV4dHJhPXtcclxuICAgICAgLy8gICAgIDxCdXR0b24gdHlwZT1cInByaW1hcnlcIiBocmVmPVwiL1wiPlxyXG4gICAgICAvLyAgICAgICBCYWNrIEhvbWVcclxuICAgICAgLy8gICAgIDwvQnV0dG9uPlxyXG4gICAgICAvLyAgIH1cclxuICAgIC8+XHJcbiAgKTtcclxufTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IEZhbGxiYWNrQ29tcG9uZW50O1xyXG4iXSwibmFtZXMiOlsiUmVzdWx0IiwianN4IiwiX2pzeCIsIkZhbGxiYWNrQ29tcG9uZW50Iiwic3RhdHVzIiwidGl0bGUiLCJzdWJUaXRsZSJdLCJzb3VyY2VSb290IjoiIn0=
//# sourceURL=webpack-internal:///65573
`)},24414:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){"use strict";eval(`/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(5574);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _services_customerUser__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(40063);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(51042);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(7837);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(98138);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(25514);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(14726);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(85576);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(45360);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(71230);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(15746);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(96365);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(84567);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(67294);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(85893);











var Item = antd__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Z.Item;
var Title = antd__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .Z.Title;
var CreateRoleForm = function CreateRoleForm(params) {
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false),
    _useState2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2___default()(_useState, 2),
    loading = _useState2[0],
    setLoading = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false),
    _useState4 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2___default()(_useState3, 2),
    isOpen = _useState4[0],
    setOpen = _useState4[1];
  var _Form$useForm = antd__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Z.useForm(),
    _Form$useForm2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2___default()(_Form$useForm, 1),
    form = _Form$useForm2[0];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([]),
    _useState6 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2___default()(_useState5, 2),
    sectionCategories = _useState6[0],
    setSectionCategories = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false),
    _useState8 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2___default()(_useState7, 2),
    showFieldLevelPermissions = _useState8[0],
    setShowFieldLevelPermissions = _useState8[1];
  (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(function () {
    fetchData();
  }, []);
  var fetchData = /*#__PURE__*/function () {
    var _ref = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee() {
      var data, sectionArray;
      return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _context.prev = 0;
            _context.next = 3;
            return (0,_services_customerUser__WEBPACK_IMPORTED_MODULE_3__/* .listDynamicRoleAllSection */ .Lf)();
          case 3:
            data = _context.sent;
            sectionArray = Object.entries(data).map(function (_ref2) {
              var _ref3 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2___default()(_ref2, 2),
                key = _ref3[0],
                value = _ref3[1];
              return {
                label: key,
                value: value
              };
            });
            categorizeActions(sectionArray);
            _context.next = 11;
            break;
          case 8:
            _context.prev = 8;
            _context.t0 = _context["catch"](0);
            console.error('Error fetching sections data:', _context.t0);
          case 11:
          case "end":
            return _context.stop();
        }
      }, _callee, null, [[0, 8]]);
    }));
    return function fetchData() {
      return _ref.apply(this, arguments);
    };
  }();
  var categorizeActions = function categorizeActions(data) {
    var categories = {
      System: [],
      Project: [],
      Zone: [],
      Plant: [],
      Crop: [],
      Task: [],
      Plan: [],
      State: [],
      Category: [],
      Storage: [],
      CategoryInventory: [],
      CategoryInventoryFieldLevel: [],
      Employee: [],
      DynamicRole: [],
      Timekeeping: [],
      Visitor: [],
      IoTDevice: []
    };
    Object.entries(data).forEach(function (_ref4) {
      var _ref5 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2___default()(_ref4, 2),
        index = _ref5[0],
        obj = _ref5[1];
      var _ref6 = obj,
        label = _ref6.label,
        value = _ref6.value;
      if (label.includes('PROJECT')) {
        categories.Project.push({
          label: value,
          value: label
        });
      } else if (label.includes('ZONE')) {
        categories.Zone.push({
          label: value,
          value: label
        });
      } else if (label.includes('PLANT')) {
        categories.Plant.push({
          label: value,
          value: label
        });
      } else if (label.includes('CROP')) {
        categories.Crop.push({
          label: value,
          value: label
        });
      } else if (label.includes('TASK')) {
        categories.Task.push({
          label: value,
          value: label
        });
      } else if (label.includes('STATE')) {
        categories.State.push({
          label: value,
          value: label
        });
      } else if (label.includes('CATEGORY') && !label.includes('CATEGORY_INVENTORY')) {
        categories.Category.push({
          label: value,
          value: label
        });
      } else if (label.includes('STORAGE')) {
        categories.Storage.push({
          label: value,
          value: label
        });
      } else if (label.includes('CATEGORY_INVENTORY') && !label.includes('CATEGORY_INVENTORY_FIELD_LEVEL')) {
        categories.CategoryInventory.push({
          label: value,
          value: label
        });
      } else if (label.includes('CATEGORY_INVENTORY_FIELD_LEVEL')) {
        categories.CategoryInventoryFieldLevel.push({
          label: value,
          value: label
        });
      } else if (label.includes('EMPLOYEE')) {
        categories.Employee.push({
          label: value,
          value: label
        });
      } else if (label.includes('DYNAMIC_ROLE')) {
        categories.DynamicRole.push({
          label: value,
          value: label
        });
      } else if (label.includes('TIMEKEEPING')) {
        categories.Timekeeping.push({
          label: value,
          value: label
        });
      } else if (label.includes('VISITOR')) {
        categories.Visitor.push({
          label: value,
          value: label
        });
      } else if (label.includes('SYSTEM')) {
        categories.System.push({
          label: value,
          value: label
        });
      } else if (label.includes('IOT_DEVICE')) {
        categories.IoTDevice.push({
          label: value,
          value: label
        });
      }
    });
    setSectionCategories(categories);
  };
  var showModal = function showModal() {
    setOpen(true);
  };
  var hideModal = function hideModal() {
    setOpen(false);
  };
  var handleOk = function handleOk() {
    form.submit();
  };
  var handleCancel = function handleCancel() {
    hideModal();
    form.resetFields();
  };
  var handleReset = function handleReset() {
    form.resetFields();
  };
  var _useIntl = (0,_umijs_max__WEBPACK_IMPORTED_MODULE_4__.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  var handleFieldLevelPermissionsChange = function handleFieldLevelPermissionsChange(e) {
    setShowFieldLevelPermissions(e.target.checked);
  };
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.Fragment, {
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .ZP, {
      type: "primary",
      onClick: showModal,
      style: {
        display: 'flex',
        alignItems: 'center'
      },
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .Z, {}), " ", formatMessage({
        id: 'common.add_new_role'
      })]
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(antd__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .Z, {
      title: formatMessage({
        id: 'common.add_new_role'
      }),
      open: isOpen,
      onOk: handleOk,
      onCancel: handleCancel,
      width: 800,
      confirmLoading: loading,
      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Z, {
        layout: "horizontal",
        labelCol: {
          span: 24
        },
        wrapperCol: {
          span: 24
        },
        labelAlign: "left",
        form: form,
        onFinish: ( /*#__PURE__*/function () {
          var _ref7 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee2(value) {
            var values, flattenedString, dynamicRole, req;
            return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee2$(_context2) {
              while (1) switch (_context2.prev = _context2.next) {
                case 0:
                  _context2.prev = 0;
                  values = Object.entries(value).filter(function (_ref8) {
                    var _ref9 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2___default()(_ref8, 2),
                      key = _ref9[0],
                      value = _ref9[1];
                    return key !== 'label' && Array.isArray(value);
                  }).map(function (_ref10) {
                    var _ref11 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2___default()(_ref10, 2),
                      key = _ref11[0],
                      value = _ref11[1];
                    return value;
                  }).flat();
                  flattenedString = values.join(',');
                  dynamicRole = {
                    label: value.label,
                    iot_customer: params.customer_id,
                    sections: flattenedString
                  };
                  _context2.next = 6;
                  return (0,_services_customerUser__WEBPACK_IMPORTED_MODULE_3__/* .createDynamicRole */ .f6)(dynamicRole);
                case 6:
                  req = _context2.sent;
                  hideModal();
                  if (!params.refreshFnc) {
                    _context2.next = 11;
                    break;
                  }
                  _context2.next = 11;
                  return params.refreshFnc();
                case 11:
                  antd__WEBPACK_IMPORTED_MODULE_12__/* ["default"] */ .ZP.success('Success!');
                  _context2.next = 17;
                  break;
                case 14:
                  _context2.prev = 14;
                  _context2.t0 = _context2["catch"](0);
                  antd__WEBPACK_IMPORTED_MODULE_12__/* ["default"] */ .ZP.error(_context2.t0.toString());
                case 17:
                  _context2.prev = 17;
                  setLoading(false);
                  return _context2.finish(17);
                case 20:
                case "end":
                  return _context2.stop();
              }
            }, _callee2, null, [[0, 14, 17, 20]]);
          }));
          return function (_x) {
            return _ref7.apply(this, arguments);
          };
        }()),
        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_13__/* ["default"] */ .Z, {
          gutter: 16,
          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(antd__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .Z, {
            md: 12,
            children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Item, {
              label: formatMessage({
                id: 'common.role_name'
              }),
              rules: [{
                required: true,
                message: 'B\u1EAFt bu\u1ED9c \u0111i\u1EC1n'
              }],
              name: "label",
              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(antd__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .Z, {})
            })
          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(antd__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .Z, {
            span: 24,
            style: {
              textAlign: 'left',
              marginBottom: '16px'
            },
            children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(antd__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .ZP, {
              size: "small",
              type: "default",
              onClick: handleReset,
              children: "T\\u1EA1o l\\u1EA1i"
            })
          })]
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_13__/* ["default"] */ .Z, {
          gutter: 16,
          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .Z, {
            md: 6,
            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Title, {
              level: 5,
              children: "Qu\\u1EA3n tr\\u1ECB vi\\xEAn"
            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Item, {
              name: "system_section",
              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(antd__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .Z.Group, {
                options: sectionCategories.System
              })
            })]
          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .Z, {
            md: 6,
            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Title, {
              level: 5,
              children: "Quy\\u1EC1n d\\u1EF1 \\xE1n"
            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Item, {
              name: "project_section",
              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(antd__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .Z.Group, {
                options: sectionCategories.Project
              })
            })]
          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .Z, {
            md: 6,
            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Title, {
              level: 5,
              children: "Quy\\u1EC1n khu v\\u1EF1c"
            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Item, {
              name: "zone_section",
              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(antd__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .Z.Group, {
                options: sectionCategories.Zone
              })
            })]
          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .Z, {
            md: 6,
            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Title, {
              level: 5,
              children: "Quy\\u1EC1n c\\xE2y tr\\u1ED3ng"
            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Item, {
              name: "plant_section",
              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(antd__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .Z.Group, {
                options: sectionCategories.Plant
              })
            })]
          })]
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_13__/* ["default"] */ .Z, {
          gutter: 16,
          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .Z, {
            md: 6,
            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Title, {
              level: 5,
              children: "Quy\\u1EC1n m\\xF9a v\\u1EE5"
            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Item, {
              name: "crop_section",
              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(antd__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .Z.Group, {
                options: sectionCategories.Crop
              })
            })]
          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .Z, {
            md: 6,
            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Title, {
              level: 5,
              children: "Quy\\u1EC1n giai \\u0111o\\u1EA1n"
            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Item, {
              name: "state_section",
              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(antd__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .Z.Group, {
                options: sectionCategories.State
              })
            })]
          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .Z, {
            md: 6,
            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Title, {
              level: 5,
              children: "Quy\\u1EC1n c\\xF4ng vi\\u1EC7c"
            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Item, {
              name: "task_section",
              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(antd__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .Z.Group, {
                options: sectionCategories.Task
              })
            })]
          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .Z, {
            md: 6,
            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Title, {
              level: 5,
              children: "Quy\\u1EC1n h\\xE0ng h\\xF3a"
            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Item, {
              name: "category_section",
              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(antd__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .Z.Group, {
                options: sectionCategories.Category
              })
            })]
          })]
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_13__/* ["default"] */ .Z, {
          gutter: 16,
          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .Z, {
            md: 6,
            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Title, {
              level: 5,
              children: "Quy\\u1EC1n kho"
            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Item, {
              name: "storage_section",
              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(antd__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .Z.Group, {
                options: sectionCategories.Storage
              })
            })]
          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .Z, {
            md: 6,
            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Title, {
              level: 5,
              children: "Quy\\u1EC1n t\\u1ED3n kho"
            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxs)("div", {
              style: {
                display: 'flex',
                flexDirection: 'column',
                gap: '0px'
              },
              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Item, {
                name: "category_inventory_section",
                style: {
                  marginBottom: '0px'
                },
                children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(antd__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .Z.Group, {
                  options: sectionCategories.CategoryInventory
                })
              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(antd__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .Z, {
                onChange: handleFieldLevelPermissionsChange,
                style: {
                  marginTop: '0px'
                },
                children: "Quy\\u1EC1n chi ti\\u1EBFt"
              }), showFieldLevelPermissions && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)("div", {
                style: {
                  paddingLeft: '24px',
                  marginTop: '8px'
                },
                children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Item, {
                  name: "category_inventory_field_level_section",
                  children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(antd__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .Z.Group, {
                    options: sectionCategories.CategoryInventoryFieldLevel
                  })
                })
              })]
            })]
          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .Z, {
            md: 6,
            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Title, {
              level: 5,
              children: "Qu\\u1EA3n l\\xFD nh\\xE2n vi\\xEAn"
            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Item, {
              name: "employee_section",
              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(antd__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .Z.Group, {
                options: sectionCategories.Employee
              })
            })]
          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .Z, {
            md: 6,
            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Title, {
              level: 5,
              children: "Qu\\u1EA3n l\\xFD vai tr\\xF2"
            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Item, {
              name: "dynamic_role_section",
              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(antd__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .Z.Group, {
                options: sectionCategories.DynamicRole
              })
            })]
          })]
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_13__/* ["default"] */ .Z, {
          gutter: 16,
          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .Z, {
            md: 6,
            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Title, {
              level: 5,
              children: "Qu\\u1EA3n l\\xFD ch\\u1EA5m c\\xF4ng"
            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Item, {
              name: "dynamic_timekeeping_section",
              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(antd__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .Z.Group, {
                options: sectionCategories.Timekeeping
              })
            })]
          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .Z, {
            md: 6,
            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Title, {
              level: 5,
              children: "Kh\\xE1ch tham quan"
            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Item, {
              name: "visitor_section",
              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(antd__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .Z.Group, {
                options: sectionCategories.Visitor
              })
            })]
          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .Z, {
            md: 6,
            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Title, {
              level: 5,
              children: "Qu\\u1EA3n l\\xFD thi\\u1EBFt b\\u1ECB IoT"
            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Item, {
              name: "iot_device_section",
              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(antd__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .Z.Group, {
                options: sectionCategories.IoTDevice
              })
            })]
          })]
        })]
      })
    })]
  });
};
/* harmony default export */ __webpack_exports__.Z = (CreateRoleForm);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///24414
`)},4544:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){"use strict";eval(`// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return /* binding */ DynamicRole; }
});

// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/regeneratorRuntime.js
var regeneratorRuntime = __webpack_require__(15009);
var regeneratorRuntime_default = /*#__PURE__*/__webpack_require__.n(regeneratorRuntime);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(99289);
var asyncToGenerator_default = /*#__PURE__*/__webpack_require__.n(asyncToGenerator);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/slicedToArray.js
var slicedToArray = __webpack_require__(5574);
var slicedToArray_default = /*#__PURE__*/__webpack_require__.n(slicedToArray);
// EXTERNAL MODULE: ./src/components/FallbackContent/index.tsx
var FallbackContent = __webpack_require__(65573);
// EXTERNAL MODULE: ./src/services/sscript.ts
var sscript = __webpack_require__(39750);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-layout/es/components/PageContainer/index.js + 18 modules
var PageContainer = __webpack_require__(6110);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-table/es/Table.js + 76 modules
var Table = __webpack_require__(4894);
// EXTERNAL MODULE: ./src/.umi-production/exports.ts + 34 modules
var _umi_production_exports = __webpack_require__(7837);
// EXTERNAL MODULE: ./node_modules/antd/es/space/index.js + 2 modules
var space = __webpack_require__(42075);
// EXTERNAL MODULE: ./node_modules/antd/es/config-provider/index.js + 7 modules
var config_provider = __webpack_require__(28459);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./src/pages/MyUser/DynamicRole/Components/CreateNewRole.tsx
var CreateNewRole = __webpack_require__(24414);
// EXTERNAL MODULE: ./src/components/ActionPopConfirm/index.tsx
var ActionPopConfirm = __webpack_require__(81568);
// EXTERNAL MODULE: ./src/services/customerUser.ts
var customerUser = __webpack_require__(40063);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/DeleteFilled.js + 1 modules
var DeleteFilled = __webpack_require__(27704);
// EXTERNAL MODULE: ./node_modules/antd/es/form/index.js + 18 modules
var es_form = __webpack_require__(98138);
// EXTERNAL MODULE: ./node_modules/antd/es/message/index.js
var message = __webpack_require__(45360);
// EXTERNAL MODULE: ./node_modules/react/jsx-runtime.js
var jsx_runtime = __webpack_require__(85893);
;// CONCATENATED MODULE: ./src/pages/MyUser/DynamicRole/Components/DeleteRole.tsx







var Item = es_form/* default */.Z.Item;
var DeleteRole = function DeleteRole(params) {
  var removeData = /*#__PURE__*/function () {
    var _ref = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
      return regeneratorRuntime_default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _context.prev = 0;
            _context.next = 3;
            return (0,customerUser/* removeDynamicRole */.rX)(params.user_info);
          case 3:
            _context.next = 8;
            break;
          case 5:
            _context.prev = 5;
            _context.t0 = _context["catch"](0);
            message/* default */.ZP.error(_context.t0.toString());
          case 8:
          case "end":
            return _context.stop();
        }
      }, _callee, null, [[0, 5]]);
    }));
    return function removeData() {
      return _ref.apply(this, arguments);
    };
  }();
  return /*#__PURE__*/(0,jsx_runtime.jsx)(ActionPopConfirm/* default */.Z, {
    actionCall: removeData,
    refreshData: params.refreshFnc,
    text: /*#__PURE__*/(0,jsx_runtime.jsx)(DeleteFilled/* default */.Z, {})
    //buttonType={'dashed'}
    ,
    danger: true,
    size: "small"
  });
};
/* harmony default export */ var Components_DeleteRole = (DeleteRole);
// EXTERNAL MODULE: ./node_modules/antd/es/select/index.js + 30 modules
var es_select = __webpack_require__(83863);
// EXTERNAL MODULE: ./node_modules/antd/es/checkbox/index.js + 3 modules
var es_checkbox = __webpack_require__(84567);
// EXTERNAL MODULE: ./node_modules/antd/es/button/index.js + 9 modules
var es_button = __webpack_require__(14726);
// EXTERNAL MODULE: ./node_modules/antd/es/modal/index.js + 1 modules
var modal = __webpack_require__(85576);
// EXTERNAL MODULE: ./node_modules/antd/es/row/index.js
var row = __webpack_require__(71230);
// EXTERNAL MODULE: ./node_modules/antd/es/col/index.js
var col = __webpack_require__(15746);
// EXTERNAL MODULE: ./node_modules/antd/es/input/index.js + 4 modules
var input = __webpack_require__(96365);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectSpread2.js
var objectSpread2 = __webpack_require__(1413);
;// CONCATENATED MODULE: ./node_modules/@ant-design/icons-svg/es/asn/EditFilled.js
// This icon file is generated automatically.
var EditFilled = { "icon": { "tag": "svg", "attrs": { "viewBox": "64 64 896 896", "focusable": "false" }, "children": [{ "tag": "path", "attrs": { "d": "M880 836H144c-17.7 0-32 14.3-32 32v36c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-36c0-17.7-14.3-32-32-32zm-622.3-84c2 0 4-.2 6-.5L431.9 722c2-.4 3.9-1.3 5.3-2.8l423.9-423.9a9.96 9.96 0 000-14.1L694.9 114.9c-1.9-1.9-4.4-2.9-7.1-2.9s-5.2 1-7.1 2.9L256.8 538.8c-1.5 1.5-2.4 3.3-2.8 5.3l-29.5 168.2a33.5 33.5 0 009.4 29.8c6.6 6.4 14.9 9.9 23.8 9.9z" } }] }, "name": "edit", "theme": "filled" };
/* harmony default export */ var asn_EditFilled = (EditFilled);

// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/components/AntdIcon.js + 5 modules
var AntdIcon = __webpack_require__(89099);
;// CONCATENATED MODULE: ./node_modules/@ant-design/icons/es/icons/EditFilled.js

// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY



var EditFilled_EditFilled = function EditFilled(props, ref) {
  return /*#__PURE__*/react.createElement(AntdIcon/* default */.Z, (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, props), {}, {
    ref: ref,
    icon: asn_EditFilled
  }));
};
EditFilled_EditFilled.displayName = 'EditFilled';
/* harmony default export */ var icons_EditFilled = (/*#__PURE__*/react.forwardRef(EditFilled_EditFilled));
// EXTERNAL MODULE: ./node_modules/antd/es/typography/index.js + 18 modules
var typography = __webpack_require__(25514);
;// CONCATENATED MODULE: ./src/pages/MyUser/DynamicRole/Components/UpdateNewRole.tsx





var UpdateNewRole_Item = es_form/* default */.Z.Item;







var Title = typography/* default */.Z.Title;
var Option = es_select/* default */.Z.Option;
var CheckboxGroup = es_checkbox/* default */.Z.Group;
var UpdateRoleForm = function UpdateRoleForm(params) {
  var _useState = (0,react.useState)(false),
    _useState2 = slicedToArray_default()(_useState, 2),
    loading = _useState2[0],
    setLoading = _useState2[1];
  var _useState3 = (0,react.useState)(false),
    _useState4 = slicedToArray_default()(_useState3, 2),
    isOpen = _useState4[0],
    setOpen = _useState4[1];
  var _Form$useForm = es_form/* default */.Z.useForm(),
    _Form$useForm2 = slicedToArray_default()(_Form$useForm, 1),
    form = _Form$useForm2[0];
  var _useState5 = (0,react.useState)({
      System: [],
      Project: [],
      Zone: [],
      Plant: [],
      Crop: [],
      Task: [],
      Plan: [],
      State: [],
      Category: [],
      Product: [],
      Storage: [],
      CategoryInventory: [],
      CategoryInventoryFieldLevel: [],
      ProductInventory: [],
      Employee: [],
      DynamicRole: [],
      Timekeeping: [],
      Visitor: [],
      IoTDevice: []
    }),
    _useState6 = slicedToArray_default()(_useState5, 2),
    sectionCategories = _useState6[0],
    setSectionCategories = _useState6[1];
  var _useState7 = (0,react.useState)(true),
    _useState8 = slicedToArray_default()(_useState7, 2),
    showFieldLevelPermissions = _useState8[0],
    setShowFieldLevelPermissions = _useState8[1];
  var _useState9 = (0,react.useState)([]),
    _useState10 = slicedToArray_default()(_useState9, 2),
    permissionArray = _useState10[0],
    setPermissionArray = _useState10[1];
  (0,react.useEffect)(function () {
    fetchData();
    var permissionArray = params.user_info.sections ? params.user_info.sections.split(',') : [];
    setPermissionArray(permissionArray);
  }, []); // Empty dependency array means this effect will only run once after the initial render

  var fetchData = /*#__PURE__*/function () {
    var _ref = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
      var data, sectionArray;
      return regeneratorRuntime_default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _context.prev = 0;
            _context.next = 3;
            return (0,customerUser/* listDynamicRoleAllSection */.Lf)();
          case 3:
            data = _context.sent;
            // Check if data is truthy before processing
            sectionArray = Object.entries(data).map(function (_ref2) {
              var _ref3 = slicedToArray_default()(_ref2, 2),
                key = _ref3[0],
                value = _ref3[1];
              return {
                label: key,
                value: value
              };
            });
            categorizeActions(sectionArray);
            _context.next = 11;
            break;
          case 8:
            _context.prev = 8;
            _context.t0 = _context["catch"](0);
            console.error('Error sections data:', _context.t0);
          case 11:
          case "end":
            return _context.stop();
        }
      }, _callee, null, [[0, 8]]);
    }));
    return function fetchData() {
      return _ref.apply(this, arguments);
    };
  }();
  var categorizeActions = function categorizeActions(data) {
    var categories = {
      System: [],
      Project: [],
      Zone: [],
      Plant: [],
      Crop: [],
      Task: [],
      Plan: [],
      State: [],
      Category: [],
      Storage: [],
      CategoryInventory: [],
      CategoryInventoryFieldLevel: [],
      Employee: [],
      DynamicRole: [],
      Timekeeping: [],
      Visitor: [],
      IoTDevice: []
      // ... other categories
    };
    Object.entries(data).forEach(function (_ref4) {
      var _ref5 = slicedToArray_default()(_ref4, 2),
        index = _ref5[0],
        obj = _ref5[1];
      var _ref6 = obj,
        label = _ref6.label,
        value = _ref6.value;
      if (label.includes('PROJECT')) {
        categories.Project.push({
          label: value,
          value: label
        });
      } else if (label.includes('ZONE')) {
        categories.Zone.push({
          label: value,
          value: label
        });
      } else if (label.includes('PLANT')) {
        categories.Plant.push({
          label: value,
          value: label
        });
      } else if (label.includes('CROP')) {
        categories.Crop.push({
          label: value,
          value: label
        });
      } else if (label.includes('TASK')) {
        categories.Task.push({
          label: value,
          value: label
        });
      } else if (label.includes('PLAN')) {
        categories.Plan.push({
          label: value,
          value: label
        });
      } else if (label.includes('STATE')) {
        categories.State.push({
          label: value,
          value: label
        });
      } else if (label.includes('CATEGORY') && !label.includes('CATEGORY_INVENTORY')) {
        categories.Category.push({
          label: value,
          value: label
        });
      } else if (label.includes('STORAGE')) {
        categories.Storage.push({
          label: value,
          value: label
        });
      } else if (label.includes('CATEGORY_INVENTORY') && !label.includes('CATEGORY_INVENTORY_FIELD_LEVEL')) {
        categories.CategoryInventory.push({
          label: value,
          value: label
        });
      } else if (label.includes('CATEGORY_INVENTORY_FIELD_LEVEL')) {
        categories.CategoryInventoryFieldLevel.push({
          label: value,
          value: label
        });
      } else if (label.includes('EMPLOYEE')) {
        categories.Employee.push({
          label: value,
          value: label
        });
      } else if (label.includes('DYNAMIC_ROLE')) {
        categories.DynamicRole.push({
          label: value,
          value: label
        });
      } else if (label.includes('TIMEKEEPING')) {
        categories.Timekeeping.push({
          label: value,
          value: label
        });
      } else if (label.includes('VISITOR')) {
        categories.Visitor.push({
          label: value,
          value: label
        });
      } else if (label.includes('SYSTEM')) {
        categories.System.push({
          label: value,
          value: label
        });
      } else if (label.includes('IOT_DEVICE')) {
        categories.IoTDevice.push({
          label: value,
          value: label
        });
      }
      // Add more conditions for other categories
      setSectionCategories(categories);
    });
    return categories;
  };
  //fix hot
  var showModal = function showModal() {
    form.setFieldsValue({
      label: params.user_info.label,
      admin_section: permissionArray.filter(function (item) {
        return item.includes('SYSTEM');
      }),
      category_inventory_section: permissionArray.filter(function (item) {
        return item.includes('CATEGORY_INVENTORY') && !item.includes('CATEGORY_INVENTORY_FIELD_LEVEL');
      }),
      category_inventory_field_level_section: permissionArray.filter(function (item) {
        return item.includes('CATEGORY_INVENTORY_FIELD_LEVEL');
      }),
      category_section: permissionArray.filter(function (item) {
        return !item.includes('CATEGORY_INVENTORY') && item.includes('CATEGORY');
      }),
      crop_section: permissionArray.filter(function (item) {
        return item.includes('CROP');
      }),
      plant_section: permissionArray.filter(function (item) {
        return item.includes('PLANT');
      }),
      project_section: permissionArray.filter(function (item) {
        return item.includes('PROJECT');
      }),
      zone_section: permissionArray.filter(function (item) {
        return item.includes('ZONE');
      }),
      state_section: permissionArray.filter(function (item) {
        return item.includes('STATE');
      }),
      storage_section: permissionArray.filter(function (item) {
        return item.includes('STORAGE');
      }),
      task_section: permissionArray.filter(function (item) {
        return item.includes('TASK');
      }),
      employee_section: permissionArray.filter(function (item) {
        return item.includes('EMPLOYEE');
      }),
      dynamic_role_section: permissionArray.filter(function (item) {
        return item.includes('DYNAMIC_ROLE');
      }),
      dynamic_timekeeping_section: permissionArray.filter(function (item) {
        return item.includes('TIMEKEEPING');
      }),
      visitor_section: permissionArray.filter(function (item) {
        return item.includes('VISITOR');
      }),
      iot_device_section: permissionArray.filter(function (item) {
        return item.includes('IOT_DEVICE');
      })
    });
    setOpen(true);
  };
  var hideModal = function hideModal() {
    setOpen(false);
  };
  var handleOk = function handleOk() {
    form.submit();
  };
  var handleCancel = function handleCancel() {
    hideModal();
    form.resetFields();
  };
  var handleReset = function handleReset() {
    form.resetFields();
    form.setFieldValue('label', params.user_info.label);
  };
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  var handleFieldLevelPermissionsChange = function handleFieldLevelPermissionsChange(e) {
    setShowFieldLevelPermissions(e.target.checked);
  };
  return /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
    children: [/*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
      size: "small",
      type: "default",
      onClick: showModal,
      style: {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      },
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(icons_EditFilled, {})
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(modal/* default */.Z, {
      title: "Ch\\u1EC9nh s\\u1EEDa vai tr\\xF2",
      open: isOpen,
      onOk: handleOk,
      onCancel: handleCancel,
      width: 800,
      confirmLoading: loading,
      children: /*#__PURE__*/(0,jsx_runtime.jsxs)(es_form/* default */.Z, {
        layout: "horizontal",
        labelCol: {
          span: 24
        } // Set the label column to take full width
        ,
        wrapperCol: {
          span: 24
        } // Set the input field to take full width
        ,
        labelAlign: "left",
        form: form,
        onFinish: ( /*#__PURE__*/function () {
          var _ref7 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2(value) {
            var sections, dynamicRole, updatePermissionArray, req;
            return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
              while (1) switch (_context2.prev = _context2.next) {
                case 0:
                  _context2.prev = 0;
                  console.log('checkbox values', value);
                  sections = Object.keys(value).filter(function (key) {
                    return key.endsWith('_section');
                  }).reduce(function (acc, key) {
                    var sectionValues = (value[key] || []).map(function (section) {
                      return section.toUpperCase();
                    });
                    return acc.concat(sectionValues);
                  }, []).join(',');
                  dynamicRole = {
                    name: params.user_info.name,
                    label: value.label,
                    iot_customer: params.user_info.iot_customer,
                    sections: sections
                  };
                  updatePermissionArray = sections.split(',');
                  console.log('new permissionarr', updatePermissionArray);
                  setPermissionArray(updatePermissionArray);
                  _context2.next = 9;
                  return (0,customerUser/* updateDynamicRole */.fh)(dynamicRole);
                case 9:
                  req = _context2.sent;
                  hideModal();
                  if (!params.refreshFnc) {
                    _context2.next = 14;
                    break;
                  }
                  _context2.next = 14;
                  return params.refreshFnc();
                case 14:
                  message/* default */.ZP.success('Success!');
                  _context2.next = 20;
                  break;
                case 17:
                  _context2.prev = 17;
                  _context2.t0 = _context2["catch"](0);
                  message/* default */.ZP.error('H\xE3y ch\u1ECDn \xEDt nh\u1EA5t m\u1ED9t vai tr\xF2 v\xE0 th\u1EED l\u1EA1i');
                case 20:
                  _context2.prev = 20;
                  setLoading(false);
                  return _context2.finish(20);
                case 23:
                case "end":
                  return _context2.stop();
              }
            }, _callee2, null, [[0, 17, 20, 23]]);
          }));
          return function (_x) {
            return _ref7.apply(this, arguments);
          };
        }()),
        children: [/*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
          gutter: 16,
          children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
            md: 12,
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(UpdateNewRole_Item, {
              label: formatMessage({
                id: 'common.role_name'
              }),
              rules: [{
                required: true,
                message: 'B\u1EAFt bu\u1ED9c \u0111i\u1EC1n'
              }],
              name: "label",
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(input/* default */.Z, {})
            })
          }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
            span: 24,
            style: {
              textAlign: 'left',
              marginBottom: '16px'
            },
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
              size: "small",
              type: "default",
              onClick: handleReset,
              children: "T\\u1EA1o l\\u1EA1i"
            })
          })]
        }), /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
          gutter: 16,
          children: [/*#__PURE__*/(0,jsx_runtime.jsxs)(col/* default */.Z, {
            md: 6,
            children: [/*#__PURE__*/(0,jsx_runtime.jsx)(Title, {
              level: 5,
              children: "Qu\\u1EA3n tr\\u1ECB vi\\xEAn"
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(UpdateNewRole_Item, {
              name: "admin_section",
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_checkbox/* default */.Z.Group, {
                options: sectionCategories.System
              })
            })]
          }), /*#__PURE__*/(0,jsx_runtime.jsxs)(col/* default */.Z, {
            md: 6,
            children: [/*#__PURE__*/(0,jsx_runtime.jsx)(Title, {
              level: 5,
              children: "Quy\\u1EC1n d\\u1EF1 \\xE1n"
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(UpdateNewRole_Item, {
              name: "project_section",
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_checkbox/* default */.Z.Group, {
                options: sectionCategories.Project
              })
            })]
          }), /*#__PURE__*/(0,jsx_runtime.jsxs)(col/* default */.Z, {
            md: 6,
            children: [/*#__PURE__*/(0,jsx_runtime.jsx)(Title, {
              level: 5,
              children: "Quy\\u1EC1n khu v\\u1EF1c"
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(UpdateNewRole_Item, {
              name: "zone_section",
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_checkbox/* default */.Z.Group, {
                options: sectionCategories.Zone
              })
            })]
          }), /*#__PURE__*/(0,jsx_runtime.jsxs)(col/* default */.Z, {
            md: 6,
            children: [/*#__PURE__*/(0,jsx_runtime.jsx)(Title, {
              level: 5,
              children: "Quy\\u1EC1n c\\xE2y tr\\u1ED3ng"
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(UpdateNewRole_Item, {
              name: "plant_section",
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_checkbox/* default */.Z.Group, {
                options: sectionCategories.Plant
              })
            })]
          })]
        }), /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
          gutter: 16,
          children: [/*#__PURE__*/(0,jsx_runtime.jsxs)(col/* default */.Z, {
            md: 6,
            children: [/*#__PURE__*/(0,jsx_runtime.jsx)(Title, {
              level: 5,
              children: "Quy\\u1EC1n m\\xF9a v\\u1EE5"
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(UpdateNewRole_Item, {
              name: "crop_section",
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_checkbox/* default */.Z.Group, {
                options: sectionCategories.Crop
              })
            })]
          }), /*#__PURE__*/(0,jsx_runtime.jsxs)(col/* default */.Z, {
            md: 6,
            children: [/*#__PURE__*/(0,jsx_runtime.jsx)(Title, {
              level: 5,
              children: "Quy\\u1EC1n giai \\u0111o\\u1EA1n"
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(UpdateNewRole_Item, {
              name: "state_section",
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_checkbox/* default */.Z.Group, {
                options: sectionCategories.State
              })
            })]
          }), /*#__PURE__*/(0,jsx_runtime.jsxs)(col/* default */.Z, {
            md: 6,
            children: [/*#__PURE__*/(0,jsx_runtime.jsx)(Title, {
              level: 5,
              children: "Quy\\u1EC1n c\\xF4ng vi\\u1EC7c"
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(UpdateNewRole_Item, {
              name: "task_section",
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_checkbox/* default */.Z.Group, {
                options: sectionCategories.Task
              })
            })]
          }), /*#__PURE__*/(0,jsx_runtime.jsxs)(col/* default */.Z, {
            md: 6,
            children: [/*#__PURE__*/(0,jsx_runtime.jsx)(Title, {
              level: 5,
              children: "Quy\\u1EC1n h\\xE0ng h\\xF3a"
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(UpdateNewRole_Item, {
              name: "category_section",
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_checkbox/* default */.Z.Group, {
                options: sectionCategories.Category
              })
            })]
          })]
        }), /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
          gutter: 16,
          children: [/*#__PURE__*/(0,jsx_runtime.jsxs)(col/* default */.Z, {
            md: 6,
            children: [/*#__PURE__*/(0,jsx_runtime.jsx)(Title, {
              level: 5,
              children: "Quy\\u1EC1n kho"
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(UpdateNewRole_Item, {
              name: "storage_section",
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_checkbox/* default */.Z.Group, {
                options: sectionCategories.Storage
              })
            })]
          }), /*#__PURE__*/(0,jsx_runtime.jsxs)(col/* default */.Z, {
            md: 6,
            children: [/*#__PURE__*/(0,jsx_runtime.jsx)(Title, {
              level: 5,
              children: "Quy\\u1EC1n t\\u1ED3n kho"
            }), /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
              style: {
                display: 'flex',
                flexDirection: 'column',
                gap: '0px'
              },
              children: [/*#__PURE__*/(0,jsx_runtime.jsx)(UpdateNewRole_Item, {
                name: "category_inventory_section",
                style: {
                  marginBottom: '0px'
                },
                children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_checkbox/* default */.Z.Group, {
                  options: sectionCategories.CategoryInventory
                })
              }), /*#__PURE__*/(0,jsx_runtime.jsx)(es_checkbox/* default */.Z, {
                onChange: handleFieldLevelPermissionsChange,
                defaultChecked: showFieldLevelPermissions,
                style: {
                  marginTop: '0px'
                },
                children: "Quy\\u1EC1n chi ti\\u1EBFt"
              }), showFieldLevelPermissions && /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
                style: {
                  paddingLeft: '24px',
                  marginTop: '8px'
                },
                children: /*#__PURE__*/(0,jsx_runtime.jsx)(UpdateNewRole_Item, {
                  name: "category_inventory_field_level_section",
                  children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_checkbox/* default */.Z.Group, {
                    options: sectionCategories.CategoryInventoryFieldLevel
                  })
                })
              })]
            })]
          }), /*#__PURE__*/(0,jsx_runtime.jsxs)(col/* default */.Z, {
            md: 6,
            children: [/*#__PURE__*/(0,jsx_runtime.jsx)(Title, {
              level: 5,
              children: "Qu\\u1EA3n l\\xFD nh\\xE2n vi\\xEAn"
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(UpdateNewRole_Item, {
              name: "employee_section",
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_checkbox/* default */.Z.Group, {
                options: sectionCategories.Employee
              })
            })]
          }), /*#__PURE__*/(0,jsx_runtime.jsxs)(col/* default */.Z, {
            md: 6,
            children: [/*#__PURE__*/(0,jsx_runtime.jsx)(Title, {
              level: 5,
              children: "Qu\\u1EA3n l\\xFD vai tr\\xF2"
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(UpdateNewRole_Item, {
              name: "dynamic_role_section",
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_checkbox/* default */.Z.Group, {
                options: sectionCategories.DynamicRole
              })
            })]
          })]
        }), /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
          gutter: 16,
          children: [/*#__PURE__*/(0,jsx_runtime.jsxs)(col/* default */.Z, {
            md: 6,
            children: [/*#__PURE__*/(0,jsx_runtime.jsx)(Title, {
              level: 5,
              children: "Qu\\u1EA3n l\\xFD ch\\u1EA5m c\\xF4ng"
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(UpdateNewRole_Item, {
              name: "dynamic_timekeeping_section",
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_checkbox/* default */.Z.Group, {
                options: sectionCategories.Timekeeping
              })
            })]
          }), /*#__PURE__*/(0,jsx_runtime.jsxs)(col/* default */.Z, {
            md: 6,
            children: [/*#__PURE__*/(0,jsx_runtime.jsx)(Title, {
              level: 5,
              children: "Kh\\xE1ch tham quan"
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(UpdateNewRole_Item, {
              name: "visitor_section",
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_checkbox/* default */.Z.Group, {
                options: sectionCategories.Visitor
              })
            })]
          }), /*#__PURE__*/(0,jsx_runtime.jsxs)(col/* default */.Z, {
            md: 6,
            children: [/*#__PURE__*/(0,jsx_runtime.jsx)(Title, {
              level: 5,
              children: "Qu\\u1EA3n l\\xFD thi\\u1EBFt b\\u1ECB IoT"
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(UpdateNewRole_Item, {
              name: "iot_device_section",
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_checkbox/* default */.Z.Group, {
                options: sectionCategories.IoTDevice
              })
            })]
          })]
        })]
      })
    })]
  });
};
/* harmony default export */ var UpdateNewRole = (UpdateRoleForm);
;// CONCATENATED MODULE: ./src/pages/MyUser/DynamicRole/index.tsx















var CustomerUser = function CustomerUser() {
  var _initialState$current;
  var tableRef = (0,react.useRef)();
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  var _useSearchParams = (0,_umi_production_exports.useSearchParams)(),
    _useSearchParams2 = slicedToArray_default()(_useSearchParams, 2),
    searchParams = _useSearchParams2[0],
    setSearchParams = _useSearchParams2[1];
  var _useModel = (0,_umi_production_exports.useModel)('@@initialState'),
    initialState = _useModel.initialState;
  var customer_name = initialState === null || initialState === void 0 || (_initialState$current = initialState.currentUser) === null || _initialState$current === void 0 ? void 0 : _initialState$current.customer_id;
  if (!customer_name) return /*#__PURE__*/(0,jsx_runtime.jsx)(jsx_runtime.Fragment, {});
  var access = (0,_umi_production_exports.useAccess)();
  var canRead = access.canAccessPageRoleManagement();
  var canCreate = access.canCreateInRoleManagement();
  var canUpdate = access.canUpdateInRoleManagement();
  var canDelete = access.canDeleteInRoleManagement();
  var columns = [{
    title: formatMessage({
      id: 'common.action'
    }),
    dataIndex: 'name',
    render: function render(dom, entity) {
      return /*#__PURE__*/(0,jsx_runtime.jsxs)(space/* default */.Z, {
        size: "middle",
        children: [canUpdate && /*#__PURE__*/(0,jsx_runtime.jsx)(UpdateNewRole, {
          refreshFnc: reloadTable,
          user_info: entity
        }), canDelete && /*#__PURE__*/(0,jsx_runtime.jsx)(Components_DeleteRole, {
          refreshFnc: reloadTable,
          user_info: entity
        })]
      });
    },
    fixed: 'left',
    width: 30
  }, {
    title: 'ID',
    dataIndex: 'name',
    width: 80,
    hideInSearch: true,
    hideInTable: true
  }, {
    title: formatMessage({
      id: 'common.role'
    }),
    dataIndex: 'label',
    width: 80
  }, {
    title: 'Sections',
    dataIndex: 'sections',
    width: 80,
    ellipsis: true
  }];
  var reloadTable = /*#__PURE__*/function () {
    var _ref = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
      var _tableRef$current;
      return regeneratorRuntime_default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            (_tableRef$current = tableRef.current) === null || _tableRef$current === void 0 || _tableRef$current.reload();
          case 1:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }));
    return function reloadTable() {
      return _ref.apply(this, arguments);
    };
  }();
  return /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.Access, {
    accessible: canRead,
    fallback: /*#__PURE__*/(0,jsx_runtime.jsx)(FallbackContent/* default */.Z, {}),
    children: /*#__PURE__*/(0,jsx_runtime.jsx)(PageContainer/* PageContainer */._z, {
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(config_provider/* default */.ZP, {
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(Table/* default */.Z, {
          size: "small",
          actionRef: tableRef,
          rowKey: "name",
          request: ( /*#__PURE__*/function () {
            var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2(params, sort, filter) {
              var order_by, current, pageSize, searchFields, filterArr, result;
              return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
                while (1) switch (_context2.prev = _context2.next) {
                  case 0:
                    order_by = 'modified desc';
                    if (Object.keys(sort).length) {
                      order_by = "".concat(Object.keys(sort)[0], " ").concat(Object.values(sort)[0] === 'ascend' ? 'asc' : 'desc');
                    }
                    current = params.current, pageSize = params.pageSize;
                    searchFields = Object.keys(params).filter(function (field) {
                      var value = params[field];
                      return field !== 'current' && field !== 'pageSize' && value !== 'all';
                    });
                    filterArr = searchFields.map(function (field) {
                      var value = params[field];
                      return ['iot_customer_user', field, 'like', "%".concat(value, "%")];
                    });
                    if (customer_name) {
                      filterArr.push(['iot_dynamic_role', 'iot_customer', 'like', customer_name]);
                    }
                    _context2.prev = 6;
                    _context2.next = 9;
                    return (0,sscript/* sscriptGeneralList */.RB)({
                      doc_name: 'iot_dynamic_role',
                      filters: filterArr,
                      page: current ? current : 0 + 1,
                      size: pageSize,
                      fields: ['*'],
                      order_by: order_by
                    });
                  case 9:
                    result = _context2.sent;
                    console.log('result', result);
                    return _context2.abrupt("return", {
                      data: result.data,
                      success: true,
                      total: result.pagination.totalElements
                    });
                  case 14:
                    _context2.prev = 14;
                    _context2.t0 = _context2["catch"](6);
                    console.log(_context2.t0);
                  case 17:
                    _context2.prev = 17;
                    return _context2.finish(17);
                  case 19:
                  case "end":
                    return _context2.stop();
                }
              }, _callee2, null, [[6, 14, 17, 19]]);
            }));
            return function (_x, _x2, _x3) {
              return _ref2.apply(this, arguments);
            };
          }()),
          bordered: true,
          columns: columns,
          search: false,
          toolBarRender: function toolBarRender() {
            if (canCreate) {
              return [/*#__PURE__*/(0,jsx_runtime.jsx)(CreateNewRole/* default */.Z, {
                refreshFnc: reloadTable,
                customer_id: customer_name
              }, "create")];
            } else return [];
          },
          pagination: {
            defaultPageSize: 20,
            showSizeChanger: true,
            pageSizeOptions: ['20', '50', '100']
          }
        })
      })
    })
  });
};
/* harmony default export */ var DynamicRole = (CustomerUser);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///4544
`)},40063:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){"use strict";eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   J9: function() { return /* binding */ getCustomerUserList; },
/* harmony export */   Lf: function() { return /* binding */ listDynamicRoleAllSection; },
/* harmony export */   cb: function() { return /* binding */ updateCustomerUser; },
/* harmony export */   f6: function() { return /* binding */ createDynamicRole; },
/* harmony export */   fh: function() { return /* binding */ updateDynamicRole; },
/* harmony export */   jt: function() { return /* binding */ customerUserListAll; },
/* harmony export */   rX: function() { return /* binding */ removeDynamicRole; },
/* harmony export */   w: function() { return /* binding */ getDynamicRole; },
/* harmony export */   y_: function() { return /* binding */ createCustomerUser; }
/* harmony export */ });
/* unused harmony exports IIotDynamicRole, getCustomerUserIndividualList, deleteCustomerUser, deleteCustomerUserCredential */
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_createClass_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(72004);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_createClass_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_createClass_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_classCallCheck_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(12444);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_classCallCheck_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_classCallCheck_js__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_defineProperty_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(9783);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_defineProperty_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_defineProperty_js__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(7837);
/* harmony import */ var _sscript__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(39750);
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(467);








var IIotDynamicRole = /*#__PURE__*/(/* unused pure expression or super */ null && (_createClass(function IIotDynamicRole() {
  _classCallCheck(this, IIotDynamicRole);
  _defineProperty(this, "name", void 0);
  _defineProperty(this, "label", void 0);
  // Data
  _defineProperty(this, "role", void 0);
  // Data
  _defineProperty(this, "iot_customer", void 0);
  // Link
  _defineProperty(this, "sections", void 0);
} // Data
)));
var createCustomerUser = /*#__PURE__*/function () {
  var _ref = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_5__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_7__/* .generateAPIPath */ .rH)('api/v2/register/customer-user-with-role'), {
            method: 'POST',
            data: data
          });
        case 2:
          res = _context.sent;
          return _context.abrupt("return", res);
        case 4:
        case "end":
          return _context.stop();
      }
    }, _callee);
  }));
  return function createCustomerUser(_x) {
    return _ref.apply(this, arguments);
  };
}();
var getCustomerUserList = /*#__PURE__*/function () {
  var _ref2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee2(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          _context2.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_5__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_7__/* .generateAPIPath */ .rH)('api/v2/customerUser/user'), {
            params: (0,_utils__WEBPACK_IMPORTED_MODULE_7__/* .getParamsReqList */ .vj)(params)
          });
        case 2:
          res = _context2.sent;
          return _context2.abrupt("return", res.result);
        case 4:
        case "end":
          return _context2.stop();
      }
    }, _callee2);
  }));
  return function getCustomerUserList(_x2) {
    return _ref2.apply(this, arguments);
  };
}();
var getCustomerUserIndividualList = /*#__PURE__*/(/* unused pure expression or super */ null && (function () {
  var _ref3 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee3(params) {
    var res;
    return _regeneratorRuntime().wrap(function _callee3$(_context3) {
      while (1) switch (_context3.prev = _context3.next) {
        case 0:
          _context3.next = 2;
          return request(generateAPIPath('api/v2/customerUser/user/individual'), {
            params: getParamsReqList(params)
          });
        case 2:
          res = _context3.sent;
          return _context3.abrupt("return", res.result);
        case 4:
        case "end":
          return _context3.stop();
      }
    }, _callee3);
  }));
  return function getCustomerUserIndividualList(_x3) {
    return _ref3.apply(this, arguments);
  };
}()));
function customerUserListAll() {
  return _customerUserListAll.apply(this, arguments);
}

//update customer user
function _customerUserListAll() {
  _customerUserListAll = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee7() {
    var result;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee7$(_context7) {
      while (1) switch (_context7.prev = _context7.next) {
        case 0:
          _context7.prev = 0;
          _context7.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_5__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_7__/* .generateAPIPath */ .rH)("api/v2/customerUser/user"), {
            method: 'GET',
            params: {
              fields: ['*']
            }
          });
        case 3:
          result = _context7.sent;
          return _context7.abrupt("return", result.result);
        case 7:
          _context7.prev = 7;
          _context7.t0 = _context7["catch"](0);
          console.log(_context7.t0);
          throw _context7.t0;
        case 11:
        case "end":
          return _context7.stop();
      }
    }, _callee7, null, [[0, 7]]);
  }));
  return _customerUserListAll.apply(this, arguments);
}
var updateCustomerUser = /*#__PURE__*/function () {
  var _ref4 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee4(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee4$(_context4) {
      while (1) switch (_context4.prev = _context4.next) {
        case 0:
          _context4.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_5__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_7__/* .generateAPIPath */ .rH)('api/v2/customerUser/user'), {
            method: 'PUT',
            data: data
          });
        case 2:
          res = _context4.sent;
          return _context4.abrupt("return", res);
        case 4:
        case "end":
          return _context4.stop();
      }
    }, _callee4);
  }));
  return function updateCustomerUser(_x4) {
    return _ref4.apply(this, arguments);
  };
}();

//delete customer user
var deleteCustomerUser = /*#__PURE__*/(/* unused pure expression or super */ null && (function () {
  var _ref5 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee5(name) {
    var res;
    return _regeneratorRuntime().wrap(function _callee5$(_context5) {
      while (1) switch (_context5.prev = _context5.next) {
        case 0:
          _context5.next = 2;
          return request(generateAPIPath('api/v2/customerUser/user'), {
            method: 'DELETE',
            params: {
              name: name
            }
          });
        case 2:
          res = _context5.sent;
          return _context5.abrupt("return", res);
        case 4:
        case "end":
          return _context5.stop();
      }
    }, _callee5);
  }));
  return function deleteCustomerUser(_x5) {
    return _ref5.apply(this, arguments);
  };
}()));

//delete customer user credential
var deleteCustomerUserCredential = /*#__PURE__*/(/* unused pure expression or super */ null && (function () {
  var _ref6 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee6(name) {
    var res;
    return _regeneratorRuntime().wrap(function _callee6$(_context6) {
      while (1) switch (_context6.prev = _context6.next) {
        case 0:
          _context6.next = 2;
          return request(generateAPIPath('api/v2/customerUser/user-credential'), {
            method: 'DELETE',
            params: {
              name: name
            }
          });
        case 2:
          res = _context6.sent;
          return _context6.abrupt("return", res);
        case 4:
        case "end":
          return _context6.stop();
      }
    }, _callee6);
  }));
  return function deleteCustomerUserCredential(_x6) {
    return _ref6.apply(this, arguments);
  };
}()));
/**\r
 *\r
 * DYNAMIC ROLE APIs\r
 */
function listDynamicRoleAllSection() {
  return _listDynamicRoleAllSection.apply(this, arguments);
}
function _listDynamicRoleAllSection() {
  _listDynamicRoleAllSection = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee8() {
    var result;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee8$(_context8) {
      while (1) switch (_context8.prev = _context8.next) {
        case 0:
          _context8.prev = 0;
          _context8.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_5__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_7__/* .generateAPIPath */ .rH)("api/v2/dynamicRole/listAllSection"), {
            method: 'GET'
          });
        case 3:
          result = _context8.sent;
          return _context8.abrupt("return", result.result);
        case 7:
          _context8.prev = 7;
          _context8.t0 = _context8["catch"](0);
          console.log(_context8.t0);
          throw _context8.t0;
        case 11:
        case "end":
          return _context8.stop();
      }
    }, _callee8, null, [[0, 7]]);
  }));
  return _listDynamicRoleAllSection.apply(this, arguments);
}
function getDynamicRole() {
  return _getDynamicRole.apply(this, arguments);
}
function _getDynamicRole() {
  _getDynamicRole = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee9() {
    var result;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee9$(_context9) {
      while (1) switch (_context9.prev = _context9.next) {
        case 0:
          _context9.prev = 0;
          _context9.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_5__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_7__/* .generateAPIPath */ .rH)("api/v2/dynamicRole"), {
            method: 'GET',
            params: {
              page: 1,
              size: 100
            }
          });
        case 3:
          result = _context9.sent;
          return _context9.abrupt("return", result.result.data);
        case 7:
          _context9.prev = 7;
          _context9.t0 = _context9["catch"](0);
          console.log(_context9.t0);
          throw _context9.t0;
        case 11:
        case "end":
          return _context9.stop();
      }
    }, _callee9, null, [[0, 7]]);
  }));
  return _getDynamicRole.apply(this, arguments);
}
function createDynamicRole(_x7) {
  return _createDynamicRole.apply(this, arguments);
}
function _createDynamicRole() {
  _createDynamicRole = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee10(data) {
    var result;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee10$(_context10) {
      while (1) switch (_context10.prev = _context10.next) {
        case 0:
          _context10.prev = 0;
          _context10.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_5__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_7__/* .generateAPIPath */ .rH)("api/v2/dynamicRole"), {
            method: 'POST',
            data: data
          });
        case 3:
          result = _context10.sent;
          return _context10.abrupt("return", result.result);
        case 7:
          _context10.prev = 7;
          _context10.t0 = _context10["catch"](0);
          console.log(_context10.t0);
          throw _context10.t0;
        case 11:
        case "end":
          return _context10.stop();
      }
    }, _callee10, null, [[0, 7]]);
  }));
  return _createDynamicRole.apply(this, arguments);
}
function updateDynamicRole(_x8) {
  return _updateDynamicRole.apply(this, arguments);
}
function _updateDynamicRole() {
  _updateDynamicRole = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee11(data) {
    var result;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee11$(_context11) {
      while (1) switch (_context11.prev = _context11.next) {
        case 0:
          _context11.prev = 0;
          _context11.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_5__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_7__/* .generateAPIPath */ .rH)("api/v2/dynamicRole"), {
            method: 'PUT',
            data: data
          });
        case 3:
          result = _context11.sent;
          return _context11.abrupt("return", result.result);
        case 7:
          _context11.prev = 7;
          _context11.t0 = _context11["catch"](0);
          console.log(_context11.t0);
          throw _context11.t0;
        case 11:
        case "end":
          return _context11.stop();
      }
    }, _callee11, null, [[0, 7]]);
  }));
  return _updateDynamicRole.apply(this, arguments);
}
function removeDynamicRole(_x9) {
  return _removeDynamicRole.apply(this, arguments);
}
function _removeDynamicRole() {
  _removeDynamicRole = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee12(data) {
    var name, result;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee12$(_context12) {
      while (1) switch (_context12.prev = _context12.next) {
        case 0:
          _context12.prev = 0;
          name = data.name ? data.name : '';
          _context12.next = 4;
          return (0,_sscript__WEBPACK_IMPORTED_MODULE_6__/* .generalDelete */ .ID)('iot_dynamic_role', name);
        case 4:
          result = _context12.sent;
          return _context12.abrupt("return", result);
        case 8:
          _context12.prev = 8;
          _context12.t0 = _context12["catch"](0);
          throw _context12.t0;
        case 11:
        case "end":
          return _context12.stop();
      }
    }, _callee12, null, [[0, 8]]);
  }));
  return _removeDynamicRole.apply(this, arguments);
}//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///40063
`)},75302:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){"use strict";eval(`/* harmony import */ var _hooks_useBreakpoint__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(25378);
"use client";




// Do not export params
function useBreakpoint() {
  return (0,_hooks_useBreakpoint__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .Z)();
}

/* harmony default export */ __webpack_exports__.ZP = ({
  useBreakpoint
});//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNzUzMDIuanMiLCJtYXBwaW5ncyI6IjtBQUFBOztBQUV3QjtBQUNrQztBQUNsQztBQUN4QjtBQUNBO0FBQ0EsU0FBUyx5RUFBcUI7QUFDOUI7QUFDb0I7QUFDcEIsdURBQWU7QUFDZjtBQUNBLENBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hbnQtZGVzaWduLXByby8uL25vZGVfbW9kdWxlcy9hbnRkL2VzL2dyaWQvaW5kZXguanM/YTQxNyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcblxuaW1wb3J0IENvbCBmcm9tICcuL2NvbCc7XG5pbXBvcnQgdXNlSW50ZXJuYWxCcmVha3BvaW50IGZyb20gJy4vaG9va3MvdXNlQnJlYWtwb2ludCc7XG5pbXBvcnQgUm93IGZyb20gJy4vcm93Jztcbi8vIERvIG5vdCBleHBvcnQgcGFyYW1zXG5mdW5jdGlvbiB1c2VCcmVha3BvaW50KCkge1xuICByZXR1cm4gdXNlSW50ZXJuYWxCcmVha3BvaW50KCk7XG59XG5leHBvcnQgeyBDb2wsIFJvdyB9O1xuZXhwb3J0IGRlZmF1bHQge1xuICB1c2VCcmVha3BvaW50XG59OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==
//# sourceURL=webpack-internal:///75302
`)},85576:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){"use strict";eval(`
// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  Z: function() { return /* binding */ modal; }
});

// EXTERNAL MODULE: ./node_modules/antd/es/modal/confirm.js
var modal_confirm = __webpack_require__(56080);
// EXTERNAL MODULE: ./node_modules/antd/es/modal/destroyFns.js
var destroyFns = __webpack_require__(38657);
// EXTERNAL MODULE: ./node_modules/antd/es/modal/Modal.js + 1 modules
var Modal = __webpack_require__(56745);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./node_modules/classnames/index.js
var classnames = __webpack_require__(93967);
var classnames_default = /*#__PURE__*/__webpack_require__.n(classnames);
// EXTERNAL MODULE: ./node_modules/antd/node_modules/rc-dialog/es/index.js + 8 modules
var es = __webpack_require__(31058);
// EXTERNAL MODULE: ./node_modules/antd/es/_util/PurePanel.js
var PurePanel = __webpack_require__(8745);
// EXTERNAL MODULE: ./node_modules/antd/es/config-provider/context.js
var context = __webpack_require__(53124);
// EXTERNAL MODULE: ./node_modules/antd/es/modal/ConfirmDialog.js + 3 modules
var ConfirmDialog = __webpack_require__(32409);
// EXTERNAL MODULE: ./node_modules/antd/es/modal/shared.js + 2 modules
var shared = __webpack_require__(4941);
// EXTERNAL MODULE: ./node_modules/antd/es/modal/style/index.js
var style = __webpack_require__(71194);
// EXTERNAL MODULE: ./node_modules/antd/es/config-provider/hooks/useCSSVarCls.js
var useCSSVarCls = __webpack_require__(35792);
;// CONCATENATED MODULE: ./node_modules/antd/es/modal/PurePanel.js
"use client";

var __rest = undefined && undefined.__rest || function (s, e) {
  var t = {};
  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
  }
  return t;
};
/* eslint-disable react/jsx-no-useless-fragment */









const PurePanel_PurePanel = props => {
  const {
      prefixCls: customizePrefixCls,
      className,
      closeIcon,
      closable,
      type,
      title,
      children,
      footer
    } = props,
    restProps = __rest(props, ["prefixCls", "className", "closeIcon", "closable", "type", "title", "children", "footer"]);
  const {
    getPrefixCls
  } = react.useContext(context/* ConfigContext */.E_);
  const rootPrefixCls = getPrefixCls();
  const prefixCls = customizePrefixCls || getPrefixCls('modal');
  const rootCls = (0,useCSSVarCls/* default */.Z)(rootPrefixCls);
  const [wrapCSSVar, hashId, cssVarCls] = (0,style/* default */.ZP)(prefixCls, rootCls);
  const confirmPrefixCls = \`\${prefixCls}-confirm\`;
  // Choose target props by confirm mark
  let additionalProps = {};
  if (type) {
    additionalProps = {
      closable: closable !== null && closable !== void 0 ? closable : false,
      title: '',
      footer: '',
      children: ( /*#__PURE__*/react.createElement(ConfirmDialog/* ConfirmContent */.O, Object.assign({}, props, {
        prefixCls: prefixCls,
        confirmPrefixCls: confirmPrefixCls,
        rootPrefixCls: rootPrefixCls,
        content: children
      })))
    };
  } else {
    additionalProps = {
      closable: closable !== null && closable !== void 0 ? closable : true,
      title,
      footer: footer !== null && /*#__PURE__*/react.createElement(shared/* Footer */.$, Object.assign({}, props)),
      children
    };
  }
  return wrapCSSVar( /*#__PURE__*/react.createElement(es/* Panel */.s, Object.assign({
    prefixCls: prefixCls,
    className: classnames_default()(hashId, \`\${prefixCls}-pure-panel\`, type && confirmPrefixCls, type && \`\${confirmPrefixCls}-\${type}\`, className, cssVarCls, rootCls)
  }, restProps, {
    closeIcon: (0,shared/* renderCloseIcon */.b)(prefixCls, closeIcon),
    closable: closable
  }, additionalProps)));
};
/* harmony default export */ var modal_PurePanel = ((0,PurePanel/* withPureRenderTheme */.i)(PurePanel_PurePanel));
// EXTERNAL MODULE: ./node_modules/antd/es/modal/useModal/index.js + 2 modules
var useModal = __webpack_require__(94423);
;// CONCATENATED MODULE: ./node_modules/antd/es/modal/index.js
"use client";






function modalWarn(props) {
  return (0,modal_confirm/* default */.ZP)((0,modal_confirm/* withWarn */.uW)(props));
}
const modal_Modal = Modal/* default */.Z;
modal_Modal.useModal = useModal/* default */.Z;
modal_Modal.info = function infoFn(props) {
  return (0,modal_confirm/* default */.ZP)((0,modal_confirm/* withInfo */.cw)(props));
};
modal_Modal.success = function successFn(props) {
  return (0,modal_confirm/* default */.ZP)((0,modal_confirm/* withSuccess */.vq)(props));
};
modal_Modal.error = function errorFn(props) {
  return (0,modal_confirm/* default */.ZP)((0,modal_confirm/* withError */.AQ)(props));
};
modal_Modal.warning = modalWarn;
modal_Modal.warn = modalWarn;
modal_Modal.confirm = function confirmFn(props) {
  return (0,modal_confirm/* default */.ZP)((0,modal_confirm/* withConfirm */.Au)(props));
};
modal_Modal.destroyAll = function destroyAllFn() {
  while (destroyFns/* default */.Z.length) {
    const close = destroyFns/* default */.Z.pop();
    if (close) {
      close();
    }
  }
};
modal_Modal.config = modal_confirm/* modalGlobalConfig */.ai;
modal_Modal._InternalPanelDoNotUseOrYouWillBeFired = modal_PurePanel;
if (false) {}
/* harmony default export */ var modal = (modal_Modal);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///85576
`)},49867:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){"use strict";eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   N: function() { return /* binding */ operationUnit; }
/* harmony export */ });
// eslint-disable-next-line import/prefer-default-export
const operationUnit = token => ({
  // FIXME: This use link but is a operation unit. Seems should be a colorPrimary.
  // And Typography use this to generate link style which should not do this.
  color: token.colorLink,
  textDecoration: 'none',
  outline: 'none',
  cursor: 'pointer',
  transition: \`color \${token.motionDurationSlow}\`,
  '&:focus, &:hover': {
    color: token.colorLinkHover
  },
  '&:active': {
    color: token.colorLinkActive
  }
});//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNDk4NjcuanMiLCJtYXBwaW5ncyI6Ijs7O0FBQUE7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1Qix5QkFBeUI7QUFDaEQ7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQSxDQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvYW50ZC9lcy9zdHlsZS9vcGVyYXRpb25Vbml0LmpzPzNjODIiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIGltcG9ydC9wcmVmZXItZGVmYXVsdC1leHBvcnRcbmV4cG9ydCBjb25zdCBvcGVyYXRpb25Vbml0ID0gdG9rZW4gPT4gKHtcbiAgLy8gRklYTUU6IFRoaXMgdXNlIGxpbmsgYnV0IGlzIGEgb3BlcmF0aW9uIHVuaXQuIFNlZW1zIHNob3VsZCBiZSBhIGNvbG9yUHJpbWFyeS5cbiAgLy8gQW5kIFR5cG9ncmFwaHkgdXNlIHRoaXMgdG8gZ2VuZXJhdGUgbGluayBzdHlsZSB3aGljaCBzaG91bGQgbm90IGRvIHRoaXMuXG4gIGNvbG9yOiB0b2tlbi5jb2xvckxpbmssXG4gIHRleHREZWNvcmF0aW9uOiAnbm9uZScsXG4gIG91dGxpbmU6ICdub25lJyxcbiAgY3Vyc29yOiAncG9pbnRlcicsXG4gIHRyYW5zaXRpb246IGBjb2xvciAke3Rva2VuLm1vdGlvbkR1cmF0aW9uU2xvd31gLFxuICAnJjpmb2N1cywgJjpob3Zlcic6IHtcbiAgICBjb2xvcjogdG9rZW4uY29sb3JMaW5rSG92ZXJcbiAgfSxcbiAgJyY6YWN0aXZlJzoge1xuICAgIGNvbG9yOiB0b2tlbi5jb2xvckxpbmtBY3RpdmVcbiAgfVxufSk7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9
//# sourceURL=webpack-internal:///49867
`)},59542:function(module){eval(`!function(e,t){ true?module.exports=t():0}(this,(function(){"use strict";var e="day";return function(t,i,s){var a=function(t){return t.add(4-t.isoWeekday(),e)},d=i.prototype;d.isoWeekYear=function(){return a(this).year()},d.isoWeek=function(t){if(!this.$utils().u(t))return this.add(7*(t-this.isoWeek()),e);var i,d,n,o,r=a(this),u=(i=this.isoWeekYear(),d=this.$u,n=(d?s.utc:s)().year(i).startOf("year"),o=4-n.isoWeekday(),n.isoWeekday()>4&&(o+=7),n.add(o,e));return r.diff(u,"week")+1},d.isoWeekday=function(e){return this.$utils().u(e)?this.day()||7:this.day(this.day()%7?e:e-7)};var n=d.startOf;d.startOf=function(e,t){var i=this.$utils(),s=!!i.u(t)||t;return"isoweek"===i.p(e)?s?this.date(this.date()-(this.isoWeekday()-1)).startOf("day"):this.date(this.date()-1-(this.isoWeekday()-1)+7).endOf("day"):n.bind(this)(e,t)}}}));//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNTk1NDIuanMiLCJtYXBwaW5ncyI6IkFBQUEsZUFBZSxLQUFvRCxvQkFBb0IsQ0FBOEgsQ0FBQyxrQkFBa0IsYUFBYSxZQUFZLHVCQUF1QixrQkFBa0IsaUNBQWlDLGVBQWUseUJBQXlCLHNCQUFzQix1QkFBdUIsK0RBQStELHdKQUF3SiwwQkFBMEIsMEJBQTBCLHNFQUFzRSxnQkFBZ0Isd0JBQXdCLGtDQUFrQyx5S0FBeUsiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hbnQtZGVzaWduLXByby8uL25vZGVfbW9kdWxlcy9kYXlqcy9wbHVnaW4vaXNvV2Vlay5qcz80NGI0Il0sInNvdXJjZXNDb250ZW50IjpbIiFmdW5jdGlvbihlLHQpe1wib2JqZWN0XCI9PXR5cGVvZiBleHBvcnRzJiZcInVuZGVmaW5lZFwiIT10eXBlb2YgbW9kdWxlP21vZHVsZS5leHBvcnRzPXQoKTpcImZ1bmN0aW9uXCI9PXR5cGVvZiBkZWZpbmUmJmRlZmluZS5hbWQ/ZGVmaW5lKHQpOihlPVwidW5kZWZpbmVkXCIhPXR5cGVvZiBnbG9iYWxUaGlzP2dsb2JhbFRoaXM6ZXx8c2VsZikuZGF5anNfcGx1Z2luX2lzb1dlZWs9dCgpfSh0aGlzLChmdW5jdGlvbigpe1widXNlIHN0cmljdFwiO3ZhciBlPVwiZGF5XCI7cmV0dXJuIGZ1bmN0aW9uKHQsaSxzKXt2YXIgYT1mdW5jdGlvbih0KXtyZXR1cm4gdC5hZGQoNC10Lmlzb1dlZWtkYXkoKSxlKX0sZD1pLnByb3RvdHlwZTtkLmlzb1dlZWtZZWFyPWZ1bmN0aW9uKCl7cmV0dXJuIGEodGhpcykueWVhcigpfSxkLmlzb1dlZWs9ZnVuY3Rpb24odCl7aWYoIXRoaXMuJHV0aWxzKCkudSh0KSlyZXR1cm4gdGhpcy5hZGQoNyoodC10aGlzLmlzb1dlZWsoKSksZSk7dmFyIGksZCxuLG8scj1hKHRoaXMpLHU9KGk9dGhpcy5pc29XZWVrWWVhcigpLGQ9dGhpcy4kdSxuPShkP3MudXRjOnMpKCkueWVhcihpKS5zdGFydE9mKFwieWVhclwiKSxvPTQtbi5pc29XZWVrZGF5KCksbi5pc29XZWVrZGF5KCk+NCYmKG8rPTcpLG4uYWRkKG8sZSkpO3JldHVybiByLmRpZmYodSxcIndlZWtcIikrMX0sZC5pc29XZWVrZGF5PWZ1bmN0aW9uKGUpe3JldHVybiB0aGlzLiR1dGlscygpLnUoZSk/dGhpcy5kYXkoKXx8Nzp0aGlzLmRheSh0aGlzLmRheSgpJTc/ZTplLTcpfTt2YXIgbj1kLnN0YXJ0T2Y7ZC5zdGFydE9mPWZ1bmN0aW9uKGUsdCl7dmFyIGk9dGhpcy4kdXRpbHMoKSxzPSEhaS51KHQpfHx0O3JldHVyblwiaXNvd2Vla1wiPT09aS5wKGUpP3M/dGhpcy5kYXRlKHRoaXMuZGF0ZSgpLSh0aGlzLmlzb1dlZWtkYXkoKS0xKSkuc3RhcnRPZihcImRheVwiKTp0aGlzLmRhdGUodGhpcy5kYXRlKCktMS0odGhpcy5pc29XZWVrZGF5KCktMSkrNykuZW5kT2YoXCJkYXlcIik6bi5iaW5kKHRoaXMpKGUsdCl9fX0pKTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=
//# sourceURL=webpack-internal:///59542
`)},79370:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){"use strict";eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   G: function() { return /* binding */ isStyleSupport; }
/* harmony export */ });
/* harmony import */ var _canUseDom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(98924);

var isStyleNameSupport = function isStyleNameSupport(styleName) {
  if ((0,_canUseDom__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .Z)() && window.document.documentElement) {
    var styleNameList = Array.isArray(styleName) ? styleName : [styleName];
    var documentElement = window.document.documentElement;
    return styleNameList.some(function (name) {
      return name in documentElement.style;
    });
  }
  return false;
};
var isStyleValueSupport = function isStyleValueSupport(styleName, value) {
  if (!isStyleNameSupport(styleName)) {
    return false;
  }
  var ele = document.createElement('div');
  var origin = ele.style[styleName];
  ele.style[styleName] = value;
  return ele.style[styleName] !== origin;
};
function isStyleSupport(styleName, styleValue) {
  if (!Array.isArray(styleName) && styleValue !== undefined) {
    return isStyleValueSupport(styleName, styleValue);
  }
  return isStyleNameSupport(styleName);
}//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNzkzNzAuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFvQztBQUNwQztBQUNBLE1BQU0sK0RBQVM7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hbnQtZGVzaWduLXByby8uL25vZGVfbW9kdWxlcy9yYy11dGlsL2VzL0RvbS9zdHlsZUNoZWNrZXIuanM/YWU2YyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY2FuVXNlRG9tIGZyb20gXCIuL2NhblVzZURvbVwiO1xudmFyIGlzU3R5bGVOYW1lU3VwcG9ydCA9IGZ1bmN0aW9uIGlzU3R5bGVOYW1lU3VwcG9ydChzdHlsZU5hbWUpIHtcbiAgaWYgKGNhblVzZURvbSgpICYmIHdpbmRvdy5kb2N1bWVudC5kb2N1bWVudEVsZW1lbnQpIHtcbiAgICB2YXIgc3R5bGVOYW1lTGlzdCA9IEFycmF5LmlzQXJyYXkoc3R5bGVOYW1lKSA/IHN0eWxlTmFtZSA6IFtzdHlsZU5hbWVdO1xuICAgIHZhciBkb2N1bWVudEVsZW1lbnQgPSB3aW5kb3cuZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50O1xuICAgIHJldHVybiBzdHlsZU5hbWVMaXN0LnNvbWUoZnVuY3Rpb24gKG5hbWUpIHtcbiAgICAgIHJldHVybiBuYW1lIGluIGRvY3VtZW50RWxlbWVudC5zdHlsZTtcbiAgICB9KTtcbiAgfVxuICByZXR1cm4gZmFsc2U7XG59O1xudmFyIGlzU3R5bGVWYWx1ZVN1cHBvcnQgPSBmdW5jdGlvbiBpc1N0eWxlVmFsdWVTdXBwb3J0KHN0eWxlTmFtZSwgdmFsdWUpIHtcbiAgaWYgKCFpc1N0eWxlTmFtZVN1cHBvcnQoc3R5bGVOYW1lKSkge1xuICAgIHJldHVybiBmYWxzZTtcbiAgfVxuICB2YXIgZWxlID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnZGl2Jyk7XG4gIHZhciBvcmlnaW4gPSBlbGUuc3R5bGVbc3R5bGVOYW1lXTtcbiAgZWxlLnN0eWxlW3N0eWxlTmFtZV0gPSB2YWx1ZTtcbiAgcmV0dXJuIGVsZS5zdHlsZVtzdHlsZU5hbWVdICE9PSBvcmlnaW47XG59O1xuZXhwb3J0IGZ1bmN0aW9uIGlzU3R5bGVTdXBwb3J0KHN0eWxlTmFtZSwgc3R5bGVWYWx1ZSkge1xuICBpZiAoIUFycmF5LmlzQXJyYXkoc3R5bGVOYW1lKSAmJiBzdHlsZVZhbHVlICE9PSB1bmRlZmluZWQpIHtcbiAgICByZXR1cm4gaXNTdHlsZVZhbHVlU3VwcG9ydChzdHlsZU5hbWUsIHN0eWxlVmFsdWUpO1xuICB9XG4gIHJldHVybiBpc1N0eWxlTmFtZVN1cHBvcnQoc3R5bGVOYW1lKTtcbn0iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=
//# sourceURL=webpack-internal:///79370
`)}}]);
