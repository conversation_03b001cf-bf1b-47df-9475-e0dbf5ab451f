"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[6818],{13490:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   W: function() { return /* binding */ DEFAULT_FALLBACK_IMG; }
/* harmony export */ });
var DEFAULT_FALLBACK_IMG = 'data:image/png;base64,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';//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///13490
`)},46229:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`
// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  Z: function() { return /* binding */ components_HighChartComponentCommon; }
});

// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/objectSpread2.js
var objectSpread2 = __webpack_require__(97857);
var objectSpread2_default = /*#__PURE__*/__webpack_require__.n(objectSpread2);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/slicedToArray.js
var slicedToArray = __webpack_require__(5574);
var slicedToArray_default = /*#__PURE__*/__webpack_require__.n(slicedToArray);
// EXTERNAL MODULE: ./src/utils/format.ts
var format = __webpack_require__(5251);
// EXTERNAL MODULE: ./node_modules/dayjs/dayjs.min.js
var dayjs_min = __webpack_require__(27484);
var dayjs_min_default = /*#__PURE__*/__webpack_require__.n(dayjs_min);
// EXTERNAL MODULE: ./node_modules/dayjs/plugin/localeData.js
var localeData = __webpack_require__(96036);
var localeData_default = /*#__PURE__*/__webpack_require__.n(localeData);
// EXTERNAL MODULE: ./node_modules/highcharts/highcharts.js
var highcharts = __webpack_require__(78840);
var highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts);
// EXTERNAL MODULE: ./node_modules/highcharts-react-official/dist/highcharts-react.min.js
var highcharts_react_min = __webpack_require__(75708);
var highcharts_react_min_default = /*#__PURE__*/__webpack_require__.n(highcharts_react_min);
// EXTERNAL MODULE: ./node_modules/highcharts/highcharts-more.js
var highcharts_more = __webpack_require__(44814);
var highcharts_more_default = /*#__PURE__*/__webpack_require__.n(highcharts_more);
// EXTERNAL MODULE: ./node_modules/lodash/lodash.js
var lodash = __webpack_require__(96486);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
;// CONCATENATED MODULE: ./src/components/HighChartComponentCommon/index.less
// extracted by mini-css-extract-plugin

// EXTERNAL MODULE: ./node_modules/react/jsx-runtime.js
var jsx_runtime = __webpack_require__(85893);
;// CONCATENATED MODULE: ./src/components/HighChartComponentCommon/index.tsx












__webpack_require__(2929)((highcharts_default()));
__webpack_require__(18393)((highcharts_default()));
__webpack_require__(17732)((highcharts_default()));
highcharts_more_default()((highcharts_default()));
dayjs_min_default().extend((localeData_default()));
highcharts_default().setOptions({
  lang: {
    months: dayjs_min_default().months(),
    weekdays: dayjs_min_default().weekdays(),
    shortMonths: dayjs_min_default().monthsShort(),
    shortWeekdays: dayjs_min_default().weekdaysShort()
  }
});
var defaultChartProps = {
  exporting: {
    enabled: true,
    buttons: {
      contextButton: {
        menuItems: ['downloadPNG', 'downloadSVG']
      }
    },
    chartOptions: {
      xAxis: [{
        min: 0,
        max: null
      }]
    }
  },
  chart: {},
  title: {
    text: '' // \u0110\u1EB7t text r\u1ED7ng \u0111\u1EC3 \u1EA9n ti\xEAu \u0111\u1EC1
    // Ho\u1EB7c d\xF9ng enabled: false \u0111\u1EC3 t\u1EAFt ho\xE0n to\xE0n: { enabled: false }
  },
  credits: {
    enabled: false
  },
  yAxis: {
    startOnTick: true,
    endOnTick: true,
    gridLineDashStyle: 'Dash',
    stackLabels: {
      enabled: false
    },
    scrollbar: {
      liveRedraw: false
    },
    events: {
      afterSetExtremes: function afterSetExtremes(e) {
        var _this$chart;
        (_this$chart = this.chart) === null || _this$chart === void 0 || (_this$chart = _this$chart.scrollingContainer) === null || _this$chart === void 0 || _this$chart.scrollTo({
          top: 0,
          behavior: 'instant'
        });
      }
    }
  },
  plotOptions: {
    series: {
      allowPointSelect: false
    },
    column: {
      borderColor: undefined,
      pointWidth: 20,
      dataLabels: {
        enabled: true,
        formatter: function formatter() {
          return (0,format/* formatNumberSummary */.tq)(this.y || 0);
        },
        style: {
          fontWeight: '400',
          textOutline: 'none'
        }
      }
    }
  },
  legend: {
    align: 'center',
    verticalAlign: 'top'
  }
};
var HighChartComponentCommon = function HighChartComponentCommon(props) {
  var chartProps = props.chartProps,
    chartCb = props.chartCb;
  var ref = (0,react.useRef)();
  var _useState = (0,react.useState)({
      sourceWidth: undefined,
      sourceHeight: undefined
    }),
    _useState2 = slicedToArray_default()(_useState, 2),
    exportState = _useState2[0],
    setExportState = _useState2[1];
  var chartPropsMerge = (0,react.useMemo)(function () {
    return (0,lodash.merge)((0,lodash.cloneDeep)(defaultChartProps), objectSpread2_default()(objectSpread2_default()({}, chartProps), {}, {
      exporting: objectSpread2_default()(objectSpread2_default()({}, chartProps.exporting), exportState)
    }));
  }, [chartProps, exportState]);
  var cb = function cb(chart) {
    if (chart.container) {
      ref.current = chart.container;
    }
    if (chartCb) {
      chartCb(chart);
    }
  };
  (0,react.useEffect)(function () {
    var myObserver = new ResizeObserver(function () {
      if (ref.current) {
        var width = ref.current.clientWidth;
        var height = ref.current.clientHeight;
        setExportState({
          sourceWidth: width,
          sourceHeight: height
        });
      }
    });
    if (ref.current) myObserver.observe(ref.current);
    return function () {
      if (ref.current) myObserver.unobserve(ref.current);
    };
  }, [ref.current]);
  if (!chartProps) return null;
  return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
    className: "chart-common-wrapper",
    children: /*#__PURE__*/(0,jsx_runtime.jsx)((highcharts_react_min_default()), {
      highcharts: (highcharts_default()),
      options: chartPropsMerge,
      callback: cb
    })
  });
};
/* harmony default export */ var components_HighChartComponentCommon = (HighChartComponentCommon);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///46229
`)},13334:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return /* binding */ DashboardNew; }
});

// EXTERNAL MODULE: ./node_modules/antd/es/config-provider/index.js + 7 modules
var config_provider = __webpack_require__(28459);
// EXTERNAL MODULE: ./node_modules/antd/es/row/index.js
var row = __webpack_require__(71230);
// EXTERNAL MODULE: ./node_modules/antd/es/col/index.js
var col = __webpack_require__(15746);
// EXTERNAL MODULE: ./node_modules/classnames/index.js
var classnames = __webpack_require__(93967);
var classnames_default = /*#__PURE__*/__webpack_require__.n(classnames);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/objectSpread2.js
var objectSpread2 = __webpack_require__(97857);
var objectSpread2_default = /*#__PURE__*/__webpack_require__.n(objectSpread2);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/regeneratorRuntime.js
var regeneratorRuntime = __webpack_require__(15009);
var regeneratorRuntime_default = /*#__PURE__*/__webpack_require__.n(regeneratorRuntime);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(99289);
var asyncToGenerator_default = /*#__PURE__*/__webpack_require__.n(asyncToGenerator);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/slicedToArray.js
var slicedToArray = __webpack_require__(5574);
var slicedToArray_default = /*#__PURE__*/__webpack_require__.n(slicedToArray);
// EXTERNAL MODULE: ./src/components/NoticeIcon/hook/useNoticeAlarmData.tsx + 1 modules
var useNoticeAlarmData = __webpack_require__(39484);
// EXTERNAL MODULE: ./src/components/NoticeIcon/hook/useUpdateIsRead.ts
var useUpdateIsRead = __webpack_require__(89474);
// EXTERNAL MODULE: ./src/components/NoticeIcon/hook/useUpdateReadAll.ts
var useUpdateReadAll = __webpack_require__(64263);
// EXTERNAL MODULE: ./src/pages/IoTDeviceMangement/components/MonitorDevice/index.tsx + 2 modules
var MonitorDevice = __webpack_require__(75806);
// EXTERNAL MODULE: ./src/utils/date.ts
var utils_date = __webpack_require__(28382);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/FieldTimeOutlined.js + 1 modules
var FieldTimeOutlined = __webpack_require__(36223);
// EXTERNAL MODULE: ./src/.umi-production/exports.ts + 34 modules
var _umi_production_exports = __webpack_require__(7837);
// EXTERNAL MODULE: ./node_modules/antd/es/avatar/index.js + 4 modules
var es_avatar = __webpack_require__(7134);
// EXTERNAL MODULE: ./node_modules/antd/es/divider/index.js + 1 modules
var divider = __webpack_require__(96074);
// EXTERNAL MODULE: ./node_modules/antd/es/skeleton/index.js + 12 modules
var skeleton = __webpack_require__(99559);
// EXTERNAL MODULE: ./node_modules/antd/es/card/index.js + 3 modules
var card = __webpack_require__(76216);
// EXTERNAL MODULE: ./node_modules/antd/es/button/index.js + 9 modules
var es_button = __webpack_require__(14726);
// EXTERNAL MODULE: ./node_modules/antd/es/spin/index.js + 2 modules
var spin = __webpack_require__(75081);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./node_modules/react-virtuoso/dist/index.mjs
var dist = __webpack_require__(26246);
// EXTERNAL MODULE: ./node_modules/react/jsx-runtime.js
var jsx_runtime = __webpack_require__(85893);
;// CONCATENATED MODULE: ./src/pages/DashboardNew/components/Alarm.tsx

















var AlarmItem = function AlarmItem(_ref) {
  var _data$data;
  var data = _ref.data,
    _onClick = _ref.onClick;
  var isWarning = (data === null || data === void 0 || (_data$data = data.data) === null || _data$data === void 0 ? void 0 : _data$data.type) !== 'task';
  return /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
    className: classnames_default()('flex items-start gap-3 py-1 px-2 cursor-pointer hover:shadow', data.read ? 'opacity-50' : ''),
    onClick: function onClick() {
      return _onClick === null || _onClick === void 0 ? void 0 : _onClick(data);
    },
    children: [/*#__PURE__*/(0,jsx_runtime.jsx)("div", {
      className: "flex-none",
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_avatar/* default */.C, {
        src: isWarning ? '/images/new-dashboard/bell.png' : '/images/new-dashboard/calendar.png',
        shape: "square"
      })
    }), /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
      className: "flex-1 gap-2",
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        className: "font-semibold",
        children: data.title
      }), /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        className: "flex items-center justify-between text-gray-400",
        children: /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
          className: "flex items-center",
          children: [/*#__PURE__*/(0,jsx_runtime.jsx)("span", {
            className: "mr-1 ",
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(FieldTimeOutlined/* default */.Z, {})
          }), /*#__PURE__*/(0,jsx_runtime.jsx)("span", {
            children: (0,utils_date/* dayjsUtil */.PF)(data.datetime).fromNow()
          })]
        })
      })]
    })]
  });
};
var Alarm = function Alarm(_ref2) {
  var _alarmRes$pagination;
  var children = _ref2.children;
  var mqttModel = (0,_umi_production_exports.useModel)('MQTTNotification');
  var _useState = (0,react.useState)(false),
    _useState2 = slicedToArray_default()(_useState, 2),
    openNotice = _useState2[0],
    setOpenNotice = _useState2[1];
  var _useNoticeAlarmData = (0,useNoticeAlarmData/* useNoticeAlarmData */.w)(),
    alarmRes = _useNoticeAlarmData.alarmRes,
    loadAlarm = _useNoticeAlarmData.loadAlarm,
    alarmNotices = _useNoticeAlarmData.alarmNotices,
    isLoadingAlarmNotice = _useNoticeAlarmData.isLoadingAlarmNotice,
    setAlarmNotices = _useNoticeAlarmData.setAlarmNotices,
    alarmPendingCount = _useNoticeAlarmData.alarmPendingCount;
  var _useUpdateIsRead = (0,useUpdateIsRead/* useUpdateIsRead */.X)(),
    updateIsRead = _useUpdateIsRead.run;
  var _useUpdateReadAll = (0,useUpdateReadAll/* useUpdateReadAll */.n)(),
    updateReadAll = _useUpdateReadAll.run,
    isUpdatingReadAll = _useUpdateReadAll.loading;
  var hashLoadMoreAlarm = ((alarmRes === null || alarmRes === void 0 ? void 0 : alarmRes.pagination.pageNumber) || 1) < ((alarmRes === null || alarmRes === void 0 || (_alarmRes$pagination = alarmRes.pagination) === null || _alarmRes$pagination === void 0 ? void 0 : _alarmRes$pagination.totalPages) || 1);
  var loadMoreAlarm = function loadMoreAlarm() {
    var _alarmRes$pagination2, _alarmRes$pagination3;
    if (isLoadingAlarmNotice) return;
    var pageNumber = ((alarmRes === null || alarmRes === void 0 || (_alarmRes$pagination2 = alarmRes.pagination) === null || _alarmRes$pagination2 === void 0 ? void 0 : _alarmRes$pagination2.pageNumber) || 1) + 1;
    if (alarmRes !== null && alarmRes !== void 0 && (_alarmRes$pagination3 = alarmRes.pagination) !== null && _alarmRes$pagination3 !== void 0 && _alarmRes$pagination3.totalPages && pageNumber > alarmRes.pagination.totalPages) {
      return;
    }
    loadAlarm({
      page: pageNumber
    });
  };
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;

  // const alarmNoticesData = useDeferredValue(getNoticeData(alarmNotices));
  /**\r
   * @description state for device\r
   */
  var _useState3 = (0,react.useState)(false),
    _useState4 = slicedToArray_default()(_useState3, 2),
    openMonitor = _useState4[0],
    setOpenMonitor = _useState4[1];
  var _useState5 = (0,react.useState)(null),
    _useState6 = slicedToArray_default()(_useState5, 2),
    deviceId = _useState6[0],
    setDeviceId = _useState6[1];
  var openDeviceDetail = function openDeviceDetail(deviceId) {
    setDeviceId(deviceId);
    setOpenMonitor(true);
  };
  var RenderFooter = function RenderFooter() {
    if (!hashLoadMoreAlarm) return /*#__PURE__*/(0,jsx_runtime.jsx)(divider/* default */.Z, {
      plain: true,
      children: "\\u0110\\xE3 t\\u1EA3i h\\u1EBFt th\\xF4ng b\\xE1o!"
    });
    return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
      style: {
        minHeight: 10
      },
      children: Array.from(new Array(2)).map(function (_item, index) {
        return /*#__PURE__*/(0,jsx_runtime.jsx)(skeleton/* default */.Z, {
          avatar: true,
          paragraph: {
            rows: 1
          },
          active: true
        }, index);
      })
    });
  };
  return /*#__PURE__*/(0,jsx_runtime.jsx)(card/* default */.Z, {
    title: formatMessage({
      id: 'new-dashboard.notice-and-alarm'
    }),
    extra: /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
      type: "link",
      loading: isUpdatingReadAll,
      onClick: /*#__PURE__*/asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
        return regeneratorRuntime_default()().wrap(function _callee$(_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              _context.next = 2;
              return updateReadAll();
            case 2:
            case "end":
              return _context.stop();
          }
        }, _callee);
      })),
      children: formatMessage({
        id: 'new-dashboard.see-all'
      })
    }),
    styles: {
      body: {
        padding: 0,
        paddingBlockEnd: 10
      }
    },
    children: /*#__PURE__*/(0,jsx_runtime.jsxs)(spin/* default */.Z, {
      spinning: isLoadingAlarmNotice,
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        className: " py-2 px-6 ",
        children: !alarmNotices || alarmNotices.length === 0 ? /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
          children: [/*#__PURE__*/(0,jsx_runtime.jsx)("img", {
            src: "https://gw.alipayobjects.com/zos/rmsportal/sAuJeJzSKbUmHfBQRzmZ.svg",
            alt: "not found"
          }), /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
            children: "Ch\\u01B0a c\\xF3 d\\u1EEF li\\u1EC7u"
          })]
        }) : /*#__PURE__*/(0,jsx_runtime.jsx)(dist/* Virtuoso */.OO, {
          className: 'scrollbar-thin',
          style: {
            height: 350
          },
          data: alarmNotices,
          overscan: 500,
          endReached: loadMoreAlarm,
          itemContent: function itemContent(index, item) {
            return /*#__PURE__*/(0,jsx_runtime.jsx)(AlarmItem, {
              data: item,
              onClick: ( /*#__PURE__*/function () {
                var _ref4 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2(item) {
                  var _item$data3;
                  var dataUpdate, _item$data, _taskId, _item$data2, _deviceId, taskId;
                  return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
                    while (1) switch (_context2.prev = _context2.next) {
                      case 0:
                        if (!(item.read === false)) {
                          _context2.next = 10;
                          break;
                        }
                        _context2.prev = 1;
                        dataUpdate = objectSpread2_default()(objectSpread2_default()({}, item.data), {}, {
                          is_read: true
                        });
                        _context2.next = 5;
                        return updateIsRead({
                          name: dataUpdate.name,
                          is_read: dataUpdate.is_read
                        });
                      case 5:
                        mqttModel === null || mqttModel === void 0 || mqttModel.handleMessage.noticeHandle.emit.updateNoticeTask(dataUpdate);
                        _context2.next = 10;
                        break;
                      case 8:
                        _context2.prev = 8;
                        _context2.t0 = _context2["catch"](1);
                      case 10:
                        if (!['approval response', 'approval request'].includes(item.type)) {
                          _context2.next = 14;
                          break;
                        }
                        _taskId = item === null || item === void 0 || (_item$data = item.data) === null || _item$data === void 0 ? void 0 : _item$data.entity;
                        if (_taskId) _umi_production_exports.history.push("/employee-management/approval/details/".concat(_taskId));
                        return _context2.abrupt("return");
                      case 14:
                        if (!(item.type === 'device')) {
                          _context2.next = 18;
                          break;
                        }
                        _deviceId = item === null || item === void 0 || (_item$data2 = item.data) === null || _item$data2 === void 0 ? void 0 : _item$data2.entity;
                        openDeviceDetail(_deviceId);
                        return _context2.abrupt("return");
                      case 18:
                        //
                        taskId = item === null || item === void 0 || (_item$data3 = item.data) === null || _item$data3 === void 0 ? void 0 : _item$data3.entity;
                        if (taskId) _umi_production_exports.history.push("/farming-management/workflow-management/detail/".concat(taskId));
                      case 20:
                      case "end":
                        return _context2.stop();
                    }
                  }, _callee2, null, [[1, 8]]);
                }));
                return function (_x) {
                  return _ref4.apply(this, arguments);
                };
              }())
            }, item.id);
          },
          components: {
            Footer: RenderFooter
          }
        })
      }), openMonitor && deviceId && /*#__PURE__*/(0,jsx_runtime.jsx)(MonitorDevice["default"], {
        open: openMonitor,
        deviceId: deviceId,
        onOpenChange: setOpenMonitor
      })]
    })
  });
};
/* harmony default export */ var components_Alarm = (Alarm);
// EXTERNAL MODULE: ./src/common/contanst/constanst.ts
var constanst = __webpack_require__(86604);
// EXTERNAL MODULE: ./src/common/contanst/img.ts
var img = __webpack_require__(13490);
// EXTERNAL MODULE: ./src/services/cropManager.ts
var cropManager = __webpack_require__(77890);
// EXTERNAL MODULE: ./src/utils/file.ts
var file = __webpack_require__(80320);
// EXTERNAL MODULE: ./node_modules/antd/es/progress/index.js + 13 modules
var progress = __webpack_require__(38703);
;// CONCATENATED MODULE: ./src/pages/DashboardNew/hooks/useGetCropStage.ts




function useGetCropStage(_ref) {
  var cropId = _ref.cropId;
  return (0,_umi_production_exports.useRequest)( /*#__PURE__*/asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
    var res;
    return regeneratorRuntime_default()().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.next = 2;
          return (0,cropManager/* getCurrentStateOfCrop */.vW)(cropId);
        case 2:
          res = _context.sent;
          return _context.abrupt("return", {
            data: (res === null || res === void 0 ? void 0 : res[0]) || null
          });
        case 4:
        case "end":
          return _context.stop();
      }
    }, _callee);
  })), {
    refreshDeps: [cropId]
  });
}
// EXTERNAL MODULE: ./src/pages/IoTDeviceMangement/hooks/useDeviceOnlineChange.ts
var useDeviceOnlineChange = __webpack_require__(52180);
// EXTERNAL MODULE: ./src/services/devices.ts
var devices = __webpack_require__(84081);
;// CONCATENATED MODULE: ./src/pages/DashboardNew/components/Devices.tsx















var Tag = function Tag(_ref) {
  var isOffline = _ref.isOffline,
    children = _ref.children;
  var textColor = !isOffline ? 'text-[#52c41a]' : 'text-[#ff0000]';
  var bgColor = !isOffline ? 'bg-[#edf9f0]' : 'bg-[#feefef]';
  return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
    className: classnames_default()('px-0 py-1 text-xs font-medium', textColor),
    children: children ? children : isOffline ? 'Offline' : 'Online'
  });
};
var DeviceItem = function DeviceItem(_ref2) {
  var data = _ref2.data,
    onOpenMonitor = _ref2.onOpenMonitor;
  var lastedDataOnline = !!data.latest_data.find(function (item) {
    return item.key === 'online' && item.value === true;
  });
  var _useDeviceOnlineChang = (0,useDeviceOnlineChange/* useDeviceOnlineChange */.P)({
      deviceId: data.device_id_thingsboard,
      initOnline: lastedDataOnline
    }),
    isOnline = _useDeviceOnlineChang.isOnline;
  return /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
    onClick: function onClick() {
      return onOpenMonitor === null || onOpenMonitor === void 0 ? void 0 : onOpenMonitor(data.name);
    },
    className: "border bg-[#ffffff] p-4 min-w-[14rem] max-w-[14rem] h-24 flex flex-col justify-between cursor-pointer transition-all duration-300 hover:border-[#9DDBB1] hover:shadow-sm",
    children: [/*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
      className: "flex items-start gap-3",
      children: [/*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
        className: "flex-1 min-w-0",
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)("div", {
          className: "text-base font-medium text-gray-900 truncate",
          children: data.label
        }), /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
          className: "text-sm text-gray-500 mt-1 truncate",
          children: data.zone_label || '-'
        })]
      }), /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        className: "flex-none w-12",
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_avatar/* default */.C, {
          shape: "square",
          size: 44 // gi\u1EA3m size avatar xu\u1ED1ng m\u1ED9t ch\xFAt
          ,
          src: data !== null && data !== void 0 && data.device_profile_image ? (0,file/* genDownloadUrl */.h)(data.device_profile_image) : img/* DEFAULT_FALLBACK_IMG */.W,
          className: "!bg-white border"
        })
      })]
    }), /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
      className: "flex items-center justify-between",
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(Tag, {
        isOffline: !isOnline
      })
    })]
  });
};
var Devices = function Devices(_ref3) {
  var _data$data;
  var children = _ref3.children;
  var _useRequest = (0,_umi_production_exports.useRequest)( /*#__PURE__*/asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
      var res;
      return regeneratorRuntime_default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _context.next = 2;
            return (0,devices/* deviceInProjectList */.pL)({
              filters: [],
              page: 1,
              size: constanst/* DEFAULT_PAGE_SIZE_ALL */.YY,
              fields: ['*'],
              order_by: 'online'
              // project_id: projectId,
            });
          case 2:
            res = _context.sent;
            return _context.abrupt("return", {
              data: {
                total: res.length,
                data: res.sort(function (a, b) {
                  return Number(b.online) - Number(a.online);
                }),
                deviceOnline: res.reduce(function (acc, item) {
                  return item.online ? acc + 1 : acc;
                }, 0)
              }
            });
          case 4:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }))),
    data = _useRequest.data,
    loading = _useRequest.loading;
  /**\r
   * @description state for device\r
   */
  var _useState = (0,react.useState)(false),
    _useState2 = slicedToArray_default()(_useState, 2),
    openMonitor = _useState2[0],
    setOpenMonitor = _useState2[1];
  var _useState3 = (0,react.useState)(null),
    _useState4 = slicedToArray_default()(_useState3, 2),
    deviceId = _useState4[0],
    setDeviceId = _useState4[1];
  var openDeviceDetail = function openDeviceDetail(deviceId) {
    setDeviceId(deviceId);
    setOpenMonitor(true);
  };
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  return /*#__PURE__*/(0,jsx_runtime.jsx)(card/* default */.Z, {
    className: "border-0 shadow-sm",
    title: /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
      className: "px-2",
      children: loading ? /*#__PURE__*/(0,jsx_runtime.jsx)(skeleton/* default */.Z.Input, {}) : /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
        className: "flex items-center gap-2",
        children: [/*#__PURE__*/(0,jsx_runtime.jsxs)("span", {
          className: "text-lg font-medium",
          children: [/*#__PURE__*/(0,jsx_runtime.jsx)("span", {
            className: "text-green-500",
            children: data === null || data === void 0 ? void 0 : data.deviceOnline
          }), "/", data === null || data === void 0 ? void 0 : data.total]
        }), /*#__PURE__*/(0,jsx_runtime.jsx)("span", {
          className: "text-gray-600",
          children: formatMessage({
            id: 'new-dashboard.active-iot-devices'
          })
        })]
      })
    }),
    children: /*#__PURE__*/(0,jsx_runtime.jsxs)(spin/* default */.Z, {
      spinning: loading,
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        className: "flex gap-4 overflow-x-auto scrollbar-thin scrollbar-thumb-gray-200 scrollbar-track-transparent py-2 px-2",
        children: data === null || data === void 0 || (_data$data = data.data) === null || _data$data === void 0 ? void 0 : _data$data.map(function (item) {
          return /*#__PURE__*/(0,jsx_runtime.jsx)(DeviceItem, {
            onOpenMonitor: openDeviceDetail,
            data: item
          }, item.name);
        })
      }), openMonitor && deviceId && /*#__PURE__*/(0,jsx_runtime.jsx)(MonitorDevice["default"], {
        open: openMonitor,
        deviceId: deviceId,
        onOpenChange: setOpenMonitor
      })]
    })
  });
};
/* harmony default export */ var components_Devices = (Devices);
;// CONCATENATED MODULE: ./src/pages/DashboardNew/components/Crop.tsx














var CropItem = function CropItem(_ref) {
  var data = _ref.data;
  var totalDate = (0,react.useMemo)(function () {
    return (0,utils_date/* dayjsUtil */.PF)(data.end_date).diff(data.start_date, 'd');
  }, [data.start_date, data.end_date]);
  var daysLeft = (0,react.useMemo)(function () {
    return (0,utils_date/* dayjsUtil */.PF)(data.end_date).diff((0,utils_date/* dayjsUtil */.PF)(), 'd');
  }, [data.end_date]);
  var absDayLeft = Math.abs(daysLeft);
  var isDone = (0,react.useMemo)(function () {
    return data.status === 'Done';
  }, [data.status]);
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  var _useGetCropStage = useGetCropStage({
      cropId: data.name
    }),
    currentState = _useGetCropStage.data,
    loading = _useGetCropStage.loading;
  return /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.Link, {
    to: "/farming-management/seasonal-management/detail/".concat(data.name),
    children: /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
      className: "border bg-white p-3 flex gap-3 items-start hover:border-[#9DDBB1] hover:shadow-sm transition-all duration-300",
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        className: "flex-none",
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_avatar/* default */.C, {
          shape: "square",
          size: 44,
          src: data.avatar ? (0,file/* genDownloadUrl */.h)(data.avatar) : img/* DEFAULT_FALLBACK_IMG */.W,
          className: "!bg-gray-50"
        })
      }), /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
        className: "flex-1 min-w-0",
        children: [/*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
          className: "flex items-center gap-2 mb-1",
          children: [' ', /*#__PURE__*/(0,jsx_runtime.jsx)(Tag, {
            isOffline: isDone,
            children: data.status === 'In progress' ? formatMessage({
              id: 'new-dashboard.ongoing'
            }) : formatMessage({
              id: 'new-dashboard.completed'
            })
          })]
        }), /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
          className: "flex flex-col gap-1",
          children: [' ', /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
            className: "font-medium text-base truncate",
            children: data.label
          }), loading ? /*#__PURE__*/(0,jsx_runtime.jsx)(skeleton/* default */.Z.Input, {
            size: "small",
            active: true
          }) : /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
            className: "text-sm text-gray-500 truncate",
            children: currentState !== null && currentState !== void 0 && currentState.label ? "".concat(formatMessage({
              id: 'new-dashboard.stage'
            }), ": ").concat(currentState.label) : '-'
          })]
        }), /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
          className: "flex items-center gap-3 mt-1",
          children: [' ', /*#__PURE__*/(0,jsx_runtime.jsx)(progress/* default */.Z, {
            percent: 100,
            strokeColor: ['#a1ffbc'],
            success: {
              percent: absDayLeft / totalDate * 100
            },
            className: "flex-1 [&_.ant-progress-outer]:!rounded-none [&_.ant-progress-bg]:!rounded-none [&_.ant-progress-success-bg]:!rounded-none"
          }), /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
            className: "flex-none whitespace-nowrap",
            children: [/*#__PURE__*/(0,jsx_runtime.jsx)("span", {
              className: "font-medium",
              children: absDayLeft
            }), "/", totalDate, ' ', formatMessage({
              id: 'common.date'
            })]
          })]
        })]
      })]
    })
  });
};
var Crop = function Crop(_ref2) {
  var _data$data;
  var children = _ref2.children;
  var _useRequest = (0,_umi_production_exports.useRequest)( /*#__PURE__*/asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
      var _res$pagination;
      var res;
      return regeneratorRuntime_default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _context.next = 2;
            return (0,cropManager/* getCropManagementInfoList */.Gz)({
              page: 1,
              size: constanst/* DEFAULT_PAGE_SIZE_ALL */.YY
              // status: JSON.stringify(['In progress']),
            });
          case 2:
            res = _context.sent;
            return _context.abrupt("return", {
              data: {
                data: res.data,
                success: true,
                total: res === null || res === void 0 || (_res$pagination = res.pagination) === null || _res$pagination === void 0 ? void 0 : _res$pagination.totalElements
              }
            });
          case 4:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }))),
    data = _useRequest.data;
  var _useIntl2 = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl2.formatMessage;
  return /*#__PURE__*/(0,jsx_runtime.jsx)(card/* default */.Z, {
    className: "border-0 shadow-sm",
    title: formatMessage({
      id: 'new-dashboard.all-crop'
    }),
    extra: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.Link, {
      to: "/farming-management/seasonal-management?tab=all",
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
        type: "link",
        children: formatMessage({
          id: 'new-dashboard.see-all'
        })
      })
    }),
    children: /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
      className: "flex flex-col gap-2 overflow-y-auto max-h-[40rem] scrollbar-thin scrollbar-thumb-gray-200 scrollbar-track-transparent p-3",
      children: data === null || data === void 0 || (_data$data = data.data) === null || _data$data === void 0 ? void 0 : _data$data.map(function (item) {
        return /*#__PURE__*/(0,jsx_runtime.jsx)(CropItem, {
          data: item
        }, item.name);
      })
    })
  });
};
/* harmony default export */ var components_Crop = (Crop);
// EXTERNAL MODULE: ./src/services/farming-plan.ts
var farming_plan = __webpack_require__(74459);
// EXTERNAL MODULE: ./node_modules/antd/es/typography/index.js + 18 modules
var typography = __webpack_require__(25514);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/defineProperty.js
var defineProperty = __webpack_require__(9783);
var defineProperty_default = /*#__PURE__*/__webpack_require__.n(defineProperty);
;// CONCATENATED MODULE: ./src/pages/DashboardNew/hooks/useGetTimeName.ts



function useGetTimeName() {
  var _useState = (0,react.useState)(),
    _useState2 = slicedToArray_default()(_useState, 2),
    timeName = _useState2[0],
    setTimeNames = _useState2[1];
  var getTimeNames = function getTimeNames() {
    var times = [{
      time: [5, 10],
      greetings: defineProperty_default()(defineProperty_default()({}, "vi-VN", "Ch\xE0o bu\u1ED5i s\xE1ng"), "en-US", "Good morning")
    }, {
      time: [11, 13],
      greetings: defineProperty_default()(defineProperty_default()({}, "vi-VN", "Ch\xE0o bu\u1ED5i tr\u01B0a"), "en-US", "Good afternoon")
    }, {
      time: [14, 18],
      greetings: defineProperty_default()(defineProperty_default()({}, "vi-VN", "Ch\xE0o bu\u1ED5i chi\u1EC1u"), "en-US", "Good evening")
    }, {
      time: [19, 4],
      greetings: defineProperty_default()(defineProperty_default()({}, "vi-VN", "Ch\xE0o bu\u1ED5i t\u1ED1i"), "en-US", "Good night")
    }];
    var currentHour = new Date().getHours();
    var currentTimeName = times.find(function (time) {
      var _time$time = slicedToArray_default()(time.time, 2),
        start = _time$time[0],
        end = _time$time[1];
      if (start <= end) {
        return currentHour >= start && currentHour <= end;
      } else {
        return currentHour >= start || currentHour <= end;
      }
    });
    if (currentTimeName) {
      setTimeNames(currentTimeName);
    } else {
      console.error("No time name found for the current hour.");
    }
  };
  (0,react.useEffect)(function () {
    getTimeNames(); // Set initial time names

    var interval = setInterval(getTimeNames, 10 * 60 * 1000); // Update every 10 minutes

    return function () {
      return clearInterval(interval);
    };
  }, []);
  return {
    timeName: timeName
  };
}
;// CONCATENATED MODULE: ./src/pages/DashboardNew/components/Header/Greetings.tsx










var Greetings = function Greetings(_ref) {
  var _timeName$greetings, _initialState$current;
  var children = _ref.children;
  var _useGetTimeName = useGetTimeName(),
    timeName = _useGetTimeName.timeName;
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    locale = _useIntl.locale;
  var _useModel = (0,_umi_production_exports.useModel)('@@initialState'),
    initialState = _useModel.initialState;
  var greeting = timeName === null || timeName === void 0 || (_timeName$greetings = timeName.greetings) === null || _timeName$greetings === void 0 ? void 0 : _timeName$greetings[locale];
  var _useRequest = (0,_umi_production_exports.useRequest)( /*#__PURE__*/asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
      var today, filters, res;
      return regeneratorRuntime_default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            today = (0,utils_date/* dayjsUtil */.PF)().format('YYYY-MM-DD'); // const startDate = moment().startOf('date').toISOString();
            // const endDate = moment().endOf('date').toISOString();
            filters = [[constanst/* DOCTYPE_ERP */.lH.iotFarmingPlanTask, 'end_date', '>=', today], [constanst/* DOCTYPE_ERP */.lH.iotFarmingPlanTask, 'start_date', '<=', today]];
            _context.next = 4;
            return (0,farming_plan/* getTaskManagerList */.UM)({
              page: 1,
              size: 1,
              filters: filters,
              or_filters: []
            });
          case 4:
            res = _context.sent;
            return _context.abrupt("return", {
              data: {
                data: res.data,
                total: res.pagination.totalElements
              }
            });
          case 6:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }))),
    data = _useRequest.data,
    loading = _useRequest.loading;
  var _useIntl2 = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl2.formatMessage;
  return /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
    children: [/*#__PURE__*/(0,jsx_runtime.jsxs)(typography/* default */.Z.Title, {
      level: 3,
      className: "!mb-2 ",
      children: [greeting, ", ", initialState === null || initialState === void 0 || (_initialState$current = initialState.currentUser) === null || _initialState$current === void 0 ? void 0 : _initialState$current.full_name, "!"]
    }), data !== null && data !== void 0 && data.total ? /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.Link, {
      to: "/farming-management/workflow-management",
      children: /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        className: "text-gray-400 hover:underline",
        children: formatMessage({
          id: 'new-dashboard.notice-task'
        }, {
          count: data.total
        })
      })
    }) : null]
  });
};
/* harmony default export */ var Header_Greetings = (Greetings);
// EXTERNAL MODULE: ./src/services/customerUser.ts
var customerUser = __webpack_require__(40063);
// EXTERNAL MODULE: ./src/services/projects.ts
var projects = __webpack_require__(78263);
// EXTERNAL MODULE: ./src/services/sharing-device.ts
var sharing_device = __webpack_require__(65155);
// EXTERNAL MODULE: ./src/services/stock/dashboard.ts
var dashboard = __webpack_require__(98041);
// EXTERNAL MODULE: ./src/utils/format.ts
var format = __webpack_require__(5251);
;// CONCATENATED MODULE: ./src/pages/DashboardNew/components/Header/Statistics.tsx

















var Item = function Item(_ref) {
  var avatar = _ref.avatar,
    title = _ref.title,
    description = _ref.description,
    link = _ref.link,
    isLoading = _ref.isLoading;
  return /*#__PURE__*/_jsx(Link, {
    to: link,
    className: "flex",
    children: /*#__PURE__*/_jsxs("div", {
      className: "border rounded-xl p-2 flex gap-3 min-w-48 items-end hover:shadow cursor-pointer",
      children: [/*#__PURE__*/_jsx("div", {
        className: "flex-none",
        children: /*#__PURE__*/_jsx(Avatar, {
          shape: "square",
          size: 55,
          src: avatar
        })
      }), /*#__PURE__*/_jsxs("div", {
        children: [/*#__PURE__*/_jsx("div", {
          className: "text-base font-semibold mb-1",
          children: title
        }), /*#__PURE__*/_jsx("div", {
          className: "text-slate-400",
          children: isLoading ? /*#__PURE__*/_jsx(Skeleton.Input, {
            size: "small"
          }) : description
        })]
      })]
    })
  });
};
var Statistics = function Statistics(_ref2) {
  var children = _ref2.children;
  var _useRequest = (0,_umi_production_exports.useRequest)( /*#__PURE__*/asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
      var res;
      return regeneratorRuntime_default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _context.next = 2;
            return (0,devices/* deviceInProjectList */.pL)({
              filters: [],
              page: 1,
              size: constanst/* DEFAULT_PAGE_SIZE_ALL */.YY,
              fields: ['*'],
              order_by: 'online'
              // project_id: projectId,
            });
          case 2:
            res = _context.sent;
            return _context.abrupt("return", {
              data: {
                total: res.length,
                deviceOnline: res.reduce(function (acc, item) {
                  return item.online ? acc + 1 : acc;
                }, 0)
              }
            });
          case 4:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }))),
    dataDevice = _useRequest.data,
    deviceLoading = _useRequest.loading;
  var _useRequest2 = (0,_umi_production_exports.useRequest)( /*#__PURE__*/asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2() {
      var _res$pagination;
      var res;
      return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            _context2.next = 2;
            return (0,cropManager/* getCropManagementInfoList */.Gz)({
              page: 1,
              size: 1,
              status: JSON.stringify(['In progress'])
            });
          case 2:
            res = _context2.sent;
            return _context2.abrupt("return", {
              data: {
                data: res.data,
                success: true,
                total: res === null || res === void 0 || (_res$pagination = res.pagination) === null || _res$pagination === void 0 ? void 0 : _res$pagination.totalElements
              }
            });
          case 4:
          case "end":
            return _context2.stop();
        }
      }, _callee2);
    }))),
    dataCrop = _useRequest2.data,
    dataCropLoading = _useRequest2.loading;
  var _useRequest3 = (0,_umi_production_exports.useRequest)( /*#__PURE__*/asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee3() {
      var res;
      return regeneratorRuntime_default()().wrap(function _callee3$(_context3) {
        while (1) switch (_context3.prev = _context3.next) {
          case 0:
            _context3.next = 2;
            return (0,dashboard/* getTotalWarehouseQty */.Bg)({
              start_date: (0,utils_date/* dayjsUtil */.PF)().startOf('years').format('YYYY-MM-DD'),
              end_date: (0,utils_date/* dayjsUtil */.PF)().format('YYYY-MM-DD'),
              warehouse: undefined
            });
          case 2:
            res = _context3.sent;
            return _context3.abrupt("return", {
              data: res.result
            });
          case 4:
          case "end":
            return _context3.stop();
        }
      }, _callee3);
    }))),
    totalWarehouseQty = _useRequest3.data,
    warehouseLoading = _useRequest3.loading;
  var _useRequest4 = (0,_umi_production_exports.useRequest)( /*#__PURE__*/asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee4() {
      var res;
      return regeneratorRuntime_default()().wrap(function _callee4$(_context4) {
        while (1) switch (_context4.prev = _context4.next) {
          case 0:
            _context4.next = 2;
            return (0,customerUser/* getCustomerUserList */.J9)({
              page: 1,
              size: 1
            });
          case 2:
            res = _context4.sent;
            return _context4.abrupt("return", {
              data: {
                data: res.data,
                total: res.pagination.totalElements
              }
            });
          case 4:
          case "end":
            return _context4.stop();
        }
      }, _callee4);
    }))),
    myUser = _useRequest4.data,
    userLoading = _useRequest4.loading;
  var _useRequest5 = (0,_umi_production_exports.useRequest)( /*#__PURE__*/asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee5() {
      var res;
      return regeneratorRuntime_default()().wrap(function _callee5$(_context5) {
        while (1) switch (_context5.prev = _context5.next) {
          case 0:
            _context5.next = 2;
            return (0,projects/* projectList */.d9)({
              page: 1,
              size: 1,
              fields: ['name']
            });
          case 2:
            res = _context5.sent;
            return _context5.abrupt("return", {
              data: {
                data: res.data,
                total: res.pagination.totalElements
              }
            });
          case 4:
          case "end":
            return _context5.stop();
        }
      }, _callee5);
    }))),
    projectData = _useRequest5.data,
    projectLoading = _useRequest5.loading;
  var _useRequest6 = (0,_umi_production_exports.useRequest)( /*#__PURE__*/asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee6() {
      var res;
      return regeneratorRuntime_default()().wrap(function _callee6$(_context6) {
        while (1) switch (_context6.prev = _context6.next) {
          case 0:
            _context6.next = 2;
            return (0,sharing_device/* deviceSharedInProjectList */.kE)({
              filters: [],
              page: 1,
              size: constanst/* DEFAULT_PAGE_SIZE_ALL */.YY,
              fields: ['*'],
              order_by: 'online'
              // project_id: projectId,
            });
          case 2:
            res = _context6.sent;
            return _context6.abrupt("return", {
              data: {
                total: res.length,
                deviceOnline: res.reduce(function (acc, item) {
                  return item.online ? acc + 1 : acc;
                }, 0)
              }
            });
          case 4:
          case "end":
            return _context6.stop();
        }
      }, _callee6);
    }))),
    dataDeviceShare = _useRequest6.data,
    deviceShareLoading = _useRequest6.loading;
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  var cardRender = [{
    key: '0',
    avatar: '/images/new-dashboard/iot.png',
    title: formatMessage({
      id: 'new-dashboard.iot-device'
    }),
    description: "".concat(dataDevice === null || dataDevice === void 0 ? void 0 : dataDevice.total, " ").concat(formatMessage({
      id: 'new-dashboard.iot-device'
    })),
    link: '/iot-device-management',
    isLoading: deviceLoading
  }, {
    key: '1',
    avatar: '/images/new-dashboard/iot.png',
    title: formatMessage({
      id: 'common.shared_devices'
    }),
    description: "".concat(dataDeviceShare === null || dataDeviceShare === void 0 ? void 0 : dataDeviceShare.total, " ").concat(formatMessage({
      id: 'new-dashboard.iot-device'
    })),
    link: '/iot-device-management?tab=thiet+bi+duoc+chia+se',
    isLoading: deviceShareLoading
  }, {
    key: '3',
    avatar: '/images/new-dashboard/crop.png',
    title: formatMessage({
      id: 'common.crop'
    }),
    description: "".concat(dataCrop === null || dataCrop === void 0 ? void 0 : dataCrop.total, " ").concat(formatMessage({
      id: 'new-dashboard.crop-ongoing'
    })),
    link: '/farming-management/seasonal-management',
    isLoading: dataCropLoading
  }, {
    key: '4',
    avatar: '/images/new-dashboard/inventoryy.png',
    title: formatMessage({
      id: 'common.inventory'
    }),
    description: "".concat(formatMessage({
      id: 'new-dashboard.inventory-value'
    }), " ").concat((0,format/* formatMoney */.lb)(totalWarehouseQty === null || totalWarehouseQty === void 0 ? void 0 : totalWarehouseQty.total_price), " VN\\u0110"),
    link: '/warehouse-management-v3/inventory',
    isLoading: warehouseLoading
  }, {
    key: '5',
    avatar: '/images/new-dashboard/employee.png',
    title: formatMessage({
      id: 'new-dashboard.employee'
    }),
    description: "".concat(myUser === null || myUser === void 0 ? void 0 : myUser.total, " ").concat(formatMessage({
      id: 'new-dashboard.employee-working'
    })),
    link: '/employee-management/employee-list',
    isLoading: userLoading
  }, {
    key: '6',
    avatar: '/images/new-dashboard/project.png',
    title: formatMessage({
      id: 'new-dashboard.project'
    }),
    description: "".concat(projectData === null || projectData === void 0 ? void 0 : projectData.total, " ").concat(formatMessage({
      id: 'new-dashboard.project-ongoing'
    })),
    link: '/project-management',
    isLoading: projectLoading
  }];
  var ItemCustom = function ItemCustom(_ref9) {
    var avatar = _ref9.avatar,
      title = _ref9.title,
      description = _ref9.description,
      link = _ref9.link,
      isLoading = _ref9.isLoading;
    return /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.Link, {
      to: link,
      className: "flex-none",
      children: /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
        className: "border hover:border-[#9DDBB1] hover:shadow-sm transition-colors duration-300 p-4 flex gap-4 min-w-[12rem] h-24 items-center cursor-pointer",
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)("div", {
          className: "flex-none",
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_avatar/* default */.C, {
            shape: "square",
            size: 44,
            src: avatar,
            className: "!bg-gray-50"
          })
        }), /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
          className: "flex flex-col justify-center min-w-0",
          children: [/*#__PURE__*/(0,jsx_runtime.jsx)("div", {
            className: "text-sm text-gray-500 mb-1",
            children: title
          }), /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
            className: "text-base font-medium text-gray-900 truncate",
            children: isLoading ? /*#__PURE__*/(0,jsx_runtime.jsx)(skeleton/* default */.Z.Input, {
              size: "small"
            }) : description
          })]
        })]
      })
    });
  };
  return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
    className: "flex items-stretch gap-4 flex-nowrap overflow-auto max-w-full scrollbar-thin scrollbar-thumb-gray-200 scrollbar-track-transparent pb-1",
    children: cardRender.map(function (item) {
      return /*#__PURE__*/(0,react.createElement)(ItemCustom, objectSpread2_default()(objectSpread2_default()({}, item), {}, {
        key: item.key
      }));
    })
  });
};
/* harmony default export */ var Header_Statistics = (Statistics);
// EXTERNAL MODULE: ./src/utils/string.ts
var string = __webpack_require__(7369);
// EXTERNAL MODULE: ./src/services/utils.ts
var utils = __webpack_require__(467);
;// CONCATENATED MODULE: ./src/services/weather.ts




function getCurrentWeather(_x) {
  return _getCurrentWeather.apply(this, arguments);
}
function _getCurrentWeather() {
  _getCurrentWeather = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(params) {
    var res;
    return regeneratorRuntime_default()().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.next = 2;
          return (0,_umi_production_exports.request)((0,utils/* generateAPIPath */.rH)('api/v2/weather'), {
            method: 'GET',
            params: params
          });
        case 2:
          res = _context.sent;
          return _context.abrupt("return", res.result);
        case 4:
        case "end":
          return _context.stop();
      }
    }, _callee);
  }));
  return _getCurrentWeather.apply(this, arguments);
}
;// CONCATENATED MODULE: ./src/pages/DashboardNew/hooks/useWeather.ts






// Type for use in component state

function useWeather(cityQuery) {
  var _useState = (0,react.useState)({
      lat: '',
      lot: ''
    }),
    _useState2 = slicedToArray_default()(_useState, 2),
    location = _useState2[0],
    setLocation = _useState2[1];
  var _useState3 = (0,react.useState)(null),
    _useState4 = slicedToArray_default()(_useState3, 2),
    weather = _useState4[0],
    setWeather = _useState4[1];
  (0,react.useEffect)(function () {
    navigator.geolocation.getCurrentPosition(function (position) {
      setLocation({
        lat: position.coords.latitude.toString(),
        lot: position.coords.longitude.toString()
      });
    }, function (error) {
      console.error('Error obtaining location', error);
    });
  }, []);
  (0,react.useEffect)(function () {
    var fetchWeather = /*#__PURE__*/function () {
      var _ref = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
        var response, current, date, newWeather;
        return regeneratorRuntime_default()().wrap(function _callee$(_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              _context.prev = 0;
              if (!(!location.lat || !location.lot)) {
                _context.next = 3;
                break;
              }
              return _context.abrupt("return");
            case 3:
              _context.next = 5;
              return getCurrentWeather({
                q: "".concat(location.lat, ",").concat(location.lot)
              });
            case 5:
              response = _context.sent;
              current = response.data.current;
              date = new Date().toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: '2-digit',
                day: '2-digit'
              });
              newWeather = {
                date: date,
                temperature: current.temp_c,
                weather: current.condition.text,
                icon: current.condition.icon,
                windSpeed: current.wind_kph,
                precipitation: current.precip_mm,
                humidity: current.humidity,
                uvIndex: current.uv
              };
              setWeather(newWeather);
              _context.next = 15;
              break;
            case 12:
              _context.prev = 12;
              _context.t0 = _context["catch"](0);
              console.error('Failed to fetch weather data:', _context.t0);
            case 15:
            case "end":
              return _context.stop();
          }
        }, _callee, null, [[0, 12]]);
      }));
      return function fetchWeather() {
        return _ref.apply(this, arguments);
      };
    }();
    fetchWeather();
  }, [location]);
  return {
    weather: weather
  };
}
;// CONCATENATED MODULE: ./src/pages/DashboardNew/components/Header/Weather.tsx








var Weather = function Weather(_ref) {
  var children = _ref.children;
  var date = (0,react.useMemo)(function () {
    var currentDate = (0,utils_date/* dayjsUtil */.PF)();
    var day = currentDate.format('dddd, DD/MM/YYYY');
    var dayLunar = (0,utils_date/* convertToLunarCalendar */.Pc)(currentDate.toISOString());
    return "".concat(day, " (").concat(dayLunar === null || dayLunar === void 0 ? void 0 : dayLunar.day, "/").concat(dayLunar === null || dayLunar === void 0 ? void 0 : dayLunar.month, " AL)");
  }, []);
  var _useWeather = useWeather('10.7720803%2C106.6553215'),
    weather = _useWeather.weather;
  console.log('weather', weather);
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  return /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
    className: "flex gap-2 text-right",
    children: [/*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
      className: "",
      children: [/*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
        className: "text-xl font-semibold",
        children: [weather === null || weather === void 0 ? void 0 : weather.temperature, "\\xB0C"]
      }), /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        children: (0,string/* toFirstUpperCase */.W)(date)
      }), /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
        className: "flex flex-wrap gap-4 items-center justify-between",
        children: [/*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
          children: [formatMessage({
            id: 'common.windSpeed'
          }), ": ", weather === null || weather === void 0 ? void 0 : weather.windSpeed, " km/h"]
        }), /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
          children: [formatMessage({
            id: 'common.precipitation'
          }), ": ", weather === null || weather === void 0 ? void 0 : weather.precipitation, " mm"]
        }), /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
          children: [formatMessage({
            id: 'common.humidity'
          }), ": ", weather === null || weather === void 0 ? void 0 : weather.humidity, "%"]
        }), /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
          children: [formatMessage({
            id: 'common.uvIndex'
          }), ": ", weather === null || weather === void 0 ? void 0 : weather.uvIndex]
        })]
      })]
    }), /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_avatar/* default */.C, {
        src: weather === null || weather === void 0 ? void 0 : weather.icon,
        size: 40,
        shape: "square"
      })
    })]
  });
};
/* harmony default export */ var Header_Weather = (Weather);
;// CONCATENATED MODULE: ./src/pages/DashboardNew/components/Header/index.tsx





var Header = function Header(_ref) {
  var children = _ref.children;
  return /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
    children: [/*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
      className: "flex items-center justify-between sm:mb-2 lg:mb-6 xl:mb-6 2xl:mb-6",
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)(Header_Greetings, {}), /*#__PURE__*/(0,jsx_runtime.jsx)(Header_Weather, {})]
    }), /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(Header_Statistics, {})
    })]
  });
};
/* harmony default export */ var components_Header = (Header);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/createForOfIteratorHelper.js
var createForOfIteratorHelper = __webpack_require__(64599);
var createForOfIteratorHelper_default = /*#__PURE__*/__webpack_require__.n(createForOfIteratorHelper);
// EXTERNAL MODULE: ./src/components/HighChartComponentCommon/index.tsx + 1 modules
var HighChartComponentCommon = __webpack_require__(46229);
// EXTERNAL MODULE: ./node_modules/ahooks/es/useRequest/index.js + 23 modules
var useRequest = __webpack_require__(79718);
;// CONCATENATED MODULE: ./src/pages/DashboardNew/components/Warehouse.tsx














var OptionItem = function OptionItem(_ref) {
  var icon = _ref.icon,
    title = _ref.title,
    value = _ref.value,
    textColor = _ref.textColor;
  return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
    className: "p-2",
    children: /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
      className: "flex gap-2 items-center",
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_avatar/* default */.C, {
          size: 'large',
          shape: "square",
          src: icon
        })
      }), /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)("div", {
          className: "text-base font-semibold",
          children: title
        }), /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
          className: "text-slate-400 font-semibold",
          style: {
            color: textColor
          },
          children: [value, " VN\\u0110"]
        })]
      })]
    })
  });
};
var startTime = (0,utils_date/* dayjsUtil */.PF)().add(-1, 'year').format('YYYY-MM-DD');
var endTime = (0,utils_date/* dayjsUtil */.PF)().format('YYYY-MM-DD');
var Warehouse = function Warehouse(_ref2) {
  var _data$data3, _data$data4, _data$data5;
  var children = _ref2.children;
  var _useState = (0,react.useState)('month'),
    _useState2 = slicedToArray_default()(_useState, 2),
    groupBy = _useState2[0],
    setGroupBy = _useState2[1];
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  var _useRequest = (0,useRequest/* default */.Z)( /*#__PURE__*/asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
      var res, formatTimestamps, formattedData, total, totalExport, totalImport;
      return regeneratorRuntime_default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            formatTimestamps = function _formatTimestamps(timestamps) {
              var formattedTimestamps = [];
              var _iterator = createForOfIteratorHelper_default()(timestamps),
                _step;
              try {
                for (_iterator.s(); !(_step = _iterator.n()).done;) {
                  var timestamp = _step.value;
                  // Parse the timestamp string
                  var dt = (0,utils_date/* dayjsUtil */.PF)(timestamp);

                  // Format based on groupBy attribute
                  var formattedString = void 0;
                  switch (groupBy) {
                    case 'week':
                      // Get the week number (Sunday as first day of the week)

                      formattedString = "".concat(dt.startOf('week'), " - ").concat(dt.endOf('week'));
                      break;
                    case 'month':
                      formattedString = dt.format('MMMM YYYY');
                      break;
                    case 'year':
                      formattedString = dt.format('YYYY');
                      break;
                    default:
                      formattedString = timestamp;
                  }
                  formattedTimestamps.push(formattedString);
                }
              } catch (err) {
                _iterator.e(err);
              } finally {
                _iterator.f();
              }
              return formattedTimestamps;
            };
            _context.next = 3;
            return (0,dashboard/* getImportExportWarehouse */.w5)({
              warehouse: undefined,
              start_date: startTime,
              end_date: endTime,
              group_by: groupBy
            });
          case 3:
            res = _context.sent;
            formattedData = {
              labels: formatTimestamps(res.result.at(0).map(function (item) {
                return item[groupBy];
              })),
              datasets: [{
                type: 'bar',
                label: 'Nh\u1EADp',
                data: res.result.at(0).map(function (item) {
                  return item.total_price;
                }),
                backgroundColor: '#4793AF'
              }, {
                type: 'bar',
                label: 'Xu\u1EA5t',
                data: res.result.at(1).map(function (item) {
                  return item.total_price;
                }),
                backgroundColor: '#DD5746'
              }]
            };
            _context.next = 7;
            return (0,dashboard/* getTotalImportExportWarehouseQty */.Ej)({
              start_date: startTime,
              end_date: endTime
            });
          case 7:
            total = _context.sent;
            totalExport = total.result.deliveryNoteTotal.total_qty;
            totalImport = total.result.purchaseReceiptTotal.total_price;
            return _context.abrupt("return", {
              data: {
                chart: formattedData,
                totalExport: totalExport,
                totalImport: totalImport
              }
            });
          case 11:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }))),
    data = _useRequest.data;
  var maxColumn = (0,react.useMemo)(function () {
    var _data$data, _data$data2;
    var a = (data === null || data === void 0 || (_data$data = data.data) === null || _data$data === void 0 || (_data$data = _data$data.chart.datasets) === null || _data$data === void 0 || (_data$data = _data$data[0]) === null || _data$data === void 0 || (_data$data = _data$data.data) === null || _data$data === void 0 ? void 0 : _data$data.length) || 1;
    var b = (data === null || data === void 0 || (_data$data2 = data.data) === null || _data$data2 === void 0 || (_data$data2 = _data$data2.chart.datasets) === null || _data$data2 === void 0 || (_data$data2 = _data$data2[1]) === null || _data$data2 === void 0 || (_data$data2 = _data$data2.data) === null || _data$data2 === void 0 ? void 0 : _data$data2.length) || 1;
    return Math.max(a, b);
  }, [data === null || data === void 0 || (_data$data3 = data.data) === null || _data$data3 === void 0 ? void 0 : _data$data3.chart.datasets]);
  return /*#__PURE__*/(0,jsx_runtime.jsxs)(card/* default */.Z, {
    bordered: false,
    title: formatMessage({
      id: 'new-dashboard.import-and-export-warehouse'
    }),
    extra: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.Link, {
      to: "/warehouse-management-v3/inventory",
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
        type: "link",
        children: formatMessage({
          id: 'new-dashboard.see-all'
        })
      })
    }),
    children: [/*#__PURE__*/(0,jsx_runtime.jsx)(HighChartComponentCommon/* default */.Z, {
      chartProps: {
        chart: {
          type: 'column',
          scrollablePlotArea: {
            minWidth: maxColumn * 70,
            // nr of interval data x (40 + 30) where 40 are column width pixels and
            // 30 is additional distancing between columns;
            // Increase spacing pixels if needed
            scrollPositionX: 1
          }
        },
        exporting: {
          enabled: false
        },
        title: {
          // text: 'Corn vs wheat estimated production for 2020',
          text: ''
        },
        navigator: {
          enabled: false
        },
        subtitle: {
          // text:
          //   'Source: <a target="_blank" ' +
          //   'href="https://www.indexmundi.com/agriculture/?commodity=corn">indexmundi</a>',
          // align: 'left',
        },
        xAxis: {
          type: 'category',
          categories: data === null || data === void 0 ? void 0 : data.data.chart.labels,
          crosshair: true,
          accessibility: {
            description: 'Time'
          }
        },
        yAxis: {
          min: 0,
          title: {
            text: 'VN\u0110'
          }
        },
        tooltip: {
          valueSuffix: ' (VN\u0110)'
        },
        plotOptions: {
          column: {
            pointPadding: 0.2,
            borderWidth: 0,
            pointRange: 20,
            borderRadius: 4,
            dataLabels: {
              enabled: false
            }
          }
        },
        series: [{
          type: 'column',
          name: formatMessage({
            id: 'new-dashboard.export-warehouse'
          }),
          data: data === null || data === void 0 || (_data$data4 = data.data) === null || _data$data4 === void 0 || (_data$data4 = _data$data4.chart) === null || _data$data4 === void 0 || (_data$data4 = _data$data4.datasets) === null || _data$data4 === void 0 || (_data$data4 = _data$data4[1]) === null || _data$data4 === void 0 ? void 0 : _data$data4.data,
          color: '#ffcf00'
        }, {
          type: 'column',
          name: formatMessage({
            id: 'new-dashboard.import-warehouse'
          }),
          data: data === null || data === void 0 || (_data$data5 = data.data) === null || _data$data5 === void 0 || (_data$data5 = _data$data5.chart.datasets) === null || _data$data5 === void 0 || (_data$data5 = _data$data5[0]) === null || _data$data5 === void 0 ? void 0 : _data$data5.data,
          color: '#47b494'
        }]
      }
    }), /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
      className: "mt-2 flex items-center gap-2 justify-between",
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)(OptionItem, {
        textColor: '#ffc66b',
        icon: "/images/new-dashboard/export.png",
        title: formatMessage({
          id: 'new-dashboard.export-warehouse'
        }),
        value: (0,format/* formatMoney */.lb)(data === null || data === void 0 ? void 0 : data.data.totalExport)
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(OptionItem, {
        textColor: "#27ae60",
        icon: "/images/new-dashboard/import.png",
        title: formatMessage({
          id: 'new-dashboard.import-warehouse'
        }),
        value: (0,format/* formatMoney */.lb)(data === null || data === void 0 ? void 0 : data.data.totalImport)
      })]
    })]
  });
};
/* harmony default export */ var components_Warehouse = (Warehouse);
;// CONCATENATED MODULE: ./src/pages/DashboardNew/index.tsx









var Index = function Index(_ref) {
  var children = _ref.children;
  var fixClass = 'px-[20px] py-[10px]';
  return /*#__PURE__*/(0,jsx_runtime.jsx)(config_provider/* default */.ZP, {
    children: /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
      className: "-mx-[40px] -my-[10px]",
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        className: classnames_default()(fixClass),
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(components_Header, {})
      }), /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        className: classnames_default()(fixClass, 'bg-[#f3f5f9]'),
        children: /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
          gutter: [8, 8],
          children: [/*#__PURE__*/(0,jsx_runtime.jsxs)(col/* default */.Z, {
            span: 24,
            lg: 9,
            children: [/*#__PURE__*/(0,jsx_runtime.jsx)(components_Alarm, {}), /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
              className: "mt-2",
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(components_Warehouse, {})
            })]
          }), /*#__PURE__*/(0,jsx_runtime.jsxs)(col/* default */.Z, {
            span: 24,
            lg: 15,
            children: [/*#__PURE__*/(0,jsx_runtime.jsx)(components_Devices, {}), /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
              className: "mt-2",
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(components_Crop, {})
            })]
          })]
        })
      })]
    })
  });
};
/* harmony default export */ var DashboardNew = (Index);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///13334
`)},77890:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Ak: function() { return /* binding */ updateCrop; },
/* harmony export */   Gz: function() { return /* binding */ getCropManagementInfoList; },
/* harmony export */   Ir: function() { return /* binding */ deleteCropNote; },
/* harmony export */   JB: function() { return /* binding */ addParticipantInCrop; },
/* harmony export */   LY: function() { return /* binding */ getTemplateCropList; },
/* harmony export */   No: function() { return /* binding */ getParticipantsInCrop; },
/* harmony export */   TQ: function() { return /* binding */ getCropList; },
/* harmony export */   Tq: function() { return /* binding */ deleteParticipantsInCrop; },
/* harmony export */   WP: function() { return /* binding */ getStatisticNoteList; },
/* harmony export */   bx: function() { return /* binding */ updateCropNote; },
/* harmony export */   mP: function() { return /* binding */ createCrop; },
/* harmony export */   rC: function() { return /* binding */ createCropNote; },
/* harmony export */   vW: function() { return /* binding */ getCurrentStateOfCrop; },
/* harmony export */   xu: function() { return /* binding */ getCropNoteList; }
/* harmony export */ });
/* unused harmony exports updateParticipantsInCrop, getStatisticPestList */
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(7837);
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(467);




var getCropList = /*#__PURE__*/function () {
  var _ref = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/crop'), {
            method: 'GET',
            params: (0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .getParamsReqList */ .vj)(params)
          });
        case 2:
          res = _context.sent;
          return _context.abrupt("return", res.result);
        case 4:
        case "end":
          return _context.stop();
      }
    }, _callee);
  }));
  return function getCropList(_x) {
    return _ref.apply(this, arguments);
  };
}();
var getTemplateCropList = /*#__PURE__*/function () {
  var _ref2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee2(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          _context2.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/crop/template'), {
            method: 'GET',
            params: (0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .getParamsReqList */ .vj)(params)
          });
        case 2:
          res = _context2.sent;
          return _context2.abrupt("return", res.result);
        case 4:
        case "end":
          return _context2.stop();
      }
    }, _callee2);
  }));
  return function getTemplateCropList(_x2) {
    return _ref2.apply(this, arguments);
  };
}();
var getCropManagementInfoList = /*#__PURE__*/function () {
  var _ref3 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee3(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee3$(_context3) {
      while (1) switch (_context3.prev = _context3.next) {
        case 0:
          _context3.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/crop-management-info'), {
            method: 'GET',
            params: params
          });
        case 2:
          res = _context3.sent;
          return _context3.abrupt("return", res.result);
        case 4:
        case "end":
          return _context3.stop();
      }
    }, _callee3);
  }));
  return function getCropManagementInfoList(_x3) {
    return _ref3.apply(this, arguments);
  };
}();
var getCurrentStateOfCrop = /*#__PURE__*/function () {
  var _ref4 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee4(cropId) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee4$(_context4) {
      while (1) switch (_context4.prev = _context4.next) {
        case 0:
          _context4.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/crop-current-state'), {
            method: 'GET',
            params: {
              crop_id: cropId
            }
          });
        case 2:
          res = _context4.sent;
          return _context4.abrupt("return", res.result);
        case 4:
        case "end":
          return _context4.stop();
      }
    }, _callee4);
  }));
  return function getCurrentStateOfCrop(_x4) {
    return _ref4.apply(this, arguments);
  };
}();
var createCrop = /*#__PURE__*/function () {
  var _ref5 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee5(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee5$(_context5) {
      while (1) switch (_context5.prev = _context5.next) {
        case 0:
          _context5.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/crop'), {
            method: 'POST',
            data: data
          });
        case 2:
          res = _context5.sent;
          return _context5.abrupt("return", res.result);
        case 4:
        case "end":
          return _context5.stop();
      }
    }, _callee5);
  }));
  return function createCrop(_x5) {
    return _ref5.apply(this, arguments);
  };
}();
var updateCrop = /*#__PURE__*/function () {
  var _ref6 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee6(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee6$(_context6) {
      while (1) switch (_context6.prev = _context6.next) {
        case 0:
          _context6.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/crop'), {
            method: 'PUT',
            data: data
          });
        case 2:
          res = _context6.sent;
          return _context6.abrupt("return", res.result);
        case 4:
        case "end":
          return _context6.stop();
      }
    }, _callee6);
  }));
  return function updateCrop(_x6) {
    return _ref6.apply(this, arguments);
  };
}();
var getCropNoteList = /*#__PURE__*/function () {
  var _ref7 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee7(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee7$(_context7) {
      while (1) switch (_context7.prev = _context7.next) {
        case 0:
          _context7.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/note'), {
            method: 'GET',
            params: (0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .getParamsReqList */ .vj)(params)
          });
        case 2:
          res = _context7.sent;
          return _context7.abrupt("return", res.result);
        case 4:
        case "end":
          return _context7.stop();
      }
    }, _callee7);
  }));
  return function getCropNoteList(_x7) {
    return _ref7.apply(this, arguments);
  };
}();
var createCropNote = /*#__PURE__*/function () {
  var _ref8 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee8(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee8$(_context8) {
      while (1) switch (_context8.prev = _context8.next) {
        case 0:
          _context8.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/note'), {
            method: 'POST',
            data: data
          });
        case 2:
          res = _context8.sent;
          return _context8.abrupt("return", res.result);
        case 4:
        case "end":
          return _context8.stop();
      }
    }, _callee8);
  }));
  return function createCropNote(_x8) {
    return _ref8.apply(this, arguments);
  };
}();
var updateCropNote = /*#__PURE__*/function () {
  var _ref9 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee9(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee9$(_context9) {
      while (1) switch (_context9.prev = _context9.next) {
        case 0:
          _context9.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/note'), {
            method: 'PUT',
            data: data
          });
        case 2:
          res = _context9.sent;
          return _context9.abrupt("return", res.result);
        case 4:
        case "end":
          return _context9.stop();
      }
    }, _callee9);
  }));
  return function updateCropNote(_x9) {
    return _ref9.apply(this, arguments);
  };
}();
var deleteCropNote = /*#__PURE__*/function () {
  var _ref11 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee10(_ref10) {
    var name, res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee10$(_context10) {
      while (1) switch (_context10.prev = _context10.next) {
        case 0:
          name = _ref10.name;
          _context10.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)("api/v2/cropManage/note?name=".concat(name)), {
            method: 'DELETE'
          });
        case 3:
          res = _context10.sent;
          return _context10.abrupt("return", res.result);
        case 5:
        case "end":
          return _context10.stop();
      }
    }, _callee10);
  }));
  return function deleteCropNote(_x10) {
    return _ref11.apply(this, arguments);
  };
}();
// Participants
var getParticipantsInCrop = /*#__PURE__*/function () {
  var _ref12 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee11(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee11$(_context11) {
      while (1) switch (_context11.prev = _context11.next) {
        case 0:
          _context11.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/iot_employee_in_crop'), {
            method: 'GET',
            params: (0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .getParamsReqList */ .vj)(params)
          });
        case 2:
          res = _context11.sent;
          return _context11.abrupt("return", res.result);
        case 4:
        case "end":
          return _context11.stop();
      }
    }, _callee11);
  }));
  return function getParticipantsInCrop(_x11) {
    return _ref12.apply(this, arguments);
  };
}();
var addParticipantInCrop = /*#__PURE__*/function () {
  var _ref13 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee12(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee12$(_context12) {
      while (1) switch (_context12.prev = _context12.next) {
        case 0:
          _context12.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/iot_employee_in_crop'), {
            method: 'POST',
            data: data
          });
        case 2:
          res = _context12.sent;
          return _context12.abrupt("return", res);
        case 4:
        case "end":
          return _context12.stop();
      }
    }, _callee12);
  }));
  return function addParticipantInCrop(_x12) {
    return _ref13.apply(this, arguments);
  };
}();
var updateParticipantsInCrop = /*#__PURE__*/(/* unused pure expression or super */ null && (function () {
  var _ref14 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee13(data) {
    var res;
    return _regeneratorRuntime().wrap(function _callee13$(_context13) {
      while (1) switch (_context13.prev = _context13.next) {
        case 0:
          _context13.next = 2;
          return request(generateAPIPath('api/v2/cropManage/iot_employee_in_crop'), {
            method: 'PUT',
            data: data
          });
        case 2:
          res = _context13.sent;
          return _context13.abrupt("return", res);
        case 4:
        case "end":
          return _context13.stop();
      }
    }, _callee13);
  }));
  return function updateParticipantsInCrop(_x13) {
    return _ref14.apply(this, arguments);
  };
}()));
var deleteParticipantsInCrop = /*#__PURE__*/function () {
  var _ref15 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee14(name) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee14$(_context14) {
      while (1) switch (_context14.prev = _context14.next) {
        case 0:
          _context14.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/iot_employee_in_crop'), {
            method: 'DELETE',
            params: {
              name: name
            }
          });
        case 2:
          res = _context14.sent;
          return _context14.abrupt("return", res);
        case 4:
        case "end":
          return _context14.stop();
      }
    }, _callee14);
  }));
  return function deleteParticipantsInCrop(_x14) {
    return _ref15.apply(this, arguments);
  };
}();
var getStatisticPestList = /*#__PURE__*/(/* unused pure expression or super */ null && (function () {
  var _ref16 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee15(params) {
    var res;
    return _regeneratorRuntime().wrap(function _callee15$(_context15) {
      while (1) switch (_context15.prev = _context15.next) {
        case 0:
          _context15.next = 2;
          return request(generateAPIPath("api/v2/cropManage/statisticPestList?crop_id=".concat(params === null || params === void 0 ? void 0 : params.cropId)), {
            method: 'GET',
            params: getParamsReqList(params)
          });
        case 2:
          res = _context15.sent;
          return _context15.abrupt("return", res.result);
        case 4:
        case "end":
          return _context15.stop();
      }
    }, _callee15);
  }));
  return function getStatisticPestList(_x15) {
    return _ref16.apply(this, arguments);
  };
}()));
var getStatisticNoteList = /*#__PURE__*/function () {
  var _ref17 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee16(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee16$(_context16) {
      while (1) switch (_context16.prev = _context16.next) {
        case 0:
          _context16.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)("api/v2/cropManage/statisticNoteList?crop_id=".concat(params === null || params === void 0 ? void 0 : params.cropId)), {
            method: 'GET',
            params: (0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .getParamsReqList */ .vj)(params)
          });
        case 2:
          res = _context16.sent;
          return _context16.abrupt("return", res.result);
        case 4:
        case "end":
          return _context16.stop();
      }
    }, _callee16);
  }));
  return function getStatisticNoteList(_x16) {
    return _ref17.apply(this, arguments);
  };
}();//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///77890
`)},40063:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   J9: function() { return /* binding */ getCustomerUserList; },
/* harmony export */   Lf: function() { return /* binding */ listDynamicRoleAllSection; },
/* harmony export */   cb: function() { return /* binding */ updateCustomerUser; },
/* harmony export */   f6: function() { return /* binding */ createDynamicRole; },
/* harmony export */   fh: function() { return /* binding */ updateDynamicRole; },
/* harmony export */   jt: function() { return /* binding */ customerUserListAll; },
/* harmony export */   rX: function() { return /* binding */ removeDynamicRole; },
/* harmony export */   w: function() { return /* binding */ getDynamicRole; },
/* harmony export */   y_: function() { return /* binding */ createCustomerUser; }
/* harmony export */ });
/* unused harmony exports IIotDynamicRole, getCustomerUserIndividualList, deleteCustomerUser, deleteCustomerUserCredential */
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_createClass_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(72004);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_createClass_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_createClass_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_classCallCheck_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(12444);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_classCallCheck_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_classCallCheck_js__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_defineProperty_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(9783);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_defineProperty_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_defineProperty_js__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(7837);
/* harmony import */ var _sscript__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(39750);
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(467);








var IIotDynamicRole = /*#__PURE__*/(/* unused pure expression or super */ null && (_createClass(function IIotDynamicRole() {
  _classCallCheck(this, IIotDynamicRole);
  _defineProperty(this, "name", void 0);
  _defineProperty(this, "label", void 0);
  // Data
  _defineProperty(this, "role", void 0);
  // Data
  _defineProperty(this, "iot_customer", void 0);
  // Link
  _defineProperty(this, "sections", void 0);
} // Data
)));
var createCustomerUser = /*#__PURE__*/function () {
  var _ref = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_5__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_7__/* .generateAPIPath */ .rH)('api/v2/register/customer-user-with-role'), {
            method: 'POST',
            data: data
          });
        case 2:
          res = _context.sent;
          return _context.abrupt("return", res);
        case 4:
        case "end":
          return _context.stop();
      }
    }, _callee);
  }));
  return function createCustomerUser(_x) {
    return _ref.apply(this, arguments);
  };
}();
var getCustomerUserList = /*#__PURE__*/function () {
  var _ref2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee2(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          _context2.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_5__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_7__/* .generateAPIPath */ .rH)('api/v2/customerUser/user'), {
            params: (0,_utils__WEBPACK_IMPORTED_MODULE_7__/* .getParamsReqList */ .vj)(params)
          });
        case 2:
          res = _context2.sent;
          return _context2.abrupt("return", res.result);
        case 4:
        case "end":
          return _context2.stop();
      }
    }, _callee2);
  }));
  return function getCustomerUserList(_x2) {
    return _ref2.apply(this, arguments);
  };
}();
var getCustomerUserIndividualList = /*#__PURE__*/(/* unused pure expression or super */ null && (function () {
  var _ref3 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee3(params) {
    var res;
    return _regeneratorRuntime().wrap(function _callee3$(_context3) {
      while (1) switch (_context3.prev = _context3.next) {
        case 0:
          _context3.next = 2;
          return request(generateAPIPath('api/v2/customerUser/user/individual'), {
            params: getParamsReqList(params)
          });
        case 2:
          res = _context3.sent;
          return _context3.abrupt("return", res.result);
        case 4:
        case "end":
          return _context3.stop();
      }
    }, _callee3);
  }));
  return function getCustomerUserIndividualList(_x3) {
    return _ref3.apply(this, arguments);
  };
}()));
function customerUserListAll() {
  return _customerUserListAll.apply(this, arguments);
}

//update customer user
function _customerUserListAll() {
  _customerUserListAll = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee7() {
    var result;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee7$(_context7) {
      while (1) switch (_context7.prev = _context7.next) {
        case 0:
          _context7.prev = 0;
          _context7.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_5__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_7__/* .generateAPIPath */ .rH)("api/v2/customerUser/user"), {
            method: 'GET',
            params: {
              fields: ['*']
            }
          });
        case 3:
          result = _context7.sent;
          return _context7.abrupt("return", result.result);
        case 7:
          _context7.prev = 7;
          _context7.t0 = _context7["catch"](0);
          console.log(_context7.t0);
          throw _context7.t0;
        case 11:
        case "end":
          return _context7.stop();
      }
    }, _callee7, null, [[0, 7]]);
  }));
  return _customerUserListAll.apply(this, arguments);
}
var updateCustomerUser = /*#__PURE__*/function () {
  var _ref4 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee4(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee4$(_context4) {
      while (1) switch (_context4.prev = _context4.next) {
        case 0:
          _context4.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_5__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_7__/* .generateAPIPath */ .rH)('api/v2/customerUser/user'), {
            method: 'PUT',
            data: data
          });
        case 2:
          res = _context4.sent;
          return _context4.abrupt("return", res);
        case 4:
        case "end":
          return _context4.stop();
      }
    }, _callee4);
  }));
  return function updateCustomerUser(_x4) {
    return _ref4.apply(this, arguments);
  };
}();

//delete customer user
var deleteCustomerUser = /*#__PURE__*/(/* unused pure expression or super */ null && (function () {
  var _ref5 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee5(name) {
    var res;
    return _regeneratorRuntime().wrap(function _callee5$(_context5) {
      while (1) switch (_context5.prev = _context5.next) {
        case 0:
          _context5.next = 2;
          return request(generateAPIPath('api/v2/customerUser/user'), {
            method: 'DELETE',
            params: {
              name: name
            }
          });
        case 2:
          res = _context5.sent;
          return _context5.abrupt("return", res);
        case 4:
        case "end":
          return _context5.stop();
      }
    }, _callee5);
  }));
  return function deleteCustomerUser(_x5) {
    return _ref5.apply(this, arguments);
  };
}()));

//delete customer user credential
var deleteCustomerUserCredential = /*#__PURE__*/(/* unused pure expression or super */ null && (function () {
  var _ref6 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee6(name) {
    var res;
    return _regeneratorRuntime().wrap(function _callee6$(_context6) {
      while (1) switch (_context6.prev = _context6.next) {
        case 0:
          _context6.next = 2;
          return request(generateAPIPath('api/v2/customerUser/user-credential'), {
            method: 'DELETE',
            params: {
              name: name
            }
          });
        case 2:
          res = _context6.sent;
          return _context6.abrupt("return", res);
        case 4:
        case "end":
          return _context6.stop();
      }
    }, _callee6);
  }));
  return function deleteCustomerUserCredential(_x6) {
    return _ref6.apply(this, arguments);
  };
}()));
/**\r
 *\r
 * DYNAMIC ROLE APIs\r
 */
function listDynamicRoleAllSection() {
  return _listDynamicRoleAllSection.apply(this, arguments);
}
function _listDynamicRoleAllSection() {
  _listDynamicRoleAllSection = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee8() {
    var result;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee8$(_context8) {
      while (1) switch (_context8.prev = _context8.next) {
        case 0:
          _context8.prev = 0;
          _context8.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_5__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_7__/* .generateAPIPath */ .rH)("api/v2/dynamicRole/listAllSection"), {
            method: 'GET'
          });
        case 3:
          result = _context8.sent;
          return _context8.abrupt("return", result.result);
        case 7:
          _context8.prev = 7;
          _context8.t0 = _context8["catch"](0);
          console.log(_context8.t0);
          throw _context8.t0;
        case 11:
        case "end":
          return _context8.stop();
      }
    }, _callee8, null, [[0, 7]]);
  }));
  return _listDynamicRoleAllSection.apply(this, arguments);
}
function getDynamicRole() {
  return _getDynamicRole.apply(this, arguments);
}
function _getDynamicRole() {
  _getDynamicRole = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee9() {
    var result;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee9$(_context9) {
      while (1) switch (_context9.prev = _context9.next) {
        case 0:
          _context9.prev = 0;
          _context9.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_5__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_7__/* .generateAPIPath */ .rH)("api/v2/dynamicRole"), {
            method: 'GET',
            params: {
              page: 1,
              size: 100
            }
          });
        case 3:
          result = _context9.sent;
          return _context9.abrupt("return", result.result.data);
        case 7:
          _context9.prev = 7;
          _context9.t0 = _context9["catch"](0);
          console.log(_context9.t0);
          throw _context9.t0;
        case 11:
        case "end":
          return _context9.stop();
      }
    }, _callee9, null, [[0, 7]]);
  }));
  return _getDynamicRole.apply(this, arguments);
}
function createDynamicRole(_x7) {
  return _createDynamicRole.apply(this, arguments);
}
function _createDynamicRole() {
  _createDynamicRole = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee10(data) {
    var result;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee10$(_context10) {
      while (1) switch (_context10.prev = _context10.next) {
        case 0:
          _context10.prev = 0;
          _context10.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_5__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_7__/* .generateAPIPath */ .rH)("api/v2/dynamicRole"), {
            method: 'POST',
            data: data
          });
        case 3:
          result = _context10.sent;
          return _context10.abrupt("return", result.result);
        case 7:
          _context10.prev = 7;
          _context10.t0 = _context10["catch"](0);
          console.log(_context10.t0);
          throw _context10.t0;
        case 11:
        case "end":
          return _context10.stop();
      }
    }, _callee10, null, [[0, 7]]);
  }));
  return _createDynamicRole.apply(this, arguments);
}
function updateDynamicRole(_x8) {
  return _updateDynamicRole.apply(this, arguments);
}
function _updateDynamicRole() {
  _updateDynamicRole = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee11(data) {
    var result;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee11$(_context11) {
      while (1) switch (_context11.prev = _context11.next) {
        case 0:
          _context11.prev = 0;
          _context11.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_5__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_7__/* .generateAPIPath */ .rH)("api/v2/dynamicRole"), {
            method: 'PUT',
            data: data
          });
        case 3:
          result = _context11.sent;
          return _context11.abrupt("return", result.result);
        case 7:
          _context11.prev = 7;
          _context11.t0 = _context11["catch"](0);
          console.log(_context11.t0);
          throw _context11.t0;
        case 11:
        case "end":
          return _context11.stop();
      }
    }, _callee11, null, [[0, 7]]);
  }));
  return _updateDynamicRole.apply(this, arguments);
}
function removeDynamicRole(_x9) {
  return _removeDynamicRole.apply(this, arguments);
}
function _removeDynamicRole() {
  _removeDynamicRole = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee12(data) {
    var name, result;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee12$(_context12) {
      while (1) switch (_context12.prev = _context12.next) {
        case 0:
          _context12.prev = 0;
          name = data.name ? data.name : '';
          _context12.next = 4;
          return (0,_sscript__WEBPACK_IMPORTED_MODULE_6__/* .generalDelete */ .ID)('iot_dynamic_role', name);
        case 4:
          result = _context12.sent;
          return _context12.abrupt("return", result);
        case 8:
          _context12.prev = 8;
          _context12.t0 = _context12["catch"](0);
          throw _context12.t0;
        case 11:
        case "end":
          return _context12.stop();
      }
    }, _callee12, null, [[0, 8]]);
  }));
  return _removeDynamicRole.apply(this, arguments);
}//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///40063
`)},98041:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Bg: function() { return /* binding */ getTotalWarehouseQty; },
/* harmony export */   Ej: function() { return /* binding */ getTotalImportExportWarehouseQty; },
/* harmony export */   w5: function() { return /* binding */ getImportExportWarehouse; },
/* harmony export */   yI: function() { return /* binding */ getTotalItemQtyInRange; },
/* harmony export */   zB: function() { return /* binding */ getTotalWarerhouseItemQty; }
/* harmony export */ });
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(97857);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(7837);
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(467);





var handleError = function handleError(error) {
  console.log("Error in services/stock/dashboard: \\n".concat(error));
};
var CRUD_PATH = {
  totalQtyValue: 'stockReport/dashboard/totalQtyValue',
  totalImportExportQtyValue: 'stockReport/dashboard/totalQtyValue/import-export',
  totalItemQty: 'stockReport/dashboard/totalItemQty',
  totalImportExport: 'stockReport/dashboard/import-export',
  totalItemQtyInRange: 'stockReport/dashboard/totalItemQtyInRange',
  SUBMIT: 'deliveryNote/submit'
};
var getTotalWarerhouseItemQty = /*#__PURE__*/function () {
  var _ref = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.prev = 0;
          _context.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)("api/v2/".concat(CRUD_PATH.totalItemQty)), {
            method: 'GET',
            params: params
          });
        case 3:
          res = _context.sent;
          return _context.abrupt("return", {
            result: res.result.data,
            stockStatusCounts: res.result.stockStatusCounts
          });
        case 7:
          _context.prev = 7;
          _context.t0 = _context["catch"](0);
          handleError(_context.t0);
          return _context.abrupt("return", {
            result: null
          });
        case 11:
        case "end":
          return _context.stop();
      }
    }, _callee, null, [[0, 7]]);
  }));
  return function getTotalWarerhouseItemQty(_x) {
    return _ref.apply(this, arguments);
  };
}();
var getTotalWarehouseQty = /*#__PURE__*/function () {
  var _ref2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee2(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          _context2.prev = 0;
          _context2.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)("api/v2/".concat(CRUD_PATH.totalQtyValue)), {
            method: 'GET',
            params: params
          });
        case 3:
          res = _context2.sent;
          return _context2.abrupt("return", {
            result: res.result
          });
        case 7:
          _context2.prev = 7;
          _context2.t0 = _context2["catch"](0);
          handleError(_context2.t0);
          return _context2.abrupt("return", {
            result: null
          });
        case 11:
        case "end":
          return _context2.stop();
      }
    }, _callee2, null, [[0, 7]]);
  }));
  return function getTotalWarehouseQty(_x2) {
    return _ref2.apply(this, arguments);
  };
}();
var getTotalImportExportWarehouseQty = /*#__PURE__*/function () {
  var _ref3 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee3(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee3$(_context3) {
      while (1) switch (_context3.prev = _context3.next) {
        case 0:
          _context3.prev = 0;
          _context3.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)("api/v2/".concat(CRUD_PATH.totalImportExportQtyValue)), {
            method: 'GET',
            params: params
          });
        case 3:
          res = _context3.sent;
          return _context3.abrupt("return", {
            result: res.result
          });
        case 7:
          _context3.prev = 7;
          _context3.t0 = _context3["catch"](0);
          handleError(_context3.t0);
          return _context3.abrupt("return", {
            result: null
          });
        case 11:
        case "end":
          return _context3.stop();
      }
    }, _callee3, null, [[0, 7]]);
  }));
  return function getTotalImportExportWarehouseQty(_x3) {
    return _ref3.apply(this, arguments);
  };
}();
var getImportExportWarehouse = /*#__PURE__*/function () {
  var _ref4 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee4(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee4$(_context4) {
      while (1) switch (_context4.prev = _context4.next) {
        case 0:
          _context4.prev = 0;
          _context4.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)("api/v2/".concat(CRUD_PATH.totalImportExport)), {
            method: 'GET',
            params: params
          });
        case 3:
          res = _context4.sent;
          return _context4.abrupt("return", {
            result: res.result
          });
        case 7:
          _context4.prev = 7;
          _context4.t0 = _context4["catch"](0);
          handleError(_context4.t0);
          return _context4.abrupt("return", {
            result: null
          });
        case 11:
        case "end":
          return _context4.stop();
      }
    }, _callee4, null, [[0, 7]]);
  }));
  return function getImportExportWarehouse(_x4) {
    return _ref4.apply(this, arguments);
  };
}();
var getTotalItemQtyInRange = /*#__PURE__*/function () {
  var _ref5 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee5(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee5$(_context5) {
      while (1) switch (_context5.prev = _context5.next) {
        case 0:
          _context5.prev = 0;
          _context5.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)("api/v2/".concat(CRUD_PATH.totalItemQtyInRange)), {
            method: 'GET',
            params: D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default()({}, params), {}, {
              item_code_list: JSON.stringify(params.item_code_list)
            })
          });
        case 3:
          res = _context5.sent;
          return _context5.abrupt("return", {
            data: res.result.data
          });
        case 7:
          _context5.prev = 7;
          _context5.t0 = _context5["catch"](0);
          handleError(_context5.t0);
          return _context5.abrupt("return", {
            data: null
          });
        case 11:
        case "end":
          return _context5.stop();
      }
    }, _callee5, null, [[0, 7]]);
  }));
  return function getTotalItemQtyInRange(_x5) {
    return _ref5.apply(this, arguments);
  };
}();//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///98041
`)},80320:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   h: function() { return /* binding */ genDownloadUrl; }
/* harmony export */ });
/* harmony import */ var _common_contanst__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(95028);

var genDownloadUrl = function genDownloadUrl(fileUrl) {
  return "".concat(_common_contanst__WEBPACK_IMPORTED_MODULE_0__/* .API_URL_DEV */ .v, "/api/v2/file/download?file_url=").concat(fileUrl);
};//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiODAzMjAuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFnRDtBQUV6QyxJQUFNQyxjQUFjLEdBQUcsU0FBakJBLGNBQWNBLENBQUlDLE9BQWUsRUFBSztFQUNqRCxVQUFBQyxNQUFBLENBQVVILGtFQUFXLHFDQUFBRyxNQUFBLENBQWtDRCxPQUFPO0FBQ2hFLENBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hbnQtZGVzaWduLXByby8uL3NyYy91dGlscy9maWxlLnRzP2FlNDQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQVBJX1VSTF9ERVYgfSBmcm9tICdAL2NvbW1vbi9jb250YW5zdCc7XHJcblxyXG5leHBvcnQgY29uc3QgZ2VuRG93bmxvYWRVcmwgPSAoZmlsZVVybDogc3RyaW5nKSA9PiB7XHJcbiAgcmV0dXJuIGAke0FQSV9VUkxfREVWfS9hcGkvdjIvZmlsZS9kb3dubG9hZD9maWxlX3VybD0ke2ZpbGVVcmx9YDtcclxufTtcclxuIl0sIm5hbWVzIjpbIkFQSV9VUkxfREVWIiwiZ2VuRG93bmxvYWRVcmwiLCJmaWxlVXJsIiwiY29uY2F0Il0sInNvdXJjZVJvb3QiOiIifQ==
//# sourceURL=webpack-internal:///80320
`)},5251:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   dZ: function() { return /* binding */ formatNumberOrString; },
/* harmony export */   lb: function() { return /* binding */ formatMoney; },
/* harmony export */   tq: function() { return /* binding */ formatNumberSummary; }
/* harmony export */ });
/* unused harmony export formatNumberWithSpe */
/* harmony import */ var numeral__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(92077);
/* harmony import */ var numeral__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(numeral__WEBPACK_IMPORTED_MODULE_0__);

var formatNumberSummary = function formatNumberSummary(num) {
  var formatter = Intl.NumberFormat('en', {
    notation: 'compact'
  });
  return formatter.format(num || 0);
};
var formatNumberWithSpe = function formatNumberWithSpe(num) {
  try {
    var n = new Intl.NumberFormat('ja-JP').format(num);
    return Number(n);
  } catch (error) {
    return NaN;
  }
};
var formatNumber = function formatNumber(num, options) {
  if (typeof num === 'number') return parseFloat(num.toFixed((options === null || options === void 0 ? void 0 : options.digits) || 2));
  return typeof (options === null || options === void 0 ? void 0 : options.defaultValue) === 'undefined' ? undefined : options === null || options === void 0 ? void 0 : options.defaultValue;
};
/**\r
 * @description 2.434333333333 || '2.434333333333' => 2.43\r
 */
var formatNumberOrString = function formatNumberOrString(stringLikeNumber, options) {
  try {
    var num = options.parseFloat || options.digits && options.digits > 0 ? parseFloat(stringLikeNumber) : parseInt(stringLikeNumber);
    num = num || options["default"];
    if (options !== null && options !== void 0 && options.min) {
      num = num <= options.min ? options.min : num;
    }
    if (options !== null && options !== void 0 && options.max) {
      num = num >= options.max ? options.max : num;
    }
    return formatNumber(num, {
      defaultValue: options["default"],
      digits: options.digits
    });
  } catch (error) {
    return options["default"];
  }
};
var formatMoney = function formatMoney(money) {
  try {
    // return numeral(money).format('0,0.00') || '0'
    return numeral__WEBPACK_IMPORTED_MODULE_0___default()(money).format('0,0') || '0';
  } catch (error) {
    return '0';
  }
};//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///5251
`)},7369:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   W: function() { return /* binding */ toFirstUpperCase; },
/* harmony export */   w: function() { return /* binding */ nonAccentVietnamese; }
/* harmony export */ });
function nonAccentVietnamese(str) {
  var _str$toString;
  var _str = str === null || str === void 0 || (_str$toString = str.toString) === null || _str$toString === void 0 ? void 0 : _str$toString.call(str);
  if (typeof _str !== 'string') return str;
  _str = _str.toLowerCase();
  //     We can also use this instead of from line 11 to line 17
  //     str = str.replace(/\\u00E0|\\u00E1|\\u1EA1|\\u1EA3|\\u00E3|\\u00E2|\\u1EA7|\\u1EA5|\\u1EAD|\\u1EA9|\\u1EAB|\\u0103|\\u1EB1|\\u1EAF|\\u1EB7|\\u1EB3|\\u1EB5/g, "a");
  //     str = str.replace(/\\u00E8|\\u00E9|\\u1EB9|\\u1EBB|\\u1EBD|\\u00EA|\\u1EC1|\\u1EBF|\\u1EC7|\\u1EC3|\\u1EC5/g, "e");
  //     str = str.replace(/\\u00EC|\\u00ED|\\u1ECB|\\u1EC9|\\u0129/g, "i");
  //     str = str.replace(/\\u00F2|\\u00F3|\\u1ECD|\\u1ECF|\\u00F5|\\u00F4|\\u1ED3|\\u1ED1|\\u1ED9|\\u1ED5|\\u1ED7|\\u01A1|\\u1EDD|\\u1EDB|\\u1EE3|\\u1EDF|\\u1EE1/g, "o");
  //     str = str.replace(/\\u00F9|\\u00FA|\\u1EE5|\\u1EE7|\\u0169|\\u01B0|\\u1EEB|\\u1EE9|\\u1EF1|\\u1EED|\\u1EEF/g, "u");
  //     str = str.replace(/\\u1EF3|\\u00FD|\\u1EF5|\\u1EF7|\\u1EF9/g, "y");
  //     str = str.replace(/\\u0111/g, "d");
  _str = _str.replace(/\xE0|\xE1|\u1EA1|\u1EA3|\xE3|\xE2|\u1EA7|\u1EA5|\u1EAD|\u1EA9|\u1EAB|\u0103|\u1EB1|\u1EAF|\u1EB7|\u1EB3|\u1EB5/g, 'a');
  _str = _str.replace(/\xE8|\xE9|\u1EB9|\u1EBB|\u1EBD|\xEA|\u1EC1|\u1EBF|\u1EC7|\u1EC3|\u1EC5/g, 'e');
  _str = _str.replace(/\xEC|\xED|\u1ECB|\u1EC9|\u0129/g, 'i');
  _str = _str.replace(/\xF2|\xF3|\u1ECD|\u1ECF|\xF5|\xF4|\u1ED3|\u1ED1|\u1ED9|\u1ED5|\u1ED7|\u01A1|\u1EDD|\u1EDB|\u1EE3|\u1EDF|\u1EE1/g, 'o');
  _str = _str.replace(/\xF9|\xFA|\u1EE5|\u1EE7|\u0169|\u01B0|\u1EEB|\u1EE9|\u1EF1|\u1EED|\u1EEF/g, 'u');
  _str = _str.replace(/\u1EF3|\xFD|\u1EF5|\u1EF7|\u1EF9/g, 'y');
  _str = _str.replace(/\u0111/g, 'd');
  // Some system encode vietnamese combining accent as individual utf-8 characters
  _str = _str.replace(/\\u0300|\\u0301|\\u0303|\\u0309|\\u0323/g, ''); // Huy\u1EC1n s\u1EAFc h\u1ECFi ng\xE3 n\u1EB7ng
  _str = _str.replace(/\\u02C6|\\u0306|\\u031B/g, ''); // \xC2, \xCA, \u0102, \u01A0, \u01AF
  return _str.split(',').join('');
}
var toFirstUpperCase = function toFirstUpperCase(str) {
  if (!str) return '';
  return str.charAt(0).toUpperCase() + str.slice(1);
};//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///7369
`)}}]);
