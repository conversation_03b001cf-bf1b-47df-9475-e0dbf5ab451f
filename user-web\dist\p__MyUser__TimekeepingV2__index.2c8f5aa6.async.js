"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[7573],{65573:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(29905);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(85893);


var FallbackComponent = function FallbackComponent() {
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .ZP, {
    status: "403",
    title: "Kh\\xF4ng th\\u1EC3 truy c\\u1EADp",
    subTitle: "Xin l\\u1ED7i, b\\u1EA1n kh\\xF4ng c\\xF3 quy\\u1EC1n xem n\\u1ED9i dung n\\xE0y."
    //   extra={
    //     <Button type="primary" href="/">
    //       Back Home
    //     </Button>
    //   }
  });
};
/* harmony default export */ __webpack_exports__.Z = (FallbackComponent);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNjU1NzMuanMiLCJtYXBwaW5ncyI6Ijs7QUFBOEI7QUFBQTtBQUU5QixJQUFNRyxpQkFBaUIsR0FBRyxTQUFwQkEsaUJBQWlCQSxDQUFBLEVBQVM7RUFDOUIsb0JBQ0VELHNEQUFBLENBQUNGLHNEQUFNO0lBQ0xJLE1BQU0sRUFBQyxLQUFLO0lBQ1pDLEtBQUssRUFBQyxpQ0FBb0I7SUFDMUJDLFFBQVEsRUFBQztJQUNUO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7RUFBQSxDQUNELENBQUM7QUFFTixDQUFDO0FBRUQsc0RBQWVILGlCQUFpQiIsInNvdXJjZXMiOlsid2VicGFjazovL2FudC1kZXNpZ24tcHJvLy4vc3JjL2NvbXBvbmVudHMvRmFsbGJhY2tDb250ZW50L2luZGV4LnRzeD9mZGRhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFJlc3VsdCB9IGZyb20gJ2FudGQnO1xyXG5cclxuY29uc3QgRmFsbGJhY2tDb21wb25lbnQgPSAoKSA9PiB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxSZXN1bHRcclxuICAgICAgc3RhdHVzPVwiNDAzXCJcclxuICAgICAgdGl0bGU9XCJLaMO0bmcgdGjhu4MgdHJ1eSBj4bqtcFwiXHJcbiAgICAgIHN1YlRpdGxlPVwiWGluIGzhu5dpLCBi4bqhbiBraMO0bmcgY8OzIHF1eeG7gW4geGVtIG7hu5lpIGR1bmcgbsOgeS5cIlxyXG4gICAgICAvLyAgIGV4dHJhPXtcclxuICAgICAgLy8gICAgIDxCdXR0b24gdHlwZT1cInByaW1hcnlcIiBocmVmPVwiL1wiPlxyXG4gICAgICAvLyAgICAgICBCYWNrIEhvbWVcclxuICAgICAgLy8gICAgIDwvQnV0dG9uPlxyXG4gICAgICAvLyAgIH1cclxuICAgIC8+XHJcbiAgKTtcclxufTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IEZhbGxiYWNrQ29tcG9uZW50O1xyXG4iXSwibmFtZXMiOlsiUmVzdWx0IiwianN4IiwiX2pzeCIsIkZhbGxiYWNrQ29tcG9uZW50Iiwic3RhdHVzIiwidGl0bGUiLCJzdWJUaXRsZSJdLCJzb3VyY2VSb290IjoiIn0=
//# sourceURL=webpack-internal:///65573
`)},27076:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(5574);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(97857);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _utils_string__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(7369);
/* harmony import */ var _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(6110);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(7837);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(67294);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(85893);







var PageContainerTabsWithSearch = function PageContainerTabsWithSearch(_ref) {
  var tabsItems = _ref.tabsItems,
    _ref$searchParamsUrlK = _ref.searchParamsUrlKey,
    searchParamsUrlKey = _ref$searchParamsUrlK === void 0 ? 'tab' : _ref$searchParamsUrlK,
    _onTabChange = _ref.onTabChange,
    pageTitle = _ref.pageTitle;
  var tabsItemsFormat = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(function () {
    return tabsItems === null || tabsItems === void 0 ? void 0 : tabsItems.map(function (item) {
      return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({}, item), {}, {
        tabKey: item.tabKey || (0,_utils_string__WEBPACK_IMPORTED_MODULE_5__/* .nonAccentVietnamese */ .w)(item.label)
      });
    });
  }, [tabsItems]);
  var _useSearchParams = (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)(),
    _useSearchParams2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_0___default()(_useSearchParams, 2),
    searchParams = _useSearchParams2[0],
    setSearchParams = _useSearchParams2[1];
  var tabActive = searchParamsUrlKey ? searchParams.get(searchParamsUrlKey) : undefined;
  console.log('tabActive', tabActive);
  var setTabActive = searchParamsUrlKey ? function (tabActiveVal) {
    searchParams.set(searchParamsUrlKey, tabActiveVal.toString());
    setSearchParams(searchParams);
  } : undefined;
  var tabList = tabsItemsFormat === null || tabsItemsFormat === void 0 ? void 0 : tabsItemsFormat.map(function (item) {
    return {
      tab: item.label,
      key: item.tabKey,
      tabKey: item.tabKey
    };
  });
  var tabPageActive = searchParamsUrlKey ? (tabsItemsFormat === null || tabsItemsFormat === void 0 ? void 0 : tabsItemsFormat.find(function (item) {
    return item.tabKey === tabActive;
  })) || (tabsItemsFormat === null || tabsItemsFormat === void 0 ? void 0 : tabsItemsFormat[0]) : undefined;
  var ComponentActive = tabPageActive === null || tabPageActive === void 0 ? void 0 : tabPageActive.component;
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_6__/* .PageContainer */ ._z, {
    fixedHeader: true,
    extra: tabPageActive === null || tabPageActive === void 0 ? void 0 : tabPageActive.extraPage,
    tabActiveKey: tabPageActive === null || tabPageActive === void 0 ? void 0 : tabPageActive.tabKey,
    tabList: tabList,
    onTabChange: function onTabChange(tabActiveVal) {
      _onTabChange === null || _onTabChange === void 0 || _onTabChange(tabActiveVal);
      if (searchParamsUrlKey) setTabActive === null || setTabActive === void 0 || setTabActive(tabActiveVal);
    },
    title: pageTitle,
    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(react__WEBPACK_IMPORTED_MODULE_3__.Suspense, {
      fallback: (tabPageActive === null || tabPageActive === void 0 ? void 0 : tabPageActive.fallback) || null,
      children: ComponentActive && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(ComponentActive, {})
    })
  });
};
/* harmony default export */ __webpack_exports__.Z = (PageContainerTabsWithSearch);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///27076
`)},92273:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return /* binding */ TimekeepingV2; }
});

// EXTERNAL MODULE: ./src/components/FallbackContent/index.tsx
var FallbackContent = __webpack_require__(65573);
// EXTERNAL MODULE: ./src/components/PageContainerTabsWithSearch/index.tsx
var PageContainerTabsWithSearch = __webpack_require__(27076);
// EXTERNAL MODULE: ./src/utils/lazy.tsx
var lazy = __webpack_require__(48576);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-skeleton/es/components/Descriptions/index.js
var Descriptions = __webpack_require__(44688);
// EXTERNAL MODULE: ./src/.umi-production/exports.ts + 34 modules
var _umi_production_exports = __webpack_require__(7837);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/toConsumableArray.js
var toConsumableArray = __webpack_require__(19632);
var toConsumableArray_default = /*#__PURE__*/__webpack_require__.n(toConsumableArray);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/regeneratorRuntime.js
var regeneratorRuntime = __webpack_require__(15009);
var regeneratorRuntime_default = /*#__PURE__*/__webpack_require__.n(regeneratorRuntime);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/objectSpread2.js
var objectSpread2 = __webpack_require__(97857);
var objectSpread2_default = /*#__PURE__*/__webpack_require__.n(objectSpread2);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(99289);
var asyncToGenerator_default = /*#__PURE__*/__webpack_require__.n(asyncToGenerator);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/slicedToArray.js
var slicedToArray = __webpack_require__(5574);
var slicedToArray_default = /*#__PURE__*/__webpack_require__.n(slicedToArray);
// EXTERNAL MODULE: ./src/services/customerUser.ts
var customerUser = __webpack_require__(40063);
// EXTERNAL MODULE: ./src/services/utils.ts
var utils = __webpack_require__(467);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/SelectOutlined.js + 1 modules
var SelectOutlined = __webpack_require__(49591);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/PrinterOutlined.js + 1 modules
var PrinterOutlined = __webpack_require__(30019);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/layouts/ProForm/index.js + 3 modules
var ProForm = __webpack_require__(34994);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/Select/index.js
var Select = __webpack_require__(64317);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/DatePicker/index.js + 5 modules
var DatePicker = __webpack_require__(50335);
// EXTERNAL MODULE: ./node_modules/antd/es/form/index.js + 18 modules
var es_form = __webpack_require__(98138);
// EXTERNAL MODULE: ./node_modules/antd/es/card/index.js + 3 modules
var card = __webpack_require__(76216);
// EXTERNAL MODULE: ./node_modules/antd/es/flex/index.js + 2 modules
var flex = __webpack_require__(86250);
// EXTERNAL MODULE: ./node_modules/antd/es/space/index.js + 2 modules
var space = __webpack_require__(42075);
// EXTERNAL MODULE: ./node_modules/antd/es/select/index.js + 30 modules
var es_select = __webpack_require__(83863);
// EXTERNAL MODULE: ./node_modules/antd/es/dropdown/index.js + 1 modules
var dropdown = __webpack_require__(85418);
// EXTERNAL MODULE: ./node_modules/antd/es/button/index.js + 9 modules
var es_button = __webpack_require__(14726);
// EXTERNAL MODULE: ./node_modules/dayjs/dayjs.min.js
var dayjs_min = __webpack_require__(27484);
var dayjs_min_default = /*#__PURE__*/__webpack_require__.n(dayjs_min);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./src/pages/MyUser/ExportPage/components/AttendanceReportV2Print.tsx
var AttendanceReportV2Print = __webpack_require__(23984);
// EXTERNAL MODULE: ./src/pages/MyUser/ExportPage/components/TimesheetReportPrint.tsx
var TimesheetReportPrint = __webpack_require__(3672);
// EXTERNAL MODULE: ./node_modules/react/jsx-runtime.js
var jsx_runtime = __webpack_require__(85893);
;// CONCATENATED MODULE: ./src/pages/MyUser/TimekeepingV2/Report/index.tsx


















var TimekeepingReport = function TimekeepingReport() {
  var _useState = (0,react.useState)(true),
    _useState2 = slicedToArray_default()(_useState, 2),
    open = _useState2[0],
    setOpen = _useState2[1];
  var _Form$useForm = es_form/* default */.Z.useForm(),
    _Form$useForm2 = slicedToArray_default()(_Form$useForm, 1),
    form = _Form$useForm2[0];
  var _useState3 = (0,react.useState)(),
    _useState4 = slicedToArray_default()(_useState3, 2),
    filter = _useState4[0],
    setFilter = _useState4[1];
  var _useState5 = (0,react.useState)('timesheet-report'),
    _useState6 = slicedToArray_default()(_useState5, 2),
    type = _useState6[0],
    setType = _useState6[1];
  //current user
  var _useModel = (0,_umi_production_exports.useModel)("@@initialState"),
    initialState = _useModel.initialState;
  var curentUser = initialState === null || initialState === void 0 ? void 0 : initialState.currentUser;
  var handleFinish = /*#__PURE__*/function () {
    var _ref = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(e) {
      var newFilter;
      return regeneratorRuntime_default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            newFilter = objectSpread2_default()(objectSpread2_default()({}, form.getFieldsValue()), {}, {
              start_date: dayjs_min_default()(form.getFieldValue('month')).set('date', 1).format('YYYY-MM-DD'),
              end_date: dayjs_min_default()(form.getFieldValue('month')).set('date', 1).add(1, 'month').subtract(1, 'day').format('YYYY-MM-DD')
            });
            setFilter(objectSpread2_default()({}, newFilter));
            setOpen(false);
            return _context.abrupt("return", true);
          case 4:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }));
    return function handleFinish(_x) {
      return _ref.apply(this, arguments);
    };
  }();
  return /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
    children: [/*#__PURE__*/(0,jsx_runtime.jsx)(card/* default */.Z, {
      children: /*#__PURE__*/(0,jsx_runtime.jsxs)(flex/* default */.Z, {
        justify: "space-between",
        children: [/*#__PURE__*/(0,jsx_runtime.jsxs)(space/* default */.Z, {
          size: 'middle',
          children: [/*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
            id: 'common.report'
          }), /*#__PURE__*/(0,jsx_runtime.jsx)(es_select/* default */.Z
          // disabled
          , {
            defaultValue: 'timesheet-report',
            options: [{
              value: 'attendance-report-v2',
              label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
                id: 'common.attendance_report'
              })
            }, {
              value: 'timesheet-report',
              label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
                id: 'common.timesheet_report'
              })
            }],
            onSelect: function onSelect(e) {
              setType(e);
            }
          }), /*#__PURE__*/(0,jsx_runtime.jsx)(dropdown/* default */.Z, {
            menu: {
              items: []
            },
            open: open,
            dropdownRender: function dropdownRender(menu) {
              return /*#__PURE__*/(0,jsx_runtime.jsx)(card/* default */.Z, {
                size: "small",
                onClick: function onClick(e) {
                  e.preventDefault();
                  e.stopPropagation();
                },
                children: /*#__PURE__*/(0,jsx_runtime.jsxs)(ProForm/* ProForm */.A, {
                  form: form,
                  grid: true,
                  size: "small",
                  autoFocusFirstInput: true,
                  style: {
                    width: '30vh'
                  },
                  onFinish: handleFinish,
                  onClick: function onClick(e) {
                    e.preventDefault();
                    e.stopPropagation();
                  },
                  submitter: {
                    render: function render(props, defaultDoms) {
                      return [/*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
                        onClick: function onClick() {
                          props.reset();
                        },
                        style: {
                          width: '30%'
                        },
                        children: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
                          id: "common.cancel"
                        })
                      }, "reset"), /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
                        onClick: function onClick() {
                          props.submit();
                        },
                        type: "primary",
                        style: {
                          width: '30%'
                        },
                        children: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
                          id: "common.ok"
                        })
                      }, "ok")];
                    }
                  },
                  children: [/*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
                    showSearch: true
                    // required
                    ,
                    initialValue: curentUser === null || curentUser === void 0 ? void 0 : curentUser.user_id,
                    label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
                      id: "warehouse-management.import-voucher.employee"
                    }),
                    request: ( /*#__PURE__*/function () {
                      var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2(option) {
                        var listUser, options;
                        return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
                          while (1) switch (_context2.prev = _context2.next) {
                            case 0:
                              _context2.next = 2;
                              return (0,customerUser/* getCustomerUserList */.J9)();
                            case 2:
                              listUser = _context2.sent;
                              options = listUser.data.map(function (item) {
                                return {
                                  label: "".concat(item.first_name, " ").concat(item.last_name),
                                  value: item.name
                                };
                              });
                              return _context2.abrupt("return", [{
                                label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
                                  id: 'common.all'
                                }),
                                value: ''
                              }].concat(toConsumableArray_default()(options)));
                            case 5:
                            case "end":
                              return _context2.stop();
                          }
                        }, _callee2);
                      }));
                      return function (_x2) {
                        return _ref2.apply(this, arguments);
                      };
                    }()),
                    name: "employee_id"
                    // onChange={handleChooseItem}
                    // disabled
                    ,
                    colProps: {
                      span: 24
                    },
                    width: 'md'
                  }), /*#__PURE__*/(0,jsx_runtime.jsx)(DatePicker/* default */.Z, {
                    name: "month",
                    required: true,
                    rules: [{
                      required: true,
                      message: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
                        id: "common.required_error"
                      })
                    }],
                    label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
                      id: 'common.month'
                    }),
                    colProps: {
                      span: 12
                    },
                    fieldProps: {
                      picker: 'month',
                      // format: DEFAULT_DATE_FORMAT_WITHOUT_TIME,
                      format: 'MM-YYYY'
                    }
                  })]
                })
              });
            },
            trigger: ['hover'],
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
              type: "primary",
              onClick: function onClick() {
                setOpen(!open);
              },
              children: /*#__PURE__*/(0,jsx_runtime.jsxs)(space/* default */.Z, {
                children: [/*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
                  id: 'common.filter'
                }), /*#__PURE__*/(0,jsx_runtime.jsx)(SelectOutlined/* default */.Z, {})]
              })
            })
          })]
        }), filter && /*#__PURE__*/(0,jsx_runtime.jsxs)(es_button/* default */.ZP, {
          icon: /*#__PURE__*/(0,jsx_runtime.jsx)(PrinterOutlined/* default */.Z, {}),
          onClick: function onClick() {
            return (0,utils/* openInNewTab */.YQ)("/employee-management/timekeeping/to-pdf?type=".concat(type, "&start_date=").concat(filter.start_date, "&end_date=").concat(filter.end_date).concat(filter.employee_id ? "&employee_id=".concat(filter.employee_id) : ''));
          },
          type: "primary",
          children: [' ', /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
            id: 'common.print_receipt'
          })]
        }, 'download')]
      })
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(card/* default */.Z, {
      children: filter && (type === 'attendance-report-v2' ? /*#__PURE__*/(0,jsx_runtime.jsx)(AttendanceReportV2Print/* default */.Z, {
        end_date: filter.end_date,
        start_date: filter.start_date,
        employee_id: filter.employee_id
      }) : /*#__PURE__*/(0,jsx_runtime.jsx)(TimesheetReportPrint/* default */.Z, {
        end_date: filter.end_date,
        start_date: filter.start_date,
        employee_id: filter.employee_id
      }))
    })]
  });
};
/* harmony default export */ var Report = (TimekeepingReport);
;// CONCATENATED MODULE: ./src/pages/MyUser/TimekeepingV2/index.tsx







var Approval = (0,lazy/* myLazy */.Q)(function () {
  return Promise.all(/* import() */[__webpack_require__.e(5514), __webpack_require__.e(8021), __webpack_require__.e(1403), __webpack_require__.e(4560), __webpack_require__.e(691), __webpack_require__.e(2674)]).then(__webpack_require__.bind(__webpack_require__, 96798));
});
var TimesheetTable = (0,lazy/* myLazy */.Q)(function () {
  return Promise.all(/* import() */[__webpack_require__.e(5514), __webpack_require__.e(8021), __webpack_require__.e(1403), __webpack_require__.e(4560), __webpack_require__.e(691), __webpack_require__.e(6435)]).then(__webpack_require__.bind(__webpack_require__, 80875));
});
var Index = function Index(_ref) {
  var children = _ref.children;
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  var access = (0,_umi_production_exports.useAccess)();
  var canRead = access.canAccessPageTimeKeepingManagement();
  return /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.Access, {
    accessible: canRead,
    fallback: /*#__PURE__*/(0,jsx_runtime.jsx)(FallbackContent/* default */.Z, {}),
    children: /*#__PURE__*/(0,jsx_runtime.jsx)(PageContainerTabsWithSearch/* default */.Z, {
      tabsItems: [{
        label: formatMessage({
          id: 'common.timesheet'
        }),
        component: function component() {
          return /*#__PURE__*/(0,jsx_runtime.jsx)(TimesheetTable, {});
        },
        fallback: /*#__PURE__*/(0,jsx_runtime.jsx)(Descriptions/* DescriptionsSkeleton */.Yk, {
          active: true
        })
      }, {
        label: formatMessage({
          id: 'common.approval'
        }),
        component: function component() {
          return /*#__PURE__*/(0,jsx_runtime.jsx)(Approval, {});
        },
        fallback: /*#__PURE__*/(0,jsx_runtime.jsx)(Descriptions/* DescriptionsSkeleton */.Yk, {
          active: true
        })
      }, {
        label: formatMessage({
          id: 'common.report'
        }),
        fallback: /*#__PURE__*/(0,jsx_runtime.jsx)(Descriptions/* DescriptionsSkeleton */.Yk, {
          active: true
        }),
        component: function component() {
          return /*#__PURE__*/(0,jsx_runtime.jsx)(Report, {});
        }
      }]
    })
  });
};
/* harmony default export */ var TimekeepingV2 = (Index);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///92273
`)},40063:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   J9: function() { return /* binding */ getCustomerUserList; },
/* harmony export */   Lf: function() { return /* binding */ listDynamicRoleAllSection; },
/* harmony export */   cb: function() { return /* binding */ updateCustomerUser; },
/* harmony export */   f6: function() { return /* binding */ createDynamicRole; },
/* harmony export */   fh: function() { return /* binding */ updateDynamicRole; },
/* harmony export */   jt: function() { return /* binding */ customerUserListAll; },
/* harmony export */   rX: function() { return /* binding */ removeDynamicRole; },
/* harmony export */   w: function() { return /* binding */ getDynamicRole; },
/* harmony export */   y_: function() { return /* binding */ createCustomerUser; }
/* harmony export */ });
/* unused harmony exports IIotDynamicRole, getCustomerUserIndividualList, deleteCustomerUser, deleteCustomerUserCredential */
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_createClass_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(72004);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_createClass_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_createClass_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_classCallCheck_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(12444);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_classCallCheck_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_classCallCheck_js__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_defineProperty_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(9783);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_defineProperty_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_defineProperty_js__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(7837);
/* harmony import */ var _sscript__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(39750);
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(467);








var IIotDynamicRole = /*#__PURE__*/(/* unused pure expression or super */ null && (_createClass(function IIotDynamicRole() {
  _classCallCheck(this, IIotDynamicRole);
  _defineProperty(this, "name", void 0);
  _defineProperty(this, "label", void 0);
  // Data
  _defineProperty(this, "role", void 0);
  // Data
  _defineProperty(this, "iot_customer", void 0);
  // Link
  _defineProperty(this, "sections", void 0);
} // Data
)));
var createCustomerUser = /*#__PURE__*/function () {
  var _ref = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_5__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_7__/* .generateAPIPath */ .rH)('api/v2/register/customer-user-with-role'), {
            method: 'POST',
            data: data
          });
        case 2:
          res = _context.sent;
          return _context.abrupt("return", res);
        case 4:
        case "end":
          return _context.stop();
      }
    }, _callee);
  }));
  return function createCustomerUser(_x) {
    return _ref.apply(this, arguments);
  };
}();
var getCustomerUserList = /*#__PURE__*/function () {
  var _ref2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee2(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          _context2.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_5__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_7__/* .generateAPIPath */ .rH)('api/v2/customerUser/user'), {
            params: (0,_utils__WEBPACK_IMPORTED_MODULE_7__/* .getParamsReqList */ .vj)(params)
          });
        case 2:
          res = _context2.sent;
          return _context2.abrupt("return", res.result);
        case 4:
        case "end":
          return _context2.stop();
      }
    }, _callee2);
  }));
  return function getCustomerUserList(_x2) {
    return _ref2.apply(this, arguments);
  };
}();
var getCustomerUserIndividualList = /*#__PURE__*/(/* unused pure expression or super */ null && (function () {
  var _ref3 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee3(params) {
    var res;
    return _regeneratorRuntime().wrap(function _callee3$(_context3) {
      while (1) switch (_context3.prev = _context3.next) {
        case 0:
          _context3.next = 2;
          return request(generateAPIPath('api/v2/customerUser/user/individual'), {
            params: getParamsReqList(params)
          });
        case 2:
          res = _context3.sent;
          return _context3.abrupt("return", res.result);
        case 4:
        case "end":
          return _context3.stop();
      }
    }, _callee3);
  }));
  return function getCustomerUserIndividualList(_x3) {
    return _ref3.apply(this, arguments);
  };
}()));
function customerUserListAll() {
  return _customerUserListAll.apply(this, arguments);
}

//update customer user
function _customerUserListAll() {
  _customerUserListAll = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee7() {
    var result;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee7$(_context7) {
      while (1) switch (_context7.prev = _context7.next) {
        case 0:
          _context7.prev = 0;
          _context7.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_5__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_7__/* .generateAPIPath */ .rH)("api/v2/customerUser/user"), {
            method: 'GET',
            params: {
              fields: ['*']
            }
          });
        case 3:
          result = _context7.sent;
          return _context7.abrupt("return", result.result);
        case 7:
          _context7.prev = 7;
          _context7.t0 = _context7["catch"](0);
          console.log(_context7.t0);
          throw _context7.t0;
        case 11:
        case "end":
          return _context7.stop();
      }
    }, _callee7, null, [[0, 7]]);
  }));
  return _customerUserListAll.apply(this, arguments);
}
var updateCustomerUser = /*#__PURE__*/function () {
  var _ref4 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee4(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee4$(_context4) {
      while (1) switch (_context4.prev = _context4.next) {
        case 0:
          _context4.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_5__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_7__/* .generateAPIPath */ .rH)('api/v2/customerUser/user'), {
            method: 'PUT',
            data: data
          });
        case 2:
          res = _context4.sent;
          return _context4.abrupt("return", res);
        case 4:
        case "end":
          return _context4.stop();
      }
    }, _callee4);
  }));
  return function updateCustomerUser(_x4) {
    return _ref4.apply(this, arguments);
  };
}();

//delete customer user
var deleteCustomerUser = /*#__PURE__*/(/* unused pure expression or super */ null && (function () {
  var _ref5 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee5(name) {
    var res;
    return _regeneratorRuntime().wrap(function _callee5$(_context5) {
      while (1) switch (_context5.prev = _context5.next) {
        case 0:
          _context5.next = 2;
          return request(generateAPIPath('api/v2/customerUser/user'), {
            method: 'DELETE',
            params: {
              name: name
            }
          });
        case 2:
          res = _context5.sent;
          return _context5.abrupt("return", res);
        case 4:
        case "end":
          return _context5.stop();
      }
    }, _callee5);
  }));
  return function deleteCustomerUser(_x5) {
    return _ref5.apply(this, arguments);
  };
}()));

//delete customer user credential
var deleteCustomerUserCredential = /*#__PURE__*/(/* unused pure expression or super */ null && (function () {
  var _ref6 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee6(name) {
    var res;
    return _regeneratorRuntime().wrap(function _callee6$(_context6) {
      while (1) switch (_context6.prev = _context6.next) {
        case 0:
          _context6.next = 2;
          return request(generateAPIPath('api/v2/customerUser/user-credential'), {
            method: 'DELETE',
            params: {
              name: name
            }
          });
        case 2:
          res = _context6.sent;
          return _context6.abrupt("return", res);
        case 4:
        case "end":
          return _context6.stop();
      }
    }, _callee6);
  }));
  return function deleteCustomerUserCredential(_x6) {
    return _ref6.apply(this, arguments);
  };
}()));
/**\r
 *\r
 * DYNAMIC ROLE APIs\r
 */
function listDynamicRoleAllSection() {
  return _listDynamicRoleAllSection.apply(this, arguments);
}
function _listDynamicRoleAllSection() {
  _listDynamicRoleAllSection = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee8() {
    var result;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee8$(_context8) {
      while (1) switch (_context8.prev = _context8.next) {
        case 0:
          _context8.prev = 0;
          _context8.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_5__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_7__/* .generateAPIPath */ .rH)("api/v2/dynamicRole/listAllSection"), {
            method: 'GET'
          });
        case 3:
          result = _context8.sent;
          return _context8.abrupt("return", result.result);
        case 7:
          _context8.prev = 7;
          _context8.t0 = _context8["catch"](0);
          console.log(_context8.t0);
          throw _context8.t0;
        case 11:
        case "end":
          return _context8.stop();
      }
    }, _callee8, null, [[0, 7]]);
  }));
  return _listDynamicRoleAllSection.apply(this, arguments);
}
function getDynamicRole() {
  return _getDynamicRole.apply(this, arguments);
}
function _getDynamicRole() {
  _getDynamicRole = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee9() {
    var result;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee9$(_context9) {
      while (1) switch (_context9.prev = _context9.next) {
        case 0:
          _context9.prev = 0;
          _context9.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_5__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_7__/* .generateAPIPath */ .rH)("api/v2/dynamicRole"), {
            method: 'GET',
            params: {
              page: 1,
              size: 100
            }
          });
        case 3:
          result = _context9.sent;
          return _context9.abrupt("return", result.result.data);
        case 7:
          _context9.prev = 7;
          _context9.t0 = _context9["catch"](0);
          console.log(_context9.t0);
          throw _context9.t0;
        case 11:
        case "end":
          return _context9.stop();
      }
    }, _callee9, null, [[0, 7]]);
  }));
  return _getDynamicRole.apply(this, arguments);
}
function createDynamicRole(_x7) {
  return _createDynamicRole.apply(this, arguments);
}
function _createDynamicRole() {
  _createDynamicRole = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee10(data) {
    var result;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee10$(_context10) {
      while (1) switch (_context10.prev = _context10.next) {
        case 0:
          _context10.prev = 0;
          _context10.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_5__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_7__/* .generateAPIPath */ .rH)("api/v2/dynamicRole"), {
            method: 'POST',
            data: data
          });
        case 3:
          result = _context10.sent;
          return _context10.abrupt("return", result.result);
        case 7:
          _context10.prev = 7;
          _context10.t0 = _context10["catch"](0);
          console.log(_context10.t0);
          throw _context10.t0;
        case 11:
        case "end":
          return _context10.stop();
      }
    }, _callee10, null, [[0, 7]]);
  }));
  return _createDynamicRole.apply(this, arguments);
}
function updateDynamicRole(_x8) {
  return _updateDynamicRole.apply(this, arguments);
}
function _updateDynamicRole() {
  _updateDynamicRole = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee11(data) {
    var result;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee11$(_context11) {
      while (1) switch (_context11.prev = _context11.next) {
        case 0:
          _context11.prev = 0;
          _context11.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_5__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_7__/* .generateAPIPath */ .rH)("api/v2/dynamicRole"), {
            method: 'PUT',
            data: data
          });
        case 3:
          result = _context11.sent;
          return _context11.abrupt("return", result.result);
        case 7:
          _context11.prev = 7;
          _context11.t0 = _context11["catch"](0);
          console.log(_context11.t0);
          throw _context11.t0;
        case 11:
        case "end":
          return _context11.stop();
      }
    }, _callee11, null, [[0, 7]]);
  }));
  return _updateDynamicRole.apply(this, arguments);
}
function removeDynamicRole(_x9) {
  return _removeDynamicRole.apply(this, arguments);
}
function _removeDynamicRole() {
  _removeDynamicRole = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee12(data) {
    var name, result;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee12$(_context12) {
      while (1) switch (_context12.prev = _context12.next) {
        case 0:
          _context12.prev = 0;
          name = data.name ? data.name : '';
          _context12.next = 4;
          return (0,_sscript__WEBPACK_IMPORTED_MODULE_6__/* .generalDelete */ .ID)('iot_dynamic_role', name);
        case 4:
          result = _context12.sent;
          return _context12.abrupt("return", result);
        case 8:
          _context12.prev = 8;
          _context12.t0 = _context12["catch"](0);
          throw _context12.t0;
        case 11:
        case "end":
          return _context12.stop();
      }
    }, _callee12, null, [[0, 8]]);
  }));
  return _removeDynamicRole.apply(this, arguments);
}//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///40063
`)},7369:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   W: function() { return /* binding */ toFirstUpperCase; },
/* harmony export */   w: function() { return /* binding */ nonAccentVietnamese; }
/* harmony export */ });
function nonAccentVietnamese(str) {
  var _str$toString;
  var _str = str === null || str === void 0 || (_str$toString = str.toString) === null || _str$toString === void 0 ? void 0 : _str$toString.call(str);
  if (typeof _str !== 'string') return str;
  _str = _str.toLowerCase();
  //     We can also use this instead of from line 11 to line 17
  //     str = str.replace(/\\u00E0|\\u00E1|\\u1EA1|\\u1EA3|\\u00E3|\\u00E2|\\u1EA7|\\u1EA5|\\u1EAD|\\u1EA9|\\u1EAB|\\u0103|\\u1EB1|\\u1EAF|\\u1EB7|\\u1EB3|\\u1EB5/g, "a");
  //     str = str.replace(/\\u00E8|\\u00E9|\\u1EB9|\\u1EBB|\\u1EBD|\\u00EA|\\u1EC1|\\u1EBF|\\u1EC7|\\u1EC3|\\u1EC5/g, "e");
  //     str = str.replace(/\\u00EC|\\u00ED|\\u1ECB|\\u1EC9|\\u0129/g, "i");
  //     str = str.replace(/\\u00F2|\\u00F3|\\u1ECD|\\u1ECF|\\u00F5|\\u00F4|\\u1ED3|\\u1ED1|\\u1ED9|\\u1ED5|\\u1ED7|\\u01A1|\\u1EDD|\\u1EDB|\\u1EE3|\\u1EDF|\\u1EE1/g, "o");
  //     str = str.replace(/\\u00F9|\\u00FA|\\u1EE5|\\u1EE7|\\u0169|\\u01B0|\\u1EEB|\\u1EE9|\\u1EF1|\\u1EED|\\u1EEF/g, "u");
  //     str = str.replace(/\\u1EF3|\\u00FD|\\u1EF5|\\u1EF7|\\u1EF9/g, "y");
  //     str = str.replace(/\\u0111/g, "d");
  _str = _str.replace(/\xE0|\xE1|\u1EA1|\u1EA3|\xE3|\xE2|\u1EA7|\u1EA5|\u1EAD|\u1EA9|\u1EAB|\u0103|\u1EB1|\u1EAF|\u1EB7|\u1EB3|\u1EB5/g, 'a');
  _str = _str.replace(/\xE8|\xE9|\u1EB9|\u1EBB|\u1EBD|\xEA|\u1EC1|\u1EBF|\u1EC7|\u1EC3|\u1EC5/g, 'e');
  _str = _str.replace(/\xEC|\xED|\u1ECB|\u1EC9|\u0129/g, 'i');
  _str = _str.replace(/\xF2|\xF3|\u1ECD|\u1ECF|\xF5|\xF4|\u1ED3|\u1ED1|\u1ED9|\u1ED5|\u1ED7|\u01A1|\u1EDD|\u1EDB|\u1EE3|\u1EDF|\u1EE1/g, 'o');
  _str = _str.replace(/\xF9|\xFA|\u1EE5|\u1EE7|\u0169|\u01B0|\u1EEB|\u1EE9|\u1EF1|\u1EED|\u1EEF/g, 'u');
  _str = _str.replace(/\u1EF3|\xFD|\u1EF5|\u1EF7|\u1EF9/g, 'y');
  _str = _str.replace(/\u0111/g, 'd');
  // Some system encode vietnamese combining accent as individual utf-8 characters
  _str = _str.replace(/\\u0300|\\u0301|\\u0303|\\u0309|\\u0323/g, ''); // Huy\u1EC1n s\u1EAFc h\u1ECFi ng\xE3 n\u1EB7ng
  _str = _str.replace(/\\u02C6|\\u0306|\\u031B/g, ''); // \xC2, \xCA, \u0102, \u01A0, \u01AF
  return _str.split(',').join('');
}
var toFirstUpperCase = function toFirstUpperCase(str) {
  if (!str) return '';
  return str.charAt(0).toUpperCase() + str.slice(1);
};//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///7369
`)}}]);
