"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[8586],{81568:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(5574);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(7837);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(45360);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(86738);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(14726);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(67294);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(85893);







var ActionPopConfirm = function ActionPopConfirm(_ref) {
  var actionCall = _ref.actionCall,
    refreshData = _ref.refreshData,
    buttonType = _ref.buttonType,
    text = _ref.text,
    danger = _ref.danger,
    size = _ref.size;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false),
    _useState2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2___default()(_useState, 2),
    loading = _useState2[0],
    setLoading = _useState2[1];
  var onConfirm = /*#__PURE__*/function () {
    var _ref2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee() {
      return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _context.prev = 0;
            setLoading(true);
            if (!actionCall) {
              _context.next = 5;
              break;
            }
            _context.next = 5;
            return actionCall();
          case 5:
            if (!refreshData) {
              _context.next = 8;
              break;
            }
            _context.next = 8;
            return refreshData();
          case 8:
            _context.next = 14;
            break;
          case 10:
            _context.prev = 10;
            _context.t0 = _context["catch"](0);
            antd__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .ZP.error(_context.t0.toString());
            console.log(_context.t0);
          case 14:
            _context.prev = 14;
            setLoading(false);
            return _context.finish(14);
          case 17:
          case "end":
            return _context.stop();
        }
      }, _callee, null, [[0, 10, 14, 17]]);
    }));
    return function onConfirm() {
      return _ref2.apply(this, arguments);
    };
  }();
  var onCancel = /*#__PURE__*/function () {
    var _ref3 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee2() {
      return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee2$(_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
          case "end":
            return _context2.stop();
        }
      }, _callee2);
    }));
    return function onCancel() {
      return _ref3.apply(this, arguments);
    };
  }();
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(antd__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Z, {
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_3__.FormattedMessage, {
      id: "action.confirm"
    }),
    description: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_3__.FormattedMessage, {
      id: "action.verify"
    }),
    onConfirm: onConfirm,
    onCancel: onCancel,
    okText: "\\u0110\\u1ED3ng \\xFD",
    cancelText: "Hu\\u1EF7",
    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(antd__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .ZP, {
      style: {
        display: 'flex',
        alignItems: 'center',
        borderRadius: 0 // Thi\u1EBFt l\u1EADp g\xF3c vu\xF4ng cho button
      },
      size: size || 'middle',
      danger: danger,
      type: buttonType,
      loading: loading,
      children: text
    })
  });
};
/* harmony default export */ __webpack_exports__.Z = (ActionPopConfirm);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///81568
`)},65573:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(29905);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(85893);


var FallbackComponent = function FallbackComponent() {
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .ZP, {
    status: "403",
    title: "Kh\\xF4ng th\\u1EC3 truy c\\u1EADp",
    subTitle: "Xin l\\u1ED7i, b\\u1EA1n kh\\xF4ng c\\xF3 quy\\u1EC1n xem n\\u1ED9i dung n\\xE0y."
    //   extra={
    //     <Button type="primary" href="/">
    //       Back Home
    //     </Button>
    //   }
  });
};
/* harmony default export */ __webpack_exports__.Z = (FallbackComponent);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNjU1NzMuanMiLCJtYXBwaW5ncyI6Ijs7QUFBOEI7QUFBQTtBQUU5QixJQUFNRyxpQkFBaUIsR0FBRyxTQUFwQkEsaUJBQWlCQSxDQUFBLEVBQVM7RUFDOUIsb0JBQ0VELHNEQUFBLENBQUNGLHNEQUFNO0lBQ0xJLE1BQU0sRUFBQyxLQUFLO0lBQ1pDLEtBQUssRUFBQyxpQ0FBb0I7SUFDMUJDLFFBQVEsRUFBQztJQUNUO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7RUFBQSxDQUNELENBQUM7QUFFTixDQUFDO0FBRUQsc0RBQWVILGlCQUFpQiIsInNvdXJjZXMiOlsid2VicGFjazovL2FudC1kZXNpZ24tcHJvLy4vc3JjL2NvbXBvbmVudHMvRmFsbGJhY2tDb250ZW50L2luZGV4LnRzeD9mZGRhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFJlc3VsdCB9IGZyb20gJ2FudGQnO1xyXG5cclxuY29uc3QgRmFsbGJhY2tDb21wb25lbnQgPSAoKSA9PiB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxSZXN1bHRcclxuICAgICAgc3RhdHVzPVwiNDAzXCJcclxuICAgICAgdGl0bGU9XCJLaMO0bmcgdGjhu4MgdHJ1eSBj4bqtcFwiXHJcbiAgICAgIHN1YlRpdGxlPVwiWGluIGzhu5dpLCBi4bqhbiBraMO0bmcgY8OzIHF1eeG7gW4geGVtIG7hu5lpIGR1bmcgbsOgeS5cIlxyXG4gICAgICAvLyAgIGV4dHJhPXtcclxuICAgICAgLy8gICAgIDxCdXR0b24gdHlwZT1cInByaW1hcnlcIiBocmVmPVwiL1wiPlxyXG4gICAgICAvLyAgICAgICBCYWNrIEhvbWVcclxuICAgICAgLy8gICAgIDwvQnV0dG9uPlxyXG4gICAgICAvLyAgIH1cclxuICAgIC8+XHJcbiAgKTtcclxufTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IEZhbGxiYWNrQ29tcG9uZW50O1xyXG4iXSwibmFtZXMiOlsiUmVzdWx0IiwianN4IiwiX2pzeCIsIkZhbGxiYWNrQ29tcG9uZW50Iiwic3RhdHVzIiwidGl0bGUiLCJzdWJUaXRsZSJdLCJzb3VyY2VSb290IjoiIn0=
//# sourceURL=webpack-internal:///65573
`)},30653:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(97857);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(5574);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectWithoutProperties_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(13769);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectWithoutProperties_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectWithoutProperties_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _utils_string__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(7369);
/* harmony import */ var _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(33983);
/* harmony import */ var _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(19054);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(96074);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(67294);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(85893);



var _excluded = ["dropdownBottom"];






var toLowerCase = function toLowerCase() {
  var input = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';
  return (0,_utils_string__WEBPACK_IMPORTED_MODULE_5__/* .nonAccentVietnamese */ .w)(input.toLowerCase().normalize('NFD').replace(/[\\u0300-\\u036f]/g, ''));
};
var FormTreeSelectSearch = function FormTreeSelectSearch(_ref) {
  var _props$fieldProps;
  var dropdownBottom = _ref.dropdownBottom,
    props = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectWithoutProperties_js__WEBPACK_IMPORTED_MODULE_2___default()(_ref, _excluded);
  // eslint-disable-next-line @typescript-eslint/no-non-null-asserted-optional-chain
  var treeData = (_props$fieldProps = props.fieldProps) === null || _props$fieldProps === void 0 ? void 0 : _props$fieldProps.treeData;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(''),
    _useState2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_1___default()(_useState, 2),
    searchValue = _useState2[0],
    setSearchValue = _useState2[1];
  var searchValueDebounce = (0,_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_6__/* .useDebounceValue */ .n)(searchValue || '', 100);
  var _treeData = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(function () {
    var loop = function loop() {
      var data = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];
      return (data || []).map(function (item) {
        var normalizedSearchValue = toLowerCase(searchValueDebounce);
        var itemTitle = (item.title || '').toString();
        var strTitle = (0,_utils_string__WEBPACK_IMPORTED_MODULE_5__/* .nonAccentVietnamese */ .w)(itemTitle);
        var index = strTitle.indexOf(normalizedSearchValue);
        var beforeStr = itemTitle.substring(0, index);
        var str = itemTitle.substring(index, index + searchValueDebounce.length);
        var afterStr = itemTitle.slice(index + searchValueDebounce.length);
        var title = searchValueDebounce === '' ? /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)("span", {
          children: itemTitle
        }) : index > -1 ? /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)("span", {
          children: [beforeStr, /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)("span", {
            style: {
              color: 'white',
              backgroundColor: 'green'
            },
            children: str
          }), afterStr]
        }) : /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)("span", {
          children: itemTitle
        });
        if (item.children) {
          return {
            title: title,
            key: item.value,
            children: loop(item.children),
            value: item.value,
            _title: itemTitle
          };
        }
        return {
          title: title,
          key: item.value,
          value: item.value,
          _title: itemTitle
        };
      });
    };
    return loop(treeData);
  }, [treeData, searchValueDebounce]);
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Z, D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default()({}, props), {}, {
    fieldProps: D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default()({}, props.fieldProps || {}), {}, {
      treeData: _treeData,
      onSearch: function onSearch(value) {
        setSearchValue(value);
      },
      filterTreeNode: function filterTreeNode(input, treeNode) {
        var treeNodeChildrenArr = treeNode.children || [];
        var normalizedInput = toLowerCase(input);
        var normalizedLabel = toLowerCase(treeNode._title);
        var childrenMatch = false;
        for (var i = 0; i < treeNodeChildrenArr.length; i++) {
          var _normalizedLabel = toLowerCase(treeNodeChildrenArr[i]._title);
          if (_normalizedLabel.includes(normalizedInput)) {
            childrenMatch = true;
            return true;
          }
        }
        if (normalizedLabel.includes(normalizedInput)) {
          return true;
        }
        return childrenMatch;
      },
      dropdownRender: !dropdownBottom ? undefined : function (menu) {
        return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)("div", {
          children: [menu, /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(antd__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .Z, {
            style: {
              margin: '4px 0'
            }
          }), dropdownBottom]
        });
      },
      showSearch: true,
      multiple: true,
      autoClearSearchValue: true,
      treeCheckable: true,
      treeDefaultExpandAll: true,
      showCheckedStrategy: 'SHOW_CHILD'
    })
  }));
};
/* harmony default export */ __webpack_exports__.Z = (FormTreeSelectSearch);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///30653
`)},40250:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(28058);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(38925);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(67294);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(85893);





var StockRepostWarning = function StockRepostWarning(_ref) {
  var type = _ref.type;
  var messages = {
    sleReCal: "L\\u01B0u \\xFD: S\\u1ED1 li\\u1EC7u t\\u1ED3n kho \\u0111ang \\u0111\\u01B0\\u1EE3c t\\xEDnh to\\xE1n l\\u1EA1i v\\xE0 c\\xF3 th\\u1EC3 ch\\u01B0a ph\\u1EA3n \\xE1nh ch\\xEDnh x\\xE1c t\\xECnh tr\\u1EA1ng hi\\u1EC7n t\\u1EA1i. \\n            Vui l\\xF2ng l\\xE0m m\\u1EDBi trang sau v\\xE0i gi\\xE2y \\u0111\\u1EC3 xem d\\u1EEF li\\u1EC7u m\\u1EDBi nh\\u1EA5t.",
    fullRecal: "L\\u01B0u \\xFD: S\\u1ED1 li\\u1EC7u t\\u1ED3n kho \\u0111\\u01B0\\u1EE3c t\\xEDnh l\\u1EA1i v\\xE0o 00h00 h\\xE0ng ng\\xE0y v\\xE0 c\\xF3 th\\u1EC3 ch\\u01B0a ph\\u1EA3n \\xE1nh ch\\xEDnh x\\xE1c t\\xECnh tr\\u1EA1ng hi\\u1EC7n t\\u1EA1i."
  };
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z, {
    message: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)("div", {
      style: {
        display: 'flex',
        alignItems: 'center',
        gap: '8px'
      },
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z, {}), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)("span", {
        children: messages[type]
      })]
    }),
    type: "warning",
    showIcon: false,
    style: {
      marginBottom: 24
    }
  });
};
/* harmony default export */ __webpack_exports__.Z = (StockRepostWarning);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///40250
`)},59238:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return /* binding */ InventoryManagementV3; }
});

// EXTERNAL MODULE: ./node_modules/@ant-design/pro-layout/es/components/PageContainer/index.js + 18 modules
var PageContainer = __webpack_require__(6110);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/slicedToArray.js
var slicedToArray = __webpack_require__(5574);
var slicedToArray_default = /*#__PURE__*/__webpack_require__.n(slicedToArray);
// EXTERNAL MODULE: ./src/components/FallbackContent/index.tsx
var FallbackContent = __webpack_require__(65573);
// EXTERNAL MODULE: ./src/.umi-production/exports.ts + 34 modules
var _umi_production_exports = __webpack_require__(7837);
// EXTERNAL MODULE: ./node_modules/antd/es/row/index.js
var row = __webpack_require__(71230);
// EXTERNAL MODULE: ./node_modules/antd/es/col/index.js
var col = __webpack_require__(15746);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/regeneratorRuntime.js
var regeneratorRuntime = __webpack_require__(15009);
var regeneratorRuntime_default = /*#__PURE__*/__webpack_require__.n(regeneratorRuntime);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/objectSpread2.js
var objectSpread2 = __webpack_require__(97857);
var objectSpread2_default = /*#__PURE__*/__webpack_require__.n(objectSpread2);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/defineProperty.js
var defineProperty = __webpack_require__(9783);
var defineProperty_default = /*#__PURE__*/__webpack_require__.n(defineProperty);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/toConsumableArray.js
var toConsumableArray = __webpack_require__(19632);
var toConsumableArray_default = /*#__PURE__*/__webpack_require__.n(toConsumableArray);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(99289);
var asyncToGenerator_default = /*#__PURE__*/__webpack_require__.n(asyncToGenerator);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/taggedTemplateLiteral.js
var taggedTemplateLiteral = __webpack_require__(68400);
var taggedTemplateLiteral_default = /*#__PURE__*/__webpack_require__.n(taggedTemplateLiteral);
// EXTERNAL MODULE: ./src/services/InventoryManagementV3/Item-group.ts
var Item_group = __webpack_require__(19903);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/ReloadOutlined.js
var ReloadOutlined = __webpack_require__(43471);
// EXTERNAL MODULE: ./node_modules/@emotion/css/dist/emotion-css.esm.js + 14 modules
var emotion_css_esm = __webpack_require__(29978);
// EXTERNAL MODULE: ./node_modules/antd/es/message/index.js
var message = __webpack_require__(45360);
// EXTERNAL MODULE: ./node_modules/antd/es/card/index.js + 3 modules
var card = __webpack_require__(76216);
// EXTERNAL MODULE: ./node_modules/antd/es/space/index.js + 2 modules
var space = __webpack_require__(42075);
// EXTERNAL MODULE: ./node_modules/antd/es/button/index.js + 9 modules
var es_button = __webpack_require__(14726);
// EXTERNAL MODULE: ./node_modules/antd/es/list/index.js + 3 modules
var list = __webpack_require__(2487);
// EXTERNAL MODULE: ./src/pages/InventoryManagementV3/ProductManagement/Category/components/CreateCategory.tsx + 1 modules
var CreateCategory = __webpack_require__(80049);
// EXTERNAL MODULE: ./src/components/ActionPopConfirm/index.tsx
var ActionPopConfirm = __webpack_require__(81568);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/DeleteOutlined.js
var DeleteOutlined = __webpack_require__(82061);
// EXTERNAL MODULE: ./node_modules/antd/es/form/index.js + 18 modules
var es_form = __webpack_require__(98138);
// EXTERNAL MODULE: ./node_modules/react/jsx-runtime.js
var jsx_runtime = __webpack_require__(85893);
;// CONCATENATED MODULE: ./src/pages/InventoryManagementV3/ProductManagement/Category/components/DeleteCategory.tsx







var Item = es_form/* default */.Z.Item;
var DeleteCategory = function DeleteCategory(params) {
  var removeData = /*#__PURE__*/function () {
    var _ref = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
      return regeneratorRuntime_default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _context.prev = 0;
            _context.next = 3;
            return (0,Item_group/* deleteItemGroupV3 */.DB)({
              name: params.value.name,
              label: params.value.label
            });
          case 3:
            _context.next = 8;
            break;
          case 5:
            _context.prev = 5;
            _context.t0 = _context["catch"](0);
            message/* default */.ZP.error(_context.t0.toString());
          case 8:
          case "end":
            return _context.stop();
        }
      }, _callee, null, [[0, 5]]);
    }));
    return function removeData() {
      return _ref.apply(this, arguments);
    };
  }();
  return /*#__PURE__*/(0,jsx_runtime.jsx)(ActionPopConfirm/* default */.Z, {
    actionCall: removeData,
    refreshData: params.refreshFnc,
    text: /*#__PURE__*/(0,jsx_runtime.jsx)(DeleteOutlined/* default */.Z, {})
    // //buttonType={'dashed'}
    ,
    danger: true,
    size: "small"
  });
};
/* harmony default export */ var components_DeleteCategory = (DeleteCategory);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/EditOutlined.js
var EditOutlined = __webpack_require__(47389);
// EXTERNAL MODULE: ./node_modules/antd/es/modal/index.js + 1 modules
var modal = __webpack_require__(85576);
// EXTERNAL MODULE: ./node_modules/antd/es/input/index.js + 4 modules
var input = __webpack_require__(96365);
;// CONCATENATED MODULE: ./src/pages/InventoryManagementV3/ProductManagement/Category/components/UpdateCategory.tsx











var UpdateCategory_Item = es_form/* default */.Z.Item;
var UpdateCategory = function UpdateCategory(params) {
  var _useState = (0,react.useState)(false),
    _useState2 = slicedToArray_default()(_useState, 2),
    loading = _useState2[0],
    setLoading = _useState2[1];
  var _useState3 = (0,react.useState)(false),
    _useState4 = slicedToArray_default()(_useState3, 2),
    isOpen = _useState4[0],
    setOpen = _useState4[1];
  var _Form$useForm = es_form/* default */.Z.useForm(),
    _Form$useForm2 = slicedToArray_default()(_Form$useForm, 1),
    form = _Form$useForm2[0];
  var showModal = function showModal() {
    form.setFieldsValue(params.value);
    setOpen(true);
  };
  var hideModal = function hideModal() {
    setOpen(false);
  };
  var handleOk = function handleOk() {
    form.submit();
  };
  var handleCancel = function handleCancel() {
    hideModal();
    form.resetFields();
  };
  return /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
    children: [/*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
      style: {
        display: 'flex',
        alignItems: 'center',
        borderRadius: 0 // Thi\u1EBFt l\u1EADp g\xF3c vu\xF4ng cho button
      },
      size: "small"
      // //type="dashed"
      ,
      onClick: showModal,
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(EditOutlined/* default */.Z, {})
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(modal/* default */.Z, {
      title: "Ch\\u1EC9nh s\\u1EEDa danh m\\u1EE5c v\\u1EADt t\\u01B0",
      open: isOpen,
      onOk: handleOk,
      onCancel: handleCancel,
      confirmLoading: loading,
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_form/* default */.Z, {
        layout: "horizontal",
        labelCol: {
          span: 24
        },
        labelAlign: "left",
        form: form,
        onFinish: ( /*#__PURE__*/function () {
          var _ref = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(value) {
            var result;
            return regeneratorRuntime_default()().wrap(function _callee$(_context) {
              while (1) switch (_context.prev = _context.next) {
                case 0:
                  _context.prev = 0;
                  _context.next = 3;
                  return (0,Item_group/* updateItemGroupV3 */.jb)(objectSpread2_default()({
                    name: params.value.name
                  }, value));
                case 3:
                  result = _context.sent;
                  form.resetFields();
                  hideModal();
                  message/* default */.ZP.success('Success!');
                  if (!params.refreshFnc) {
                    _context.next = 10;
                    break;
                  }
                  _context.next = 10;
                  return params.refreshFnc();
                case 10:
                  _context.next = 15;
                  break;
                case 12:
                  _context.prev = 12;
                  _context.t0 = _context["catch"](0);
                  message/* default */.ZP.error(_context.t0.toString());
                case 15:
                  _context.prev = 15;
                  setLoading(false);
                  return _context.finish(15);
                case 18:
                case "end":
                  return _context.stop();
              }
            }, _callee, null, [[0, 12, 15, 18]]);
          }));
          return function (_x) {
            return _ref.apply(this, arguments);
          };
        }()),
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(row/* default */.Z, {
          gutter: 5,
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
            className: "gutter-row",
            md: 12,
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(UpdateCategory_Item, {
              label: "T\\xEAn lo\\u1EA1i v\\u1EADt t\\u01B0",
              labelCol: {
                span: 24
              },
              rules: [{
                required: true,
                message: 'B\u1EAFt bu\u1ED9c \u0111i\u1EC1n'
              }],
              name: "label",
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(input/* default */.Z, {})
            })
          })
        })
      })
    })]
  });
};
/* harmony default export */ var components_UpdateCategory = (UpdateCategory);
;// CONCATENATED MODULE: ./src/pages/InventoryManagementV3/ProductManagement/Category/index.tsx







var _templateObject;











var listItemClass = (0,emotion_css_esm/* css */.iv)(_templateObject || (_templateObject = taggedTemplateLiteral_default()(["\\n  .action-buttons {\\n    position: absolute;\\n    right: 24px;\\n    display: none;\\n  }\\n  &:hover .action-buttons {\\n    display: flex;\\n  }\\n  &.selected {\\n    background-color: rgb(209, 250, 229); // Tailwind bg-emerald-100\\n  }\\n  .ant-list-item-meta {\\n    flex: 1;\\n  }\\n"])));
var isAllKey = Symbol('all');
var CategoryManagement = function CategoryManagement(_ref) {
  var onItemClick = _ref.onItemClick;
  var _useState = (0,react.useState)(false),
    _useState2 = slicedToArray_default()(_useState, 2),
    loading = _useState2[0],
    setLoading = _useState2[1];
  var _useState3 = (0,react.useState)([]),
    _useState4 = slicedToArray_default()(_useState3, 2),
    data = _useState4[0],
    setData = _useState4[1];
  var _useState5 = (0,react.useState)(null),
    _useState6 = slicedToArray_default()(_useState5, 2),
    selectedItem = _useState6[0],
    setSelectedItem = _useState6[1];
  var _useState7 = (0,react.useState)({
      current: 1,
      pageSize: 20,
      total: 0
    }),
    _useState8 = slicedToArray_default()(_useState7, 2),
    pagination = _useState8[0],
    setPagination = _useState8[1];
  var fetchData = /*#__PURE__*/function () {
    var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(params) {
      var _result$pagination, result, listData;
      return regeneratorRuntime_default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _context.prev = 0;
            setLoading(true);
            _context.next = 4;
            return (0,Item_group/* getItemGroupV3 */._D)({
              page: params.current,
              size: params.pageSize
            });
          case 4:
            result = _context.sent;
            listData = [defineProperty_default()({
              label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
                id: "common.all"
              }),
              name: 'all'
            }, isAllKey, true)].concat(toConsumableArray_default()(result.data)).filter(function (item) {
              return item.label;
            });
            setData(listData);
            setPagination(objectSpread2_default()(objectSpread2_default()({}, pagination), {}, {
              total: ((_result$pagination = result.pagination) === null || _result$pagination === void 0 ? void 0 : _result$pagination.totalElements) || 0
            }));
            _context.next = 13;
            break;
          case 10:
            _context.prev = 10;
            _context.t0 = _context["catch"](0);
            message/* default */.ZP.error('C\xF3 l\u1ED7i x\u1EA3y ra, vui l\xF2ng th\u1EED l\u1EA1i');
          case 13:
            _context.prev = 13;
            setLoading(false);
            return _context.finish(13);
          case 16:
          case "end":
            return _context.stop();
        }
      }, _callee, null, [[0, 10, 13, 16]]);
    }));
    return function fetchData(_x) {
      return _ref2.apply(this, arguments);
    };
  }();
  (0,react.useEffect)(function () {
    fetchData({
      current: 1,
      pageSize: 20
    });
  }, []);
  var handleReload = function handleReload() {
    fetchData({
      current: pagination.current,
      pageSize: pagination.pageSize
    });
  };
  var access = (0,_umi_production_exports.useAccess)();
  var canCreateCategory = access.canCreateInCategoryManagement();
  var canUpdateCategory = access.canUpdateInCategoryManagement();
  var canDeleteCategory = access.canDeleteInCategoryManagement();
  var handleItemClick = function handleItemClick(item) {
    if (item[isAllKey]) {
      onItemClick();
      setSelectedItem('all');
    } else {
      onItemClick(item.name);
      setSelectedItem(item.name);
    }
  };
  return /*#__PURE__*/(0,jsx_runtime.jsx)(card/* default */.Z, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "category.material-management.category_type"
    }),
    extra: /*#__PURE__*/(0,jsx_runtime.jsxs)(space/* default */.Z, {
      children: [canCreateCategory && /*#__PURE__*/(0,jsx_runtime.jsx)(CreateCategory/* default */.Z, {
        refreshFnc: handleReload
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
        onClick: handleReload,
        icon: /*#__PURE__*/(0,jsx_runtime.jsx)(ReloadOutlined/* default */.Z, {})
      })]
    }),
    styles: {
      body: {
        padding: 4
      }
    },
    children: /*#__PURE__*/(0,jsx_runtime.jsx)(list/* default */.Z, {
      loading: loading,
      dataSource: data,
      rowKey: "name",
      pagination: {
        current: pagination.current,
        pageSize: pagination.pageSize,
        total: pagination.total,
        onChange: function onChange(page, pageSize) {
          fetchData({
            current: page,
            pageSize: pageSize
          });
        }
      },
      renderItem: function renderItem(item) {
        return /*#__PURE__*/(0,jsx_runtime.jsxs)(list/* default */.Z.Item, {
          className: "".concat(listItemClass, " ").concat(selectedItem === item.name ? 'selected' : ''),
          onClick: function onClick() {
            return handleItemClick(item);
          },
          style: {
            cursor: 'pointer',
            padding: '12px 24px',
            position: 'relative'
          },
          children: [/*#__PURE__*/(0,jsx_runtime.jsx)(list/* default */.Z.Item.Meta, {
            title: item.label,
            description: item[isAllKey] ? 'L\u1ECDc theo t\u1EA5t c\u1EA3 lo\u1EA1i v\u1EADt t\u01B0' : "L\\u1ECDc theo lo\\u1EA1i ".concat(item.label)
          }), !item[isAllKey] && /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
            className: "action-buttons",
            children: /*#__PURE__*/(0,jsx_runtime.jsxs)(space/* default */.Z, {
              size: 4,
              children: [canUpdateCategory && /*#__PURE__*/(0,jsx_runtime.jsx)(components_UpdateCategory, {
                refreshFnc: handleReload,
                value: item
              }, 'edit' + item.name), canDeleteCategory && /*#__PURE__*/(0,jsx_runtime.jsx)(components_DeleteCategory, {
                refreshFnc: handleReload,
                value: item
              }, 'remove' + item.name)]
            })
          })]
        });
      }
    })
  });
};
/* harmony default export */ var Category = (CategoryManagement);
// EXTERNAL MODULE: ./src/common/contanst/constanst.ts
var constanst = __webpack_require__(86604);
// EXTERNAL MODULE: ./src/services/InventoryManagementV3/product-item.ts
var product_item = __webpack_require__(58642);
// EXTERNAL MODULE: ./src/services/utils.ts
var utils = __webpack_require__(467);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-table/es/Table.js + 76 modules
var Table = __webpack_require__(4894);
// EXTERNAL MODULE: ./node_modules/numeral/numeral.js
var numeral = __webpack_require__(92077);
var numeral_default = /*#__PURE__*/__webpack_require__.n(numeral);
// EXTERNAL MODULE: ./src/pages/InventoryManagementV3/ProductManagement/Products/CreateProduct-v2/index.tsx + 3 modules
var CreateProduct_v2 = __webpack_require__(53328);
// EXTERNAL MODULE: ./node_modules/antd/es/app/index.js + 1 modules
var app = __webpack_require__(31418);
;// CONCATENATED MODULE: ./src/pages/InventoryManagementV3/ProductManagement/Products/hooks/useDeleteProductItemV3.ts



var useDeleteProductItemV3 = function useDeleteProductItemV3() {
  var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},
    _onSuccess = _ref.onSuccess;
  var _App$useApp = app/* default */.Z.useApp(),
    message = _App$useApp.message;
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  return (0,_umi_production_exports.useRequest)(product_item/* deleteProductItemV3 */.fu, {
    manual: true,
    onSuccess: function onSuccess() {
      message.success(formatMessage({
        id: 'common.success'
      }));
      _onSuccess === null || _onSuccess === void 0 || _onSuccess();
    },
    onError: function onError(e, params) {
      message.error(formatMessage({
        id: 'common.error'
      }));
    }
  });
};
;// CONCATENATED MODULE: ./src/pages/InventoryManagementV3/ProductManagement/Products/DeleteProduct/index.tsx





// import useDeleteProject from '../hooks/useDeleleteProject';

var DeleteProduct = function DeleteProduct(_ref) {
  var name = _ref.name,
    onDeleteSuccess = _ref.onDeleteSuccess,
    label = _ref.label;
  var _App$useApp = app/* default */.Z.useApp(),
    modal = _App$useApp.modal;
  var _useDeleteProductItem = useDeleteProductItemV3(),
    run = _useDeleteProductItem.run;
  return /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
    style: {
      // display: 'flex',
      alignItems: 'center',
      borderRadius: 0 // Thi\u1EBFt l\u1EADp g\xF3c vu\xF4ng cho button
    }
    // size="small"
    ,
    icon: /*#__PURE__*/(0,jsx_runtime.jsx)(DeleteOutlined/* default */.Z, {}),
    danger: true,
    onClick: function onClick(e) {
      e.stopPropagation();
      e.preventDefault();
      modal.confirm({
        title: "B\\u1EA1n c\\xF3 ch\\u1EAFc ch\\u1EAFc mu\\u1ED1n x\\xF3a h\\xE0ng ho\\xE1 ".concat(label, "?"),
        content: 'H\xE0nh \u0111\u1ED9ng n\xE0y kh\xF4ng th\u1EC3 ho\xE0n t\xE1c!',
        okButtonProps: {
          danger: true
        },
        onOk: function () {
          var _onOk = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
            return regeneratorRuntime_default()().wrap(function _callee$(_context) {
              while (1) switch (_context.prev = _context.next) {
                case 0:
                  _context.prev = 0;
                  _context.next = 3;
                  return run({
                    name: name,
                    label: label
                  });
                case 3:
                  onDeleteSuccess === null || onDeleteSuccess === void 0 || onDeleteSuccess();
                  return _context.abrupt("return", true);
                case 7:
                  _context.prev = 7;
                  _context.t0 = _context["catch"](0);
                  return _context.abrupt("return", false);
                case 10:
                case "end":
                  return _context.stop();
              }
            }, _callee, null, [[0, 7]]);
          }));
          function onOk() {
            return _onOk.apply(this, arguments);
          }
          return onOk;
        }()
      });
    }
  });
};
/* harmony default export */ var Products_DeleteProduct = (DeleteProduct);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/layouts/ProForm/index.js + 3 modules
var ProForm = __webpack_require__(34994);
// EXTERNAL MODULE: ./node_modules/antd/es/tabs/index.js + 30 modules
var tabs = __webpack_require__(48096);
// EXTERNAL MODULE: ./src/HOC/withTriggerFormModal/index.tsx
var withTriggerFormModal = __webpack_require__(28591);
// EXTERNAL MODULE: ./src/pages/WarehouseManagementV3/Inventory/InventoryListTable/components/InventoryDetail.tsx
var InventoryDetail = __webpack_require__(15311);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/layouts/ModalForm/index.js
var ModalForm = __webpack_require__(37476);
;// CONCATENATED MODULE: ./src/pages/InventoryManagementV3/ProductManagement/Products/hooks/useGetDetailsProductItemV3.ts



var useGetDetailsProductItemV3 = function useGetDetailsProductItemV3() {
  var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},
    _onSuccess = _ref.onSuccess;
  var _App$useApp = app/* default */.Z.useApp(),
    message = _App$useApp.message;
  var intl = (0,_umi_production_exports.useIntl)();
  return (0,_umi_production_exports.useRequest)(product_item/* getDetailsProductItemV3 */.eX, {
    manual: true,
    onSuccess: function onSuccess(data) {
      _onSuccess === null || _onSuccess === void 0 || _onSuccess(data);
    },
    onError: function onError(e, params) {
      message.error(intl.formatMessage({
        id: 'common.error'
      }));
    }
  });
};
;// CONCATENATED MODULE: ./src/pages/InventoryManagementV3/ProductManagement/Products/hooks/useUpdateProductItemV3.ts



var useUpdateProductItemV3 = function useUpdateProductItemV3() {
  var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},
    _onSuccess = _ref.onSuccess;
  var _App$useApp = app/* default */.Z.useApp(),
    message = _App$useApp.message;
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  return (0,_umi_production_exports.useRequest)(product_item/* updateProductItemV3 */.T1, {
    manual: true,
    onSuccess: function onSuccess() {
      message.success(formatMessage({
        id: 'common.success'
      }));
      _onSuccess === null || _onSuccess === void 0 || _onSuccess();
    },
    onError: function onError(e, params) {
      message.error(formatMessage({
        id: 'common.error'
      }));
    }
  });
};
// EXTERNAL MODULE: ./src/pages/WarehouseManagementV3/_utils.ts
var _utils = __webpack_require__(25770);
// EXTERNAL MODULE: ./src/utils/array.ts
var array = __webpack_require__(19073);
;// CONCATENATED MODULE: ./src/pages/InventoryManagementV3/ProductManagement/Products/EditProduct-v2/components/CustomerData.tsx










var TableCustomerData = function TableCustomerData(_ref) {
  var entity = _ref.entity;
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  var columns = [{
    title: formatMessage({
      id: 'common.customer_code'
    }),
    dataIndex: 'customer_name',
    width: 80
    // valueType: 'text',
  }, {
    title: formatMessage({
      id: 'common.customer_name'
    }),
    dataIndex: 'customer_label',
    width: 80
  }, {
    title: formatMessage({
      id: 'common.total_out_qty'
    }),
    dataIndex: 'total_qty',
    hideInSearch: true,
    align: 'center',
    render: function render(total_qty) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)("span", {
        style: {
          color: 'green'
        },
        children: "".concat(numeral_default()(total_qty).format('0,0.00'))
      });
    },
    width: 80
  }, {
    title: formatMessage({
      id: 'common.total_price'
    }),
    dataIndex: 'total_price',
    render: function render(qty_after_transaction) {
      return numeral_default()(qty_after_transaction).format('0,0.00');
    },
    hideInSearch: true,
    align: 'center',
    width: 80
  }];
  return /*#__PURE__*/(0,jsx_runtime.jsx)(Table/* default */.Z
  // scroll={{
  //   x: 1400,
  // }}
  , {
    pagination: {
      // pageSize: 5,
      pageSizeOptions: [10, 20, 50, 100],
      showSizeChanger: true
    },
    columns: (0,_utils/* addDefaultConfigColumns */.m)(columns),
    size: "small",
    search: {
      labelWidth: 'auto',
      defaultCollapsed: true
    },
    rowKey: function rowKey(record) {
      return record.customer_id;
    },
    request: ( /*#__PURE__*/function () {
      var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(params, sort, filter) {
        var res;
        return regeneratorRuntime_default()().wrap(function _callee$(_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              _context.next = 2;
              return (0,product_item/* getItemCustomerVouchersSum */.Zk)({
                page: params.current,
                size: params.pageSize,
                name: entity.name,
                customer_label: params.customer_label,
                customer_name: params.customer_name
              });
            case 2:
              res = _context.sent;
              return _context.abrupt("return", {
                data: (0,array/* sortArrayByObjectKey */.G3)({
                  arr: res.data,
                  sort: sort
                }),
                total: res.pagination.totalElements
              });
            case 4:
            case "end":
              return _context.stop();
          }
        }, _callee);
      }));
      return function (_x, _x2, _x3) {
        return _ref2.apply(this, arguments);
      };
    }())
  });
};
var CustomerDataModal = function CustomerDataModal(_ref3) {
  var entity = _ref3.entity,
    isModalOpen = _ref3.isModalOpen,
    setIsModalOpen = _ref3.setIsModalOpen;
  return /*#__PURE__*/_jsx(Modal, {
    open: isModalOpen,
    title: /*#__PURE__*/_jsx(FormattedMessage, {
      id: "common.detail"
    }),
    onCancel: function onCancel() {
      setIsModalOpen(false);
    },
    footer: [],
    width: 1600,
    children: /*#__PURE__*/_jsx(TableCustomerData, {
      entity: entity
    })
  });
};
/* harmony default export */ var CustomerData = ((/* unused pure expression or super */ null && (CustomerDataModal)));
// EXTERNAL MODULE: ./src/components/FormUploadsPreviewable/index.tsx
var FormUploadsPreviewable = __webpack_require__(97679);
// EXTERNAL MODULE: ./src/services/InventoryManagementV3/uom.ts
var uom = __webpack_require__(94966);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/TextArea/index.js
var TextArea = __webpack_require__(90672);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/Text/index.js
var Text = __webpack_require__(5966);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/Select/index.js
var Select = __webpack_require__(64317);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/Digit/index.js
var Digit = __webpack_require__(31199);
// EXTERNAL MODULE: ./node_modules/antd/es/divider/index.js + 1 modules
var divider = __webpack_require__(96074);
// EXTERNAL MODULE: ./src/pages/InventoryManagementV3/ProductManagement/UOM/UOMList/components/Create.tsx + 1 modules
var Create = __webpack_require__(60489);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/BarcodeOutlined.js + 1 modules
var BarcodeOutlined = __webpack_require__(28950);
// EXTERNAL MODULE: ./node_modules/antd/es/typography/index.js + 18 modules
var typography = __webpack_require__(25514);
// EXTERNAL MODULE: ./node_modules/react-barcode/lib/react-barcode.js
var react_barcode = __webpack_require__(78566);
var react_barcode_default = /*#__PURE__*/__webpack_require__.n(react_barcode);
;// CONCATENATED MODULE: ./src/pages/InventoryManagementV3/ProductManagement/Products/BARModal.tsx








var BARModal = function BARModal(_ref) {
  var children = _ref.children,
    currentItem = _ref.data;
  var _useState = (0,react.useState)(false),
    _useState2 = slicedToArray_default()(_useState, 2),
    isModalOpenBarcode = _useState2[0],
    setIsModalOpenBarcode = _useState2[1];
  var downloadBarCode = function downloadBarCode(category_id) {
    var _document$getElementB;
    var canvas = (_document$getElementB = document.getElementById("mybarcode-".concat(category_id))) === null || _document$getElementB === void 0 ? void 0 : _document$getElementB.querySelector('canvas');
    console.log('canvas barcode', document.getElementById("mybarcode-".concat(category_id)));
    if (canvas) {
      var url = canvas.toDataURL();
      var a = document.createElement('a');
      a.download = 'BarCode.png';
      a.href = url;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    }
  };
  return /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
    children: [/*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
      icon: /*#__PURE__*/(0,jsx_runtime.jsx)(BarcodeOutlined/* default */.Z, {
        style: {
          fontSize: '48px'
        }
      }),
      style: {
        padding: '48px 48px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      },
      onClick: function onClick() {
        return setIsModalOpenBarcode(true);
      }
    }), isModalOpenBarcode && /*#__PURE__*/(0,jsx_runtime.jsx)(modal/* default */.Z, {
      open: isModalOpenBarcode,
      onCancel: function onCancel() {
        return setIsModalOpenBarcode(false);
      },
      footer: [],
      title: "M\\xE3 v\\u1EA1ch c\\u1EE7a ".concat(currentItem === null || currentItem === void 0 ? void 0 : currentItem.label),
      children: /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        id: "mybarcode-".concat(currentItem === null || currentItem === void 0 ? void 0 : currentItem.item_name),
        children: /*#__PURE__*/(0,jsx_runtime.jsxs)(space/* default */.Z, {
          direction: "vertical",
          align: "center",
          children: [/*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
            type: "link",
            onClick: function onClick() {
              return downloadBarCode(currentItem === null || currentItem === void 0 ? void 0 : currentItem.item_name);
            },
            children: currentItem && /*#__PURE__*/(0,jsx_runtime.jsx)((react_barcode_default()), {
              value: "category,".concat(currentItem.item_name)
            })
          }), /*#__PURE__*/(0,jsx_runtime.jsx)(typography/* default */.Z.Text, {
            italic: true,
            children: "Nh\\u1EA5n v\\xE0o m\\xE3 v\\u1EA1ch \\u0111\\u1EC3 t\\u1EA3i \\u1EA3nh v\\u1EC1 m\\xE1y"
          })]
        })
      })
    })]
  });
};
/* harmony default export */ var Products_BARModal = (BARModal);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/QrcodeOutlined.js + 1 modules
var QrcodeOutlined = __webpack_require__(77516);
// EXTERNAL MODULE: ./node_modules/antd/es/qr-code/index.js + 3 modules
var qr_code = __webpack_require__(10397);
;// CONCATENATED MODULE: ./src/pages/InventoryManagementV3/ProductManagement/Products/QRModal.tsx







var QRModal = function QRModal(_ref) {
  var children = _ref.children,
    currentItem = _ref.data;
  var _useState = (0,react.useState)(false),
    _useState2 = slicedToArray_default()(_useState, 2),
    isModalOpenQR = _useState2[0],
    setIsModalOpenQR = _useState2[1];
  var downloadQRCode = function downloadQRCode(category_id) {
    var _document$getElementB;
    var canvas = (_document$getElementB = document.getElementById("myqrcode-".concat(category_id))) === null || _document$getElementB === void 0 ? void 0 : _document$getElementB.querySelector('canvas');
    console.log('canvas qr', document.getElementById("myqrcode-".concat(category_id)));
    if (canvas) {
      var url = canvas.toDataURL();
      var a = document.createElement('a');
      a.download = 'QRCode.png';
      a.href = url;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    }
  };
  return /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
    children: [/*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
      icon: /*#__PURE__*/(0,jsx_runtime.jsx)(QrcodeOutlined/* default */.Z, {
        style: {
          fontSize: '48px'
        }
      }),
      style: {
        padding: '48px 48px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      },
      onClick: function onClick() {
        return setIsModalOpenQR(true);
      }
    }), isModalOpenQR && /*#__PURE__*/(0,jsx_runtime.jsx)(modal/* default */.Z, {
      open: isModalOpenQR,
      onCancel: function onCancel() {
        return setIsModalOpenQR(false);
      },
      footer: [],
      title: "M\\xE3 QR c\\u1EE7a ".concat(currentItem === null || currentItem === void 0 ? void 0 : currentItem.label),
      children: /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        id: "myqrcode-".concat(currentItem === null || currentItem === void 0 ? void 0 : currentItem.item_name),
        children: /*#__PURE__*/(0,jsx_runtime.jsxs)(space/* default */.Z, {
          direction: "vertical",
          align: "center",
          children: [/*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
            type: "link",
            onClick: function onClick() {
              return downloadQRCode(currentItem === null || currentItem === void 0 ? void 0 : currentItem.item_name);
            },
            children: currentItem && /*#__PURE__*/(0,jsx_runtime.jsx)(qr_code/* default */.Z, {
              size: 400,
              value: "category,".concat(currentItem.item_name),
              bgColor: "#fff"
            })
          }), /*#__PURE__*/(0,jsx_runtime.jsx)(typography/* default */.Z.Text, {
            italic: true,
            children: "Nh\\u1EA5n v\\xE0o QR \\u0111\\u1EC3 t\\u1EA3i \\u1EA3nh v\\u1EC1 m\\xE1y"
          })]
        })
      })
    })]
  });
};
/* harmony default export */ var Products_QRModal = (QRModal);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/ExclamationCircleOutlined.js + 1 modules
var ExclamationCircleOutlined = __webpack_require__(11475);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-table/es/components/EditableTable/index.js + 1 modules
var EditableTable = __webpack_require__(88280);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-utils/es/nanoid/index.js
var nanoid = __webpack_require__(75661);
// EXTERNAL MODULE: ./node_modules/antd/es/tooltip/index.js + 3 modules
var tooltip = __webpack_require__(83062);
;// CONCATENATED MODULE: ./src/pages/InventoryManagementV3/ProductManagement/Products/EditProduct-v2/UnitConversion.tsx












var UnitConversion = function UnitConversion(_ref) {
  var children = _ref.children;
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  var editorFormRef = (0,react.useRef)();
  var nameKey = 'uoms';
  var form = ProForm/* ProForm */.A.useFormInstance();
  var intl = (0,_umi_production_exports.useIntl)();
  var columns = (0,react.useMemo)(function () {
    return [{
      title: formatMessage({
        id: 'common.unit'
      }),
      dataIndex: 'uom',
      valueType: 'select',
      request: function () {
        var _request = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(params) {
          var filters, res;
          return regeneratorRuntime_default()().wrap(function _callee$(_context) {
            while (1) switch (_context.prev = _context.next) {
              case 0:
                filters = [];
                _context.next = 3;
                return (0,uom/* getUOM_v3 */.kD)({
                  page: 1,
                  size: constanst/* DEFAULT_PAGE_SIZE_ALL */.YY,
                  filters: filters
                });
              case 3:
                res = _context.sent;
                return _context.abrupt("return", res.data.map(function (item) {
                  return {
                    label: "".concat(item.uom_name),
                    value: item.name
                  };
                }));
              case 5:
              case "end":
                return _context.stop();
            }
          }, _callee);
        }));
        function request(_x) {
          return _request.apply(this, arguments);
        }
        return request;
      }(),
      formItemProps: {
        rules: [{
          required: true
        }]
      },
      width: 40
    }, {
      title: /*#__PURE__*/(0,jsx_runtime.jsxs)(tooltip/* default */.Z, {
        color: constanst/* COLOR_HEX */.wE.GREEN_TOOLTIP,
        title: intl.formatMessage({
          id: 'T\u1EC9 l\u1EC7 = \u0110\u01A1n v\u1ECB chuy\u1EC3n \u0111\u1ED5i / \u0110\u01A1n v\u1ECB g\u1ED1c'
        }),
        children: [/*#__PURE__*/(0,jsx_runtime.jsxs)("span", {
          children: [intl.formatMessage({
            id: 'category.material-management.conversion_factor'
          }), ' ']
        }), /*#__PURE__*/(0,jsx_runtime.jsx)(ExclamationCircleOutlined/* default */.Z, {})]
      }, constanst/* COLOR_HEX */.wE.GREEN_TOOLTIP),
      dataIndex: 'conversion_factor',
      valueType: 'digit',
      width: 40,
      fieldProps: {
        precision: 9
      },
      render: function render(text) {
        return /*#__PURE__*/(0,jsx_runtime.jsx)("span", {
          children: text
        });
      },
      formItemProps: {
        rules: [{
          required: true
        }]
      }
    },
    // {
    //   title: '\u9898\u578B',
    //   key: 'type',
    //   dataIndex: 'type',
    //   valueType: 'select',
    //   valueEnum: {
    //     multiple: { text: '\u591A\u9009\u9898', status: 'Default' },
    //     radio: { text: '\u5355\u9009\u9898', status: 'Warning' },
    //     vacant: {
    //       text: '\u586B\u7A7A\u9898',
    //       status: 'Error',
    //     },
    //     judge: {
    //       text: '\u5224\u65AD\u9898',
    //       status: 'Success',
    //     },
    //   },
    // },
    {
      // title: intl.formatMessage({ id: 'common.action' }),
      valueType: 'option',
      width: 20,
      render: function render(text, record, _, action) {
        return [/*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
          icon: /*#__PURE__*/(0,jsx_runtime.jsx)(EditOutlined/* default */.Z, {}),
          onClick: function onClick() {
            var _action$startEditable;
            action === null || action === void 0 || (_action$startEditable = action.startEditable) === null || _action$startEditable === void 0 || _action$startEditable.call(action, record.name);
          },
          size: "small"
        }, "editable"), /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
          size: "small",
          danger: true,
          onClick: function onClick() {
            var tableDataSource = form === null || form === void 0 ? void 0 : form.getFieldValue(nameKey);
            form === null || form === void 0 || form.setFieldsValue(defineProperty_default()({}, nameKey, tableDataSource.filter(function (item) {
              return item.name !== record.name;
            })));
          },
          icon: /*#__PURE__*/(0,jsx_runtime.jsx)(DeleteOutlined/* default */.Z, {})
        }, "delete")];
      }
    }];
  }, [form]);
  return /*#__PURE__*/(0,jsx_runtime.jsx)(EditableTable/* default */.Z, {
    editableFormRef: editorFormRef,
    headerTitle: intl.formatMessage({
      id: 'common.unit-conversion-table'
    }),
    rowKey: 'name',
    recordCreatorProps: {
      // newRecordType: 'dataSource',
      position: 'bottom',
      record: function record() {
        return {
          name: (0,nanoid/* nanoid */.x)(),
          isAddNew: true,
          doctype: 'UOM Conversion Detail',
          uom: undefined,
          conversion_factor: 0,
          parent: form.getFieldValue('name'),
          parentfield: 'uoms'
        };
      }
    },
    editable: {
      type: 'multiple',
      actionRender: function actionRender(row, config, defaultDom) {
        return [
        /*#__PURE__*/
        // <Button
        //   key="save"
        //   onClick={() => {
        //     // config?.onSave?.(row.name);
        //     const originRow = row;
        //     config?.onSave?.(row, row);
        //   }}
        // >
        //   {formatMessage({ id: 'common.save' })}
        // </Button>,
        (0,jsx_runtime.jsx)("a", {
          onClick: function onClick() {
            var _config$cancelEditabl;
            config === null || config === void 0 || (_config$cancelEditabl = config.cancelEditable) === null || _config$cancelEditabl === void 0 || _config$cancelEditabl.call(config, row.name);
          },
          children: formatMessage({
            id: 'common.done'
          })
        }, "cancel")];
      }
    },
    name: nameKey,
    columns: columns
  });
};
/* harmony default export */ var EditProduct_v2_UnitConversion = (UnitConversion);
;// CONCATENATED MODULE: ./src/pages/InventoryManagementV3/ProductManagement/Products/EditProduct-v2/components/InforTab.tsx



















var InfoTab = function InfoTab(_ref) {
  var form = _ref.form,
    data = _ref.data;
  var _useState = (0,react.useState)(0),
    _useState2 = slicedToArray_default()(_useState, 2),
    refreshKey = _useState2[0],
    setRefreshKey = _useState2[1];
  var refreshOptions = function refreshOptions() {
    console.log('refreshOptions called');
    setRefreshKey(function (prevKey) {
      return prevKey + 1;
    });
  };
  var intl = (0,_umi_production_exports.useIntl)();
  return /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
    gutter: 16,
    justify: "space-between",
    align: "bottom",
    children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
      span: 8,
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(FormUploadsPreviewable/* default */.Z, {
        label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
          id: 'common.form.image'
        }),
        fileLimit: 1,
        formItemName: 'image',
        initialImages: form.getFieldValue('image')
      })
    }), /*#__PURE__*/(0,jsx_runtime.jsxs)(col/* default */.Z, {
      span: 8,
      style: {
        padding: '10px'
      },
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        style: {
          paddingBottom: '10px'
        },
        children: "Bar code"
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(Products_BARModal, {
        data: data
      })]
    }), /*#__PURE__*/(0,jsx_runtime.jsxs)(col/* default */.Z, {
      span: 8,
      style: {
        padding: '10px'
      },
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        style: {
          paddingBottom: '10px'
        },
        children: "QR code"
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(Products_QRModal, {
        data: data
      })]
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
      span: 24,
      children: /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
        gutter: 16,
        justify: "space-between",
        align: "bottom",
        children: [/*#__PURE__*/(0,jsx_runtime.jsxs)(col/* default */.Z, {
          span: 8,
          children: [/*#__PURE__*/(0,jsx_runtime.jsx)(Text/* default */.Z, {
            width: 'sm',
            name: "item_name",
            label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
              id: "common.item_name"
            }),
            rules: [{
              required: true,
              message: 'B\u1EAFt bu\u1ED9c \u0111i\u1EC1n'
            }]
          }), /*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
            label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
              id: "category.material-management.unit"
            }),
            name: "stock_uom",
            width: 'sm',
            rules: [{
              required: true
            }],
            showSearch: true,
            fieldProps: {
              dropdownRender: function dropdownRender(menu) {
                return /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
                  children: [menu, /*#__PURE__*/(0,jsx_runtime.jsx)(divider/* default */.Z, {
                    style: {
                      margin: '4px 0'
                    }
                  }), /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
                    style: {
                      display: 'flex',
                      flexWrap: 'nowrap',
                      padding: 8
                    },
                    children: /*#__PURE__*/(0,jsx_runtime.jsx)(Create/* default */.Z, {
                      buttonType: "link",
                      refreshFnc: refreshOptions
                    })
                  })]
                });
              }
            },
            request: ( /*#__PURE__*/function () {
              var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(params) {
                var filters, res;
                return regeneratorRuntime_default()().wrap(function _callee$(_context) {
                  while (1) switch (_context.prev = _context.next) {
                    case 0:
                      filters = [];
                      _context.next = 3;
                      return (0,uom/* getUOM_v3 */.kD)({
                        page: 1,
                        size: constanst/* DEFAULT_PAGE_SIZE_ALL */.YY,
                        filters: filters,
                        order_by: 'uom_name ASC'
                      });
                    case 3:
                      res = _context.sent;
                      return _context.abrupt("return", res.data.map(function (item) {
                        return {
                          label: "".concat(item.uom_name),
                          value: item.name
                        };
                      }));
                    case 5:
                    case "end":
                      return _context.stop();
                  }
                }, _callee);
              }));
              return function (_x) {
                return _ref2.apply(this, arguments);
              };
            }())
          }, refreshKey + 'uom')]
        }), /*#__PURE__*/(0,jsx_runtime.jsxs)(col/* default */.Z, {
          span: 8,
          children: [/*#__PURE__*/(0,jsx_runtime.jsx)(Text/* default */.Z, {
            width: 'sm',
            name: "label",
            label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
              id: "category.material-management.category_name"
            }),
            rules: [{
              required: true,
              message: 'B\u1EAFt bu\u1ED9c \u0111i\u1EC1n'
            }]
          }), /*#__PURE__*/(0,jsx_runtime.jsx)(Digit/* default */.Z, {
            width: 'sm',
            name: "standard_rate",
            min: 1
            // rules={[
            //   {
            //     required: true,
            //   },
            // ]}
            ,
            label: intl.formatMessage({
              id: 'common.default-price'
            }),
            fieldProps: {
              formatter: function formatter(value) {
                return "".concat(value).replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');
              },
              parser: function parser(value) {
                return value.replace(/\\$\\s?|(,*)/g, '');
              }
            }
          })]
        }), /*#__PURE__*/(0,jsx_runtime.jsxs)(col/* default */.Z, {
          span: 8,
          children: [/*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
            width: 'sm',
            rules: [{
              required: true,
              message: 'B\u1EAFt bu\u1ED9c \u0111i\u1EC1n'
            }],
            allowClear: true,
            label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
              id: "category.material-management.category_type"
            }),
            name: "item_group",
            showSearch: true,
            fieldProps: {
              dropdownRender: function dropdownRender(menu) {
                return /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
                  children: [menu, /*#__PURE__*/(0,jsx_runtime.jsx)(divider/* default */.Z, {
                    style: {
                      margin: '4px 0'
                    }
                  }), /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
                    style: {
                      display: 'flex',
                      flexWrap: 'nowrap',
                      padding: 8
                    },
                    children: /*#__PURE__*/(0,jsx_runtime.jsx)(CreateCategory/* default */.Z, {
                      refreshFnc: refreshOptions,
                      inLineButton: true
                    })
                  })]
                });
              }
            },
            request: ( /*#__PURE__*/function () {
              var _ref3 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2(params) {
                var filters, res;
                return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
                  while (1) switch (_context2.prev = _context2.next) {
                    case 0:
                      filters = []; // if (params.keyWords) {
                      //   filters.push(['','label', 'like', \`%\${params.keyWords}%\`]);
                      // }
                      _context2.next = 3;
                      return (0,Item_group/* getItemGroupV3 */._D)({
                        page: 1,
                        size: constanst/* DEFAULT_PAGE_SIZE_ALL */.YY,
                        filters: filters,
                        order_by: 'label ASC'
                      });
                    case 3:
                      res = _context2.sent;
                      return _context2.abrupt("return", res.data.map(function (item) {
                        return {
                          label: item.label,
                          value: item.name
                        };
                      }));
                    case 5:
                    case "end":
                      return _context2.stop();
                  }
                }, _callee2);
              }));
              return function (_x2) {
                return _ref3.apply(this, arguments);
              };
            }())
          }, refreshKey + 'item_group'), /*#__PURE__*/(0,jsx_runtime.jsx)(Digit/* default */.Z, {
            width: 'sm',
            name: "valuation_rate",
            min: 1
            // rules={[
            //   {
            //     required: true,
            //   },
            // ]}
            ,
            label: intl.formatMessage({
              id: 'common.default-purchase-price'
            }),
            fieldProps: {
              formatter: function formatter(value) {
                return "".concat(value).replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');
              },
              parser: function parser(value) {
                return value.replace(/\\$\\s?|(,*)/g, '');
              }
            }
          })]
        })]
      })
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
      span: 8,
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(Digit/* default */.Z, {
        width: 'sm',
        label: intl.formatMessage({
          id: 'common.max_stock_level'
        }),
        name: "max_stock_level",
        fieldProps: {
          formatter: function formatter(value) {
            return "".concat(value).replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');
          },
          parser: function parser(value) {
            return value.replace(/\\$\\s?|(,*)/g, '');
          }
        }
      })
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
      span: 8,
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(Digit/* default */.Z, {
        width: 'sm',
        label: intl.formatMessage({
          id: 'common.min_stock_level'
        }),
        name: "min_stock_level",
        fieldProps: {
          formatter: function formatter(value) {
            return "".concat(value).replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');
          },
          parser: function parser(value) {
            return value.replace(/\\$\\s?|(,*)/g, '');
          }
        }
      })
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
      span: 8,
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(TextArea/* default */.Z, {
        name: "description",
        label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
          id: "common.description"
        })
      })
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(EditProduct_v2_UnitConversion, {}, refreshKey)]
  });
};
/* harmony default export */ var InforTab = (InfoTab);
;// CONCATENATED MODULE: ./src/pages/InventoryManagementV3/ProductManagement/Products/EditProduct-v2/components/SupplierData.tsx










var TableSupplierData = function TableSupplierData(_ref) {
  var entity = _ref.entity;
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  var columns = [{
    title: formatMessage({
      id: 'common.supplier_code'
    }),
    dataIndex: 'supplier_name',
    width: 80
    // valueType: 'text',
  }, {
    title: formatMessage({
      id: 'common.supplier_name'
    }),
    dataIndex: 'supplier_label',
    width: 80
  }, {
    title: formatMessage({
      id: 'common.total_in_qty'
    }),
    dataIndex: 'total_qty',
    hideInSearch: true,
    align: 'center',
    render: function render(total_qty) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)("span", {
        style: {
          color: 'green'
        },
        children: "".concat(numeral_default()(total_qty).format('0,0.00'))
      });
    },
    width: 80
  }, {
    title: formatMessage({
      id: 'common.total_price'
    }),
    dataIndex: 'total_price',
    render: function render(qty_after_transaction) {
      return numeral_default()(qty_after_transaction).format('0,0.00');
    },
    hideInSearch: true,
    align: 'center',
    width: 80
  }];
  return /*#__PURE__*/(0,jsx_runtime.jsx)(Table/* default */.Z
  // scroll={{
  //   x: 1400,
  // }}
  , {
    pagination: {
      // pageSize: 5,
      pageSizeOptions: [10, 20, 50, 100],
      showSizeChanger: true
    },
    columns: (0,_utils/* addDefaultConfigColumns */.m)(columns),
    size: "small",
    search: {
      labelWidth: 'auto',
      defaultCollapsed: true
    },
    rowKey: function rowKey(record) {
      return record.supplier_id;
    },
    request: ( /*#__PURE__*/function () {
      var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(params, sort, filter) {
        var res;
        return regeneratorRuntime_default()().wrap(function _callee$(_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              _context.next = 2;
              return (0,product_item/* getItemSupplierVouchersSum */.oD)({
                page: params.current,
                size: params.pageSize,
                name: entity.name,
                supplier_label: params.supplier_label,
                supplier_name: params.supplier_name
              });
            case 2:
              res = _context.sent;
              return _context.abrupt("return", {
                data: (0,array/* sortArrayByObjectKey */.G3)({
                  arr: res.data,
                  sort: sort
                }),
                total: res.pagination.totalElements
              });
            case 4:
            case "end":
              return _context.stop();
          }
        }, _callee);
      }));
      return function (_x, _x2, _x3) {
        return _ref2.apply(this, arguments);
      };
    }())
  });
};
var SupplierDataModal = function SupplierDataModal(_ref3) {
  var entity = _ref3.entity,
    isModalOpen = _ref3.isModalOpen,
    setIsModalOpen = _ref3.setIsModalOpen;
  return /*#__PURE__*/_jsx(Modal, {
    open: isModalOpen,
    title: /*#__PURE__*/_jsx(FormattedMessage, {
      id: "common.detail"
    }),
    onCancel: function onCancel() {
      setIsModalOpen(false);
    },
    footer: [],
    width: 1600,
    children: /*#__PURE__*/_jsx(TableSupplierData, {
      entity: entity
    })
  });
};
/* harmony default export */ var SupplierData = ((/* unused pure expression or super */ null && (SupplierDataModal)));
;// CONCATENATED MODULE: ./src/pages/InventoryManagementV3/ProductManagement/Products/EditProduct-v2/index.tsx




/* eslint-disable no-useless-escape */














var ContentForm = function ContentForm(_ref) {
  var open = _ref.open,
    onOpenChange = _ref.onOpenChange,
    onSuccess = _ref.onSuccess,
    modalProps = _ref.modalProps,
    trigger = _ref.trigger;
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  var _ProForm$useForm = ProForm/* ProForm */.A.useForm(),
    _ProForm$useForm2 = slicedToArray_default()(_ProForm$useForm, 1),
    form = _ProForm$useForm2[0];
  var _App$useApp = app/* default */.Z.useApp(),
    message = _App$useApp.message;
  var _useGetDetailsProduct = useGetDetailsProductItemV3({
      onSuccess: function onSuccess(data) {
        form.setFieldsValue(objectSpread2_default()({}, data));
      }
    }),
    loading = _useGetDetailsProduct.loading,
    getDetail = _useGetDetailsProduct.run,
    data = _useGetDetailsProduct.data;
  (0,react.useEffect)(function () {
    if (modalProps !== null && modalProps !== void 0 && modalProps.id) {
      getDetail({
        name: modalProps.id
      });
    }
  }, [modalProps === null || modalProps === void 0 ? void 0 : modalProps.id]);
  var _useUpdateProductItem = useUpdateProductItemV3({
      onSuccess: onSuccess
    }),
    updateProductItemV3 = _useUpdateProductItem.run;
  var intl = (0,_umi_production_exports.useIntl)();
  return /*#__PURE__*/(0,jsx_runtime.jsx)(ModalForm/* ModalForm */.Y, {
    loading: loading || undefined,
    form: form,
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: 'category.material-management.add_category'
    }),
    name: "'category.material-management.update_category'",
    width: 1000,
    onFinish: ( /*#__PURE__*/function () {
      var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(values) {
        var dataReq;
        return regeneratorRuntime_default()().wrap(function _callee$(_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              if (data) {
                _context.next = 3;
                break;
              }
              formatMessage({
                id: 'common.error'
              });
              return _context.abrupt("return", false);
            case 3:
              //in values.uoms array, if object has isAddNew = true, delete field name from object
              values.uoms = values.uoms.map(function (item) {
                if (item.isAddNew) {
                  delete item.name;
                  delete item.isAddNew;
                }
                return item;
              });
              dataReq = {
                name: data.name,
                image: values.image,
                stock_uom: values.stock_uom,
                standard_rate: values.standard_rate,
                //gia ban mac dinh
                label: values.label,
                item_name: values.item_name,
                // "iot_customer": "09cb98f0-e0f5-11ec-b13b-4376e531a14a", //optional
                item_group: values.item_group,
                valuation_rate: values.valuation_rate,
                //gia mua mac dinh
                uoms: values.uoms,
                description: values.description,
                max_stock_level: values.max_stock_level,
                min_stock_level: values.min_stock_level
              };
              _context.next = 7;
              return updateProductItemV3(dataReq);
            case 7:
              onSuccess === null || onSuccess === void 0 || onSuccess();
              return _context.abrupt("return", true);
            case 9:
            case "end":
              return _context.stop();
          }
        }, _callee);
      }));
      return function (_x) {
        return _ref2.apply(this, arguments);
      };
    }()),
    open: open,
    onOpenChange: onOpenChange,
    trigger: trigger,
    children: /*#__PURE__*/(0,jsx_runtime.jsxs)(tabs/* default */.Z, {
      defaultActiveKey: "1",
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)(tabs/* default */.Z.TabPane, {
        tab: intl.formatMessage({
          id: 'common.info'
        }),
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(InforTab, {
          form: form,
          data: data
        })
      }, "1"), /*#__PURE__*/(0,jsx_runtime.jsx)(tabs/* default */.Z.TabPane, {
        tab: intl.formatMessage({
          id: 'common.attribute'
        })
      }, "2"), /*#__PURE__*/(0,jsx_runtime.jsx)(tabs/* default */.Z.TabPane, {
        tab: intl.formatMessage({
          id: 'common.supplier'
        }),
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(TableSupplierData, {
          entity: data
        }, (data === null || data === void 0 ? void 0 : data.item_code) || (data === null || data === void 0 ? void 0 : data.name))
      }, "3"), /*#__PURE__*/(0,jsx_runtime.jsx)(tabs/* default */.Z.TabPane, {
        tab: intl.formatMessage({
          id: 'common.customer'
        }),
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(TableCustomerData, {
          entity: data
        }, (data === null || data === void 0 ? void 0 : data.item_code) || (data === null || data === void 0 ? void 0 : data.name))
      }, "4"), /*#__PURE__*/(0,jsx_runtime.jsx)(tabs/* default */.Z.TabPane, {
        tab: intl.formatMessage({
          id: 'common.inventory-voucher'
        }),
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(InventoryDetail/* TableInventoryDetail */.x, {
          entity: data
        }, (data === null || data === void 0 ? void 0 : data.item_code) || (data === null || data === void 0 ? void 0 : data.name))
      }, "5")]
    })
  });
};
var UpdateProduct = (0,withTriggerFormModal/* default */.Z)({
  defaultTrigger: function defaultTrigger(_ref3) {
    var changeOpen = _ref3.changeOpen,
      disabled = _ref3.disabled;
    return /*#__PURE__*/(0,jsx_runtime.jsx)("a", {
      href: "#",
      onClick: function onClick(e) {
        e.preventDefault();
        if (!disabled) {
          changeOpen(true);
        }
      }
    });
  },
  contentRender: ContentForm
});
/* harmony default export */ var EditProduct_v2 = (UpdateProduct);
;// CONCATENATED MODULE: ./src/pages/InventoryManagementV3/ProductManagement/Products/index.tsx















// import CreateItem from './Components/CreateItem';
// import DeleteItem from './Components/DeleteItem';
// import UpdateItem from './Components/UpdateItem';
// import UnitAndPacking from './UnitAndPacking';



var ProductsTable = function ProductsTable(_ref) {
  var categoryName = _ref.categoryName;
  var _useState = (0,react.useState)(false),
    _useState2 = slicedToArray_default()(_useState, 2),
    isModalOpen = _useState2[0],
    setModalOpen = _useState2[1];
  var _useState3 = (0,react.useState)(undefined),
    _useState4 = slicedToArray_default()(_useState3, 2),
    currentItemId = _useState4[0],
    setCurrentItemId = _useState4[1];
  var tableRef = (0,react.useRef)();
  var reloadTable = /*#__PURE__*/function () {
    var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
      var _tableRef$current;
      return regeneratorRuntime_default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            (_tableRef$current = tableRef.current) === null || _tableRef$current === void 0 || _tableRef$current.reload();
          case 1:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }));
    return function reloadTable() {
      return _ref2.apply(this, arguments);
    };
  }();
  (0,react.useEffect)(function () {
    reloadTable();
  }, [categoryName]);
  var handleLabelClick = function handleLabelClick(id) {
    setCurrentItemId(id);
    setModalOpen(true);
  };
  var access = (0,_umi_production_exports.useAccess)();
  var canCreateCategory = access.canCreateInCategoryManagement();
  var canUpdateCategory = access.canUpdateInCategoryManagement();
  var canDeleteCategory = access.canDeleteInCategoryManagement();
  var _toolBarRender = [];
  if (canCreateCategory) {
    _toolBarRender.push(
    /*#__PURE__*/
    // <UnitAndPacking refreshFnc={reloadTable} key={'unit'} />,
    (0,jsx_runtime.jsx)(es_button/* default */.ZP, {
      onClick: function onClick() {
        _umi_production_exports.history.push('/inventory-management-v3/uom');
      },
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
        id: "common.uom",
        defaultMessage: "UOM"
      })
    }, 'uom'), /*#__PURE__*/(0,jsx_runtime.jsx)(CreateProduct_v2/* default */.Z, {
      onSuccess: reloadTable
    }, "create"));
  }
  var columns = (0,react.useMemo)(function () {
    return [{
      title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
        id: "category.material-management.category_name"
      }),
      dataIndex: 'label',
      width: 200,
      render: function render(dom, entity) {
        return /*#__PURE__*/(0,jsx_runtime.jsx)("a", {
          onClick: function onClick() {
            return handleLabelClick(entity.name);
          },
          children: entity.label
        });
      },
      sorter: true,
      fixed: 'left'
    }, {
      title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
        id: "common.item_name"
      }),
      dataIndex: 'item_name',
      width: 150,
      sorter: true
    }, {
      title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
        id: "common.form.image"
      }),
      dataIndex: 'image',
      search: false,
      sorter: true,
      width: 100,
      render: function render(dom, entity) {
        return entity.image ? /*#__PURE__*/(0,jsx_runtime.jsx)("img", {
          width: '70px',
          src: (0,utils/* generateAPIPath */.rH)('api/v2/file/download?file_url=' + entity.image)
        }) : '';
      }
      // fixed: 'left',
    }, {
      title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
        id: "category.material-management.category_type"
      }),
      dataIndex: 'item_group_label',
      width: 150,
      search: false,
      sorter: true
    }, {
      title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
        id: "category.material-management.unit"
      }),
      dataIndex: 'uom_label',
      width: 100,
      search: false,
      sorter: true
    }, {
      title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
        id: "common.valuation_rate"
      }),
      dataIndex: 'valuation_rate',
      width: 120,
      render: function render(dom, entity) {
        return /*#__PURE__*/(0,jsx_runtime.jsx)(jsx_runtime.Fragment, {
          children: numeral_default()(entity.valuation_rate).format('0,0')
        });
      },
      search: false,
      sorter: true
    }, {
      title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
        id: "common.standard_rate"
      }),
      dataIndex: 'standard_rate',
      width: 120,
      render: function render(dom, entity) {
        return /*#__PURE__*/(0,jsx_runtime.jsx)(jsx_runtime.Fragment, {
          children: numeral_default()(entity.standard_rate).format('0,0')
        });
      },
      search: false,
      sorter: true
    }, {
      title: 'Action',
      dataIndex: 'name',
      search: false,
      width: 100,
      fixed: 'right',
      render: function render(dom, entity) {
        return /*#__PURE__*/(0,jsx_runtime.jsx)(space/* default */.Z, {
          children: canDeleteCategory && /*#__PURE__*/(0,jsx_runtime.jsx)(Products_DeleteProduct, {
            onDeleteSuccess: reloadTable,
            name: entity.name,
            label: entity.label || ''
          })
        });
      }
    }];
  }, []);
  var _useModel = (0,_umi_production_exports.useModel)('@@initialState'),
    setInitialState = _useModel.setInitialState,
    initialState = _useModel.initialState;
  (0,react.useEffect)(function () {
    setInitialState(objectSpread2_default()(objectSpread2_default()({}, initialState), {}, {
      collapsed: true
    }));
  }, []);
  return /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
    children: [/*#__PURE__*/(0,jsx_runtime.jsx)(EditProduct_v2, {
      open: isModalOpen,
      onOpenChange: setModalOpen,
      onSuccess: reloadTable,
      modalProps: {
        id: currentItemId
      }
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(Table/* default */.Z, {
      scroll: {
        x: 960
      },
      size: "small",
      actionRef: tableRef,
      rowKey: "name",
      search: {
        labelWidth: 'auto'
      },
      request: ( /*#__PURE__*/function () {
        var _ref3 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2(params, sort, filter) {
          var moreFilter, paramsReq, res;
          return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
            while (1) switch (_context2.prev = _context2.next) {
              case 0:
                moreFilter = [];
                if (categoryName) {
                  moreFilter.push([constanst/* DOCTYPE_ERP */.lH.Item, 'item_group', '=', categoryName]);
                }
                _context2.prev = 2;
                paramsReq = (0,utils/* getParamsReqTable */.wh)({
                  doc_name: constanst/* DOCTYPE_ERP */.lH.Item,
                  tableReqParams: {
                    params: params,
                    sort: sort,
                    filter: filter
                  },
                  concatFilter: moreFilter
                });
                _context2.next = 6;
                return (0,product_item/* getProductItemV3 */.yI)(paramsReq);
              case 6:
                res = _context2.sent;
                return _context2.abrupt("return", {
                  data: res.data,
                  total: res.pagination.totalElements
                });
              case 10:
                _context2.prev = 10;
                _context2.t0 = _context2["catch"](2);
                return _context2.abrupt("return", {
                  success: false
                });
              case 13:
              case "end":
                return _context2.stop();
            }
          }, _callee2, null, [[2, 10]]);
        }));
        return function (_x, _x2, _x3) {
          return _ref3.apply(this, arguments);
        };
      }()),
      columns: columns,
      headerTitle: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
        id: 'category.material-management.category_list'
      })
      // rowSelection={{}}
      // tableAlertOptionRender={({ selectedRowKeys, selectedRows, onCleanSelected }) => {
      //   return <></>;
      // }}
      ,
      toolBarRender: function toolBarRender() {
        return _toolBarRender;
      },
      pagination: {
        defaultPageSize: 20,
        showSizeChanger: true,
        pageSizeOptions: ['20', '50', '100']
      }
    })]
  });
};
/* harmony default export */ var Products = (ProductsTable);
;// CONCATENATED MODULE: ./src/pages/InventoryManagementV3/ProductManagement/index.tsx









var ProductsManagement = function ProductsManagement(_ref) {
  var children = _ref.children;
  var access = (0,_umi_production_exports.useAccess)();
  var _useState = (0,react.useState)(''),
    _useState2 = slicedToArray_default()(_useState, 2),
    categoryName = _useState2[0],
    setCategoryName = _useState2[1];
  var onLeftClick = function onLeftClick(newLabel) {
    setCategoryName(newLabel);
  };
  return /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.Access, {
    accessible: access.canAccessPageCategoryManagement(),
    fallback: /*#__PURE__*/(0,jsx_runtime.jsx)(FallbackContent/* default */.Z, {}),
    children: /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
      gutter: [5, 5],
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        md: 6,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(Category, {
          onItemClick: onLeftClick
        })
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        md: 18,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(Products, {
          categoryName: categoryName
        })
      })]
    })
  });
};
/* harmony default export */ var ProductManagement = (ProductsManagement);
;// CONCATENATED MODULE: ./src/pages/InventoryManagementV3/index.tsx



var Index = function Index(_ref) {
  var children = _ref.children;
  return /*#__PURE__*/(0,jsx_runtime.jsx)(PageContainer/* PageContainer */._z, {
    children: /*#__PURE__*/(0,jsx_runtime.jsx)(ProductManagement, {})
  });
};
/* harmony default export */ var InventoryManagementV3 = (Index);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///59238
`)},15311:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   x: function() { return /* binding */ TableInventoryDetail; }
/* harmony export */ });
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(97857);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(5574);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _components_StockRepostWarning__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(40250);
/* harmony import */ var _services_cropManager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(77890);
/* harmony import */ var _services_InventoryManagementV3_product_item__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(58642);
/* harmony import */ var _services_stock_warehouse__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(18327);
/* harmony import */ var _utils_array__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(19073);
/* harmony import */ var _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(4894);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(7837);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(47676);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(66309);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(85576);
/* harmony import */ var numeral__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(92077);
/* harmony import */ var numeral__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(numeral__WEBPACK_IMPORTED_MODULE_10__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(67294);
/* harmony import */ var _ViewDetail__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(43370);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(85893);


















var TableInventoryDetail = function TableInventoryDetail(_ref) {
  var entity = _ref.entity;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_11__.useState)(false),
    _useState2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_3___default()(_useState, 2),
    hasRepostingJobs = _useState2[0],
    setHasRepostingJobs = _useState2[1];
  var _useIntl = (0,_umijs_max__WEBPACK_IMPORTED_MODULE_9__.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  var columns = [{
    title: formatMessage({
      id: 'common.voucher_code'
    }),
    dataIndex: 'voucher_no',
    width: 90,
    // valueType: 'text',
    hideInSearch: true,
    sorter: function sorter(a, b) {
      return a.voucher_no.localeCompare(b.voucher_no);
    },
    render: function render(__dom, entity, index, action, schema) {
      var voucherNo = entity.voucher_no;
      var voucherType = entity.voucher_type;
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_13__.jsx)(_ViewDetail__WEBPACK_IMPORTED_MODULE_12__/* ["default"] */ .Z, {
        voucherNo: voucherNo,
        voucherType: voucherType
      }, voucherNo);
    } // sorter: {
    //   multiple: 1,
    // },
    // sortDirections: ['ascend', 'descend', 'ascend'],
  }, {
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_13__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_9__.FormattedMessage, {
      id: "common.date"
    }),
    dataIndex: 'posting_date',
    valueType: 'date',
    width: 80,
    sorter: function sorter(a, b) {
      return new Date(a.posting_date).getTime() - new Date(b.posting_date).getTime();
    },
    renderFormItem: function renderFormItem(schema, config, form, action) {
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_13__.jsx)(antd__WEBPACK_IMPORTED_MODULE_14__["default"].RangePicker, {});
    } // sorter: {
    //   multiple: 2,
    // },
    // sortDirections: ['ascend', 'descend', 'ascend'],
  }, {
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_13__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_9__.FormattedMessage, {
      id: "common.time"
    }),
    dataIndex: 'posting_time',
    valueType: 'time',
    hideInSearch: true,
    width: 80
    // sorter: {
    //   multiple: 3,
    // },
    // sortDirections: ['ascend', 'descend', 'ascend'],
  },
  // {
  //   title: <FormattedMessage id="common.date" />,
  //   dataIndex: 'date',
  //   valueType: 'dateTime',
  //   width: 80,
  //   hideInSearch: true,
  //   align: 'center',
  //   render: (text: any, record, index, action) => formatDateDefault(record.date),
  // },
  // {
  //   title: <FormattedMessage id="common.item_label" />,
  //   dataIndex: 'item_label',
  //   width: 80,
  // },
  // {
  //   title: <FormattedMessage id="common.category" />,
  //   dataIndex: 'item_group',
  //   width: 80,
  // },
  // {
  //   title: <FormattedMessage id="common.supplier" />,
  //   dataIndex: 'supplier_label',
  //   width: 80,
  //   hideInSearch: true,
  //   align: 'center',
  // },
  // {
  //   title: <FormattedMessage id="common.customer" />,
  //   dataIndex: 'customer_label',
  //   width: 80,
  //   hideInSearch: true,
  //   align: 'center',
  // },
  {
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_13__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_9__.FormattedMessage, {
      id: "common.customer/supplier"
    }),
    // dataIndex: 'supplier_customer',
    width: 160,
    hideInSearch: true,
    align: 'center',
    sorter: function sorter(a, b) {
      // Extract the label for comparison
      var labelA = a.supplier_label !== '-' ? a.supplier_label : a.customer_label;
      var labelB = b.supplier_label !== '-' ? b.supplier_label : b.customer_label;

      // Use localeCompare for string comparison
      // return labelA!.localeCompare(labelB!);
      //only sort if both are not '-'
      if (labelA && labelB && labelA !== '-' && labelB !== '-') {
        return labelA.localeCompare(labelB);
      } else return 0;
    },
    render: function render(_, record) {
      if (record.supplier_label !== '-') {
        return record.supplier_label;
      }
      if (record.customer_label !== '-') {
        return record.customer_label;
      }
      return '-';
    }
  }, {
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_13__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_9__.FormattedMessage, {
      id: "common.type"
    }),
    dataIndex: 'voucher_type',
    width: 40,
    hideInSearch: true,
    align: 'center',
    sorter: function sorter(a, b) {
      return a.voucher_type.localeCompare(b.voucher_type);
    },
    render: function render(dom, entity, index, action, schema) {
      if (dom === 'Purchase Receipt') {
        return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_13__.jsx)(antd__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .Z, {
          style: {
            maxWidth: '100%'
          },
          color: "green",
          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_13__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_9__.FormattedMessage, {
            id: "common.import_voucher"
          })
        });
      } else if (dom === 'Delivery Note') {
        return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_13__.jsx)(antd__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .Z, {
          style: {
            maxWidth: '100%'
          },
          color: "blue",
          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_13__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_9__.FormattedMessage, {
            id: "common.export_voucher"
          })
        });
      } else if (dom === 'Stock Reconciliation') {
        return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_13__.jsx)(antd__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .Z, {
          style: {
            maxWidth: '100%'
          },
          color: "orange",
          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_13__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_9__.FormattedMessage, {
            id: "common.stock_reconciliation"
          })
        });
      } else if (dom === 'Stock Entry' && entity.purpose === 'Material Issue') {
        return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_13__.jsx)(antd__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .Z, {
          style: {
            maxWidth: '100%'
          },
          color: "volcano",
          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_13__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_9__.FormattedMessage, {
            id: "common.material_issue"
          })
        });
      } else if (dom === 'Stock Entry' && entity.purpose === 'Material Receipt') {
        return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_13__.jsx)(antd__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .Z, {
          style: {
            maxWidth: '100%'
          },
          color: "purple",
          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_13__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_9__.FormattedMessage, {
            id: "common.material_receipt"
          })
        });
      } else if (dom === 'Stock Entry' && entity.purpose === 'Material Transfer') {
        return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_13__.jsx)(antd__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .Z, {
          style: {
            maxWidth: '100%'
          },
          color: "cyan",
          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_13__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_9__.FormattedMessage, {
            id: "common.material-transfer"
          })
        });
      }
    }
  }, {
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_13__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_9__.FormattedMessage, {
      id: "common.transaction_qty"
    }),
    dataIndex: 'actual_qty',
    hideInSearch: true,
    align: 'center',
    render: function render(actual_qty) {
      var parsedQty = parseFloat(actual_qty);
      if (actual_qty && parsedQty > 0) {
        return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_13__.jsx)("span", {
          style: {
            color: 'green'
          },
          children: "+".concat(numeral__WEBPACK_IMPORTED_MODULE_10___default()(actual_qty).format('0,0.00'))
        });
      } else if (actual_qty && parsedQty < 0) {
        return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_13__.jsx)("span", {
          style: {
            color: 'red'
          },
          children: numeral__WEBPACK_IMPORTED_MODULE_10___default()(actual_qty).format('0,0.00')
        });
      }
    },
    width: 80
  }, {
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_13__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_9__.FormattedMessage, {
      id: "common.qty_after_transaction"
    }),
    dataIndex: 'qty_after_transaction',
    render: function render(qty_after_transaction) {
      return numeral__WEBPACK_IMPORTED_MODULE_10___default()(qty_after_transaction).format('0,0.00');
    },
    hideInSearch: true,
    align: 'center',
    width: 80
  }, {
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_13__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_9__.FormattedMessage, {
      id: "common.unit"
    }),
    dataIndex: 'uom_name',
    hideInSearch: true,
    align: 'center',
    width: 80
  }, {
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_13__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_9__.FormattedMessage, {
      id: "common.warehouse"
    }),
    dataIndex: 'warehouse',
    valueType: 'select',
    // render: (warehouse, record) => {
    //   // Assuming record has a warehouse_label property
    //   return record.warehouse_label;
    // },
    // hideInSearch: true,
    request: function () {
      var _request = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee() {
        var res;
        return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee$(_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              _context.next = 2;
              return (0,_services_stock_warehouse__WEBPACK_IMPORTED_MODULE_7__/* .getWarehouseList */ .Aq)({
                or_filters: JSON.stringify([['Warehouse', 'name', '=', 'Work In Progress - V']])
              });
            case 2:
              res = _context.sent;
              return _context.abrupt("return", res.data.map(function (item) {
                return {
                  label: item.label,
                  value: item.name
                };
              }));
            case 4:
            case "end":
              return _context.stop();
          }
        }, _callee);
      }));
      function request() {
        return _request.apply(this, arguments);
      }
      return request;
    }(),
    fieldProps: {
      showSearch: true
    },
    align: 'center',
    width: 80
  }, {
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_13__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_9__.FormattedMessage, {
      id: "common.crop"
    }),
    dataIndex: 'iot_crop',
    valueType: 'select',
    request: function () {
      var _request2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee2() {
        var res;
        return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee2$(_context2) {
          while (1) switch (_context2.prev = _context2.next) {
            case 0:
              _context2.next = 2;
              return (0,_services_cropManager__WEBPACK_IMPORTED_MODULE_5__/* .getCropList */ .TQ)({});
            case 2:
              res = _context2.sent;
              return _context2.abrupt("return", res.data.map(function (item) {
                return {
                  label: item.label,
                  value: item.name
                };
              }));
            case 4:
            case "end":
              return _context2.stop();
          }
        }, _callee2);
      }));
      function request() {
        return _request2.apply(this, arguments);
      }
      return request;
    }(),
    fieldProps: {
      showSearch: true
    },
    // render: (warehouse, record) => {
    //   // Assuming record has a warehouse_label property
    //   return record.warehouse_label;
    // },
    // hideInSearch: true,
    // renderFormItem(schema, config, form, action) {
    //   return (
    //     <ProFormSelect
    //       showSearch
    //       name="iot_crop"
    //       // rules={[{ required: true, message: 'Vui l\xF2ng ch\u1ECDn kho' }]}
    //       // required
    //       colProps={{ span: 8 }}
    //       request={async () => {
    //         const res = await getCropList({});
    //         return res.data.map((item) => ({ label: item.label, value: item.name }));
    //       }}
    //       width={'md'}
    //     />
    //   );
    // },
    align: 'center',
    width: 100
  }, {
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_13__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_9__.FormattedMessage, {
      id: "common.inventory_price"
    }),
    dataIndex: 'stock_value',
    hideInSearch: true,
    // valueType: 'money',
    // fieldProps: {
    //   precision: 0, // \u0110\u1EB7t precision = 0 \u0111\u1EC3 b\u1ECF s\u1ED1 th\u1EADp ph\xE2n
    // },
    align: 'center',
    width: 80,
    render: function render(stock_value) {
      return numeral__WEBPACK_IMPORTED_MODULE_10___default()(stock_value).format('0,0');
    }
  }, {
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_13__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_9__.FormattedMessage, {
      id: "common.inventory_price_difference"
    }),
    dataIndex: 'stock_value_difference',
    hideInSearch: true,
    align: 'center',
    // valueType: 'money',
    // fieldProps: {
    //   precision: 0, // \u0110\u1EB7t precision = 0 \u0111\u1EC3 b\u1ECF s\u1ED1 th\u1EADp ph\xE2n
    // },
    width: 80,
    render: function render(stock_value_difference) {
      var stockValueDiff = parseInt(stock_value_difference);
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_13__.jsx)("span", {
        style: {
          color: stockValueDiff < 0 ? 'red' : 'green'
        },
        children: numeral__WEBPACK_IMPORTED_MODULE_10___default()(stock_value_difference).format('0,0')
      });
    }
  }];
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_13__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_13__.Fragment, {
    children: [hasRepostingJobs && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_13__.jsx)(_components_StockRepostWarning__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z, {
      type: "sleReCal"
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_13__.jsx)(_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .Z, {
      scroll: {
        x: 1400
      },
      pagination: {
        // pageSize: 5,
        pageSizeOptions: [10, 20, 50, 100],
        showSizeChanger: true
      },
      columns: columns,
      size: "small",
      search: {
        labelWidth: 'auto',
        defaultCollapsed: true
      },
      rowKey: function rowKey(record) {
        return record.item_code + record.warehouse + record.voucher_no + record.stock_value_difference;
      },
      request: ( /*#__PURE__*/function () {
        var _ref2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee3(params, sort, filter) {
          var from_date, to_date, _params$posting_date, res;
          return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee3$(_context3) {
            while (1) switch (_context3.prev = _context3.next) {
              case 0:
                console.log('sort', sort);
                if (params.posting_date) {
                  _params$posting_date = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_3___default()(params.posting_date, 2);
                  from_date = _params$posting_date[0];
                  to_date = _params$posting_date[1];
                }
                params.order_by = sort;
                params.from_date = from_date;
                params.to_date = to_date;
                params.page = params.current;
                params.size = params.pageSize;
                console.log('params is', params);
                _context3.next = 10;
                return (0,_services_InventoryManagementV3_product_item__WEBPACK_IMPORTED_MODULE_6__/* .getItemInventoryVouchers */ .E6)(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default()({}, params), {}, {
                  name: entity.item_code
                }));
              case 10:
                res = _context3.sent;
                setHasRepostingJobs(res.hasRepostingJobs);
                return _context3.abrupt("return", {
                  data: (0,_utils_array__WEBPACK_IMPORTED_MODULE_8__/* .sortArrayByObjectKey */ .G3)({
                    arr: res.data,
                    sort: sort
                  }),
                  total: res.pagination.totalElements
                });
              case 13:
              case "end":
                return _context3.stop();
            }
          }, _callee3);
        }));
        return function (_x, _x2, _x3) {
          return _ref2.apply(this, arguments);
        };
      }())
    })]
  });
};
var InventoryDetail = function InventoryDetail(_ref3) {
  var entity = _ref3.entity,
    isModalOpen = _ref3.isModalOpen,
    setIsModalOpen = _ref3.setIsModalOpen;
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_13__.jsx)(antd__WEBPACK_IMPORTED_MODULE_17__/* ["default"] */ .Z, {
    open: isModalOpen,
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_13__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_9__.FormattedMessage, {
      id: "common.detail"
    }),
    onCancel: function onCancel() {
      setIsModalOpen(false);
    },
    footer: [],
    width: 1600,
    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_13__.jsx)(TableInventoryDetail, {
      entity: entity
    })
  });
};
/* harmony default export */ __webpack_exports__.Z = (InventoryDetail);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///15311
`)},25770:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   m: function() { return /* binding */ addDefaultConfigColumns; }
/* harmony export */ });
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(97857);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0__);

var addDefaultConfigColumns = function addDefaultConfigColumns(columns) {
  return columns
  // add sort multiple columns
  .map(function (item, index) {
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default()({}, item), {}, {
      sorter: {
        multiple: index
      }
    });
  });
};//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiMjU3NzAuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBRU8sSUFBTUEsdUJBQXVCLEdBQUcsU0FBMUJBLHVCQUF1QkEsQ0FBSUMsT0FBMEIsRUFBSztFQUNyRSxPQUNFQTtFQUNFO0VBQUEsQ0FDQ0MsR0FBRyxDQUNGLFVBQUNDLElBQUksRUFBRUMsS0FBSztJQUFBLE9BQUFDLDRLQUFBLENBQUFBLDRLQUFBLEtBRUxGLElBQUk7TUFDUEcsTUFBTSxFQUFFO1FBQ05DLFFBQVEsRUFBRUg7TUFDWjtJQUFDO0VBQUEsQ0FFUCxDQUFDO0FBRVAsQ0FBQyIsInNvdXJjZXMiOlsid2VicGFjazovL2FudC1kZXNpZ24tcHJvLy4vc3JjL3BhZ2VzL1dhcmVob3VzZU1hbmFnZW1lbnRWMy9fdXRpbHMudHM/ZGFiMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcm9Db2x1bW5zIH0gZnJvbSAnQGFudC1kZXNpZ24vcHJvLWNvbXBvbmVudHMnO1xyXG5cclxuZXhwb3J0IGNvbnN0IGFkZERlZmF1bHRDb25maWdDb2x1bW5zID0gKGNvbHVtbnM6IFByb0NvbHVtbnM8YW55PltdKSA9PiB7XHJcbiAgcmV0dXJuIChcclxuICAgIGNvbHVtbnNcclxuICAgICAgLy8gYWRkIHNvcnQgbXVsdGlwbGUgY29sdW1uc1xyXG4gICAgICAubWFwPFByb0NvbHVtbnM8YW55Pj4oXHJcbiAgICAgICAgKGl0ZW0sIGluZGV4KSA9PlxyXG4gICAgICAgICAgKHtcclxuICAgICAgICAgICAgLi4uaXRlbSxcclxuICAgICAgICAgICAgc29ydGVyOiB7XHJcbiAgICAgICAgICAgICAgbXVsdGlwbGU6IGluZGV4LFxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgfSBhcyBQcm9Db2x1bW5zPGFueT4gYXMgYW55KSxcclxuICAgICAgKVxyXG4gICk7XHJcbn07XHJcbiJdLCJuYW1lcyI6WyJhZGREZWZhdWx0Q29uZmlnQ29sdW1ucyIsImNvbHVtbnMiLCJtYXAiLCJpdGVtIiwiaW5kZXgiLCJfb2JqZWN0U3ByZWFkIiwic29ydGVyIiwibXVsdGlwbGUiXSwic291cmNlUm9vdCI6IiJ9
//# sourceURL=webpack-internal:///25770
`)},77890:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Ak: function() { return /* binding */ updateCrop; },
/* harmony export */   Gz: function() { return /* binding */ getCropManagementInfoList; },
/* harmony export */   Ir: function() { return /* binding */ deleteCropNote; },
/* harmony export */   JB: function() { return /* binding */ addParticipantInCrop; },
/* harmony export */   LY: function() { return /* binding */ getTemplateCropList; },
/* harmony export */   No: function() { return /* binding */ getParticipantsInCrop; },
/* harmony export */   TQ: function() { return /* binding */ getCropList; },
/* harmony export */   Tq: function() { return /* binding */ deleteParticipantsInCrop; },
/* harmony export */   WP: function() { return /* binding */ getStatisticNoteList; },
/* harmony export */   bx: function() { return /* binding */ updateCropNote; },
/* harmony export */   mP: function() { return /* binding */ createCrop; },
/* harmony export */   rC: function() { return /* binding */ createCropNote; },
/* harmony export */   vW: function() { return /* binding */ getCurrentStateOfCrop; },
/* harmony export */   xu: function() { return /* binding */ getCropNoteList; }
/* harmony export */ });
/* unused harmony exports updateParticipantsInCrop, getStatisticPestList */
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(7837);
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(467);




var getCropList = /*#__PURE__*/function () {
  var _ref = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/crop'), {
            method: 'GET',
            params: (0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .getParamsReqList */ .vj)(params)
          });
        case 2:
          res = _context.sent;
          return _context.abrupt("return", res.result);
        case 4:
        case "end":
          return _context.stop();
      }
    }, _callee);
  }));
  return function getCropList(_x) {
    return _ref.apply(this, arguments);
  };
}();
var getTemplateCropList = /*#__PURE__*/function () {
  var _ref2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee2(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          _context2.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/crop/template'), {
            method: 'GET',
            params: (0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .getParamsReqList */ .vj)(params)
          });
        case 2:
          res = _context2.sent;
          return _context2.abrupt("return", res.result);
        case 4:
        case "end":
          return _context2.stop();
      }
    }, _callee2);
  }));
  return function getTemplateCropList(_x2) {
    return _ref2.apply(this, arguments);
  };
}();
var getCropManagementInfoList = /*#__PURE__*/function () {
  var _ref3 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee3(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee3$(_context3) {
      while (1) switch (_context3.prev = _context3.next) {
        case 0:
          _context3.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/crop-management-info'), {
            method: 'GET',
            params: params
          });
        case 2:
          res = _context3.sent;
          return _context3.abrupt("return", res.result);
        case 4:
        case "end":
          return _context3.stop();
      }
    }, _callee3);
  }));
  return function getCropManagementInfoList(_x3) {
    return _ref3.apply(this, arguments);
  };
}();
var getCurrentStateOfCrop = /*#__PURE__*/function () {
  var _ref4 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee4(cropId) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee4$(_context4) {
      while (1) switch (_context4.prev = _context4.next) {
        case 0:
          _context4.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/crop-current-state'), {
            method: 'GET',
            params: {
              crop_id: cropId
            }
          });
        case 2:
          res = _context4.sent;
          return _context4.abrupt("return", res.result);
        case 4:
        case "end":
          return _context4.stop();
      }
    }, _callee4);
  }));
  return function getCurrentStateOfCrop(_x4) {
    return _ref4.apply(this, arguments);
  };
}();
var createCrop = /*#__PURE__*/function () {
  var _ref5 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee5(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee5$(_context5) {
      while (1) switch (_context5.prev = _context5.next) {
        case 0:
          _context5.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/crop'), {
            method: 'POST',
            data: data
          });
        case 2:
          res = _context5.sent;
          return _context5.abrupt("return", res.result);
        case 4:
        case "end":
          return _context5.stop();
      }
    }, _callee5);
  }));
  return function createCrop(_x5) {
    return _ref5.apply(this, arguments);
  };
}();
var updateCrop = /*#__PURE__*/function () {
  var _ref6 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee6(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee6$(_context6) {
      while (1) switch (_context6.prev = _context6.next) {
        case 0:
          _context6.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/crop'), {
            method: 'PUT',
            data: data
          });
        case 2:
          res = _context6.sent;
          return _context6.abrupt("return", res.result);
        case 4:
        case "end":
          return _context6.stop();
      }
    }, _callee6);
  }));
  return function updateCrop(_x6) {
    return _ref6.apply(this, arguments);
  };
}();
var getCropNoteList = /*#__PURE__*/function () {
  var _ref7 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee7(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee7$(_context7) {
      while (1) switch (_context7.prev = _context7.next) {
        case 0:
          _context7.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/note'), {
            method: 'GET',
            params: (0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .getParamsReqList */ .vj)(params)
          });
        case 2:
          res = _context7.sent;
          return _context7.abrupt("return", res.result);
        case 4:
        case "end":
          return _context7.stop();
      }
    }, _callee7);
  }));
  return function getCropNoteList(_x7) {
    return _ref7.apply(this, arguments);
  };
}();
var createCropNote = /*#__PURE__*/function () {
  var _ref8 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee8(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee8$(_context8) {
      while (1) switch (_context8.prev = _context8.next) {
        case 0:
          _context8.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/note'), {
            method: 'POST',
            data: data
          });
        case 2:
          res = _context8.sent;
          return _context8.abrupt("return", res.result);
        case 4:
        case "end":
          return _context8.stop();
      }
    }, _callee8);
  }));
  return function createCropNote(_x8) {
    return _ref8.apply(this, arguments);
  };
}();
var updateCropNote = /*#__PURE__*/function () {
  var _ref9 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee9(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee9$(_context9) {
      while (1) switch (_context9.prev = _context9.next) {
        case 0:
          _context9.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/note'), {
            method: 'PUT',
            data: data
          });
        case 2:
          res = _context9.sent;
          return _context9.abrupt("return", res.result);
        case 4:
        case "end":
          return _context9.stop();
      }
    }, _callee9);
  }));
  return function updateCropNote(_x9) {
    return _ref9.apply(this, arguments);
  };
}();
var deleteCropNote = /*#__PURE__*/function () {
  var _ref11 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee10(_ref10) {
    var name, res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee10$(_context10) {
      while (1) switch (_context10.prev = _context10.next) {
        case 0:
          name = _ref10.name;
          _context10.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)("api/v2/cropManage/note?name=".concat(name)), {
            method: 'DELETE'
          });
        case 3:
          res = _context10.sent;
          return _context10.abrupt("return", res.result);
        case 5:
        case "end":
          return _context10.stop();
      }
    }, _callee10);
  }));
  return function deleteCropNote(_x10) {
    return _ref11.apply(this, arguments);
  };
}();
// Participants
var getParticipantsInCrop = /*#__PURE__*/function () {
  var _ref12 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee11(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee11$(_context11) {
      while (1) switch (_context11.prev = _context11.next) {
        case 0:
          _context11.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/iot_employee_in_crop'), {
            method: 'GET',
            params: (0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .getParamsReqList */ .vj)(params)
          });
        case 2:
          res = _context11.sent;
          return _context11.abrupt("return", res.result);
        case 4:
        case "end":
          return _context11.stop();
      }
    }, _callee11);
  }));
  return function getParticipantsInCrop(_x11) {
    return _ref12.apply(this, arguments);
  };
}();
var addParticipantInCrop = /*#__PURE__*/function () {
  var _ref13 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee12(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee12$(_context12) {
      while (1) switch (_context12.prev = _context12.next) {
        case 0:
          _context12.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/iot_employee_in_crop'), {
            method: 'POST',
            data: data
          });
        case 2:
          res = _context12.sent;
          return _context12.abrupt("return", res);
        case 4:
        case "end":
          return _context12.stop();
      }
    }, _callee12);
  }));
  return function addParticipantInCrop(_x12) {
    return _ref13.apply(this, arguments);
  };
}();
var updateParticipantsInCrop = /*#__PURE__*/(/* unused pure expression or super */ null && (function () {
  var _ref14 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee13(data) {
    var res;
    return _regeneratorRuntime().wrap(function _callee13$(_context13) {
      while (1) switch (_context13.prev = _context13.next) {
        case 0:
          _context13.next = 2;
          return request(generateAPIPath('api/v2/cropManage/iot_employee_in_crop'), {
            method: 'PUT',
            data: data
          });
        case 2:
          res = _context13.sent;
          return _context13.abrupt("return", res);
        case 4:
        case "end":
          return _context13.stop();
      }
    }, _callee13);
  }));
  return function updateParticipantsInCrop(_x13) {
    return _ref14.apply(this, arguments);
  };
}()));
var deleteParticipantsInCrop = /*#__PURE__*/function () {
  var _ref15 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee14(name) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee14$(_context14) {
      while (1) switch (_context14.prev = _context14.next) {
        case 0:
          _context14.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/iot_employee_in_crop'), {
            method: 'DELETE',
            params: {
              name: name
            }
          });
        case 2:
          res = _context14.sent;
          return _context14.abrupt("return", res);
        case 4:
        case "end":
          return _context14.stop();
      }
    }, _callee14);
  }));
  return function deleteParticipantsInCrop(_x14) {
    return _ref15.apply(this, arguments);
  };
}();
var getStatisticPestList = /*#__PURE__*/(/* unused pure expression or super */ null && (function () {
  var _ref16 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee15(params) {
    var res;
    return _regeneratorRuntime().wrap(function _callee15$(_context15) {
      while (1) switch (_context15.prev = _context15.next) {
        case 0:
          _context15.next = 2;
          return request(generateAPIPath("api/v2/cropManage/statisticPestList?crop_id=".concat(params === null || params === void 0 ? void 0 : params.cropId)), {
            method: 'GET',
            params: getParamsReqList(params)
          });
        case 2:
          res = _context15.sent;
          return _context15.abrupt("return", res.result);
        case 4:
        case "end":
          return _context15.stop();
      }
    }, _callee15);
  }));
  return function getStatisticPestList(_x15) {
    return _ref16.apply(this, arguments);
  };
}()));
var getStatisticNoteList = /*#__PURE__*/function () {
  var _ref17 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee16(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee16$(_context16) {
      while (1) switch (_context16.prev = _context16.next) {
        case 0:
          _context16.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)("api/v2/cropManage/statisticNoteList?crop_id=".concat(params === null || params === void 0 ? void 0 : params.cropId)), {
            method: 'GET',
            params: (0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .getParamsReqList */ .vj)(params)
          });
        case 2:
          res = _context16.sent;
          return _context16.abrupt("return", res.result);
        case 4:
        case "end":
          return _context16.stop();
      }
    }, _callee16);
  }));
  return function getStatisticNoteList(_x16) {
    return _ref17.apply(this, arguments);
  };
}();//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///77890
`)},40063:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   J9: function() { return /* binding */ getCustomerUserList; },
/* harmony export */   Lf: function() { return /* binding */ listDynamicRoleAllSection; },
/* harmony export */   cb: function() { return /* binding */ updateCustomerUser; },
/* harmony export */   f6: function() { return /* binding */ createDynamicRole; },
/* harmony export */   fh: function() { return /* binding */ updateDynamicRole; },
/* harmony export */   jt: function() { return /* binding */ customerUserListAll; },
/* harmony export */   rX: function() { return /* binding */ removeDynamicRole; },
/* harmony export */   w: function() { return /* binding */ getDynamicRole; },
/* harmony export */   y_: function() { return /* binding */ createCustomerUser; }
/* harmony export */ });
/* unused harmony exports IIotDynamicRole, getCustomerUserIndividualList, deleteCustomerUser, deleteCustomerUserCredential */
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_createClass_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(72004);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_createClass_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_createClass_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_classCallCheck_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(12444);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_classCallCheck_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_classCallCheck_js__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_defineProperty_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(9783);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_defineProperty_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_defineProperty_js__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(7837);
/* harmony import */ var _sscript__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(39750);
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(467);








var IIotDynamicRole = /*#__PURE__*/(/* unused pure expression or super */ null && (_createClass(function IIotDynamicRole() {
  _classCallCheck(this, IIotDynamicRole);
  _defineProperty(this, "name", void 0);
  _defineProperty(this, "label", void 0);
  // Data
  _defineProperty(this, "role", void 0);
  // Data
  _defineProperty(this, "iot_customer", void 0);
  // Link
  _defineProperty(this, "sections", void 0);
} // Data
)));
var createCustomerUser = /*#__PURE__*/function () {
  var _ref = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_5__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_7__/* .generateAPIPath */ .rH)('api/v2/register/customer-user-with-role'), {
            method: 'POST',
            data: data
          });
        case 2:
          res = _context.sent;
          return _context.abrupt("return", res);
        case 4:
        case "end":
          return _context.stop();
      }
    }, _callee);
  }));
  return function createCustomerUser(_x) {
    return _ref.apply(this, arguments);
  };
}();
var getCustomerUserList = /*#__PURE__*/function () {
  var _ref2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee2(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          _context2.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_5__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_7__/* .generateAPIPath */ .rH)('api/v2/customerUser/user'), {
            params: (0,_utils__WEBPACK_IMPORTED_MODULE_7__/* .getParamsReqList */ .vj)(params)
          });
        case 2:
          res = _context2.sent;
          return _context2.abrupt("return", res.result);
        case 4:
        case "end":
          return _context2.stop();
      }
    }, _callee2);
  }));
  return function getCustomerUserList(_x2) {
    return _ref2.apply(this, arguments);
  };
}();
var getCustomerUserIndividualList = /*#__PURE__*/(/* unused pure expression or super */ null && (function () {
  var _ref3 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee3(params) {
    var res;
    return _regeneratorRuntime().wrap(function _callee3$(_context3) {
      while (1) switch (_context3.prev = _context3.next) {
        case 0:
          _context3.next = 2;
          return request(generateAPIPath('api/v2/customerUser/user/individual'), {
            params: getParamsReqList(params)
          });
        case 2:
          res = _context3.sent;
          return _context3.abrupt("return", res.result);
        case 4:
        case "end":
          return _context3.stop();
      }
    }, _callee3);
  }));
  return function getCustomerUserIndividualList(_x3) {
    return _ref3.apply(this, arguments);
  };
}()));
function customerUserListAll() {
  return _customerUserListAll.apply(this, arguments);
}

//update customer user
function _customerUserListAll() {
  _customerUserListAll = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee7() {
    var result;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee7$(_context7) {
      while (1) switch (_context7.prev = _context7.next) {
        case 0:
          _context7.prev = 0;
          _context7.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_5__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_7__/* .generateAPIPath */ .rH)("api/v2/customerUser/user"), {
            method: 'GET',
            params: {
              fields: ['*']
            }
          });
        case 3:
          result = _context7.sent;
          return _context7.abrupt("return", result.result);
        case 7:
          _context7.prev = 7;
          _context7.t0 = _context7["catch"](0);
          console.log(_context7.t0);
          throw _context7.t0;
        case 11:
        case "end":
          return _context7.stop();
      }
    }, _callee7, null, [[0, 7]]);
  }));
  return _customerUserListAll.apply(this, arguments);
}
var updateCustomerUser = /*#__PURE__*/function () {
  var _ref4 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee4(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee4$(_context4) {
      while (1) switch (_context4.prev = _context4.next) {
        case 0:
          _context4.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_5__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_7__/* .generateAPIPath */ .rH)('api/v2/customerUser/user'), {
            method: 'PUT',
            data: data
          });
        case 2:
          res = _context4.sent;
          return _context4.abrupt("return", res);
        case 4:
        case "end":
          return _context4.stop();
      }
    }, _callee4);
  }));
  return function updateCustomerUser(_x4) {
    return _ref4.apply(this, arguments);
  };
}();

//delete customer user
var deleteCustomerUser = /*#__PURE__*/(/* unused pure expression or super */ null && (function () {
  var _ref5 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee5(name) {
    var res;
    return _regeneratorRuntime().wrap(function _callee5$(_context5) {
      while (1) switch (_context5.prev = _context5.next) {
        case 0:
          _context5.next = 2;
          return request(generateAPIPath('api/v2/customerUser/user'), {
            method: 'DELETE',
            params: {
              name: name
            }
          });
        case 2:
          res = _context5.sent;
          return _context5.abrupt("return", res);
        case 4:
        case "end":
          return _context5.stop();
      }
    }, _callee5);
  }));
  return function deleteCustomerUser(_x5) {
    return _ref5.apply(this, arguments);
  };
}()));

//delete customer user credential
var deleteCustomerUserCredential = /*#__PURE__*/(/* unused pure expression or super */ null && (function () {
  var _ref6 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee6(name) {
    var res;
    return _regeneratorRuntime().wrap(function _callee6$(_context6) {
      while (1) switch (_context6.prev = _context6.next) {
        case 0:
          _context6.next = 2;
          return request(generateAPIPath('api/v2/customerUser/user-credential'), {
            method: 'DELETE',
            params: {
              name: name
            }
          });
        case 2:
          res = _context6.sent;
          return _context6.abrupt("return", res);
        case 4:
        case "end":
          return _context6.stop();
      }
    }, _callee6);
  }));
  return function deleteCustomerUserCredential(_x6) {
    return _ref6.apply(this, arguments);
  };
}()));
/**\r
 *\r
 * DYNAMIC ROLE APIs\r
 */
function listDynamicRoleAllSection() {
  return _listDynamicRoleAllSection.apply(this, arguments);
}
function _listDynamicRoleAllSection() {
  _listDynamicRoleAllSection = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee8() {
    var result;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee8$(_context8) {
      while (1) switch (_context8.prev = _context8.next) {
        case 0:
          _context8.prev = 0;
          _context8.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_5__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_7__/* .generateAPIPath */ .rH)("api/v2/dynamicRole/listAllSection"), {
            method: 'GET'
          });
        case 3:
          result = _context8.sent;
          return _context8.abrupt("return", result.result);
        case 7:
          _context8.prev = 7;
          _context8.t0 = _context8["catch"](0);
          console.log(_context8.t0);
          throw _context8.t0;
        case 11:
        case "end":
          return _context8.stop();
      }
    }, _callee8, null, [[0, 7]]);
  }));
  return _listDynamicRoleAllSection.apply(this, arguments);
}
function getDynamicRole() {
  return _getDynamicRole.apply(this, arguments);
}
function _getDynamicRole() {
  _getDynamicRole = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee9() {
    var result;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee9$(_context9) {
      while (1) switch (_context9.prev = _context9.next) {
        case 0:
          _context9.prev = 0;
          _context9.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_5__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_7__/* .generateAPIPath */ .rH)("api/v2/dynamicRole"), {
            method: 'GET',
            params: {
              page: 1,
              size: 100
            }
          });
        case 3:
          result = _context9.sent;
          return _context9.abrupt("return", result.result.data);
        case 7:
          _context9.prev = 7;
          _context9.t0 = _context9["catch"](0);
          console.log(_context9.t0);
          throw _context9.t0;
        case 11:
        case "end":
          return _context9.stop();
      }
    }, _callee9, null, [[0, 7]]);
  }));
  return _getDynamicRole.apply(this, arguments);
}
function createDynamicRole(_x7) {
  return _createDynamicRole.apply(this, arguments);
}
function _createDynamicRole() {
  _createDynamicRole = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee10(data) {
    var result;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee10$(_context10) {
      while (1) switch (_context10.prev = _context10.next) {
        case 0:
          _context10.prev = 0;
          _context10.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_5__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_7__/* .generateAPIPath */ .rH)("api/v2/dynamicRole"), {
            method: 'POST',
            data: data
          });
        case 3:
          result = _context10.sent;
          return _context10.abrupt("return", result.result);
        case 7:
          _context10.prev = 7;
          _context10.t0 = _context10["catch"](0);
          console.log(_context10.t0);
          throw _context10.t0;
        case 11:
        case "end":
          return _context10.stop();
      }
    }, _callee10, null, [[0, 7]]);
  }));
  return _createDynamicRole.apply(this, arguments);
}
function updateDynamicRole(_x8) {
  return _updateDynamicRole.apply(this, arguments);
}
function _updateDynamicRole() {
  _updateDynamicRole = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee11(data) {
    var result;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee11$(_context11) {
      while (1) switch (_context11.prev = _context11.next) {
        case 0:
          _context11.prev = 0;
          _context11.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_5__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_7__/* .generateAPIPath */ .rH)("api/v2/dynamicRole"), {
            method: 'PUT',
            data: data
          });
        case 3:
          result = _context11.sent;
          return _context11.abrupt("return", result.result);
        case 7:
          _context11.prev = 7;
          _context11.t0 = _context11["catch"](0);
          console.log(_context11.t0);
          throw _context11.t0;
        case 11:
        case "end":
          return _context11.stop();
      }
    }, _callee11, null, [[0, 7]]);
  }));
  return _updateDynamicRole.apply(this, arguments);
}
function removeDynamicRole(_x9) {
  return _removeDynamicRole.apply(this, arguments);
}
function _removeDynamicRole() {
  _removeDynamicRole = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee12(data) {
    var name, result;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee12$(_context12) {
      while (1) switch (_context12.prev = _context12.next) {
        case 0:
          _context12.prev = 0;
          name = data.name ? data.name : '';
          _context12.next = 4;
          return (0,_sscript__WEBPACK_IMPORTED_MODULE_6__/* .generalDelete */ .ID)('iot_dynamic_role', name);
        case 4:
          result = _context12.sent;
          return _context12.abrupt("return", result);
        case 8:
          _context12.prev = 8;
          _context12.t0 = _context12["catch"](0);
          throw _context12.t0;
        case 11:
        case "end":
          return _context12.stop();
      }
    }, _callee12, null, [[0, 8]]);
  }));
  return _removeDynamicRole.apply(this, arguments);
}//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///40063
`)},89436:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: function() { return /* binding */ getItemGroupList; },
/* harmony export */   m: function() { return /* binding */ getItemList; }
/* harmony export */ });
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(97857);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(7837);
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(467);





var handleError = function handleError(error) {
  console.log("Error in services/warehouse: \\n".concat(error));
};
var CRUD_PATH = {
  READ: 'item',
  READ_GROUP: 'itemGroup'
};
var getItemList = /*#__PURE__*/function () {
  var _ref = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.prev = 0;
          _context.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)("api/v2/".concat(CRUD_PATH.READ)), {
            method: 'GET',
            params: D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({
              page: 1,
              size: 100
            }, (0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .getParamsReqList */ .vj)(params))
          });
        case 3:
          res = _context.sent;
          return _context.abrupt("return", {
            data: res.result.data || []
          });
        case 7:
          _context.prev = 7;
          _context.t0 = _context["catch"](0);
          handleError(_context.t0);
          return _context.abrupt("return", {
            data: []
          });
        case 11:
        case "end":
          return _context.stop();
      }
    }, _callee, null, [[0, 7]]);
  }));
  return function getItemList(_x) {
    return _ref.apply(this, arguments);
  };
}();
var getItemGroupList = /*#__PURE__*/function () {
  var _ref2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee2(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          _context2.prev = 0;
          _context2.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)("api/v2/".concat(CRUD_PATH.READ_GROUP)), {
            method: 'GET',
            params: D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({
              page: 1,
              size: 100
            }, (0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .getParamsReqList */ .vj)(params))
          });
        case 3:
          res = _context2.sent;
          return _context2.abrupt("return", {
            data: res.result.data || []
          });
        case 7:
          _context2.prev = 7;
          _context2.t0 = _context2["catch"](0);
          handleError(_context2.t0);
          return _context2.abrupt("return", {
            data: []
          });
        case 11:
        case "end":
          return _context2.stop();
      }
    }, _callee2, null, [[0, 7]]);
  }));
  return function getItemGroupList(_x2) {
    return _ref2.apply(this, arguments);
  };
}();//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///89436
`)},19073:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   G3: function() { return /* binding */ sortArrayByObjectKey; },
/* harmony export */   Pr: function() { return /* binding */ createEmptyArray; }
/* harmony export */ });
/* unused harmony export getDuplicateInArrayObj */
/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(96486);
/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_0__);

var createEmptyArray = function createEmptyArray(length) {
  return Array.from(new Array(length)).map(function (__, index) {
    return index;
  });
};
var getDuplicateInArrayObj = function getDuplicateInArrayObj(params) {
  var duplicates = _(params.arr).groupBy(params.groupBy).filter(function (group) {
    return group.length > 1;
  }).flatten().value();
  return duplicates;
};
var sortArrayByObjectKey = function sortArrayByObjectKey(params) {
  // const sorted = _.orderBy(
  //   params.arr,
  //   Object.keys(params.sort),
  //   Object.values(params.sort).map((key) => (key === 'ascend' ? 'asc' : 'desc')),
  // );
  // return sorted;
  // sorter with localCompare
  return params.arr.sort(function (a, b) {
    for (var _key in params.sort) {
      if (Object.prototype.hasOwnProperty.call(params.sort, _key)) {
        var order = params.sort[_key];
        var aValue = a[_key] ? lodash__WEBPACK_IMPORTED_MODULE_0___default().toString(a[_key]) : '';
        var bValue = b[_key] ? lodash__WEBPACK_IMPORTED_MODULE_0___default().toString(b[_key]) : '';
        var localCompare = aValue.localeCompare(bValue);
        if (localCompare < 0) {
          return order === 'ascend' ? -1 : 1;
        }
        if (localCompare > 0) {
          return order === 'ascend' ? 1 : -1;
        }
      }
    }
    return 0;
  });
};//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///19073
`)},7369:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   W: function() { return /* binding */ toFirstUpperCase; },
/* harmony export */   w: function() { return /* binding */ nonAccentVietnamese; }
/* harmony export */ });
function nonAccentVietnamese(str) {
  var _str$toString;
  var _str = str === null || str === void 0 || (_str$toString = str.toString) === null || _str$toString === void 0 ? void 0 : _str$toString.call(str);
  if (typeof _str !== 'string') return str;
  _str = _str.toLowerCase();
  //     We can also use this instead of from line 11 to line 17
  //     str = str.replace(/\\u00E0|\\u00E1|\\u1EA1|\\u1EA3|\\u00E3|\\u00E2|\\u1EA7|\\u1EA5|\\u1EAD|\\u1EA9|\\u1EAB|\\u0103|\\u1EB1|\\u1EAF|\\u1EB7|\\u1EB3|\\u1EB5/g, "a");
  //     str = str.replace(/\\u00E8|\\u00E9|\\u1EB9|\\u1EBB|\\u1EBD|\\u00EA|\\u1EC1|\\u1EBF|\\u1EC7|\\u1EC3|\\u1EC5/g, "e");
  //     str = str.replace(/\\u00EC|\\u00ED|\\u1ECB|\\u1EC9|\\u0129/g, "i");
  //     str = str.replace(/\\u00F2|\\u00F3|\\u1ECD|\\u1ECF|\\u00F5|\\u00F4|\\u1ED3|\\u1ED1|\\u1ED9|\\u1ED5|\\u1ED7|\\u01A1|\\u1EDD|\\u1EDB|\\u1EE3|\\u1EDF|\\u1EE1/g, "o");
  //     str = str.replace(/\\u00F9|\\u00FA|\\u1EE5|\\u1EE7|\\u0169|\\u01B0|\\u1EEB|\\u1EE9|\\u1EF1|\\u1EED|\\u1EEF/g, "u");
  //     str = str.replace(/\\u1EF3|\\u00FD|\\u1EF5|\\u1EF7|\\u1EF9/g, "y");
  //     str = str.replace(/\\u0111/g, "d");
  _str = _str.replace(/\xE0|\xE1|\u1EA1|\u1EA3|\xE3|\xE2|\u1EA7|\u1EA5|\u1EAD|\u1EA9|\u1EAB|\u0103|\u1EB1|\u1EAF|\u1EB7|\u1EB3|\u1EB5/g, 'a');
  _str = _str.replace(/\xE8|\xE9|\u1EB9|\u1EBB|\u1EBD|\xEA|\u1EC1|\u1EBF|\u1EC7|\u1EC3|\u1EC5/g, 'e');
  _str = _str.replace(/\xEC|\xED|\u1ECB|\u1EC9|\u0129/g, 'i');
  _str = _str.replace(/\xF2|\xF3|\u1ECD|\u1ECF|\xF5|\xF4|\u1ED3|\u1ED1|\u1ED9|\u1ED5|\u1ED7|\u01A1|\u1EDD|\u1EDB|\u1EE3|\u1EDF|\u1EE1/g, 'o');
  _str = _str.replace(/\xF9|\xFA|\u1EE5|\u1EE7|\u0169|\u01B0|\u1EEB|\u1EE9|\u1EF1|\u1EED|\u1EEF/g, 'u');
  _str = _str.replace(/\u1EF3|\xFD|\u1EF5|\u1EF7|\u1EF9/g, 'y');
  _str = _str.replace(/\u0111/g, 'd');
  // Some system encode vietnamese combining accent as individual utf-8 characters
  _str = _str.replace(/\\u0300|\\u0301|\\u0303|\\u0309|\\u0323/g, ''); // Huy\u1EC1n s\u1EAFc h\u1ECFi ng\xE3 n\u1EB7ng
  _str = _str.replace(/\\u02C6|\\u0306|\\u031B/g, ''); // \xC2, \xCA, \u0102, \u01A0, \u01AF
  return _str.split(',').join('');
}
var toFirstUpperCase = function toFirstUpperCase(str) {
  if (!str) return '';
  return str.charAt(0).toUpperCase() + str.slice(1);
};//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///7369
`)}}]);
