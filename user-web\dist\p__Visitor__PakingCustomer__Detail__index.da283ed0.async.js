"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[3384],{81568:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(5574);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(7837);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(45360);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(86738);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(14726);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(67294);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(85893);







var ActionPopConfirm = function ActionPopConfirm(_ref) {
  var actionCall = _ref.actionCall,
    refreshData = _ref.refreshData,
    buttonType = _ref.buttonType,
    text = _ref.text,
    danger = _ref.danger,
    size = _ref.size;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false),
    _useState2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2___default()(_useState, 2),
    loading = _useState2[0],
    setLoading = _useState2[1];
  var onConfirm = /*#__PURE__*/function () {
    var _ref2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee() {
      return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _context.prev = 0;
            setLoading(true);
            if (!actionCall) {
              _context.next = 5;
              break;
            }
            _context.next = 5;
            return actionCall();
          case 5:
            if (!refreshData) {
              _context.next = 8;
              break;
            }
            _context.next = 8;
            return refreshData();
          case 8:
            _context.next = 14;
            break;
          case 10:
            _context.prev = 10;
            _context.t0 = _context["catch"](0);
            antd__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .ZP.error(_context.t0.toString());
            console.log(_context.t0);
          case 14:
            _context.prev = 14;
            setLoading(false);
            return _context.finish(14);
          case 17:
          case "end":
            return _context.stop();
        }
      }, _callee, null, [[0, 10, 14, 17]]);
    }));
    return function onConfirm() {
      return _ref2.apply(this, arguments);
    };
  }();
  var onCancel = /*#__PURE__*/function () {
    var _ref3 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee2() {
      return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee2$(_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
          case "end":
            return _context2.stop();
        }
      }, _callee2);
    }));
    return function onCancel() {
      return _ref3.apply(this, arguments);
    };
  }();
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(antd__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Z, {
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_3__.FormattedMessage, {
      id: "action.confirm"
    }),
    description: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_3__.FormattedMessage, {
      id: "action.verify"
    }),
    onConfirm: onConfirm,
    onCancel: onCancel,
    okText: "\\u0110\\u1ED3ng \\xFD",
    cancelText: "Hu\\u1EF7",
    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(antd__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .ZP, {
      style: {
        display: 'flex',
        alignItems: 'center',
        borderRadius: 0 // Thi\u1EBFt l\u1EADp g\xF3c vu\xF4ng cho button
      },
      size: size || 'middle',
      danger: danger,
      type: buttonType,
      loading: loading,
      children: text
    })
  });
};
/* harmony default export */ __webpack_exports__.Z = (ActionPopConfirm);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///81568
`)},6962:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return /* binding */ Detail; }
});

// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/regeneratorRuntime.js
var regeneratorRuntime = __webpack_require__(15009);
var regeneratorRuntime_default = /*#__PURE__*/__webpack_require__.n(regeneratorRuntime);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(99289);
var asyncToGenerator_default = /*#__PURE__*/__webpack_require__.n(asyncToGenerator);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/slicedToArray.js
var slicedToArray = __webpack_require__(5574);
var slicedToArray_default = /*#__PURE__*/__webpack_require__.n(slicedToArray);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/objectSpread2.js
var objectSpread2 = __webpack_require__(97857);
var objectSpread2_default = /*#__PURE__*/__webpack_require__.n(objectSpread2);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/toConsumableArray.js
var toConsumableArray = __webpack_require__(19632);
var toConsumableArray_default = /*#__PURE__*/__webpack_require__.n(toConsumableArray);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/PlusOutlined.js
var PlusOutlined = __webpack_require__(51042);
// EXTERNAL MODULE: ./node_modules/antd/es/message/index.js
var message = __webpack_require__(45360);
// EXTERNAL MODULE: ./node_modules/antd/es/card/index.js + 3 modules
var card = __webpack_require__(76216);
// EXTERNAL MODULE: ./node_modules/antd/es/upload/index.js + 26 modules
var upload = __webpack_require__(78367);
// EXTERNAL MODULE: ./node_modules/antd/es/modal/index.js + 1 modules
var modal = __webpack_require__(85576);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./src/services/uploadFile.ts
var uploadFile = __webpack_require__(64639);
// EXTERNAL MODULE: ./src/services/utils.ts
var utils = __webpack_require__(467);
// EXTERNAL MODULE: ./src/.umi-production/exports.ts + 34 modules
var _umi_production_exports = __webpack_require__(7837);
// EXTERNAL MODULE: ./node_modules/react/jsx-runtime.js
var jsx_runtime = __webpack_require__(85893);
;// CONCATENATED MODULE: ./src/components/AttachmentsOfDoctype/AttachPictureOfDoctype.tsx














var getBase64 = function getBase64(file) {
  return new Promise(function (resolve, reject) {
    var reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = function () {
      return resolve(reader.result);
    };
    reader.onerror = function (error) {
      return reject(error);
    };
  });
};
var AttachPictureOfDoctype = function AttachPictureOfDoctype(_ref) {
  var doctype = _ref.doctype,
    docname = _ref.docname,
    _ref$is_private = _ref.is_private,
    is_private = _ref$is_private === void 0 ? 1 : _ref$is_private,
    _ref$folder = _ref.folder,
    folder = _ref$folder === void 0 ? 'Home/Attachments' : _ref$folder,
    _ref$optimize = _ref.optimize,
    optimize = _ref$optimize === void 0 ? false : _ref$optimize;
  var _useState = (0,react.useState)(false),
    _useState2 = slicedToArray_default()(_useState, 2),
    loading = _useState2[0],
    setLoading = _useState2[1];
  var _useState3 = (0,react.useState)(false),
    _useState4 = slicedToArray_default()(_useState3, 2),
    uploading = _useState4[0],
    setUploading = _useState4[1];
  var _useState5 = (0,react.useState)([]),
    _useState6 = slicedToArray_default()(_useState5, 2),
    fileList = _useState6[0],
    setFileList = _useState6[1];
  var _useState7 = (0,react.useState)(false),
    _useState8 = slicedToArray_default()(_useState7, 2),
    previewOpen = _useState8[0],
    setPreviewOpen = _useState8[1];
  var _useState9 = (0,react.useState)(''),
    _useState10 = slicedToArray_default()(_useState9, 2),
    previewImage = _useState10[0],
    setPreviewImage = _useState10[1];
  var _useState11 = (0,react.useState)(''),
    _useState12 = slicedToArray_default()(_useState11, 2),
    previewTitle = _useState12[0],
    setPreviewTitle = _useState12[1];
  var _useState13 = (0,react.useState)(false),
    _useState14 = slicedToArray_default()(_useState13, 2),
    updating = _useState14[0],
    setUpdating = _useState14[1];
  var user_token = JSON.parse(localStorage.getItem('token')).token;
  var getFileList = /*#__PURE__*/function () {
    var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
      var result, privateImg, _fileList;
      return regeneratorRuntime_default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _context.prev = 0;
            setUpdating(true);
            _context.next = 4;
            return (0,uploadFile/* getListFileByDocname */.g8)({
              doctype: doctype,
              name: docname
            });
          case 4:
            result = _context.sent;
            // console.log(result);
            privateImg = [];
            _fileList = result.map(function (f) {
              if (f.file_url.search('private') !== -1) {
                privateImg.push({
                  uid: f.name,
                  name: f.file_name,
                  type: 'image/png',
                  status: 'done',
                  url: "".concat(utils/* HOST */.M5, "/api/v2/file/download?token=") + user_token + "&file_url=" + f.file_url + '&dt=' + doctype + '&dn=' + docname
                });
              }
              return {
                uid: f.name,
                name: f.file_name,
                type: 'image/png',
                status: 'done',
                url: "".concat(utils/* HOST */.M5, "/api/v2/file/download?token=") + user_token + "&file_url=" + f.file_url + '&dt=' + doctype + '&dn=' + docname
              };
            });
            setFileList(privateImg);
            // setFileList(_fileList);
            _context.next = 12;
            break;
          case 10:
            _context.prev = 10;
            _context.t0 = _context["catch"](0);
          case 12:
            _context.prev = 12;
            setUpdating(false);
            return _context.finish(12);
          case 15:
          case "end":
            return _context.stop();
        }
      }, _callee, null, [[0, 10, 12, 15]]);
    }));
    return function getFileList() {
      return _ref2.apply(this, arguments);
    };
  }();
  (0,react.useEffect)(function () {
    getFileList();
  }, [uploading]);
  var handleChange = function handleChange(info) {
    var newFileList = toConsumableArray_default()(info.fileList);

    // 1. Limit the number of uploaded files
    // Only to show two recent uploaded files, and old ones will be replaced by the new
    // 2. Read from response and show file link
    newFileList = newFileList.map(function (file) {
      //   console.log(file);
      if (file.response) {
        // Component will show file.url as link
        file.uid = file.response.name;
        file.url = "".concat(utils/* HOST */.M5, "/api/v2/file/download?token=") + user_token + "&file_url=" + file.response.file_url + '&dt=' + doctype + '&dn=' + docname;
      }
      return file;
    });
    console.log(newFileList);
    setFileList(newFileList);
  };
  var handleUpload = /*#__PURE__*/function () {
    var _ref3 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2(options) {
      var onSuccess, onError, file, onProgress, res, error;
      return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            onSuccess = options.onSuccess, onError = options.onError, file = options.file, onProgress = options.onProgress;
            _context2.prev = 1;
            setUploading(true);
            _context2.next = 5;
            return (0,uploadFile/* uploadFile */.cT)({
              file: file,
              doctype: doctype,
              docname: docname,
              is_private: is_private,
              folder: folder,
              optimize: optimize
            });
          case 5:
            res = _context2.sent;
            onSuccess(res.message);
            _context2.next = 14;
            break;
          case 9:
            _context2.prev = 9;
            _context2.t0 = _context2["catch"](1);
            console.log('Eroor: ', _context2.t0);
            error = new Error('Some error');
            onError({
              err: _context2.t0
            });
          case 14:
            _context2.prev = 14;
            setUploading(false);
            return _context2.finish(14);
          case 17:
          case "end":
            return _context2.stop();
        }
      }, _callee2, null, [[1, 9, 14, 17]]);
    }));
    return function handleUpload(_x) {
      return _ref3.apply(this, arguments);
    };
  }();
  var handleCancel = function handleCancel() {
    return setPreviewOpen(false);
  };
  var handlePreview = /*#__PURE__*/function () {
    var _ref4 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee3(file) {
      return regeneratorRuntime_default()().wrap(function _callee3$(_context3) {
        while (1) switch (_context3.prev = _context3.next) {
          case 0:
            if (!(!file.url && !file.preview)) {
              _context3.next = 4;
              break;
            }
            _context3.next = 3;
            return getBase64(file.originFileObj);
          case 3:
            file.preview = _context3.sent;
          case 4:
            console.log('File URL:', file.url);
            setPreviewImage(file.url || file.preview);
            setPreviewOpen(true);
            setPreviewTitle(file.name || file.url.substring(file.url.lastIndexOf('/') + 1));
          case 8:
          case "end":
            return _context3.stop();
        }
      }, _callee3);
    }));
    return function handlePreview(_x2) {
      return _ref4.apply(this, arguments);
    };
  }();
  var props = {
    onRemove: function () {
      var _onRemove = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee4(file) {
        var newFileList;
        return regeneratorRuntime_default()().wrap(function _callee4$(_context4) {
          while (1) switch (_context4.prev = _context4.next) {
            case 0:
              if (!(file.status === 'done')) {
                _context4.next = 14;
                break;
              }
              _context4.prev = 1;
              _context4.next = 4;
              return (0,uploadFile/* removeFile */.Yd)({
                fid: file.uid,
                dt: doctype,
                dn: docname
              });
            case 4:
              newFileList = fileList.filter(function (f) {
                return f.uid !== file.uid;
              });
              console.log('newFileList', newFileList);
              setFileList(toConsumableArray_default()(newFileList));
              _context4.next = 12;
              break;
            case 9:
              _context4.prev = 9;
              _context4.t0 = _context4["catch"](1);
              message/* default */.ZP.error('Delete Error,try again!');
            case 12:
              _context4.prev = 12;
              return _context4.finish(12);
            case 14:
            case "end":
              return _context4.stop();
          }
        }, _callee4, null, [[1, 9, 12, 14]]);
      }));
      function onRemove(_x3) {
        return _onRemove.apply(this, arguments);
      }
      return onRemove;
    }(),
    fileList: fileList,
    multiple: true,
    customRequest: handleUpload,
    onChange: handleChange,
    // showUploadList: {
    //   showDownloadIcon: true,
    //   downloadIcon: <DownloadOutlined></DownloadOutlined>,
    // },
    onPreview: handlePreview,
    listType: 'picture-card',
    onDownload: function () {
      var _onDownload = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee5(file) {
        var res;
        return regeneratorRuntime_default()().wrap(function _callee5$(_context5) {
          while (1) switch (_context5.prev = _context5.next) {
            case 0:
              _context5.prev = 0;
              _context5.next = 3;
              return (0,uploadFile/* downloadFile */.Sv)({
                file_url: file.url,
                fid: file.fid,
                dt: doctype,
                dn: docname
              });
            case 3:
              res = _context5.sent;
              console.log(res);
              _context5.next = 10;
              break;
            case 7:
              _context5.prev = 7;
              _context5.t0 = _context5["catch"](0);
              console.log('Eroor: ', _context5.t0);
            case 10:
              _context5.prev = 10;
              return _context5.finish(10);
            case 12:
            case "end":
              return _context5.stop();
          }
        }, _callee5, null, [[0, 7, 10, 12]]);
      }));
      function onDownload(_x4) {
        return _onDownload.apply(this, arguments);
      }
      return onDownload;
    }()
  };
  var uploadButton = /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
    children: [/*#__PURE__*/(0,jsx_runtime.jsx)(PlusOutlined/* default */.Z, {}), /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
      style: {
        marginTop: 8
      },
      children: "Upload"
    })]
  });
  var access = (0,_umi_production_exports.useAccess)();
  var canUpdate = access.canUpdateInVisitorManagement();
  return /*#__PURE__*/(0,jsx_runtime.jsx)(jsx_runtime.Fragment, {
    children: /*#__PURE__*/(0,jsx_runtime.jsxs)(card/* default */.Z, {
      title: "\\u1EA2nh",
      loading: loading,
      children: [canUpdate && /*#__PURE__*/(0,jsx_runtime.jsx)(upload/* default */.Z, objectSpread2_default()(objectSpread2_default()({}, props), {}, {
        children: fileList.length >= 8 ? null : uploadButton
      })), /*#__PURE__*/(0,jsx_runtime.jsx)(modal/* default */.Z, {
        open: previewOpen,
        title: previewTitle,
        footer: null,
        onCancel: handleCancel,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)("img", {
          alt: "example",
          style: {
            width: '100%'
          },
          src: previewImage
        })
      })]
    })
  });
};
/* harmony default export */ var AttachmentsOfDoctype_AttachPictureOfDoctype = (AttachPictureOfDoctype);
// EXTERNAL MODULE: ./src/services/visitor.ts
var visitor = __webpack_require__(98465);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-layout/es/components/PageContainer/index.js + 18 modules
var PageContainer = __webpack_require__(6110);
// EXTERNAL MODULE: ./node_modules/antd/es/space/index.js + 2 modules
var space = __webpack_require__(42075);
// EXTERNAL MODULE: ./node_modules/antd/es/tabs/index.js + 30 modules
var tabs = __webpack_require__(48096);
// EXTERNAL MODULE: ./node_modules/antd/es/row/index.js
var row = __webpack_require__(71230);
// EXTERNAL MODULE: ./node_modules/antd/es/col/index.js
var col = __webpack_require__(15746);
// EXTERNAL MODULE: ./node_modules/antd/es/result/index.js + 6 modules
var result = __webpack_require__(29905);
// EXTERNAL MODULE: ./node_modules/dayjs/dayjs.min.js
var dayjs_min = __webpack_require__(27484);
var dayjs_min_default = /*#__PURE__*/__webpack_require__.n(dayjs_min);
// EXTERNAL MODULE: ./src/components/ActionPopConfirm/index.tsx
var ActionPopConfirm = __webpack_require__(81568);
;// CONCATENATED MODULE: ./src/pages/Visitor/PakingCustomer/Components/RemovePakingCustomer.tsx






var RemoveForm = function RemoveForm(params) {
  var removeEnity = /*#__PURE__*/function () {
    var _ref = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
      return regeneratorRuntime_default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _context.prev = 0;
            _context.next = 3;
            return visitor/* visitorInforService */.tO.updateStatusDeleted([{
              name: params.name
            }]);
          case 3:
            _context.next = 8;
            break;
          case 5:
            _context.prev = 5;
            _context.t0 = _context["catch"](0);
            message/* default */.ZP.error(_context.t0.toString());
          case 8:
          case "end":
            return _context.stop();
        }
      }, _callee, null, [[0, 5]]);
    }));
    return function removeEnity() {
      return _ref.apply(this, arguments);
    };
  }();
  return /*#__PURE__*/(0,jsx_runtime.jsx)(ActionPopConfirm/* default */.Z, {
    actionCall: removeEnity,
    refreshData: params.refreshFnc,
    text: 'Remove',
    buttonType: 'primary',
    danger: true
  });
};
/* harmony default export */ var RemovePakingCustomer = (RemoveForm);
// EXTERNAL MODULE: ./node_modules/antd/es/form/index.js + 18 modules
var es_form = __webpack_require__(98138);
// EXTERNAL MODULE: ./node_modules/antd/es/select/index.js + 30 modules
var es_select = __webpack_require__(83863);
// EXTERNAL MODULE: ./node_modules/antd/es/typography/index.js + 18 modules
var typography = __webpack_require__(25514);
// EXTERNAL MODULE: ./node_modules/antd/es/button/index.js + 9 modules
var es_button = __webpack_require__(14726);
// EXTERNAL MODULE: ./node_modules/antd/es/input/index.js + 4 modules
var input = __webpack_require__(96365);
// EXTERNAL MODULE: ./node_modules/antd/es/date-picker/index.js + 78 modules
var date_picker = __webpack_require__(47676);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/LoadingOutlined.js
var LoadingOutlined = __webpack_require__(79090);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/DeleteFilled.js + 1 modules
var DeleteFilled = __webpack_require__(27704);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/Select/index.js
var Select = __webpack_require__(64317);
;// CONCATENATED MODULE: ./src/pages/Visitor/PakingCustomer/Components/UpdatePakingCustomerForm.tsx






var Item = es_form/* default */.Z.Item;










var Option = es_select/* default */.Z.Option;
var Title = typography/* default */.Z.Title,
  Text = typography/* default */.Z.Text;
var UpdateForm = function UpdateForm(params) {
  var _useState = (0,react.useState)(false),
    _useState2 = slicedToArray_default()(_useState, 2),
    loading = _useState2[0],
    setLoading = _useState2[1];
  var _Form$useForm = es_form/* default */.Z.useForm(),
    _Form$useForm2 = slicedToArray_default()(_Form$useForm, 1),
    form = _Form$useForm2[0];
  console.log('params.customer', params.customer);
  var _useState3 = (0,react.useState)('Code'),
    _useState4 = slicedToArray_default()(_useState3, 2),
    idCardCode = _useState4[0],
    setIdCardCode = _useState4[1];
  var _useState5 = (0,react.useState)(false),
    _useState6 = slicedToArray_default()(_useState5, 2),
    searching = _useState6[0],
    setSearching = _useState6[1];
  var _useState7 = (0,react.useState)([]),
    _useState8 = slicedToArray_default()(_useState7, 2),
    options = _useState8[0],
    setOptions = _useState8[1];
  var _useState9 = (0,react.useState)([]),
    _useState10 = slicedToArray_default()(_useState9, 2),
    listCard = _useState10[0],
    setListCard = _useState10[1];
  var _useState11 = (0,react.useState)(),
    _useState12 = slicedToArray_default()(_useState11, 2),
    imageUrl = _useState12[0],
    setImageUrl = _useState12[1];
  var _useState13 = (0,react.useState)(false),
    _useState14 = slicedToArray_default()(_useState13, 2),
    uploading = _useState14[0],
    setUploading = _useState14[1];
  var _useState15 = (0,react.useState)([]),
    _useState16 = slicedToArray_default()(_useState15, 2),
    fileList = _useState16[0],
    setFileList = _useState16[1];
  (0,react.useEffect)(function () {
    if (Object.keys(params.customer).length) {
      form.setFieldsValue(params.customer);
    }
    setImageUrl(params.customer.image || '');
    var locations = params.customer.location ? params.customer.location.split(',') : [];
    form.setFieldValue('location', locations);
  }, [params.customer]);

  // const listCardID = async () => {
  //   try {
  //     setLoading(true);
  //     const result = await sscriptGeneralList({
  //       doc_name: 'iot_visitor_card',
  //       filters: [],
  //       page: 1,
  //       size: 1000,
  //       fields: ['*'],
  //     });
  //     const carddata = result.data.map((s: any) => {
  //       s.search_text = toLowerCaseNonAccentVietnamese(s.name);
  //       return s;
  //     });
  //     setListCard(carddata);
  //   } catch (error) {
  //     console.log(error);
  //   } finally {
  //     setLoading(false);
  //   }
  // };
  // useEffect(() => {
  //   listCardID();
  // }, []);

  // const handleSearch = async (newValue: string) => {
  //   try {
  //     setSearching(true);
  //     if (newValue) {
  //       const search_text = toLowerCaseNonAccentVietnamese(newValue.trim());
  //       let data = listCard.filter((s: any) => {
  //         return s.search_text?.indexOf(search_text) >= 0;
  //       });
  //       let data_select: any = data.map((d: any) => {
  //         return {
  //           value: d.name,
  //           label: \`\${d.name}\`,
  //         };
  //       });
  //       // console.log({ newValue, data });
  //       setOptions(data_select);
  //     } else {
  //       setOptions([]);
  //     }
  //   } catch (error) {
  //   } finally {
  //     setSearching(false);
  //   }
  // };
  // const handleChange = (newValue: string) => {
  //   form.setFieldValue('card_id', newValue);
  //   setIdCardCode(newValue);
  // };

  var handleRemove = /*#__PURE__*/function () {
    var _ref = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(file) {
      return regeneratorRuntime_default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            if (!(file.status === 'done')) {
              _context.next = 11;
              break;
            }
            _context.prev = 1;
            _context.next = 4;
            return (0,uploadFile/* removeFile */.Yd)({
              fid: file.uid,
              dt: '',
              dn: ''
            });
          case 4:
            _context.next = 9;
            break;
          case 6:
            _context.prev = 6;
            _context.t0 = _context["catch"](1);
            message/* default */.ZP.error('Delete Error,try again!');
          case 9:
            _context.prev = 9;
            return _context.finish(9);
          case 11:
          case "end":
            return _context.stop();
        }
      }, _callee, null, [[1, 6, 9, 11]]);
    }));
    return function handleRemove(_x) {
      return _ref.apply(this, arguments);
    };
  }();
  var handleChangeUpload = function handleChangeUpload(info) {
    console.log('handleChangeUpload', info);
    var newFileList = toConsumableArray_default()(info.fileList);
    // 1. Limit the number of uploaded files
    // Only to show two recent uploaded files, and old ones will be replaced by the new
    // 2. Read from response and show file link
    newFileList = newFileList.map(function (file) {
      if (file.response) {
        // Component will show file.url as link
        file.uid = file.response.name;
        var userdata = JSON.parse(localStorage.getItem('token') || '{}');
        file.url = (0,utils/* generateAPIPath */.rH)('api/v2/file/download?file_url=' + file.response.file_url + '&token=' + (userdata === null || userdata === void 0 ? void 0 : userdata.token));
        file.raw_url = file.response.file_url;
      }
      return file;
    });
    setFileList(newFileList);
    if (newFileList.length) {
      var _newFileList$;
      setImageUrl(((_newFileList$ = newFileList[0]) === null || _newFileList$ === void 0 ? void 0 : _newFileList$.url) || '');
    }
  };
  var handleUpload = /*#__PURE__*/function () {
    var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2(options) {
      var onSuccess, onError, file, res;
      return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            onSuccess = options.onSuccess, onError = options.onError, file = options.file;
            _context2.prev = 1;
            setUploading(true);
            _context2.next = 5;
            return (0,uploadFile/* uploadFile */.cT)({
              file: file,
              doctype: '',
              docname: '',
              is_private: 1,
              folder: 'Home/Attachments',
              optimize: false
            });
          case 5:
            res = _context2.sent;
            console.log('file_url', res);
            onSuccess(res.message);
            _context2.next = 14;
            break;
          case 10:
            _context2.prev = 10;
            _context2.t0 = _context2["catch"](1);
            console.log('Eroor: ', _context2.t0);
            onError({
              err: _context2.t0
            });
          case 14:
            _context2.prev = 14;
            setUploading(false);
            return _context2.finish(14);
          case 17:
          case "end":
            return _context2.stop();
        }
      }, _callee2, null, [[1, 10, 14, 17]]);
    }));
    return function handleUpload(_x2) {
      return _ref2.apply(this, arguments);
    };
  }();
  var handleOk = function handleOk() {
    form.submit();
  };
  var uploadButton = /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
    children: [uploading ? /*#__PURE__*/(0,jsx_runtime.jsx)(LoadingOutlined/* default */.Z, {}) : /*#__PURE__*/(0,jsx_runtime.jsx)(PlusOutlined/* default */.Z, {}), /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
      style: {
        marginTop: 8
      },
      children: "Upload"
    })]
  });
  var access = (0,_umi_production_exports.useAccess)();
  var canUpdate = access.canUpdateInVisitorManagement();
  var intl = (0,_umi_production_exports.useIntl)();
  return /*#__PURE__*/(0,jsx_runtime.jsx)(jsx_runtime.Fragment, {
    children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_form/* default */.Z, {
      layout: "horizontal",
      labelCol: {
        span: 24
      },
      labelAlign: "left",
      form: form,
      onFinish: ( /*#__PURE__*/function () {
        var _ref3 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee3(value) {
          var result;
          return regeneratorRuntime_default()().wrap(function _callee3$(_context3) {
            while (1) switch (_context3.prev = _context3.next) {
              case 0:
                _context3.prev = 0;
                if (canUpdate) {
                  _context3.next = 4;
                  break;
                }
                message/* default */.ZP.error('You do not have permission to update!');
                return _context3.abrupt("return");
              case 4:
                value.image = '';
                if (fileList.length) {
                  value.image = fileList[0].raw_url;
                }
                value.join_date = dayjs_min_default()(value.join_date).format('YYYY-MM-DD');
                value.expired_day = dayjs_min_default()(value.expired_day).format('YYYY-MM-DD');
                value.birthday = dayjs_min_default()(value.birthday).format('YYYY-MM-DD');
                value.user_status = 'updated';
                value.name = params.customer.name;
                value.location = value.location.join(',');
                _context3.next = 14;
                return visitor/* visitorInforService */.tO.update(value);
              case 14:
                result = _context3.sent;
                message/* default */.ZP.success('Success!');
                if (!params.refreshFnc) {
                  _context3.next = 19;
                  break;
                }
                _context3.next = 19;
                return params.refreshFnc();
              case 19:
                _context3.next = 24;
                break;
              case 21:
                _context3.prev = 21;
                _context3.t0 = _context3["catch"](0);
                message/* default */.ZP.error('M\xE3 th\u1EBB \u0111\xE3 \u0111\u01B0\u1EE3c s\u1EED d\u1EE5ng');
              case 24:
                _context3.prev = 24;
                setLoading(false);
                return _context3.finish(24);
              case 27:
              case "end":
                return _context3.stop();
            }
          }, _callee3, null, [[0, 21, 24, 27]]);
        }));
        return function (_x3) {
          return _ref3.apply(this, arguments);
        };
      }()),
      children: /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
        gutter: 5,
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
          className: "gutter-row",
          md: 8,
          style: {
            display: 'flex',
            justifyContent: 'center'
          },
          children: /*#__PURE__*/(0,jsx_runtime.jsxs)(space/* default */.Z, {
            direction: "vertical",
            align: "center",
            size: "small",
            children: [/*#__PURE__*/(0,jsx_runtime.jsx)(Item, {
              labelCol: {
                span: 24
              },
              name: "image",
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(upload/* default */.Z, {
                name: "upload-image",
                listType: "picture-card",
                className: "avatar-uploader",
                showUploadList: false,
                customRequest: handleUpload,
                onRemove: handleRemove,
                onChange: handleChangeUpload,
                accept: "image/png, image/jpeg, image/svg+xml",
                children: imageUrl ? /*#__PURE__*/(0,jsx_runtime.jsx)("img", {
                  src: (0,utils/* generateAPIPath */.rH)('api/v2/file/download?file_url=' + imageUrl),
                  alt: "avatar",
                  style: {
                    width: '100%'
                  }
                }) : uploadButton
              })
            }), imageUrl ? /*#__PURE__*/(0,jsx_runtime.jsx)(jsx_runtime.Fragment, {
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
                onClick: function onClick() {
                  setFileList([]);
                  setImageUrl('');
                },
                children: /*#__PURE__*/(0,jsx_runtime.jsx)(DeleteFilled/* default */.Z, {})
              })
            }) : /*#__PURE__*/(0,jsx_runtime.jsx)(jsx_runtime.Fragment, {}), /*#__PURE__*/(0,jsx_runtime.jsx)(Title, {
              level: 5,
              children: params.customer.fullname
            }), /*#__PURE__*/(0,jsx_runtime.jsxs)(Text, {
              strong: true,
              children: ["#", params.customer.card_id]
            })]
          })
        }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
          className: "gutter-row",
          md: 16,
          children: /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
            gutter: 5,
            children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
              className: "gutter-row",
              md: 12,
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(Item, {
                label: intl.formatMessage({
                  id: 'common.card_id'
                }),
                labelCol: {
                  span: 24
                },
                rules: [{
                  required: true,
                  message: 'B\u1EAFt bu\u1ED9c \u0111i\u1EC1n'
                }],
                name: "card_id",
                children: /*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
                  showSearch: true,
                  placeholder: 'Search to Select',
                  style: {
                    width: '100%'
                  },
                  request: /*#__PURE__*/asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee4() {
                    var result;
                    return regeneratorRuntime_default()().wrap(function _callee4$(_context4) {
                      while (1) switch (_context4.prev = _context4.next) {
                        case 0:
                          _context4.next = 2;
                          return visitor/* visitorCardService */.N_.getList();
                        case 2:
                          result = _context4.sent;
                          return _context4.abrupt("return", result.data.map(function (item) {
                            return {
                              label: item.card_number,
                              value: item.name
                            };
                          }));
                        case 4:
                        case "end":
                          return _context4.stop();
                      }
                    }, _callee4);
                  }))
                })
              })
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
              className: "gutter-row",
              md: 12,
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(Item, {
                label: intl.formatMessage({
                  id: 'common.status'
                }),
                labelCol: {
                  span: 24
                },
                rules: [{
                  required: true,
                  message: 'B\u1EAFt bu\u1ED9c \u0111i\u1EC1n'
                }],
                name: "user_status",
                children: /*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
                  showSearch: true,
                  placeholder: 'Search to Select',
                  style: {
                    width: '100%'
                  },
                  disabled: true
                })
              })
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
              className: "gutter-row",
              md: 12,
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(Item, {
                label: intl.formatMessage({
                  id: 'common.fullname'
                }),
                labelCol: {
                  span: 24
                },
                rules: [{
                  required: true,
                  message: 'B\u1EAFt bu\u1ED9c \u0111i\u1EC1n'
                }],
                name: "fullname",
                children: /*#__PURE__*/(0,jsx_runtime.jsx)(input/* default */.Z, {
                  placeholder: "Nguy\\u1EC5n V\\u0103n A"
                })
              })
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
              className: "gutter-row",
              md: 12,
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(Item, {
                label: intl.formatMessage({
                  id: 'common.gender'
                }),
                labelCol: {
                  span: 24
                },
                rules: [{
                  required: true,
                  message: 'B\u1EAFt bu\u1ED9c \u0111i\u1EC1n'
                }],
                name: "gender",
                children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_select/* default */.Z, {
                  options: [{
                    value: 'Nam'
                  }, {
                    value: 'N\u1EEF'
                  }]
                })
              })
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
              className: "gutter-row",
              md: 12,
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(Item, {
                label: intl.formatMessage({
                  id: 'common.birthday'
                }),
                labelCol: {
                  span: 24
                },
                name: "birthday",
                children: /*#__PURE__*/(0,jsx_runtime.jsx)(date_picker["default"], {
                  style: {
                    width: '100%'
                  }
                })
              })
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
              className: "gutter-row",
              md: 12,
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(Item, {
                label: intl.formatMessage({
                  id: 'common.address'
                }),
                labelCol: {
                  span: 24
                },
                name: "address",
                children: /*#__PURE__*/(0,jsx_runtime.jsx)(input/* default */.Z, {
                  placeholder: "568 L\\xFD Th\\u01B0\\u1EDDng K\\u1EC7t, Ph\\u01B0\\u1EDDng 14"
                })
              })
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
              className: "gutter-row",
              md: 12,
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(Item, {
                label: intl.formatMessage({
                  id: 'common.identity_card'
                }),
                labelCol: {
                  span: 24
                },
                name: "passport",
                children: /*#__PURE__*/(0,jsx_runtime.jsx)(input/* default */.Z, {})
              })
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
              className: "gutter-row",
              md: 12,
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(Item, {
                label: intl.formatMessage({
                  id: 'common.country'
                }),
                labelCol: {
                  span: 24
                },
                name: "country",
                children: /*#__PURE__*/(0,jsx_runtime.jsx)(input/* default */.Z, {
                  defaultValue: 'Vi\u1EC7t Nam'
                })
              })
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
              className: "gutter-row",
              md: 12,
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(Item, {
                label: intl.formatMessage({
                  id: 'common.join_date'
                }),
                labelCol: {
                  span: 24
                },
                rules: [{
                  required: true,
                  message: 'B\u1EAFt bu\u1ED9c \u0111i\u1EC1n'
                }],
                name: "join_date",
                children: /*#__PURE__*/(0,jsx_runtime.jsx)(date_picker["default"], {
                  disabled: true,
                  style: {
                    width: '100%'
                  }
                })
              })
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
              className: "gutter-row",
              md: 12,
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(Item, {
                label: intl.formatMessage({
                  id: 'common.expire_date'
                }),
                labelCol: {
                  span: 24
                },
                rules: [{
                  required: true,
                  message: 'B\u1EAFt bu\u1ED9c \u0111i\u1EC1n'
                }],
                name: "expired_day",
                children: /*#__PURE__*/(0,jsx_runtime.jsx)(date_picker["default"], {
                  disabled: params.isExpried,
                  style: {
                    width: '100%'
                  }
                })
              })
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
              className: "gutter-row",
              md: 12,
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(Item, {
                label: intl.formatMessage({
                  id: 'common.unit'
                }),
                labelCol: {
                  span: 24
                },
                name: "shareholder"
                // rules={[
                //   {
                //     required: true,
                //     message: 'B\u1EAFt bu\u1ED9c \u0111i\u1EC1n',
                //   },
                // ]}
                ,
                children: /*#__PURE__*/(0,jsx_runtime.jsx)(input/* default */.Z, {})
              })
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
              className: "gutter-row",
              md: 12,
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(Item, {
                label: intl.formatMessage({
                  id: 'common.role'
                }),
                labelCol: {
                  span: 24
                },
                name: "agency"
                // rules={[
                //   {
                //     required: true,
                //     message: 'B\u1EAFt bu\u1ED9c \u0111i\u1EC1n',
                //   },
                // ]}
                ,
                children: /*#__PURE__*/(0,jsx_runtime.jsx)(input/* default */.Z, {})
              })
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
              className: "gutter-row",
              md: 12,
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(Item, {
                label: intl.formatMessage({
                  id: 'common.location'
                }),
                labelCol: {
                  span: 24
                },
                name: "location",
                children: /*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
                  mode: "multiple",
                  showSearch: true,
                  request: /*#__PURE__*/asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee5() {
                    var result;
                    return regeneratorRuntime_default()().wrap(function _callee5$(_context5) {
                      while (1) switch (_context5.prev = _context5.next) {
                        case 0:
                          _context5.next = 2;
                          return visitor/* visitorLocationService */.v7.getList();
                        case 2:
                          result = _context5.sent;
                          return _context5.abrupt("return", result.data.map(function (item) {
                            return {
                              label: item.label,
                              value: item.name
                            };
                          }));
                        case 4:
                        case "end":
                          return _context5.stop();
                      }
                    }, _callee5);
                  }))
                })
              })
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
              className: "gutter-row",
              md: 24,
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
                loading: loading,
                type: "primary",
                htmlType: "submit",
                style: {
                  width: '100%'
                },
                children: intl.formatMessage({
                  id: 'common.save'
                })
              })
            })]
          })
        })]
      })
    })
  });
};
/* harmony default export */ var UpdatePakingCustomerForm = (UpdateForm);
;// CONCATENATED MODULE: ./src/pages/Visitor/PakingCustomer/Detail/index.tsx
















var CustomerCompany = function CustomerCompany() {
  var _useState = (0,react.useState)(false),
    _useState2 = slicedToArray_default()(_useState, 2),
    loading = _useState2[0],
    setLoading = _useState2[1];
  var _useSearchParams = (0,_umi_production_exports.useSearchParams)(),
    _useSearchParams2 = slicedToArray_default()(_useSearchParams, 2),
    searchParams = _useSearchParams2[0],
    setSearchParams = _useSearchParams2[1];
  var _useState3 = (0,react.useState)({}),
    _useState4 = slicedToArray_default()(_useState3, 2),
    customer = _useState4[0],
    setCustomer = _useState4[1];
  var _useState5 = (0,react.useState)(true),
    _useState6 = slicedToArray_default()(_useState5, 2),
    expried = _useState6[0],
    setExpried = _useState6[1];
  var _useState7 = (0,react.useState)(''),
    _useState8 = slicedToArray_default()(_useState7, 2),
    img = _useState8[0],
    setImg = _useState8[1];
  var _useState9 = (0,react.useState)(''),
    _useState10 = slicedToArray_default()(_useState9, 2),
    id = _useState10[0],
    setId = _useState10[1];
  var name = searchParams.get('customer_name');
  var refreshData = /*#__PURE__*/function () {
    var _ref = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
      var filter, result, customer_infor, user_token, full_url;
      return regeneratorRuntime_default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _context.prev = 0;
            setLoading(true);
            // const result = await sscriptGeneralList({
            //   doc_name: 'iot_visitor_infor',
            //   filters: [['iot_visitor_infor', 'name', 'like', \`\${name}\`]],
            //   page: 1,
            //   size: 1,
            //   fields: ['*'],
            // });
            filter = JSON.stringify([['iot_visitor_infor', 'name', 'like', "".concat(name)]]);
            _context.next = 5;
            return visitor/* visitorInforService */.tO.getList({
              filters: filter
            });
          case 5:
            result = _context.sent;
            console.log('mother er', result);
            if (result.data.length > 0) {
              customer_infor = result.data[0];
              customer_infor.birthday = dayjs_min_default()(customer_infor.birthday);
              customer_infor.join_date = dayjs_min_default()(customer_infor.join_date);
              customer_infor.expired_day = dayjs_min_default()(customer_infor.expired_day);
              setCustomer(customer_infor);
              user_token = JSON.parse(localStorage.getItem('token')).token;
              full_url = "".concat(utils/* HOST */.M5, "/api/file/download?token=") + user_token + "&file_url=" + customer_infor.avata + '&dt=' + 'parking_hoivien' + '&dn=' + customer_infor.name;
              setImg(full_url);
              setId(customer_infor.name);
            }
            _context.next = 13;
            break;
          case 10:
            _context.prev = 10;
            _context.t0 = _context["catch"](0);
            console.log(_context.t0);
          case 13:
            _context.prev = 13;
            setLoading(false);
            return _context.finish(13);
          case 16:
          case "end":
            return _context.stop();
        }
      }, _callee, null, [[0, 10, 13, 16]]);
    }));
    return function refreshData() {
      return _ref.apply(this, arguments);
    };
  }();
  (0,react.useEffect)(function () {
    refreshData();
  }, []);
  var handleActive = function handleActive() {
    setExpried(function (pre_val) {
      return !pre_val;
    });
  };
  var access = (0,_umi_production_exports.useAccess)();
  var canDelete = access.canDeleteInVisitorManagement();
  if (name) {
    return /*#__PURE__*/(0,jsx_runtime.jsx)(PageContainer/* PageContainer */._z, {
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(card/* default */.Z, {
        title: name,
        loading: loading,
        extra: /*#__PURE__*/(0,jsx_runtime.jsx)(space/* default */.Z, {
          children: canDelete && /*#__PURE__*/(0,jsx_runtime.jsx)(RemovePakingCustomer, {
            name: name,
            refreshFnc: function refreshFnc() {
              _umi_production_exports.history.push('/employee-management/visitor-management/member/all');
            }
          })
        }),
        children: Object.keys(customer).length ? /*#__PURE__*/(0,jsx_runtime.jsx)(jsx_runtime.Fragment, {
          children: /*#__PURE__*/(0,jsx_runtime.jsxs)(tabs/* default */.Z, {
            defaultActiveKey: "1",
            children: [/*#__PURE__*/(0,jsx_runtime.jsx)(tabs/* default */.Z.TabPane, {
              tab: "Th\\xF4ng tin chi ti\\u1EBFt",
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(UpdatePakingCustomerForm, {
                refreshFnc: refreshData,
                customer: customer,
                isExpried: expried
              })
            }, "1"), /*#__PURE__*/(0,jsx_runtime.jsx)(tabs/* default */.Z.TabPane, {
              tab: "\\u1EA2nh Passport",
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(row/* default */.Z, {
                children: /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
                  md: 12,
                  children: /*#__PURE__*/(0,jsx_runtime.jsx)(AttachmentsOfDoctype_AttachPictureOfDoctype, {
                    docname: name,
                    doctype: "iot_visitor_infor"
                  })
                })
              })
            }, "5")]
          })
        }) : /*#__PURE__*/(0,jsx_runtime.jsx)(jsx_runtime.Fragment, {
          children: "Unkown Error, Reload and try Again"
        })
      })
    });
  } else {
    return /*#__PURE__*/(0,jsx_runtime.jsx)(jsx_runtime.Fragment, {
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(result/* default */.ZP, {
        status: "404",
        title: "404",
        subTitle: "Sorry, the page you visited does not exist.",
        extra: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.Link, {
          to: '/customer/company',
          type: "primary",
          children: "Go Back"
        })
      })
    });
  }
};
/* harmony default export */ var Detail = (CustomerCompany);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNjk2Mi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQWlEO0FBQ0c7QUFDUjtBQUV1RDtBQUczRDtBQUNEO0FBQUE7QUFBQTtBQUFBO0FBRXZDLElBQU1tQixTQUFTLEdBQUcsU0FBWkEsU0FBU0EsQ0FBSUMsSUFBWTtFQUFBLE9BQzdCLElBQUlDLE9BQU8sQ0FBQyxVQUFDQyxPQUFPLEVBQUVDLE1BQU0sRUFBSztJQUMvQixJQUFNQyxNQUFNLEdBQUcsSUFBSUMsVUFBVSxDQUFDLENBQUM7SUFDL0JELE1BQU0sQ0FBQ0UsYUFBYSxDQUFDTixJQUFJLENBQUM7SUFDMUJJLE1BQU0sQ0FBQ0csTUFBTSxHQUFHO01BQUEsT0FBTUwsT0FBTyxDQUFDRSxNQUFNLENBQUNJLE1BQWdCLENBQUM7SUFBQTtJQUN0REosTUFBTSxDQUFDSyxPQUFPLEdBQUcsVUFBQ0MsS0FBSztNQUFBLE9BQUtQLE1BQU0sQ0FBQ08sS0FBSyxDQUFDO0lBQUE7RUFDM0MsQ0FBQyxDQUFDO0FBQUE7QUFFSixJQUFNQyxzQkFBc0IsR0FBRyxTQUF6QkEsc0JBQXNCQSxDQUFBQyxJQUFBLEVBWXRCO0VBQUEsSUFYSkMsT0FBTyxHQUFBRCxJQUFBLENBQVBDLE9BQU87SUFDUEMsT0FBTyxHQUFBRixJQUFBLENBQVBFLE9BQU87SUFBQUMsZUFBQSxHQUFBSCxJQUFBLENBQ1BJLFVBQVU7SUFBVkEsVUFBVSxHQUFBRCxlQUFBLGNBQUcsQ0FBQyxHQUFBQSxlQUFBO0lBQUFFLFdBQUEsR0FBQUwsSUFBQSxDQUNkTSxNQUFNO0lBQU5BLE1BQU0sR0FBQUQsV0FBQSxjQUFHLGtCQUFrQixHQUFBQSxXQUFBO0lBQUFFLGFBQUEsR0FBQVAsSUFBQSxDQUMzQlEsUUFBUTtJQUFSQSxRQUFRLEdBQUFELGFBQUEsY0FBRyxLQUFLLEdBQUFBLGFBQUE7RUFRaEIsSUFBQUUsU0FBQSxHQUE4Qm5DLGtCQUFRLENBQUMsS0FBSyxDQUFDO0lBQUFvQyxVQUFBLEdBQUFDLHVCQUFBLENBQUFGLFNBQUE7SUFBdENHLE9BQU8sR0FBQUYsVUFBQTtJQUFFRyxVQUFVLEdBQUFILFVBQUE7RUFDMUIsSUFBQUksVUFBQSxHQUFrQ3hDLGtCQUFRLENBQUMsS0FBSyxDQUFDO0lBQUF5QyxVQUFBLEdBQUFKLHVCQUFBLENBQUFHLFVBQUE7SUFBMUNFLFNBQVMsR0FBQUQsVUFBQTtJQUFFRSxZQUFZLEdBQUFGLFVBQUE7RUFDOUIsSUFBQUcsVUFBQSxHQUFnQzVDLGtCQUFRLENBQWUsRUFBRSxDQUFDO0lBQUE2QyxVQUFBLEdBQUFSLHVCQUFBLENBQUFPLFVBQUE7SUFBbkRFLFFBQVEsR0FBQUQsVUFBQTtJQUFFRSxXQUFXLEdBQUFGLFVBQUE7RUFDNUIsSUFBQUcsVUFBQSxHQUFzQ2hELGtCQUFRLENBQUMsS0FBSyxDQUFDO0lBQUFpRCxVQUFBLEdBQUFaLHVCQUFBLENBQUFXLFVBQUE7SUFBOUNFLFdBQVcsR0FBQUQsVUFBQTtJQUFFRSxjQUFjLEdBQUFGLFVBQUE7RUFDbEMsSUFBQUcsVUFBQSxHQUF3Q3BELGtCQUFRLENBQUMsRUFBRSxDQUFDO0lBQUFxRCxXQUFBLEdBQUFoQix1QkFBQSxDQUFBZSxVQUFBO0lBQTdDRSxZQUFZLEdBQUFELFdBQUE7SUFBRUUsZUFBZSxHQUFBRixXQUFBO0VBQ3BDLElBQUFHLFdBQUEsR0FBd0N4RCxrQkFBUSxDQUFDLEVBQUUsQ0FBQztJQUFBeUQsV0FBQSxHQUFBcEIsdUJBQUEsQ0FBQW1CLFdBQUE7SUFBN0NFLFlBQVksR0FBQUQsV0FBQTtJQUFFRSxlQUFlLEdBQUFGLFdBQUE7RUFDcEMsSUFBQUcsV0FBQSxHQUFnQzVELGtCQUFRLENBQUMsS0FBSyxDQUFDO0lBQUE2RCxXQUFBLEdBQUF4Qix1QkFBQSxDQUFBdUIsV0FBQTtJQUF4Q0UsUUFBUSxHQUFBRCxXQUFBO0lBQUVFLFdBQVcsR0FBQUYsV0FBQTtFQUU1QixJQUFNRyxVQUFVLEdBQUdDLElBQUksQ0FBQ0MsS0FBSyxDQUFDQyxZQUFZLENBQUNDLE9BQU8sQ0FBQyxPQUFPLENBQUMsQ0FBQyxDQUFDQyxLQUFLO0VBQ2xFLElBQU1DLFdBQVc7SUFBQSxJQUFBQyxLQUFBLEdBQUFDLDBCQUFBLGVBQUFDLDRCQUFBLEdBQUFDLElBQUEsQ0FBRyxTQUFBQyxRQUFBO01BQUEsSUFBQXJELE1BQUEsRUFBQXNELFVBQUEsRUFBQUMsU0FBQTtNQUFBLE9BQUFKLDRCQUFBLEdBQUFLLElBQUEsVUFBQUMsU0FBQUMsUUFBQTtRQUFBLGtCQUFBQSxRQUFBLENBQUFDLElBQUEsR0FBQUQsUUFBQSxDQUFBRSxJQUFBO1VBQUE7WUFBQUYsUUFBQSxDQUFBQyxJQUFBO1lBRWhCbEIsV0FBVyxDQUFDLElBQUksQ0FBQztZQUFDaUIsUUFBQSxDQUFBRSxJQUFBO1lBQUEsT0FDR2hGLDJDQUFvQixDQUFDO2NBQUV5QixPQUFPLEVBQVBBLE9BQU87Y0FBRXdELElBQUksRUFBRXZEO1lBQVEsQ0FBQyxDQUFDO1VBQUE7WUFBL0ROLE1BQU0sR0FBQTBELFFBQUEsQ0FBQUksSUFBQTtZQUNaO1lBQ0lSLFVBQWUsR0FBRyxFQUFFO1lBQ2xCQyxTQUFTLEdBQUd2RCxNQUFNLENBQUMrRCxHQUFHLENBQUMsVUFBQ0MsQ0FBTSxFQUFLO2NBQ3ZDLElBQUlBLENBQUMsQ0FBQ0MsUUFBUSxDQUFDQyxNQUFNLENBQUMsU0FBUyxDQUFDLEtBQUssQ0FBQyxDQUFDLEVBQUU7Z0JBQ3ZDWixVQUFVLENBQUNhLElBQUksQ0FBQztrQkFDZEMsR0FBRyxFQUFFSixDQUFDLENBQUNILElBQUk7a0JBQ1hBLElBQUksRUFBRUcsQ0FBQyxDQUFDSyxTQUFTO2tCQUNqQkMsSUFBSSxFQUFFLFdBQVc7a0JBQ2pCQyxNQUFNLEVBQUUsTUFBTTtrQkFDZEMsR0FBRyxFQUNELEdBQUFDLE1BQUEsQ0FBRzFGLGtCQUFJLG9DQUNQMkQsVUFBVSxlQUNFLEdBQ1pzQixDQUFDLENBQUNDLFFBQVEsR0FDVixNQUFNLEdBQ041RCxPQUFPLEdBQ1AsTUFBTSxHQUNOQztnQkFDSixDQUFDLENBQUM7Y0FDSjtjQUNBLE9BQU87Z0JBQ0w4RCxHQUFHLEVBQUVKLENBQUMsQ0FBQ0gsSUFBSTtnQkFDWEEsSUFBSSxFQUFFRyxDQUFDLENBQUNLLFNBQVM7Z0JBQ2pCQyxJQUFJLEVBQUUsV0FBVztnQkFDakJDLE1BQU0sRUFBRSxNQUFNO2dCQUNkQyxHQUFHLEVBQ0QsR0FBQUMsTUFBQSxDQUFHMUYsa0JBQUksb0NBQ1AyRCxVQUFVLGVBQ0UsR0FDWnNCLENBQUMsQ0FBQ0MsUUFBUSxHQUNWLE1BQU0sR0FDTjVELE9BQU8sR0FDUCxNQUFNLEdBQ05DO2NBQ0osQ0FBQztZQUNILENBQUMsQ0FBQztZQUNGbUIsV0FBVyxDQUFDNkIsVUFBVSxDQUFDO1lBQ3ZCO1lBQUFJLFFBQUEsQ0FBQUUsSUFBQTtZQUFBO1VBQUE7WUFBQUYsUUFBQSxDQUFBQyxJQUFBO1lBQUFELFFBQUEsQ0FBQWdCLEVBQUEsR0FBQWhCLFFBQUE7VUFBQTtZQUFBQSxRQUFBLENBQUFDLElBQUE7WUFHQWxCLFdBQVcsQ0FBQyxLQUFLLENBQUM7WUFBQyxPQUFBaUIsUUFBQSxDQUFBaUIsTUFBQTtVQUFBO1VBQUE7WUFBQSxPQUFBakIsUUFBQSxDQUFBa0IsSUFBQTtRQUFBO01BQUEsR0FBQXZCLE9BQUE7SUFBQSxDQUV0QjtJQUFBLGdCQTlDS0wsV0FBV0EsQ0FBQTtNQUFBLE9BQUFDLEtBQUEsQ0FBQTRCLEtBQUEsT0FBQUMsU0FBQTtJQUFBO0VBQUEsR0E4Q2hCO0VBQ0RyRyxtQkFBUyxDQUFDLFlBQU07SUFDZHVFLFdBQVcsQ0FBQyxDQUFDO0VBQ2YsQ0FBQyxFQUFFLENBQUM1QixTQUFTLENBQUMsQ0FBQztFQUVmLElBQU0yRCxZQUFxQyxHQUFHLFNBQXhDQSxZQUFxQ0EsQ0FBSUMsSUFBSSxFQUFLO0lBQ3RELElBQUlDLFdBQVcsR0FBQUMsMkJBQUEsQ0FBT0YsSUFBSSxDQUFDeEQsUUFBUSxDQUFDOztJQUVwQztJQUNBO0lBQ0E7SUFDQXlELFdBQVcsR0FBR0EsV0FBVyxDQUFDbEIsR0FBRyxDQUFDLFVBQUN2RSxJQUFTLEVBQUs7TUFDM0M7TUFDQSxJQUFJQSxJQUFJLENBQUMyRixRQUFRLEVBQUU7UUFDakI7UUFDQTNGLElBQUksQ0FBQzRFLEdBQUcsR0FBRzVFLElBQUksQ0FBQzJGLFFBQVEsQ0FBQ3RCLElBQUk7UUFDN0JyRSxJQUFJLENBQUNnRixHQUFHLEdBQ04sR0FBQUMsTUFBQSxDQUFHMUYsa0JBQUksb0NBQ1AyRCxVQUFVLGVBQ0UsR0FDWmxELElBQUksQ0FBQzJGLFFBQVEsQ0FBQ2xCLFFBQVEsR0FDdEIsTUFBTSxHQUNONUQsT0FBTyxHQUNQLE1BQU0sR0FDTkMsT0FBTztNQUNYO01BQ0EsT0FBT2QsSUFBSTtJQUNiLENBQUMsQ0FBQztJQUNGNEYsT0FBTyxDQUFDQyxHQUFHLENBQUNKLFdBQVcsQ0FBQztJQUN4QnhELFdBQVcsQ0FBQ3dELFdBQVcsQ0FBQztFQUMxQixDQUFDO0VBRUQsSUFBTUssWUFBWTtJQUFBLElBQUFDLEtBQUEsR0FBQXJDLDBCQUFBLGVBQUFDLDRCQUFBLEdBQUFDLElBQUEsQ0FBRyxTQUFBb0MsU0FBT0MsT0FBWTtNQUFBLElBQUFDLFNBQUEsRUFBQUMsT0FBQSxFQUFBbkcsSUFBQSxFQUFBb0csVUFBQSxFQUFBQyxHQUFBLEVBQUEzRixLQUFBO01BQUEsT0FBQWlELDRCQUFBLEdBQUFLLElBQUEsVUFBQXNDLFVBQUFDLFNBQUE7UUFBQSxrQkFBQUEsU0FBQSxDQUFBcEMsSUFBQSxHQUFBb0MsU0FBQSxDQUFBbkMsSUFBQTtVQUFBO1lBQzlCOEIsU0FBUyxHQUFnQ0QsT0FBTyxDQUFoREMsU0FBUyxFQUFFQyxPQUFPLEdBQXVCRixPQUFPLENBQXJDRSxPQUFPLEVBQUVuRyxJQUFJLEdBQWlCaUcsT0FBTyxDQUE1QmpHLElBQUksRUFBRW9HLFVBQVUsR0FBS0gsT0FBTyxDQUF0QkcsVUFBVTtZQUFBRyxTQUFBLENBQUFwQyxJQUFBO1lBRTFDdEMsWUFBWSxDQUFDLElBQUksQ0FBQztZQUFDMEUsU0FBQSxDQUFBbkMsSUFBQTtZQUFBLE9BQ0Q5RSxpQ0FBVSxDQUFDO2NBQzNCVSxJQUFJLEVBQUpBLElBQUk7Y0FDSmEsT0FBTyxFQUFQQSxPQUFPO2NBQ1BDLE9BQU8sRUFBUEEsT0FBTztjQUNQRSxVQUFVLEVBQVZBLFVBQVU7Y0FDVkUsTUFBTSxFQUFOQSxNQUFNO2NBQ05FLFFBQVEsRUFBUkE7WUFDRixDQUFDLENBQUM7VUFBQTtZQVBJaUYsR0FBRyxHQUFBRSxTQUFBLENBQUFqQyxJQUFBO1lBU1Q0QixTQUFTLENBQUNHLEdBQUcsQ0FBQ3ZILE9BQU8sQ0FBQztZQUFDeUgsU0FBQSxDQUFBbkMsSUFBQTtZQUFBO1VBQUE7WUFBQW1DLFNBQUEsQ0FBQXBDLElBQUE7WUFBQW9DLFNBQUEsQ0FBQXJCLEVBQUEsR0FBQXFCLFNBQUE7WUFFdkJYLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLFNBQVMsRUFBQVUsU0FBQSxDQUFBckIsRUFBSyxDQUFDO1lBQ3JCeEUsS0FBSyxHQUFHLElBQUk4RixLQUFLLENBQUMsWUFBWSxDQUFDO1lBQ3JDTCxPQUFPLENBQUM7Y0FBRU0sR0FBRyxFQUFBRixTQUFBLENBQUFyQjtZQUFDLENBQUMsQ0FBQztVQUFDO1lBQUFxQixTQUFBLENBQUFwQyxJQUFBO1lBRWpCdEMsWUFBWSxDQUFDLEtBQUssQ0FBQztZQUFDLE9BQUEwRSxTQUFBLENBQUFwQixNQUFBO1VBQUE7VUFBQTtZQUFBLE9BQUFvQixTQUFBLENBQUFuQixJQUFBO1FBQUE7TUFBQSxHQUFBWSxRQUFBO0lBQUEsQ0FFdkI7SUFBQSxnQkFyQktGLFlBQVlBLENBQUFZLEVBQUE7TUFBQSxPQUFBWCxLQUFBLENBQUFWLEtBQUEsT0FBQUMsU0FBQTtJQUFBO0VBQUEsR0FxQmpCO0VBRUQsSUFBTXFCLFlBQVksR0FBRyxTQUFmQSxZQUFZQSxDQUFBO0lBQUEsT0FBU3RFLGNBQWMsQ0FBQyxLQUFLLENBQUM7RUFBQTtFQUVoRCxJQUFNdUUsYUFBYTtJQUFBLElBQUFDLEtBQUEsR0FBQW5ELDBCQUFBLGVBQUFDLDRCQUFBLEdBQUFDLElBQUEsQ0FBRyxTQUFBa0QsU0FBTzlHLElBQWdCO01BQUEsT0FBQTJELDRCQUFBLEdBQUFLLElBQUEsVUFBQStDLFVBQUFDLFNBQUE7UUFBQSxrQkFBQUEsU0FBQSxDQUFBN0MsSUFBQSxHQUFBNkMsU0FBQSxDQUFBNUMsSUFBQTtVQUFBO1lBQUEsTUFDdkMsQ0FBQ3BFLElBQUksQ0FBQ2dGLEdBQUcsSUFBSSxDQUFDaEYsSUFBSSxDQUFDaUgsT0FBTztjQUFBRCxTQUFBLENBQUE1QyxJQUFBO2NBQUE7WUFBQTtZQUFBNEMsU0FBQSxDQUFBNUMsSUFBQTtZQUFBLE9BQ1ByRSxTQUFTLENBQUNDLElBQUksQ0FBQ2tILGFBQXVCLENBQUM7VUFBQTtZQUE1RGxILElBQUksQ0FBQ2lILE9BQU8sR0FBQUQsU0FBQSxDQUFBMUMsSUFBQTtVQUFBO1lBRWRzQixPQUFPLENBQUNDLEdBQUcsQ0FBQyxXQUFXLEVBQUU3RixJQUFJLENBQUNnRixHQUFHLENBQUM7WUFDbEN2QyxlQUFlLENBQUN6QyxJQUFJLENBQUNnRixHQUFHLElBQUtoRixJQUFJLENBQUNpSCxPQUFrQixDQUFDO1lBQ3JENUUsY0FBYyxDQUFDLElBQUksQ0FBQztZQUNwQlEsZUFBZSxDQUFDN0MsSUFBSSxDQUFDcUUsSUFBSSxJQUFJckUsSUFBSSxDQUFDZ0YsR0FBRyxDQUFFbUMsU0FBUyxDQUFDbkgsSUFBSSxDQUFDZ0YsR0FBRyxDQUFFb0MsV0FBVyxDQUFDLEdBQUcsQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDO1VBQUM7VUFBQTtZQUFBLE9BQUFKLFNBQUEsQ0FBQTVCLElBQUE7UUFBQTtNQUFBLEdBQUEwQixRQUFBO0lBQUEsQ0FDbkY7SUFBQSxnQkFSS0YsYUFBYUEsQ0FBQVMsR0FBQTtNQUFBLE9BQUFSLEtBQUEsQ0FBQXhCLEtBQUEsT0FBQUMsU0FBQTtJQUFBO0VBQUEsR0FRbEI7RUFFRCxJQUFNZ0MsS0FBa0IsR0FBRztJQUN6QkMsUUFBUTtNQUFBLElBQUFDLFNBQUEsR0FBQTlELDBCQUFBLGVBQUFDLDRCQUFBLEdBQUFDLElBQUEsQ0FBRSxTQUFBNkQsU0FBT3pILElBQUk7UUFBQSxJQUFBeUYsV0FBQTtRQUFBLE9BQUE5Qiw0QkFBQSxHQUFBSyxJQUFBLFVBQUEwRCxVQUFBQyxTQUFBO1VBQUEsa0JBQUFBLFNBQUEsQ0FBQXhELElBQUEsR0FBQXdELFNBQUEsQ0FBQXZELElBQUE7WUFBQTtjQUFBLE1BQ2ZwRSxJQUFJLENBQUMrRSxNQUFNLEtBQUssTUFBTTtnQkFBQTRDLFNBQUEsQ0FBQXZELElBQUE7Z0JBQUE7Y0FBQTtjQUFBdUQsU0FBQSxDQUFBeEQsSUFBQTtjQUFBd0QsU0FBQSxDQUFBdkQsSUFBQTtjQUFBLE9BRWhCL0UsaUNBQVUsQ0FBQztnQkFDZnVJLEdBQUcsRUFBRTVILElBQUksQ0FBQzRFLEdBQUc7Z0JBQ2JpRCxFQUFFLEVBQUVoSCxPQUFPO2dCQUNYaUgsRUFBRSxFQUFFaEg7Y0FDTixDQUFDLENBQUM7WUFBQTtjQUNJMkUsV0FBVyxHQUFHekQsUUFBUSxDQUFDK0YsTUFBTSxDQUFDLFVBQUN2RCxDQUFDO2dCQUFBLE9BQUtBLENBQUMsQ0FBQ0ksR0FBRyxLQUFLNUUsSUFBSSxDQUFDNEUsR0FBRztjQUFBLEVBQUM7Y0FDOURnQixPQUFPLENBQUNDLEdBQUcsQ0FBQyxhQUFhLEVBQUVKLFdBQVcsQ0FBQztjQUN2Q3hELFdBQVcsQ0FBQXlELDJCQUFBLENBQUtELFdBQVcsQ0FBQyxDQUFDO2NBQUNrQyxTQUFBLENBQUF2RCxJQUFBO2NBQUE7WUFBQTtjQUFBdUQsU0FBQSxDQUFBeEQsSUFBQTtjQUFBd0QsU0FBQSxDQUFBekMsRUFBQSxHQUFBeUMsU0FBQTtjQUU5QjdJLHVCQUFPLENBQUM0QixLQUFLLENBQUMseUJBQXlCLENBQUM7WUFBQztjQUFBaUgsU0FBQSxDQUFBeEQsSUFBQTtjQUFBLE9BQUF3RCxTQUFBLENBQUF4QyxNQUFBO1lBQUE7WUFBQTtjQUFBLE9BQUF3QyxTQUFBLENBQUF2QyxJQUFBO1VBQUE7UUFBQSxHQUFBcUMsUUFBQTtNQUFBLENBSTlDO01BQUEsU0FBQUYsU0FBQVMsR0FBQTtRQUFBLE9BQUFSLFNBQUEsQ0FBQW5DLEtBQUEsT0FBQUMsU0FBQTtNQUFBO01BQUEsT0FBQWlDLFFBQUE7SUFBQTtJQUNEdkYsUUFBUSxFQUFFQSxRQUFRO0lBQ2xCaUcsUUFBUSxFQUFFLElBQUk7SUFDZEMsYUFBYSxFQUFFcEMsWUFBWTtJQUUzQnFDLFFBQVEsRUFBRTVDLFlBQVk7SUFDdEI7SUFDQTtJQUNBO0lBQ0E7SUFDQTZDLFNBQVMsRUFBRXhCLGFBQWE7SUFDeEJ5QixRQUFRLEVBQUUsY0FBYztJQUN4QkMsVUFBVTtNQUFBLElBQUFDLFdBQUEsR0FBQTdFLDBCQUFBLGVBQUFDLDRCQUFBLEdBQUFDLElBQUEsQ0FBRSxTQUFBNEUsU0FBT3hJLElBQVM7UUFBQSxJQUFBcUcsR0FBQTtRQUFBLE9BQUExQyw0QkFBQSxHQUFBSyxJQUFBLFVBQUF5RSxVQUFBQyxTQUFBO1VBQUEsa0JBQUFBLFNBQUEsQ0FBQXZFLElBQUEsR0FBQXVFLFNBQUEsQ0FBQXRFLElBQUE7WUFBQTtjQUFBc0UsU0FBQSxDQUFBdkUsSUFBQTtjQUFBdUUsU0FBQSxDQUFBdEUsSUFBQTtjQUFBLE9BR05qRixtQ0FBWSxDQUFDO2dCQUM3QnNGLFFBQVEsRUFBRXpFLElBQUksQ0FBQ2dGLEdBQUc7Z0JBQ2xCNEMsR0FBRyxFQUFFNUgsSUFBSSxDQUFDNEgsR0FBRztnQkFDYkMsRUFBRSxFQUFFaEgsT0FBTztnQkFDWGlILEVBQUUsRUFBRWhIO2NBQ04sQ0FBQyxDQUFDO1lBQUE7Y0FMSXVGLEdBQUcsR0FBQXFDLFNBQUEsQ0FBQXBFLElBQUE7Y0FNVHNCLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDUSxHQUFHLENBQUM7Y0FBQ3FDLFNBQUEsQ0FBQXRFLElBQUE7Y0FBQTtZQUFBO2NBQUFzRSxTQUFBLENBQUF2RSxJQUFBO2NBQUF1RSxTQUFBLENBQUF4RCxFQUFBLEdBQUF3RCxTQUFBO2NBRWpCOUMsT0FBTyxDQUFDQyxHQUFHLENBQUMsU0FBUyxFQUFBNkMsU0FBQSxDQUFBeEQsRUFBSyxDQUFDO1lBQUM7Y0FBQXdELFNBQUEsQ0FBQXZFLElBQUE7Y0FBQSxPQUFBdUUsU0FBQSxDQUFBdkQsTUFBQTtZQUFBO1lBQUE7Y0FBQSxPQUFBdUQsU0FBQSxDQUFBdEQsSUFBQTtVQUFBO1FBQUEsR0FBQW9ELFFBQUE7TUFBQSxDQUcvQjtNQUFBLFNBQUFGLFdBQUFLLEdBQUE7UUFBQSxPQUFBSixXQUFBLENBQUFsRCxLQUFBLE9BQUFDLFNBQUE7TUFBQTtNQUFBLE9BQUFnRCxVQUFBO0lBQUE7RUFDSCxDQUFDO0VBQ0QsSUFBTU0sWUFBWSxnQkFDaEJoSixvQkFBQTtJQUFBaUosUUFBQSxnQkFDRW5KLG1CQUFBLENBQUNkLDJCQUFZLElBQUUsQ0FBQyxlQUNoQmMsbUJBQUE7TUFBS29KLEtBQUssRUFBRTtRQUFFQyxTQUFTLEVBQUU7TUFBRSxDQUFFO01BQUFGLFFBQUEsRUFBQztJQUFNLENBQUssQ0FBQztFQUFBLENBQ3ZDLENBQ047RUFDRCxJQUFNRyxNQUFNLEdBQUd4SixxQ0FBUyxDQUFDLENBQUM7RUFDMUIsSUFBTXlKLFNBQVMsR0FBR0QsTUFBTSxDQUFDRSw0QkFBNEIsQ0FBQyxDQUFDO0VBQ3ZELG9CQUNFeEosbUJBQUEsQ0FBQUksb0JBQUE7SUFBQStJLFFBQUEsZUFDRWpKLG9CQUFBLENBQUNmLG1CQUFJO01BQUNzSyxLQUFLLEVBQUMsVUFBSztNQUFDM0gsT0FBTyxFQUFFQSxPQUFRO01BQUFxSCxRQUFBLEdBQ2hDSSxTQUFTLGlCQUFJdkosbUJBQUEsQ0FBQ1YscUJBQU0sRUFBQW9LLHVCQUFBLENBQUFBLHVCQUFBLEtBQUs5QixLQUFLO1FBQUF1QixRQUFBLEVBQUc3RyxRQUFRLENBQUNxSCxNQUFNLElBQUksQ0FBQyxHQUFHLElBQUksR0FBR1Q7TUFBWSxFQUFTLENBQUMsZUFDdEZsSixtQkFBQSxDQUFDWCxvQkFBSztRQUFDdUssSUFBSSxFQUFFbEgsV0FBWTtRQUFDK0csS0FBSyxFQUFFdkcsWUFBYTtRQUFDMkcsTUFBTSxFQUFFLElBQUs7UUFBQ0MsUUFBUSxFQUFFN0MsWUFBYTtRQUFBa0MsUUFBQSxlQUNsRm5KLG1CQUFBO1VBQUsrSixHQUFHLEVBQUMsU0FBUztVQUFDWCxLQUFLLEVBQUU7WUFBRVksS0FBSyxFQUFFO1VBQU8sQ0FBRTtVQUFDQyxHQUFHLEVBQUVuSDtRQUFhLENBQUU7TUFBQyxDQUM3RCxDQUFDO0lBQUEsQ0FDSjtFQUFDLENBQ1AsQ0FBQztBQUVQLENBQUM7QUFFRCxnRkFBZTdCLHNCQUFzQixFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQzFOd0I7QUFDSjtBQUMxQjtBQUFBO0FBRS9CLElBQU1tSixVQUFVLEdBQUcsU0FBYkEsVUFBVUEsQ0FBSUMsTUFBeUMsRUFBSztFQUNoRSxJQUFNQyxXQUFXO0lBQUEsSUFBQXBKLElBQUEsR0FBQThDLDBCQUFBLGVBQUFDLDRCQUFBLEdBQUFDLElBQUEsQ0FBRyxTQUFBQyxRQUFBO01BQUEsT0FBQUYsNEJBQUEsR0FBQUssSUFBQSxVQUFBQyxTQUFBQyxRQUFBO1FBQUEsa0JBQUFBLFFBQUEsQ0FBQUMsSUFBQSxHQUFBRCxRQUFBLENBQUFFLElBQUE7VUFBQTtZQUFBRixRQUFBLENBQUFDLElBQUE7WUFBQUQsUUFBQSxDQUFBRSxJQUFBO1lBQUEsT0FHVnlGLG1DQUFtQixDQUFDSSxtQkFBbUIsQ0FBQyxDQUFDO2NBQUU1RixJQUFJLEVBQUUwRixNQUFNLENBQUMxRjtZQUFLLENBQUMsQ0FBQyxDQUFDO1VBQUE7WUFBQUgsUUFBQSxDQUFBRSxJQUFBO1lBQUE7VUFBQTtZQUFBRixRQUFBLENBQUFDLElBQUE7WUFBQUQsUUFBQSxDQUFBZ0IsRUFBQSxHQUFBaEIsUUFBQTtZQUV0RXBGLHVCQUFPLENBQUM0QixLQUFLLENBQUN3RCxRQUFBLENBQUFnQixFQUFBLENBQU1nRixRQUFRLENBQUMsQ0FBQyxDQUFDO1VBQUM7VUFBQTtZQUFBLE9BQUFoRyxRQUFBLENBQUFrQixJQUFBO1FBQUE7TUFBQSxHQUFBdkIsT0FBQTtJQUFBLENBRW5DO0lBQUEsZ0JBUEttRyxXQUFXQSxDQUFBO01BQUEsT0FBQXBKLElBQUEsQ0FBQXlFLEtBQUEsT0FBQUMsU0FBQTtJQUFBO0VBQUEsR0FPaEI7RUFDRCxvQkFDRTVGLG1CQUFBLENBQUNrSywrQkFBZ0I7SUFDZk8sVUFBVSxFQUFFSCxXQUFZO0lBQ3hCSSxXQUFXLEVBQUVMLE1BQU0sQ0FBQ00sVUFBVztJQUMvQkMsSUFBSSxFQUFFLFFBQVM7SUFDZkMsVUFBVSxFQUFFLFNBQVU7SUFDdEJDLE1BQU0sRUFBRTtFQUFLLENBQ2QsQ0FBQztBQUVOLENBQUM7QUFFRCx5REFBZVYsVUFBVSxFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUNYWDtBQUM4QjtBQUM1QyxJQUFRb0IsSUFBSSxHQUFLTixzQkFBSSxDQUFiTSxJQUFJO0FBRW1EO0FBQ1o7QUFLdkI7QUFDb0Q7QUFDckI7QUFDWDtBQUNwQjtBQUFBO0FBQUE7QUFBQTtBQUM1QixJQUFRUyxNQUFNLEdBQUtaLHdCQUFNLENBQWpCWSxNQUFNO0FBRWQsSUFBUUMsS0FBSyxHQUFXWCx5QkFBVSxDQUExQlcsS0FBSztFQUFFQyxJQUFJLEdBQUtaLHlCQUFVLENBQW5CWSxJQUFJO0FBRW5CLElBQU1DLFVBQVUsR0FBRyxTQUFiQSxVQUFVQSxDQUFJL0IsTUFBOEQsRUFBSztFQUNyRixJQUFBMUksU0FBQSxHQUE4Qm5DLGtCQUFRLENBQUMsS0FBSyxDQUFDO0lBQUFvQyxVQUFBLEdBQUFDLHVCQUFBLENBQUFGLFNBQUE7SUFBdENHLE9BQU8sR0FBQUYsVUFBQTtJQUFFRyxVQUFVLEdBQUFILFVBQUE7RUFDMUIsSUFBQXlLLGFBQUEsR0FBZW5CLHNCQUFJLENBQUNvQixPQUFPLENBQUMsQ0FBQztJQUFBQyxjQUFBLEdBQUExSyx1QkFBQSxDQUFBd0ssYUFBQTtJQUF0QkcsSUFBSSxHQUFBRCxjQUFBO0VBQ1hyRyxPQUFPLENBQUNDLEdBQUcsQ0FBQyxpQkFBaUIsRUFBRWtFLE1BQU0sQ0FBQ29DLFFBQVEsQ0FBQztFQUUvQyxJQUFBekssVUFBQSxHQUFvQ3hDLGtCQUFRLENBQUMsTUFBTSxDQUFDO0lBQUF5QyxVQUFBLEdBQUFKLHVCQUFBLENBQUFHLFVBQUE7SUFBN0MwSyxVQUFVLEdBQUF6SyxVQUFBO0lBQUUwSyxhQUFhLEdBQUExSyxVQUFBO0VBQ2hDLElBQUFHLFVBQUEsR0FBa0M1QyxrQkFBUSxDQUFDLEtBQUssQ0FBQztJQUFBNkMsVUFBQSxHQUFBUix1QkFBQSxDQUFBTyxVQUFBO0lBQTFDd0ssU0FBUyxHQUFBdkssVUFBQTtJQUFFd0ssWUFBWSxHQUFBeEssVUFBQTtFQUM5QixJQUFBRyxVQUFBLEdBQThCaEQsa0JBQVEsQ0FBQyxFQUFFLENBQUM7SUFBQWlELFVBQUEsR0FBQVosdUJBQUEsQ0FBQVcsVUFBQTtJQUFuQytELE9BQU8sR0FBQTlELFVBQUE7SUFBRXFLLFVBQVUsR0FBQXJLLFVBQUE7RUFDMUIsSUFBQUcsVUFBQSxHQUFnQ3BELGtCQUFRLENBQUMsRUFBRSxDQUFDO0lBQUFxRCxXQUFBLEdBQUFoQix1QkFBQSxDQUFBZSxVQUFBO0lBQXJDbUssUUFBUSxHQUFBbEssV0FBQTtJQUFFbUssV0FBVyxHQUFBbkssV0FBQTtFQUU1QixJQUFBRyxXQUFBLEdBQWdDeEQsa0JBQVEsQ0FBUyxDQUFDO0lBQUF5RCxXQUFBLEdBQUFwQix1QkFBQSxDQUFBbUIsV0FBQTtJQUEzQ2lLLFFBQVEsR0FBQWhLLFdBQUE7SUFBRWlLLFdBQVcsR0FBQWpLLFdBQUE7RUFDNUIsSUFBQUcsV0FBQSxHQUFrQzVELGtCQUFRLENBQUMsS0FBSyxDQUFDO0lBQUE2RCxXQUFBLEdBQUF4Qix1QkFBQSxDQUFBdUIsV0FBQTtJQUExQ2xCLFNBQVMsR0FBQW1CLFdBQUE7SUFBRWxCLFlBQVksR0FBQWtCLFdBQUE7RUFDOUIsSUFBQThKLFdBQUEsR0FBZ0MzTixrQkFBUSxDQUFRLEVBQUUsQ0FBQztJQUFBNE4sV0FBQSxHQUFBdkwsdUJBQUEsQ0FBQXNMLFdBQUE7SUFBNUM3SyxRQUFRLEdBQUE4SyxXQUFBO0lBQUU3SyxXQUFXLEdBQUE2SyxXQUFBO0VBRTVCN04sbUJBQVMsQ0FBQyxZQUFNO0lBQ2QsSUFBSThOLE1BQU0sQ0FBQ0MsSUFBSSxDQUFDakQsTUFBTSxDQUFDb0MsUUFBUSxDQUFDLENBQUM5QyxNQUFNLEVBQUU7TUFDdkM2QyxJQUFJLENBQUNlLGNBQWMsQ0FBQ2xELE1BQU0sQ0FBQ29DLFFBQVEsQ0FBQztJQUN0QztJQUNBUyxXQUFXLENBQUM3QyxNQUFNLENBQUNvQyxRQUFRLENBQUNlLEtBQUssSUFBSSxFQUFFLENBQUM7SUFDeEMsSUFBTUMsU0FBUyxHQUFHcEQsTUFBTSxDQUFDb0MsUUFBUSxDQUFDaUIsUUFBUSxHQUFHckQsTUFBTSxDQUFDb0MsUUFBUSxDQUFDaUIsUUFBUSxDQUFDQyxLQUFLLENBQUMsR0FBRyxDQUFDLEdBQUcsRUFBRTtJQUNyRm5CLElBQUksQ0FBQ29CLGFBQWEsQ0FBQyxVQUFVLEVBQUVILFNBQVMsQ0FBQztFQUMzQyxDQUFDLEVBQUUsQ0FBQ3BELE1BQU0sQ0FBQ29DLFFBQVEsQ0FBQyxDQUFDOztFQUVyQjtFQUNBO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7O0VBRUE7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7O0VBRUEsSUFBTW9CLFlBQVk7SUFBQSxJQUFBM00sSUFBQSxHQUFBOEMsMEJBQUEsZUFBQUMsNEJBQUEsR0FBQUMsSUFBQSxDQUFHLFNBQUFDLFFBQU83RCxJQUFTO01BQUEsT0FBQTJELDRCQUFBLEdBQUFLLElBQUEsVUFBQUMsU0FBQUMsUUFBQTtRQUFBLGtCQUFBQSxRQUFBLENBQUFDLElBQUEsR0FBQUQsUUFBQSxDQUFBRSxJQUFBO1VBQUE7WUFBQSxNQUMvQnBFLElBQUksQ0FBQytFLE1BQU0sS0FBSyxNQUFNO2NBQUFiLFFBQUEsQ0FBQUUsSUFBQTtjQUFBO1lBQUE7WUFBQUYsUUFBQSxDQUFBQyxJQUFBO1lBQUFELFFBQUEsQ0FBQUUsSUFBQTtZQUFBLE9BRWhCL0UsaUNBQVUsQ0FBQztjQUNmdUksR0FBRyxFQUFFNUgsSUFBSSxDQUFDNEUsR0FBRztjQUNiaUQsRUFBRSxFQUFFLEVBQUU7Y0FDTkMsRUFBRSxFQUFFO1lBQ04sQ0FBQyxDQUFDO1VBQUE7WUFBQTVELFFBQUEsQ0FBQUUsSUFBQTtZQUFBO1VBQUE7WUFBQUYsUUFBQSxDQUFBQyxJQUFBO1lBQUFELFFBQUEsQ0FBQWdCLEVBQUEsR0FBQWhCLFFBQUE7WUFFRnBGLHVCQUFPLENBQUM0QixLQUFLLENBQUMseUJBQXlCLENBQUM7VUFBQztZQUFBd0QsUUFBQSxDQUFBQyxJQUFBO1lBQUEsT0FBQUQsUUFBQSxDQUFBaUIsTUFBQTtVQUFBO1VBQUE7WUFBQSxPQUFBakIsUUFBQSxDQUFBa0IsSUFBQTtRQUFBO01BQUEsR0FBQXZCLE9BQUE7SUFBQSxDQUk5QztJQUFBLGdCQWJLMEosWUFBWUEsQ0FBQTdHLEVBQUE7TUFBQSxPQUFBOUYsSUFBQSxDQUFBeUUsS0FBQSxPQUFBQyxTQUFBO0lBQUE7RUFBQSxHQWFqQjtFQUVELElBQU1rSSxrQkFBMkMsR0FBRyxTQUE5Q0Esa0JBQTJDQSxDQUFJaEksSUFBSSxFQUFLO0lBQzVESSxPQUFPLENBQUNDLEdBQUcsQ0FBQyxvQkFBb0IsRUFBRUwsSUFBSSxDQUFDO0lBQ3ZDLElBQUlDLFdBQVcsR0FBQUMsMkJBQUEsQ0FBT0YsSUFBSSxDQUFDeEQsUUFBUSxDQUFDO0lBQ3BDO0lBQ0E7SUFDQTtJQUNBeUQsV0FBVyxHQUFHQSxXQUFXLENBQUNsQixHQUFHLENBQUMsVUFBQ3ZFLElBQVMsRUFBSztNQUMzQyxJQUFJQSxJQUFJLENBQUMyRixRQUFRLEVBQUU7UUFDakI7UUFDQTNGLElBQUksQ0FBQzRFLEdBQUcsR0FBRzVFLElBQUksQ0FBQzJGLFFBQVEsQ0FBQ3RCLElBQUk7UUFDN0IsSUFBTW9KLFFBQVEsR0FBR3RLLElBQUksQ0FBQ0MsS0FBSyxDQUFDQyxZQUFZLENBQUNDLE9BQU8sQ0FBQyxPQUFPLENBQUMsSUFBSSxJQUFJLENBQUM7UUFDbEV0RCxJQUFJLENBQUNnRixHQUFHLEdBQUdtRyxpQ0FBZSxDQUN4QixnQ0FBZ0MsR0FBR25MLElBQUksQ0FBQzJGLFFBQVEsQ0FBQ2xCLFFBQVEsR0FBRyxTQUFTLElBQUdnSixRQUFRLGFBQVJBLFFBQVEsdUJBQVJBLFFBQVEsQ0FBRWxLLEtBQUssQ0FDekYsQ0FBQztRQUNEdkQsSUFBSSxDQUFDME4sT0FBTyxHQUFHMU4sSUFBSSxDQUFDMkYsUUFBUSxDQUFDbEIsUUFBUTtNQUN2QztNQUNBLE9BQU96RSxJQUFJO0lBQ2IsQ0FBQyxDQUFDO0lBQ0ZpQyxXQUFXLENBQUN3RCxXQUFXLENBQUM7SUFDeEIsSUFBSUEsV0FBVyxDQUFDNEQsTUFBTSxFQUFFO01BQUEsSUFBQXNFLGFBQUE7TUFDdEJmLFdBQVcsQ0FBQyxFQUFBZSxhQUFBLEdBQUFsSSxXQUFXLENBQUMsQ0FBQyxDQUFDLGNBQUFrSSxhQUFBLHVCQUFkQSxhQUFBLENBQWdCM0ksR0FBRyxLQUFJLEVBQUUsQ0FBQztJQUN4QztFQUNGLENBQUM7RUFFRCxJQUFNYyxZQUFZO0lBQUEsSUFBQXJDLEtBQUEsR0FBQUMsMEJBQUEsZUFBQUMsNEJBQUEsR0FBQUMsSUFBQSxDQUFHLFNBQUFvQyxTQUFPQyxPQUFZO01BQUEsSUFBQUMsU0FBQSxFQUFBQyxPQUFBLEVBQUFuRyxJQUFBLEVBQUFxRyxHQUFBO01BQUEsT0FBQTFDLDRCQUFBLEdBQUFLLElBQUEsVUFBQXNDLFVBQUFDLFNBQUE7UUFBQSxrQkFBQUEsU0FBQSxDQUFBcEMsSUFBQSxHQUFBb0MsU0FBQSxDQUFBbkMsSUFBQTtVQUFBO1lBQzlCOEIsU0FBUyxHQUFvQkQsT0FBTyxDQUFwQ0MsU0FBUyxFQUFFQyxPQUFPLEdBQVdGLE9BQU8sQ0FBekJFLE9BQU8sRUFBRW5HLElBQUksR0FBS2lHLE9BQU8sQ0FBaEJqRyxJQUFJO1lBQUF1RyxTQUFBLENBQUFwQyxJQUFBO1lBRTlCdEMsWUFBWSxDQUFDLElBQUksQ0FBQztZQUFDMEUsU0FBQSxDQUFBbkMsSUFBQTtZQUFBLE9BQ0Q5RSxpQ0FBVSxDQUFDO2NBQzNCVSxJQUFJLEVBQUpBLElBQUk7Y0FDSmEsT0FBTyxFQUFFLEVBQUU7Y0FDWEMsT0FBTyxFQUFFLEVBQUU7Y0FDWEUsVUFBVSxFQUFFLENBQUM7Y0FDYkUsTUFBTSxFQUFFLGtCQUFrQjtjQUMxQkUsUUFBUSxFQUFFO1lBQ1osQ0FBQyxDQUFDO1VBQUE7WUFQSWlGLEdBQUcsR0FBQUUsU0FBQSxDQUFBakMsSUFBQTtZQVFUc0IsT0FBTyxDQUFDQyxHQUFHLENBQUMsVUFBVSxFQUFFUSxHQUFHLENBQUM7WUFDNUJILFNBQVMsQ0FBQ0csR0FBRyxDQUFDdkgsT0FBTyxDQUFDO1lBQUN5SCxTQUFBLENBQUFuQyxJQUFBO1lBQUE7VUFBQTtZQUFBbUMsU0FBQSxDQUFBcEMsSUFBQTtZQUFBb0MsU0FBQSxDQUFBckIsRUFBQSxHQUFBcUIsU0FBQTtZQUV2QlgsT0FBTyxDQUFDQyxHQUFHLENBQUMsU0FBUyxFQUFBVSxTQUFBLENBQUFyQixFQUFLLENBQUM7WUFDM0JpQixPQUFPLENBQUM7Y0FBRU0sR0FBRyxFQUFBRixTQUFBLENBQUFyQjtZQUFDLENBQUMsQ0FBQztVQUFDO1lBQUFxQixTQUFBLENBQUFwQyxJQUFBO1lBRWpCdEMsWUFBWSxDQUFDLEtBQUssQ0FBQztZQUFDLE9BQUEwRSxTQUFBLENBQUFwQixNQUFBO1VBQUE7VUFBQTtZQUFBLE9BQUFvQixTQUFBLENBQUFuQixJQUFBO1FBQUE7TUFBQSxHQUFBWSxRQUFBO0lBQUEsQ0FFdkI7SUFBQSxnQkFwQktGLFlBQVlBLENBQUF1QixHQUFBO01BQUEsT0FBQTVELEtBQUEsQ0FBQTRCLEtBQUEsT0FBQUMsU0FBQTtJQUFBO0VBQUEsR0FvQmpCO0VBRUQsSUFBTXNJLFFBQVEsR0FBRyxTQUFYQSxRQUFRQSxDQUFBLEVBQVM7SUFDckIxQixJQUFJLENBQUMyQixNQUFNLENBQUMsQ0FBQztFQUNmLENBQUM7RUFFRCxJQUFNakYsWUFBWSxnQkFDaEJoSixvQkFBQTtJQUFBaUosUUFBQSxHQUNHakgsU0FBUyxnQkFBR2xDLG1CQUFBLENBQUM2TCw4QkFBZSxJQUFFLENBQUMsZ0JBQUc3TCxtQkFBQSxDQUFDZCwyQkFBWSxJQUFFLENBQUMsZUFDbkRjLG1CQUFBO01BQUtvSixLQUFLLEVBQUU7UUFBRUMsU0FBUyxFQUFFO01BQUUsQ0FBRTtNQUFBRixRQUFBLEVBQUM7SUFBTSxDQUFLLENBQUM7RUFBQSxDQUN2QyxDQUNOO0VBQ0QsSUFBTUcsTUFBTSxHQUFHeEoscUNBQVMsQ0FBQyxDQUFDO0VBQzFCLElBQU15SixTQUFTLEdBQUdELE1BQU0sQ0FBQ0UsNEJBQTRCLENBQUMsQ0FBQztFQUN2RCxJQUFNNEUsSUFBSSxHQUFHckMsbUNBQU8sQ0FBQyxDQUFDO0VBQ3RCLG9CQUNFL0wsbUJBQUEsQ0FBQUksb0JBQUE7SUFBQStJLFFBQUEsZUFDRW5KLG1CQUFBLENBQUNrTCxzQkFBSTtNQUNIbUQsTUFBTSxFQUFDLFlBQVk7TUFDbkJDLFFBQVEsRUFBRTtRQUFFQyxJQUFJLEVBQUU7TUFBRyxDQUFFO01BQ3ZCQyxVQUFVLEVBQUMsTUFBTTtNQUNqQmhDLElBQUksRUFBRUEsSUFBSztNQUNYaUMsUUFBUTtRQUFBLElBQUFwSSxLQUFBLEdBQUFyQywwQkFBQSxlQUFBQyw0QkFBQSxHQUFBQyxJQUFBLENBQUUsU0FBQWtELFNBQU9zSCxLQUFVO1VBQUEsSUFBQTVOLE1BQUE7VUFBQSxPQUFBbUQsNEJBQUEsR0FBQUssSUFBQSxVQUFBK0MsVUFBQUMsU0FBQTtZQUFBLGtCQUFBQSxTQUFBLENBQUE3QyxJQUFBLEdBQUE2QyxTQUFBLENBQUE1QyxJQUFBO2NBQUE7Z0JBQUE0QyxTQUFBLENBQUE3QyxJQUFBO2dCQUFBLElBRWxCOEUsU0FBUztrQkFBQWpDLFNBQUEsQ0FBQTVDLElBQUE7a0JBQUE7Z0JBQUE7Z0JBQ1p0Rix1QkFBTyxDQUFDNEIsS0FBSyxDQUFDLHVDQUF1QyxDQUFDO2dCQUFDLE9BQUFzRyxTQUFBLENBQUFxSCxNQUFBO2NBQUE7Z0JBR3pERCxLQUFLLENBQUNsQixLQUFLLEdBQUcsRUFBRTtnQkFDaEIsSUFBSWxMLFFBQVEsQ0FBQ3FILE1BQU0sRUFBRTtrQkFDbkIrRSxLQUFLLENBQUNsQixLQUFLLEdBQUdsTCxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUMwTCxPQUFPO2dCQUNuQztnQkFDQVUsS0FBSyxDQUFDRSxTQUFTLEdBQUc1QyxtQkFBTSxDQUFDMEMsS0FBSyxDQUFDRSxTQUFTLENBQUMsQ0FBQ0MsTUFBTSxDQUFDLFlBQVksQ0FBQztnQkFDOURILEtBQUssQ0FBQ0ksV0FBVyxHQUFHOUMsbUJBQU0sQ0FBQzBDLEtBQUssQ0FBQ0ksV0FBVyxDQUFDLENBQUNELE1BQU0sQ0FBQyxZQUFZLENBQUM7Z0JBQ2xFSCxLQUFLLENBQUNLLFFBQVEsR0FBRy9DLG1CQUFNLENBQUMwQyxLQUFLLENBQUNLLFFBQVEsQ0FBQyxDQUFDRixNQUFNLENBQUMsWUFBWSxDQUFDO2dCQUM1REgsS0FBSyxDQUFDTSxXQUFXLEdBQUcsU0FBUztnQkFDN0JOLEtBQUssQ0FBQy9KLElBQUksR0FBRzBGLE1BQU0sQ0FBQ29DLFFBQVEsQ0FBQzlILElBQUk7Z0JBQ2pDK0osS0FBSyxDQUFDaEIsUUFBUSxHQUFHZ0IsS0FBSyxDQUFDaEIsUUFBUSxDQUFDdUIsSUFBSSxDQUFDLEdBQUcsQ0FBQztnQkFBQzNILFNBQUEsQ0FBQTVDLElBQUE7Z0JBQUEsT0FDckJ5RixtQ0FBbUIsQ0FBQytFLE1BQU0sQ0FBQ1IsS0FBSyxDQUFDO2NBQUE7Z0JBQWhENU4sTUFBTSxHQUFBd0csU0FBQSxDQUFBMUMsSUFBQTtnQkFDWnhGLHVCQUFPLENBQUMrUCxPQUFPLENBQUMsVUFBVSxDQUFDO2dCQUFDLEtBQ3hCOUUsTUFBTSxDQUFDTSxVQUFVO2tCQUFBckQsU0FBQSxDQUFBNUMsSUFBQTtrQkFBQTtnQkFBQTtnQkFBQTRDLFNBQUEsQ0FBQTVDLElBQUE7Z0JBQUEsT0FDYjJGLE1BQU0sQ0FBQ00sVUFBVSxDQUFDLENBQUM7Y0FBQTtnQkFBQXJELFNBQUEsQ0FBQTVDLElBQUE7Z0JBQUE7Y0FBQTtnQkFBQTRDLFNBQUEsQ0FBQTdDLElBQUE7Z0JBQUE2QyxTQUFBLENBQUE5QixFQUFBLEdBQUE4QixTQUFBO2dCQUczQmxJLHVCQUFPLENBQUM0QixLQUFLLENBQUMsd0JBQXdCLENBQUM7Y0FBQztnQkFBQXNHLFNBQUEsQ0FBQTdDLElBQUE7Z0JBRXhDMUMsVUFBVSxDQUFDLEtBQUssQ0FBQztnQkFBQyxPQUFBdUYsU0FBQSxDQUFBN0IsTUFBQTtjQUFBO2NBQUE7Z0JBQUEsT0FBQTZCLFNBQUEsQ0FBQTVCLElBQUE7WUFBQTtVQUFBLEdBQUEwQixRQUFBO1FBQUEsQ0FFckI7UUFBQSxpQkFBQWtCLEdBQUE7VUFBQSxPQUFBakMsS0FBQSxDQUFBVixLQUFBLE9BQUFDLFNBQUE7UUFBQTtNQUFBLElBQUM7TUFBQXVELFFBQUEsZUFFRmpKLG9CQUFBLENBQUNrTCxrQkFBRztRQUFDZ0UsTUFBTSxFQUFFLENBQUU7UUFBQWpHLFFBQUEsZ0JBQ2JuSixtQkFBQSxDQUFDZ0wsa0JBQUc7VUFDRnFFLFNBQVMsRUFBQyxZQUFZO1VBQ3RCQyxFQUFFLEVBQUUsQ0FBRTtVQUNObEcsS0FBSyxFQUFFO1lBQ0xtRyxPQUFPLEVBQUUsTUFBTTtZQUNmQyxjQUFjLEVBQUU7VUFDbEIsQ0FBRTtVQUFBckcsUUFBQSxlQUdGakosb0JBQUEsQ0FBQ29MLG9CQUFLO1lBQUNtRSxTQUFTLEVBQUMsVUFBVTtZQUFDQyxLQUFLLEVBQUMsUUFBUTtZQUFDQyxJQUFJLEVBQUMsT0FBTztZQUFBeEcsUUFBQSxnQkFDckRuSixtQkFBQSxDQUFDd0wsSUFBSTtjQUFDOEMsUUFBUSxFQUFFO2dCQUFFQyxJQUFJLEVBQUU7Y0FBRyxDQUFFO2NBQUM1SixJQUFJLEVBQUMsT0FBTztjQUFBd0UsUUFBQSxlQUN4Q25KLG1CQUFBLENBQUNWLHFCQUFNO2dCQUNMcUYsSUFBSSxFQUFDLGNBQWM7Z0JBQ25CZ0UsUUFBUSxFQUFDLGNBQWM7Z0JBQ3ZCMEcsU0FBUyxFQUFDLGlCQUFpQjtnQkFDM0JPLGNBQWMsRUFBRSxLQUFNO2dCQUN0QnBILGFBQWEsRUFBRXBDLFlBQWE7Z0JBQzVCeUIsUUFBUSxFQUFFZ0csWUFBYTtnQkFDdkJwRixRQUFRLEVBQUVxRixrQkFBbUI7Z0JBQzdCK0IsTUFBTSxFQUFDLHNDQUFzQztnQkFBQTFHLFFBQUEsRUFFNUM4RCxRQUFRLGdCQUNQak4sbUJBQUE7a0JBQ0VpSyxHQUFHLEVBQUV3QixpQ0FBZSxDQUFDLGdDQUFnQyxHQUFHd0IsUUFBUSxDQUFFO2tCQUNsRWxELEdBQUcsRUFBQyxRQUFRO2tCQUNaWCxLQUFLLEVBQUU7b0JBQUVZLEtBQUssRUFBRTtrQkFBTztnQkFBRSxDQUMxQixDQUFDLEdBRUZkO2NBQ0QsQ0FDSztZQUFDLENBQ0wsQ0FBQyxFQUNOK0QsUUFBUSxnQkFDUGpOLG1CQUFBLENBQUFJLG9CQUFBO2NBQUErSSxRQUFBLGVBQ0VuSixtQkFBQSxDQUFDK0sseUJBQU07Z0JBQ0wrRSxPQUFPLEVBQUUsU0FBQUEsUUFBQSxFQUFNO2tCQUNidk4sV0FBVyxDQUFDLEVBQUUsQ0FBQztrQkFDZjJLLFdBQVcsQ0FBQyxFQUFFLENBQUM7Z0JBQ2pCLENBQUU7Z0JBQUEvRCxRQUFBLGVBRUZuSixtQkFBQSxDQUFDNEwsMkJBQVksSUFBZTtjQUFDLENBQ3ZCO1lBQUMsQ0FDVCxDQUFDLGdCQUVINUwsbUJBQUEsQ0FBQUksb0JBQUEsSUFBSSxDQUNMLGVBQ0RKLG1CQUFBLENBQUNrTSxLQUFLO2NBQUM2RCxLQUFLLEVBQUUsQ0FBRTtjQUFBNUcsUUFBQSxFQUFFa0IsTUFBTSxDQUFDb0MsUUFBUSxDQUFDdUQ7WUFBUSxDQUFRLENBQUMsZUFDbkQ5UCxvQkFBQSxDQUFDaU0sSUFBSTtjQUFDOEQsTUFBTTtjQUFBOUcsUUFBQSxHQUFDLEdBQUMsRUFBQ2tCLE1BQU0sQ0FBQ29DLFFBQVEsQ0FBQ3lELE9BQU87WUFBQSxDQUFPLENBQUM7VUFBQSxDQUN6QztRQUFDLENBQ0wsQ0FBQyxlQUNObFEsbUJBQUEsQ0FBQ2dMLGtCQUFHO1VBQUNxRSxTQUFTLEVBQUMsWUFBWTtVQUFDQyxFQUFFLEVBQUUsRUFBRztVQUFBbkcsUUFBQSxlQUNqQ2pKLG9CQUFBLENBQUNrTCxrQkFBRztZQUFDZ0UsTUFBTSxFQUFFLENBQUU7WUFBQWpHLFFBQUEsZ0JBZ0JibkosbUJBQUEsQ0FBQ2dMLGtCQUFHO2NBQUNxRSxTQUFTLEVBQUMsWUFBWTtjQUFDQyxFQUFFLEVBQUUsRUFBRztjQUFBbkcsUUFBQSxlQUNqQ25KLG1CQUFBLENBQUN3TCxJQUFJO2dCQUNIMkUsS0FBSyxFQUFFL0IsSUFBSSxDQUFDZ0MsYUFBYSxDQUFDO2tCQUFFQyxFQUFFLEVBQUU7Z0JBQWlCLENBQUMsQ0FBRTtnQkFDcEQvQixRQUFRLEVBQUU7a0JBQUVDLElBQUksRUFBRTtnQkFBRyxDQUFFO2dCQUN2QitCLEtBQUssRUFBRSxDQUNMO2tCQUNFQyxRQUFRLEVBQUUsSUFBSTtrQkFDZG5SLE9BQU8sRUFBRTtnQkFDWCxDQUFDLENBQ0Q7Z0JBQ0Z1RixJQUFJLEVBQUMsU0FBUztnQkFBQXdFLFFBQUEsZUFlZG5KLG1CQUFBLENBQUM4TCxxQkFBYTtrQkFDWjBFLFVBQVU7a0JBQ1ZDLFdBQVcsRUFBRSxrQkFBbUI7a0JBQ2hDckgsS0FBSyxFQUFFO29CQUFFWSxLQUFLLEVBQUU7a0JBQU8sQ0FBRTtrQkFDekIwRyxPQUFPLGVBQUExTSwwQkFBQSxlQUFBQyw0QkFBQSxHQUFBQyxJQUFBLENBQUUsU0FBQTZELFNBQUE7b0JBQUEsSUFBQWpILE1BQUE7b0JBQUEsT0FBQW1ELDRCQUFBLEdBQUFLLElBQUEsVUFBQTBELFVBQUFDLFNBQUE7c0JBQUEsa0JBQUFBLFNBQUEsQ0FBQXhELElBQUEsR0FBQXdELFNBQUEsQ0FBQXZELElBQUE7d0JBQUE7MEJBQUF1RCxTQUFBLENBQUF2RCxJQUFBOzBCQUFBLE9BQ2NnSCxrQ0FBa0IsQ0FBQ2lGLE9BQU8sQ0FBQyxDQUFDO3dCQUFBOzBCQUEzQzdQLE1BQU0sR0FBQW1ILFNBQUEsQ0FBQXJELElBQUE7MEJBQUEsT0FBQXFELFNBQUEsQ0FBQTBHLE1BQUEsV0FDTDdOLE1BQU0sQ0FBQzhQLElBQUksQ0FBQy9MLEdBQUcsQ0FBQyxVQUFDZ00sSUFBUzs0QkFBQSxPQUFNOzhCQUNyQ1YsS0FBSyxFQUFFVSxJQUFJLENBQUNDLFdBQVc7OEJBQ3ZCcEMsS0FBSyxFQUFFbUMsSUFBSSxDQUFDbE07NEJBQ2QsQ0FBQzswQkFBQSxDQUFDLENBQUM7d0JBQUE7d0JBQUE7MEJBQUEsT0FBQXNELFNBQUEsQ0FBQXZDLElBQUE7c0JBQUE7b0JBQUEsR0FBQXFDLFFBQUE7a0JBQUEsQ0FDSjtnQkFBQyxDQUNIO2NBQUMsQ0FDRTtZQUFDLENBQ0osQ0FBQyxlQUNOL0gsbUJBQUEsQ0FBQ2dMLGtCQUFHO2NBQUNxRSxTQUFTLEVBQUMsWUFBWTtjQUFDQyxFQUFFLEVBQUUsRUFBRztjQUFBbkcsUUFBQSxlQUNqQ25KLG1CQUFBLENBQUN3TCxJQUFJO2dCQUNIMkUsS0FBSyxFQUFFL0IsSUFBSSxDQUFDZ0MsYUFBYSxDQUFDO2tCQUFFQyxFQUFFLEVBQUU7Z0JBQWdCLENBQUMsQ0FBRTtnQkFDbkQvQixRQUFRLEVBQUU7a0JBQUVDLElBQUksRUFBRTtnQkFBRyxDQUFFO2dCQUN2QitCLEtBQUssRUFBRSxDQUNMO2tCQUNFQyxRQUFRLEVBQUUsSUFBSTtrQkFDZG5SLE9BQU8sRUFBRTtnQkFDWCxDQUFDLENBQ0Q7Z0JBQ0Z1RixJQUFJLEVBQUMsYUFBYTtnQkFBQXdFLFFBQUEsZUFFbEJuSixtQkFBQSxDQUFDOEwscUJBQWE7a0JBQ1owRSxVQUFVO2tCQUNWQyxXQUFXLEVBQUUsa0JBQW1CO2tCQUNoQ3JILEtBQUssRUFBRTtvQkFBRVksS0FBSyxFQUFFO2tCQUFPLENBQUU7a0JBQ3pCK0csUUFBUTtnQkFBQSxDQUNUO2NBQUMsQ0FDRTtZQUFDLENBQ0osQ0FBQyxlQUNOL1EsbUJBQUEsQ0FBQ2dMLGtCQUFHO2NBQUNxRSxTQUFTLEVBQUMsWUFBWTtjQUFDQyxFQUFFLEVBQUUsRUFBRztjQUFBbkcsUUFBQSxlQUNqQ25KLG1CQUFBLENBQUN3TCxJQUFJO2dCQUNIMkUsS0FBSyxFQUFFL0IsSUFBSSxDQUFDZ0MsYUFBYSxDQUFDO2tCQUFFQyxFQUFFLEVBQUU7Z0JBQWtCLENBQUMsQ0FBRTtnQkFDckQvQixRQUFRLEVBQUU7a0JBQUVDLElBQUksRUFBRTtnQkFBRyxDQUFFO2dCQUN2QitCLEtBQUssRUFBRSxDQUNMO2tCQUNFQyxRQUFRLEVBQUUsSUFBSTtrQkFDZG5SLE9BQU8sRUFBRTtnQkFDWCxDQUFDLENBQ0Q7Z0JBQ0Z1RixJQUFJLEVBQUMsVUFBVTtnQkFBQXdFLFFBQUEsZUFFZm5KLG1CQUFBLENBQUNtTCxvQkFBSztrQkFBQ3NGLFdBQVcsRUFBQztnQkFBYyxDQUFFO2NBQUMsQ0FDaEM7WUFBQyxDQUNKLENBQUMsZUFDTnpRLG1CQUFBLENBQUNnTCxrQkFBRztjQUFDcUUsU0FBUyxFQUFDLFlBQVk7Y0FBQ0MsRUFBRSxFQUFFLEVBQUc7Y0FBQW5HLFFBQUEsZUFDakNuSixtQkFBQSxDQUFDd0wsSUFBSTtnQkFDSDJFLEtBQUssRUFBRS9CLElBQUksQ0FBQ2dDLGFBQWEsQ0FBQztrQkFBRUMsRUFBRSxFQUFFO2dCQUFnQixDQUFDLENBQUU7Z0JBQ25EL0IsUUFBUSxFQUFFO2tCQUFFQyxJQUFJLEVBQUU7Z0JBQUcsQ0FBRTtnQkFDdkIrQixLQUFLLEVBQUUsQ0FDTDtrQkFDRUMsUUFBUSxFQUFFLElBQUk7a0JBQ2RuUixPQUFPLEVBQUU7Z0JBQ1gsQ0FBQyxDQUNEO2dCQUNGdUYsSUFBSSxFQUFDLFFBQVE7Z0JBQUF3RSxRQUFBLGVBRWJuSixtQkFBQSxDQUFDcUwsd0JBQU07a0JBQUM5RSxPQUFPLEVBQUUsQ0FBQztvQkFBRW1JLEtBQUssRUFBRTtrQkFBTSxDQUFDLEVBQUU7b0JBQUVBLEtBQUssRUFBRTtrQkFBSyxDQUFDO2dCQUFFLENBQUU7Y0FBQyxDQUNwRDtZQUFDLENBQ0osQ0FBQyxlQUNOMU8sbUJBQUEsQ0FBQ2dMLGtCQUFHO2NBQUNxRSxTQUFTLEVBQUMsWUFBWTtjQUFDQyxFQUFFLEVBQUUsRUFBRztjQUFBbkcsUUFBQSxlQUNqQ25KLG1CQUFBLENBQUN3TCxJQUFJO2dCQUNIMkUsS0FBSyxFQUFFL0IsSUFBSSxDQUFDZ0MsYUFBYSxDQUFDO2tCQUFFQyxFQUFFLEVBQUU7Z0JBQWtCLENBQUMsQ0FBRTtnQkFDckQvQixRQUFRLEVBQUU7a0JBQUVDLElBQUksRUFBRTtnQkFBRyxDQUFFO2dCQUN2QjVKLElBQUksRUFBQyxVQUFVO2dCQUFBd0UsUUFBQSxlQUVmbkosbUJBQUEsQ0FBQ2lMLHNCQUFVO2tCQUFDN0IsS0FBSyxFQUFFO29CQUFFWSxLQUFLLEVBQUU7a0JBQU87Z0JBQUUsQ0FBRTtjQUFDLENBQ3BDO1lBQUMsQ0FDSixDQUFDLGVBQ05oSyxtQkFBQSxDQUFDZ0wsa0JBQUc7Y0FBQ3FFLFNBQVMsRUFBQyxZQUFZO2NBQUNDLEVBQUUsRUFBRSxFQUFHO2NBQUFuRyxRQUFBLGVBQ2pDbkosbUJBQUEsQ0FBQ3dMLElBQUk7Z0JBQ0gyRSxLQUFLLEVBQUUvQixJQUFJLENBQUNnQyxhQUFhLENBQUM7a0JBQUVDLEVBQUUsRUFBRTtnQkFBaUIsQ0FBQyxDQUFFO2dCQUNwRC9CLFFBQVEsRUFBRTtrQkFBRUMsSUFBSSxFQUFFO2dCQUFHLENBQUU7Z0JBQ3ZCNUosSUFBSSxFQUFDLFNBQVM7Z0JBQUF3RSxRQUFBLGVBRWRuSixtQkFBQSxDQUFDbUwsb0JBQUs7a0JBQUNzRixXQUFXLEVBQUM7Z0JBQThCLENBQUU7Y0FBQyxDQUNoRDtZQUFDLENBQ0osQ0FBQyxlQUNOelEsbUJBQUEsQ0FBQ2dMLGtCQUFHO2NBQUNxRSxTQUFTLEVBQUMsWUFBWTtjQUFDQyxFQUFFLEVBQUUsRUFBRztjQUFBbkcsUUFBQSxlQUNqQ25KLG1CQUFBLENBQUN3TCxJQUFJO2dCQUNIMkUsS0FBSyxFQUFFL0IsSUFBSSxDQUFDZ0MsYUFBYSxDQUFDO2tCQUFFQyxFQUFFLEVBQUU7Z0JBQXVCLENBQUMsQ0FBRTtnQkFDMUQvQixRQUFRLEVBQUU7a0JBQUVDLElBQUksRUFBRTtnQkFBRyxDQUFFO2dCQUN2QjVKLElBQUksRUFBQyxVQUFVO2dCQUFBd0UsUUFBQSxlQUVmbkosbUJBQUEsQ0FBQ21MLG9CQUFLLElBQUU7Y0FBQyxDQUNMO1lBQUMsQ0FDSixDQUFDLGVBQ05uTCxtQkFBQSxDQUFDZ0wsa0JBQUc7Y0FBQ3FFLFNBQVMsRUFBQyxZQUFZO2NBQUNDLEVBQUUsRUFBRSxFQUFHO2NBQUFuRyxRQUFBLGVBQ2pDbkosbUJBQUEsQ0FBQ3dMLElBQUk7Z0JBQ0gyRSxLQUFLLEVBQUUvQixJQUFJLENBQUNnQyxhQUFhLENBQUM7a0JBQUVDLEVBQUUsRUFBRTtnQkFBaUIsQ0FBQyxDQUFFO2dCQUNwRC9CLFFBQVEsRUFBRTtrQkFBRUMsSUFBSSxFQUFFO2dCQUFHLENBQUU7Z0JBQ3ZCNUosSUFBSSxFQUFDLFNBQVM7Z0JBQUF3RSxRQUFBLGVBRWRuSixtQkFBQSxDQUFDbUwsb0JBQUs7a0JBQUM2RixZQUFZLEVBQUU7Z0JBQVcsQ0FBRTtjQUFDLENBQy9CO1lBQUMsQ0FDSixDQUFDLGVBQ05oUixtQkFBQSxDQUFDZ0wsa0JBQUc7Y0FBQ3FFLFNBQVMsRUFBQyxZQUFZO2NBQUNDLEVBQUUsRUFBRSxFQUFHO2NBQUFuRyxRQUFBLGVBQ2pDbkosbUJBQUEsQ0FBQ3dMLElBQUk7Z0JBQ0gyRSxLQUFLLEVBQUUvQixJQUFJLENBQUNnQyxhQUFhLENBQUM7a0JBQUVDLEVBQUUsRUFBRTtnQkFBbUIsQ0FBQyxDQUFFO2dCQUN0RC9CLFFBQVEsRUFBRTtrQkFBRUMsSUFBSSxFQUFFO2dCQUFHLENBQUU7Z0JBQ3ZCK0IsS0FBSyxFQUFFLENBQ0w7a0JBQ0VDLFFBQVEsRUFBRSxJQUFJO2tCQUNkblIsT0FBTyxFQUFFO2dCQUNYLENBQUMsQ0FDRDtnQkFDRnVGLElBQUksRUFBQyxXQUFXO2dCQUFBd0UsUUFBQSxlQUVoQm5KLG1CQUFBLENBQUNpTCxzQkFBVTtrQkFBQzhGLFFBQVEsRUFBRSxJQUFLO2tCQUFDM0gsS0FBSyxFQUFFO29CQUFFWSxLQUFLLEVBQUU7a0JBQU87Z0JBQUUsQ0FBRTtjQUFDLENBQ3BEO1lBQUMsQ0FDSixDQUFDLGVBQ05oSyxtQkFBQSxDQUFDZ0wsa0JBQUc7Y0FBQ3FFLFNBQVMsRUFBQyxZQUFZO2NBQUNDLEVBQUUsRUFBRSxFQUFHO2NBQUFuRyxRQUFBLGVBQ2pDbkosbUJBQUEsQ0FBQ3dMLElBQUk7Z0JBQ0gyRSxLQUFLLEVBQUUvQixJQUFJLENBQUNnQyxhQUFhLENBQUM7a0JBQUVDLEVBQUUsRUFBRTtnQkFBcUIsQ0FBQyxDQUFFO2dCQUN4RC9CLFFBQVEsRUFBRTtrQkFBRUMsSUFBSSxFQUFFO2dCQUFHLENBQUU7Z0JBQ3ZCK0IsS0FBSyxFQUFFLENBQ0w7a0JBQ0VDLFFBQVEsRUFBRSxJQUFJO2tCQUNkblIsT0FBTyxFQUFFO2dCQUNYLENBQUMsQ0FDRDtnQkFDRnVGLElBQUksRUFBQyxhQUFhO2dCQUFBd0UsUUFBQSxlQUVsQm5KLG1CQUFBLENBQUNpTCxzQkFBVTtrQkFBQzhGLFFBQVEsRUFBRTFHLE1BQU0sQ0FBQzRHLFNBQVU7a0JBQUM3SCxLQUFLLEVBQUU7b0JBQUVZLEtBQUssRUFBRTtrQkFBTztnQkFBRSxDQUFFO2NBQUMsQ0FDaEU7WUFBQyxDQUNKLENBQUMsZUFDTmhLLG1CQUFBLENBQUNnTCxrQkFBRztjQUFDcUUsU0FBUyxFQUFDLFlBQVk7Y0FBQ0MsRUFBRSxFQUFFLEVBQUc7Y0FBQW5HLFFBQUEsZUFDakNuSixtQkFBQSxDQUFDd0wsSUFBSTtnQkFDSDJFLEtBQUssRUFBRS9CLElBQUksQ0FBQ2dDLGFBQWEsQ0FBQztrQkFBRUMsRUFBRSxFQUFFO2dCQUFjLENBQUMsQ0FBRTtnQkFDakQvQixRQUFRLEVBQUU7a0JBQUVDLElBQUksRUFBRTtnQkFBRyxDQUFFO2dCQUN2QjVKLElBQUksRUFBQztnQkFDTDtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFBQTtnQkFBQXdFLFFBQUEsZUFFQW5KLG1CQUFBLENBQUNtTCxvQkFBSyxJQUFFO2NBQUMsQ0FDTDtZQUFDLENBQ0osQ0FBQyxlQUNObkwsbUJBQUEsQ0FBQ2dMLGtCQUFHO2NBQUNxRSxTQUFTLEVBQUMsWUFBWTtjQUFDQyxFQUFFLEVBQUUsRUFBRztjQUFBbkcsUUFBQSxlQUNqQ25KLG1CQUFBLENBQUN3TCxJQUFJO2dCQUNIMkUsS0FBSyxFQUFFL0IsSUFBSSxDQUFDZ0MsYUFBYSxDQUFDO2tCQUFFQyxFQUFFLEVBQUU7Z0JBQWMsQ0FBQyxDQUFFO2dCQUNqRC9CLFFBQVEsRUFBRTtrQkFBRUMsSUFBSSxFQUFFO2dCQUFHLENBQUU7Z0JBQ3ZCNUosSUFBSSxFQUFDO2dCQUNMO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUFBO2dCQUFBd0UsUUFBQSxlQUVBbkosbUJBQUEsQ0FBQ21MLG9CQUFLLElBQUU7Y0FBQyxDQUNMO1lBQUMsQ0FDSixDQUFDLGVBQ05uTCxtQkFBQSxDQUFDZ0wsa0JBQUc7Y0FBQ3FFLFNBQVMsRUFBQyxZQUFZO2NBQUNDLEVBQUUsRUFBRSxFQUFHO2NBQUFuRyxRQUFBLGVBQ2pDbkosbUJBQUEsQ0FBQ3dMLElBQUk7Z0JBQ0gyRSxLQUFLLEVBQUUvQixJQUFJLENBQUNnQyxhQUFhLENBQUM7a0JBQUVDLEVBQUUsRUFBRTtnQkFBa0IsQ0FBQyxDQUFFO2dCQUNyRC9CLFFBQVEsRUFBRTtrQkFBRUMsSUFBSSxFQUFFO2dCQUFHLENBQUU7Z0JBQ3ZCNUosSUFBSSxFQUFDLFVBQVU7Z0JBQUF3RSxRQUFBLGVBRWZuSixtQkFBQSxDQUFDOEwscUJBQWE7a0JBQ1pvRixJQUFJLEVBQUMsVUFBVTtrQkFDZlYsVUFBVTtrQkFDVkUsT0FBTyxlQUFBMU0sMEJBQUEsZUFBQUMsNEJBQUEsR0FBQUMsSUFBQSxDQUFFLFNBQUE0RSxTQUFBO29CQUFBLElBQUFoSSxNQUFBO29CQUFBLE9BQUFtRCw0QkFBQSxHQUFBSyxJQUFBLFVBQUF5RSxVQUFBQyxTQUFBO3NCQUFBLGtCQUFBQSxTQUFBLENBQUF2RSxJQUFBLEdBQUF1RSxTQUFBLENBQUF0RSxJQUFBO3dCQUFBOzBCQUFBc0UsU0FBQSxDQUFBdEUsSUFBQTswQkFBQSxPQUNjaUgsc0NBQXNCLENBQUNnRixPQUFPLENBQUMsQ0FBQzt3QkFBQTswQkFBL0M3UCxNQUFNLEdBQUFrSSxTQUFBLENBQUFwRSxJQUFBOzBCQUFBLE9BQUFvRSxTQUFBLENBQUEyRixNQUFBLFdBQ0w3TixNQUFNLENBQUM4UCxJQUFJLENBQUMvTCxHQUFHLENBQUMsVUFBQ2dNLElBQVM7NEJBQUEsT0FBTTs4QkFDckNWLEtBQUssRUFBRVUsSUFBSSxDQUFDVixLQUFLOzhCQUNqQnpCLEtBQUssRUFBRW1DLElBQUksQ0FBQ2xNOzRCQUNkLENBQUM7MEJBQUEsQ0FBQyxDQUFDO3dCQUFBO3dCQUFBOzBCQUFBLE9BQUFxRSxTQUFBLENBQUF0RCxJQUFBO3NCQUFBO29CQUFBLEdBQUFvRCxRQUFBO2tCQUFBLENBQ0o7Z0JBQUMsQ0FDSDtjQUFDLENBQ0U7WUFBQyxDQUNKLENBQUMsZUFDTjlJLG1CQUFBLENBQUNnTCxrQkFBRztjQUFDcUUsU0FBUyxFQUFDLFlBQVk7Y0FBQ0MsRUFBRSxFQUFFLEVBQUc7Y0FBQW5HLFFBQUEsZUFDakNuSixtQkFBQSxDQUFDK0sseUJBQU07Z0JBQ0xqSixPQUFPLEVBQUVBLE9BQVE7Z0JBQ2pCc0QsSUFBSSxFQUFDLFNBQVM7Z0JBQ2QrTCxRQUFRLEVBQUMsUUFBUTtnQkFDakIvSCxLQUFLLEVBQUU7a0JBQUVZLEtBQUssRUFBRTtnQkFBTyxDQUFFO2dCQUFBYixRQUFBLEVBRXhCaUYsSUFBSSxDQUFDZ0MsYUFBYSxDQUFDO2tCQUFFQyxFQUFFLEVBQUU7Z0JBQWMsQ0FBQztjQUFDLENBQ3BDO1lBQUMsQ0FDTixDQUFDO1VBQUEsQ0FDSDtRQUFDLENBQ0gsQ0FBQztNQUFBLENBQ0g7SUFBQyxDQUNGO0VBQUMsQ0FDUCxDQUFDO0FBRVAsQ0FBQztBQUVELDZEQUFlakUsVUFBVSxFOzs7OztBQzVmcUU7QUFDdEQ7QUFDaUI7QUFDRTtBQUNZO0FBQ1o7QUFDL0I7QUFDdUI7QUFDbUI7QUFDUTtBQUFBO0FBQUE7QUFBQTtBQUU5RSxJQUFNeUYsZUFBeUIsR0FBRyxTQUE1QkEsZUFBeUJBLENBQUEsRUFBUztFQUN0QyxJQUFBbFEsU0FBQSxHQUE4Qm5DLGtCQUFRLENBQUMsS0FBSyxDQUFDO0lBQUFvQyxVQUFBLEdBQUFDLHVCQUFBLENBQUFGLFNBQUE7SUFBdENHLE9BQU8sR0FBQUYsVUFBQTtJQUFFRyxVQUFVLEdBQUFILFVBQUE7RUFDMUIsSUFBQWtRLGdCQUFBLEdBQXdDUCwyQ0FBZSxDQUFDLENBQUM7SUFBQVEsaUJBQUEsR0FBQWxRLHVCQUFBLENBQUFpUSxnQkFBQTtJQUFsREUsWUFBWSxHQUFBRCxpQkFBQTtJQUFFRSxlQUFlLEdBQUFGLGlCQUFBO0VBQ3BDLElBQUEvUCxVQUFBLEdBQWdDeEMsa0JBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQztJQUFBeUMsVUFBQSxHQUFBSix1QkFBQSxDQUFBRyxVQUFBO0lBQXJDeUssUUFBUSxHQUFBeEssVUFBQTtJQUFFaVEsV0FBVyxHQUFBalEsVUFBQTtFQUM1QixJQUFBRyxVQUFBLEdBQThCNUMsa0JBQVEsQ0FBQyxJQUFJLENBQUM7SUFBQTZDLFVBQUEsR0FBQVIsdUJBQUEsQ0FBQU8sVUFBQTtJQUFyQytQLE9BQU8sR0FBQTlQLFVBQUE7SUFBRStQLFVBQVUsR0FBQS9QLFVBQUE7RUFDMUIsSUFBQUcsVUFBQSxHQUFzQmhELGtCQUFRLENBQUMsRUFBRSxDQUFDO0lBQUFpRCxVQUFBLEdBQUFaLHVCQUFBLENBQUFXLFVBQUE7SUFBM0I2UCxHQUFHLEdBQUE1UCxVQUFBO0lBQUU2UCxNQUFNLEdBQUE3UCxVQUFBO0VBQ2xCLElBQUFHLFVBQUEsR0FBb0JwRCxrQkFBUSxDQUFDLEVBQUUsQ0FBQztJQUFBcUQsV0FBQSxHQUFBaEIsdUJBQUEsQ0FBQWUsVUFBQTtJQUF6QnlOLEVBQUUsR0FBQXhOLFdBQUE7SUFBRTBQLEtBQUssR0FBQTFQLFdBQUE7RUFFaEIsSUFBTThCLElBQUksR0FBR3FOLFlBQVksQ0FBQ1EsR0FBRyxDQUFDLGVBQWUsQ0FBQztFQUU5QyxJQUFNOUgsV0FBVztJQUFBLElBQUF4SixJQUFBLEdBQUE4QywwQkFBQSxlQUFBQyw0QkFBQSxHQUFBQyxJQUFBLENBQUcsU0FBQUMsUUFBQTtNQUFBLElBQUFrRSxNQUFBLEVBQUF2SCxNQUFBLEVBQUEyUixjQUFBLEVBQUFqUCxVQUFBLEVBQUFrUCxRQUFBO01BQUEsT0FBQXpPLDRCQUFBLEdBQUFLLElBQUEsVUFBQUMsU0FBQUMsUUFBQTtRQUFBLGtCQUFBQSxRQUFBLENBQUFDLElBQUEsR0FBQUQsUUFBQSxDQUFBRSxJQUFBO1VBQUE7WUFBQUYsUUFBQSxDQUFBQyxJQUFBO1lBRWhCMUMsVUFBVSxDQUFDLElBQUksQ0FBQztZQUNoQjtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNNc0csTUFBTSxHQUFHNUUsSUFBSSxDQUFDa1AsU0FBUyxDQUFDLENBQUMsQ0FBQyxtQkFBbUIsRUFBRSxNQUFNLEVBQUUsTUFBTSxLQUFBcE4sTUFBQSxDQUFLWixJQUFJLEVBQUcsQ0FBQyxDQUFDO1lBQUFILFFBQUEsQ0FBQUUsSUFBQTtZQUFBLE9BQzVEeUYsbUNBQW1CLENBQUN3RyxPQUFPLENBQUM7Y0FBRWlDLE9BQU8sRUFBRXZLO1lBQU8sQ0FBQyxDQUFDO1VBQUE7WUFBL0R2SCxNQUFNLEdBQUEwRCxRQUFBLENBQUFJLElBQUE7WUFDWnNCLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLFdBQVcsRUFBRXJGLE1BQU0sQ0FBQztZQUNoQyxJQUFJQSxNQUFNLENBQUM4UCxJQUFJLENBQUNqSCxNQUFNLEdBQUcsQ0FBQyxFQUFFO2NBQ3BCOEksY0FBYyxHQUFHM1IsTUFBTSxDQUFDOFAsSUFBSSxDQUFDLENBQUMsQ0FBQztjQUNyQzZCLGNBQWMsQ0FBQzFELFFBQVEsR0FBRy9DLG1CQUFNLENBQUN5RyxjQUFjLENBQUMxRCxRQUFRLENBQUM7Y0FDekQwRCxjQUFjLENBQUM3RCxTQUFTLEdBQUc1QyxtQkFBTSxDQUFDeUcsY0FBYyxDQUFDN0QsU0FBUyxDQUFDO2NBQzNENkQsY0FBYyxDQUFDM0QsV0FBVyxHQUFHOUMsbUJBQU0sQ0FBQ3lHLGNBQWMsQ0FBQzNELFdBQVcsQ0FBQztjQUMvRG9ELFdBQVcsQ0FBQ08sY0FBYyxDQUFDO2NBQ3JCalAsVUFBVSxHQUFHQyxJQUFJLENBQUNDLEtBQUssQ0FBQ0MsWUFBWSxDQUFDQyxPQUFPLENBQUMsT0FBTyxDQUFDLENBQUMsQ0FBQ0MsS0FBSztjQUM1RDZPLFFBQVEsR0FDWixHQUFBbk4sTUFBQSxDQUFHMUYsa0JBQUksaUNBQ1AyRCxVQUFVLGVBQ0UsR0FDWmlQLGNBQWMsQ0FBQ0ksS0FBSyxHQUNwQixNQUFNLEdBQ04saUJBQWlCLEdBQ2pCLE1BQU0sR0FDTkosY0FBYyxDQUFDOU4sSUFBSTtjQUNyQjJOLE1BQU0sQ0FBQ0ksUUFBUSxDQUFDO2NBQ2hCSCxLQUFLLENBQUNFLGNBQWMsQ0FBQzlOLElBQUksQ0FBQztZQUM1QjtZQUFDSCxRQUFBLENBQUFFLElBQUE7WUFBQTtVQUFBO1lBQUFGLFFBQUEsQ0FBQUMsSUFBQTtZQUFBRCxRQUFBLENBQUFnQixFQUFBLEdBQUFoQixRQUFBO1lBRUQwQixPQUFPLENBQUNDLEdBQUcsQ0FBQTNCLFFBQUEsQ0FBQWdCLEVBQU0sQ0FBQztVQUFDO1lBQUFoQixRQUFBLENBQUFDLElBQUE7WUFFbkIxQyxVQUFVLENBQUMsS0FBSyxDQUFDO1lBQUMsT0FBQXlDLFFBQUEsQ0FBQWlCLE1BQUE7VUFBQTtVQUFBO1lBQUEsT0FBQWpCLFFBQUEsQ0FBQWtCLElBQUE7UUFBQTtNQUFBLEdBQUF2QixPQUFBO0lBQUEsQ0FFckI7SUFBQSxnQkFyQ0t1RyxXQUFXQSxDQUFBO01BQUEsT0FBQXhKLElBQUEsQ0FBQXlFLEtBQUEsT0FBQUMsU0FBQTtJQUFBO0VBQUEsR0FxQ2hCO0VBRURyRyxtQkFBUyxDQUFDLFlBQU07SUFDZG1MLFdBQVcsQ0FBQyxDQUFDO0VBQ2YsQ0FBQyxFQUFFLEVBQUUsQ0FBQztFQUNOLElBQU1vSSxZQUFZLEdBQUcsU0FBZkEsWUFBWUEsQ0FBQSxFQUFTO0lBQ3pCVixVQUFVLENBQUMsVUFBQ1csT0FBTztNQUFBLE9BQUssQ0FBQ0EsT0FBTztJQUFBLEVBQUM7RUFDbkMsQ0FBQztFQUNELElBQU16SixNQUFNLEdBQUd4SixxQ0FBUyxDQUFDLENBQUM7RUFDMUIsSUFBTWtULFNBQVMsR0FBRzFKLE1BQU0sQ0FBQzJKLDRCQUE0QixDQUFDLENBQUM7RUFDdkQsSUFBSXRPLElBQUksRUFBRTtJQUNSLG9CQUNFM0UsbUJBQUEsQ0FBQ29SLG1DQUFhO01BQUFqSSxRQUFBLGVBQ1puSixtQkFBQSxDQUFDYixtQkFBSTtRQUNIc0ssS0FBSyxFQUFFOUUsSUFBSztRQUNaN0MsT0FBTyxFQUFFQSxPQUFRO1FBQ2pCb1IsS0FBSyxlQUNIbFQsbUJBQUEsQ0FBQ3NMLG9CQUFLO1VBQUFuQyxRQUFBLEVBSUg2SixTQUFTLGlCQUNSaFQsbUJBQUEsQ0FBQzJSLG9CQUFvQjtZQUNuQmhOLElBQUksRUFBRUEsSUFBSztZQUNYZ0csVUFBVSxFQUFFLFNBQUFBLFdBQUEsRUFBTTtjQUNoQjBHLCtCQUFPLENBQUNwTSxJQUFJLENBQUMsb0RBQW9ELENBQUM7WUFDcEU7VUFBRSxDQUNIO1FBQ0YsQ0FDSSxDQUNSO1FBQUFrRSxRQUFBLEVBRUFrRSxNQUFNLENBQUNDLElBQUksQ0FBQ2IsUUFBUSxDQUFDLENBQUM5QyxNQUFNLGdCQUMzQjNKLG1CQUFBLENBQUFJLG9CQUFBO1VBQUErSSxRQUFBLGVBQ0VqSixvQkFBQSxDQUFDdVIsbUJBQUk7WUFBQzBCLGdCQUFnQixFQUFDLEdBQUc7WUFBQWhLLFFBQUEsZ0JBQ3hCbkosbUJBQUEsQ0FBQ3lSLG1CQUFJLENBQUMyQixPQUFPO2NBQUNDLEdBQUcsRUFBQyw0QkFBb0I7Y0FBQWxLLFFBQUEsZUFDcENuSixtQkFBQSxDQUFDNFIsd0JBQXdCO2dCQUN2QmpILFVBQVUsRUFBRUQsV0FBWTtnQkFDeEIrQixRQUFRLEVBQUVBLFFBQVM7Z0JBQ25Cd0UsU0FBUyxFQUFFa0I7Y0FBUSxDQUNwQjtZQUFDLEdBTHVDLEdBTTdCLENBQUMsZUFDZm5TLG1CQUFBLENBQUN5UixtQkFBSSxDQUFDMkIsT0FBTztjQUFDQyxHQUFHLEVBQUMsbUJBQWM7Y0FBQWxLLFFBQUEsZUFDOUJuSixtQkFBQSxDQUFDb0wsa0JBQUc7Z0JBQUFqQyxRQUFBLGVBQ0ZuSixtQkFBQSxDQUFDZ0wsa0JBQUc7a0JBQUNzRSxFQUFFLEVBQUUsRUFBRztrQkFBQW5HLFFBQUEsZUFDVm5KLG1CQUFBLENBQUNpQiwyQ0FBc0I7b0JBQUNHLE9BQU8sRUFBRXVELElBQUs7b0JBQUN4RCxPQUFPLEVBQUM7a0JBQW1CLENBQUU7Z0JBQUMsQ0FDbEU7Y0FBQyxDQUNIO1lBQUMsR0FMNkIsR0FNdkIsQ0FBQztVQUFBLENBSVg7UUFBQyxDQUNQLENBQUMsZ0JBRUhuQixtQkFBQSxDQUFBSSxvQkFBQTtVQUFBK0ksUUFBQSxFQUFFO1FBQWtDLENBQUU7TUFDdkMsQ0FDRztJQUFDLENBQ00sQ0FBQztFQUVwQixDQUFDLE1BQU07SUFDTCxvQkFDRW5KLG1CQUFBLENBQUFJLG9CQUFBO01BQUErSSxRQUFBLGVBQ0VuSixtQkFBQSxDQUFDd1Isc0JBQU07UUFDTG5NLE1BQU0sRUFBQyxLQUFLO1FBQ1pvRSxLQUFLLEVBQUMsS0FBSztRQUNYNkosUUFBUSxFQUFDLDZDQUE2QztRQUN0REosS0FBSyxlQUNIbFQsbUJBQUEsQ0FBQ3NSLDRCQUFJO1VBQUNpQyxFQUFFLEVBQUUsbUJBQW9CO1VBQUNuTyxJQUFJLEVBQUMsU0FBUztVQUFBK0QsUUFBQSxFQUFDO1FBRTlDLENBQU07TUFDUCxDQUNGO0lBQUMsQ0FDRixDQUFDO0VBRVA7QUFDRixDQUFDO0FBRUQsMkNBQWUwSSxlQUFlIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9zcmMvY29tcG9uZW50cy9BdHRhY2htZW50c09mRG9jdHlwZS9BdHRhY2hQaWN0dXJlT2ZEb2N0eXBlLnRzeD82NjE5Iiwid2VicGFjazovL2FudC1kZXNpZ24tcHJvLy4vc3JjL3BhZ2VzL1Zpc2l0b3IvUGFraW5nQ3VzdG9tZXIvQ29tcG9uZW50cy9SZW1vdmVQYWtpbmdDdXN0b21lci50c3g/YTk5YiIsIndlYnBhY2s6Ly9hbnQtZGVzaWduLXByby8uL3NyYy9wYWdlcy9WaXNpdG9yL1Bha2luZ0N1c3RvbWVyL0NvbXBvbmVudHMvVXBkYXRlUGFraW5nQ3VzdG9tZXJGb3JtLnRzeD9lODljIiwid2VicGFjazovL2FudC1kZXNpZ24tcHJvLy4vc3JjL3BhZ2VzL1Zpc2l0b3IvUGFraW5nQ3VzdG9tZXIvRGV0YWlsL2luZGV4LnRzeD82NDEwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFBsdXNPdXRsaW5lZCB9IGZyb20gJ0BhbnQtZGVzaWduL2ljb25zJztcclxuaW1wb3J0IHsgQ2FyZCwgbWVzc2FnZSwgTW9kYWwsIFVwbG9hZCB9IGZyb20gJ2FudGQnO1xyXG5pbXBvcnQgeyB1c2VFZmZlY3QsIHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xyXG5cclxuaW1wb3J0IHsgZG93bmxvYWRGaWxlLCBnZXRMaXN0RmlsZUJ5RG9jbmFtZSwgcmVtb3ZlRmlsZSwgdXBsb2FkRmlsZSB9IGZyb20gJ0Avc2VydmljZXMvdXBsb2FkRmlsZSc7XHJcbmltcG9ydCB0eXBlIHsgUmNGaWxlLCBVcGxvYWRGaWxlLCBVcGxvYWRQcm9wcyB9IGZyb20gJ2FudGQvZXMvdXBsb2FkL2ludGVyZmFjZSc7XHJcblxyXG5pbXBvcnQgeyBIT1NUIH0gZnJvbSAnQC9zZXJ2aWNlcy91dGlscyc7XHJcbmltcG9ydCB7IHVzZUFjY2VzcyB9IGZyb20gJ0B1bWlqcy9tYXgnO1xyXG5cclxuY29uc3QgZ2V0QmFzZTY0ID0gKGZpbGU6IFJjRmlsZSk6IFByb21pc2U8c3RyaW5nPiA9PlxyXG4gIG5ldyBQcm9taXNlKChyZXNvbHZlLCByZWplY3QpID0+IHtcclxuICAgIGNvbnN0IHJlYWRlciA9IG5ldyBGaWxlUmVhZGVyKCk7XHJcbiAgICByZWFkZXIucmVhZEFzRGF0YVVSTChmaWxlKTtcclxuICAgIHJlYWRlci5vbmxvYWQgPSAoKSA9PiByZXNvbHZlKHJlYWRlci5yZXN1bHQgYXMgc3RyaW5nKTtcclxuICAgIHJlYWRlci5vbmVycm9yID0gKGVycm9yKSA9PiByZWplY3QoZXJyb3IpO1xyXG4gIH0pO1xyXG5cclxuY29uc3QgQXR0YWNoUGljdHVyZU9mRG9jdHlwZSA9ICh7XHJcbiAgZG9jdHlwZSxcclxuICBkb2NuYW1lLFxyXG4gIGlzX3ByaXZhdGUgPSAxLFxyXG4gIGZvbGRlciA9ICdIb21lL0F0dGFjaG1lbnRzJyxcclxuICBvcHRpbWl6ZSA9IGZhbHNlLFxyXG59OiB7XHJcbiAgZG9jdHlwZTogc3RyaW5nO1xyXG4gIGRvY25hbWU6IHN0cmluZztcclxuICBpc19wcml2YXRlPzogbnVtYmVyO1xyXG4gIGZvbGRlcj86IHN0cmluZztcclxuICBvcHRpbWl6ZT86IGJvb2xlYW47XHJcbn0pID0+IHtcclxuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW3VwbG9hZGluZywgc2V0VXBsb2FkaW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbZmlsZUxpc3QsIHNldEZpbGVMaXN0XSA9IHVzZVN0YXRlPFVwbG9hZEZpbGVbXT4oW10pO1xyXG4gIGNvbnN0IFtwcmV2aWV3T3Blbiwgc2V0UHJldmlld09wZW5dID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtwcmV2aWV3SW1hZ2UsIHNldFByZXZpZXdJbWFnZV0gPSB1c2VTdGF0ZSgnJyk7XHJcbiAgY29uc3QgW3ByZXZpZXdUaXRsZSwgc2V0UHJldmlld1RpdGxlXSA9IHVzZVN0YXRlKCcnKTtcclxuICBjb25zdCBbdXBkYXRpbmcsIHNldFVwZGF0aW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuXHJcbiAgY29uc3QgdXNlcl90b2tlbiA9IEpTT04ucGFyc2UobG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3Rva2VuJykpLnRva2VuO1xyXG4gIGNvbnN0IGdldEZpbGVMaXN0ID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgc2V0VXBkYXRpbmcodHJ1ZSk7XHJcbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IGdldExpc3RGaWxlQnlEb2NuYW1lKHsgZG9jdHlwZSwgbmFtZTogZG9jbmFtZSB9KTtcclxuICAgICAgLy8gY29uc29sZS5sb2cocmVzdWx0KTtcclxuICAgICAgbGV0IHByaXZhdGVJbWc6IGFueSA9IFtdO1xyXG4gICAgICBjb25zdCBfZmlsZUxpc3QgPSByZXN1bHQubWFwKChmOiBhbnkpID0+IHtcclxuICAgICAgICBpZiAoZi5maWxlX3VybC5zZWFyY2goJ3ByaXZhdGUnKSAhPT0gLTEpIHtcclxuICAgICAgICAgIHByaXZhdGVJbWcucHVzaCh7XHJcbiAgICAgICAgICAgIHVpZDogZi5uYW1lLFxyXG4gICAgICAgICAgICBuYW1lOiBmLmZpbGVfbmFtZSxcclxuICAgICAgICAgICAgdHlwZTogJ2ltYWdlL3BuZycsXHJcbiAgICAgICAgICAgIHN0YXR1czogJ2RvbmUnLFxyXG4gICAgICAgICAgICB1cmw6XHJcbiAgICAgICAgICAgICAgYCR7SE9TVH0vYXBpL3YyL2ZpbGUvZG93bmxvYWQ/dG9rZW49YCArXHJcbiAgICAgICAgICAgICAgdXNlcl90b2tlbiArXHJcbiAgICAgICAgICAgICAgYCZmaWxlX3VybD1gICtcclxuICAgICAgICAgICAgICBmLmZpbGVfdXJsICtcclxuICAgICAgICAgICAgICAnJmR0PScgK1xyXG4gICAgICAgICAgICAgIGRvY3R5cGUgK1xyXG4gICAgICAgICAgICAgICcmZG49JyArXHJcbiAgICAgICAgICAgICAgZG9jbmFtZSxcclxuICAgICAgICAgIH0pO1xyXG4gICAgICAgIH1cclxuICAgICAgICByZXR1cm4ge1xyXG4gICAgICAgICAgdWlkOiBmLm5hbWUsXHJcbiAgICAgICAgICBuYW1lOiBmLmZpbGVfbmFtZSxcclxuICAgICAgICAgIHR5cGU6ICdpbWFnZS9wbmcnLFxyXG4gICAgICAgICAgc3RhdHVzOiAnZG9uZScsXHJcbiAgICAgICAgICB1cmw6XHJcbiAgICAgICAgICAgIGAke0hPU1R9L2FwaS92Mi9maWxlL2Rvd25sb2FkP3Rva2VuPWAgK1xyXG4gICAgICAgICAgICB1c2VyX3Rva2VuICtcclxuICAgICAgICAgICAgYCZmaWxlX3VybD1gICtcclxuICAgICAgICAgICAgZi5maWxlX3VybCArXHJcbiAgICAgICAgICAgICcmZHQ9JyArXHJcbiAgICAgICAgICAgIGRvY3R5cGUgK1xyXG4gICAgICAgICAgICAnJmRuPScgK1xyXG4gICAgICAgICAgICBkb2NuYW1lLFxyXG4gICAgICAgIH07XHJcbiAgICAgIH0pO1xyXG4gICAgICBzZXRGaWxlTGlzdChwcml2YXRlSW1nKTtcclxuICAgICAgLy8gc2V0RmlsZUxpc3QoX2ZpbGVMaXN0KTtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICB9IGZpbmFsbHkge1xyXG4gICAgICBzZXRVcGRhdGluZyhmYWxzZSk7XHJcbiAgICB9XHJcbiAgfTtcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgZ2V0RmlsZUxpc3QoKTtcclxuICB9LCBbdXBsb2FkaW5nXSk7XHJcblxyXG4gIGNvbnN0IGhhbmRsZUNoYW5nZTogVXBsb2FkUHJvcHNbJ29uQ2hhbmdlJ10gPSAoaW5mbykgPT4ge1xyXG4gICAgbGV0IG5ld0ZpbGVMaXN0ID0gWy4uLmluZm8uZmlsZUxpc3RdO1xyXG5cclxuICAgIC8vIDEuIExpbWl0IHRoZSBudW1iZXIgb2YgdXBsb2FkZWQgZmlsZXNcclxuICAgIC8vIE9ubHkgdG8gc2hvdyB0d28gcmVjZW50IHVwbG9hZGVkIGZpbGVzLCBhbmQgb2xkIG9uZXMgd2lsbCBiZSByZXBsYWNlZCBieSB0aGUgbmV3XHJcbiAgICAvLyAyLiBSZWFkIGZyb20gcmVzcG9uc2UgYW5kIHNob3cgZmlsZSBsaW5rXHJcbiAgICBuZXdGaWxlTGlzdCA9IG5ld0ZpbGVMaXN0Lm1hcCgoZmlsZTogYW55KSA9PiB7XHJcbiAgICAgIC8vICAgY29uc29sZS5sb2coZmlsZSk7XHJcbiAgICAgIGlmIChmaWxlLnJlc3BvbnNlKSB7XHJcbiAgICAgICAgLy8gQ29tcG9uZW50IHdpbGwgc2hvdyBmaWxlLnVybCBhcyBsaW5rXHJcbiAgICAgICAgZmlsZS51aWQgPSBmaWxlLnJlc3BvbnNlLm5hbWU7XHJcbiAgICAgICAgZmlsZS51cmwgPVxyXG4gICAgICAgICAgYCR7SE9TVH0vYXBpL3YyL2ZpbGUvZG93bmxvYWQ/dG9rZW49YCArXHJcbiAgICAgICAgICB1c2VyX3Rva2VuICtcclxuICAgICAgICAgIGAmZmlsZV91cmw9YCArXHJcbiAgICAgICAgICBmaWxlLnJlc3BvbnNlLmZpbGVfdXJsICtcclxuICAgICAgICAgICcmZHQ9JyArXHJcbiAgICAgICAgICBkb2N0eXBlICtcclxuICAgICAgICAgICcmZG49JyArXHJcbiAgICAgICAgICBkb2NuYW1lO1xyXG4gICAgICB9XHJcbiAgICAgIHJldHVybiBmaWxlO1xyXG4gICAgfSk7XHJcbiAgICBjb25zb2xlLmxvZyhuZXdGaWxlTGlzdCk7XHJcbiAgICBzZXRGaWxlTGlzdChuZXdGaWxlTGlzdCk7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgaGFuZGxlVXBsb2FkID0gYXN5bmMgKG9wdGlvbnM6IGFueSkgPT4ge1xyXG4gICAgY29uc3QgeyBvblN1Y2Nlc3MsIG9uRXJyb3IsIGZpbGUsIG9uUHJvZ3Jlc3MgfSA9IG9wdGlvbnM7XHJcbiAgICB0cnkge1xyXG4gICAgICBzZXRVcGxvYWRpbmcodHJ1ZSk7XHJcbiAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IHVwbG9hZEZpbGUoe1xyXG4gICAgICAgIGZpbGUsXHJcbiAgICAgICAgZG9jdHlwZSxcclxuICAgICAgICBkb2NuYW1lLFxyXG4gICAgICAgIGlzX3ByaXZhdGUsXHJcbiAgICAgICAgZm9sZGVyLFxyXG4gICAgICAgIG9wdGltaXplLFxyXG4gICAgICB9KTtcclxuXHJcbiAgICAgIG9uU3VjY2VzcyhyZXMubWVzc2FnZSk7XHJcbiAgICB9IGNhdGNoIChlcnIpIHtcclxuICAgICAgY29uc29sZS5sb2coJ0Vyb29yOiAnLCBlcnIpO1xyXG4gICAgICBjb25zdCBlcnJvciA9IG5ldyBFcnJvcignU29tZSBlcnJvcicpO1xyXG4gICAgICBvbkVycm9yKHsgZXJyIH0pO1xyXG4gICAgfSBmaW5hbGx5IHtcclxuICAgICAgc2V0VXBsb2FkaW5nKGZhbHNlKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVDYW5jZWwgPSAoKSA9PiBzZXRQcmV2aWV3T3BlbihmYWxzZSk7XHJcblxyXG4gIGNvbnN0IGhhbmRsZVByZXZpZXcgPSBhc3luYyAoZmlsZTogVXBsb2FkRmlsZSkgPT4ge1xyXG4gICAgaWYgKCFmaWxlLnVybCAmJiAhZmlsZS5wcmV2aWV3KSB7XHJcbiAgICAgIGZpbGUucHJldmlldyA9IGF3YWl0IGdldEJhc2U2NChmaWxlLm9yaWdpbkZpbGVPYmogYXMgUmNGaWxlKTtcclxuICAgIH1cclxuICAgIGNvbnNvbGUubG9nKCdGaWxlIFVSTDonLCBmaWxlLnVybCk7XHJcbiAgICBzZXRQcmV2aWV3SW1hZ2UoZmlsZS51cmwgfHwgKGZpbGUucHJldmlldyBhcyBzdHJpbmcpKTtcclxuICAgIHNldFByZXZpZXdPcGVuKHRydWUpO1xyXG4gICAgc2V0UHJldmlld1RpdGxlKGZpbGUubmFtZSB8fCBmaWxlLnVybCEuc3Vic3RyaW5nKGZpbGUudXJsIS5sYXN0SW5kZXhPZignLycpICsgMSkpO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IHByb3BzOiBVcGxvYWRQcm9wcyA9IHtcclxuICAgIG9uUmVtb3ZlOiBhc3luYyAoZmlsZSkgPT4ge1xyXG4gICAgICBpZiAoZmlsZS5zdGF0dXMgPT09ICdkb25lJykge1xyXG4gICAgICAgIHRyeSB7XHJcbiAgICAgICAgICBhd2FpdCByZW1vdmVGaWxlKHtcclxuICAgICAgICAgICAgZmlkOiBmaWxlLnVpZCxcclxuICAgICAgICAgICAgZHQ6IGRvY3R5cGUsXHJcbiAgICAgICAgICAgIGRuOiBkb2NuYW1lLFxyXG4gICAgICAgICAgfSk7XHJcbiAgICAgICAgICBjb25zdCBuZXdGaWxlTGlzdCA9IGZpbGVMaXN0LmZpbHRlcigoZikgPT4gZi51aWQgIT09IGZpbGUudWlkKTtcclxuICAgICAgICAgIGNvbnNvbGUubG9nKCduZXdGaWxlTGlzdCcsIG5ld0ZpbGVMaXN0KTtcclxuICAgICAgICAgIHNldEZpbGVMaXN0KFsuLi5uZXdGaWxlTGlzdF0pO1xyXG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgICBtZXNzYWdlLmVycm9yKCdEZWxldGUgRXJyb3IsdHJ5IGFnYWluIScpO1xyXG4gICAgICAgIH0gZmluYWxseSB7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9LFxyXG4gICAgZmlsZUxpc3Q6IGZpbGVMaXN0LFxyXG4gICAgbXVsdGlwbGU6IHRydWUsXHJcbiAgICBjdXN0b21SZXF1ZXN0OiBoYW5kbGVVcGxvYWQsXHJcblxyXG4gICAgb25DaGFuZ2U6IGhhbmRsZUNoYW5nZSxcclxuICAgIC8vIHNob3dVcGxvYWRMaXN0OiB7XHJcbiAgICAvLyAgIHNob3dEb3dubG9hZEljb246IHRydWUsXHJcbiAgICAvLyAgIGRvd25sb2FkSWNvbjogPERvd25sb2FkT3V0bGluZWQ+PC9Eb3dubG9hZE91dGxpbmVkPixcclxuICAgIC8vIH0sXHJcbiAgICBvblByZXZpZXc6IGhhbmRsZVByZXZpZXcsXHJcbiAgICBsaXN0VHlwZTogJ3BpY3R1cmUtY2FyZCcsXHJcbiAgICBvbkRvd25sb2FkOiBhc3luYyAoZmlsZTogYW55KSA9PiB7XHJcbiAgICAgIC8vICAgY29uc29sZS5sb2coJ2Rvd25sb2FkJywgZmlsZSk7XHJcbiAgICAgIHRyeSB7XHJcbiAgICAgICAgY29uc3QgcmVzID0gYXdhaXQgZG93bmxvYWRGaWxlKHtcclxuICAgICAgICAgIGZpbGVfdXJsOiBmaWxlLnVybCxcclxuICAgICAgICAgIGZpZDogZmlsZS5maWQsXHJcbiAgICAgICAgICBkdDogZG9jdHlwZSxcclxuICAgICAgICAgIGRuOiBkb2NuYW1lLFxyXG4gICAgICAgIH0pO1xyXG4gICAgICAgIGNvbnNvbGUubG9nKHJlcyk7XHJcbiAgICAgIH0gY2F0Y2ggKGVycikge1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCdFcm9vcjogJywgZXJyKTtcclxuICAgICAgfSBmaW5hbGx5IHtcclxuICAgICAgfVxyXG4gICAgfSxcclxuICB9O1xyXG4gIGNvbnN0IHVwbG9hZEJ1dHRvbiA9IChcclxuICAgIDxkaXY+XHJcbiAgICAgIDxQbHVzT3V0bGluZWQgLz5cclxuICAgICAgPGRpdiBzdHlsZT17eyBtYXJnaW5Ub3A6IDggfX0+VXBsb2FkPC9kaXY+XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG4gIGNvbnN0IGFjY2VzcyA9IHVzZUFjY2VzcygpO1xyXG4gIGNvbnN0IGNhblVwZGF0ZSA9IGFjY2Vzcy5jYW5VcGRhdGVJblZpc2l0b3JNYW5hZ2VtZW50KCk7XHJcbiAgcmV0dXJuIChcclxuICAgIDw+XHJcbiAgICAgIDxDYXJkIHRpdGxlPVwi4bqibmhcIiBsb2FkaW5nPXtsb2FkaW5nfT5cclxuICAgICAgICB7Y2FuVXBkYXRlICYmIDxVcGxvYWQgey4uLnByb3BzfT57ZmlsZUxpc3QubGVuZ3RoID49IDggPyBudWxsIDogdXBsb2FkQnV0dG9ufTwvVXBsb2FkPn1cclxuICAgICAgICA8TW9kYWwgb3Blbj17cHJldmlld09wZW59IHRpdGxlPXtwcmV2aWV3VGl0bGV9IGZvb3Rlcj17bnVsbH0gb25DYW5jZWw9e2hhbmRsZUNhbmNlbH0+XHJcbiAgICAgICAgICA8aW1nIGFsdD1cImV4YW1wbGVcIiBzdHlsZT17eyB3aWR0aDogJzEwMCUnIH19IHNyYz17cHJldmlld0ltYWdlfSAvPlxyXG4gICAgICAgIDwvTW9kYWw+XHJcbiAgICAgIDwvQ2FyZD5cclxuICAgIDwvPlxyXG4gICk7XHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBBdHRhY2hQaWN0dXJlT2ZEb2N0eXBlO1xyXG4iLCJpbXBvcnQgQWN0aW9uUG9wQ29uZmlybSBmcm9tICdAL2NvbXBvbmVudHMvQWN0aW9uUG9wQ29uZmlybSc7XHJcbmltcG9ydCB7IHZpc2l0b3JJbmZvclNlcnZpY2UgfSBmcm9tICdAL3NlcnZpY2VzL3Zpc2l0b3InO1xyXG5pbXBvcnQgeyBtZXNzYWdlIH0gZnJvbSAnYW50ZCc7XHJcblxyXG5jb25zdCBSZW1vdmVGb3JtID0gKHBhcmFtczogeyByZWZyZXNoRm5jOiBhbnk7IG5hbWU6IHN0cmluZyB9KSA9PiB7XHJcbiAgY29uc3QgcmVtb3ZlRW5pdHkgPSBhc3luYyAoKSA9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICAvLyBhd2FpdCBnZW5lcmFsRGVsZXRlKCdpb3RfdmlzaXRvcl9pbmZvcicsIHBhcmFtcy5uYW1lKTtcclxuICAgICAgYXdhaXQgdmlzaXRvckluZm9yU2VydmljZS51cGRhdGVTdGF0dXNEZWxldGVkKFt7IG5hbWU6IHBhcmFtcy5uYW1lIH1dKTtcclxuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcclxuICAgICAgbWVzc2FnZS5lcnJvcihlcnJvci50b1N0cmluZygpKTtcclxuICAgIH1cclxuICB9O1xyXG4gIHJldHVybiAoXHJcbiAgICA8QWN0aW9uUG9wQ29uZmlybVxyXG4gICAgICBhY3Rpb25DYWxsPXtyZW1vdmVFbml0eX1cclxuICAgICAgcmVmcmVzaERhdGE9e3BhcmFtcy5yZWZyZXNoRm5jfVxyXG4gICAgICB0ZXh0PXsnUmVtb3ZlJ31cclxuICAgICAgYnV0dG9uVHlwZT17J3ByaW1hcnknfVxyXG4gICAgICBkYW5nZXI9e3RydWV9XHJcbiAgICAvPlxyXG4gICk7XHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBSZW1vdmVGb3JtO1xyXG4iLCJpbXBvcnQge1xyXG4gIEJ1dHRvbixcclxuICBDb2wsXHJcbiAgRGF0ZVBpY2tlcixcclxuICBGb3JtLFxyXG4gIElucHV0LFxyXG4gIG1lc3NhZ2UsXHJcbiAgUm93LFxyXG4gIFNlbGVjdCxcclxuICBTcGFjZSxcclxuICBUeXBvZ3JhcGh5LFxyXG4gIFVwbG9hZCxcclxuICBVcGxvYWRQcm9wcyxcclxufSBmcm9tICdhbnRkJztcclxuaW1wb3J0IHsgdXNlRWZmZWN0LCB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcclxuY29uc3QgeyBJdGVtIH0gPSBGb3JtO1xyXG5cclxuaW1wb3J0IHsgcmVtb3ZlRmlsZSwgdXBsb2FkRmlsZSB9IGZyb20gJ0Avc2VydmljZXMvdXBsb2FkRmlsZSc7XHJcbmltcG9ydCB7IGdlbmVyYXRlQVBJUGF0aCB9IGZyb20gJ0Avc2VydmljZXMvdXRpbHMnO1xyXG5pbXBvcnQge1xyXG4gIHZpc2l0b3JDYXJkU2VydmljZSxcclxuICB2aXNpdG9ySW5mb3JTZXJ2aWNlLFxyXG4gIHZpc2l0b3JMb2NhdGlvblNlcnZpY2UsXHJcbn0gZnJvbSAnQC9zZXJ2aWNlcy92aXNpdG9yJztcclxuaW1wb3J0IHsgRGVsZXRlRmlsbGVkLCBMb2FkaW5nT3V0bGluZWQsIFBsdXNPdXRsaW5lZCB9IGZyb20gJ0BhbnQtZGVzaWduL2ljb25zJztcclxuaW1wb3J0IHsgUHJvRm9ybVNlbGVjdCB9IGZyb20gJ0BhbnQtZGVzaWduL3Byby1jb21wb25lbnRzJztcclxuaW1wb3J0IHsgdXNlQWNjZXNzLCB1c2VJbnRsIH0gZnJvbSAnQHVtaWpzL21heCc7XHJcbmltcG9ydCBtb21lbnQgZnJvbSAnbW9tZW50JztcclxuY29uc3QgeyBPcHRpb24gfSA9IFNlbGVjdDtcclxuXHJcbmNvbnN0IHsgVGl0bGUsIFRleHQgfSA9IFR5cG9ncmFwaHk7XHJcblxyXG5jb25zdCBVcGRhdGVGb3JtID0gKHBhcmFtczogeyBjdXN0b21lcjogYW55OyByZWZyZXNoRm5jOiBhbnk7IGlzRXhwcmllZDogYm9vbGVhbiB9KSA9PiB7XHJcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtmb3JtXSA9IEZvcm0udXNlRm9ybSgpO1xyXG4gIGNvbnNvbGUubG9nKCdwYXJhbXMuY3VzdG9tZXInLCBwYXJhbXMuY3VzdG9tZXIpO1xyXG5cclxuICBjb25zdCBbaWRDYXJkQ29kZSwgc2V0SWRDYXJkQ29kZV0gPSB1c2VTdGF0ZSgnQ29kZScpO1xyXG4gIGNvbnN0IFtzZWFyY2hpbmcsIHNldFNlYXJjaGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW29wdGlvbnMsIHNldE9wdGlvbnNdID0gdXNlU3RhdGUoW10pO1xyXG4gIGNvbnN0IFtsaXN0Q2FyZCwgc2V0TGlzdENhcmRdID0gdXNlU3RhdGUoW10pO1xyXG5cclxuICBjb25zdCBbaW1hZ2VVcmwsIHNldEltYWdlVXJsXSA9IHVzZVN0YXRlPHN0cmluZz4oKTtcclxuICBjb25zdCBbdXBsb2FkaW5nLCBzZXRVcGxvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtmaWxlTGlzdCwgc2V0RmlsZUxpc3RdID0gdXNlU3RhdGU8YW55W10+KFtdKTtcclxuXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGlmIChPYmplY3Qua2V5cyhwYXJhbXMuY3VzdG9tZXIpLmxlbmd0aCkge1xyXG4gICAgICBmb3JtLnNldEZpZWxkc1ZhbHVlKHBhcmFtcy5jdXN0b21lcik7XHJcbiAgICB9XHJcbiAgICBzZXRJbWFnZVVybChwYXJhbXMuY3VzdG9tZXIuaW1hZ2UgfHwgJycpO1xyXG4gICAgY29uc3QgbG9jYXRpb25zID0gcGFyYW1zLmN1c3RvbWVyLmxvY2F0aW9uID8gcGFyYW1zLmN1c3RvbWVyLmxvY2F0aW9uLnNwbGl0KCcsJykgOiBbXTtcclxuICAgIGZvcm0uc2V0RmllbGRWYWx1ZSgnbG9jYXRpb24nLCBsb2NhdGlvbnMpO1xyXG4gIH0sIFtwYXJhbXMuY3VzdG9tZXJdKTtcclxuXHJcbiAgLy8gY29uc3QgbGlzdENhcmRJRCA9IGFzeW5jICgpID0+IHtcclxuICAvLyAgIHRyeSB7XHJcbiAgLy8gICAgIHNldExvYWRpbmcodHJ1ZSk7XHJcbiAgLy8gICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHNzY3JpcHRHZW5lcmFsTGlzdCh7XHJcbiAgLy8gICAgICAgZG9jX25hbWU6ICdpb3RfdmlzaXRvcl9jYXJkJyxcclxuICAvLyAgICAgICBmaWx0ZXJzOiBbXSxcclxuICAvLyAgICAgICBwYWdlOiAxLFxyXG4gIC8vICAgICAgIHNpemU6IDEwMDAsXHJcbiAgLy8gICAgICAgZmllbGRzOiBbJyonXSxcclxuICAvLyAgICAgfSk7XHJcbiAgLy8gICAgIGNvbnN0IGNhcmRkYXRhID0gcmVzdWx0LmRhdGEubWFwKChzOiBhbnkpID0+IHtcclxuICAvLyAgICAgICBzLnNlYXJjaF90ZXh0ID0gdG9Mb3dlckNhc2VOb25BY2NlbnRWaWV0bmFtZXNlKHMubmFtZSk7XHJcbiAgLy8gICAgICAgcmV0dXJuIHM7XHJcbiAgLy8gICAgIH0pO1xyXG4gIC8vICAgICBzZXRMaXN0Q2FyZChjYXJkZGF0YSk7XHJcbiAgLy8gICB9IGNhdGNoIChlcnJvcikge1xyXG4gIC8vICAgICBjb25zb2xlLmxvZyhlcnJvcik7XHJcbiAgLy8gICB9IGZpbmFsbHkge1xyXG4gIC8vICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcclxuICAvLyAgIH1cclxuICAvLyB9O1xyXG4gIC8vIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgLy8gICBsaXN0Q2FyZElEKCk7XHJcbiAgLy8gfSwgW10pO1xyXG5cclxuICAvLyBjb25zdCBoYW5kbGVTZWFyY2ggPSBhc3luYyAobmV3VmFsdWU6IHN0cmluZykgPT4ge1xyXG4gIC8vICAgdHJ5IHtcclxuICAvLyAgICAgc2V0U2VhcmNoaW5nKHRydWUpO1xyXG4gIC8vICAgICBpZiAobmV3VmFsdWUpIHtcclxuICAvLyAgICAgICBjb25zdCBzZWFyY2hfdGV4dCA9IHRvTG93ZXJDYXNlTm9uQWNjZW50VmlldG5hbWVzZShuZXdWYWx1ZS50cmltKCkpO1xyXG4gIC8vICAgICAgIGxldCBkYXRhID0gbGlzdENhcmQuZmlsdGVyKChzOiBhbnkpID0+IHtcclxuICAvLyAgICAgICAgIHJldHVybiBzLnNlYXJjaF90ZXh0Py5pbmRleE9mKHNlYXJjaF90ZXh0KSA+PSAwO1xyXG4gIC8vICAgICAgIH0pO1xyXG4gIC8vICAgICAgIGxldCBkYXRhX3NlbGVjdDogYW55ID0gZGF0YS5tYXAoKGQ6IGFueSkgPT4ge1xyXG4gIC8vICAgICAgICAgcmV0dXJuIHtcclxuICAvLyAgICAgICAgICAgdmFsdWU6IGQubmFtZSxcclxuICAvLyAgICAgICAgICAgbGFiZWw6IGAke2QubmFtZX1gLFxyXG4gIC8vICAgICAgICAgfTtcclxuICAvLyAgICAgICB9KTtcclxuICAvLyAgICAgICAvLyBjb25zb2xlLmxvZyh7IG5ld1ZhbHVlLCBkYXRhIH0pO1xyXG4gIC8vICAgICAgIHNldE9wdGlvbnMoZGF0YV9zZWxlY3QpO1xyXG4gIC8vICAgICB9IGVsc2Uge1xyXG4gIC8vICAgICAgIHNldE9wdGlvbnMoW10pO1xyXG4gIC8vICAgICB9XHJcbiAgLy8gICB9IGNhdGNoIChlcnJvcikge1xyXG4gIC8vICAgfSBmaW5hbGx5IHtcclxuICAvLyAgICAgc2V0U2VhcmNoaW5nKGZhbHNlKTtcclxuICAvLyAgIH1cclxuICAvLyB9O1xyXG4gIC8vIGNvbnN0IGhhbmRsZUNoYW5nZSA9IChuZXdWYWx1ZTogc3RyaW5nKSA9PiB7XHJcbiAgLy8gICBmb3JtLnNldEZpZWxkVmFsdWUoJ2NhcmRfaWQnLCBuZXdWYWx1ZSk7XHJcbiAgLy8gICBzZXRJZENhcmRDb2RlKG5ld1ZhbHVlKTtcclxuICAvLyB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVSZW1vdmUgPSBhc3luYyAoZmlsZTogYW55KSA9PiB7XHJcbiAgICBpZiAoZmlsZS5zdGF0dXMgPT09ICdkb25lJykge1xyXG4gICAgICB0cnkge1xyXG4gICAgICAgIGF3YWl0IHJlbW92ZUZpbGUoe1xyXG4gICAgICAgICAgZmlkOiBmaWxlLnVpZCxcclxuICAgICAgICAgIGR0OiAnJyxcclxuICAgICAgICAgIGRuOiAnJyxcclxuICAgICAgICB9KTtcclxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICBtZXNzYWdlLmVycm9yKCdEZWxldGUgRXJyb3IsdHJ5IGFnYWluIScpO1xyXG4gICAgICB9IGZpbmFsbHkge1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgaGFuZGxlQ2hhbmdlVXBsb2FkOiBVcGxvYWRQcm9wc1snb25DaGFuZ2UnXSA9IChpbmZvKSA9PiB7XHJcbiAgICBjb25zb2xlLmxvZygnaGFuZGxlQ2hhbmdlVXBsb2FkJywgaW5mbyk7XHJcbiAgICBsZXQgbmV3RmlsZUxpc3QgPSBbLi4uaW5mby5maWxlTGlzdF07XHJcbiAgICAvLyAxLiBMaW1pdCB0aGUgbnVtYmVyIG9mIHVwbG9hZGVkIGZpbGVzXHJcbiAgICAvLyBPbmx5IHRvIHNob3cgdHdvIHJlY2VudCB1cGxvYWRlZCBmaWxlcywgYW5kIG9sZCBvbmVzIHdpbGwgYmUgcmVwbGFjZWQgYnkgdGhlIG5ld1xyXG4gICAgLy8gMi4gUmVhZCBmcm9tIHJlc3BvbnNlIGFuZCBzaG93IGZpbGUgbGlua1xyXG4gICAgbmV3RmlsZUxpc3QgPSBuZXdGaWxlTGlzdC5tYXAoKGZpbGU6IGFueSkgPT4ge1xyXG4gICAgICBpZiAoZmlsZS5yZXNwb25zZSkge1xyXG4gICAgICAgIC8vIENvbXBvbmVudCB3aWxsIHNob3cgZmlsZS51cmwgYXMgbGlua1xyXG4gICAgICAgIGZpbGUudWlkID0gZmlsZS5yZXNwb25zZS5uYW1lO1xyXG4gICAgICAgIGNvbnN0IHVzZXJkYXRhID0gSlNPTi5wYXJzZShsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgndG9rZW4nKSB8fCAne30nKTtcclxuICAgICAgICBmaWxlLnVybCA9IGdlbmVyYXRlQVBJUGF0aChcclxuICAgICAgICAgICdhcGkvdjIvZmlsZS9kb3dubG9hZD9maWxlX3VybD0nICsgZmlsZS5yZXNwb25zZS5maWxlX3VybCArICcmdG9rZW49JyArIHVzZXJkYXRhPy50b2tlbixcclxuICAgICAgICApO1xyXG4gICAgICAgIGZpbGUucmF3X3VybCA9IGZpbGUucmVzcG9uc2UuZmlsZV91cmw7XHJcbiAgICAgIH1cclxuICAgICAgcmV0dXJuIGZpbGU7XHJcbiAgICB9KTtcclxuICAgIHNldEZpbGVMaXN0KG5ld0ZpbGVMaXN0KTtcclxuICAgIGlmIChuZXdGaWxlTGlzdC5sZW5ndGgpIHtcclxuICAgICAgc2V0SW1hZ2VVcmwobmV3RmlsZUxpc3RbMF0/LnVybCB8fCAnJyk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgaGFuZGxlVXBsb2FkID0gYXN5bmMgKG9wdGlvbnM6IGFueSkgPT4ge1xyXG4gICAgY29uc3QgeyBvblN1Y2Nlc3MsIG9uRXJyb3IsIGZpbGUgfSA9IG9wdGlvbnM7XHJcbiAgICB0cnkge1xyXG4gICAgICBzZXRVcGxvYWRpbmcodHJ1ZSk7XHJcbiAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IHVwbG9hZEZpbGUoe1xyXG4gICAgICAgIGZpbGUsXHJcbiAgICAgICAgZG9jdHlwZTogJycsXHJcbiAgICAgICAgZG9jbmFtZTogJycsXHJcbiAgICAgICAgaXNfcHJpdmF0ZTogMSxcclxuICAgICAgICBmb2xkZXI6ICdIb21lL0F0dGFjaG1lbnRzJyxcclxuICAgICAgICBvcHRpbWl6ZTogZmFsc2UsXHJcbiAgICAgIH0pO1xyXG4gICAgICBjb25zb2xlLmxvZygnZmlsZV91cmwnLCByZXMpO1xyXG4gICAgICBvblN1Y2Nlc3MocmVzLm1lc3NhZ2UpO1xyXG4gICAgfSBjYXRjaCAoZXJyKSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKCdFcm9vcjogJywgZXJyKTtcclxuICAgICAgb25FcnJvcih7IGVyciB9KTtcclxuICAgIH0gZmluYWxseSB7XHJcbiAgICAgIHNldFVwbG9hZGluZyhmYWxzZSk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgaGFuZGxlT2sgPSAoKSA9PiB7XHJcbiAgICBmb3JtLnN1Ym1pdCgpO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IHVwbG9hZEJ1dHRvbiA9IChcclxuICAgIDxkaXY+XHJcbiAgICAgIHt1cGxvYWRpbmcgPyA8TG9hZGluZ091dGxpbmVkIC8+IDogPFBsdXNPdXRsaW5lZCAvPn1cclxuICAgICAgPGRpdiBzdHlsZT17eyBtYXJnaW5Ub3A6IDggfX0+VXBsb2FkPC9kaXY+XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG4gIGNvbnN0IGFjY2VzcyA9IHVzZUFjY2VzcygpO1xyXG4gIGNvbnN0IGNhblVwZGF0ZSA9IGFjY2Vzcy5jYW5VcGRhdGVJblZpc2l0b3JNYW5hZ2VtZW50KCk7XHJcbiAgY29uc3QgaW50bCA9IHVzZUludGwoKTtcclxuICByZXR1cm4gKFxyXG4gICAgPD5cclxuICAgICAgPEZvcm1cclxuICAgICAgICBsYXlvdXQ9XCJob3Jpem9udGFsXCJcclxuICAgICAgICBsYWJlbENvbD17eyBzcGFuOiAyNCB9fVxyXG4gICAgICAgIGxhYmVsQWxpZ249XCJsZWZ0XCJcclxuICAgICAgICBmb3JtPXtmb3JtfVxyXG4gICAgICAgIG9uRmluaXNoPXthc3luYyAodmFsdWU6IGFueSkgPT4ge1xyXG4gICAgICAgICAgdHJ5IHtcclxuICAgICAgICAgICAgaWYgKCFjYW5VcGRhdGUpIHtcclxuICAgICAgICAgICAgICBtZXNzYWdlLmVycm9yKCdZb3UgZG8gbm90IGhhdmUgcGVybWlzc2lvbiB0byB1cGRhdGUhJyk7XHJcbiAgICAgICAgICAgICAgcmV0dXJuO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIHZhbHVlLmltYWdlID0gJyc7XHJcbiAgICAgICAgICAgIGlmIChmaWxlTGlzdC5sZW5ndGgpIHtcclxuICAgICAgICAgICAgICB2YWx1ZS5pbWFnZSA9IGZpbGVMaXN0WzBdLnJhd191cmw7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgdmFsdWUuam9pbl9kYXRlID0gbW9tZW50KHZhbHVlLmpvaW5fZGF0ZSkuZm9ybWF0KCdZWVlZLU1NLUREJyk7XHJcbiAgICAgICAgICAgIHZhbHVlLmV4cGlyZWRfZGF5ID0gbW9tZW50KHZhbHVlLmV4cGlyZWRfZGF5KS5mb3JtYXQoJ1lZWVktTU0tREQnKTtcclxuICAgICAgICAgICAgdmFsdWUuYmlydGhkYXkgPSBtb21lbnQodmFsdWUuYmlydGhkYXkpLmZvcm1hdCgnWVlZWS1NTS1ERCcpO1xyXG4gICAgICAgICAgICB2YWx1ZS51c2VyX3N0YXR1cyA9ICd1cGRhdGVkJztcclxuICAgICAgICAgICAgdmFsdWUubmFtZSA9IHBhcmFtcy5jdXN0b21lci5uYW1lO1xyXG4gICAgICAgICAgICB2YWx1ZS5sb2NhdGlvbiA9IHZhbHVlLmxvY2F0aW9uLmpvaW4oJywnKTtcclxuICAgICAgICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgdmlzaXRvckluZm9yU2VydmljZS51cGRhdGUodmFsdWUpO1xyXG4gICAgICAgICAgICBtZXNzYWdlLnN1Y2Nlc3MoJ1N1Y2Nlc3MhJyk7XHJcbiAgICAgICAgICAgIGlmIChwYXJhbXMucmVmcmVzaEZuYykge1xyXG4gICAgICAgICAgICAgIGF3YWl0IHBhcmFtcy5yZWZyZXNoRm5jKCk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcclxuICAgICAgICAgICAgbWVzc2FnZS5lcnJvcignTcOjIHRo4bq7IMSRw6MgxJHGsOG7o2Mgc+G7rSBk4bulbmcnKTtcclxuICAgICAgICAgIH0gZmluYWxseSB7XHJcbiAgICAgICAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH19XHJcbiAgICAgID5cclxuICAgICAgICA8Um93IGd1dHRlcj17NX0+XHJcbiAgICAgICAgICA8Q29sXHJcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImd1dHRlci1yb3dcIlxyXG4gICAgICAgICAgICBtZD17OH1cclxuICAgICAgICAgICAgc3R5bGU9e3tcclxuICAgICAgICAgICAgICBkaXNwbGF5OiAnZmxleCcsXHJcbiAgICAgICAgICAgICAganVzdGlmeUNvbnRlbnQ6ICdjZW50ZXInLFxyXG4gICAgICAgICAgICB9fVxyXG4gICAgICAgICAgPlxyXG4gICAgICAgICAgICB7LyogPEF2YXRhciBzaXplPXsxODB9IHNyYz17dXJsfSAvPiAqL31cclxuICAgICAgICAgICAgPFNwYWNlIGRpcmVjdGlvbj1cInZlcnRpY2FsXCIgYWxpZ249XCJjZW50ZXJcIiBzaXplPVwic21hbGxcIj5cclxuICAgICAgICAgICAgICA8SXRlbSBsYWJlbENvbD17eyBzcGFuOiAyNCB9fSBuYW1lPVwiaW1hZ2VcIj5cclxuICAgICAgICAgICAgICAgIDxVcGxvYWRcclxuICAgICAgICAgICAgICAgICAgbmFtZT1cInVwbG9hZC1pbWFnZVwiXHJcbiAgICAgICAgICAgICAgICAgIGxpc3RUeXBlPVwicGljdHVyZS1jYXJkXCJcclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYXZhdGFyLXVwbG9hZGVyXCJcclxuICAgICAgICAgICAgICAgICAgc2hvd1VwbG9hZExpc3Q9e2ZhbHNlfVxyXG4gICAgICAgICAgICAgICAgICBjdXN0b21SZXF1ZXN0PXtoYW5kbGVVcGxvYWR9XHJcbiAgICAgICAgICAgICAgICAgIG9uUmVtb3ZlPXtoYW5kbGVSZW1vdmV9XHJcbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVDaGFuZ2VVcGxvYWR9XHJcbiAgICAgICAgICAgICAgICAgIGFjY2VwdD1cImltYWdlL3BuZywgaW1hZ2UvanBlZywgaW1hZ2Uvc3ZnK3htbFwiXHJcbiAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgIHtpbWFnZVVybCA/IChcclxuICAgICAgICAgICAgICAgICAgICA8aW1nXHJcbiAgICAgICAgICAgICAgICAgICAgICBzcmM9e2dlbmVyYXRlQVBJUGF0aCgnYXBpL3YyL2ZpbGUvZG93bmxvYWQ/ZmlsZV91cmw9JyArIGltYWdlVXJsKX1cclxuICAgICAgICAgICAgICAgICAgICAgIGFsdD1cImF2YXRhclwiXHJcbiAgICAgICAgICAgICAgICAgICAgICBzdHlsZT17eyB3aWR0aDogJzEwMCUnIH19XHJcbiAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgICAgICB1cGxvYWRCdXR0b25cclxuICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgIDwvVXBsb2FkPlxyXG4gICAgICAgICAgICAgIDwvSXRlbT5cclxuICAgICAgICAgICAgICB7aW1hZ2VVcmwgPyAoXHJcbiAgICAgICAgICAgICAgICA8PlxyXG4gICAgICAgICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgc2V0RmlsZUxpc3QoW10pO1xyXG4gICAgICAgICAgICAgICAgICAgICAgc2V0SW1hZ2VVcmwoJycpO1xyXG4gICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICA8RGVsZXRlRmlsbGVkPjwvRGVsZXRlRmlsbGVkPlxyXG4gICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgICAgIDwvPlxyXG4gICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICA8PjwvPlxyXG4gICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgPFRpdGxlIGxldmVsPXs1fT57cGFyYW1zLmN1c3RvbWVyLmZ1bGxuYW1lfTwvVGl0bGU+XHJcbiAgICAgICAgICAgICAgPFRleHQgc3Ryb25nPiN7cGFyYW1zLmN1c3RvbWVyLmNhcmRfaWR9PC9UZXh0PlxyXG4gICAgICAgICAgICA8L1NwYWNlPlxyXG4gICAgICAgICAgPC9Db2w+XHJcbiAgICAgICAgICA8Q29sIGNsYXNzTmFtZT1cImd1dHRlci1yb3dcIiBtZD17MTZ9PlxyXG4gICAgICAgICAgICA8Um93IGd1dHRlcj17NX0+XHJcbiAgICAgICAgICAgICAgey8qIDxDb2wgY2xhc3NOYW1lPVwiZ3V0dGVyLXJvd1wiIG1kPXsxMn0+XHJcbiAgICAgICAgICAgICAgICA8SXRlbVxyXG4gICAgICAgICAgICAgICAgICBsYWJlbD1cIlPhu5EgdGjhu6kgdOG7sVwiXHJcbiAgICAgICAgICAgICAgICAgIGxhYmVsQ29sPXt7IHNwYW46IDI0IH19XHJcbiAgICAgICAgICAgICAgICAgIHJ1bGVzPXtbXHJcbiAgICAgICAgICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsXHJcbiAgICAgICAgICAgICAgICAgICAgICBtZXNzYWdlOiAnQuG6r3QgYnXhu5ljIMSRaeG7gW4nLFxyXG4gICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgIF19XHJcbiAgICAgICAgICAgICAgICAgIG5hbWU9XCJjdXN0b21lcl9vcmRlclwiXHJcbiAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgIDxJbnB1dE51bWJlciBzdHlsZT17eyB3aWR0aDogJzEwMCUnIH19IC8+XHJcbiAgICAgICAgICAgICAgICA8L0l0ZW0+XHJcbiAgICAgICAgICAgICAgPC9Db2w+ICovfVxyXG4gICAgICAgICAgICAgIDxDb2wgY2xhc3NOYW1lPVwiZ3V0dGVyLXJvd1wiIG1kPXsxMn0+XHJcbiAgICAgICAgICAgICAgICA8SXRlbVxyXG4gICAgICAgICAgICAgICAgICBsYWJlbD17aW50bC5mb3JtYXRNZXNzYWdlKHsgaWQ6ICdjb21tb24uY2FyZF9pZCcgfSl9XHJcbiAgICAgICAgICAgICAgICAgIGxhYmVsQ29sPXt7IHNwYW46IDI0IH19XHJcbiAgICAgICAgICAgICAgICAgIHJ1bGVzPXtbXHJcbiAgICAgICAgICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsXHJcbiAgICAgICAgICAgICAgICAgICAgICBtZXNzYWdlOiAnQuG6r3QgYnXhu5ljIMSRaeG7gW4nLFxyXG4gICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgIF19XHJcbiAgICAgICAgICAgICAgICAgIG5hbWU9XCJjYXJkX2lkXCJcclxuICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgey8qIDxTZWxlY3RcclxuICAgICAgICAgICAgICAgICAgICBzaG93U2VhcmNoXHJcbiAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJTZWFyY2ggdG8gU2VsZWN0XCJcclxuICAgICAgICAgICAgICAgICAgICBvcHRpb25GaWx0ZXJQcm9wPVwiY2hpbGRyZW5cIlxyXG4gICAgICAgICAgICAgICAgICAgIGxvYWRpbmc9e3NlYXJjaGluZ31cclxuICAgICAgICAgICAgICAgICAgICBvblNlYXJjaD17aGFuZGxlU2VhcmNofVxyXG4gICAgICAgICAgICAgICAgICAgIHN0eWxlPXt7IHdpZHRoOiAnMTAwJScgfX1cclxuICAgICAgICAgICAgICAgICAgICBub3RGb3VuZENvbnRlbnQ9e251bGx9XHJcbiAgICAgICAgICAgICAgICAgICAgb3B0aW9ucz17bGlzdENhcmR9XHJcbiAgICAgICAgICAgICAgICAgICAgc2hvd0Fycm93PXtmYWxzZX1cclxuICAgICAgICAgICAgICAgICAgICBmaWx0ZXJPcHRpb249e2ZhbHNlfVxyXG4gICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVDaGFuZ2V9XHJcbiAgICAgICAgICAgICAgICAgIC8+ICovfVxyXG4gICAgICAgICAgICAgICAgICA8UHJvRm9ybVNlbGVjdFxyXG4gICAgICAgICAgICAgICAgICAgIHNob3dTZWFyY2hcclxuICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj17J1NlYXJjaCB0byBTZWxlY3QnfVxyXG4gICAgICAgICAgICAgICAgICAgIHN0eWxlPXt7IHdpZHRoOiAnMTAwJScgfX1cclxuICAgICAgICAgICAgICAgICAgICByZXF1ZXN0PXthc3luYyAoKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCB2aXNpdG9yQ2FyZFNlcnZpY2UuZ2V0TGlzdCgpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHJlc3VsdC5kYXRhLm1hcCgoaXRlbTogYW55KSA9PiAoe1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBsYWJlbDogaXRlbS5jYXJkX251bWJlcixcclxuICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU6IGl0ZW0ubmFtZSxcclxuICAgICAgICAgICAgICAgICAgICAgIH0pKTtcclxuICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgPC9JdGVtPlxyXG4gICAgICAgICAgICAgIDwvQ29sPlxyXG4gICAgICAgICAgICAgIDxDb2wgY2xhc3NOYW1lPVwiZ3V0dGVyLXJvd1wiIG1kPXsxMn0+XHJcbiAgICAgICAgICAgICAgICA8SXRlbVxyXG4gICAgICAgICAgICAgICAgICBsYWJlbD17aW50bC5mb3JtYXRNZXNzYWdlKHsgaWQ6ICdjb21tb24uc3RhdHVzJyB9KX1cclxuICAgICAgICAgICAgICAgICAgbGFiZWxDb2w9e3sgc3BhbjogMjQgfX1cclxuICAgICAgICAgICAgICAgICAgcnVsZXM9e1tcclxuICAgICAgICAgICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSxcclxuICAgICAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICdC4bqvdCBideG7mWMgxJFp4buBbicsXHJcbiAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgXX1cclxuICAgICAgICAgICAgICAgICAgbmFtZT1cInVzZXJfc3RhdHVzXCJcclxuICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgPFByb0Zvcm1TZWxlY3RcclxuICAgICAgICAgICAgICAgICAgICBzaG93U2VhcmNoXHJcbiAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9eydTZWFyY2ggdG8gU2VsZWN0J31cclxuICAgICAgICAgICAgICAgICAgICBzdHlsZT17eyB3aWR0aDogJzEwMCUnIH19XHJcbiAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWRcclxuICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgIDwvSXRlbT5cclxuICAgICAgICAgICAgICA8L0NvbD5cclxuICAgICAgICAgICAgICA8Q29sIGNsYXNzTmFtZT1cImd1dHRlci1yb3dcIiBtZD17MTJ9PlxyXG4gICAgICAgICAgICAgICAgPEl0ZW1cclxuICAgICAgICAgICAgICAgICAgbGFiZWw9e2ludGwuZm9ybWF0TWVzc2FnZSh7IGlkOiAnY29tbW9uLmZ1bGxuYW1lJyB9KX1cclxuICAgICAgICAgICAgICAgICAgbGFiZWxDb2w9e3sgc3BhbjogMjQgfX1cclxuICAgICAgICAgICAgICAgICAgcnVsZXM9e1tcclxuICAgICAgICAgICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSxcclxuICAgICAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICdC4bqvdCBideG7mWMgxJFp4buBbicsXHJcbiAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgXX1cclxuICAgICAgICAgICAgICAgICAgbmFtZT1cImZ1bGxuYW1lXCJcclxuICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgPElucHV0IHBsYWNlaG9sZGVyPVwiTmd1eeG7hW4gVsSDbiBBXCIgLz5cclxuICAgICAgICAgICAgICAgIDwvSXRlbT5cclxuICAgICAgICAgICAgICA8L0NvbD5cclxuICAgICAgICAgICAgICA8Q29sIGNsYXNzTmFtZT1cImd1dHRlci1yb3dcIiBtZD17MTJ9PlxyXG4gICAgICAgICAgICAgICAgPEl0ZW1cclxuICAgICAgICAgICAgICAgICAgbGFiZWw9e2ludGwuZm9ybWF0TWVzc2FnZSh7IGlkOiAnY29tbW9uLmdlbmRlcicgfSl9XHJcbiAgICAgICAgICAgICAgICAgIGxhYmVsQ29sPXt7IHNwYW46IDI0IH19XHJcbiAgICAgICAgICAgICAgICAgIHJ1bGVzPXtbXHJcbiAgICAgICAgICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsXHJcbiAgICAgICAgICAgICAgICAgICAgICBtZXNzYWdlOiAnQuG6r3QgYnXhu5ljIMSRaeG7gW4nLFxyXG4gICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgIF19XHJcbiAgICAgICAgICAgICAgICAgIG5hbWU9XCJnZW5kZXJcIlxyXG4gICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICA8U2VsZWN0IG9wdGlvbnM9e1t7IHZhbHVlOiAnTmFtJyB9LCB7IHZhbHVlOiAnTuG7rycgfV19IC8+XHJcbiAgICAgICAgICAgICAgICA8L0l0ZW0+XHJcbiAgICAgICAgICAgICAgPC9Db2w+XHJcbiAgICAgICAgICAgICAgPENvbCBjbGFzc05hbWU9XCJndXR0ZXItcm93XCIgbWQ9ezEyfT5cclxuICAgICAgICAgICAgICAgIDxJdGVtXHJcbiAgICAgICAgICAgICAgICAgIGxhYmVsPXtpbnRsLmZvcm1hdE1lc3NhZ2UoeyBpZDogJ2NvbW1vbi5iaXJ0aGRheScgfSl9XHJcbiAgICAgICAgICAgICAgICAgIGxhYmVsQ29sPXt7IHNwYW46IDI0IH19XHJcbiAgICAgICAgICAgICAgICAgIG5hbWU9XCJiaXJ0aGRheVwiXHJcbiAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgIDxEYXRlUGlja2VyIHN0eWxlPXt7IHdpZHRoOiAnMTAwJScgfX0gLz5cclxuICAgICAgICAgICAgICAgIDwvSXRlbT5cclxuICAgICAgICAgICAgICA8L0NvbD5cclxuICAgICAgICAgICAgICA8Q29sIGNsYXNzTmFtZT1cImd1dHRlci1yb3dcIiBtZD17MTJ9PlxyXG4gICAgICAgICAgICAgICAgPEl0ZW1cclxuICAgICAgICAgICAgICAgICAgbGFiZWw9e2ludGwuZm9ybWF0TWVzc2FnZSh7IGlkOiAnY29tbW9uLmFkZHJlc3MnIH0pfVxyXG4gICAgICAgICAgICAgICAgICBsYWJlbENvbD17eyBzcGFuOiAyNCB9fVxyXG4gICAgICAgICAgICAgICAgICBuYW1lPVwiYWRkcmVzc1wiXHJcbiAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgIDxJbnB1dCBwbGFjZWhvbGRlcj1cIjU2OCBMw70gVGjGsOG7nW5nIEvhu4d0LCBQaMaw4budbmcgMTRcIiAvPlxyXG4gICAgICAgICAgICAgICAgPC9JdGVtPlxyXG4gICAgICAgICAgICAgIDwvQ29sPlxyXG4gICAgICAgICAgICAgIDxDb2wgY2xhc3NOYW1lPVwiZ3V0dGVyLXJvd1wiIG1kPXsxMn0+XHJcbiAgICAgICAgICAgICAgICA8SXRlbVxyXG4gICAgICAgICAgICAgICAgICBsYWJlbD17aW50bC5mb3JtYXRNZXNzYWdlKHsgaWQ6ICdjb21tb24uaWRlbnRpdHlfY2FyZCcgfSl9XHJcbiAgICAgICAgICAgICAgICAgIGxhYmVsQ29sPXt7IHNwYW46IDI0IH19XHJcbiAgICAgICAgICAgICAgICAgIG5hbWU9XCJwYXNzcG9ydFwiXHJcbiAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgIDxJbnB1dCAvPlxyXG4gICAgICAgICAgICAgICAgPC9JdGVtPlxyXG4gICAgICAgICAgICAgIDwvQ29sPlxyXG4gICAgICAgICAgICAgIDxDb2wgY2xhc3NOYW1lPVwiZ3V0dGVyLXJvd1wiIG1kPXsxMn0+XHJcbiAgICAgICAgICAgICAgICA8SXRlbVxyXG4gICAgICAgICAgICAgICAgICBsYWJlbD17aW50bC5mb3JtYXRNZXNzYWdlKHsgaWQ6ICdjb21tb24uY291bnRyeScgfSl9XHJcbiAgICAgICAgICAgICAgICAgIGxhYmVsQ29sPXt7IHNwYW46IDI0IH19XHJcbiAgICAgICAgICAgICAgICAgIG5hbWU9XCJjb3VudHJ5XCJcclxuICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgPElucHV0IGRlZmF1bHRWYWx1ZT17J1Zp4buHdCBOYW0nfSAvPlxyXG4gICAgICAgICAgICAgICAgPC9JdGVtPlxyXG4gICAgICAgICAgICAgIDwvQ29sPlxyXG4gICAgICAgICAgICAgIDxDb2wgY2xhc3NOYW1lPVwiZ3V0dGVyLXJvd1wiIG1kPXsxMn0+XHJcbiAgICAgICAgICAgICAgICA8SXRlbVxyXG4gICAgICAgICAgICAgICAgICBsYWJlbD17aW50bC5mb3JtYXRNZXNzYWdlKHsgaWQ6ICdjb21tb24uam9pbl9kYXRlJyB9KX1cclxuICAgICAgICAgICAgICAgICAgbGFiZWxDb2w9e3sgc3BhbjogMjQgfX1cclxuICAgICAgICAgICAgICAgICAgcnVsZXM9e1tcclxuICAgICAgICAgICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSxcclxuICAgICAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICdC4bqvdCBideG7mWMgxJFp4buBbicsXHJcbiAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgXX1cclxuICAgICAgICAgICAgICAgICAgbmFtZT1cImpvaW5fZGF0ZVwiXHJcbiAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgIDxEYXRlUGlja2VyIGRpc2FibGVkPXt0cnVlfSBzdHlsZT17eyB3aWR0aDogJzEwMCUnIH19IC8+XHJcbiAgICAgICAgICAgICAgICA8L0l0ZW0+XHJcbiAgICAgICAgICAgICAgPC9Db2w+XHJcbiAgICAgICAgICAgICAgPENvbCBjbGFzc05hbWU9XCJndXR0ZXItcm93XCIgbWQ9ezEyfT5cclxuICAgICAgICAgICAgICAgIDxJdGVtXHJcbiAgICAgICAgICAgICAgICAgIGxhYmVsPXtpbnRsLmZvcm1hdE1lc3NhZ2UoeyBpZDogJ2NvbW1vbi5leHBpcmVfZGF0ZScgfSl9XHJcbiAgICAgICAgICAgICAgICAgIGxhYmVsQ29sPXt7IHNwYW46IDI0IH19XHJcbiAgICAgICAgICAgICAgICAgIHJ1bGVzPXtbXHJcbiAgICAgICAgICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsXHJcbiAgICAgICAgICAgICAgICAgICAgICBtZXNzYWdlOiAnQuG6r3QgYnXhu5ljIMSRaeG7gW4nLFxyXG4gICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgIF19XHJcbiAgICAgICAgICAgICAgICAgIG5hbWU9XCJleHBpcmVkX2RheVwiXHJcbiAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgIDxEYXRlUGlja2VyIGRpc2FibGVkPXtwYXJhbXMuaXNFeHByaWVkfSBzdHlsZT17eyB3aWR0aDogJzEwMCUnIH19IC8+XHJcbiAgICAgICAgICAgICAgICA8L0l0ZW0+XHJcbiAgICAgICAgICAgICAgPC9Db2w+XHJcbiAgICAgICAgICAgICAgPENvbCBjbGFzc05hbWU9XCJndXR0ZXItcm93XCIgbWQ9ezEyfT5cclxuICAgICAgICAgICAgICAgIDxJdGVtXHJcbiAgICAgICAgICAgICAgICAgIGxhYmVsPXtpbnRsLmZvcm1hdE1lc3NhZ2UoeyBpZDogJ2NvbW1vbi51bml0JyB9KX1cclxuICAgICAgICAgICAgICAgICAgbGFiZWxDb2w9e3sgc3BhbjogMjQgfX1cclxuICAgICAgICAgICAgICAgICAgbmFtZT1cInNoYXJlaG9sZGVyXCJcclxuICAgICAgICAgICAgICAgICAgLy8gcnVsZXM9e1tcclxuICAgICAgICAgICAgICAgICAgLy8gICB7XHJcbiAgICAgICAgICAgICAgICAgIC8vICAgICByZXF1aXJlZDogdHJ1ZSxcclxuICAgICAgICAgICAgICAgICAgLy8gICAgIG1lc3NhZ2U6ICdC4bqvdCBideG7mWMgxJFp4buBbicsXHJcbiAgICAgICAgICAgICAgICAgIC8vICAgfSxcclxuICAgICAgICAgICAgICAgICAgLy8gXX1cclxuICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgPElucHV0IC8+XHJcbiAgICAgICAgICAgICAgICA8L0l0ZW0+XHJcbiAgICAgICAgICAgICAgPC9Db2w+XHJcbiAgICAgICAgICAgICAgPENvbCBjbGFzc05hbWU9XCJndXR0ZXItcm93XCIgbWQ9ezEyfT5cclxuICAgICAgICAgICAgICAgIDxJdGVtXHJcbiAgICAgICAgICAgICAgICAgIGxhYmVsPXtpbnRsLmZvcm1hdE1lc3NhZ2UoeyBpZDogJ2NvbW1vbi5yb2xlJyB9KX1cclxuICAgICAgICAgICAgICAgICAgbGFiZWxDb2w9e3sgc3BhbjogMjQgfX1cclxuICAgICAgICAgICAgICAgICAgbmFtZT1cImFnZW5jeVwiXHJcbiAgICAgICAgICAgICAgICAgIC8vIHJ1bGVzPXtbXHJcbiAgICAgICAgICAgICAgICAgIC8vICAge1xyXG4gICAgICAgICAgICAgICAgICAvLyAgICAgcmVxdWlyZWQ6IHRydWUsXHJcbiAgICAgICAgICAgICAgICAgIC8vICAgICBtZXNzYWdlOiAnQuG6r3QgYnXhu5ljIMSRaeG7gW4nLFxyXG4gICAgICAgICAgICAgICAgICAvLyAgIH0sXHJcbiAgICAgICAgICAgICAgICAgIC8vIF19XHJcbiAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgIDxJbnB1dCAvPlxyXG4gICAgICAgICAgICAgICAgPC9JdGVtPlxyXG4gICAgICAgICAgICAgIDwvQ29sPlxyXG4gICAgICAgICAgICAgIDxDb2wgY2xhc3NOYW1lPVwiZ3V0dGVyLXJvd1wiIG1kPXsxMn0+XHJcbiAgICAgICAgICAgICAgICA8SXRlbVxyXG4gICAgICAgICAgICAgICAgICBsYWJlbD17aW50bC5mb3JtYXRNZXNzYWdlKHsgaWQ6ICdjb21tb24ubG9jYXRpb24nIH0pfVxyXG4gICAgICAgICAgICAgICAgICBsYWJlbENvbD17eyBzcGFuOiAyNCB9fVxyXG4gICAgICAgICAgICAgICAgICBuYW1lPVwibG9jYXRpb25cIlxyXG4gICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICA8UHJvRm9ybVNlbGVjdFxyXG4gICAgICAgICAgICAgICAgICAgIG1vZGU9XCJtdWx0aXBsZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgc2hvd1NlYXJjaFxyXG4gICAgICAgICAgICAgICAgICAgIHJlcXVlc3Q9e2FzeW5jICgpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHZpc2l0b3JMb2NhdGlvblNlcnZpY2UuZ2V0TGlzdCgpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHJlc3VsdC5kYXRhLm1hcCgoaXRlbTogYW55KSA9PiAoe1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBsYWJlbDogaXRlbS5sYWJlbCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU6IGl0ZW0ubmFtZSxcclxuICAgICAgICAgICAgICAgICAgICAgIH0pKTtcclxuICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgPC9JdGVtPlxyXG4gICAgICAgICAgICAgIDwvQ29sPlxyXG4gICAgICAgICAgICAgIDxDb2wgY2xhc3NOYW1lPVwiZ3V0dGVyLXJvd1wiIG1kPXsyNH0+XHJcbiAgICAgICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgIGxvYWRpbmc9e2xvYWRpbmd9XHJcbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJwcmltYXJ5XCJcclxuICAgICAgICAgICAgICAgICAgaHRtbFR5cGU9XCJzdWJtaXRcIlxyXG4gICAgICAgICAgICAgICAgICBzdHlsZT17eyB3aWR0aDogJzEwMCUnIH19XHJcbiAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgIHtpbnRsLmZvcm1hdE1lc3NhZ2UoeyBpZDogJ2NvbW1vbi5zYXZlJyB9KX1cclxuICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICAgIDwvQ29sPlxyXG4gICAgICAgICAgICA8L1Jvdz5cclxuICAgICAgICAgIDwvQ29sPlxyXG4gICAgICAgIDwvUm93PlxyXG4gICAgICA8L0Zvcm0+XHJcbiAgICA8Lz5cclxuICApO1xyXG59O1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgVXBkYXRlRm9ybTtcclxuIiwiaW1wb3J0IEF0dGFjaFBpY3R1cmVPZkRvY3R5cGUgZnJvbSAnQC9jb21wb25lbnRzL0F0dGFjaG1lbnRzT2ZEb2N0eXBlL0F0dGFjaFBpY3R1cmVPZkRvY3R5cGUnO1xyXG5pbXBvcnQgeyBIT1NUIH0gZnJvbSAnQC9zZXJ2aWNlcy91dGlscyc7XHJcbmltcG9ydCB7IHZpc2l0b3JJbmZvclNlcnZpY2UgfSBmcm9tICdAL3NlcnZpY2VzL3Zpc2l0b3InO1xyXG5pbXBvcnQgeyBQYWdlQ29udGFpbmVyIH0gZnJvbSAnQGFudC1kZXNpZ24vcHJvLWNvbXBvbmVudHMnO1xyXG5pbXBvcnQgeyBoaXN0b3J5LCBMaW5rLCB1c2VBY2Nlc3MsIHVzZVNlYXJjaFBhcmFtcyB9IGZyb20gJ0B1bWlqcy9tYXgnO1xyXG5pbXBvcnQgeyBDYXJkLCBDb2wsIFJlc3VsdCwgUm93LCBTcGFjZSwgVGFicyB9IGZyb20gJ2FudGQnO1xyXG5pbXBvcnQgbW9tZW50IGZyb20gJ21vbWVudCc7XHJcbmltcG9ydCBSZWFjdCwgeyB1c2VFZmZlY3QsIHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgUmVtb3ZlUGFraW5nQ3VzdG9tZXIgZnJvbSAnLi4vQ29tcG9uZW50cy9SZW1vdmVQYWtpbmdDdXN0b21lcic7XHJcbmltcG9ydCBVcGRhdGVQYWtpbmdDdXN0b21lckZvcm0gZnJvbSAnLi4vQ29tcG9uZW50cy9VcGRhdGVQYWtpbmdDdXN0b21lckZvcm0nO1xyXG5cclxuY29uc3QgQ3VzdG9tZXJDb21wYW55OiBSZWFjdC5GQyA9ICgpID0+IHtcclxuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW3NlYXJjaFBhcmFtcywgc2V0U2VhcmNoUGFyYW1zXSA9IHVzZVNlYXJjaFBhcmFtcygpO1xyXG4gIGNvbnN0IFtjdXN0b21lciwgc2V0Q3VzdG9tZXJdID0gdXNlU3RhdGUoe30pO1xyXG4gIGNvbnN0IFtleHByaWVkLCBzZXRFeHByaWVkXSA9IHVzZVN0YXRlKHRydWUpO1xyXG4gIGNvbnN0IFtpbWcsIHNldEltZ10gPSB1c2VTdGF0ZSgnJyk7XHJcbiAgY29uc3QgW2lkLCBzZXRJZF0gPSB1c2VTdGF0ZSgnJyk7XHJcblxyXG4gIGNvbnN0IG5hbWUgPSBzZWFyY2hQYXJhbXMuZ2V0KCdjdXN0b21lcl9uYW1lJyk7XHJcblxyXG4gIGNvbnN0IHJlZnJlc2hEYXRhID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgc2V0TG9hZGluZyh0cnVlKTtcclxuICAgICAgLy8gY29uc3QgcmVzdWx0ID0gYXdhaXQgc3NjcmlwdEdlbmVyYWxMaXN0KHtcclxuICAgICAgLy8gICBkb2NfbmFtZTogJ2lvdF92aXNpdG9yX2luZm9yJyxcclxuICAgICAgLy8gICBmaWx0ZXJzOiBbWydpb3RfdmlzaXRvcl9pbmZvcicsICduYW1lJywgJ2xpa2UnLCBgJHtuYW1lfWBdXSxcclxuICAgICAgLy8gICBwYWdlOiAxLFxyXG4gICAgICAvLyAgIHNpemU6IDEsXHJcbiAgICAgIC8vICAgZmllbGRzOiBbJyonXSxcclxuICAgICAgLy8gfSk7XHJcbiAgICAgIGNvbnN0IGZpbHRlciA9IEpTT04uc3RyaW5naWZ5KFtbJ2lvdF92aXNpdG9yX2luZm9yJywgJ25hbWUnLCAnbGlrZScsIGAke25hbWV9YF1dKTtcclxuICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgdmlzaXRvckluZm9yU2VydmljZS5nZXRMaXN0KHsgZmlsdGVyczogZmlsdGVyIH0pO1xyXG4gICAgICBjb25zb2xlLmxvZygnbW90aGVyIGVyJywgcmVzdWx0KTtcclxuICAgICAgaWYgKHJlc3VsdC5kYXRhLmxlbmd0aCA+IDApIHtcclxuICAgICAgICBjb25zdCBjdXN0b21lcl9pbmZvciA9IHJlc3VsdC5kYXRhWzBdO1xyXG4gICAgICAgIGN1c3RvbWVyX2luZm9yLmJpcnRoZGF5ID0gbW9tZW50KGN1c3RvbWVyX2luZm9yLmJpcnRoZGF5KTtcclxuICAgICAgICBjdXN0b21lcl9pbmZvci5qb2luX2RhdGUgPSBtb21lbnQoY3VzdG9tZXJfaW5mb3Iuam9pbl9kYXRlKTtcclxuICAgICAgICBjdXN0b21lcl9pbmZvci5leHBpcmVkX2RheSA9IG1vbWVudChjdXN0b21lcl9pbmZvci5leHBpcmVkX2RheSk7XHJcbiAgICAgICAgc2V0Q3VzdG9tZXIoY3VzdG9tZXJfaW5mb3IpO1xyXG4gICAgICAgIGNvbnN0IHVzZXJfdG9rZW4gPSBKU09OLnBhcnNlKGxvY2FsU3RvcmFnZS5nZXRJdGVtKCd0b2tlbicpKS50b2tlbjtcclxuICAgICAgICBjb25zdCBmdWxsX3VybCA9XHJcbiAgICAgICAgICBgJHtIT1NUfS9hcGkvZmlsZS9kb3dubG9hZD90b2tlbj1gICtcclxuICAgICAgICAgIHVzZXJfdG9rZW4gK1xyXG4gICAgICAgICAgYCZmaWxlX3VybD1gICtcclxuICAgICAgICAgIGN1c3RvbWVyX2luZm9yLmF2YXRhICtcclxuICAgICAgICAgICcmZHQ9JyArXHJcbiAgICAgICAgICAncGFya2luZ19ob2l2aWVuJyArXHJcbiAgICAgICAgICAnJmRuPScgK1xyXG4gICAgICAgICAgY3VzdG9tZXJfaW5mb3IubmFtZTtcclxuICAgICAgICBzZXRJbWcoZnVsbF91cmwpO1xyXG4gICAgICAgIHNldElkKGN1c3RvbWVyX2luZm9yLm5hbWUpO1xyXG4gICAgICB9XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmxvZyhlcnJvcik7XHJcbiAgICB9IGZpbmFsbHkge1xyXG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgcmVmcmVzaERhdGEoKTtcclxuICB9LCBbXSk7XHJcbiAgY29uc3QgaGFuZGxlQWN0aXZlID0gKCkgPT4ge1xyXG4gICAgc2V0RXhwcmllZCgocHJlX3ZhbCkgPT4gIXByZV92YWwpO1xyXG4gIH07XHJcbiAgY29uc3QgYWNjZXNzID0gdXNlQWNjZXNzKCk7XHJcbiAgY29uc3QgY2FuRGVsZXRlID0gYWNjZXNzLmNhbkRlbGV0ZUluVmlzaXRvck1hbmFnZW1lbnQoKTtcclxuICBpZiAobmFtZSkge1xyXG4gICAgcmV0dXJuIChcclxuICAgICAgPFBhZ2VDb250YWluZXI+XHJcbiAgICAgICAgPENhcmRcclxuICAgICAgICAgIHRpdGxlPXtuYW1lfVxyXG4gICAgICAgICAgbG9hZGluZz17bG9hZGluZ31cclxuICAgICAgICAgIGV4dHJhPXtcclxuICAgICAgICAgICAgPFNwYWNlPlxyXG4gICAgICAgICAgICAgIHsvKiA8QnV0dG9uIG9uQ2xpY2s9e2hhbmRsZUFjdGl2ZX0gZGFuZ2VyPXshZXhwcmllZH0+XHJcbiAgICAgICAgICAgICAgICB7ZXhwcmllZCA/ICdHaWEgaOG6oW4nIDogJ0jhu6d5IGdpYSBo4bqhbid9XHJcbiAgICAgICAgICAgICAgPC9CdXR0b24+ICovfVxyXG4gICAgICAgICAgICAgIHtjYW5EZWxldGUgJiYgKFxyXG4gICAgICAgICAgICAgICAgPFJlbW92ZVBha2luZ0N1c3RvbWVyXHJcbiAgICAgICAgICAgICAgICAgIG5hbWU9e25hbWV9XHJcbiAgICAgICAgICAgICAgICAgIHJlZnJlc2hGbmM9eygpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICBoaXN0b3J5LnB1c2goJy9lbXBsb3llZS1tYW5hZ2VtZW50L3Zpc2l0b3ItbWFuYWdlbWVudC9tZW1iZXIvYWxsJyk7XHJcbiAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgIDwvU3BhY2U+XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgPlxyXG4gICAgICAgICAge09iamVjdC5rZXlzKGN1c3RvbWVyKS5sZW5ndGggPyAoXHJcbiAgICAgICAgICAgIDw+XHJcbiAgICAgICAgICAgICAgPFRhYnMgZGVmYXVsdEFjdGl2ZUtleT1cIjFcIj5cclxuICAgICAgICAgICAgICAgIDxUYWJzLlRhYlBhbmUgdGFiPVwiVGjDtG5nIHRpbiBjaGkgdGnhur90XCIga2V5PVwiMVwiPlxyXG4gICAgICAgICAgICAgICAgICA8VXBkYXRlUGFraW5nQ3VzdG9tZXJGb3JtXHJcbiAgICAgICAgICAgICAgICAgICAgcmVmcmVzaEZuYz17cmVmcmVzaERhdGF9XHJcbiAgICAgICAgICAgICAgICAgICAgY3VzdG9tZXI9e2N1c3RvbWVyfVxyXG4gICAgICAgICAgICAgICAgICAgIGlzRXhwcmllZD17ZXhwcmllZH1cclxuICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgIDwvVGFicy5UYWJQYW5lPlxyXG4gICAgICAgICAgICAgICAgPFRhYnMuVGFiUGFuZSB0YWI9XCLhuqJuaCBQYXNzcG9ydFwiIGtleT1cIjVcIj5cclxuICAgICAgICAgICAgICAgICAgPFJvdz5cclxuICAgICAgICAgICAgICAgICAgICA8Q29sIG1kPXsxMn0+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8QXR0YWNoUGljdHVyZU9mRG9jdHlwZSBkb2NuYW1lPXtuYW1lfSBkb2N0eXBlPVwiaW90X3Zpc2l0b3JfaW5mb3JcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvQ29sPlxyXG4gICAgICAgICAgICAgICAgICA8L1Jvdz5cclxuICAgICAgICAgICAgICAgIDwvVGFicy5UYWJQYW5lPlxyXG4gICAgICAgICAgICAgICAgey8qIDxUYWJzLlRhYlBhbmUgdGFiPVwiSW4gdGjhurtcIiBrZXk9XCI5XCI+XHJcbiAgICAgICAgICAgICAgICAgIDxDcmVhdGVBVE1DYXJkIHVybD17aW1nfSBjYXJkSUQ9e2lkfT48L0NyZWF0ZUFUTUNhcmQ+XHJcbiAgICAgICAgICAgICAgICA8L1RhYnMuVGFiUGFuZT4gKi99XHJcbiAgICAgICAgICAgICAgPC9UYWJzPlxyXG4gICAgICAgICAgICA8Lz5cclxuICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgIDw+VW5rb3duIEVycm9yLCBSZWxvYWQgYW5kIHRyeSBBZ2FpbjwvPlxyXG4gICAgICAgICAgKX1cclxuICAgICAgICA8L0NhcmQ+XHJcbiAgICAgIDwvUGFnZUNvbnRhaW5lcj5cclxuICAgICk7XHJcbiAgfSBlbHNlIHtcclxuICAgIHJldHVybiAoXHJcbiAgICAgIDw+XHJcbiAgICAgICAgPFJlc3VsdFxyXG4gICAgICAgICAgc3RhdHVzPVwiNDA0XCJcclxuICAgICAgICAgIHRpdGxlPVwiNDA0XCJcclxuICAgICAgICAgIHN1YlRpdGxlPVwiU29ycnksIHRoZSBwYWdlIHlvdSB2aXNpdGVkIGRvZXMgbm90IGV4aXN0LlwiXHJcbiAgICAgICAgICBleHRyYT17XHJcbiAgICAgICAgICAgIDxMaW5rIHRvPXsnL2N1c3RvbWVyL2NvbXBhbnknfSB0eXBlPVwicHJpbWFyeVwiPlxyXG4gICAgICAgICAgICAgIEdvIEJhY2tcclxuICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgfVxyXG4gICAgICAgIC8+XHJcbiAgICAgIDwvPlxyXG4gICAgKTtcclxuICB9XHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBDdXN0b21lckNvbXBhbnk7XHJcbiJdLCJuYW1lcyI6WyJQbHVzT3V0bGluZWQiLCJDYXJkIiwibWVzc2FnZSIsIk1vZGFsIiwiVXBsb2FkIiwidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJkb3dubG9hZEZpbGUiLCJnZXRMaXN0RmlsZUJ5RG9jbmFtZSIsInJlbW92ZUZpbGUiLCJ1cGxvYWRGaWxlIiwiSE9TVCIsInVzZUFjY2VzcyIsImpzeCIsIl9qc3giLCJqc3hzIiwiX2pzeHMiLCJGcmFnbWVudCIsIl9GcmFnbWVudCIsImdldEJhc2U2NCIsImZpbGUiLCJQcm9taXNlIiwicmVzb2x2ZSIsInJlamVjdCIsInJlYWRlciIsIkZpbGVSZWFkZXIiLCJyZWFkQXNEYXRhVVJMIiwib25sb2FkIiwicmVzdWx0Iiwib25lcnJvciIsImVycm9yIiwiQXR0YWNoUGljdHVyZU9mRG9jdHlwZSIsIl9yZWYiLCJkb2N0eXBlIiwiZG9jbmFtZSIsIl9yZWYkaXNfcHJpdmF0ZSIsImlzX3ByaXZhdGUiLCJfcmVmJGZvbGRlciIsImZvbGRlciIsIl9yZWYkb3B0aW1pemUiLCJvcHRpbWl6ZSIsIl91c2VTdGF0ZSIsIl91c2VTdGF0ZTIiLCJfc2xpY2VkVG9BcnJheSIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwiX3VzZVN0YXRlMyIsIl91c2VTdGF0ZTQiLCJ1cGxvYWRpbmciLCJzZXRVcGxvYWRpbmciLCJfdXNlU3RhdGU1IiwiX3VzZVN0YXRlNiIsImZpbGVMaXN0Iiwic2V0RmlsZUxpc3QiLCJfdXNlU3RhdGU3IiwiX3VzZVN0YXRlOCIsInByZXZpZXdPcGVuIiwic2V0UHJldmlld09wZW4iLCJfdXNlU3RhdGU5IiwiX3VzZVN0YXRlMTAiLCJwcmV2aWV3SW1hZ2UiLCJzZXRQcmV2aWV3SW1hZ2UiLCJfdXNlU3RhdGUxMSIsIl91c2VTdGF0ZTEyIiwicHJldmlld1RpdGxlIiwic2V0UHJldmlld1RpdGxlIiwiX3VzZVN0YXRlMTMiLCJfdXNlU3RhdGUxNCIsInVwZGF0aW5nIiwic2V0VXBkYXRpbmciLCJ1c2VyX3Rva2VuIiwiSlNPTiIsInBhcnNlIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsInRva2VuIiwiZ2V0RmlsZUxpc3QiLCJfcmVmMiIsIl9hc3luY1RvR2VuZXJhdG9yIiwiX3JlZ2VuZXJhdG9yUnVudGltZSIsIm1hcmsiLCJfY2FsbGVlIiwicHJpdmF0ZUltZyIsIl9maWxlTGlzdCIsIndyYXAiLCJfY2FsbGVlJCIsIl9jb250ZXh0IiwicHJldiIsIm5leHQiLCJuYW1lIiwic2VudCIsIm1hcCIsImYiLCJmaWxlX3VybCIsInNlYXJjaCIsInB1c2giLCJ1aWQiLCJmaWxlX25hbWUiLCJ0eXBlIiwic3RhdHVzIiwidXJsIiwiY29uY2F0IiwidDAiLCJmaW5pc2giLCJzdG9wIiwiYXBwbHkiLCJhcmd1bWVudHMiLCJoYW5kbGVDaGFuZ2UiLCJpbmZvIiwibmV3RmlsZUxpc3QiLCJfdG9Db25zdW1hYmxlQXJyYXkiLCJyZXNwb25zZSIsImNvbnNvbGUiLCJsb2ciLCJoYW5kbGVVcGxvYWQiLCJfcmVmMyIsIl9jYWxsZWUyIiwib3B0aW9ucyIsIm9uU3VjY2VzcyIsIm9uRXJyb3IiLCJvblByb2dyZXNzIiwicmVzIiwiX2NhbGxlZTIkIiwiX2NvbnRleHQyIiwiRXJyb3IiLCJlcnIiLCJfeCIsImhhbmRsZUNhbmNlbCIsImhhbmRsZVByZXZpZXciLCJfcmVmNCIsIl9jYWxsZWUzIiwiX2NhbGxlZTMkIiwiX2NvbnRleHQzIiwicHJldmlldyIsIm9yaWdpbkZpbGVPYmoiLCJzdWJzdHJpbmciLCJsYXN0SW5kZXhPZiIsIl94MiIsInByb3BzIiwib25SZW1vdmUiLCJfb25SZW1vdmUiLCJfY2FsbGVlNCIsIl9jYWxsZWU0JCIsIl9jb250ZXh0NCIsImZpZCIsImR0IiwiZG4iLCJmaWx0ZXIiLCJfeDMiLCJtdWx0aXBsZSIsImN1c3RvbVJlcXVlc3QiLCJvbkNoYW5nZSIsIm9uUHJldmlldyIsImxpc3RUeXBlIiwib25Eb3dubG9hZCIsIl9vbkRvd25sb2FkIiwiX2NhbGxlZTUiLCJfY2FsbGVlNSQiLCJfY29udGV4dDUiLCJfeDQiLCJ1cGxvYWRCdXR0b24iLCJjaGlsZHJlbiIsInN0eWxlIiwibWFyZ2luVG9wIiwiYWNjZXNzIiwiY2FuVXBkYXRlIiwiY2FuVXBkYXRlSW5WaXNpdG9yTWFuYWdlbWVudCIsInRpdGxlIiwiX29iamVjdFNwcmVhZCIsImxlbmd0aCIsIm9wZW4iLCJmb290ZXIiLCJvbkNhbmNlbCIsImFsdCIsIndpZHRoIiwic3JjIiwiQWN0aW9uUG9wQ29uZmlybSIsInZpc2l0b3JJbmZvclNlcnZpY2UiLCJSZW1vdmVGb3JtIiwicGFyYW1zIiwicmVtb3ZlRW5pdHkiLCJ1cGRhdGVTdGF0dXNEZWxldGVkIiwidG9TdHJpbmciLCJhY3Rpb25DYWxsIiwicmVmcmVzaERhdGEiLCJyZWZyZXNoRm5jIiwidGV4dCIsImJ1dHRvblR5cGUiLCJkYW5nZXIiLCJCdXR0b24iLCJDb2wiLCJEYXRlUGlja2VyIiwiRm9ybSIsIklucHV0IiwiUm93IiwiU2VsZWN0IiwiU3BhY2UiLCJUeXBvZ3JhcGh5IiwiSXRlbSIsImdlbmVyYXRlQVBJUGF0aCIsInZpc2l0b3JDYXJkU2VydmljZSIsInZpc2l0b3JMb2NhdGlvblNlcnZpY2UiLCJEZWxldGVGaWxsZWQiLCJMb2FkaW5nT3V0bGluZWQiLCJQcm9Gb3JtU2VsZWN0IiwidXNlSW50bCIsIm1vbWVudCIsIk9wdGlvbiIsIlRpdGxlIiwiVGV4dCIsIlVwZGF0ZUZvcm0iLCJfRm9ybSR1c2VGb3JtIiwidXNlRm9ybSIsIl9Gb3JtJHVzZUZvcm0yIiwiZm9ybSIsImN1c3RvbWVyIiwiaWRDYXJkQ29kZSIsInNldElkQ2FyZENvZGUiLCJzZWFyY2hpbmciLCJzZXRTZWFyY2hpbmciLCJzZXRPcHRpb25zIiwibGlzdENhcmQiLCJzZXRMaXN0Q2FyZCIsImltYWdlVXJsIiwic2V0SW1hZ2VVcmwiLCJfdXNlU3RhdGUxNSIsIl91c2VTdGF0ZTE2IiwiT2JqZWN0Iiwia2V5cyIsInNldEZpZWxkc1ZhbHVlIiwiaW1hZ2UiLCJsb2NhdGlvbnMiLCJsb2NhdGlvbiIsInNwbGl0Iiwic2V0RmllbGRWYWx1ZSIsImhhbmRsZVJlbW92ZSIsImhhbmRsZUNoYW5nZVVwbG9hZCIsInVzZXJkYXRhIiwicmF3X3VybCIsIl9uZXdGaWxlTGlzdCQiLCJoYW5kbGVPayIsInN1Ym1pdCIsImludGwiLCJsYXlvdXQiLCJsYWJlbENvbCIsInNwYW4iLCJsYWJlbEFsaWduIiwib25GaW5pc2giLCJ2YWx1ZSIsImFicnVwdCIsImpvaW5fZGF0ZSIsImZvcm1hdCIsImV4cGlyZWRfZGF5IiwiYmlydGhkYXkiLCJ1c2VyX3N0YXR1cyIsImpvaW4iLCJ1cGRhdGUiLCJzdWNjZXNzIiwiZ3V0dGVyIiwiY2xhc3NOYW1lIiwibWQiLCJkaXNwbGF5IiwianVzdGlmeUNvbnRlbnQiLCJkaXJlY3Rpb24iLCJhbGlnbiIsInNpemUiLCJzaG93VXBsb2FkTGlzdCIsImFjY2VwdCIsIm9uQ2xpY2siLCJsZXZlbCIsImZ1bGxuYW1lIiwic3Ryb25nIiwiY2FyZF9pZCIsImxhYmVsIiwiZm9ybWF0TWVzc2FnZSIsImlkIiwicnVsZXMiLCJyZXF1aXJlZCIsInNob3dTZWFyY2giLCJwbGFjZWhvbGRlciIsInJlcXVlc3QiLCJnZXRMaXN0IiwiZGF0YSIsIml0ZW0iLCJjYXJkX251bWJlciIsImRpc2FibGVkIiwiZGVmYXVsdFZhbHVlIiwiaXNFeHByaWVkIiwibW9kZSIsImh0bWxUeXBlIiwiUGFnZUNvbnRhaW5lciIsImhpc3RvcnkiLCJMaW5rIiwidXNlU2VhcmNoUGFyYW1zIiwiUmVzdWx0IiwiVGFicyIsIlJlYWN0IiwiUmVtb3ZlUGFraW5nQ3VzdG9tZXIiLCJVcGRhdGVQYWtpbmdDdXN0b21lckZvcm0iLCJDdXN0b21lckNvbXBhbnkiLCJfdXNlU2VhcmNoUGFyYW1zIiwiX3VzZVNlYXJjaFBhcmFtczIiLCJzZWFyY2hQYXJhbXMiLCJzZXRTZWFyY2hQYXJhbXMiLCJzZXRDdXN0b21lciIsImV4cHJpZWQiLCJzZXRFeHByaWVkIiwiaW1nIiwic2V0SW1nIiwic2V0SWQiLCJnZXQiLCJjdXN0b21lcl9pbmZvciIsImZ1bGxfdXJsIiwic3RyaW5naWZ5IiwiZmlsdGVycyIsImF2YXRhIiwiaGFuZGxlQWN0aXZlIiwicHJlX3ZhbCIsImNhbkRlbGV0ZSIsImNhbkRlbGV0ZUluVmlzaXRvck1hbmFnZW1lbnQiLCJleHRyYSIsImRlZmF1bHRBY3RpdmVLZXkiLCJUYWJQYW5lIiwidGFiIiwic3ViVGl0bGUiLCJ0byJdLCJzb3VyY2VSb290IjoiIn0=
//# sourceURL=webpack-internal:///6962
`)},64639:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Sv: function() { return /* binding */ downloadFile; },
/* harmony export */   Yd: function() { return /* binding */ removeFile; },
/* harmony export */   cT: function() { return /* binding */ uploadFile; },
/* harmony export */   g8: function() { return /* binding */ getListFileByDocname; }
/* harmony export */ });
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(7837);
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(467);




function uploadFile(_x) {
  return _uploadFile.apply(this, arguments);
}
function _uploadFile() {
  _uploadFile = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee(_ref) {
    var file, _ref$is_private, is_private, _ref$folder, folder, doctype, docname, _ref$optimize, optimize, formData, result;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          file = _ref.file, _ref$is_private = _ref.is_private, is_private = _ref$is_private === void 0 ? 1 : _ref$is_private, _ref$folder = _ref.folder, folder = _ref$folder === void 0 ? 'Home/Attachments' : _ref$folder, doctype = _ref.doctype, docname = _ref.docname, _ref$optimize = _ref.optimize, optimize = _ref$optimize === void 0 ? false : _ref$optimize;
          formData = new FormData();
          formData.append('is_private', is_private.toString());
          formData.append('folder', folder);
          formData.append('doctype', doctype);
          formData.append('docname', docname);
          formData.append('file', file);
          formData.append('optimize', optimize.toString());
          _context.prev = 8;
          _context.next = 11;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)("api/v2/file/upload"), {
            withCredentials: true,
            method: 'POST',
            data: formData
          });
        case 11:
          result = _context.sent;
          return _context.abrupt("return", result.result);
        case 15:
          _context.prev = 15;
          _context.t0 = _context["catch"](8);
          console.log(_context.t0);
          throw _context.t0;
        case 19:
        case "end":
          return _context.stop();
      }
    }, _callee, null, [[8, 15]]);
  }));
  return _uploadFile.apply(this, arguments);
}
function removeFile(_x2) {
  return _removeFile.apply(this, arguments);
}
function _removeFile() {
  _removeFile = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee2(_ref2) {
    var fid, dt, dn, result;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          fid = _ref2.fid, dt = _ref2.dt, dn = _ref2.dn;
          _context2.prev = 1;
          _context2.next = 4;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)("api/v2/file"), {
            withCredentials: true,
            method: 'DELETE',
            data: {
              fid: fid,
              dt: dt,
              dn: dn
            }
          });
        case 4:
          result = _context2.sent;
          return _context2.abrupt("return", result.result);
        case 8:
          _context2.prev = 8;
          _context2.t0 = _context2["catch"](1);
          console.log(_context2.t0);
          throw _context2.t0;
        case 12:
        case "end":
          return _context2.stop();
      }
    }, _callee2, null, [[1, 8]]);
  }));
  return _removeFile.apply(this, arguments);
}
function downloadFile(_x3) {
  return _downloadFile.apply(this, arguments);
}
function _downloadFile() {
  _downloadFile = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee3(_ref3) {
    var file_url, fid, dt, dn, result;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee3$(_context3) {
      while (1) switch (_context3.prev = _context3.next) {
        case 0:
          file_url = _ref3.file_url, fid = _ref3.fid, dt = _ref3.dt, dn = _ref3.dn;
          _context3.prev = 1;
          _context3.next = 4;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)("api/v2/file/download"), {
            withCredentials: true,
            method: 'GET',
            params: {
              file_url: file_url,
              fid: fid,
              dt: dt,
              dn: dn
            }
          });
        case 4:
          result = _context3.sent;
          return _context3.abrupt("return", result.result);
        case 8:
          _context3.prev = 8;
          _context3.t0 = _context3["catch"](1);
          console.log(_context3.t0);
          throw _context3.t0;
        case 12:
        case "end":
          return _context3.stop();
      }
    }, _callee3, null, [[1, 8]]);
  }));
  return _downloadFile.apply(this, arguments);
}
function getListFileByDocname(_x4) {
  return _getListFileByDocname.apply(this, arguments);
}
function _getListFileByDocname() {
  _getListFileByDocname = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee4(_ref4) {
    var doctype, name, result;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee4$(_context4) {
      while (1) switch (_context4.prev = _context4.next) {
        case 0:
          doctype = _ref4.doctype, name = _ref4.name;
          _context4.prev = 1;
          _context4.next = 4;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)("api/v2/file/list"), {
            withCredentials: true,
            method: 'GET',
            params: {
              doctype: doctype,
              name: name
            }
          });
        case 4:
          result = _context4.sent;
          return _context4.abrupt("return", result.result);
        case 8:
          _context4.prev = 8;
          _context4.t0 = _context4["catch"](1);
          console.log(_context4.t0);
          throw _context4.t0;
        case 12:
        case "end":
          return _context4.stop();
      }
    }, _callee4, null, [[1, 8]]);
  }));
  return _getListFileByDocname.apply(this, arguments);
}//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///64639
`)}}]);
