"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[6658,4679],{97679:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(97857);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(19632);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(5574);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var _common_contanst_constanst__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(86604);
/* harmony import */ var _services_fileUpload__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(96876);
/* harmony import */ var _services_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(467);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(51042);
/* harmony import */ var _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(27068);
/* harmony import */ var _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(34994);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(98138);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(45360);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(78367);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(85576);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(67294);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(85893);















var getBase64 = function getBase64(file) {
  return new Promise(function (resolve, reject) {
    var reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = function () {
      return resolve(reader.result);
    };
    reader.onerror = function (error) {
      return reject(error);
    };
  });
};
var FormUploadsPreviewable = function FormUploadsPreviewable(_ref) {
  var formItemName = _ref.formItemName,
    fileLimit = _ref.fileLimit,
    label = _ref.label,
    initialImages = _ref.initialImages,
    docType = _ref.docType,
    isRequired = _ref.isRequired,
    hideUploadButton = _ref.hideUploadButton,
    hideDeleteIcon = _ref.hideDeleteIcon,
    hidePreviewIcon = _ref.hidePreviewIcon,
    _ref$uploadButtonLayo = _ref.uploadButtonLayout,
    uploadButtonLayout = _ref$uploadButtonLayo === void 0 ? 'vertical' : _ref$uploadButtonLayo,
    _ref$uploadButtonIcon = _ref.uploadButtonIcon,
    uploadButtonIcon = _ref$uploadButtonIcon === void 0 ? /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .Z, {}) : _ref$uploadButtonIcon,
    _ref$uploadButtonText = _ref.uploadButtonText,
    uploadButtonText = _ref$uploadButtonText === void 0 ? 'T\u1EA3i l\xEAn' : _ref$uploadButtonText,
    uploadButtonStyle = _ref.uploadButtonStyle,
    readOnly = _ref.readOnly;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(false),
    _useState2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_4___default()(_useState, 2),
    previewOpen = _useState2[0],
    setPreviewOpen = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(''),
    _useState4 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_4___default()(_useState3, 2),
    previewImage = _useState4[0],
    setPreviewImage = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(''),
    _useState6 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_4___default()(_useState5, 2),
    previewTitle = _useState6[0],
    setPreviewTitle = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(initialImages),
    _useState8 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_4___default()(_useState7, 2),
    imageList = _useState8[0],
    setImageList = _useState8[1];
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)([]),
    _useState10 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_4___default()(_useState9, 2),
    fileList = _useState10[0],
    setFileList = _useState10[1];
  var form = antd__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .Z.useFormInstance();
  (0,_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_12__/* .useDeepCompareEffect */ .KW)(function () {
    var listImg = (0,_services_utils__WEBPACK_IMPORTED_MODULE_7__/* .getListFileUrlFromStringV2 */ .JJ)({
      arrUrlString: initialImages
    }).map(function (url, index) {
      return {
        name: "\\u1EA2nh ".concat((index + 1).toString()),
        url: url || '',
        uid: (-index).toString(),
        status: url ? 'done' : 'error'
      };
    });
    setFileList(listImg);
  }, [initialImages]);
  var handlePreview = /*#__PURE__*/function () {
    var _ref2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee(file) {
      return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            if (!(!file.url && !file.preview)) {
              _context.next = 4;
              break;
            }
            _context.next = 3;
            return getBase64(file.originFileObj);
          case 3:
            file.preview = _context.sent;
          case 4:
            setPreviewImage(file.url || file.preview);
            setPreviewOpen(true);
            setPreviewTitle(file.name || file.url.substring(file.url.lastIndexOf('/') + 1));
          case 7:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }));
    return function handlePreview(_x) {
      return _ref2.apply(this, arguments);
    };
  }();
  var handleChange = /*#__PURE__*/function () {
    var _ref4 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee3(_ref3) {
      var newFileList, oldFileList, uploadListRes, checkUploadFailed, arrFileUrl;
      return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee3$(_context3) {
        while (1) switch (_context3.prev = _context3.next) {
          case 0:
            newFileList = _ref3.fileList;
            oldFileList = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_1___default()(fileList);
            if (!readOnly) {
              _context3.next = 4;
              break;
            }
            return _context3.abrupt("return");
          case 4:
            setFileList(newFileList);
            _context3.next = 7;
            return Promise.allSettled(newFileList.map( /*#__PURE__*/function () {
              var _ref5 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee2(item) {
                var _item$lastModified;
                return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee2$(_context2) {
                  while (1) switch (_context2.prev = _context2.next) {
                    case 0:
                      if (!item.url) {
                        _context2.next = 2;
                        break;
                      }
                      return _context2.abrupt("return", {
                        data: {
                          message: {
                            file_url: item.url.split('file_url=').at(-1)
                          }
                        }
                      });
                    case 2:
                      _context2.next = 4;
                      return (0,_services_fileUpload__WEBPACK_IMPORTED_MODULE_6__/* .uploadFile */ .cT)({
                        docType: docType || _common_contanst_constanst__WEBPACK_IMPORTED_MODULE_5__/* .DOCTYPE_ERP */ .lH.iotPlant,
                        docName: item.name + Math.random().toString(4) + ((_item$lastModified = item.lastModified) === null || _item$lastModified === void 0 ? void 0 : _item$lastModified.toString(4)),
                        file: item.originFileObj
                      });
                    case 4:
                      return _context2.abrupt("return", _context2.sent);
                    case 5:
                    case "end":
                      return _context2.stop();
                  }
                }, _callee2);
              }));
              return function (_x3) {
                return _ref5.apply(this, arguments);
              };
            }()));
          case 7:
            uploadListRes = _context3.sent;
            checkUploadFailed = uploadListRes.find(function (item) {
              return item.status === 'rejected';
            });
            if (checkUploadFailed) {
              antd__WEBPACK_IMPORTED_MODULE_13__/* ["default"] */ .ZP.error({
                content: "upload \\u1EA3nh kh\\xF4ng th\\xE0nh c\\xF4ng"
              });
              setFileList(oldFileList);
            }
            arrFileUrl = uploadListRes.reduce(function (prev, item) {
              var _item$value, _item$value2;
              return item.status === 'fulfilled' && item !== null && item !== void 0 && (_item$value = item.value) !== null && _item$value !== void 0 && (_item$value = _item$value.data) !== null && _item$value !== void 0 && (_item$value = _item$value.message) !== null && _item$value !== void 0 && _item$value.file_url ? [].concat(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_1___default()(prev), [item === null || item === void 0 || (_item$value2 = item.value) === null || _item$value2 === void 0 || (_item$value2 = _item$value2.data) === null || _item$value2 === void 0 || (_item$value2 = _item$value2.message) === null || _item$value2 === void 0 ? void 0 : _item$value2.file_url]) : prev;
            }, []).filter(function (item) {
              return typeof item === 'string';
            });
            if (arrFileUrl) {
              setImageList(arrFileUrl.join(','));
              form.setFieldValue(formItemName, arrFileUrl.join(','));
            }
          case 12:
          case "end":
            return _context3.stop();
        }
      }, _callee3);
    }));
    return function handleChange(_x2) {
      return _ref4.apply(this, arguments);
    };
  }();
  var uploadButton = /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxs)("div", {
    style: D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default()({
      display: 'flex',
      flexDirection: uploadButtonLayout === 'vertical' ? 'column' : 'row',
      alignItems: 'center',
      gap: uploadButtonLayout === 'vertical' ? '8px' : '4px'
    }, uploadButtonStyle),
    children: [uploadButtonIcon, /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)("div", {
      children: uploadButtonText
    })]
  });
  var handleCancel = function handleCancel() {
    return setPreviewOpen(false);
  };
  var normFile = function normFile(e) {
    if (Array.isArray(e)) {
      return e;
    }
    return e && e.fileList;
  };
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.Fragment, {
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_14__/* .ProForm */ .A.Item, {
      name: formItemName,
      initialValue: imageList,
      label: label,
      rules: D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_1___default()(isRequired ? [{
        required: isRequired
      }] : []),
      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(antd__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .Z, {
        listType: "picture-card",
        fileList: fileList,
        onPreview: handlePreview,
        maxCount: fileLimit,
        onChange: handleChange,
        multiple: true,
        showUploadList: {
          showRemoveIcon: !hideDeleteIcon,
          showPreviewIcon: !hidePreviewIcon
        },
        children: !hideUploadButton && (fileList.length >= fileLimit ? null : uploadButton)
      })
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(antd__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .Z, {
      open: previewOpen,
      title: previewTitle,
      footer: null,
      onCancel: handleCancel,
      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)("img", {
        alt: "example",
        style: {
          width: '100%'
        },
        src: previewImage
      })
    })]
  });
};
/* harmony default export */ __webpack_exports__.Z = (FormUploadsPreviewable);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///97679
`)},30653:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(97857);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(5574);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectWithoutProperties_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(13769);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectWithoutProperties_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectWithoutProperties_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _utils_string__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(7369);
/* harmony import */ var _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(33983);
/* harmony import */ var _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(19054);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(96074);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(67294);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(85893);



var _excluded = ["dropdownBottom"];






var toLowerCase = function toLowerCase() {
  var input = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';
  return (0,_utils_string__WEBPACK_IMPORTED_MODULE_5__/* .nonAccentVietnamese */ .w)(input.toLowerCase().normalize('NFD').replace(/[\\u0300-\\u036f]/g, ''));
};
var FormTreeSelectSearch = function FormTreeSelectSearch(_ref) {
  var _props$fieldProps;
  var dropdownBottom = _ref.dropdownBottom,
    props = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectWithoutProperties_js__WEBPACK_IMPORTED_MODULE_2___default()(_ref, _excluded);
  // eslint-disable-next-line @typescript-eslint/no-non-null-asserted-optional-chain
  var treeData = (_props$fieldProps = props.fieldProps) === null || _props$fieldProps === void 0 ? void 0 : _props$fieldProps.treeData;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(''),
    _useState2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_1___default()(_useState, 2),
    searchValue = _useState2[0],
    setSearchValue = _useState2[1];
  var searchValueDebounce = (0,_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_6__/* .useDebounceValue */ .n)(searchValue || '', 100);
  var _treeData = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(function () {
    var loop = function loop() {
      var data = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];
      return (data || []).map(function (item) {
        var normalizedSearchValue = toLowerCase(searchValueDebounce);
        var itemTitle = (item.title || '').toString();
        var strTitle = (0,_utils_string__WEBPACK_IMPORTED_MODULE_5__/* .nonAccentVietnamese */ .w)(itemTitle);
        var index = strTitle.indexOf(normalizedSearchValue);
        var beforeStr = itemTitle.substring(0, index);
        var str = itemTitle.substring(index, index + searchValueDebounce.length);
        var afterStr = itemTitle.slice(index + searchValueDebounce.length);
        var title = searchValueDebounce === '' ? /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)("span", {
          children: itemTitle
        }) : index > -1 ? /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)("span", {
          children: [beforeStr, /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)("span", {
            style: {
              color: 'white',
              backgroundColor: 'green'
            },
            children: str
          }), afterStr]
        }) : /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)("span", {
          children: itemTitle
        });
        if (item.children) {
          return {
            title: title,
            key: item.value,
            children: loop(item.children),
            value: item.value,
            _title: itemTitle
          };
        }
        return {
          title: title,
          key: item.value,
          value: item.value,
          _title: itemTitle
        };
      });
    };
    return loop(treeData);
  }, [treeData, searchValueDebounce]);
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Z, D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default()({}, props), {}, {
    fieldProps: D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default()({}, props.fieldProps || {}), {}, {
      treeData: _treeData,
      onSearch: function onSearch(value) {
        setSearchValue(value);
      },
      filterTreeNode: function filterTreeNode(input, treeNode) {
        var treeNodeChildrenArr = treeNode.children || [];
        var normalizedInput = toLowerCase(input);
        var normalizedLabel = toLowerCase(treeNode._title);
        var childrenMatch = false;
        for (var i = 0; i < treeNodeChildrenArr.length; i++) {
          var _normalizedLabel = toLowerCase(treeNodeChildrenArr[i]._title);
          if (_normalizedLabel.includes(normalizedInput)) {
            childrenMatch = true;
            return true;
          }
        }
        if (normalizedLabel.includes(normalizedInput)) {
          return true;
        }
        return childrenMatch;
      },
      dropdownRender: !dropdownBottom ? undefined : function (menu) {
        return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)("div", {
          children: [menu, /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(antd__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .Z, {
            style: {
              margin: '4px 0'
            }
          }), dropdownBottom]
        });
      },
      showSearch: true,
      multiple: true,
      autoClearSearchValue: true,
      treeCheckable: true,
      treeDefaultExpandAll: true,
      showCheckedStrategy: 'SHOW_CHILD'
    })
  }));
};
/* harmony default export */ __webpack_exports__.Z = (FormTreeSelectSearch);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///30653
`)},27076:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(5574);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(97857);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _utils_string__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(7369);
/* harmony import */ var _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(6110);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(7837);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(67294);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(85893);







var PageContainerTabsWithSearch = function PageContainerTabsWithSearch(_ref) {
  var tabsItems = _ref.tabsItems,
    _ref$searchParamsUrlK = _ref.searchParamsUrlKey,
    searchParamsUrlKey = _ref$searchParamsUrlK === void 0 ? 'tab' : _ref$searchParamsUrlK,
    _onTabChange = _ref.onTabChange,
    pageTitle = _ref.pageTitle;
  var tabsItemsFormat = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(function () {
    return tabsItems === null || tabsItems === void 0 ? void 0 : tabsItems.map(function (item) {
      return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({}, item), {}, {
        tabKey: item.tabKey || (0,_utils_string__WEBPACK_IMPORTED_MODULE_5__/* .nonAccentVietnamese */ .w)(item.label)
      });
    });
  }, [tabsItems]);
  var _useSearchParams = (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)(),
    _useSearchParams2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_0___default()(_useSearchParams, 2),
    searchParams = _useSearchParams2[0],
    setSearchParams = _useSearchParams2[1];
  var tabActive = searchParamsUrlKey ? searchParams.get(searchParamsUrlKey) : undefined;
  console.log('tabActive', tabActive);
  var setTabActive = searchParamsUrlKey ? function (tabActiveVal) {
    searchParams.set(searchParamsUrlKey, tabActiveVal.toString());
    setSearchParams(searchParams);
  } : undefined;
  var tabList = tabsItemsFormat === null || tabsItemsFormat === void 0 ? void 0 : tabsItemsFormat.map(function (item) {
    return {
      tab: item.label,
      key: item.tabKey,
      tabKey: item.tabKey
    };
  });
  var tabPageActive = searchParamsUrlKey ? (tabsItemsFormat === null || tabsItemsFormat === void 0 ? void 0 : tabsItemsFormat.find(function (item) {
    return item.tabKey === tabActive;
  })) || (tabsItemsFormat === null || tabsItemsFormat === void 0 ? void 0 : tabsItemsFormat[0]) : undefined;
  var ComponentActive = tabPageActive === null || tabPageActive === void 0 ? void 0 : tabPageActive.component;
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_6__/* .PageContainer */ ._z, {
    fixedHeader: true,
    extra: tabPageActive === null || tabPageActive === void 0 ? void 0 : tabPageActive.extraPage,
    tabActiveKey: tabPageActive === null || tabPageActive === void 0 ? void 0 : tabPageActive.tabKey,
    tabList: tabList,
    onTabChange: function onTabChange(tabActiveVal) {
      _onTabChange === null || _onTabChange === void 0 || _onTabChange(tabActiveVal);
      if (searchParamsUrlKey) setTabActive === null || setTabActive === void 0 || setTabActive(tabActiveVal);
    },
    title: pageTitle,
    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(react__WEBPACK_IMPORTED_MODULE_3__.Suspense, {
      fallback: (tabPageActive === null || tabPageActive === void 0 ? void 0 : tabPageActive.fallback) || null,
      children: ComponentActive && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(ComponentActive, {})
    })
  });
};
/* harmony default export */ __webpack_exports__.Z = (PageContainerTabsWithSearch);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///27076
`)},17208:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return /* binding */ ProductManagement_BOM; }
});

// EXTERNAL MODULE: ./src/components/PageContainerTabsWithSearch/index.tsx
var PageContainerTabsWithSearch = __webpack_require__(27076);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-skeleton/es/components/Descriptions/index.js
var Descriptions = __webpack_require__(44688);
// EXTERNAL MODULE: ./src/.umi-production/exports.ts + 34 modules
var _umi_production_exports = __webpack_require__(7837);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/regeneratorRuntime.js
var regeneratorRuntime = __webpack_require__(15009);
var regeneratorRuntime_default = /*#__PURE__*/__webpack_require__.n(regeneratorRuntime);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(99289);
var asyncToGenerator_default = /*#__PURE__*/__webpack_require__.n(asyncToGenerator);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/slicedToArray.js
var slicedToArray = __webpack_require__(5574);
var slicedToArray_default = /*#__PURE__*/__webpack_require__.n(slicedToArray);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/objectSpread2.js
var objectSpread2 = __webpack_require__(97857);
var objectSpread2_default = /*#__PURE__*/__webpack_require__.n(objectSpread2);
// EXTERNAL MODULE: ./src/services/utils.ts
var utils = __webpack_require__(467);
;// CONCATENATED MODULE: ./src/services/InventoryManagementV3/bom.ts





var handleError = function handleError(error) {
  console.log("Error in services/bom: \\n".concat(error));
};
var getBOMs = /*#__PURE__*/function () {
  var _ref = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(params) {
    var res;
    return regeneratorRuntime_default()().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.next = 2;
          return (0,_umi_production_exports.request)((0,utils/* generateAPIPath */.rH)('api/v2/bom'), {
            params: (0,utils/* getParamsReqList */.vj)(params)
          });
        case 2:
          res = _context.sent;
          return _context.abrupt("return", res.result);
        case 4:
        case "end":
          return _context.stop();
      }
    }, _callee);
  }));
  return function getBOMs(_x) {
    return _ref.apply(this, arguments);
  };
}();
var getBOMDetail = /*#__PURE__*/function () {
  var _ref3 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2(_ref2) {
    var name, res;
    return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          name = _ref2.name;
          _context2.prev = 1;
          _context2.next = 4;
          return (0,_umi_production_exports.request)((0,utils/* generateAPIPath */.rH)('api/v2/bom/detail'), {
            params: {
              name: name
            }
          });
        case 4:
          res = _context2.sent;
          return _context2.abrupt("return", {
            data: res.result.docs.at(0)
          });
        case 8:
          _context2.prev = 8;
          _context2.t0 = _context2["catch"](1);
          handleError(_context2.t0);
          return _context2.abrupt("return", {
            data: {}
          });
        case 12:
        case "end":
          return _context2.stop();
      }
    }, _callee2, null, [[1, 8]]);
  }));
  return function getBOMDetail(_x2) {
    return _ref3.apply(this, arguments);
  };
}();
var saveBom = /*#__PURE__*/function () {
  var _ref4 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee3(payload) {
    var _res$result$docs, res;
    return regeneratorRuntime_default()().wrap(function _callee3$(_context3) {
      while (1) switch (_context3.prev = _context3.next) {
        case 0:
          _context3.prev = 0;
          _context3.next = 3;
          return (0,_umi_production_exports.request)((0,utils/* generateAPIPath */.rH)('api/v2/bom/save'), {
            method: 'POST',
            data: objectSpread2_default()({}, payload)
          });
        case 3:
          res = _context3.sent;
          return _context3.abrupt("return", (_res$result$docs = res.result.docs) === null || _res$result$docs === void 0 ? void 0 : _res$result$docs.at(0));
        case 7:
          _context3.prev = 7;
          _context3.t0 = _context3["catch"](0);
          handleError(_context3.t0);
          throw _context3.t0;
        case 11:
        case "end":
          return _context3.stop();
      }
    }, _callee3, null, [[0, 7]]);
  }));
  return function saveBom(_x3) {
    return _ref4.apply(this, arguments);
  };
}();
var deleteBom = /*#__PURE__*/function () {
  var _ref5 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee4(name) {
    var _res$result$docs2, res;
    return regeneratorRuntime_default()().wrap(function _callee4$(_context4) {
      while (1) switch (_context4.prev = _context4.next) {
        case 0:
          _context4.prev = 0;
          _context4.next = 3;
          return (0,_umi_production_exports.request)((0,utils/* generateAPIPath */.rH)('api/v2/bom'), {
            method: 'DELETE',
            params: {
              name: name
            }
          });
        case 3:
          res = _context4.sent;
          return _context4.abrupt("return", (_res$result$docs2 = res.result.docs) === null || _res$result$docs2 === void 0 ? void 0 : _res$result$docs2.at(0));
        case 7:
          _context4.prev = 7;
          _context4.t0 = _context4["catch"](0);
          handleError(_context4.t0);
          throw _context4.t0;
        case 11:
        case "end":
          return _context4.stop();
      }
    }, _callee4, null, [[0, 7]]);
  }));
  return function deleteBom(_x4) {
    return _ref5.apply(this, arguments);
  };
}();
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/EyeOutlined.js
var EyeOutlined = __webpack_require__(55287);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-table/es/Table.js + 76 modules
var Table = __webpack_require__(4894);
// EXTERNAL MODULE: ./node_modules/antd/es/button/index.js + 9 modules
var es_button = __webpack_require__(14726);
// EXTERNAL MODULE: ./node_modules/antd/es/space/index.js + 2 modules
var space = __webpack_require__(42075);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./src/common/contanst/constanst.ts
var constanst = __webpack_require__(86604);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/layouts/ProForm/index.js + 3 modules
var ProForm = __webpack_require__(34994);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/Text/index.js
var Text = __webpack_require__(5966);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/DatePicker/index.js + 5 modules
var DatePicker = __webpack_require__(50335);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/TextArea/index.js
var TextArea = __webpack_require__(90672);
// EXTERNAL MODULE: ./node_modules/antd/es/modal/index.js + 1 modules
var modal = __webpack_require__(85576);
// EXTERNAL MODULE: ./node_modules/antd/es/form/hooks/useForm.js + 2 modules
var useForm = __webpack_require__(88942);
// EXTERNAL MODULE: ./node_modules/react/jsx-runtime.js
var jsx_runtime = __webpack_require__(85893);
;// CONCATENATED MODULE: ./src/pages/InventoryManagementV3/ProductManagement/BOM/components/BOMDetail.tsx











var BOMDetail = function BOMDetail(_ref) {
  var name = _ref.name,
    isModalOpen = _ref.isModalOpen,
    setIsModalOpen = _ref.setIsModalOpen;
  var actionRef = (0,react.useRef)();
  var _useForm = (0,useForm/* default */.Z)(),
    _useForm2 = slicedToArray_default()(_useForm, 1),
    form = _useForm2[0];
  var _useState = (0,react.useState)([]),
    _useState2 = slicedToArray_default()(_useState, 2),
    items = _useState2[0],
    setItems = _useState2[1];
  var _useRequest = (0,_umi_production_exports.useRequest)(getBOMDetail, {
      manual: true,
      onError: function onError(error) {
        console.log('error', error.message);
      },
      onSuccess: function onSuccess(data, params) {}
    }),
    data = _useRequest.data,
    loading = _useRequest.loading,
    refresh = _useRequest.refresh,
    run = _useRequest.run;
  var handleReload = function handleReload() {
    var _actionRef$current;
    (_actionRef$current = actionRef.current) === null || _actionRef$current === void 0 || _actionRef$current.reload();
    refresh();
  };
  var columns = [{
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.index"
    }),
    dataIndex: 'index',
    render: function render(dom, entity, index) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        children: index + 1
      });
    },
    width: 10
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "warehouse-management.stock-entry-history.item_label"
    }),
    dataIndex: 'item_label',
    width: 15,
    render: function render(value, record, index) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        style: {
          whiteSpace: 'pre-wrap'
        },
        children: record.item_label
      });
    }
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.unit"
    }),
    dataIndex: 'uom_name',
    width: 20
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.quantity"
    }),
    dataIndex: 'stock_qty',
    width: 20,
    render: function render(dom, entity, index, action, schema) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        children: (0,utils/* formatNumeral */.GW)(entity.stock_qty)
      });
    }
  }];
  (0,react.useEffect)(function () {
    run({
      name: name
    });
  }, [name]);
  (0,react.useEffect)(function () {
    form.resetFields();
    form.setFieldsValue(data);
    form.setFieldValue('user', "".concat(data === null || data === void 0 ? void 0 : data.user_first_name, " ").concat(data === null || data === void 0 ? void 0 : data.user_last_name));
    setItems((data === null || data === void 0 ? void 0 : data.items) || []);
  }, [data]);
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  return /*#__PURE__*/(0,jsx_runtime.jsxs)(modal/* default */.Z, {
    open: isModalOpen,
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: 'common.bom.detail'
    }),
    onCancel: function onCancel() {
      setIsModalOpen === null || setIsModalOpen === void 0 || setIsModalOpen(false);
      handleReload();
    },
    footer: [],
    width: 800,
    children: [/*#__PURE__*/(0,jsx_runtime.jsxs)(ProForm/* ProForm */.A, {
      submitter: false,
      disabled: true,
      form: form,
      layout: "vertical",
      grid: true,
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)(Text/* default */.Z, {
        label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
          id: 'bom-management.created_item'
        }),
        colProps: {
          span: 8
        },
        name: 'item_label',
        width: "md"
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(Text/* default */.Z, {
        label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
          id: 'bom-management.created_item_quantity'
        }),
        colProps: {
          span: 8
        },
        name: 'quantity',
        width: "md"
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(Text/* default */.Z, {
        label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
          id: 'common.unit'
        }),
        colProps: {
          span: 8
        },
        name: 'uom_name',
        width: "md"
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(DatePicker/* default */.Z, {
        label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
          id: 'common.creation'
        }),
        colProps: {
          span: 8
        },
        name: 'creation',
        width: "md",
        fieldProps: {
          format: constanst/* DEFAULT_DATE_FORMAT_WITHOUT_TIME */.ug
        }
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(Text/* default */.Z, {
        label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
          id: 'common.creator'
        }),
        colProps: {
          span: 8
        },
        name: 'user',
        width: "md"
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(TextArea/* default */.Z, {
        label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
          id: 'common.description'
        }),
        name: 'bom_description',
        colProps: {
          span: 24
        },
        fieldProps: {
          autoSize: {
            minRows: 1,
            maxRows: 2
          }
        }
      })]
    }), /*#__PURE__*/(0,jsx_runtime.jsx)("br", {}), /*#__PURE__*/(0,jsx_runtime.jsx)(Table/* default */.Z, {
      columns: columns,
      cardBordered: true,
      size: "small",
      dataSource: items,
      rowKey: 'name',
      search: false
    })]
  });
};
/* harmony default export */ var components_BOMDetail = (BOMDetail);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/toConsumableArray.js
var toConsumableArray = __webpack_require__(19632);
var toConsumableArray_default = /*#__PURE__*/__webpack_require__.n(toConsumableArray);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/createForOfIteratorHelper.js
var createForOfIteratorHelper = __webpack_require__(64599);
var createForOfIteratorHelper_default = /*#__PURE__*/__webpack_require__.n(createForOfIteratorHelper);
// EXTERNAL MODULE: ./src/components/Form/FormTreeSelectSearch/index.tsx
var FormTreeSelectSearch = __webpack_require__(30653);
// EXTERNAL MODULE: ./src/pages/InventoryManagementV3/ProductManagement/Products/CreateProduct-v2/index.tsx + 3 modules
var CreateProduct_v2 = __webpack_require__(53328);
// EXTERNAL MODULE: ./src/services/InventoryManagementV3/product-item.ts
var product_item = __webpack_require__(58642);
// EXTERNAL MODULE: ./src/services/stock/item.ts
var item = __webpack_require__(89436);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/PlusOutlined.js
var PlusOutlined = __webpack_require__(51042);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/layouts/ModalForm/index.js
var ModalForm = __webpack_require__(37476);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/Digit/index.js
var Digit = __webpack_require__(31199);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/Select/index.js
var Select = __webpack_require__(64317);
// EXTERNAL MODULE: ./node_modules/antd/es/typography/index.js + 18 modules
var typography = __webpack_require__(25514);
// EXTERNAL MODULE: ./node_modules/antd/es/app/index.js + 1 modules
var app = __webpack_require__(31418);
// EXTERNAL MODULE: ./node_modules/antd/es/row/index.js
var row = __webpack_require__(71230);
// EXTERNAL MODULE: ./node_modules/antd/es/col/index.js
var col = __webpack_require__(15746);
// EXTERNAL MODULE: ./node_modules/antd/es/divider/index.js + 1 modules
var divider = __webpack_require__(96074);
// EXTERNAL MODULE: ./node_modules/dayjs/dayjs.min.js
var dayjs_min = __webpack_require__(27484);
var dayjs_min_default = /*#__PURE__*/__webpack_require__.n(dayjs_min);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/DeleteOutlined.js
var DeleteOutlined = __webpack_require__(82061);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-table/es/components/EditableTable/index.js + 1 modules
var EditableTable = __webpack_require__(88280);
// EXTERNAL MODULE: ./node_modules/antd/es/input-number/index.js + 16 modules
var input_number = __webpack_require__(9735);
;// CONCATENATED MODULE: ./src/pages/InventoryManagementV3/ProductManagement/BOM/components/CreateBomModalTable.tsx











var CreateBomModalTable = function CreateBomModalTable(_ref) {
  var data = _ref.data,
    setData = _ref.setData;
  var actionRef = (0,react.useRef)();
  var handleDeleteItem = function handleDeleteItem(index) {
    var newData = data.filter(function (item) {
      return item.key !== index;
    });
    setData(newData);
  };
  var _useState = (0,react.useState)(function () {
      return data.map(function (item) {
        return item.key;
      });
    }),
    _useState2 = slicedToArray_default()(_useState, 2),
    editableKeys = _useState2[0],
    setEditableRowKeys = _useState2[1];
  // const handleUpdateQuantity = (record: ICreateBomItem) => {
  //   record.total_price = record.qty * record.rate;
  // };
  var handleSelectUnit = function handleSelectUnit(index, value) {
    var _actionRef$current;
    var newData = structuredClone(data);
    var recordIndex = newData.findIndex(function (item) {
      return item.key === index;
    });
    newData[recordIndex].conversion_factor = value;
    setData(newData);
    (_actionRef$current = actionRef.current) === null || _actionRef$current === void 0 || _actionRef$current.reload();
  };
  var columns = [{
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: 'common.index'
    }),
    dataIndex: 'index',
    editable: false,
    render: function render(dom, entity, index, action, schema) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        children: index + 1
      });
    },
    width: 40
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: 'warehouse-management.import-voucher.item_code'
    }),
    dataIndex: 'item_name',
    editable: false,
    width: 80
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "warehouse-management.import-voucher.item_name",
      defaultMessage: "unknown"
    }),
    dataIndex: 'item_label',
    editable: false,
    width: 80
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "warehouse-management.import-voucher.quantity",
      defaultMessage: "unknown"
    }),
    dataIndex: 'qty',
    search: false,
    renderFormItem: function renderFormItem(schema, config, form, action) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)(input_number/* default */.Z, {
        style: {
          width: '100%'
        },
        formatter: function formatter(value) {
          return "".concat(value).replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');
        },
        parser: function parser(value) {
          return value.replace(/\\$\\s?|(,*)/g, '');
        },
        step: "0.01"
      });
    },
    width: 80
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.unit"
    }),
    editable: false,
    dataIndex: 'conversion_factor',
    render: function render(dom, entity, index, action, schema) {
      return [/*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
        formItemProps: {
          style: {
            marginBottom: 0
          }
        },
        fieldProps: {
          defaultValue: entity.uom_label
        },
        placeholder: "Lo\\u1EA1i \\u0111\\u1EBFm",
        request: /*#__PURE__*/asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
          var _res$data$uoms;
          var res, formatted_res, options;
          return regeneratorRuntime_default()().wrap(function _callee$(_context) {
            while (1) switch (_context.prev = _context.next) {
              case 0:
                _context.next = 2;
                return (0,product_item/* getDetailsProductItemV3 */.eX)({
                  name: entity.item_code || ''
                });
              case 2:
                _context.t0 = _context.sent;
                if (_context.t0) {
                  _context.next = 5;
                  break;
                }
                _context.t0 = [];
              case 5:
                res = _context.t0;
                formatted_res = ((_res$data$uoms = res.data.uoms) === null || _res$data$uoms === void 0 ? void 0 : _res$data$uoms.map(function (item) {
                  return {
                    label: item.uom_name,
                    value: item.conversion_factor
                  };
                })) || [];
                options = [{
                  label: entity.uom_label,
                  value: 1
                }];
                return _context.abrupt("return", [].concat(options, toConsumableArray_default()(formatted_res)));
              case 9:
              case "end":
                return _context.stop();
            }
          }, _callee);
        })),
        onChange: function onChange(e) {
          return handleSelectUnit(entity.key, +e);
        }
      }, 'conversion_factor')];
    },
    width: 80
  },
  // {
  //   title: <FormattedMessage id="warehouse-management.import-voucher.rate" defaultMessage="unknown" />,
  //   dataIndex: 'rate',
  //   search: false,
  //   width: 80,
  //   renderFormItem(schema, config, form, action) {
  //     return (
  //       <InputNumber
  //         style={{ width: '100%' }}
  //         formatter={(value) => \`\${value}\`.replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',')}
  //         parser={(value) => value!.replace(/\\$\\s?|(,*)/g, '')}
  //         step="0.01"
  //       />
  //     );
  //   },
  // },
  // {
  //   title: <FormattedMessage id="warehouse-management.import-voucher.price" defaultMessage="unknown" />,
  //   dataIndex: 'total_price',
  //   editable: false,
  //   render: (text, record) => numeral(record.total_price).format('0,0.00'), // \u0110\u1ECBnh d\u1EA1ng s\u1ED1 \u1EDF \u0111\xE2y
  //   width: 80,
  // },
  {
    title: 'Action',
    dataIndex: 'key',
    editable: false,
    render: function render(index, record, _, action) {
      return [/*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
        icon: /*#__PURE__*/(0,jsx_runtime.jsx)(DeleteOutlined/* default */.Z, {}),
        onClick: function onClick() {
          return handleDeleteItem(index);
        }
      }, "delete")];
    },
    width: 80
  }];
  (0,react.useEffect)(function () {
    setEditableRowKeys(data.map(function (item) {
      return item.key;
    }));
  }, [data]);
  (0,react.useEffect)(function () {
    var _actionRef$current2;
    (_actionRef$current2 = actionRef.current) === null || _actionRef$current2 === void 0 || _actionRef$current2.reload();
  }, [data, editableKeys]);
  return /*#__PURE__*/(0,jsx_runtime.jsx)(EditableTable/* default */.Z, {
    style: {
      minWidth: '100%'
    },
    columns: columns,
    actionRef: actionRef,
    cardBordered: true,
    request: /*#__PURE__*/asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2() {
      var params,
        sort,
        filter,
        _args2 = arguments;
      return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            params = _args2.length > 0 && _args2[0] !== undefined ? _args2[0] : {};
            sort = _args2.length > 1 ? _args2[1] : undefined;
            filter = _args2.length > 2 ? _args2[2] : undefined;
            return _context2.abrupt("return", {
              data: data,
              success: true,
              total: data.length
            });
          case 4:
          case "end":
            return _context2.stop();
        }
      }, _callee2);
    })),
    editable: {
      type: 'multiple',
      editableKeys: editableKeys,
      actionRender: function actionRender(row, config, defaultDoms) {
        return [defaultDoms["delete"]];
      },
      onValuesChange: function onValuesChange(record, recordList) {
        // handleUpdateQuantity(record);
        setData(recordList);
      },
      onChange: setEditableRowKeys
    },
    recordCreatorProps: false,
    rowKey: "key",
    search: false,
    options: {
      setting: {
        listsHeight: 400
      }
    },
    pagination: {
      defaultPageSize: 20,
      showSizeChanger: true,
      pageSizeOptions: ['20', '50', '100']
    },
    toolbar: {
      multipleLine: false
    },
    toolBarRender: false,
    size: "small",
    dateFormatter: "string"
  });
};
/* harmony default export */ var components_CreateBomModalTable = (CreateBomModalTable);
;// CONCATENATED MODULE: ./src/pages/InventoryManagementV3/ProductManagement/BOM/components/CreateBomModal.tsx






















var Title = typography/* default */.Z.Title;
var CreateBomModal = function CreateBomModal(_ref) {
  var onSuccess = _ref.onSuccess,
    importFromTask = _ref.importFromTask,
    dataProps = _ref.dataProps,
    taskID = _ref.taskID;
  var _useState = (0,react.useState)(false),
    _useState2 = slicedToArray_default()(_useState, 2),
    submitting = _useState2[0],
    setSubmitting = _useState2[1];
  var _useState3 = (0,react.useState)(),
    _useState4 = slicedToArray_default()(_useState3, 2),
    treeData = _useState4[0],
    setTreeData = _useState4[1];
  var _useState5 = (0,react.useState)([]),
    _useState6 = slicedToArray_default()(_useState5, 2),
    selectedItems = _useState6[0],
    setSelectedItems = _useState6[1];
  var _useState7 = (0,react.useState)([]),
    _useState8 = slicedToArray_default()(_useState7, 2),
    items = _useState8[0],
    setItems = _useState8[1];
  var _useState9 = (0,react.useState)(),
    _useState10 = slicedToArray_default()(_useState9, 2),
    createdItem = _useState10[0],
    setCreatedItem = _useState10[1];
  var _useForm = (0,useForm/* default */.Z)(),
    _useForm2 = slicedToArray_default()(_useForm, 1),
    form = _useForm2[0];
  //current user
  var _useModel = (0,_umi_production_exports.useModel)("@@initialState"),
    initialState = _useModel.initialState;
  var curentUser = initialState === null || initialState === void 0 ? void 0 : initialState.currentUser;
  var _App$useApp = app/* default */.Z.useApp(),
    message = _App$useApp.message;

  //get list items in group
  var getItemTreeData = /*#__PURE__*/function () {
    var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
      var groupByItemGroupWithItemGroupData, data, dataGroup, dataMap, generatedData;
      return regeneratorRuntime_default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            groupByItemGroupWithItemGroupData = function _groupByItemGroupWith(stockItems, stockItemGroups) {
              var groupedItems = {};
              stockItems.forEach(function (item) {
                var itemGroup = item.item_group;
                if (!groupedItems[itemGroup]) {
                  groupedItems[itemGroup] = {
                    items: [],
                    itemGroup: {}
                  };
                }
                groupedItems[itemGroup].items.push(item);
              });

              // Now add item group data
              var _iterator = createForOfIteratorHelper_default()(stockItemGroups),
                _step;
              try {
                for (_iterator.s(); !(_step = _iterator.n()).done;) {
                  var itemGroup = _step.value;
                  var itemGroupName = itemGroup.item_group_name;
                  if (groupedItems[itemGroupName]) {
                    groupedItems[itemGroupName].itemGroup = itemGroup;
                  }
                }
              } catch (err) {
                _iterator.e(err);
              } finally {
                _iterator.f();
              }
              return groupedItems;
            }; // Helper function
            _context.next = 3;
            return (0,item/* getItemList */.m)({});
          case 3:
            data = _context.sent;
            _context.next = 6;
            return (0,item/* getItemGroupList */.A)({});
          case 6:
            dataGroup = _context.sent;
            dataMap = groupByItemGroupWithItemGroupData(data.data, dataGroup.data);
            if (dataMap) {
              generatedData = Object.entries(dataMap).map(function (_ref3) {
                var _groupData$itemGroup;
                var _ref4 = slicedToArray_default()(_ref3, 2),
                  itemGroup = _ref4[0],
                  groupData = _ref4[1];
                return {
                  title: ((_groupData$itemGroup = groupData.itemGroup) === null || _groupData$itemGroup === void 0 ? void 0 : _groupData$itemGroup.label) || '',
                  value: itemGroup,
                  key: itemGroup,
                  children: groupData.items.map(function (item) {
                    return {
                      title: item.label || '',
                      value: item.name || '',
                      key: item.name || ''
                    };
                  })
                };
              });
              setTreeData(generatedData);
              setItems(data.data);
            }
          case 9:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }));
    return function getItemTreeData() {
      return _ref2.apply(this, arguments);
    };
  }();
  var handleCreateNewItem = /*#__PURE__*/function () {
    var _ref5 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2() {
      return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            _context2.next = 2;
            return getItemTreeData();
          case 2:
            form.resetFields(['items']);
          case 3:
          case "end":
            return _context2.stop();
        }
      }, _callee2);
    }));
    return function handleCreateNewItem() {
      return _ref5.apply(this, arguments);
    };
  }();
  var handleAddItems = /*#__PURE__*/function () {
    var _ref6 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee3() {
      var item_ids, filtered_items, formatted_items;
      return regeneratorRuntime_default()().wrap(function _callee3$(_context3) {
        while (1) switch (_context3.prev = _context3.next) {
          case 0:
            item_ids = form.getFieldValue('items');
            if (item_ids) {
              _context3.next = 4;
              break;
            }
            message.error("Vui l\\xF2ng ch\\u1ECDn h\\xE0ng h\\xF3a");
            return _context3.abrupt("return");
          case 4:
            filtered_items = items.filter(function (item) {
              return item_ids.includes(item.name);
            }); // for (const item of filtered_items) {
            //   const inventory =
            //     (
            //       await getInventoryV3({
            //         warehouse: selectedWarehouse,
            //         item_name: item.item_name,
            //         page: '1',
            //         size: '1',
            //       })
            //     ).data?.at(0)?.actual_qty || 0;
            //   item.actual_qty = inventory;
            // }
            formatted_items = filtered_items.map(function (item) {
              return {
                conversion_factor: 1,
                item_code: item.name,
                item_name: item.item_name,
                qty: 0,
                item_label: item.label,
                key: Date.now() * 100 + Math.floor(Math.random() * 100).toString(),
                uom_label: item.uom_label
              };
            });
            setSelectedItems(function (prev) {
              return [].concat(toConsumableArray_default()(formatted_items), toConsumableArray_default()(prev));
            });
            form.setFieldValue('items', []);
            return _context3.abrupt("return");
          case 9:
          case "end":
            return _context3.stop();
        }
      }, _callee3);
    }));
    return function handleAddItems() {
      return _ref6.apply(this, arguments);
    };
  }();
  var handleChoseCreatedItem = /*#__PURE__*/function () {
    var _ref7 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee4(e) {
      var item;
      return regeneratorRuntime_default()().wrap(function _callee4$(_context4) {
        while (1) switch (_context4.prev = _context4.next) {
          case 0:
            item = items.filter(function (item) {
              return item.name === e;
            }).at(0);
            console.log(e, item);
            setCreatedItem(item);
            form.resetFields(['created_item_unit']);
          case 4:
          case "end":
            return _context4.stop();
        }
      }, _callee4);
    }));
    return function handleChoseCreatedItem(_x) {
      return _ref7.apply(this, arguments);
    };
  }();

  /**\r
   * Helper functions for handleFinish\r
   */
  var validateItems = function validateItems(selectedItems) {
    var _iterator2 = createForOfIteratorHelper_default()(selectedItems),
      _step2;
    try {
      for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {
        var item = _step2.value;
        if (item.qty === null) {
          message.error("H\\xE3y cung c\\u1EA5p \\u0111\\u1EA7y \\u0111\\u1EE7 s\\u1ED1 l\\u01B0\\u1EE3ng c\\u1EE7a h\\xE0ng h\\xF3a");
          return false;
        }
        if (item.qty <= 0) {
          message.error("S\\u1ED1 l\\u01B0\\u1EE3ng c\\u1EE7a h\\xE0ng h\\xF3a ph\\u1EA3i l\\u1EDBn h\\u01A1n 0");
          return false;
        }
      }
    } catch (err) {
      _iterator2.e(err);
    } finally {
      _iterator2.f();
    }
    return true;
  };
  var createBomPayload = function createBomPayload(values, selectedItems) {
    var commonFields = {
      bom_description: values.bom_description,
      item: createdItem === null || createdItem === void 0 ? void 0 : createdItem.name,
      quantity: values.created_item_quantity * values.created_item_unit,
      items: selectedItems.map(function (item) {
        return {
          conversion_factor: item.conversion_factor,
          item_code: item.item_code,
          qty: item.qty
        };
      })
    };
    return objectSpread2_default()(objectSpread2_default()({}, commonFields), {}, {
      docstatus: 0,
      doctype: 'BOM',
      __islocal: 1,
      __unsaved: 1,
      owner: 'Administrator',
      company: 'VIIS',
      is_active: 1,
      is_default: 1,
      set_rate_of_sub_assembly_item_based_on_bom: 0,
      items: commonFields.items.map(function (item) {
        return objectSpread2_default()(objectSpread2_default()({}, item), {}, {
          docstatus: 0,
          doctype: 'BOM Item',
          name: 'new-bom-item' + Math.random().toString(36).substring(2, 8),
          __islocal: 1,
          __unsaved: 1,
          owner: 'Administrator',
          do_not_explode: 0,
          allow_alternative_item: 0,
          parentfield: 'items',
          parenttype: 'BOM'
        });
      })
    });
  };

  //run when submit Form
  var handleFinish = /*#__PURE__*/function () {
    var _ref8 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee5(values) {
      var bomPayload;
      return regeneratorRuntime_default()().wrap(function _callee5$(_context5) {
        while (1) switch (_context5.prev = _context5.next) {
          case 0:
            setSubmitting(true);
            _context5.prev = 1;
            if (validateItems(selectedItems)) {
              _context5.next = 4;
              break;
            }
            return _context5.abrupt("return");
          case 4:
            console.log({
              values: values,
              selectedItems: selectedItems
            });
            bomPayload = createBomPayload(values, selectedItems);
            _context5.next = 8;
            return saveBom(bomPayload);
          case 8:
            message.success("Success");
            onSuccess === null || onSuccess === void 0 || onSuccess();
            form.resetFields();
            setSelectedItems([]);
            return _context5.abrupt("return", true);
          case 15:
            _context5.prev = 15;
            _context5.t0 = _context5["catch"](1);
            console.log({
              error: _context5.t0
            });
            message.error("Error with Bom Saving");
            return _context5.abrupt("return");
          case 20:
            _context5.prev = 20;
            setSubmitting(false);
            return _context5.finish(20);
          case 23:
          case "end":
            return _context5.stop();
        }
      }, _callee5, null, [[1, 15, 20, 23]]);
    }));
    return function handleFinish(_x2) {
      return _ref8.apply(this, arguments);
    };
  }();
  var handleAbort = /*#__PURE__*/function () {
    var _ref9 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee6() {
      return regeneratorRuntime_default()().wrap(function _callee6$(_context6) {
        while (1) switch (_context6.prev = _context6.next) {
          case 0:
            if (!importFromTask) {
              setSelectedItems([]);
            }
          case 1:
          case "end":
            return _context6.stop();
        }
      }, _callee6);
    }));
    return function handleAbort() {
      return _ref9.apply(this, arguments);
    };
  }();
  (0,react.useEffect)(function () {
    var fetchData = /*#__PURE__*/function () {
      var _ref10 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee7() {
        return regeneratorRuntime_default()().wrap(function _callee7$(_context7) {
          while (1) switch (_context7.prev = _context7.next) {
            case 0:
              getItemTreeData();
            case 1:
            case "end":
              return _context7.stop();
          }
        }, _callee7);
      }));
      return function fetchData() {
        return _ref10.apply(this, arguments);
      };
    }();
    fetchData();
  }, []);

  // useEffect(() => {
  //   if (importFromTask) {
  //     loadData();
  //   }
  // }, [dataProps, selectedWarehouse]);

  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  return /*#__PURE__*/(0,jsx_runtime.jsxs)(ModalForm/* ModalForm */.Y, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "bom-management.creation"
    }),
    modalProps: {
      onCancel: handleAbort,
      destroyOnClose: true
    },
    trigger: /*#__PURE__*/(0,jsx_runtime.jsxs)(es_button/* default */.ZP, {
      icon: /*#__PURE__*/(0,jsx_runtime.jsx)(PlusOutlined/* default */.Z, {}),
      type: "primary",
      children: ['', /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
        id: "bom-management.creation"
      })]
    }),
    width: 800,
    form: form,
    layout: "vertical",
    rowProps: {
      gutter: [16, 0]
    },
    submitter: {
      render: function render(props, defaultDoms) {
        return [/*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
          onClick: function onClick() {
            props.submit();
          },
          type: "primary",
          style: {
            width: '100%'
          },
          loading: submitting,
          children: "\\u0110\\u1ED3ng \\xFD"
        }, "ok")];
      }
    },
    autoFocusFirstInput: true,
    grid: true,
    submitTimeout: 2000,
    initialValues: {
      posting_date: dayjs_min_default()().format(constanst/* DEFAULT_DATE_AND_HH_MM_FORMAT */.dD)
    },
    onFinish: handleFinish,
    children: [/*#__PURE__*/(0,jsx_runtime.jsx)(FormTreeSelectSearch/* default */.Z, {
      name: 'created_item',
      fieldProps: {
        treeData: treeData,
        onChange: function onChange(value) {
          while ((value === null || value === void 0 ? void 0 : value.length) > 1) {
            value.shift();
          }
          handleChoseCreatedItem(value.at(0));
        }
        // onChange={(value: any) => {
        //   if (value?.length > 1) {
        //     value.shift();
        //   }
        // }}
      },
      label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
        id: 'bom-management.created_item'
      }),
      colProps: {
        span: 8
      }
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(Digit/* default */.Z, {
      label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
        id: 'bom-management.created_item_quantity'
      }),
      name: "created_item_quantity",
      fieldProps: {
        formatter: function formatter(value) {
          return "".concat(value).replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');
        },
        parser: function parser(value) {
          return value.replace(/\\$\\s?|(,*)/g, '');
        },
        step: '0.01'
      },
      colProps: {
        span: 8
      }
    }), createdItem ? /*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
      name: 'created_item_unit',
      label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
        id: 'bom-management.created_item_unit'
      }),
      formItemProps: {
        style: {
          marginBottom: 0
        }
      },
      disabled: !createdItem,
      placeholder: "\\u0110\\u01A1n v\\u1ECB",
      required: true,
      rules: [{
        required: true,
        message: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
          id: 'common.required_error'
        })
      }],
      request: /*#__PURE__*/asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee8() {
        var _res$data$uoms;
        var res, formatted_res, options;
        return regeneratorRuntime_default()().wrap(function _callee8$(_context8) {
          while (1) switch (_context8.prev = _context8.next) {
            case 0:
              _context8.next = 2;
              return (0,product_item/* getDetailsProductItemV3 */.eX)({
                name: (createdItem === null || createdItem === void 0 ? void 0 : createdItem.name) || ''
              });
            case 2:
              _context8.t0 = _context8.sent;
              if (_context8.t0) {
                _context8.next = 5;
                break;
              }
              _context8.t0 = [];
            case 5:
              res = _context8.t0;
              formatted_res = ((_res$data$uoms = res.data.uoms) === null || _res$data$uoms === void 0 ? void 0 : _res$data$uoms.map(function (item) {
                return {
                  label: item.uom_name,
                  value: item.conversion_factor
                };
              })) || [];
              options = [{
                label: createdItem.uom_label,
                value: 1
              }];
              return _context8.abrupt("return", [].concat(options, toConsumableArray_default()(formatted_res)));
            case 9:
            case "end":
              return _context8.stop();
          }
        }, _callee8);
      })),
      colProps: {
        span: 8
      }
    }, 'conversion_factor') : /*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
      name: 'created_item_unit',
      label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
        id: 'bom-management.created_item_unit'
      }),
      formItemProps: {
        style: {
          marginBottom: 0
        }
      },
      disabled: !createdItem,
      placeholder: "Ch\\u1ECDn h\\xE0ng h\\xF3a"
      // onChange={(e) => handleSelectUnit(entity.key, +e)}
      ,
      colProps: {
        span: 8
      }
    }, 'conversion_factor'), /*#__PURE__*/(0,jsx_runtime.jsx)(TextArea/* default */.Z, {
      name: "bom_description",
      label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
        id: 'common.form.description'
      }),
      colProps: {
        span: 24
      }
      // width={'md'}
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(ProForm/* ProForm */.A.Group, {
      title: /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        style: {
          marginBottom: '-20px'
        },
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
          id: 'bom-management.item_list'
        })
      }),
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(FormTreeSelectSearch/* default */.Z, {
        name: 'items',
        fieldProps: {
          treeData: treeData
        },
        label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
          id: 'bom-management.source-items'
        }),
        dropdownBottom: /*#__PURE__*/(0,jsx_runtime.jsx)(CreateProduct_v2/* default */.Z, {
          buttonType: "link",
          onSuccess: handleCreateNewItem
        }, "create"),
        colProps: {
          span: 8
        }
      })
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(ProForm/* ProForm */.A.Group, {
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(row/* default */.Z, {
        style: {
          width: '100%'
        },
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
          span: 18,
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(space/* default */.Z, {
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
              type: "primary",
              icon: /*#__PURE__*/(0,jsx_runtime.jsx)(PlusOutlined/* default */.Z, {}),
              onClick: handleAddItems,
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
                id: 'bom-management.add_item'
              })
            })
          })
        })
      })
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(divider/* default */.Z, {}), /*#__PURE__*/(0,jsx_runtime.jsx)(components_CreateBomModalTable, {
      data: selectedItems,
      setData: setSelectedItems
    })]
  });
};
/* harmony default export */ var components_CreateBomModal = (CreateBomModal);
;// CONCATENATED MODULE: ./src/pages/InventoryManagementV3/ProductManagement/BOM/components/DeleteBom.tsx







// import useDeleteProject from '../hooks/useDeleleteProject';

var DeleteBom = function DeleteBom(_ref) {
  var name = _ref.name,
    onDeleteSuccess = _ref.onDeleteSuccess,
    label = _ref.label;
  var _App$useApp = app/* default */.Z.useApp(),
    modal = _App$useApp.modal;
  // const { run } = useDeleteProductItemV3();
  var _useRequest = (0,_umi_production_exports.useRequest)(deleteBom),
    run = _useRequest.run;
  return /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP
  // size="small"
  , {
    icon: /*#__PURE__*/(0,jsx_runtime.jsx)(DeleteOutlined/* default */.Z, {}),
    danger: true,
    onClick: function onClick(e) {
      e.stopPropagation();
      e.preventDefault();
      modal.confirm({
        title: "B\\u1EA1n c\\xF3 ch\\u1EAFc ch\\u1EAFc mu\\u1ED1n x\\xF3a BOM n\\xE0y c\\u1EE7a h\\xE0ng h\\xF3a ".concat(label, "?"),
        content: 'H\xE0nh \u0111\u1ED9ng n\xE0y kh\xF4ng th\u1EC3 ho\xE0n t\xE1c!',
        okButtonProps: {
          danger: true
        },
        onOk: function () {
          var _onOk = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
            return regeneratorRuntime_default()().wrap(function _callee$(_context) {
              while (1) switch (_context.prev = _context.next) {
                case 0:
                  _context.prev = 0;
                  _context.next = 3;
                  return run(name);
                case 3:
                  onDeleteSuccess === null || onDeleteSuccess === void 0 || onDeleteSuccess();
                  return _context.abrupt("return", true);
                case 7:
                  _context.prev = 7;
                  _context.t0 = _context["catch"](0);
                  return _context.abrupt("return", false);
                case 10:
                case "end":
                  return _context.stop();
              }
            }, _callee, null, [[0, 7]]);
          }));
          function onOk() {
            return _onOk.apply(this, arguments);
          }
          return onOk;
        }()
      });
    }
  });
};
/* harmony default export */ var components_DeleteBom = (DeleteBom);
;// CONCATENATED MODULE: ./src/pages/InventoryManagementV3/ProductManagement/BOM/components/BOMManagement.tsx















var BOMManagement = function BOMManagement() {
  var actionRef = (0,react.useRef)();
  var _useState = (0,react.useState)(false),
    _useState2 = slicedToArray_default()(_useState, 2),
    isModalOpen = _useState2[0],
    setIsModalOpen = _useState2[1];
  var _useState3 = (0,react.useState)(),
    _useState4 = slicedToArray_default()(_useState3, 2),
    currentItem = _useState4[0],
    setCurrentItem = _useState4[1];
  var handlePopupDetail = function handlePopupDetail(record) {
    setCurrentItem(record);
    setIsModalOpen(true);
  };
  var access = (0,_umi_production_exports.useAccess)();
  var _toolBarRender = [];
  var canDeleteCategory = access.canDeleteInCategoryManagement();
  if (access.canCreateInCategoryInventoryManagement()) {
    _toolBarRender.push( /*#__PURE__*/(0,jsx_runtime.jsx)(components_CreateBomModal, {
      onSuccess: function onSuccess() {
        var _actionRef$current;
        (_actionRef$current = actionRef.current) === null || _actionRef$current === void 0 || _actionRef$current.reload();
      }
    }));
  }
  var reloadTable = function reloadTable() {
    var _actionRef$current2;
    (_actionRef$current2 = actionRef.current) === null || _actionRef$current2 === void 0 || _actionRef$current2.reload();
  };
  var columns = [{
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.index"
    }),
    dataIndex: 'index',
    render: function render(dom, entity, index) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        children: index + 1
      });
    },
    hideInSearch: true,
    width: 5
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.item_label"
    }),
    dataIndex: 'item_name',
    width: 20,
    render: function render(_, record) {
      return /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
          icon: /*#__PURE__*/(0,jsx_runtime.jsx)(EyeOutlined/* default */.Z, {}),
          style: {
            marginRight: '8px'
          },
          onClick: function onClick() {
            return handlePopupDetail(record);
          }
        }), record === null || record === void 0 ? void 0 : record.item_label]
      });
    }
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: 'common.description'
    }),
    dataIndex: 'bom_description',
    // render(dom, entity, index, action, schema) {
    //   return <div>{formatDate(entity.creation)}</div>;
    // },
    hideInSearch: true,
    width: 50
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.creator"
    }),
    width: 20,
    hideInSearch: true,
    render: function render(dom, entity, index, action, schema) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        children: "".concat(entity.user_first_name, " ").concat(entity.user_last_name)
      });
    }
  }, {
    title: 'Action',
    dataIndex: 'name',
    search: false,
    width: 10,
    render: function render(dom, entity) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)(space/* default */.Z, {
        children: canDeleteCategory && /*#__PURE__*/(0,jsx_runtime.jsx)(components_DeleteBom, {
          onDeleteSuccess: reloadTable,
          name: entity.name,
          label: entity.item_label || ''
        })
      });
    }
  }];
  return /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
    children: [currentItem && /*#__PURE__*/(0,jsx_runtime.jsx)(components_BOMDetail, {
      name: currentItem.name,
      isModalOpen: isModalOpen,
      setIsModalOpen: setIsModalOpen
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(Table/* default */.Z, {
      actionRef: actionRef,
      columns: columns,
      search: {
        labelWidth: 'auto'
      },
      cardBordered: true,
      size: "small",
      pagination: {
        defaultPageSize: 20,
        showSizeChanger: true,
        pageSizeOptions: ['20', '50', '100']
      },
      request: ( /*#__PURE__*/function () {
        var _ref = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(params, sort, filter) {
          var filters, res;
          return regeneratorRuntime_default()().wrap(function _callee$(_context) {
            while (1) switch (_context.prev = _context.next) {
              case 0:
                _context.prev = 0;
                filters = [];
                if (params.item_name) {
                  filters.push(['BOM', 'item_name', 'like', params.item_name]);
                }
                _context.next = 5;
                return getBOMs({
                  page: params.current,
                  size: params.pageSize,
                  order_by: 'creation DESC',
                  filters: filters
                });
              case 5:
                res = _context.sent;
                return _context.abrupt("return", {
                  data: res.data,
                  sucess: true,
                  total: res.pagination.totalElements
                });
              case 9:
                _context.prev = 9;
                _context.t0 = _context["catch"](0);
                return _context.abrupt("return", {
                  data: [],
                  sucess: false
                });
              case 12:
              case "end":
                return _context.stop();
            }
          }, _callee, null, [[0, 9]]);
        }));
        return function (_x, _x2, _x3) {
          return _ref.apply(this, arguments);
        };
      }()),
      toolBarRender: function toolBarRender() {
        return _toolBarRender;
      }
    })]
  });
};
/* harmony default export */ var components_BOMManagement = (BOMManagement);
;// CONCATENATED MODULE: ./src/pages/InventoryManagementV3/ProductManagement/BOM/index.tsx





var BOM = function BOM() {
  var intl = (0,_umi_production_exports.useIntl)();
  return /*#__PURE__*/(0,jsx_runtime.jsx)(PageContainerTabsWithSearch/* default */.Z, {
    tabsItems: [{
      label: intl.formatMessage({
        id: 'common.bom-management'
      }),
      fallback: /*#__PURE__*/(0,jsx_runtime.jsx)(Descriptions/* DescriptionsSkeleton */.Yk, {
        active: true
      }),
      component: function component() {
        return /*#__PURE__*/(0,jsx_runtime.jsx)(components_BOMManagement, {});
      }
    }]
  });
};
/* harmony default export */ var ProductManagement_BOM = (BOM);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiMTcyMDguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFDcUM7QUFDd0I7QUFFN0QsSUFBTUcsV0FBVyxHQUFHLFNBQWRBLFdBQVdBLENBQUlDLEtBQVUsRUFBSztFQUNsQ0MsT0FBTyxDQUFDQyxHQUFHLDZCQUFBQyxNQUFBLENBQTZCSCxLQUFLLENBQUUsQ0FBQztBQUNsRCxDQUFDO0FBQ00sSUFBTUksT0FBTztFQUFBLElBQUFDLElBQUEsR0FBQUMsMEJBQUEsZUFBQUMsNEJBQUEsR0FBQUMsSUFBQSxDQUFHLFNBQUFDLFFBQU9DLE1BQTBCO0lBQUEsSUFBQUMsR0FBQTtJQUFBLE9BQUFKLDRCQUFBLEdBQUFLLElBQUEsVUFBQUMsU0FBQUMsUUFBQTtNQUFBLGtCQUFBQSxRQUFBLENBQUFDLElBQUEsR0FBQUQsUUFBQSxDQUFBRSxJQUFBO1FBQUE7VUFBQUYsUUFBQSxDQUFBRSxJQUFBO1VBQUEsT0FDcENwQixtQ0FBTyxDQUF1Q0MsaUNBQWUsQ0FBQyxZQUFZLENBQUMsRUFBRTtZQUM3RmEsTUFBTSxFQUFFWixrQ0FBZ0IsQ0FBQ1ksTUFBTTtVQUNqQyxDQUFDLENBQUM7UUFBQTtVQUZJQyxHQUFHLEdBQUFHLFFBQUEsQ0FBQUcsSUFBQTtVQUFBLE9BQUFILFFBQUEsQ0FBQUksTUFBQSxXQUdGUCxHQUFHLENBQUNRLE1BQU07UUFBQTtRQUFBO1VBQUEsT0FBQUwsUUFBQSxDQUFBTSxJQUFBO01BQUE7SUFBQSxHQUFBWCxPQUFBO0VBQUEsQ0FDbEI7RUFBQSxnQkFMWUwsT0FBT0EsQ0FBQWlCLEVBQUE7SUFBQSxPQUFBaEIsSUFBQSxDQUFBaUIsS0FBQSxPQUFBQyxTQUFBO0VBQUE7QUFBQSxHQUtuQjtBQUNNLElBQU1DLFlBQVk7RUFBQSxJQUFBQyxLQUFBLEdBQUFuQiwwQkFBQSxlQUFBQyw0QkFBQSxHQUFBQyxJQUFBLENBQUcsU0FBQWtCLFNBQUFDLEtBQUE7SUFBQSxJQUFBQyxJQUFBLEVBQUFqQixHQUFBO0lBQUEsT0FBQUosNEJBQUEsR0FBQUssSUFBQSxVQUFBaUIsVUFBQUMsU0FBQTtNQUFBLGtCQUFBQSxTQUFBLENBQUFmLElBQUEsR0FBQWUsU0FBQSxDQUFBZCxJQUFBO1FBQUE7VUFBU1ksSUFBSSxHQUFBRCxLQUFBLENBQUpDLElBQUk7VUFBQUUsU0FBQSxDQUFBZixJQUFBO1VBQUFlLFNBQUEsQ0FBQWQsSUFBQTtVQUFBLE9BRW5CcEIsbUNBQU8sQ0FBQ0MsaUNBQWUsQ0FBQyxtQkFBbUIsQ0FBQyxFQUFFO1lBQzlEYSxNQUFNLEVBQUU7Y0FDTmtCLElBQUksRUFBSkE7WUFDRjtVQUNGLENBQUMsQ0FBQztRQUFBO1VBSklqQixHQUFHLEdBQUFtQixTQUFBLENBQUFiLElBQUE7VUFBQSxPQUFBYSxTQUFBLENBQUFaLE1BQUEsV0FLRjtZQUFFYSxJQUFJLEVBQUVwQixHQUFHLENBQUNRLE1BQU0sQ0FBQ2EsSUFBSSxDQUFDQyxFQUFFLENBQUMsQ0FBQztVQUFnQixDQUFDO1FBQUE7VUFBQUgsU0FBQSxDQUFBZixJQUFBO1VBQUFlLFNBQUEsQ0FBQUksRUFBQSxHQUFBSixTQUFBO1VBRXBEL0IsV0FBVyxDQUFBK0IsU0FBQSxDQUFBSSxFQUFNLENBQUM7VUFBQyxPQUFBSixTQUFBLENBQUFaLE1BQUEsV0FDWjtZQUFFYSxJQUFJLEVBQUUsQ0FBQztVQUFnQixDQUFDO1FBQUE7UUFBQTtVQUFBLE9BQUFELFNBQUEsQ0FBQVYsSUFBQTtNQUFBO0lBQUEsR0FBQU0sUUFBQTtFQUFBLENBRXBDO0VBQUEsZ0JBWllGLFlBQVlBLENBQUFXLEdBQUE7SUFBQSxPQUFBVixLQUFBLENBQUFILEtBQUEsT0FBQUMsU0FBQTtFQUFBO0FBQUEsR0FZeEI7QUFFTSxJQUFNYSxPQUFPO0VBQUEsSUFBQUMsS0FBQSxHQUFBL0IsMEJBQUEsZUFBQUMsNEJBQUEsR0FBQUMsSUFBQSxDQUFHLFNBQUE4QixTQUFPQyxPQUFZO0lBQUEsSUFBQUMsZ0JBQUEsRUFBQTdCLEdBQUE7SUFBQSxPQUFBSiw0QkFBQSxHQUFBSyxJQUFBLFVBQUE2QixVQUFBQyxTQUFBO01BQUEsa0JBQUFBLFNBQUEsQ0FBQTNCLElBQUEsR0FBQTJCLFNBQUEsQ0FBQTFCLElBQUE7UUFBQTtVQUFBMEIsU0FBQSxDQUFBM0IsSUFBQTtVQUFBMkIsU0FBQSxDQUFBMUIsSUFBQTtVQUFBLE9BRXBCcEIsbUNBQU8sQ0FBQ0MsaUNBQWUsQ0FBQyxpQkFBaUIsQ0FBQyxFQUFFO1lBQzVEOEMsTUFBTSxFQUFFLE1BQU07WUFDZFosSUFBSSxFQUFBYSx1QkFBQSxLQUNDTCxPQUFPO1VBRWQsQ0FBQyxDQUFDO1FBQUE7VUFMSTVCLEdBQUcsR0FBQStCLFNBQUEsQ0FBQXpCLElBQUE7VUFBQSxPQUFBeUIsU0FBQSxDQUFBeEIsTUFBQSxZQUFBc0IsZ0JBQUEsR0FPRjdCLEdBQUcsQ0FBQ1EsTUFBTSxDQUFDYSxJQUFJLGNBQUFRLGdCQUFBLHVCQUFmQSxnQkFBQSxDQUFpQlAsRUFBRSxDQUFDLENBQUMsQ0FBQztRQUFBO1VBQUFTLFNBQUEsQ0FBQTNCLElBQUE7VUFBQTJCLFNBQUEsQ0FBQVIsRUFBQSxHQUFBUSxTQUFBO1VBRTdCM0MsV0FBVyxDQUFBMkMsU0FBQSxDQUFBUixFQUFNLENBQUM7VUFBQyxNQUFBUSxTQUFBLENBQUFSLEVBQUE7UUFBQTtRQUFBO1VBQUEsT0FBQVEsU0FBQSxDQUFBdEIsSUFBQTtNQUFBO0lBQUEsR0FBQWtCLFFBQUE7RUFBQSxDQUd0QjtFQUFBLGdCQWRZRixPQUFPQSxDQUFBUyxHQUFBO0lBQUEsT0FBQVIsS0FBQSxDQUFBZixLQUFBLE9BQUFDLFNBQUE7RUFBQTtBQUFBLEdBY25CO0FBRU0sSUFBTXVCLFNBQVM7RUFBQSxJQUFBQyxLQUFBLEdBQUF6QywwQkFBQSxlQUFBQyw0QkFBQSxHQUFBQyxJQUFBLENBQUcsU0FBQXdDLFNBQU9wQixJQUFZO0lBQUEsSUFBQXFCLGlCQUFBLEVBQUF0QyxHQUFBO0lBQUEsT0FBQUosNEJBQUEsR0FBQUssSUFBQSxVQUFBc0MsVUFBQUMsU0FBQTtNQUFBLGtCQUFBQSxTQUFBLENBQUFwQyxJQUFBLEdBQUFvQyxTQUFBLENBQUFuQyxJQUFBO1FBQUE7VUFBQW1DLFNBQUEsQ0FBQXBDLElBQUE7VUFBQW9DLFNBQUEsQ0FBQW5DLElBQUE7VUFBQSxPQUV0QnBCLG1DQUFPLENBQUNDLGlDQUFlLENBQUMsWUFBWSxDQUFDLEVBQUU7WUFDdkQ4QyxNQUFNLEVBQUUsUUFBUTtZQUNoQmpDLE1BQU0sRUFBRTtjQUNOa0IsSUFBSSxFQUFKQTtZQUNGO1VBQ0YsQ0FBQyxDQUFDO1FBQUE7VUFMSWpCLEdBQUcsR0FBQXdDLFNBQUEsQ0FBQWxDLElBQUE7VUFBQSxPQUFBa0MsU0FBQSxDQUFBakMsTUFBQSxZQUFBK0IsaUJBQUEsR0FPRnRDLEdBQUcsQ0FBQ1EsTUFBTSxDQUFDYSxJQUFJLGNBQUFpQixpQkFBQSx1QkFBZkEsaUJBQUEsQ0FBaUJoQixFQUFFLENBQUMsQ0FBQyxDQUFDO1FBQUE7VUFBQWtCLFNBQUEsQ0FBQXBDLElBQUE7VUFBQW9DLFNBQUEsQ0FBQWpCLEVBQUEsR0FBQWlCLFNBQUE7VUFFN0JwRCxXQUFXLENBQUFvRCxTQUFBLENBQUFqQixFQUFNLENBQUM7VUFBQyxNQUFBaUIsU0FBQSxDQUFBakIsRUFBQTtRQUFBO1FBQUE7VUFBQSxPQUFBaUIsU0FBQSxDQUFBL0IsSUFBQTtNQUFBO0lBQUEsR0FBQTRCLFFBQUE7RUFBQSxDQUd0QjtFQUFBLGdCQWRZRixTQUFTQSxDQUFBTSxHQUFBO0lBQUEsT0FBQUwsS0FBQSxDQUFBekIsS0FBQSxPQUFBQyxTQUFBO0VBQUE7QUFBQSxHQWNyQixDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQ3pEOEU7QUFDWDtBQUNuQjtBQVViO0FBQytCO0FBQ3RDO0FBQ2U7QUFDUTtBQUFBO0FBQUE7QUFPcEQsSUFBTWlELFNBQVMsR0FBRyxTQUFaQSxTQUFTQSxDQUFBbkUsSUFBQSxFQUFxRDtFQUFBLElBQS9DdUIsSUFBSSxHQUFBdkIsSUFBQSxDQUFKdUIsSUFBSTtJQUFFNkMsV0FBVyxHQUFBcEUsSUFBQSxDQUFYb0UsV0FBVztJQUFFQyxjQUFjLEdBQUFyRSxJQUFBLENBQWRxRSxjQUFjO0VBQ3BELElBQU1DLFNBQVMsR0FBR1QsZ0JBQU0sQ0FBYSxDQUFDO0VBQ3RDLElBQUFVLFFBQUEsR0FBZVosMEJBQU8sQ0FBQyxDQUFDO0lBQUFhLFNBQUEsR0FBQUMsdUJBQUEsQ0FBQUYsUUFBQTtJQUFqQkcsSUFBSSxHQUFBRixTQUFBO0VBQ1gsSUFBQUcsU0FBQSxHQUEwQmIsa0JBQVEsQ0FBbUIsRUFBRSxDQUFDO0lBQUFjLFVBQUEsR0FBQUgsdUJBQUEsQ0FBQUUsU0FBQTtJQUFqREUsS0FBSyxHQUFBRCxVQUFBO0lBQUVFLFFBQVEsR0FBQUYsVUFBQTtFQUN0QixJQUFBRyxXQUFBLEdBQXdDdEIsc0NBQVUsQ0FBQ3RDLFlBQVksRUFBRTtNQUMvRDZELE1BQU0sRUFBRSxJQUFJO01BQ1pDLE9BQU8sV0FBQUEsUUFBQ3RGLEtBQUssRUFBRTtRQUNiQyxPQUFPLENBQUNDLEdBQUcsQ0FBQyxPQUFPLEVBQUVGLEtBQUssQ0FBQ3VGLE9BQU8sQ0FBQztNQUNyQyxDQUFDO01BQ0RDLFNBQVMsV0FBQUEsVUFBQ3pELElBQUksRUFBRXJCLE1BQU0sRUFBRSxDQUFDO0lBQzNCLENBQUMsQ0FBQztJQU5NcUIsSUFBSSxHQUFBcUQsV0FBQSxDQUFKckQsSUFBSTtJQUFFMEQsT0FBTyxHQUFBTCxXQUFBLENBQVBLLE9BQU87SUFBRUMsT0FBTyxHQUFBTixXQUFBLENBQVBNLE9BQU87SUFBRUMsR0FBRyxHQUFBUCxXQUFBLENBQUhPLEdBQUc7RUFPbkMsSUFBTUMsWUFBWSxHQUFHLFNBQWZBLFlBQVlBLENBQUEsRUFBUztJQUFBLElBQUFDLGtCQUFBO0lBQ3pCLENBQUFBLGtCQUFBLEdBQUFsQixTQUFTLENBQUNtQixPQUFPLGNBQUFELGtCQUFBLGVBQWpCQSxrQkFBQSxDQUFtQkUsTUFBTSxDQUFDLENBQUM7SUFDM0JMLE9BQU8sQ0FBQyxDQUFDO0VBQ1gsQ0FBQztFQUNELElBQU1NLE9BQXFDLEdBQUcsQ0FDNUM7SUFDRUMsS0FBSyxlQUFFNUIsbUJBQUEsQ0FBQ1Qsd0NBQWdCO01BQUNzQyxFQUFFLEVBQUM7SUFBYyxDQUFFLENBQUM7SUFDN0NDLFNBQVMsRUFBRSxPQUFPO0lBQ2xCQyxNQUFNLFdBQUFBLE9BQUNDLEdBQUcsRUFBRUMsTUFBTSxFQUFFQyxLQUFLLEVBQUU7TUFDekIsb0JBQU9sQyxtQkFBQTtRQUFBbUMsUUFBQSxFQUFNRCxLQUFLLEdBQUc7TUFBQyxDQUFNLENBQUM7SUFDL0IsQ0FBQztJQUVERSxLQUFLLEVBQUU7RUFDVCxDQUFDLEVBQ0Q7SUFDRVIsS0FBSyxlQUFFNUIsbUJBQUEsQ0FBQ1Qsd0NBQWdCO01BQUNzQyxFQUFFLEVBQUM7SUFBcUQsQ0FBRSxDQUFDO0lBQ3BGQyxTQUFTLEVBQUUsWUFBWTtJQUN2Qk0sS0FBSyxFQUFFLEVBQUU7SUFDVEwsTUFBTSxXQUFBQSxPQUFDTSxLQUFLLEVBQUVDLE1BQU0sRUFBRUosS0FBSyxFQUFFO01BQzNCLG9CQUNFbEMsbUJBQUE7UUFDRXVDLEtBQUssRUFBRTtVQUNMQyxVQUFVLEVBQUU7UUFDZCxDQUFFO1FBQUFMLFFBQUEsRUFFREcsTUFBTSxDQUFDRztNQUFVLENBQ2YsQ0FBQztJQUVWO0VBQ0YsQ0FBQyxFQUNEO0lBQ0ViLEtBQUssZUFBRTVCLG1CQUFBLENBQUNULHdDQUFnQjtNQUFDc0MsRUFBRSxFQUFDO0lBQWEsQ0FBRSxDQUFDO0lBQzVDQyxTQUFTLEVBQUUsVUFBVTtJQUNyQk0sS0FBSyxFQUFFO0VBQ1QsQ0FBQyxFQUNEO0lBQ0VSLEtBQUssZUFBRTVCLG1CQUFBLENBQUNULHdDQUFnQjtNQUFDc0MsRUFBRSxFQUFDO0lBQWlCLENBQUUsQ0FBQztJQUNoREMsU0FBUyxFQUFFLFdBQVc7SUFDdEJNLEtBQUssRUFBRSxFQUFFO0lBQ1RMLE1BQU0sV0FBQUEsT0FBQ0MsR0FBRyxFQUFFQyxNQUFNLEVBQUVDLEtBQUssRUFBRVEsTUFBTSxFQUFFQyxNQUFNLEVBQUU7TUFDekMsb0JBQU8zQyxtQkFBQTtRQUFBbUMsUUFBQSxFQUFNbEQsK0JBQWEsQ0FBQ2dELE1BQU0sQ0FBQ1csU0FBUztNQUFDLENBQU0sQ0FBQztJQUNyRDtFQUNGLENBQUMsQ0FDRjtFQUNEaEQsbUJBQVMsQ0FBQyxZQUFNO0lBQ2QwQixHQUFHLENBQUM7TUFBRS9ELElBQUksRUFBSkE7SUFBSyxDQUFDLENBQUM7RUFDZixDQUFDLEVBQUUsQ0FBQ0EsSUFBSSxDQUFDLENBQUM7RUFDVnFDLG1CQUFTLENBQUMsWUFBTTtJQUNkYyxJQUFJLENBQUNtQyxXQUFXLENBQUMsQ0FBQztJQUNsQm5DLElBQUksQ0FBQ29DLGNBQWMsQ0FBQ3BGLElBQUksQ0FBQztJQUV6QmdELElBQUksQ0FBQ3FDLGFBQWEsQ0FBQyxNQUFNLEtBQUFqSCxNQUFBLENBQUs0QixJQUFJLGFBQUpBLElBQUksdUJBQUpBLElBQUksQ0FBRXNGLGVBQWUsT0FBQWxILE1BQUEsQ0FBSTRCLElBQUksYUFBSkEsSUFBSSx1QkFBSkEsSUFBSSxDQUFFdUYsY0FBYyxDQUFFLENBQUM7SUFDOUVuQyxRQUFRLENBQUMsQ0FBQXBELElBQUksYUFBSkEsSUFBSSx1QkFBSkEsSUFBSSxDQUFFbUQsS0FBSyxLQUFJLEVBQUUsQ0FBQztFQUM3QixDQUFDLEVBQUUsQ0FBQ25ELElBQUksQ0FBQyxDQUFDO0VBRVYsSUFBQXdGLFFBQUEsR0FBMEIxRCxtQ0FBTyxDQUFDLENBQUM7SUFBM0IyRCxhQUFhLEdBQUFELFFBQUEsQ0FBYkMsYUFBYTtFQUNyQixvQkFDRWpELG9CQUFBLENBQUNSLG9CQUFLO0lBQ0owRCxJQUFJLEVBQUVoRCxXQUFZO0lBQ2xCd0IsS0FBSyxlQUFFNUIsbUJBQUEsQ0FBQ1Qsd0NBQWdCO01BQUNzQyxFQUFFLEVBQUU7SUFBb0IsQ0FBRSxDQUFFO0lBQ3JEd0IsUUFBUSxFQUFFLFNBQUFBLFNBQUEsRUFBTTtNQUNkaEQsY0FBYyxhQUFkQSxjQUFjLGVBQWRBLGNBQWMsQ0FBRyxLQUFLLENBQUM7TUFDdkJrQixZQUFZLENBQUMsQ0FBQztJQUNoQixDQUFFO0lBQ0YrQixNQUFNLEVBQUUsRUFBRztJQUNYbEIsS0FBSyxFQUFFLEdBQUk7SUFBQUQsUUFBQSxnQkFFWGpDLG9CQUFBLENBQUNoQixzQkFBTztNQUFDcUUsU0FBUyxFQUFFLEtBQU07TUFBQ0MsUUFBUTtNQUFDOUMsSUFBSSxFQUFFQSxJQUFLO01BQUMrQyxNQUFNLEVBQUMsVUFBVTtNQUFDQyxJQUFJLEVBQUUsSUFBSztNQUFBdkIsUUFBQSxnQkFVM0VuQyxtQkFBQSxDQUFDWixtQkFBVztRQUNWdUUsS0FBSyxlQUFFM0QsbUJBQUEsQ0FBQ1Qsd0NBQWdCO1VBQUNzQyxFQUFFLEVBQUU7UUFBOEIsQ0FBRSxDQUFFO1FBQy9EK0IsUUFBUSxFQUFFO1VBQ1JDLElBQUksRUFBRTtRQUNSLENBQUU7UUFDRnRHLElBQUksRUFBRSxZQUFhO1FBQ25CNkUsS0FBSyxFQUFDO01BQUksQ0FDWCxDQUFDLGVBQ0ZwQyxtQkFBQSxDQUFDWixtQkFBVztRQUNWdUUsS0FBSyxlQUFFM0QsbUJBQUEsQ0FBQ1Qsd0NBQWdCO1VBQUNzQyxFQUFFLEVBQUU7UUFBdUMsQ0FBRSxDQUFFO1FBQ3hFK0IsUUFBUSxFQUFFO1VBQ1JDLElBQUksRUFBRTtRQUNSLENBQUU7UUFDRnRHLElBQUksRUFBRSxVQUFXO1FBQ2pCNkUsS0FBSyxFQUFDO01BQUksQ0FDWCxDQUFDLGVBQ0ZwQyxtQkFBQSxDQUFDWixtQkFBVztRQUNWdUUsS0FBSyxlQUFFM0QsbUJBQUEsQ0FBQ1Qsd0NBQWdCO1VBQUNzQyxFQUFFLEVBQUU7UUFBYyxDQUFFLENBQUU7UUFDL0MrQixRQUFRLEVBQUU7VUFDUkMsSUFBSSxFQUFFO1FBQ1IsQ0FBRTtRQUNGdEcsSUFBSSxFQUFFLFVBQVc7UUFDakI2RSxLQUFLLEVBQUM7TUFBSSxDQUNYLENBQUMsZUFDRnBDLG1CQUFBLENBQUNiLHlCQUFpQjtRQUNoQndFLEtBQUssZUFBRTNELG1CQUFBLENBQUNULHdDQUFnQjtVQUFDc0MsRUFBRSxFQUFFO1FBQWtCLENBQUUsQ0FBRTtRQUNuRCtCLFFBQVEsRUFBRTtVQUNSQyxJQUFJLEVBQUU7UUFDUixDQUFFO1FBQ0Z0RyxJQUFJLEVBQUUsVUFBVztRQUNqQjZFLEtBQUssRUFBQyxJQUFJO1FBQ1YwQixVQUFVLEVBQUU7VUFDVkMsTUFBTSxFQUFFL0Usa0RBQWdDQTtRQUMxQztNQUFFLENBQ0gsQ0FBQyxlQUNGZ0IsbUJBQUEsQ0FBQ1osbUJBQVc7UUFDVnVFLEtBQUssZUFBRTNELG1CQUFBLENBQUNULHdDQUFnQjtVQUFDc0MsRUFBRSxFQUFFO1FBQWlCLENBQUUsQ0FBRTtRQUNsRCtCLFFBQVEsRUFBRTtVQUNSQyxJQUFJLEVBQUU7UUFDUixDQUFFO1FBQ0Z0RyxJQUFJLEVBQUUsTUFBTztRQUNiNkUsS0FBSyxFQUFDO01BQUksQ0FDWCxDQUFDLGVBQ0ZwQyxtQkFBQSxDQUFDWCx1QkFBZTtRQUNkc0UsS0FBSyxlQUFFM0QsbUJBQUEsQ0FBQ1Qsd0NBQWdCO1VBQUNzQyxFQUFFLEVBQUU7UUFBcUIsQ0FBRSxDQUFFO1FBQ3REdEUsSUFBSSxFQUFFLGlCQUFrQjtRQUN4QnFHLFFBQVEsRUFBRTtVQUNSQyxJQUFJLEVBQUU7UUFDUixDQUFFO1FBQ0ZDLFVBQVUsRUFBRTtVQUNWRSxRQUFRLEVBQUU7WUFBRUMsT0FBTyxFQUFFLENBQUM7WUFBRUMsT0FBTyxFQUFFO1VBQUU7UUFDckM7TUFBRSxDQUNILENBQUM7SUFBQSxDQUNLLENBQUMsZUFDVmxFLG1CQUFBLFNBQVEsQ0FBQyxlQUNUQSxtQkFBQSxDQUFDVixvQkFBUTtNQUNQcUMsT0FBTyxFQUFFQSxPQUFRO01BQ2pCd0MsWUFBWTtNQUNaQyxJQUFJLEVBQUMsT0FBTztNQUNaQyxVQUFVLEVBQUV4RCxLQUFNO01BQ2xCeUQsTUFBTSxFQUFFLE1BQU87TUFDZkMsTUFBTSxFQUFFO0lBQU0sQ0FDZixDQUFDO0VBQUEsQ0FDRyxDQUFDO0FBRVosQ0FBQztBQUVELHlEQUFlcEUsU0FBUyxFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQ2xMZ0U7QUFDckM7QUFNZjtBQUNVO0FBQ0g7QUFDUztBQUFBO0FBaUJwRCxJQUFNMkUsbUJBQW1CLEdBQUcsU0FBdEJBLG1CQUFtQkEsQ0FBQTlJLElBQUEsRUFBaUM7RUFBQSxJQUEzQjBCLElBQUksR0FBQTFCLElBQUEsQ0FBSjBCLElBQUk7SUFBRXFILE9BQU8sR0FBQS9JLElBQUEsQ0FBUCtJLE9BQU87RUFDMUMsSUFBTXpFLFNBQVMsR0FBR1QsZ0JBQU0sQ0FBYSxDQUFDO0VBQ3RDLElBQU1tRixnQkFBZ0IsR0FBRyxTQUFuQkEsZ0JBQWdCQSxDQUFJOUMsS0FBVSxFQUFLO0lBQ3ZDLElBQU0rQyxPQUFPLEdBQUd2SCxJQUFJLENBQUN3SCxNQUFNLENBQUMsVUFBQ0MsSUFBSTtNQUFBLE9BQUtBLElBQUksQ0FBQ0MsR0FBRyxLQUFLbEQsS0FBSztJQUFBLEVBQUM7SUFDekQ2QyxPQUFPLENBQUNFLE9BQU8sQ0FBQztFQUNsQixDQUFDO0VBQ0QsSUFBQXRFLFNBQUEsR0FBMkNiLGtCQUFRLENBQWM7TUFBQSxPQUMvRHBDLElBQUksQ0FBQzJILEdBQUcsQ0FBQyxVQUFDRixJQUFJO1FBQUEsT0FBS0EsSUFBSSxDQUFDQyxHQUFHO01BQUEsRUFBQztJQUFBLENBQzlCLENBQUM7SUFBQXhFLFVBQUEsR0FBQUgsdUJBQUEsQ0FBQUUsU0FBQTtJQUZNMkUsWUFBWSxHQUFBMUUsVUFBQTtJQUFFMkUsa0JBQWtCLEdBQUEzRSxVQUFBO0VBR3ZDO0VBQ0E7RUFDQTtFQUNBLElBQU00RSxnQkFBZ0IsR0FBRyxTQUFuQkEsZ0JBQWdCQSxDQUFJdEQsS0FBVSxFQUFFRyxLQUFhLEVBQUs7SUFBQSxJQUFBYixrQkFBQTtJQUN0RCxJQUFNeUQsT0FBTyxHQUFHUSxlQUFlLENBQUMvSCxJQUFJLENBQUM7SUFDckMsSUFBTWdJLFdBQVcsR0FBR1QsT0FBTyxDQUFDVSxTQUFTLENBQUMsVUFBQ1IsSUFBSTtNQUFBLE9BQUtBLElBQUksQ0FBQ0MsR0FBRyxLQUFLbEQsS0FBSztJQUFBLEVBQUM7SUFFbkUrQyxPQUFPLENBQUNTLFdBQVcsQ0FBQyxDQUFDRSxpQkFBaUIsR0FBR3ZELEtBQUs7SUFDOUMwQyxPQUFPLENBQUNFLE9BQU8sQ0FBQztJQUNoQixDQUFBekQsa0JBQUEsR0FBQWxCLFNBQVMsQ0FBQ21CLE9BQU8sY0FBQUQsa0JBQUEsZUFBakJBLGtCQUFBLENBQW1CRSxNQUFNLENBQUMsQ0FBQztFQUM3QixDQUFDO0VBQ0QsSUFBTUMsT0FBcUMsR0FBRyxDQUM1QztJQUNFQyxLQUFLLGVBQUU1QixtQkFBQSxDQUFDVCx3Q0FBZ0I7TUFBQ3NDLEVBQUUsRUFBRTtJQUFlLENBQUUsQ0FBQztJQUMvQ0MsU0FBUyxFQUFFLE9BQU87SUFDbEIrRCxRQUFRLEVBQUUsS0FBSztJQUNmOUQsTUFBTSxXQUFBQSxPQUFDQyxHQUFHLEVBQUVDLE1BQU0sRUFBRUMsS0FBSyxFQUFFUSxNQUFNLEVBQUVDLE1BQU0sRUFBRTtNQUN6QyxvQkFBTzNDLG1CQUFBO1FBQUFtQyxRQUFBLEVBQU1ELEtBQUssR0FBRztNQUFDLENBQU0sQ0FBQztJQUMvQixDQUFDO0lBQ0RFLEtBQUssRUFBRTtFQUNULENBQUMsRUFDRDtJQUNFUixLQUFLLGVBQUU1QixtQkFBQSxDQUFDVCx3Q0FBZ0I7TUFBQ3NDLEVBQUUsRUFBRTtJQUFnRCxDQUFFLENBQUM7SUFDaEZDLFNBQVMsRUFBRSxXQUFXO0lBQ3RCK0QsUUFBUSxFQUFFLEtBQUs7SUFDZnpELEtBQUssRUFBRTtFQUNULENBQUMsRUFDRDtJQUNFUixLQUFLLGVBQ0g1QixtQkFBQSxDQUFDVCx3Q0FBZ0I7TUFDZnNDLEVBQUUsRUFBQywrQ0FBK0M7TUFDbERpRSxjQUFjLEVBQUM7SUFBUyxDQUN6QixDQUNGO0lBQ0RoRSxTQUFTLEVBQUUsWUFBWTtJQUN2QitELFFBQVEsRUFBRSxLQUFLO0lBQ2Z6RCxLQUFLLEVBQUU7RUFDVCxDQUFDLEVBRUQ7SUFDRVIsS0FBSyxlQUNINUIsbUJBQUEsQ0FBQ1Qsd0NBQWdCO01BQ2ZzQyxFQUFFLEVBQUMsOENBQThDO01BQ2pEaUUsY0FBYyxFQUFDO0lBQVMsQ0FDekIsQ0FDRjtJQUNEaEUsU0FBUyxFQUFFLEtBQUs7SUFDaEJ5QyxNQUFNLEVBQUUsS0FBSztJQUNid0IsY0FBYyxXQUFBQSxlQUFDcEQsTUFBTSxFQUFFcUQsTUFBTSxFQUFFdEYsSUFBSSxFQUFFZ0MsTUFBTSxFQUFFO01BQzNDLG9CQUNFMUMsbUJBQUEsQ0FBQzZFLDJCQUFXO1FBQ1Z0QyxLQUFLLEVBQUU7VUFBRUgsS0FBSyxFQUFFO1FBQU8sQ0FBRTtRQUN6QjZELFNBQVMsRUFBRSxTQUFBQSxVQUFDNUQsS0FBSztVQUFBLE9BQUssR0FBQXZHLE1BQUEsQ0FBR3VHLEtBQUssRUFBRzZELE9BQU8sQ0FBQyx1QkFBdUIsRUFBRSxHQUFHLENBQUM7UUFBQSxDQUFDO1FBQ3ZFQyxNQUFNLEVBQUUsU0FBQUEsT0FBQzlELEtBQUs7VUFBQSxPQUFLQSxLQUFLLENBQUU2RCxPQUFPLENBQUMsYUFBYSxFQUFFLEVBQUUsQ0FBQztRQUFBLENBQUM7UUFDckRFLElBQUksRUFBQztNQUFNLENBQ1osQ0FBQztJQUVOLENBQUM7SUFDRGhFLEtBQUssRUFBRTtFQUNULENBQUMsRUFDRDtJQUNFUixLQUFLLGVBQUU1QixtQkFBQSxDQUFDVCx3Q0FBZ0I7TUFBQ3NDLEVBQUUsRUFBQztJQUFhLENBQUUsQ0FBQztJQUM1Q2dFLFFBQVEsRUFBRSxLQUFLO0lBQ2YvRCxTQUFTLEVBQUUsbUJBQW1CO0lBQzlCQyxNQUFNLEVBQUUsU0FBQUEsT0FBQ0MsR0FBRyxFQUFFQyxNQUFNLEVBQUVDLEtBQUssRUFBRVEsTUFBTSxFQUFFQyxNQUFNO01BQUEsT0FBSyxjQUM5QzNDLG1CQUFBLENBQUMyRSxxQkFBYTtRQUNaMEIsYUFBYSxFQUFFO1VBQ2I5RCxLQUFLLEVBQUU7WUFDTCtELFlBQVksRUFBRTtVQUNoQjtRQUNGLENBQUU7UUFFRnhDLFVBQVUsRUFBRTtVQUNWeUMsWUFBWSxFQUFFdEUsTUFBTSxDQUFDdUU7UUFDdkIsQ0FBRTtRQUNGQyxXQUFXLEVBQUMseUJBQVU7UUFDdEJsTCxPQUFPLGVBQUFVLDBCQUFBLGVBQUFDLDRCQUFBLEdBQUFDLElBQUEsQ0FBRSxTQUFBQyxRQUFBO1VBQUEsSUFBQXNLLGNBQUE7VUFBQSxJQUFBcEssR0FBQSxFQUFBcUssYUFBQSxFQUFBQyxPQUFBO1VBQUEsT0FBQTFLLDRCQUFBLEdBQUFLLElBQUEsVUFBQUMsU0FBQUMsUUFBQTtZQUFBLGtCQUFBQSxRQUFBLENBQUFDLElBQUEsR0FBQUQsUUFBQSxDQUFBRSxJQUFBO2NBQUE7Z0JBQUFGLFFBQUEsQ0FBQUUsSUFBQTtnQkFBQSxPQUVFNkgsZ0RBQXVCLENBQUM7a0JBQzdCakgsSUFBSSxFQUFFMEUsTUFBTSxDQUFDNEUsU0FBUyxJQUFJO2dCQUM1QixDQUFDLENBQUM7Y0FBQTtnQkFBQXBLLFFBQUEsQ0FBQW9CLEVBQUEsR0FBQXBCLFFBQUEsQ0FBQUcsSUFBQTtnQkFBQSxJQUFBSCxRQUFBLENBQUFvQixFQUFBO2tCQUFBcEIsUUFBQSxDQUFBRSxJQUFBO2tCQUFBO2dCQUFBO2dCQUFBRixRQUFBLENBQUFvQixFQUFBLEdBQUssRUFBRTtjQUFBO2dCQUhMdkIsR0FBRyxHQUFBRyxRQUFBLENBQUFvQixFQUFBO2dCQUlIOEksYUFBYSxHQUNqQixFQUFBRCxjQUFBLEdBQUFwSyxHQUFHLENBQUNvQixJQUFJLENBQUNvSixJQUFJLGNBQUFKLGNBQUEsdUJBQWJBLGNBQUEsQ0FBZXJCLEdBQUcsQ0FBQyxVQUFDRixJQUFJO2tCQUFBLE9BQU07b0JBQzVCeEIsS0FBSyxFQUFFd0IsSUFBSSxDQUFDNEIsUUFBUTtvQkFDcEIxRSxLQUFLLEVBQUU4QyxJQUFJLENBQUNTO2tCQUNkLENBQUM7Z0JBQUEsQ0FBQyxDQUFDLEtBQUksRUFBRTtnQkFDTGdCLE9BQU8sR0FBRyxDQUNkO2tCQUNFakQsS0FBSyxFQUFFMUIsTUFBTSxDQUFDdUUsU0FBUztrQkFDdkJuRSxLQUFLLEVBQUU7Z0JBQ1QsQ0FBQyxDQUNGO2dCQUFBLE9BQUE1RixRQUFBLENBQUFJLE1BQUEsY0FBQWYsTUFBQSxDQUNVOEssT0FBTyxFQUFBSSwyQkFBQSxDQUFLTCxhQUFhO2NBQUE7Y0FBQTtnQkFBQSxPQUFBbEssUUFBQSxDQUFBTSxJQUFBO1lBQUE7VUFBQSxHQUFBWCxPQUFBO1FBQUEsQ0FDckMsRUFBQztRQUNGNkssUUFBUSxFQUFFLFNBQUFBLFNBQUNDLENBQUM7VUFBQSxPQUFLMUIsZ0JBQWdCLENBQUN2RCxNQUFNLENBQUNtRCxHQUFHLEVBQUUsQ0FBQzhCLENBQUMsQ0FBQztRQUFBO01BQUMsR0F2QjdDLG1CQXdCTixDQUFDLENBQ0g7SUFBQTtJQUNEOUUsS0FBSyxFQUFFO0VBQ1QsQ0FBQztFQUVEO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7RUFDQTtJQUNFUixLQUFLLEVBQUUsUUFBUTtJQUNmRSxTQUFTLEVBQUUsS0FBSztJQUNoQitELFFBQVEsRUFBRSxLQUFLO0lBQ2Y5RCxNQUFNLEVBQUUsU0FBQUEsT0FBQ0csS0FBSyxFQUFFSSxNQUFNLEVBQUU2RSxDQUFDLEVBQUV6RSxNQUFNO01BQUEsT0FBSyxjQUNwQzFDLG1CQUFBLENBQUM0RSx5QkFBTTtRQUFDd0MsSUFBSSxlQUFFcEgsbUJBQUEsQ0FBQ3lFLDZCQUFjLElBQUUsQ0FBRTtRQUFjNEMsT0FBTyxFQUFFLFNBQUFBLFFBQUE7VUFBQSxPQUFNckMsZ0JBQWdCLENBQUM5QyxLQUFLLENBQUM7UUFBQTtNQUFDLEdBQWhELFFBQWtELENBQUMsQ0FDMUY7SUFBQTtJQUNERSxLQUFLLEVBQUU7RUFDVCxDQUFDLENBQ0Y7RUFDRHhDLG1CQUFTLENBQUMsWUFBTTtJQUNkMkYsa0JBQWtCLENBQUM3SCxJQUFJLENBQUMySCxHQUFHLENBQUMsVUFBQ0YsSUFBSTtNQUFBLE9BQUtBLElBQUksQ0FBQ0MsR0FBRztJQUFBLEVBQUMsQ0FBQztFQUNsRCxDQUFDLEVBQUUsQ0FBQzFILElBQUksQ0FBQyxDQUFDO0VBQ1ZrQyxtQkFBUyxDQUFDLFlBQU07SUFBQSxJQUFBMEgsbUJBQUE7SUFDZCxDQUFBQSxtQkFBQSxHQUFBaEgsU0FBUyxDQUFDbUIsT0FBTyxjQUFBNkYsbUJBQUEsZUFBakJBLG1CQUFBLENBQW1CNUYsTUFBTSxDQUFDLENBQUM7RUFDN0IsQ0FBQyxFQUFFLENBQUNoRSxJQUFJLEVBQUU0SCxZQUFZLENBQUMsQ0FBQztFQUN4QixvQkFDRXRGLG1CQUFBLENBQUMwRSw0QkFBZ0I7SUFDZm5DLEtBQUssRUFBRTtNQUFFZ0YsUUFBUSxFQUFFO0lBQU8sQ0FBRTtJQUM1QjVGLE9BQU8sRUFBRUEsT0FBUTtJQUNqQnJCLFNBQVMsRUFBRUEsU0FBVTtJQUNyQjZELFlBQVk7SUFDWjVJLE9BQU8sZUFBQVUsMEJBQUEsZUFBQUMsNEJBQUEsR0FBQUMsSUFBQSxDQUFFLFNBQUFrQixTQUFBO01BQUEsSUFBQWhCLE1BQUE7UUFBQW1MLElBQUE7UUFBQXRDLE1BQUE7UUFBQXVDLE1BQUEsR0FBQXZLLFNBQUE7TUFBQSxPQUFBaEIsNEJBQUEsR0FBQUssSUFBQSxVQUFBaUIsVUFBQUMsU0FBQTtRQUFBLGtCQUFBQSxTQUFBLENBQUFmLElBQUEsR0FBQWUsU0FBQSxDQUFBZCxJQUFBO1VBQUE7WUFBT04sTUFBTSxHQUFBb0wsTUFBQSxDQUFBQyxNQUFBLFFBQUFELE1BQUEsUUFBQUUsU0FBQSxHQUFBRixNQUFBLE1BQUcsQ0FBQyxDQUFDO1lBQUVELElBQUksR0FBQUMsTUFBQSxDQUFBQyxNQUFBLE9BQUFELE1BQUEsTUFBQUUsU0FBQTtZQUFFekMsTUFBTSxHQUFBdUMsTUFBQSxDQUFBQyxNQUFBLE9BQUFELE1BQUEsTUFBQUUsU0FBQTtZQUFBLE9BQUFsSyxTQUFBLENBQUFaLE1BQUEsV0FDaEM7Y0FDTGEsSUFBSSxFQUFKQSxJQUFJO2NBQ0prSyxPQUFPLEVBQUUsSUFBSTtjQUNiQyxLQUFLLEVBQUVuSyxJQUFJLENBQUNnSztZQUNkLENBQUM7VUFBQTtVQUFBO1lBQUEsT0FBQWpLLFNBQUEsQ0FBQVYsSUFBQTtRQUFBO01BQUEsR0FBQU0sUUFBQTtJQUFBLENBQ0YsRUFBQztJQUNGd0ksUUFBUSxFQUFFO01BQ1JpQyxJQUFJLEVBQUUsVUFBVTtNQUNoQnhDLFlBQVksRUFBWkEsWUFBWTtNQUNaeUMsWUFBWSxFQUFFLFNBQUFBLGFBQUNDLEdBQUcsRUFBRWhDLE1BQU0sRUFBRWlDLFdBQVcsRUFBSztRQUMxQyxPQUFPLENBQUNBLFdBQVcsVUFBTyxDQUFDO01BQzdCLENBQUM7TUFDREMsY0FBYyxFQUFFLFNBQUFBLGVBQUM1RixNQUFNLEVBQUU2RixVQUFVLEVBQUs7UUFDdEM7UUFDQXBELE9BQU8sQ0FBQ29ELFVBQVUsQ0FBQztNQUNyQixDQUFDO01BQ0RsQixRQUFRLEVBQUUxQjtJQUNaLENBQUU7SUFDRjZDLGtCQUFrQixFQUFFLEtBQU07SUFDMUI5RCxNQUFNLEVBQUMsS0FBSztJQUNaQyxNQUFNLEVBQUUsS0FBTTtJQUNkcUMsT0FBTyxFQUFFO01BQ1B5QixPQUFPLEVBQUU7UUFDUEMsV0FBVyxFQUFFO01BQ2Y7SUFDRixDQUFFO0lBQ0ZDLFVBQVUsRUFBRTtNQUNWQyxlQUFlLEVBQUUsRUFBRTtNQUNuQkMsZUFBZSxFQUFFLElBQUk7TUFDckJDLGVBQWUsRUFBRSxDQUFDLElBQUksRUFBRSxJQUFJLEVBQUUsS0FBSztJQUNyQyxDQUFFO0lBQ0ZDLE9BQU8sRUFBRTtNQUNQQyxZQUFZLEVBQUU7SUFDaEIsQ0FBRTtJQUNGQyxhQUFhLEVBQUUsS0FBTTtJQUNyQnpFLElBQUksRUFBQyxPQUFPO0lBQ1owRSxhQUFhLEVBQUM7RUFBUSxDQUN2QixDQUFDO0FBRU4sQ0FBQztBQUVELG1FQUFlaEUsbUJBQW1CLEU7Ozs7Ozs7O0FDL04wQztBQUNGO0FBQ3VDO0FBQ2xEO0FBQ3lCO0FBQ2xCO0FBRXJCO0FBT2I7QUFDNkI7QUFDUTtBQUM3QjtBQUNoQjtBQUNnQjtBQUNnQztBQUFBO0FBQUE7QUFDNUUsSUFBUWlGLEtBQUssR0FBS0YseUJBQVUsQ0FBcEJFLEtBQUs7QUFvQmIsSUFBTUMsY0FBYyxHQUFHLFNBQWpCQSxjQUFjQSxDQUFBaE8sSUFBQSxFQUFnRTtFQUFBLElBQTFEbUYsU0FBUyxHQUFBbkYsSUFBQSxDQUFUbUYsU0FBUztJQUFFOEksY0FBYyxHQUFBak8sSUFBQSxDQUFkaU8sY0FBYztJQUFFQyxTQUFTLEdBQUFsTyxJQUFBLENBQVRrTyxTQUFTO0lBQUVDLE1BQU0sR0FBQW5PLElBQUEsQ0FBTm1PLE1BQU07RUFDcEUsSUFBQXhKLFNBQUEsR0FBb0NiLGtCQUFRLENBQVUsS0FBSyxDQUFDO0lBQUFjLFVBQUEsR0FBQUgsdUJBQUEsQ0FBQUUsU0FBQTtJQUFyRHlKLFVBQVUsR0FBQXhKLFVBQUE7SUFBRXlKLGFBQWEsR0FBQXpKLFVBQUE7RUFDaEMsSUFBQTBKLFVBQUEsR0FBZ0N4SyxrQkFBUSxDQUFjLENBQUM7SUFBQXlLLFVBQUEsR0FBQTlKLHVCQUFBLENBQUE2SixVQUFBO0lBQWhERSxRQUFRLEdBQUFELFVBQUE7SUFBRUUsV0FBVyxHQUFBRixVQUFBO0VBQzVCLElBQUFHLFVBQUEsR0FBMEM1SyxrQkFBUSxDQUFtQixFQUFFLENBQUM7SUFBQTZLLFVBQUEsR0FBQWxLLHVCQUFBLENBQUFpSyxVQUFBO0lBQWpFRSxhQUFhLEdBQUFELFVBQUE7SUFBRUUsZ0JBQWdCLEdBQUFGLFVBQUE7RUFDdEMsSUFBQUcsVUFBQSxHQUEwQmhMLGtCQUFRLENBQWUsRUFBRSxDQUFDO0lBQUFpTCxVQUFBLEdBQUF0Syx1QkFBQSxDQUFBcUssVUFBQTtJQUE3Q2pLLEtBQUssR0FBQWtLLFVBQUE7SUFBRWpLLFFBQVEsR0FBQWlLLFVBQUE7RUFDdEIsSUFBQUMsVUFBQSxHQUFzQ2xMLGtCQUFRLENBQWEsQ0FBQztJQUFBbUwsV0FBQSxHQUFBeEssdUJBQUEsQ0FBQXVLLFVBQUE7SUFBckRFLFdBQVcsR0FBQUQsV0FBQTtJQUFFRSxjQUFjLEdBQUFGLFdBQUE7RUFDbEMsSUFBQTFLLFFBQUEsR0FBZVosMEJBQU8sQ0FBQyxDQUFDO0lBQUFhLFNBQUEsR0FBQUMsdUJBQUEsQ0FBQUYsUUFBQTtJQUFqQkcsSUFBSSxHQUFBRixTQUFBO0VBQ1g7RUFDQSxJQUFBNEssU0FBQSxHQUF5QjdCLG9DQUFRLGlCQUFpQixDQUFDO0lBQTNDOEIsWUFBWSxHQUFBRCxTQUFBLENBQVpDLFlBQVk7RUFDcEIsSUFBTUMsVUFBVSxHQUFHRCxZQUFZLGFBQVpBLFlBQVksdUJBQVpBLFlBQVksQ0FBRUUsV0FBVztFQUM1QyxJQUFBQyxXQUFBLEdBQW9CaEMsa0JBQUcsQ0FBQ2lDLE1BQU0sQ0FBQyxDQUFDO0lBQXhCdkssT0FBTyxHQUFBc0ssV0FBQSxDQUFQdEssT0FBTzs7RUFFZjtFQUNBLElBQU13SyxlQUFlO0lBQUEsSUFBQXBPLEtBQUEsR0FBQXJCLDBCQUFBLGVBQUFDLDRCQUFBLEdBQUFDLElBQUEsQ0FBRyxTQUFBQyxRQUFBO01BQUEsSUFFYnVQLGlDQUFpQyxFQUFBak8sSUFBQSxFQUFBa08sU0FBQSxFQUFBQyxPQUFBLEVBQUFDLGFBQUE7TUFBQSxPQUFBNVAsNEJBQUEsR0FBQUssSUFBQSxVQUFBQyxTQUFBQyxRQUFBO1FBQUEsa0JBQUFBLFFBQUEsQ0FBQUMsSUFBQSxHQUFBRCxRQUFBLENBQUFFLElBQUE7VUFBQTtZQUFqQ2dQLGlDQUFpQyxZQUFBSSxzQkFDeENDLFVBQXdCLEVBQ3hCQyxlQUFrQyxFQUNtQztjQUNyRSxJQUFNQyxZQUFpRixHQUFHLENBQUMsQ0FBQztjQUU1RkYsVUFBVSxDQUFDRyxPQUFPLENBQUMsVUFBQ2hILElBQUksRUFBSztnQkFDM0IsSUFBTWlILFNBQVMsR0FBR2pILElBQUksQ0FBQ2tILFVBQVU7Z0JBQ2pDLElBQUksQ0FBQ0gsWUFBWSxDQUFDRSxTQUFTLENBQUMsRUFBRTtrQkFDNUJGLFlBQVksQ0FBQ0UsU0FBUyxDQUFDLEdBQUc7b0JBQUV2TCxLQUFLLEVBQUUsRUFBRTtvQkFBRXVMLFNBQVMsRUFBRSxDQUFDO2tCQUFxQixDQUFDO2dCQUMzRTtnQkFDQUYsWUFBWSxDQUFDRSxTQUFTLENBQUMsQ0FBQ3ZMLEtBQUssQ0FBQ3lMLElBQUksQ0FBQ25ILElBQUksQ0FBQztjQUMxQyxDQUFDLENBQUM7O2NBRUY7Y0FBQSxJQUFBb0gsU0FBQSxHQUFBQyxtQ0FBQSxDQUN3QlAsZUFBZTtnQkFBQVEsS0FBQTtjQUFBO2dCQUF2QyxLQUFBRixTQUFBLENBQUFHLENBQUEsTUFBQUQsS0FBQSxHQUFBRixTQUFBLENBQUFJLENBQUEsSUFBQUMsSUFBQSxHQUF5QztrQkFBQSxJQUE5QlIsU0FBUyxHQUFBSyxLQUFBLENBQUFwSyxLQUFBO2tCQUNsQixJQUFNd0ssYUFBYSxHQUFHVCxTQUFTLENBQUNVLGVBQWU7a0JBQy9DLElBQUlaLFlBQVksQ0FBQ1csYUFBYSxDQUFDLEVBQUU7b0JBQy9CWCxZQUFZLENBQUNXLGFBQWEsQ0FBQyxDQUFDVCxTQUFTLEdBQUdBLFNBQVM7a0JBQ25EO2dCQUNGO2NBQUMsU0FBQVcsR0FBQTtnQkFBQVIsU0FBQSxDQUFBckYsQ0FBQSxDQUFBNkYsR0FBQTtjQUFBO2dCQUFBUixTQUFBLENBQUFTLENBQUE7Y0FBQTtjQUVELE9BQU9kLFlBQVk7WUFDckIsQ0FBQyxFQXhCRDtZQUFBelAsUUFBQSxDQUFBRSxJQUFBO1lBQUEsT0F5Qm1Cd00sMkJBQVcsQ0FBQyxDQUFDLENBQUMsQ0FBQztVQUFBO1lBQTVCekwsSUFBSSxHQUFBakIsUUFBQSxDQUFBRyxJQUFBO1lBQUFILFFBQUEsQ0FBQUUsSUFBQTtZQUFBLE9BQ2N1TSxnQ0FBZ0IsQ0FBQyxDQUFDLENBQUMsQ0FBQztVQUFBO1lBQXRDMEMsU0FBUyxHQUFBblAsUUFBQSxDQUFBRyxJQUFBO1lBQ1RpUCxPQUFPLEdBQUdGLGlDQUFpQyxDQUFDak8sSUFBSSxDQUFDQSxJQUFJLEVBQUVrTyxTQUFTLENBQUNsTyxJQUFJLENBQUM7WUFDNUUsSUFBSW1PLE9BQU8sRUFBRTtjQUNMQyxhQUFhLEdBQUdtQixNQUFNLENBQUNDLE9BQU8sQ0FBQ3JCLE9BQU8sQ0FBQyxDQUFDeEcsR0FBRyxDQUFDLFVBQUFqSSxLQUFBO2dCQUFBLElBQUErUCxvQkFBQTtnQkFBQSxJQUFBblAsS0FBQSxHQUFBeUMsdUJBQUEsQ0FBQXJELEtBQUE7a0JBQUVnUCxTQUFTLEdBQUFwTyxLQUFBO2tCQUFFb1AsU0FBUyxHQUFBcFAsS0FBQTtnQkFBQSxPQUFPO2tCQUM3RTRELEtBQUssRUFBRSxFQUFBdUwsb0JBQUEsR0FBQUMsU0FBUyxDQUFDaEIsU0FBUyxjQUFBZSxvQkFBQSx1QkFBbkJBLG9CQUFBLENBQXFCeEosS0FBSyxLQUFJLEVBQUU7a0JBQ3ZDdEIsS0FBSyxFQUFFK0osU0FBUztrQkFDaEJoSCxHQUFHLEVBQUVnSCxTQUFTO2tCQUNkakssUUFBUSxFQUFFaUwsU0FBUyxDQUFDdk0sS0FBSyxDQUFDd0UsR0FBRyxDQUFDLFVBQUNGLElBQUk7b0JBQUEsT0FBTTtzQkFDdkN2RCxLQUFLLEVBQUV1RCxJQUFJLENBQUN4QixLQUFLLElBQUksRUFBRTtzQkFDdkJ0QixLQUFLLEVBQUU4QyxJQUFJLENBQUM1SCxJQUFJLElBQUksRUFBRTtzQkFDdEI2SCxHQUFHLEVBQUVELElBQUksQ0FBQzVILElBQUksSUFBSTtvQkFDcEIsQ0FBQztrQkFBQSxDQUFDO2dCQUNKLENBQUM7Y0FBQSxDQUFDLENBQUM7Y0FDSGtOLFdBQVcsQ0FBQ3FCLGFBQWEsQ0FBQztjQUMxQmhMLFFBQVEsQ0FBQ3BELElBQUksQ0FBQ0EsSUFBSSxDQUFDO1lBQ3JCO1VBQUM7VUFBQTtZQUFBLE9BQUFqQixRQUFBLENBQUFNLElBQUE7UUFBQTtNQUFBLEdBQUFYLE9BQUE7SUFBQSxDQUNGO0lBQUEsZ0JBM0NLc1AsZUFBZUEsQ0FBQTtNQUFBLE9BQUFwTyxLQUFBLENBQUFMLEtBQUEsT0FBQUMsU0FBQTtJQUFBO0VBQUEsR0EyQ3BCO0VBQ0QsSUFBTW1RLG1CQUFtQjtJQUFBLElBQUEzTyxLQUFBLEdBQUF6QywwQkFBQSxlQUFBQyw0QkFBQSxHQUFBQyxJQUFBLENBQUcsU0FBQWtCLFNBQUE7TUFBQSxPQUFBbkIsNEJBQUEsR0FBQUssSUFBQSxVQUFBaUIsVUFBQUMsU0FBQTtRQUFBLGtCQUFBQSxTQUFBLENBQUFmLElBQUEsR0FBQWUsU0FBQSxDQUFBZCxJQUFBO1VBQUE7WUFBQWMsU0FBQSxDQUFBZCxJQUFBO1lBQUEsT0FDcEIrTyxlQUFlLENBQUMsQ0FBQztVQUFBO1lBQ3ZCaEwsSUFBSSxDQUFDbUMsV0FBVyxDQUFDLENBQUMsT0FBTyxDQUFDLENBQUM7VUFBQztVQUFBO1lBQUEsT0FBQXBGLFNBQUEsQ0FBQVYsSUFBQTtRQUFBO01BQUEsR0FBQU0sUUFBQTtJQUFBLENBQzdCO0lBQUEsZ0JBSEtnUSxtQkFBbUJBLENBQUE7TUFBQSxPQUFBM08sS0FBQSxDQUFBekIsS0FBQSxPQUFBQyxTQUFBO0lBQUE7RUFBQSxHQUd4QjtFQUVELElBQU1vUSxjQUFjO0lBQUEsSUFBQUMsS0FBQSxHQUFBdFIsMEJBQUEsZUFBQUMsNEJBQUEsR0FBQUMsSUFBQSxDQUFHLFNBQUE4QixTQUFBO01BQUEsSUFBQXVQLFFBQUEsRUFBQUMsY0FBQSxFQUFBQyxlQUFBO01BQUEsT0FBQXhSLDRCQUFBLEdBQUFLLElBQUEsVUFBQTZCLFVBQUFDLFNBQUE7UUFBQSxrQkFBQUEsU0FBQSxDQUFBM0IsSUFBQSxHQUFBMkIsU0FBQSxDQUFBMUIsSUFBQTtVQUFBO1lBQ2Y2USxRQUE4QixHQUFHOU0sSUFBSSxDQUFDaU4sYUFBYSxDQUFDLE9BQU8sQ0FBQztZQUFBLElBQzdESCxRQUFRO2NBQUFuUCxTQUFBLENBQUExQixJQUFBO2NBQUE7WUFBQTtZQUNYdUUsT0FBTyxDQUFDdkYsS0FBSyx1Q0FBeUIsQ0FBQztZQUFDLE9BQUEwQyxTQUFBLENBQUF4QixNQUFBO1VBQUE7WUFHcEM0USxjQUF3RCxHQUFHNU0sS0FBSyxDQUFDcUUsTUFBTSxDQUFDLFVBQUNDLElBQUk7Y0FBQSxPQUNqRnFJLFFBQVEsQ0FBQ0ksUUFBUSxDQUFDekksSUFBSSxDQUFDNUgsSUFBSSxDQUFDO1lBQUEsQ0FDOUIsQ0FBQyxFQUVEO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNNbVEsZUFBaUMsR0FBR0QsY0FBYyxDQUFDcEksR0FBRyxDQUFDLFVBQUNGLElBQUk7Y0FBQSxPQUFNO2dCQUN0RVMsaUJBQWlCLEVBQUUsQ0FBQztnQkFDcEJpQixTQUFTLEVBQUUxQixJQUFJLENBQUM1SCxJQUFJO2dCQUNwQnNRLFNBQVMsRUFBRTFJLElBQUksQ0FBQzBJLFNBQVM7Z0JBQ3pCQyxHQUFHLEVBQUUsQ0FBQztnQkFDTnJMLFVBQVUsRUFBRTBDLElBQUksQ0FBQ3hCLEtBQUs7Z0JBQ3RCeUIsR0FBRyxFQUFFMkksSUFBSSxDQUFDQyxHQUFHLENBQUMsQ0FBQyxHQUFHLEdBQUcsR0FBR0MsSUFBSSxDQUFDQyxLQUFLLENBQUNELElBQUksQ0FBQ0UsTUFBTSxDQUFDLENBQUMsR0FBRyxHQUFHLENBQUMsQ0FBQ0MsUUFBUSxDQUFDLENBQUM7Z0JBQ2xFNUgsU0FBUyxFQUFFckIsSUFBSSxDQUFDcUI7Y0FDbEIsQ0FBQztZQUFBLENBQUMsQ0FBQztZQUNIcUUsZ0JBQWdCLENBQUMsVUFBQ25PLElBQUk7Y0FBQSxVQUFBWixNQUFBLENBQUFrTCwyQkFBQSxDQUFTMEcsZUFBZSxHQUFBMUcsMkJBQUEsQ0FBS3RLLElBQUk7WUFBQSxDQUFDLENBQUM7WUFDekRnRSxJQUFJLENBQUNxQyxhQUFhLENBQUMsT0FBTyxFQUFFLEVBQUUsQ0FBQztZQUFDLE9BQUExRSxTQUFBLENBQUF4QixNQUFBO1VBQUE7VUFBQTtZQUFBLE9BQUF3QixTQUFBLENBQUF0QixJQUFBO1FBQUE7TUFBQSxHQUFBa0IsUUFBQTtJQUFBLENBRWpDO0lBQUEsZ0JBbENLcVAsY0FBY0EsQ0FBQTtNQUFBLE9BQUFDLEtBQUEsQ0FBQXRRLEtBQUEsT0FBQUMsU0FBQTtJQUFBO0VBQUEsR0FrQ25CO0VBRUQsSUFBTW1SLHNCQUFzQjtJQUFBLElBQUFDLEtBQUEsR0FBQXJTLDBCQUFBLGVBQUFDLDRCQUFBLEdBQUFDLElBQUEsQ0FBRyxTQUFBd0MsU0FBT3VJLENBQU07TUFBQSxJQUFBL0IsSUFBQTtNQUFBLE9BQUFqSiw0QkFBQSxHQUFBSyxJQUFBLFVBQUFzQyxVQUFBQyxTQUFBO1FBQUEsa0JBQUFBLFNBQUEsQ0FBQXBDLElBQUEsR0FBQW9DLFNBQUEsQ0FBQW5DLElBQUE7VUFBQTtZQUNwQ3dJLElBQTRCLEdBQUd0RSxLQUFLLENBQUNxRSxNQUFNLENBQUMsVUFBQ0MsSUFBSTtjQUFBLE9BQUtBLElBQUksQ0FBQzVILElBQUksS0FBSzJKLENBQUM7WUFBQSxFQUFDLENBQUN0SixFQUFFLENBQUMsQ0FBQyxDQUFDO1lBQ2xGaEMsT0FBTyxDQUFDQyxHQUFHLENBQUNxTCxDQUFDLEVBQUUvQixJQUFJLENBQUM7WUFDcEJnRyxjQUFjLENBQUNoRyxJQUFJLENBQUM7WUFDcEJ6RSxJQUFJLENBQUNtQyxXQUFXLENBQUMsQ0FBQyxtQkFBbUIsQ0FBQyxDQUFDO1VBQUM7VUFBQTtZQUFBLE9BQUEvRCxTQUFBLENBQUEvQixJQUFBO1FBQUE7TUFBQSxHQUFBNEIsUUFBQTtJQUFBLENBQ3pDO0lBQUEsZ0JBTEswUCxzQkFBc0JBLENBQUFyUixFQUFBO01BQUEsT0FBQXNSLEtBQUEsQ0FBQXJSLEtBQUEsT0FBQUMsU0FBQTtJQUFBO0VBQUEsR0FLM0I7O0VBRUQ7QUFDRjtBQUNBO0VBQ0UsSUFBTXFSLGFBQWEsR0FBRyxTQUFoQkEsYUFBYUEsQ0FBSTNELGFBQStCLEVBQUs7SUFBQSxJQUFBNEQsVUFBQSxHQUFBaEMsbUNBQUEsQ0FDdEM1QixhQUFhO01BQUE2RCxNQUFBO0lBQUE7TUFBaEMsS0FBQUQsVUFBQSxDQUFBOUIsQ0FBQSxNQUFBK0IsTUFBQSxHQUFBRCxVQUFBLENBQUE3QixDQUFBLElBQUFDLElBQUEsR0FBa0M7UUFBQSxJQUF2QnpILElBQUksR0FBQXNKLE1BQUEsQ0FBQXBNLEtBQUE7UUFDYixJQUFJOEMsSUFBSSxDQUFDMkksR0FBRyxLQUFLLElBQUksRUFBRTtVQUNyQjVNLE9BQU8sQ0FBQ3ZGLEtBQUssa0dBQTRDLENBQUM7VUFDMUQsT0FBTyxLQUFLO1FBQ2Q7UUFDQSxJQUFJd0osSUFBSSxDQUFDMkksR0FBRyxJQUFJLENBQUMsRUFBRTtVQUNqQjVNLE9BQU8sQ0FBQ3ZGLEtBQUssZ0ZBQXVDLENBQUM7VUFDckQsT0FBTyxLQUFLO1FBQ2Q7TUFDRjtJQUFDLFNBQUFvUixHQUFBO01BQUF5QixVQUFBLENBQUF0SCxDQUFBLENBQUE2RixHQUFBO0lBQUE7TUFBQXlCLFVBQUEsQ0FBQXhCLENBQUE7SUFBQTtJQUNELE9BQU8sSUFBSTtFQUNiLENBQUM7RUFFRCxJQUFNMEIsZ0JBQWdCLEdBQUcsU0FBbkJBLGdCQUFnQkEsQ0FBSUMsTUFBaUIsRUFBRS9ELGFBQStCLEVBQUs7SUFDL0UsSUFBTWdFLFlBQVksR0FBRztNQUNuQkMsZUFBZSxFQUFFRixNQUFNLENBQUNFLGVBQWU7TUFDdkMxSixJQUFJLEVBQUUrRixXQUFXLGFBQVhBLFdBQVcsdUJBQVhBLFdBQVcsQ0FBRTNOLElBQUk7TUFDdkJ1UixRQUFRLEVBQUVILE1BQU0sQ0FBQ0kscUJBQXFCLEdBQUdKLE1BQU0sQ0FBQ0ssaUJBQWlCO01BRWpFbk8sS0FBSyxFQUFFK0osYUFBYSxDQUFDdkYsR0FBRyxDQUFDLFVBQUNGLElBQUk7UUFBQSxPQUFNO1VBQ2xDUyxpQkFBaUIsRUFBRVQsSUFBSSxDQUFDUyxpQkFBaUI7VUFDekNpQixTQUFTLEVBQUUxQixJQUFJLENBQUMwQixTQUFTO1VBQ3pCaUgsR0FBRyxFQUFFM0ksSUFBSSxDQUFDMkk7UUFDWixDQUFDO01BQUEsQ0FBQztJQUNKLENBQUM7SUFFRCxPQUFBdlAsdUJBQUEsQ0FBQUEsdUJBQUEsS0FDS3FRLFlBQVk7TUFDZkssU0FBUyxFQUFFLENBQUM7TUFDWkMsT0FBTyxFQUFFLEtBQUs7TUFDZEMsU0FBUyxFQUFFLENBQUM7TUFDWkMsU0FBUyxFQUFFLENBQUM7TUFDWkMsS0FBSyxFQUFFLGVBQWU7TUFDdEJDLE9BQU8sRUFBRSxNQUFNO01BQ2ZDLFNBQVMsRUFBRSxDQUFDO01BQ1pDLFVBQVUsRUFBRSxDQUFDO01BQ2JDLDBDQUEwQyxFQUFFLENBQUM7TUFDN0M1TyxLQUFLLEVBQUUrTixZQUFZLENBQUMvTixLQUFLLENBQUN3RSxHQUFHLENBQUMsVUFBQ0YsSUFBSTtRQUFBLE9BQUE1Ryx1QkFBQSxDQUFBQSx1QkFBQSxLQUM5QjRHLElBQUk7VUFDUDhKLFNBQVMsRUFBRSxDQUFDO1VBQ1pDLE9BQU8sRUFBRSxVQUFVO1VBQ25CM1IsSUFBSSxFQUFFLGNBQWMsR0FBRzBRLElBQUksQ0FBQ0UsTUFBTSxDQUFDLENBQUMsQ0FBQ0MsUUFBUSxDQUFDLEVBQUUsQ0FBQyxDQUFDc0IsU0FBUyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUM7VUFDakVQLFNBQVMsRUFBRSxDQUFDO1VBQ1pDLFNBQVMsRUFBRSxDQUFDO1VBQ1pDLEtBQUssRUFBRSxlQUFlO1VBQ3RCTSxjQUFjLEVBQUUsQ0FBQztVQUNqQkMsc0JBQXNCLEVBQUUsQ0FBQztVQUN6QkMsV0FBVyxFQUFFLE9BQU87VUFDcEJDLFVBQVUsRUFBRTtRQUFLO01BQUEsQ0FDakI7SUFBQztFQUVQLENBQUM7O0VBRUQ7RUFDQSxJQUFNQyxZQUFZO0lBQUEsSUFBQUMsS0FBQSxHQUFBL1QsMEJBQUEsZUFBQUMsNEJBQUEsR0FBQUMsSUFBQSxDQUFHLFNBQUE4VCxTQUFPdEIsTUFBaUI7TUFBQSxJQUFBdUIsVUFBQTtNQUFBLE9BQUFoVSw0QkFBQSxHQUFBSyxJQUFBLFVBQUE0VCxVQUFBQyxTQUFBO1FBQUEsa0JBQUFBLFNBQUEsQ0FBQTFULElBQUEsR0FBQTBULFNBQUEsQ0FBQXpULElBQUE7VUFBQTtZQUMzQzBOLGFBQWEsQ0FBQyxJQUFJLENBQUM7WUFBQytGLFNBQUEsQ0FBQTFULElBQUE7WUFBQSxJQUViNlIsYUFBYSxDQUFDM0QsYUFBYSxDQUFDO2NBQUF3RixTQUFBLENBQUF6VCxJQUFBO2NBQUE7WUFBQTtZQUFBLE9BQUF5VCxTQUFBLENBQUF2VCxNQUFBO1VBQUE7WUFHakNqQixPQUFPLENBQUNDLEdBQUcsQ0FBQztjQUFFOFMsTUFBTSxFQUFOQSxNQUFNO2NBQUUvRCxhQUFhLEVBQWJBO1lBQWMsQ0FBQyxDQUFDO1lBRWhDc0YsVUFBVSxHQUFHeEIsZ0JBQWdCLENBQUNDLE1BQU0sRUFBRS9ELGFBQWEsQ0FBQztZQUFBd0YsU0FBQSxDQUFBelQsSUFBQTtZQUFBLE9BQ3BEb0IsT0FBTyxDQUFDbVMsVUFBVSxDQUFDO1VBQUE7WUFFekJoUCxPQUFPLENBQUMwRyxPQUFPLFVBQVUsQ0FBQztZQUMxQnpHLFNBQVMsYUFBVEEsU0FBUyxlQUFUQSxTQUFTLENBQUcsQ0FBQztZQUNiVCxJQUFJLENBQUNtQyxXQUFXLENBQUMsQ0FBQztZQUNsQmdJLGdCQUFnQixDQUFDLEVBQUUsQ0FBQztZQUFDLE9BQUF1RixTQUFBLENBQUF2VCxNQUFBLFdBQ2QsSUFBSTtVQUFBO1lBQUF1VCxTQUFBLENBQUExVCxJQUFBO1lBQUEwVCxTQUFBLENBQUF2UyxFQUFBLEdBQUF1UyxTQUFBO1lBRVh4VSxPQUFPLENBQUNDLEdBQUcsQ0FBQztjQUFFRixLQUFLLEVBQUF5VSxTQUFBLENBQUF2UztZQUFDLENBQUMsQ0FBQztZQUN0QnFELE9BQU8sQ0FBQ3ZGLEtBQUssd0JBQXdCLENBQUM7WUFBQyxPQUFBeVUsU0FBQSxDQUFBdlQsTUFBQTtVQUFBO1lBQUF1VCxTQUFBLENBQUExVCxJQUFBO1lBR3ZDMk4sYUFBYSxDQUFDLEtBQUssQ0FBQztZQUFDLE9BQUErRixTQUFBLENBQUFDLE1BQUE7VUFBQTtVQUFBO1lBQUEsT0FBQUQsU0FBQSxDQUFBclQsSUFBQTtRQUFBO01BQUEsR0FBQWtULFFBQUE7SUFBQSxDQUV4QjtJQUFBLGdCQXZCS0YsWUFBWUEsQ0FBQWpTLEdBQUE7TUFBQSxPQUFBa1MsS0FBQSxDQUFBL1MsS0FBQSxPQUFBQyxTQUFBO0lBQUE7RUFBQSxHQXVCakI7RUFFRCxJQUFNb1QsV0FBVztJQUFBLElBQUFDLEtBQUEsR0FBQXRVLDBCQUFBLGVBQUFDLDRCQUFBLEdBQUFDLElBQUEsQ0FBRyxTQUFBcVUsU0FBQTtNQUFBLE9BQUF0VSw0QkFBQSxHQUFBSyxJQUFBLFVBQUFrVSxVQUFBQyxTQUFBO1FBQUEsa0JBQUFBLFNBQUEsQ0FBQWhVLElBQUEsR0FBQWdVLFNBQUEsQ0FBQS9ULElBQUE7VUFBQTtZQUNsQixJQUFJLENBQUNzTixjQUFjLEVBQUU7Y0FDbkJZLGdCQUFnQixDQUFDLEVBQUUsQ0FBQztZQUN0QjtVQUFDO1VBQUE7WUFBQSxPQUFBNkYsU0FBQSxDQUFBM1QsSUFBQTtRQUFBO01BQUEsR0FBQXlULFFBQUE7SUFBQSxDQUNGO0lBQUEsZ0JBSktGLFdBQVdBLENBQUE7TUFBQSxPQUFBQyxLQUFBLENBQUF0VCxLQUFBLE9BQUFDLFNBQUE7SUFBQTtFQUFBLEdBSWhCO0VBQ0QwQyxtQkFBUyxDQUFDLFlBQU07SUFDZCxJQUFNK1EsU0FBUztNQUFBLElBQUFDLE1BQUEsR0FBQTNVLDBCQUFBLGVBQUFDLDRCQUFBLEdBQUFDLElBQUEsQ0FBRyxTQUFBMFUsU0FBQTtRQUFBLE9BQUEzVSw0QkFBQSxHQUFBSyxJQUFBLFVBQUF1VSxVQUFBQyxTQUFBO1VBQUEsa0JBQUFBLFNBQUEsQ0FBQXJVLElBQUEsR0FBQXFVLFNBQUEsQ0FBQXBVLElBQUE7WUFBQTtjQUNoQitPLGVBQWUsQ0FBQyxDQUFDO1lBQUM7WUFBQTtjQUFBLE9BQUFxRixTQUFBLENBQUFoVSxJQUFBO1VBQUE7UUFBQSxHQUFBOFQsUUFBQTtNQUFBLENBQ25CO01BQUEsZ0JBRktGLFNBQVNBLENBQUE7UUFBQSxPQUFBQyxNQUFBLENBQUEzVCxLQUFBLE9BQUFDLFNBQUE7TUFBQTtJQUFBLEdBRWQ7SUFDRHlULFNBQVMsQ0FBQyxDQUFDO0VBQ2IsQ0FBQyxFQUFFLEVBQUUsQ0FBQzs7RUFFTjtFQUNBO0VBQ0E7RUFDQTtFQUNBOztFQUVBLElBQUF6TixRQUFBLEdBQTBCMUQsbUNBQU8sQ0FBQyxDQUFDO0lBQTNCMkQsYUFBYSxHQUFBRCxRQUFBLENBQWJDLGFBQWE7RUFDckIsb0JBQ0VqRCxvQkFBQSxDQUFDbUosMEJBQVM7SUFDUnpILEtBQUssZUFBRTVCLG1CQUFBLENBQUNULHdDQUFnQjtNQUFDc0MsRUFBRSxFQUFDO0lBQXlCLENBQUUsQ0FBRTtJQUN6RG1QLFVBQVUsRUFBRTtNQUNWM04sUUFBUSxFQUFFaU4sV0FBVztNQUNyQlcsY0FBYyxFQUFFO0lBQ2xCLENBQUU7SUFDRkMsT0FBTyxlQUNMaFIsb0JBQUEsQ0FBQzBFLHlCQUFNO01BQUN3QyxJQUFJLGVBQUVwSCxtQkFBQSxDQUFDb0osMkJBQVksSUFBRSxDQUFFO01BQUN0QixJQUFJLEVBQUMsU0FBUztNQUFBM0YsUUFBQSxHQUMzQyxFQUFFLGVBQ0huQyxtQkFBQSxDQUFDVCx3Q0FBZ0I7UUFBQ3NDLEVBQUUsRUFBQztNQUF5QixDQUFFLENBQUM7SUFBQSxDQUMzQyxDQUNUO0lBQ0RPLEtBQUssRUFBRSxHQUFJO0lBQ1gxQixJQUFJLEVBQUVBLElBQUs7SUFDWCtDLE1BQU0sRUFBQyxVQUFVO0lBQ2pCME4sUUFBUSxFQUFFO01BQ1JDLE1BQU0sRUFBRSxDQUFDLEVBQUUsRUFBRSxDQUFDO0lBQ2hCLENBQUU7SUFDRjdOLFNBQVMsRUFBRTtNQUNUeEIsTUFBTSxFQUFFLFNBQUFBLE9BQUNzUCxLQUFLLEVBQUVwSixXQUFXLEVBQUs7UUFDOUIsT0FBTyxjQUNMakksbUJBQUEsQ0FBQzRFLHlCQUFNO1VBRUx5QyxPQUFPLEVBQUUsU0FBQUEsUUFBQSxFQUFNO1lBQ2JnSyxLQUFLLENBQUNDLE1BQU0sQ0FBQyxDQUFDO1VBQ2hCLENBQUU7VUFDRnhKLElBQUksRUFBQyxTQUFTO1VBQ2R2RixLQUFLLEVBQUU7WUFBRUgsS0FBSyxFQUFFO1VBQU8sQ0FBRTtVQUN6QmhCLE9BQU8sRUFBRWdKLFVBQVc7VUFBQWpJLFFBQUEsRUFDckI7UUFFRCxHQVRNLElBU0UsQ0FBQyxDQUNWO01BQ0g7SUFDRixDQUFFO0lBQ0ZvUCxtQkFBbUI7SUFDbkI3TixJQUFJO0lBQ0o4TixhQUFhLEVBQUUsSUFBSztJQUNwQkMsYUFBYSxFQUFFO01BQ2JDLFlBQVksRUFBRTVILG1CQUFNLENBQUMsQ0FBQyxDQUFDL0YsTUFBTSxDQUFDZ0YsK0NBQTZCO0lBQzdELENBQUU7SUFDRjRJLFFBQVEsRUFBRTVCLFlBQWE7SUFBQTVOLFFBQUEsZ0JBRXZCbkMsbUJBQUEsQ0FBQ2dKLG1DQUFvQjtNQUNuQnpMLElBQUksRUFBRSxjQUFlO01BQ3JCdUcsVUFBVSxFQUFFO1FBQ1YwRyxRQUFRLEVBQUVBLFFBQVE7UUFFbEJ2RCxRQUFRLEVBQUUsU0FBQUEsU0FBQzVFLEtBQVUsRUFBSztVQUN4QixPQUFPLENBQUFBLEtBQUssYUFBTEEsS0FBSyx1QkFBTEEsS0FBSyxDQUFFcUYsTUFBTSxJQUFHLENBQUMsRUFBRTtZQUN4QnJGLEtBQUssQ0FBQ3VQLEtBQUssQ0FBQyxDQUFDO1VBQ2Y7VUFDQXZELHNCQUFzQixDQUFDaE0sS0FBSyxDQUFDekUsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDO1FBQ3JDO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtNQUNGLENBQUU7TUFDRitGLEtBQUssZUFBRTNELG1CQUFBLENBQUNULHdDQUFnQjtRQUFDc0MsRUFBRSxFQUFFO01BQThCLENBQUUsQ0FBRTtNQUMvRCtCLFFBQVEsRUFBRTtRQUFFQyxJQUFJLEVBQUU7TUFBRTtJQUFFLENBQ3ZCLENBQUMsZUFDRjdELG1CQUFBLENBQUNzSixvQkFBWTtNQUNYM0YsS0FBSyxlQUFFM0QsbUJBQUEsQ0FBQ1Qsd0NBQWdCO1FBQUNzQyxFQUFFLEVBQUU7TUFBdUMsQ0FBRSxDQUFFO01BQ3hFdEUsSUFBSSxFQUFDLHVCQUF1QjtNQUM1QnVHLFVBQVUsRUFBRTtRQUNWbUMsU0FBUyxFQUFFLFNBQUFBLFVBQUM1RCxLQUFLO1VBQUEsT0FBSyxHQUFBdkcsTUFBQSxDQUFHdUcsS0FBSyxFQUFHNkQsT0FBTyxDQUFDLHVCQUF1QixFQUFFLEdBQUcsQ0FBQztRQUFBO1FBQ3RFQyxNQUFNLEVBQUUsU0FBQUEsT0FBQzlELEtBQVU7VUFBQSxPQUFLQSxLQUFLLENBQUU2RCxPQUFPLENBQUMsYUFBYSxFQUFFLEVBQUUsQ0FBQztRQUFBO1FBQ3pERSxJQUFJLEVBQUU7TUFDUixDQUFFO01BQ0Z4QyxRQUFRLEVBQUU7UUFBRUMsSUFBSSxFQUFFO01BQUU7SUFBRSxDQUN2QixDQUFDLEVBQ0RxSCxXQUFXLGdCQUNWbEwsbUJBQUEsQ0FBQzJFLHFCQUFhO01BQ1pwSCxJQUFJLEVBQUUsbUJBQW9CO01BQzFCb0csS0FBSyxlQUFFM0QsbUJBQUEsQ0FBQ1Qsd0NBQWdCO1FBQUNzQyxFQUFFLEVBQUU7TUFBbUMsQ0FBRSxDQUFFO01BQ3BFd0UsYUFBYSxFQUFFO1FBQ2I5RCxLQUFLLEVBQUU7VUFDTCtELFlBQVksRUFBRTtRQUNoQjtNQUNGLENBQUU7TUFDRjlDLFFBQVEsRUFBRSxDQUFDMEgsV0FBWTtNQUV2QnpFLFdBQVcsRUFBQyx1QkFBUTtNQUNwQm9MLFFBQVE7TUFDUkMsS0FBSyxFQUFFLENBQUM7UUFBRUQsUUFBUSxFQUFFLElBQUk7UUFBRTNRLE9BQU8sZUFBRWxCLG1CQUFBLENBQUNULHdDQUFnQjtVQUFDc0MsRUFBRSxFQUFFO1FBQXdCLENBQUU7TUFBRSxDQUFDLENBQUU7TUFDeEZ0RyxPQUFPLGVBQUFVLDBCQUFBLGVBQUFDLDRCQUFBLEdBQUFDLElBQUEsQ0FBRSxTQUFBNFYsU0FBQTtRQUFBLElBQUFyTCxjQUFBO1FBQUEsSUFBQXBLLEdBQUEsRUFBQXFLLGFBQUEsRUFBQUMsT0FBQTtRQUFBLE9BQUExSyw0QkFBQSxHQUFBSyxJQUFBLFVBQUF5VixVQUFBQyxTQUFBO1VBQUEsa0JBQUFBLFNBQUEsQ0FBQXZWLElBQUEsR0FBQXVWLFNBQUEsQ0FBQXRWLElBQUE7WUFBQTtjQUFBc1YsU0FBQSxDQUFBdFYsSUFBQTtjQUFBLE9BRUU2SCxnREFBdUIsQ0FBQztnQkFDN0JqSCxJQUFJLEVBQUUsQ0FBQTJOLFdBQVcsYUFBWEEsV0FBVyx1QkFBWEEsV0FBVyxDQUFFM04sSUFBSSxLQUFJO2NBQzdCLENBQUMsQ0FBQztZQUFBO2NBQUEwVSxTQUFBLENBQUFwVSxFQUFBLEdBQUFvVSxTQUFBLENBQUFyVixJQUFBO2NBQUEsSUFBQXFWLFNBQUEsQ0FBQXBVLEVBQUE7Z0JBQUFvVSxTQUFBLENBQUF0VixJQUFBO2dCQUFBO2NBQUE7Y0FBQXNWLFNBQUEsQ0FBQXBVLEVBQUEsR0FBSyxFQUFFO1lBQUE7Y0FITHZCLEdBQUcsR0FBQTJWLFNBQUEsQ0FBQXBVLEVBQUE7Y0FJSDhJLGFBQWEsR0FDakIsRUFBQUQsY0FBQSxHQUFBcEssR0FBRyxDQUFDb0IsSUFBSSxDQUFDb0osSUFBSSxjQUFBSixjQUFBLHVCQUFiQSxjQUFBLENBQWVyQixHQUFHLENBQUMsVUFBQ0YsSUFBSTtnQkFBQSxPQUFNO2tCQUM1QnhCLEtBQUssRUFBRXdCLElBQUksQ0FBQzRCLFFBQVE7a0JBQ3BCMUUsS0FBSyxFQUFFOEMsSUFBSSxDQUFDUztnQkFDZCxDQUFDO2NBQUEsQ0FBQyxDQUFDLEtBQUksRUFBRTtjQUNMZ0IsT0FBTyxHQUFHLENBQ2Q7Z0JBQ0VqRCxLQUFLLEVBQUV1SCxXQUFXLENBQUMxRSxTQUFTO2dCQUM1Qm5FLEtBQUssRUFBRTtjQUNULENBQUMsQ0FDRjtjQUFBLE9BQUE0UCxTQUFBLENBQUFwVixNQUFBLGNBQUFmLE1BQUEsQ0FDVThLLE9BQU8sRUFBQUksMkJBQUEsQ0FBS0wsYUFBYTtZQUFBO1lBQUE7Y0FBQSxPQUFBc0wsU0FBQSxDQUFBbFYsSUFBQTtVQUFBO1FBQUEsR0FBQWdWLFFBQUE7TUFBQSxDQUNyQyxFQUFDO01BQ0ZuTyxRQUFRLEVBQUU7UUFBRUMsSUFBSSxFQUFFO01BQUU7SUFBRSxHQXRCakIsbUJBdUJOLENBQUMsZ0JBRUY3RCxtQkFBQSxDQUFDMkUscUJBQWE7TUFDWnBILElBQUksRUFBRSxtQkFBb0I7TUFDMUJvRyxLQUFLLGVBQUUzRCxtQkFBQSxDQUFDVCx3Q0FBZ0I7UUFBQ3NDLEVBQUUsRUFBRTtNQUFtQyxDQUFFLENBQUU7TUFDcEV3RSxhQUFhLEVBQUU7UUFDYjlELEtBQUssRUFBRTtVQUNMK0QsWUFBWSxFQUFFO1FBQ2hCO01BQ0YsQ0FBRTtNQUNGOUMsUUFBUSxFQUFFLENBQUMwSCxXQUFZO01BRXZCekUsV0FBVyxFQUFDO01BQ1o7TUFBQTtNQUNBN0MsUUFBUSxFQUFFO1FBQUVDLElBQUksRUFBRTtNQUFFO0lBQUUsR0FIakIsbUJBSU4sQ0FDRixlQUVEN0QsbUJBQUEsQ0FBQ1gsdUJBQWU7TUFDZDlCLElBQUksRUFBQyxpQkFBaUI7TUFDdEJvRyxLQUFLLGVBQUUzRCxtQkFBQSxDQUFDVCx3Q0FBZ0I7UUFBQ3NDLEVBQUUsRUFBRTtNQUEwQixDQUFFLENBQUU7TUFDM0QrQixRQUFRLEVBQUU7UUFBRUMsSUFBSSxFQUFFO01BQUc7TUFDckI7SUFBQSxDQUNELENBQUMsZUFDRjdELG1CQUFBLENBQUNkLHNCQUFPLENBQUNnVCxLQUFLO01BQ1p0USxLQUFLLGVBQ0g1QixtQkFBQTtRQUFLdUMsS0FBSyxFQUFFO1VBQUUrRCxZQUFZLEVBQUU7UUFBUSxDQUFFO1FBQUFuRSxRQUFBLGVBQ3BDbkMsbUJBQUEsQ0FBQ1Qsd0NBQWdCO1VBQUNzQyxFQUFFLEVBQUU7UUFBMkIsQ0FBRTtNQUFDLENBQ2pELENBQ047TUFBQU0sUUFBQSxlQUVEbkMsbUJBQUEsQ0FBQ2dKLG1DQUFvQjtRQUNuQnpMLElBQUksRUFBRSxPQUFRO1FBQ2R1RyxVQUFVLEVBQUU7VUFDVjBHLFFBQVEsRUFBRUE7UUFDWixDQUFFO1FBQ0Y3RyxLQUFLLGVBQUUzRCxtQkFBQSxDQUFDVCx3Q0FBZ0I7VUFBQ3NDLEVBQUUsRUFBRTtRQUE4QixDQUFFLENBQUU7UUFDL0RzUSxjQUFjLGVBQ1puUyxtQkFBQSxDQUFDaUosK0JBQXdCO1VBQ3ZCbUosVUFBVSxFQUFDLE1BQU07VUFDakJqUixTQUFTLEVBQUVrTTtRQUFvQixHQUMzQixRQUNMLENBQ0Y7UUFDRHpKLFFBQVEsRUFBRTtVQUFFQyxJQUFJLEVBQUU7UUFBRTtNQUFFLENBQ3ZCO0lBQUMsQ0FDVyxDQUFDLGVBRWhCN0QsbUJBQUEsQ0FBQ2Qsc0JBQU8sQ0FBQ2dULEtBQUs7TUFBQS9QLFFBQUEsZUFDWm5DLG1CQUFBLENBQUMySixrQkFBRztRQUFDcEgsS0FBSyxFQUFFO1VBQUVILEtBQUssRUFBRTtRQUFPLENBQUU7UUFBQUQsUUFBQSxlQUM1Qm5DLG1CQUFBLENBQUN5SixrQkFBRztVQUFDNUYsSUFBSSxFQUFFLEVBQUc7VUFBQTFCLFFBQUEsZUFDWm5DLG1CQUFBLENBQUM0SixvQkFBSztZQUFBekgsUUFBQSxlQUNKbkMsbUJBQUEsQ0FBQzRFLHlCQUFNO2NBQUNrRCxJQUFJLEVBQUMsU0FBUztjQUFDVixJQUFJLGVBQUVwSCxtQkFBQSxDQUFDb0osMkJBQVksSUFBRSxDQUFFO2NBQUMvQixPQUFPLEVBQUVpRyxjQUFlO2NBQUFuTCxRQUFBLGVBQ3JFbkMsbUJBQUEsQ0FBQ1Qsd0NBQWdCO2dCQUFDc0MsRUFBRSxFQUFFO2NBQTBCLENBQUU7WUFBQyxDQUM3QztVQUFDLENBVUo7UUFBQyxDQUNMO01BQUMsQ0FtQkg7SUFBQyxDQUNPLENBQUMsZUFDaEI3QixtQkFBQSxDQUFDMEosc0JBQU8sSUFBRSxDQUFDLGVBQ1gxSixtQkFBQSxDQUFDOEUsOEJBQW1CO01BQUNwSCxJQUFJLEVBQUVrTixhQUFjO01BQUM3RixPQUFPLEVBQUU4RjtJQUFpQixDQUFFLENBQUM7RUFBQSxDQUM5RCxDQUFDO0FBRWhCLENBQUM7QUFFRCw4REFBZWIsY0FBYyxFOzs7O0FDL2JvQztBQUNkO0FBQ1g7QUFDTDs7QUFFbkM7QUFBQTtBQVFBLElBQU1xSSxTQUErQixHQUFHLFNBQWxDQSxTQUErQkEsQ0FBQXJXLElBQUEsRUFBeUM7RUFBQSxJQUFuQ3VCLElBQUksR0FBQXZCLElBQUEsQ0FBSnVCLElBQUk7SUFBRStVLGVBQWUsR0FBQXRXLElBQUEsQ0FBZnNXLGVBQWU7SUFBRTNPLEtBQUssR0FBQTNILElBQUEsQ0FBTDJILEtBQUs7RUFDckUsSUFBQTZILFdBQUEsR0FBa0JoQyxrQkFBRyxDQUFDaUMsTUFBTSxDQUFDLENBQUM7SUFBdEI4RyxLQUFLLEdBQUEvRyxXQUFBLENBQUwrRyxLQUFLO0VBQ2I7RUFDQSxJQUFBeFIsV0FBQSxHQUFnQnRCLHNDQUFVLENBQUNoQixTQUFTLENBQUM7SUFBN0I2QyxHQUFHLEdBQUFQLFdBQUEsQ0FBSE8sR0FBRztFQUVYLG9CQUNFdEIsbUJBQUEsQ0FBQzRFLHlCQUFNQTtFQUNMO0VBQUE7SUFDQXdDLElBQUksZUFBRXBILG1CQUFBLENBQUN5RSw2QkFBYyxJQUFFLENBQUU7SUFDekIrTixNQUFNO0lBQ05uTCxPQUFPLEVBQUUsU0FBQUEsUUFBQ0gsQ0FBQyxFQUFLO01BQ2RBLENBQUMsQ0FBQ3VMLGVBQWUsQ0FBQyxDQUFDO01BQ25CdkwsQ0FBQyxDQUFDd0wsY0FBYyxDQUFDLENBQUM7TUFDbEJILEtBQUssQ0FBQ0ksT0FBTyxDQUFDO1FBQ1ovUSxLQUFLLDRGQUFBOUYsTUFBQSxDQUFvRDZILEtBQUssTUFBRztRQUNqRWlQLE9BQU8sRUFBRSxtQ0FBbUM7UUFDNUNDLGFBQWEsRUFBRTtVQUNiTCxNQUFNLEVBQUU7UUFDVixDQUFDO1FBQ0RNLElBQUk7VUFBQSxJQUFBQyxLQUFBLEdBQUE5VywwQkFBQSxlQUFBQyw0QkFBQSxHQUFBQyxJQUFBLENBQUUsU0FBQUMsUUFBQTtZQUFBLE9BQUFGLDRCQUFBLEdBQUFLLElBQUEsVUFBQUMsU0FBQUMsUUFBQTtjQUFBLGtCQUFBQSxRQUFBLENBQUFDLElBQUEsR0FBQUQsUUFBQSxDQUFBRSxJQUFBO2dCQUFBO2tCQUFBRixRQUFBLENBQUFDLElBQUE7a0JBQUFELFFBQUEsQ0FBQUUsSUFBQTtrQkFBQSxPQUVJMkUsR0FBRyxDQUFDL0QsSUFBSSxDQUFDO2dCQUFBO2tCQUNmK1UsZUFBZSxhQUFmQSxlQUFlLGVBQWZBLGVBQWUsQ0FBRyxDQUFDO2tCQUFDLE9BQUE3VixRQUFBLENBQUFJLE1BQUEsV0FDYixJQUFJO2dCQUFBO2tCQUFBSixRQUFBLENBQUFDLElBQUE7a0JBQUFELFFBQUEsQ0FBQW9CLEVBQUEsR0FBQXBCLFFBQUE7a0JBQUEsT0FBQUEsUUFBQSxDQUFBSSxNQUFBLFdBRUosS0FBSztnQkFBQTtnQkFBQTtrQkFBQSxPQUFBSixRQUFBLENBQUFNLElBQUE7Y0FBQTtZQUFBLEdBQUFYLE9BQUE7VUFBQSxDQUVmO1VBQUEsU0FBQTBXLEtBQUE7WUFBQSxPQUFBQyxLQUFBLENBQUE5VixLQUFBLE9BQUFDLFNBQUE7VUFBQTtVQUFBLE9BQUE0VixJQUFBO1FBQUE7TUFDSCxDQUFDLENBQUM7SUFDSjtFQUFFLENBQ0gsQ0FBQztBQUVOLENBQUM7QUFFRCx5REFBZVQsU0FBUyxFOzs7OztBQy9DdUM7QUFFZjtBQUM4QjtBQUNyQjtBQUNwQjtBQUNJO0FBQ0w7QUFDVTtBQUNWO0FBQUE7QUFBQTtBQUFBO0FBRXBDLElBQU1lLGFBQWEsR0FBRyxTQUFoQkEsYUFBYUEsQ0FBQSxFQUFTO0VBQzFCLElBQU05UyxTQUFTLEdBQUdULGdCQUFNLENBQWEsQ0FBQztFQUN0QyxJQUFBYyxTQUFBLEdBQXNDYixrQkFBUSxDQUFDLEtBQUssQ0FBQztJQUFBYyxVQUFBLEdBQUFILHVCQUFBLENBQUFFLFNBQUE7SUFBOUNQLFdBQVcsR0FBQVEsVUFBQTtJQUFFUCxjQUFjLEdBQUFPLFVBQUE7RUFDbEMsSUFBQTBKLFVBQUEsR0FBc0N4SyxrQkFBUSxDQUFPLENBQUM7SUFBQXlLLFVBQUEsR0FBQTlKLHVCQUFBLENBQUE2SixVQUFBO0lBQS9DK0ksV0FBVyxHQUFBOUksVUFBQTtJQUFFK0ksY0FBYyxHQUFBL0ksVUFBQTtFQUNsQyxJQUFNZ0osaUJBQWlCLEdBQUcsU0FBcEJBLGlCQUFpQkEsQ0FBSWpSLE1BQVcsRUFBSztJQUN6Q2dSLGNBQWMsQ0FBQ2hSLE1BQU0sQ0FBQztJQUN0QmpDLGNBQWMsQ0FBQyxJQUFJLENBQUM7RUFDdEIsQ0FBQztFQUNELElBQU1tVCxNQUFNLEdBQUdQLHFDQUFTLENBQUMsQ0FBQztFQUMxQixJQUFNcEssY0FBa0IsR0FBRyxFQUFFO0VBQzdCLElBQU00SyxpQkFBaUIsR0FBR0QsTUFBTSxDQUFDRSw2QkFBNkIsQ0FBQyxDQUFDO0VBQ2hFLElBQUlGLE1BQU0sQ0FBQ0csc0NBQXNDLENBQUMsQ0FBQyxFQUFFO0lBQ25EOUssY0FBYSxDQUFDeUQsSUFBSSxlQUNoQnRNLG1CQUFBLENBQUNnSyx5QkFBYztNQUNiN0ksU0FBUyxFQUFFLFNBQUFBLFVBQUEsRUFBTTtRQUFBLElBQUFLLGtCQUFBO1FBQ2YsQ0FBQUEsa0JBQUEsR0FBQWxCLFNBQVMsQ0FBQ21CLE9BQU8sY0FBQUQsa0JBQUEsZUFBakJBLGtCQUFBLENBQW1CRSxNQUFNLENBQUMsQ0FBQztNQUM3QjtJQUFFLENBQ0gsQ0FDSCxDQUFDO0VBQ0g7RUFDQSxJQUFNa1MsV0FBVyxHQUFHLFNBQWRBLFdBQVdBLENBQUEsRUFBUztJQUFBLElBQUF0TSxtQkFBQTtJQUN4QixDQUFBQSxtQkFBQSxHQUFBaEgsU0FBUyxDQUFDbUIsT0FBTyxjQUFBNkYsbUJBQUEsZUFBakJBLG1CQUFBLENBQW1CNUYsTUFBTSxDQUFDLENBQUM7RUFDN0IsQ0FBQztFQUNELElBQU1DLE9BQTJCLEdBQUcsQ0FDbEM7SUFDRUMsS0FBSyxlQUFFNUIsbUJBQUEsQ0FBQ1Qsd0NBQWdCO01BQUNzQyxFQUFFLEVBQUM7SUFBYyxDQUFFLENBQUM7SUFDN0NDLFNBQVMsRUFBRSxPQUFPO0lBQ2xCQyxNQUFNLFdBQUFBLE9BQUNDLEdBQUcsRUFBRUMsTUFBTSxFQUFFQyxLQUFLLEVBQUU7TUFDekIsb0JBQU9sQyxtQkFBQTtRQUFBbUMsUUFBQSxFQUFNRCxLQUFLLEdBQUc7TUFBQyxDQUFNLENBQUM7SUFDL0IsQ0FBQztJQUNEMlIsWUFBWSxFQUFFLElBQUk7SUFDbEJ6UixLQUFLLEVBQUU7RUFDVCxDQUFDLEVBQ0Q7SUFDRVIsS0FBSyxlQUFFNUIsbUJBQUEsQ0FBQ1Qsd0NBQWdCO01BQUNzQyxFQUFFLEVBQUM7SUFBbUIsQ0FBRSxDQUFDO0lBQ2xEQyxTQUFTLEVBQUUsV0FBVztJQUN0Qk0sS0FBSyxFQUFFLEVBQUU7SUFDVEwsTUFBTSxFQUFFLFNBQUFBLE9BQUNvRixDQUFDLEVBQUU3RSxNQUFNO01BQUEsb0JBQ2hCcEMsb0JBQUEsQ0FBQWlULG9CQUFBO1FBQUFoUixRQUFBLGdCQUNFbkMsbUJBQUEsQ0FBQzRFLHlCQUFNO1VBQ0x3QyxJQUFJLGVBQUVwSCxtQkFBQSxDQUFDZ1QsMEJBQVcsSUFBRSxDQUFFO1VBQ3RCelEsS0FBSyxFQUFFO1lBQUV1UixXQUFXLEVBQUU7VUFBTSxDQUFFO1VBQzlCek0sT0FBTyxFQUFFLFNBQUFBLFFBQUE7WUFBQSxPQUFNa00saUJBQWlCLENBQUNqUixNQUFNLENBQUM7VUFBQTtRQUFDLENBQzFDLENBQUMsRUFDREEsTUFBTSxhQUFOQSxNQUFNLHVCQUFOQSxNQUFNLENBQUVHLFVBQVU7TUFBQSxDQUNuQixDQUFDO0lBQUE7RUFFUCxDQUFDLEVBQ0Q7SUFDRWIsS0FBSyxlQUFFNUIsbUJBQUEsQ0FBQ1Qsd0NBQWdCO01BQUNzQyxFQUFFLEVBQUU7SUFBcUIsQ0FBRSxDQUFDO0lBQ3JEQyxTQUFTLEVBQUUsaUJBQWlCO0lBQzVCO0lBQ0E7SUFDQTtJQUNBK1IsWUFBWSxFQUFFLElBQUk7SUFDbEJ6UixLQUFLLEVBQUU7RUFDVCxDQUFDLEVBQ0Q7SUFDRVIsS0FBSyxlQUFFNUIsbUJBQUEsQ0FBQ1Qsd0NBQWdCO01BQUNzQyxFQUFFLEVBQUM7SUFBZ0IsQ0FBRSxDQUFDO0lBQy9DTyxLQUFLLEVBQUUsRUFBRTtJQUNUeVIsWUFBWSxFQUFFLElBQUk7SUFDbEI5UixNQUFNLFdBQUFBLE9BQUNDLEdBQUcsRUFBRUMsTUFBTSxFQUFFQyxLQUFLLEVBQUVRLE1BQU0sRUFBRUMsTUFBTSxFQUFFO01BQ3pDLG9CQUFPM0MsbUJBQUE7UUFBQW1DLFFBQUEsS0FBQXJHLE1BQUEsQ0FBU21HLE1BQU0sQ0FBQ2UsZUFBZSxPQUFBbEgsTUFBQSxDQUFJbUcsTUFBTSxDQUFDZ0IsY0FBYztNQUFBLENBQVEsQ0FBQztJQUMxRTtFQUNGLENBQUMsRUFDRDtJQUNFckIsS0FBSyxFQUFFLFFBQVE7SUFDZkUsU0FBUyxFQUFFLE1BQU07SUFDakJ5QyxNQUFNLEVBQUUsS0FBSztJQUNibkMsS0FBSyxFQUFFLEVBQUU7SUFDVEwsTUFBTSxFQUFFLFNBQUFBLE9BQUNDLEdBQUcsRUFBRUMsTUFBTTtNQUFBLG9CQUNsQmpDLG1CQUFBLENBQUM0SixvQkFBSztRQUFBekgsUUFBQSxFQUNIc1IsaUJBQWlCLGlCQUNoQnpULG1CQUFBLENBQUNxUyxvQkFBUztVQUNSQyxlQUFlLEVBQUVzQixXQUFZO1VBQzdCclcsSUFBSSxFQUFFMEUsTUFBTSxDQUFDMUUsSUFBSztVQUNsQm9HLEtBQUssRUFBRTFCLE1BQU0sQ0FBQ1EsVUFBVSxJQUFJO1FBQUcsQ0FDaEM7TUFDRixDQUNJLENBQUM7SUFBQTtFQUVaLENBQUMsQ0FDRjtFQUVELG9CQUNFdkMsb0JBQUEsQ0FBQWlULG9CQUFBO0lBQUFoUixRQUFBLEdBQ0drUixXQUFXLGlCQUNWclQsbUJBQUEsQ0FBQ0csb0JBQVM7TUFDUjVDLElBQUksRUFBRThWLFdBQVcsQ0FBQzlWLElBQUs7TUFDdkI2QyxXQUFXLEVBQUVBLFdBQVk7TUFDekJDLGNBQWMsRUFBRUE7SUFBZSxDQUNoQyxDQUNGLGVBQ0RMLG1CQUFBLENBQUNWLG9CQUFRO01BQ1BnQixTQUFTLEVBQUVBLFNBQVU7TUFDckJxQixPQUFPLEVBQUVBLE9BQVE7TUFDakI0QyxNQUFNLEVBQUU7UUFBRXdQLFVBQVUsRUFBRTtNQUFPLENBQUU7TUFDL0I1UCxZQUFZO01BQ1pDLElBQUksRUFBQyxPQUFPO01BQ1ptRSxVQUFVLEVBQUU7UUFDVkMsZUFBZSxFQUFFLEVBQUU7UUFDbkJDLGVBQWUsRUFBRSxJQUFJO1FBQ3JCQyxlQUFlLEVBQUUsQ0FBQyxJQUFJLEVBQUUsSUFBSSxFQUFFLEtBQUs7TUFDckMsQ0FBRTtNQUNGbk4sT0FBTztRQUFBLElBQUFTLElBQUEsR0FBQUMsMEJBQUEsZUFBQUMsNEJBQUEsR0FBQUMsSUFBQSxDQUFFLFNBQUFDLFFBQU9DLE1BQU0sRUFBRW1MLElBQUksRUFBRXRDLE1BQU07VUFBQSxJQUFBOE8sT0FBQSxFQUFBMVgsR0FBQTtVQUFBLE9BQUFKLDRCQUFBLEdBQUFLLElBQUEsVUFBQUMsU0FBQUMsUUFBQTtZQUFBLGtCQUFBQSxRQUFBLENBQUFDLElBQUEsR0FBQUQsUUFBQSxDQUFBRSxJQUFBO2NBQUE7Z0JBQUFGLFFBQUEsQ0FBQUMsSUFBQTtnQkFFMUJzWCxPQUFjLEdBQUcsRUFBRTtnQkFDekIsSUFBSTNYLE1BQU0sQ0FBQ3dSLFNBQVMsRUFBRTtrQkFDcEJtRyxPQUFPLENBQUMxSCxJQUFJLENBQUMsQ0FBQyxLQUFLLEVBQUUsV0FBVyxFQUFFLE1BQU0sRUFBRWpRLE1BQU0sQ0FBQ3dSLFNBQVMsQ0FBQyxDQUFDO2dCQUM5RDtnQkFBQ3BSLFFBQUEsQ0FBQUUsSUFBQTtnQkFBQSxPQUVpQlosT0FBTyxDQUFDO2tCQUN4QmtZLElBQUksRUFBRTVYLE1BQU0sQ0FBQ29GLE9BQU87a0JBQ3BCMkMsSUFBSSxFQUFFL0gsTUFBTSxDQUFDNlgsUUFBUTtrQkFDckJDLFFBQVEsRUFBRSxlQUFlO2tCQUN6QkgsT0FBTyxFQUFQQTtnQkFDRixDQUFDLENBQUM7Y0FBQTtnQkFMSTFYLEdBQUcsR0FBQUcsUUFBQSxDQUFBRyxJQUFBO2dCQUFBLE9BQUFILFFBQUEsQ0FBQUksTUFBQSxXQU1GO2tCQUNMYSxJQUFJLEVBQUVwQixHQUFHLENBQUNvQixJQUFJO2tCQUNkMFcsTUFBTSxFQUFFLElBQUk7a0JBQ1p2TSxLQUFLLEVBQUV2TCxHQUFHLENBQUNpTSxVQUFVLENBQUM4TDtnQkFDeEIsQ0FBQztjQUFBO2dCQUFBNVgsUUFBQSxDQUFBQyxJQUFBO2dCQUFBRCxRQUFBLENBQUFvQixFQUFBLEdBQUFwQixRQUFBO2dCQUFBLE9BQUFBLFFBQUEsQ0FBQUksTUFBQSxXQUVNO2tCQUNMYSxJQUFJLEVBQUUsRUFBRTtrQkFDUjBXLE1BQU0sRUFBRTtnQkFDVixDQUFDO2NBQUE7Y0FBQTtnQkFBQSxPQUFBM1gsUUFBQSxDQUFBTSxJQUFBO1lBQUE7VUFBQSxHQUFBWCxPQUFBO1FBQUEsQ0FFSjtRQUFBLGlCQUFBWSxFQUFBLEVBQUFjLEdBQUEsRUFBQVUsR0FBQTtVQUFBLE9BQUF4QyxJQUFBLENBQUFpQixLQUFBLE9BQUFDLFNBQUE7UUFBQTtNQUFBLElBQUM7TUFDRjJMLGFBQWEsRUFBRSxTQUFBQSxjQUFBO1FBQUEsT0FBTUEsY0FBYTtNQUFBO0lBQUMsQ0FDcEMsQ0FBQztFQUFBLENBQ0YsQ0FBQztBQUVQLENBQUM7QUFFRCw2REFBZXVLLGFBQWEsRTs7QUNsSnVEO0FBQ2pCO0FBQzdCO0FBQ2tCO0FBQUE7QUFFdkQsSUFBTW9CLEdBQUcsR0FBRyxTQUFOQSxHQUFHQSxDQUFBLEVBQVM7RUFDaEIsSUFBTUMsSUFBSSxHQUFHalYsbUNBQU8sQ0FBQyxDQUFDO0VBQ3RCLG9CQUNFUSxtQkFBQSxDQUFDc1UsMENBQTJCO0lBQzFCSSxTQUFTLEVBQUUsQ0FDVDtNQUNFL1EsS0FBSyxFQUFFOFEsSUFBSSxDQUFDdFIsYUFBYSxDQUFDO1FBQUV0QixFQUFFLEVBQUU7TUFBd0IsQ0FBQyxDQUFDO01BQzFEOFMsUUFBUSxlQUFFM1UsbUJBQUEsQ0FBQ3VVLHlDQUFvQjtRQUFDSyxNQUFNO01BQUEsQ0FBRSxDQUFDO01BQ3pDQyxTQUFTLFdBQUFBLFVBQUEsRUFBRztRQUNWLG9CQUFPN1UsbUJBQUEsQ0FBQ29ULHdCQUFhLElBQUUsQ0FBQztNQUMxQjtJQUNGLENBQUM7RUFDRCxDQUNILENBQUM7QUFFTixDQUFDO0FBRUQsMERBQWVvQixHQUFHIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9zcmMvc2VydmljZXMvSW52ZW50b3J5TWFuYWdlbWVudFYzL2JvbS50cz8xODYzIiwid2VicGFjazovL2FudC1kZXNpZ24tcHJvLy4vc3JjL3BhZ2VzL0ludmVudG9yeU1hbmFnZW1lbnRWMy9Qcm9kdWN0TWFuYWdlbWVudC9CT00vY29tcG9uZW50cy9CT01EZXRhaWwudHN4PzA5OTgiLCJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9zcmMvcGFnZXMvSW52ZW50b3J5TWFuYWdlbWVudFYzL1Byb2R1Y3RNYW5hZ2VtZW50L0JPTS9jb21wb25lbnRzL0NyZWF0ZUJvbU1vZGFsVGFibGUudHN4PzgzMGQiLCJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9zcmMvcGFnZXMvSW52ZW50b3J5TWFuYWdlbWVudFYzL1Byb2R1Y3RNYW5hZ2VtZW50L0JPTS9jb21wb25lbnRzL0NyZWF0ZUJvbU1vZGFsLnRzeD9jOGI3Iiwid2VicGFjazovL2FudC1kZXNpZ24tcHJvLy4vc3JjL3BhZ2VzL0ludmVudG9yeU1hbmFnZW1lbnRWMy9Qcm9kdWN0TWFuYWdlbWVudC9CT00vY29tcG9uZW50cy9EZWxldGVCb20udHN4P2JkZGIiLCJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9zcmMvcGFnZXMvSW52ZW50b3J5TWFuYWdlbWVudFYzL1Byb2R1Y3RNYW5hZ2VtZW50L0JPTS9jb21wb25lbnRzL0JPTU1hbmFnZW1lbnQudHN4P2Y2NTAiLCJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9zcmMvcGFnZXMvSW52ZW50b3J5TWFuYWdlbWVudFYzL1Byb2R1Y3RNYW5hZ2VtZW50L0JPTS9pbmRleC50c3g/YWZiYiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBJQm9tLCBJQm9tRGV0YWlsIH0gZnJvbSAnQC90eXBlcy9ib20udHlwZSc7XHJcbmltcG9ydCB7IHJlcXVlc3QgfSBmcm9tICdAdW1panMvbWF4JztcclxuaW1wb3J0IHsgZ2VuZXJhdGVBUElQYXRoLCBnZXRQYXJhbXNSZXFMaXN0IH0gZnJvbSAnLi4vdXRpbHMnO1xyXG5cclxuY29uc3QgaGFuZGxlRXJyb3IgPSAoZXJyb3I6IGFueSkgPT4ge1xyXG4gIGNvbnNvbGUubG9nKGBFcnJvciBpbiBzZXJ2aWNlcy9ib206IFxcbiR7ZXJyb3J9YCk7XHJcbn07XHJcbmV4cG9ydCBjb25zdCBnZXRCT01zID0gYXN5bmMgKHBhcmFtcz86IEFQSS5MaXN0UGFyYW1zUmVxKSA9PiB7XHJcbiAgY29uc3QgcmVzID0gYXdhaXQgcmVxdWVzdDxBUEkuUGFnaW5hdGlvblJlc3BvbnNlUmVzdWx0PElCb21bXT4+KGdlbmVyYXRlQVBJUGF0aCgnYXBpL3YyL2JvbScpLCB7XHJcbiAgICBwYXJhbXM6IGdldFBhcmFtc1JlcUxpc3QocGFyYW1zKSxcclxuICB9KTtcclxuICByZXR1cm4gcmVzLnJlc3VsdDtcclxufTtcclxuZXhwb3J0IGNvbnN0IGdldEJPTURldGFpbCA9IGFzeW5jICh7IG5hbWUgfTogeyBuYW1lOiBzdHJpbmcgfSkgPT4ge1xyXG4gIHRyeSB7XHJcbiAgICBjb25zdCByZXMgPSBhd2FpdCByZXF1ZXN0KGdlbmVyYXRlQVBJUGF0aCgnYXBpL3YyL2JvbS9kZXRhaWwnKSwge1xyXG4gICAgICBwYXJhbXM6IHtcclxuICAgICAgICBuYW1lLFxyXG4gICAgICB9LFxyXG4gICAgfSk7XHJcbiAgICByZXR1cm4geyBkYXRhOiByZXMucmVzdWx0LmRvY3MuYXQoMCkgYXMgSUJvbURldGFpbCB9O1xyXG4gIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICBoYW5kbGVFcnJvcihlcnJvcik7XHJcbiAgICByZXR1cm4geyBkYXRhOiB7fSBhcyBJQm9tRGV0YWlsIH07XHJcbiAgfVxyXG59O1xyXG5cclxuZXhwb3J0IGNvbnN0IHNhdmVCb20gPSBhc3luYyAocGF5bG9hZDogYW55KSA9PiB7XHJcbiAgdHJ5IHtcclxuICAgIGNvbnN0IHJlcyA9IGF3YWl0IHJlcXVlc3QoZ2VuZXJhdGVBUElQYXRoKCdhcGkvdjIvYm9tL3NhdmUnKSwge1xyXG4gICAgICBtZXRob2Q6ICdQT1NUJyxcclxuICAgICAgZGF0YToge1xyXG4gICAgICAgIC4uLnBheWxvYWQsXHJcbiAgICAgIH0sXHJcbiAgICB9KTtcclxuXHJcbiAgICByZXR1cm4gcmVzLnJlc3VsdC5kb2NzPy5hdCgwKTtcclxuICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgaGFuZGxlRXJyb3IoZXJyb3IpO1xyXG4gICAgdGhyb3cgZXJyb3I7XHJcbiAgfVxyXG59O1xyXG5cclxuZXhwb3J0IGNvbnN0IGRlbGV0ZUJvbSA9IGFzeW5jIChuYW1lOiBzdHJpbmcpID0+IHtcclxuICB0cnkge1xyXG4gICAgY29uc3QgcmVzID0gYXdhaXQgcmVxdWVzdChnZW5lcmF0ZUFQSVBhdGgoJ2FwaS92Mi9ib20nKSwge1xyXG4gICAgICBtZXRob2Q6ICdERUxFVEUnLFxyXG4gICAgICBwYXJhbXM6IHtcclxuICAgICAgICBuYW1lLFxyXG4gICAgICB9LFxyXG4gICAgfSk7XHJcblxyXG4gICAgcmV0dXJuIHJlcy5yZXN1bHQuZG9jcz8uYXQoMCk7XHJcbiAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgIGhhbmRsZUVycm9yKGVycm9yKTtcclxuICAgIHRocm93IGVycm9yO1xyXG4gIH1cclxufTtcclxuIiwiaW1wb3J0IHsgREVGQVVMVF9EQVRFX0ZPUk1BVF9XSVRIT1VUX1RJTUUgfSBmcm9tICdAL2NvbW1vbi9jb250YW5zdC9jb25zdGFuc3QnO1xyXG5pbXBvcnQgeyBnZXRCT01EZXRhaWwgfSBmcm9tICdAL3NlcnZpY2VzL0ludmVudG9yeU1hbmFnZW1lbnRWMy9ib20nO1xyXG5pbXBvcnQgeyBmb3JtYXROdW1lcmFsIH0gZnJvbSAnQC9zZXJ2aWNlcy91dGlscyc7XHJcbmltcG9ydCB7IElCb21EZXRhaWxJdGVtIH0gZnJvbSAnQC90eXBlcy9ib20udHlwZSc7XHJcbmltcG9ydCB7XHJcbiAgQWN0aW9uVHlwZSxcclxuICBQcm9Db2x1bW5zLFxyXG4gIFByb0Zvcm0sXHJcbiAgUHJvRm9ybURhdGVQaWNrZXIsXHJcbiAgUHJvRm9ybVRleHQsXHJcbiAgUHJvRm9ybVRleHRBcmVhLFxyXG4gIFByb1RhYmxlLFxyXG59IGZyb20gJ0BhbnQtZGVzaWduL3Byby1jb21wb25lbnRzJztcclxuaW1wb3J0IHsgRm9ybWF0dGVkTWVzc2FnZSwgdXNlSW50bCwgdXNlUmVxdWVzdCB9IGZyb20gJ0B1bWlqcy9tYXgnO1xyXG5pbXBvcnQgeyBNb2RhbCB9IGZyb20gJ2FudGQnO1xyXG5pbXBvcnQgeyB1c2VGb3JtIH0gZnJvbSAnYW50ZC9lcy9mb3JtL0Zvcm0nO1xyXG5pbXBvcnQgeyB1c2VFZmZlY3QsIHVzZVJlZiwgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XHJcblxyXG5pbnRlcmZhY2UgUHJvcHMge1xyXG4gIG5hbWU6IHN0cmluZztcclxuICBpc01vZGFsT3Blbj86IGJvb2xlYW47XHJcbiAgc2V0SXNNb2RhbE9wZW4/OiBhbnk7XHJcbn1cclxuY29uc3QgQk9NRGV0YWlsID0gKHsgbmFtZSwgaXNNb2RhbE9wZW4sIHNldElzTW9kYWxPcGVuIH06IFByb3BzKSA9PiB7XHJcbiAgY29uc3QgYWN0aW9uUmVmID0gdXNlUmVmPEFjdGlvblR5cGU+KCk7XHJcbiAgY29uc3QgW2Zvcm1dID0gdXNlRm9ybSgpO1xyXG4gIGNvbnN0IFtpdGVtcywgc2V0SXRlbXNdID0gdXNlU3RhdGU8SUJvbURldGFpbEl0ZW1bXT4oW10pO1xyXG4gIGNvbnN0IHsgZGF0YSwgbG9hZGluZywgcmVmcmVzaCwgcnVuIH0gPSB1c2VSZXF1ZXN0KGdldEJPTURldGFpbCwge1xyXG4gICAgbWFudWFsOiB0cnVlLFxyXG4gICAgb25FcnJvcihlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmxvZygnZXJyb3InLCBlcnJvci5tZXNzYWdlKTtcclxuICAgIH0sXHJcbiAgICBvblN1Y2Nlc3MoZGF0YSwgcGFyYW1zKSB7fSxcclxuICB9KTtcclxuICBjb25zdCBoYW5kbGVSZWxvYWQgPSAoKSA9PiB7XHJcbiAgICBhY3Rpb25SZWYuY3VycmVudD8ucmVsb2FkKCk7XHJcbiAgICByZWZyZXNoKCk7XHJcbiAgfTtcclxuICBjb25zdCBjb2x1bW5zOiBQcm9Db2x1bW5zPElCb21EZXRhaWxJdGVtPltdID0gW1xyXG4gICAge1xyXG4gICAgICB0aXRsZTogPEZvcm1hdHRlZE1lc3NhZ2UgaWQ9XCJjb21tb24uaW5kZXhcIiAvPixcclxuICAgICAgZGF0YUluZGV4OiAnaW5kZXgnLFxyXG4gICAgICByZW5kZXIoZG9tLCBlbnRpdHksIGluZGV4KSB7XHJcbiAgICAgICAgcmV0dXJuIDxkaXY+e2luZGV4ICsgMX08L2Rpdj47XHJcbiAgICAgIH0sXHJcblxyXG4gICAgICB3aWR0aDogMTAsXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICB0aXRsZTogPEZvcm1hdHRlZE1lc3NhZ2UgaWQ9XCJ3YXJlaG91c2UtbWFuYWdlbWVudC5zdG9jay1lbnRyeS1oaXN0b3J5Lml0ZW1fbGFiZWxcIiAvPixcclxuICAgICAgZGF0YUluZGV4OiAnaXRlbV9sYWJlbCcsXHJcbiAgICAgIHdpZHRoOiAxNSxcclxuICAgICAgcmVuZGVyKHZhbHVlLCByZWNvcmQsIGluZGV4KSB7XHJcbiAgICAgICAgcmV0dXJuIChcclxuICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgc3R5bGU9e3tcclxuICAgICAgICAgICAgICB3aGl0ZVNwYWNlOiAncHJlLXdyYXAnLFxyXG4gICAgICAgICAgICB9fVxyXG4gICAgICAgICAgPlxyXG4gICAgICAgICAgICB7cmVjb3JkLml0ZW1fbGFiZWx9XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICApO1xyXG4gICAgICB9LFxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgdGl0bGU6IDxGb3JtYXR0ZWRNZXNzYWdlIGlkPVwiY29tbW9uLnVuaXRcIiAvPixcclxuICAgICAgZGF0YUluZGV4OiAndW9tX25hbWUnLFxyXG4gICAgICB3aWR0aDogMjAsXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICB0aXRsZTogPEZvcm1hdHRlZE1lc3NhZ2UgaWQ9XCJjb21tb24ucXVhbnRpdHlcIiAvPixcclxuICAgICAgZGF0YUluZGV4OiAnc3RvY2tfcXR5JyxcclxuICAgICAgd2lkdGg6IDIwLFxyXG4gICAgICByZW5kZXIoZG9tLCBlbnRpdHksIGluZGV4LCBhY3Rpb24sIHNjaGVtYSkge1xyXG4gICAgICAgIHJldHVybiA8ZGl2Pntmb3JtYXROdW1lcmFsKGVudGl0eS5zdG9ja19xdHkpfTwvZGl2PjtcclxuICAgICAgfSxcclxuICAgIH0sXHJcbiAgXTtcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgcnVuKHsgbmFtZSB9KTtcclxuICB9LCBbbmFtZV0pO1xyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBmb3JtLnJlc2V0RmllbGRzKCk7XHJcbiAgICBmb3JtLnNldEZpZWxkc1ZhbHVlKGRhdGEpO1xyXG5cclxuICAgIGZvcm0uc2V0RmllbGRWYWx1ZSgndXNlcicsIGAke2RhdGE/LnVzZXJfZmlyc3RfbmFtZX0gJHtkYXRhPy51c2VyX2xhc3RfbmFtZX1gKTtcclxuICAgIHNldEl0ZW1zKGRhdGE/Lml0ZW1zIHx8IFtdKTtcclxuICB9LCBbZGF0YV0pO1xyXG5cclxuICBjb25zdCB7IGZvcm1hdE1lc3NhZ2UgfSA9IHVzZUludGwoKTtcclxuICByZXR1cm4gKFxyXG4gICAgPE1vZGFsXHJcbiAgICAgIG9wZW49e2lzTW9kYWxPcGVufVxyXG4gICAgICB0aXRsZT17PEZvcm1hdHRlZE1lc3NhZ2UgaWQ9eydjb21tb24uYm9tLmRldGFpbCd9IC8+fVxyXG4gICAgICBvbkNhbmNlbD17KCkgPT4ge1xyXG4gICAgICAgIHNldElzTW9kYWxPcGVuPy4oZmFsc2UpO1xyXG4gICAgICAgIGhhbmRsZVJlbG9hZCgpO1xyXG4gICAgICB9fVxyXG4gICAgICBmb290ZXI9e1tdfVxyXG4gICAgICB3aWR0aD17ODAwfVxyXG4gICAgPlxyXG4gICAgICA8UHJvRm9ybSBzdWJtaXR0ZXI9e2ZhbHNlfSBkaXNhYmxlZCBmb3JtPXtmb3JtfSBsYXlvdXQ9XCJ2ZXJ0aWNhbFwiIGdyaWQ9e3RydWV9PlxyXG4gICAgICAgIHsvKiA8UHJvRm9ybVRleHRcclxuICAgICAgICAgIGxhYmVsPXs8Rm9ybWF0dGVkTWVzc2FnZSBpZD17J3dhcmVob3VzZS1tYW5hZ2VtZW50LmltcG9ydC1oaXN0b3J5LmlkJ30gLz59XHJcbiAgICAgICAgICBjb2xQcm9wcz17e1xyXG4gICAgICAgICAgICBzbTogMjQsXHJcbiAgICAgICAgICAgIG1kOiA4LFxyXG4gICAgICAgICAgfX1cclxuICAgICAgICAgIG5hbWU9eyduYW1lJ31cclxuICAgICAgICAgIHdpZHRoPVwibWRcIlxyXG4gICAgICAgIC8+ICovfVxyXG4gICAgICAgIDxQcm9Gb3JtVGV4dFxyXG4gICAgICAgICAgbGFiZWw9ezxGb3JtYXR0ZWRNZXNzYWdlIGlkPXsnYm9tLW1hbmFnZW1lbnQuY3JlYXRlZF9pdGVtJ30gLz59XHJcbiAgICAgICAgICBjb2xQcm9wcz17e1xyXG4gICAgICAgICAgICBzcGFuOiA4LFxyXG4gICAgICAgICAgfX1cclxuICAgICAgICAgIG5hbWU9eydpdGVtX2xhYmVsJ31cclxuICAgICAgICAgIHdpZHRoPVwibWRcIlxyXG4gICAgICAgIC8+XHJcbiAgICAgICAgPFByb0Zvcm1UZXh0XHJcbiAgICAgICAgICBsYWJlbD17PEZvcm1hdHRlZE1lc3NhZ2UgaWQ9eydib20tbWFuYWdlbWVudC5jcmVhdGVkX2l0ZW1fcXVhbnRpdHknfSAvPn1cclxuICAgICAgICAgIGNvbFByb3BzPXt7XHJcbiAgICAgICAgICAgIHNwYW46IDgsXHJcbiAgICAgICAgICB9fVxyXG4gICAgICAgICAgbmFtZT17J3F1YW50aXR5J31cclxuICAgICAgICAgIHdpZHRoPVwibWRcIlxyXG4gICAgICAgIC8+XHJcbiAgICAgICAgPFByb0Zvcm1UZXh0XHJcbiAgICAgICAgICBsYWJlbD17PEZvcm1hdHRlZE1lc3NhZ2UgaWQ9eydjb21tb24udW5pdCd9IC8+fVxyXG4gICAgICAgICAgY29sUHJvcHM9e3tcclxuICAgICAgICAgICAgc3BhbjogOCxcclxuICAgICAgICAgIH19XHJcbiAgICAgICAgICBuYW1lPXsndW9tX25hbWUnfVxyXG4gICAgICAgICAgd2lkdGg9XCJtZFwiXHJcbiAgICAgICAgLz5cclxuICAgICAgICA8UHJvRm9ybURhdGVQaWNrZXJcclxuICAgICAgICAgIGxhYmVsPXs8Rm9ybWF0dGVkTWVzc2FnZSBpZD17J2NvbW1vbi5jcmVhdGlvbid9IC8+fVxyXG4gICAgICAgICAgY29sUHJvcHM9e3tcclxuICAgICAgICAgICAgc3BhbjogOCxcclxuICAgICAgICAgIH19XHJcbiAgICAgICAgICBuYW1lPXsnY3JlYXRpb24nfVxyXG4gICAgICAgICAgd2lkdGg9XCJtZFwiXHJcbiAgICAgICAgICBmaWVsZFByb3BzPXt7XHJcbiAgICAgICAgICAgIGZvcm1hdDogREVGQVVMVF9EQVRFX0ZPUk1BVF9XSVRIT1VUX1RJTUUsXHJcbiAgICAgICAgICB9fVxyXG4gICAgICAgIC8+XHJcbiAgICAgICAgPFByb0Zvcm1UZXh0XHJcbiAgICAgICAgICBsYWJlbD17PEZvcm1hdHRlZE1lc3NhZ2UgaWQ9eydjb21tb24uY3JlYXRvcid9IC8+fVxyXG4gICAgICAgICAgY29sUHJvcHM9e3tcclxuICAgICAgICAgICAgc3BhbjogOCxcclxuICAgICAgICAgIH19XHJcbiAgICAgICAgICBuYW1lPXsndXNlcid9XHJcbiAgICAgICAgICB3aWR0aD1cIm1kXCJcclxuICAgICAgICAvPlxyXG4gICAgICAgIDxQcm9Gb3JtVGV4dEFyZWFcclxuICAgICAgICAgIGxhYmVsPXs8Rm9ybWF0dGVkTWVzc2FnZSBpZD17J2NvbW1vbi5kZXNjcmlwdGlvbid9IC8+fVxyXG4gICAgICAgICAgbmFtZT17J2JvbV9kZXNjcmlwdGlvbid9XHJcbiAgICAgICAgICBjb2xQcm9wcz17e1xyXG4gICAgICAgICAgICBzcGFuOiAyNCxcclxuICAgICAgICAgIH19XHJcbiAgICAgICAgICBmaWVsZFByb3BzPXt7XHJcbiAgICAgICAgICAgIGF1dG9TaXplOiB7IG1pblJvd3M6IDEsIG1heFJvd3M6IDIgfSxcclxuICAgICAgICAgIH19XHJcbiAgICAgICAgLz5cclxuICAgICAgPC9Qcm9Gb3JtPlxyXG4gICAgICA8YnI+PC9icj5cclxuICAgICAgPFByb1RhYmxlPElCb21EZXRhaWxJdGVtPlxyXG4gICAgICAgIGNvbHVtbnM9e2NvbHVtbnN9XHJcbiAgICAgICAgY2FyZEJvcmRlcmVkXHJcbiAgICAgICAgc2l6ZT1cInNtYWxsXCJcclxuICAgICAgICBkYXRhU291cmNlPXtpdGVtc31cclxuICAgICAgICByb3dLZXk9eyduYW1lJ31cclxuICAgICAgICBzZWFyY2g9e2ZhbHNlfVxyXG4gICAgICAvPlxyXG4gICAgPC9Nb2RhbD5cclxuICApO1xyXG59O1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgQk9NRGV0YWlsO1xyXG4iLCJpbXBvcnQgeyBnZXREZXRhaWxzUHJvZHVjdEl0ZW1WMyB9IGZyb20gJ0Avc2VydmljZXMvSW52ZW50b3J5TWFuYWdlbWVudFYzL3Byb2R1Y3QtaXRlbSc7XHJcbmltcG9ydCB7IERlbGV0ZU91dGxpbmVkIH0gZnJvbSAnQGFudC1kZXNpZ24vaWNvbnMnO1xyXG5pbXBvcnQge1xyXG4gIEFjdGlvblR5cGUsXHJcbiAgRWRpdGFibGVQcm9UYWJsZSxcclxuICBQcm9Db2x1bW5zLFxyXG4gIFByb0Zvcm1TZWxlY3QsXHJcbn0gZnJvbSAnQGFudC1kZXNpZ24vcHJvLWNvbXBvbmVudHMnO1xyXG5pbXBvcnQgeyBGb3JtYXR0ZWRNZXNzYWdlIH0gZnJvbSAnQHVtaWpzL21heCc7XHJcbmltcG9ydCB7IEJ1dHRvbiwgSW5wdXROdW1iZXIgfSBmcm9tICdhbnRkJztcclxuaW1wb3J0IHsgdXNlRWZmZWN0LCB1c2VSZWYsIHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xyXG5cclxuZXhwb3J0IGludGVyZmFjZSBJQ3JlYXRlQm9tSXRlbSB7XHJcbiAgY29udmVyc2lvbl9mYWN0b3I6IG51bWJlcjtcclxuICBpdGVtX2NvZGU6IHN0cmluZztcclxuICBpdGVtX25hbWU6IHN0cmluZztcclxuICBxdHk6IG51bWJlcjtcclxuICAvLyByYXRlOiBudW1iZXI7XHJcbiAgLy8gdG90YWxfcHJpY2U6IG51bWJlcjtcclxuICBpdGVtX2xhYmVsOiBzdHJpbmc7XHJcbiAga2V5OiBzdHJpbmc7XHJcbiAgdW9tX2xhYmVsOiBzdHJpbmc7XHJcbn1cclxuaW50ZXJmYWNlIFByb3BzIHtcclxuICBkYXRhOiBJQ3JlYXRlQm9tSXRlbVtdO1xyXG4gIHNldERhdGE6IFJlYWN0LkRpc3BhdGNoPFJlYWN0LlNldFN0YXRlQWN0aW9uPElDcmVhdGVCb21JdGVtW10+PjtcclxufVxyXG5jb25zdCBDcmVhdGVCb21Nb2RhbFRhYmxlID0gKHsgZGF0YSwgc2V0RGF0YSB9OiBQcm9wcykgPT4ge1xyXG4gIGNvbnN0IGFjdGlvblJlZiA9IHVzZVJlZjxBY3Rpb25UeXBlPigpO1xyXG4gIGNvbnN0IGhhbmRsZURlbGV0ZUl0ZW0gPSAoaW5kZXg6IGFueSkgPT4ge1xyXG4gICAgY29uc3QgbmV3RGF0YSA9IGRhdGEuZmlsdGVyKChpdGVtKSA9PiBpdGVtLmtleSAhPT0gaW5kZXgpO1xyXG4gICAgc2V0RGF0YShuZXdEYXRhKTtcclxuICB9O1xyXG4gIGNvbnN0IFtlZGl0YWJsZUtleXMsIHNldEVkaXRhYmxlUm93S2V5c10gPSB1c2VTdGF0ZTxSZWFjdC5LZXlbXT4oKCkgPT5cclxuICAgIGRhdGEubWFwKChpdGVtKSA9PiBpdGVtLmtleSksXHJcbiAgKTtcclxuICAvLyBjb25zdCBoYW5kbGVVcGRhdGVRdWFudGl0eSA9IChyZWNvcmQ6IElDcmVhdGVCb21JdGVtKSA9PiB7XHJcbiAgLy8gICByZWNvcmQudG90YWxfcHJpY2UgPSByZWNvcmQucXR5ICogcmVjb3JkLnJhdGU7XHJcbiAgLy8gfTtcclxuICBjb25zdCBoYW5kbGVTZWxlY3RVbml0ID0gKGluZGV4OiBhbnksIHZhbHVlOiBudW1iZXIpID0+IHtcclxuICAgIGNvbnN0IG5ld0RhdGEgPSBzdHJ1Y3R1cmVkQ2xvbmUoZGF0YSk7XHJcbiAgICBjb25zdCByZWNvcmRJbmRleCA9IG5ld0RhdGEuZmluZEluZGV4KChpdGVtKSA9PiBpdGVtLmtleSA9PT0gaW5kZXgpO1xyXG5cclxuICAgIG5ld0RhdGFbcmVjb3JkSW5kZXhdLmNvbnZlcnNpb25fZmFjdG9yID0gdmFsdWU7XHJcbiAgICBzZXREYXRhKG5ld0RhdGEpO1xyXG4gICAgYWN0aW9uUmVmLmN1cnJlbnQ/LnJlbG9hZCgpO1xyXG4gIH07XHJcbiAgY29uc3QgY29sdW1uczogUHJvQ29sdW1uczxJQ3JlYXRlQm9tSXRlbT5bXSA9IFtcclxuICAgIHtcclxuICAgICAgdGl0bGU6IDxGb3JtYXR0ZWRNZXNzYWdlIGlkPXsnY29tbW9uLmluZGV4J30gLz4sXHJcbiAgICAgIGRhdGFJbmRleDogJ2luZGV4JyxcclxuICAgICAgZWRpdGFibGU6IGZhbHNlLFxyXG4gICAgICByZW5kZXIoZG9tLCBlbnRpdHksIGluZGV4LCBhY3Rpb24sIHNjaGVtYSkge1xyXG4gICAgICAgIHJldHVybiA8ZGl2PntpbmRleCArIDF9PC9kaXY+O1xyXG4gICAgICB9LFxyXG4gICAgICB3aWR0aDogNDAsXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICB0aXRsZTogPEZvcm1hdHRlZE1lc3NhZ2UgaWQ9eyd3YXJlaG91c2UtbWFuYWdlbWVudC5pbXBvcnQtdm91Y2hlci5pdGVtX2NvZGUnfSAvPixcclxuICAgICAgZGF0YUluZGV4OiAnaXRlbV9uYW1lJyxcclxuICAgICAgZWRpdGFibGU6IGZhbHNlLFxyXG4gICAgICB3aWR0aDogODAsXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICB0aXRsZTogKFxyXG4gICAgICAgIDxGb3JtYXR0ZWRNZXNzYWdlXHJcbiAgICAgICAgICBpZD1cIndhcmVob3VzZS1tYW5hZ2VtZW50LmltcG9ydC12b3VjaGVyLml0ZW1fbmFtZVwiXHJcbiAgICAgICAgICBkZWZhdWx0TWVzc2FnZT1cInVua25vd25cIlxyXG4gICAgICAgIC8+XHJcbiAgICAgICksXHJcbiAgICAgIGRhdGFJbmRleDogJ2l0ZW1fbGFiZWwnLFxyXG4gICAgICBlZGl0YWJsZTogZmFsc2UsXHJcbiAgICAgIHdpZHRoOiA4MCxcclxuICAgIH0sXHJcblxyXG4gICAge1xyXG4gICAgICB0aXRsZTogKFxyXG4gICAgICAgIDxGb3JtYXR0ZWRNZXNzYWdlXHJcbiAgICAgICAgICBpZD1cIndhcmVob3VzZS1tYW5hZ2VtZW50LmltcG9ydC12b3VjaGVyLnF1YW50aXR5XCJcclxuICAgICAgICAgIGRlZmF1bHRNZXNzYWdlPVwidW5rbm93blwiXHJcbiAgICAgICAgLz5cclxuICAgICAgKSxcclxuICAgICAgZGF0YUluZGV4OiAncXR5JyxcclxuICAgICAgc2VhcmNoOiBmYWxzZSxcclxuICAgICAgcmVuZGVyRm9ybUl0ZW0oc2NoZW1hLCBjb25maWcsIGZvcm0sIGFjdGlvbikge1xyXG4gICAgICAgIHJldHVybiAoXHJcbiAgICAgICAgICA8SW5wdXROdW1iZXJcclxuICAgICAgICAgICAgc3R5bGU9e3sgd2lkdGg6ICcxMDAlJyB9fVxyXG4gICAgICAgICAgICBmb3JtYXR0ZXI9eyh2YWx1ZSkgPT4gYCR7dmFsdWV9YC5yZXBsYWNlKC9cXEIoPz0oXFxkezN9KSsoPyFcXGQpKS9nLCAnLCcpfVxyXG4gICAgICAgICAgICBwYXJzZXI9eyh2YWx1ZSkgPT4gdmFsdWUhLnJlcGxhY2UoL1xcJFxccz98KCwqKS9nLCAnJyl9XHJcbiAgICAgICAgICAgIHN0ZXA9XCIwLjAxXCJcclxuICAgICAgICAgIC8+XHJcbiAgICAgICAgKTtcclxuICAgICAgfSxcclxuICAgICAgd2lkdGg6IDgwLFxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgdGl0bGU6IDxGb3JtYXR0ZWRNZXNzYWdlIGlkPVwiY29tbW9uLnVuaXRcIiAvPixcclxuICAgICAgZWRpdGFibGU6IGZhbHNlLFxyXG4gICAgICBkYXRhSW5kZXg6ICdjb252ZXJzaW9uX2ZhY3RvcicsXHJcbiAgICAgIHJlbmRlcjogKGRvbSwgZW50aXR5LCBpbmRleCwgYWN0aW9uLCBzY2hlbWEpID0+IFtcclxuICAgICAgICA8UHJvRm9ybVNlbGVjdFxyXG4gICAgICAgICAgZm9ybUl0ZW1Qcm9wcz17e1xyXG4gICAgICAgICAgICBzdHlsZToge1xyXG4gICAgICAgICAgICAgIG1hcmdpbkJvdHRvbTogMCxcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgIH19XHJcbiAgICAgICAgICBrZXk9eydjb252ZXJzaW9uX2ZhY3Rvcid9XHJcbiAgICAgICAgICBmaWVsZFByb3BzPXt7XHJcbiAgICAgICAgICAgIGRlZmF1bHRWYWx1ZTogZW50aXR5LnVvbV9sYWJlbCxcclxuICAgICAgICAgIH19XHJcbiAgICAgICAgICBwbGFjZWhvbGRlcj1cIkxv4bqhaSDEkeG6v21cIlxyXG4gICAgICAgICAgcmVxdWVzdD17YXN5bmMgKCkgPT4ge1xyXG4gICAgICAgICAgICBjb25zdCByZXMgPVxyXG4gICAgICAgICAgICAgIChhd2FpdCBnZXREZXRhaWxzUHJvZHVjdEl0ZW1WMyh7XHJcbiAgICAgICAgICAgICAgICBuYW1lOiBlbnRpdHkuaXRlbV9jb2RlIHx8ICcnLFxyXG4gICAgICAgICAgICAgIH0pKSB8fCBbXTtcclxuICAgICAgICAgICAgY29uc3QgZm9ybWF0dGVkX3JlcyA9XHJcbiAgICAgICAgICAgICAgcmVzLmRhdGEudW9tcz8ubWFwKChpdGVtKSA9PiAoe1xyXG4gICAgICAgICAgICAgICAgbGFiZWw6IGl0ZW0udW9tX25hbWUsXHJcbiAgICAgICAgICAgICAgICB2YWx1ZTogaXRlbS5jb252ZXJzaW9uX2ZhY3RvcixcclxuICAgICAgICAgICAgICB9KSkgfHwgW107XHJcbiAgICAgICAgICAgIGNvbnN0IG9wdGlvbnMgPSBbXHJcbiAgICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgICAgbGFiZWw6IGVudGl0eS51b21fbGFiZWwsXHJcbiAgICAgICAgICAgICAgICB2YWx1ZTogMSxcclxuICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICBdO1xyXG4gICAgICAgICAgICByZXR1cm4gWy4uLm9wdGlvbnMsIC4uLmZvcm1hdHRlZF9yZXNdO1xyXG4gICAgICAgICAgfX1cclxuICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlU2VsZWN0VW5pdChlbnRpdHkua2V5LCArZSl9XHJcbiAgICAgICAgLz4sXHJcbiAgICAgIF0sXHJcbiAgICAgIHdpZHRoOiA4MCxcclxuICAgIH0sXHJcblxyXG4gICAgLy8ge1xyXG4gICAgLy8gICB0aXRsZTogPEZvcm1hdHRlZE1lc3NhZ2UgaWQ9XCJ3YXJlaG91c2UtbWFuYWdlbWVudC5pbXBvcnQtdm91Y2hlci5yYXRlXCIgZGVmYXVsdE1lc3NhZ2U9XCJ1bmtub3duXCIgLz4sXHJcbiAgICAvLyAgIGRhdGFJbmRleDogJ3JhdGUnLFxyXG4gICAgLy8gICBzZWFyY2g6IGZhbHNlLFxyXG4gICAgLy8gICB3aWR0aDogODAsXHJcbiAgICAvLyAgIHJlbmRlckZvcm1JdGVtKHNjaGVtYSwgY29uZmlnLCBmb3JtLCBhY3Rpb24pIHtcclxuICAgIC8vICAgICByZXR1cm4gKFxyXG4gICAgLy8gICAgICAgPElucHV0TnVtYmVyXHJcbiAgICAvLyAgICAgICAgIHN0eWxlPXt7IHdpZHRoOiAnMTAwJScgfX1cclxuICAgIC8vICAgICAgICAgZm9ybWF0dGVyPXsodmFsdWUpID0+IGAke3ZhbHVlfWAucmVwbGFjZSgvXFxCKD89KFxcZHszfSkrKD8hXFxkKSkvZywgJywnKX1cclxuICAgIC8vICAgICAgICAgcGFyc2VyPXsodmFsdWUpID0+IHZhbHVlIS5yZXBsYWNlKC9cXCRcXHM/fCgsKikvZywgJycpfVxyXG4gICAgLy8gICAgICAgICBzdGVwPVwiMC4wMVwiXHJcbiAgICAvLyAgICAgICAvPlxyXG4gICAgLy8gICAgICk7XHJcbiAgICAvLyAgIH0sXHJcbiAgICAvLyB9LFxyXG4gICAgLy8ge1xyXG4gICAgLy8gICB0aXRsZTogPEZvcm1hdHRlZE1lc3NhZ2UgaWQ9XCJ3YXJlaG91c2UtbWFuYWdlbWVudC5pbXBvcnQtdm91Y2hlci5wcmljZVwiIGRlZmF1bHRNZXNzYWdlPVwidW5rbm93blwiIC8+LFxyXG4gICAgLy8gICBkYXRhSW5kZXg6ICd0b3RhbF9wcmljZScsXHJcbiAgICAvLyAgIGVkaXRhYmxlOiBmYWxzZSxcclxuICAgIC8vICAgcmVuZGVyOiAodGV4dCwgcmVjb3JkKSA9PiBudW1lcmFsKHJlY29yZC50b3RhbF9wcmljZSkuZm9ybWF0KCcwLDAuMDAnKSwgLy8gxJDhu4tuaCBk4bqhbmcgc+G7kSDhu58gxJHDonlcclxuICAgIC8vICAgd2lkdGg6IDgwLFxyXG4gICAgLy8gfSxcclxuICAgIHtcclxuICAgICAgdGl0bGU6ICdBY3Rpb24nLFxyXG4gICAgICBkYXRhSW5kZXg6ICdrZXknLFxyXG4gICAgICBlZGl0YWJsZTogZmFsc2UsXHJcbiAgICAgIHJlbmRlcjogKGluZGV4LCByZWNvcmQsIF8sIGFjdGlvbikgPT4gW1xyXG4gICAgICAgIDxCdXR0b24gaWNvbj17PERlbGV0ZU91dGxpbmVkIC8+fSBrZXk9XCJkZWxldGVcIiBvbkNsaWNrPXsoKSA9PiBoYW5kbGVEZWxldGVJdGVtKGluZGV4KX0gLz4sXHJcbiAgICAgIF0sXHJcbiAgICAgIHdpZHRoOiA4MCxcclxuICAgIH0sXHJcbiAgXTtcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgc2V0RWRpdGFibGVSb3dLZXlzKGRhdGEubWFwKChpdGVtKSA9PiBpdGVtLmtleSkpO1xyXG4gIH0sIFtkYXRhXSk7XHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGFjdGlvblJlZi5jdXJyZW50Py5yZWxvYWQoKTtcclxuICB9LCBbZGF0YSwgZWRpdGFibGVLZXlzXSk7XHJcbiAgcmV0dXJuIChcclxuICAgIDxFZGl0YWJsZVByb1RhYmxlPElDcmVhdGVCb21JdGVtPlxyXG4gICAgICBzdHlsZT17eyBtaW5XaWR0aDogJzEwMCUnIH19XHJcbiAgICAgIGNvbHVtbnM9e2NvbHVtbnN9XHJcbiAgICAgIGFjdGlvblJlZj17YWN0aW9uUmVmfVxyXG4gICAgICBjYXJkQm9yZGVyZWRcclxuICAgICAgcmVxdWVzdD17YXN5bmMgKHBhcmFtcyA9IHt9LCBzb3J0LCBmaWx0ZXIpID0+IHtcclxuICAgICAgICByZXR1cm4ge1xyXG4gICAgICAgICAgZGF0YSxcclxuICAgICAgICAgIHN1Y2Nlc3M6IHRydWUsXHJcbiAgICAgICAgICB0b3RhbDogZGF0YS5sZW5ndGgsXHJcbiAgICAgICAgfTtcclxuICAgICAgfX1cclxuICAgICAgZWRpdGFibGU9e3tcclxuICAgICAgICB0eXBlOiAnbXVsdGlwbGUnLFxyXG4gICAgICAgIGVkaXRhYmxlS2V5cyxcclxuICAgICAgICBhY3Rpb25SZW5kZXI6IChyb3csIGNvbmZpZywgZGVmYXVsdERvbXMpID0+IHtcclxuICAgICAgICAgIHJldHVybiBbZGVmYXVsdERvbXMuZGVsZXRlXTtcclxuICAgICAgICB9LFxyXG4gICAgICAgIG9uVmFsdWVzQ2hhbmdlOiAocmVjb3JkLCByZWNvcmRMaXN0KSA9PiB7XHJcbiAgICAgICAgICAvLyBoYW5kbGVVcGRhdGVRdWFudGl0eShyZWNvcmQpO1xyXG4gICAgICAgICAgc2V0RGF0YShyZWNvcmRMaXN0KTtcclxuICAgICAgICB9LFxyXG4gICAgICAgIG9uQ2hhbmdlOiBzZXRFZGl0YWJsZVJvd0tleXMsXHJcbiAgICAgIH19XHJcbiAgICAgIHJlY29yZENyZWF0b3JQcm9wcz17ZmFsc2V9XHJcbiAgICAgIHJvd0tleT1cImtleVwiXHJcbiAgICAgIHNlYXJjaD17ZmFsc2V9XHJcbiAgICAgIG9wdGlvbnM9e3tcclxuICAgICAgICBzZXR0aW5nOiB7XHJcbiAgICAgICAgICBsaXN0c0hlaWdodDogNDAwLFxyXG4gICAgICAgIH0sXHJcbiAgICAgIH19XHJcbiAgICAgIHBhZ2luYXRpb249e3tcclxuICAgICAgICBkZWZhdWx0UGFnZVNpemU6IDIwLFxyXG4gICAgICAgIHNob3dTaXplQ2hhbmdlcjogdHJ1ZSxcclxuICAgICAgICBwYWdlU2l6ZU9wdGlvbnM6IFsnMjAnLCAnNTAnLCAnMTAwJ10sXHJcbiAgICAgIH19XHJcbiAgICAgIHRvb2xiYXI9e3tcclxuICAgICAgICBtdWx0aXBsZUxpbmU6IGZhbHNlLFxyXG4gICAgICB9fVxyXG4gICAgICB0b29sQmFyUmVuZGVyPXtmYWxzZX1cclxuICAgICAgc2l6ZT1cInNtYWxsXCJcclxuICAgICAgZGF0ZUZvcm1hdHRlcj1cInN0cmluZ1wiXHJcbiAgICAvPlxyXG4gICk7XHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBDcmVhdGVCb21Nb2RhbFRhYmxlO1xyXG4iLCJpbXBvcnQgeyBERUZBVUxUX0RBVEVfQU5EX0hIX01NX0ZPUk1BVCB9IGZyb20gJ0AvY29tbW9uL2NvbnRhbnN0L2NvbnN0YW5zdCc7XHJcbmltcG9ydCBGb3JtVHJlZVNlbGVjdFNlYXJjaCBmcm9tICdAL2NvbXBvbmVudHMvRm9ybS9Gb3JtVHJlZVNlbGVjdFNlYXJjaCc7XHJcbmltcG9ydCBDcmVhdGVQcm9kdWN0V2l0aFRyaWdnZXIgZnJvbSAnQC9wYWdlcy9JbnZlbnRvcnlNYW5hZ2VtZW50VjMvUHJvZHVjdE1hbmFnZW1lbnQvUHJvZHVjdHMvQ3JlYXRlUHJvZHVjdC12Mic7XHJcbmltcG9ydCB7IHNhdmVCb20gfSBmcm9tICdAL3NlcnZpY2VzL0ludmVudG9yeU1hbmFnZW1lbnRWMy9ib20nO1xyXG5pbXBvcnQgeyBnZXREZXRhaWxzUHJvZHVjdEl0ZW1WMyB9IGZyb20gJ0Avc2VydmljZXMvSW52ZW50b3J5TWFuYWdlbWVudFYzL3Byb2R1Y3QtaXRlbSc7XHJcbmltcG9ydCB7IGdldEl0ZW1Hcm91cExpc3QsIGdldEl0ZW1MaXN0IH0gZnJvbSAnQC9zZXJ2aWNlcy9zdG9jay9pdGVtJztcclxuaW1wb3J0IHsgSVN0b2NrSXRlbSwgSVN0b2NrSXRlbUdyb3VwIH0gZnJvbSAnQC90eXBlcy93YXJlaG91c2UudHlwZSc7XHJcbmltcG9ydCB7IFBsdXNPdXRsaW5lZCB9IGZyb20gJ0BhbnQtZGVzaWduL2ljb25zJztcclxuaW1wb3J0IHtcclxuICBNb2RhbEZvcm0sXHJcbiAgUHJvRm9ybSxcclxuICBQcm9Gb3JtRGlnaXQsXHJcbiAgUHJvRm9ybVNlbGVjdCxcclxuICBQcm9Gb3JtVGV4dEFyZWEsXHJcbn0gZnJvbSAnQGFudC1kZXNpZ24vcHJvLWNvbXBvbmVudHMnO1xyXG5pbXBvcnQgeyBGb3JtYXR0ZWRNZXNzYWdlLCB1c2VJbnRsLCB1c2VNb2RlbCB9IGZyb20gJ0B1bWlqcy9tYXgnO1xyXG5pbXBvcnQgeyBBcHAsIEJ1dHRvbiwgQ29sLCBEaXZpZGVyLCBSb3csIFNwYWNlLCBUeXBvZ3JhcGh5IH0gZnJvbSAnYW50ZCc7XHJcbmltcG9ydCB7IHVzZUZvcm0gfSBmcm9tICdhbnRkL2VzL2Zvcm0vRm9ybSc7XHJcbmltcG9ydCBtb21lbnQgZnJvbSAnbW9tZW50JztcclxuaW1wb3J0IHsgdXNlRWZmZWN0LCB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcclxuaW1wb3J0IENyZWF0ZUJvbU1vZGFsVGFibGUsIHsgSUNyZWF0ZUJvbUl0ZW0gfSBmcm9tICcuL0NyZWF0ZUJvbU1vZGFsVGFibGUnO1xyXG5jb25zdCB7IFRpdGxlIH0gPSBUeXBvZ3JhcGh5O1xyXG5pbnRlcmZhY2UgSVRyZWVOb2RlIHtcclxuICB0aXRsZTogc3RyaW5nO1xyXG4gIHZhbHVlOiBzdHJpbmc7XHJcbiAga2V5OiBzdHJpbmc7XHJcbiAgY2hpbGRyZW4/OiBJVHJlZU5vZGVbXTtcclxufVxyXG5pbnRlcmZhY2UgSUZvcm1EYXRhIHtcclxuICBpdGVtczogSUNyZWF0ZUJvbUl0ZW1bXTtcclxuICBib21fZGVzY3JpcHRpb246IHN0cmluZztcclxuICBjcmVhdGVkX2l0ZW1fdW5pdDogbnVtYmVyO1xyXG4gIGNyZWF0ZWRfaXRlbV9xdWFudGl0eTogbnVtYmVyO1xyXG4gIGNyZWF0ZWRfaXRlbTogc3RyaW5nW107XHJcbn1cclxuaW50ZXJmYWNlIFByb3BzIHtcclxuICBvblN1Y2Nlc3M/OiBhbnk7XHJcbiAgaW1wb3J0RnJvbVRhc2s/OiBib29sZWFuO1xyXG4gIGRhdGFQcm9wcz86IGFueTtcclxuICB0YXNrSUQ/OiBzdHJpbmc7XHJcbn1cclxuY29uc3QgQ3JlYXRlQm9tTW9kYWwgPSAoeyBvblN1Y2Nlc3MsIGltcG9ydEZyb21UYXNrLCBkYXRhUHJvcHMsIHRhc2tJRCB9OiBQcm9wcykgPT4ge1xyXG4gIGNvbnN0IFtzdWJtaXR0aW5nLCBzZXRTdWJtaXR0aW5nXSA9IHVzZVN0YXRlPGJvb2xlYW4+KGZhbHNlKTtcclxuICBjb25zdCBbdHJlZURhdGEsIHNldFRyZWVEYXRhXSA9IHVzZVN0YXRlPElUcmVlTm9kZVtdPigpO1xyXG4gIGNvbnN0IFtzZWxlY3RlZEl0ZW1zLCBzZXRTZWxlY3RlZEl0ZW1zXSA9IHVzZVN0YXRlPElDcmVhdGVCb21JdGVtW10+KFtdKTtcclxuICBjb25zdCBbaXRlbXMsIHNldEl0ZW1zXSA9IHVzZVN0YXRlPElTdG9ja0l0ZW1bXT4oW10pO1xyXG4gIGNvbnN0IFtjcmVhdGVkSXRlbSwgc2V0Q3JlYXRlZEl0ZW1dID0gdXNlU3RhdGU8SVN0b2NrSXRlbT4oKTtcclxuICBjb25zdCBbZm9ybV0gPSB1c2VGb3JtKCk7XHJcbiAgLy9jdXJyZW50IHVzZXJcclxuICBjb25zdCB7IGluaXRpYWxTdGF0ZSB9ID0gdXNlTW9kZWwoYEBAaW5pdGlhbFN0YXRlYCk7XHJcbiAgY29uc3QgY3VyZW50VXNlciA9IGluaXRpYWxTdGF0ZT8uY3VycmVudFVzZXI7XHJcbiAgY29uc3QgeyBtZXNzYWdlIH0gPSBBcHAudXNlQXBwKCk7XHJcblxyXG4gIC8vZ2V0IGxpc3QgaXRlbXMgaW4gZ3JvdXBcclxuICBjb25zdCBnZXRJdGVtVHJlZURhdGEgPSBhc3luYyAoKSA9PiB7XHJcbiAgICAvLyBIZWxwZXIgZnVuY3Rpb25cclxuICAgIGZ1bmN0aW9uIGdyb3VwQnlJdGVtR3JvdXBXaXRoSXRlbUdyb3VwRGF0YShcclxuICAgICAgc3RvY2tJdGVtczogSVN0b2NrSXRlbVtdLFxyXG4gICAgICBzdG9ja0l0ZW1Hcm91cHM6IElTdG9ja0l0ZW1Hcm91cFtdLFxyXG4gICAgKTogUmVjb3JkPHN0cmluZywgeyBpdGVtczogSVN0b2NrSXRlbVtdOyBpdGVtR3JvdXA6IElTdG9ja0l0ZW1Hcm91cCB9PiB7XHJcbiAgICAgIGNvbnN0IGdyb3VwZWRJdGVtczogUmVjb3JkPHN0cmluZywgeyBpdGVtczogSVN0b2NrSXRlbVtdOyBpdGVtR3JvdXA6IElTdG9ja0l0ZW1Hcm91cCB9PiA9IHt9O1xyXG5cclxuICAgICAgc3RvY2tJdGVtcy5mb3JFYWNoKChpdGVtKSA9PiB7XHJcbiAgICAgICAgY29uc3QgaXRlbUdyb3VwID0gaXRlbS5pdGVtX2dyb3VwO1xyXG4gICAgICAgIGlmICghZ3JvdXBlZEl0ZW1zW2l0ZW1Hcm91cF0pIHtcclxuICAgICAgICAgIGdyb3VwZWRJdGVtc1tpdGVtR3JvdXBdID0geyBpdGVtczogW10sIGl0ZW1Hcm91cDoge30gYXMgSVN0b2NrSXRlbUdyb3VwIH07XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGdyb3VwZWRJdGVtc1tpdGVtR3JvdXBdLml0ZW1zLnB1c2goaXRlbSk7XHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgLy8gTm93IGFkZCBpdGVtIGdyb3VwIGRhdGFcclxuICAgICAgZm9yIChjb25zdCBpdGVtR3JvdXAgb2Ygc3RvY2tJdGVtR3JvdXBzKSB7XHJcbiAgICAgICAgY29uc3QgaXRlbUdyb3VwTmFtZSA9IGl0ZW1Hcm91cC5pdGVtX2dyb3VwX25hbWU7XHJcbiAgICAgICAgaWYgKGdyb3VwZWRJdGVtc1tpdGVtR3JvdXBOYW1lXSkge1xyXG4gICAgICAgICAgZ3JvdXBlZEl0ZW1zW2l0ZW1Hcm91cE5hbWVdLml0ZW1Hcm91cCA9IGl0ZW1Hcm91cDtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIHJldHVybiBncm91cGVkSXRlbXM7XHJcbiAgICB9XHJcbiAgICBjb25zdCBkYXRhID0gYXdhaXQgZ2V0SXRlbUxpc3Qoe30pO1xyXG4gICAgY29uc3QgZGF0YUdyb3VwID0gYXdhaXQgZ2V0SXRlbUdyb3VwTGlzdCh7fSk7XHJcbiAgICBjb25zdCBkYXRhTWFwID0gZ3JvdXBCeUl0ZW1Hcm91cFdpdGhJdGVtR3JvdXBEYXRhKGRhdGEuZGF0YSwgZGF0YUdyb3VwLmRhdGEpO1xyXG4gICAgaWYgKGRhdGFNYXApIHtcclxuICAgICAgY29uc3QgZ2VuZXJhdGVkRGF0YSA9IE9iamVjdC5lbnRyaWVzKGRhdGFNYXApLm1hcCgoW2l0ZW1Hcm91cCwgZ3JvdXBEYXRhXSkgPT4gKHtcclxuICAgICAgICB0aXRsZTogZ3JvdXBEYXRhLml0ZW1Hcm91cD8ubGFiZWwgfHwgJycsXHJcbiAgICAgICAgdmFsdWU6IGl0ZW1Hcm91cCxcclxuICAgICAgICBrZXk6IGl0ZW1Hcm91cCxcclxuICAgICAgICBjaGlsZHJlbjogZ3JvdXBEYXRhLml0ZW1zLm1hcCgoaXRlbSkgPT4gKHtcclxuICAgICAgICAgIHRpdGxlOiBpdGVtLmxhYmVsIHx8ICcnLFxyXG4gICAgICAgICAgdmFsdWU6IGl0ZW0ubmFtZSB8fCAnJyxcclxuICAgICAgICAgIGtleTogaXRlbS5uYW1lIHx8ICcnLFxyXG4gICAgICAgIH0pKSxcclxuICAgICAgfSkpO1xyXG4gICAgICBzZXRUcmVlRGF0YShnZW5lcmF0ZWREYXRhKTtcclxuICAgICAgc2V0SXRlbXMoZGF0YS5kYXRhKTtcclxuICAgIH1cclxuICB9O1xyXG4gIGNvbnN0IGhhbmRsZUNyZWF0ZU5ld0l0ZW0gPSBhc3luYyAoKSA9PiB7XHJcbiAgICBhd2FpdCBnZXRJdGVtVHJlZURhdGEoKTtcclxuICAgIGZvcm0ucmVzZXRGaWVsZHMoWydpdGVtcyddKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVBZGRJdGVtcyA9IGFzeW5jICgpID0+IHtcclxuICAgIGNvbnN0IGl0ZW1faWRzOiBzdHJpbmdbXSB8IHVuZGVmaW5lZCA9IGZvcm0uZ2V0RmllbGRWYWx1ZSgnaXRlbXMnKTtcclxuICAgIGlmICghaXRlbV9pZHMpIHtcclxuICAgICAgbWVzc2FnZS5lcnJvcihgVnVpIGzDsm5nIGNo4buNbiBow6BuZyBow7NhYCk7XHJcbiAgICAgIHJldHVybjtcclxuICAgIH1cclxuICAgIGNvbnN0IGZpbHRlcmVkX2l0ZW1zOiAoSVN0b2NrSXRlbSAmIHsgYWN0dWFsX3F0eT86IG51bWJlciB9KVtdID0gaXRlbXMuZmlsdGVyKChpdGVtKSA9PlxyXG4gICAgICBpdGVtX2lkcy5pbmNsdWRlcyhpdGVtLm5hbWUpLFxyXG4gICAgKTtcclxuXHJcbiAgICAvLyBmb3IgKGNvbnN0IGl0ZW0gb2YgZmlsdGVyZWRfaXRlbXMpIHtcclxuICAgIC8vICAgY29uc3QgaW52ZW50b3J5ID1cclxuICAgIC8vICAgICAoXHJcbiAgICAvLyAgICAgICBhd2FpdCBnZXRJbnZlbnRvcnlWMyh7XHJcbiAgICAvLyAgICAgICAgIHdhcmVob3VzZTogc2VsZWN0ZWRXYXJlaG91c2UsXHJcbiAgICAvLyAgICAgICAgIGl0ZW1fbmFtZTogaXRlbS5pdGVtX25hbWUsXHJcbiAgICAvLyAgICAgICAgIHBhZ2U6ICcxJyxcclxuICAgIC8vICAgICAgICAgc2l6ZTogJzEnLFxyXG4gICAgLy8gICAgICAgfSlcclxuICAgIC8vICAgICApLmRhdGE/LmF0KDApPy5hY3R1YWxfcXR5IHx8IDA7XHJcbiAgICAvLyAgIGl0ZW0uYWN0dWFsX3F0eSA9IGludmVudG9yeTtcclxuICAgIC8vIH1cclxuICAgIGNvbnN0IGZvcm1hdHRlZF9pdGVtczogSUNyZWF0ZUJvbUl0ZW1bXSA9IGZpbHRlcmVkX2l0ZW1zLm1hcCgoaXRlbSkgPT4gKHtcclxuICAgICAgY29udmVyc2lvbl9mYWN0b3I6IDEsXHJcbiAgICAgIGl0ZW1fY29kZTogaXRlbS5uYW1lLFxyXG4gICAgICBpdGVtX25hbWU6IGl0ZW0uaXRlbV9uYW1lLFxyXG4gICAgICBxdHk6IDAsXHJcbiAgICAgIGl0ZW1fbGFiZWw6IGl0ZW0ubGFiZWwsXHJcbiAgICAgIGtleTogRGF0ZS5ub3coKSAqIDEwMCArIE1hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIDEwMCkudG9TdHJpbmcoKSxcclxuICAgICAgdW9tX2xhYmVsOiBpdGVtLnVvbV9sYWJlbCxcclxuICAgIH0pKTtcclxuICAgIHNldFNlbGVjdGVkSXRlbXMoKHByZXYpID0+IFsuLi5mb3JtYXR0ZWRfaXRlbXMsIC4uLnByZXZdKTtcclxuICAgIGZvcm0uc2V0RmllbGRWYWx1ZSgnaXRlbXMnLCBbXSk7XHJcbiAgICByZXR1cm47XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgaGFuZGxlQ2hvc2VDcmVhdGVkSXRlbSA9IGFzeW5jIChlOiBhbnkpID0+IHtcclxuICAgIGNvbnN0IGl0ZW06IElTdG9ja0l0ZW0gfCB1bmRlZmluZWQgPSBpdGVtcy5maWx0ZXIoKGl0ZW0pID0+IGl0ZW0ubmFtZSA9PT0gZSkuYXQoMCk7XHJcbiAgICBjb25zb2xlLmxvZyhlLCBpdGVtKTtcclxuICAgIHNldENyZWF0ZWRJdGVtKGl0ZW0pO1xyXG4gICAgZm9ybS5yZXNldEZpZWxkcyhbJ2NyZWF0ZWRfaXRlbV91bml0J10pO1xyXG4gIH07XHJcblxyXG4gIC8qKlxyXG4gICAqIEhlbHBlciBmdW5jdGlvbnMgZm9yIGhhbmRsZUZpbmlzaFxyXG4gICAqL1xyXG4gIGNvbnN0IHZhbGlkYXRlSXRlbXMgPSAoc2VsZWN0ZWRJdGVtczogSUNyZWF0ZUJvbUl0ZW1bXSkgPT4ge1xyXG4gICAgZm9yIChjb25zdCBpdGVtIG9mIHNlbGVjdGVkSXRlbXMpIHtcclxuICAgICAgaWYgKGl0ZW0ucXR5ID09PSBudWxsKSB7XHJcbiAgICAgICAgbWVzc2FnZS5lcnJvcihgSMOjeSBjdW5nIGPhuqVwIMSR4bqneSDEkeG7pyBz4buRIGzGsOG7o25nIGPhu6dhIGjDoG5nIGjDs2FgKTtcclxuICAgICAgICByZXR1cm4gZmFsc2U7XHJcbiAgICAgIH1cclxuICAgICAgaWYgKGl0ZW0ucXR5IDw9IDApIHtcclxuICAgICAgICBtZXNzYWdlLmVycm9yKGBT4buRIGzGsOG7o25nIGPhu6dhIGjDoG5nIGjDs2EgcGjhuqNpIGzhu5tuIGjGoW4gMGApO1xyXG4gICAgICAgIHJldHVybiBmYWxzZTtcclxuICAgICAgfVxyXG4gICAgfVxyXG4gICAgcmV0dXJuIHRydWU7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgY3JlYXRlQm9tUGF5bG9hZCA9ICh2YWx1ZXM6IElGb3JtRGF0YSwgc2VsZWN0ZWRJdGVtczogSUNyZWF0ZUJvbUl0ZW1bXSkgPT4ge1xyXG4gICAgY29uc3QgY29tbW9uRmllbGRzID0ge1xyXG4gICAgICBib21fZGVzY3JpcHRpb246IHZhbHVlcy5ib21fZGVzY3JpcHRpb24sXHJcbiAgICAgIGl0ZW06IGNyZWF0ZWRJdGVtPy5uYW1lLFxyXG4gICAgICBxdWFudGl0eTogdmFsdWVzLmNyZWF0ZWRfaXRlbV9xdWFudGl0eSAqIHZhbHVlcy5jcmVhdGVkX2l0ZW1fdW5pdCxcclxuXHJcbiAgICAgIGl0ZW1zOiBzZWxlY3RlZEl0ZW1zLm1hcCgoaXRlbSkgPT4gKHtcclxuICAgICAgICBjb252ZXJzaW9uX2ZhY3RvcjogaXRlbS5jb252ZXJzaW9uX2ZhY3RvcixcclxuICAgICAgICBpdGVtX2NvZGU6IGl0ZW0uaXRlbV9jb2RlLFxyXG4gICAgICAgIHF0eTogaXRlbS5xdHksXHJcbiAgICAgIH0pKSxcclxuICAgIH07XHJcblxyXG4gICAgcmV0dXJuIHtcclxuICAgICAgLi4uY29tbW9uRmllbGRzLFxyXG4gICAgICBkb2NzdGF0dXM6IDAsXHJcbiAgICAgIGRvY3R5cGU6ICdCT00nLFxyXG4gICAgICBfX2lzbG9jYWw6IDEsXHJcbiAgICAgIF9fdW5zYXZlZDogMSxcclxuICAgICAgb3duZXI6ICdBZG1pbmlzdHJhdG9yJyxcclxuICAgICAgY29tcGFueTogJ1ZJSVMnLFxyXG4gICAgICBpc19hY3RpdmU6IDEsXHJcbiAgICAgIGlzX2RlZmF1bHQ6IDEsXHJcbiAgICAgIHNldF9yYXRlX29mX3N1Yl9hc3NlbWJseV9pdGVtX2Jhc2VkX29uX2JvbTogMCxcclxuICAgICAgaXRlbXM6IGNvbW1vbkZpZWxkcy5pdGVtcy5tYXAoKGl0ZW0pID0+ICh7XHJcbiAgICAgICAgLi4uaXRlbSxcclxuICAgICAgICBkb2NzdGF0dXM6IDAsXHJcbiAgICAgICAgZG9jdHlwZTogJ0JPTSBJdGVtJyxcclxuICAgICAgICBuYW1lOiAnbmV3LWJvbS1pdGVtJyArIE1hdGgucmFuZG9tKCkudG9TdHJpbmcoMzYpLnN1YnN0cmluZygyLCA4KSxcclxuICAgICAgICBfX2lzbG9jYWw6IDEsXHJcbiAgICAgICAgX191bnNhdmVkOiAxLFxyXG4gICAgICAgIG93bmVyOiAnQWRtaW5pc3RyYXRvcicsXHJcbiAgICAgICAgZG9fbm90X2V4cGxvZGU6IDAsXHJcbiAgICAgICAgYWxsb3dfYWx0ZXJuYXRpdmVfaXRlbTogMCxcclxuICAgICAgICBwYXJlbnRmaWVsZDogJ2l0ZW1zJyxcclxuICAgICAgICBwYXJlbnR0eXBlOiAnQk9NJyxcclxuICAgICAgfSkpLFxyXG4gICAgfTtcclxuICB9O1xyXG5cclxuICAvL3J1biB3aGVuIHN1Ym1pdCBGb3JtXHJcbiAgY29uc3QgaGFuZGxlRmluaXNoID0gYXN5bmMgKHZhbHVlczogSUZvcm1EYXRhKSA9PiB7XHJcbiAgICBzZXRTdWJtaXR0aW5nKHRydWUpO1xyXG4gICAgdHJ5IHtcclxuICAgICAgaWYgKCF2YWxpZGF0ZUl0ZW1zKHNlbGVjdGVkSXRlbXMpKSB7XHJcbiAgICAgICAgcmV0dXJuO1xyXG4gICAgICB9XHJcbiAgICAgIGNvbnNvbGUubG9nKHsgdmFsdWVzLCBzZWxlY3RlZEl0ZW1zIH0pO1xyXG5cclxuICAgICAgY29uc3QgYm9tUGF5bG9hZCA9IGNyZWF0ZUJvbVBheWxvYWQodmFsdWVzLCBzZWxlY3RlZEl0ZW1zKTtcclxuICAgICAgYXdhaXQgc2F2ZUJvbShib21QYXlsb2FkKTtcclxuXHJcbiAgICAgIG1lc3NhZ2Uuc3VjY2VzcyhgU3VjY2Vzc2ApO1xyXG4gICAgICBvblN1Y2Nlc3M/LigpO1xyXG4gICAgICBmb3JtLnJlc2V0RmllbGRzKCk7XHJcbiAgICAgIHNldFNlbGVjdGVkSXRlbXMoW10pO1xyXG4gICAgICByZXR1cm4gdHJ1ZTtcclxuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcclxuICAgICAgY29uc29sZS5sb2coeyBlcnJvciB9KTtcclxuICAgICAgbWVzc2FnZS5lcnJvcihgRXJyb3Igd2l0aCBCb20gU2F2aW5nYCk7XHJcbiAgICAgIHJldHVybjtcclxuICAgIH0gZmluYWxseSB7XHJcbiAgICAgIHNldFN1Ym1pdHRpbmcoZmFsc2UpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIGNvbnN0IGhhbmRsZUFib3J0ID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgaWYgKCFpbXBvcnRGcm9tVGFzaykge1xyXG4gICAgICBzZXRTZWxlY3RlZEl0ZW1zKFtdKTtcclxuICAgIH1cclxuICB9O1xyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBjb25zdCBmZXRjaERhdGEgPSBhc3luYyAoKSA9PiB7XHJcbiAgICAgIGdldEl0ZW1UcmVlRGF0YSgpO1xyXG4gICAgfTtcclxuICAgIGZldGNoRGF0YSgpO1xyXG4gIH0sIFtdKTtcclxuXHJcbiAgLy8gdXNlRWZmZWN0KCgpID0+IHtcclxuICAvLyAgIGlmIChpbXBvcnRGcm9tVGFzaykge1xyXG4gIC8vICAgICBsb2FkRGF0YSgpO1xyXG4gIC8vICAgfVxyXG4gIC8vIH0sIFtkYXRhUHJvcHMsIHNlbGVjdGVkV2FyZWhvdXNlXSk7XHJcblxyXG4gIGNvbnN0IHsgZm9ybWF0TWVzc2FnZSB9ID0gdXNlSW50bCgpO1xyXG4gIHJldHVybiAoXHJcbiAgICA8TW9kYWxGb3JtPElGb3JtRGF0YT5cclxuICAgICAgdGl0bGU9ezxGb3JtYXR0ZWRNZXNzYWdlIGlkPVwiYm9tLW1hbmFnZW1lbnQuY3JlYXRpb25cIiAvPn1cclxuICAgICAgbW9kYWxQcm9wcz17e1xyXG4gICAgICAgIG9uQ2FuY2VsOiBoYW5kbGVBYm9ydCxcclxuICAgICAgICBkZXN0cm95T25DbG9zZTogdHJ1ZSxcclxuICAgICAgfX1cclxuICAgICAgdHJpZ2dlcj17XHJcbiAgICAgICAgPEJ1dHRvbiBpY29uPXs8UGx1c091dGxpbmVkIC8+fSB0eXBlPVwicHJpbWFyeVwiPlxyXG4gICAgICAgICAgeycnfVxyXG4gICAgICAgICAgPEZvcm1hdHRlZE1lc3NhZ2UgaWQ9XCJib20tbWFuYWdlbWVudC5jcmVhdGlvblwiIC8+XHJcbiAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgIH1cclxuICAgICAgd2lkdGg9ezgwMH1cclxuICAgICAgZm9ybT17Zm9ybX1cclxuICAgICAgbGF5b3V0PVwidmVydGljYWxcIlxyXG4gICAgICByb3dQcm9wcz17e1xyXG4gICAgICAgIGd1dHRlcjogWzE2LCAwXSxcclxuICAgICAgfX1cclxuICAgICAgc3VibWl0dGVyPXt7XHJcbiAgICAgICAgcmVuZGVyOiAocHJvcHMsIGRlZmF1bHREb21zKSA9PiB7XHJcbiAgICAgICAgICByZXR1cm4gW1xyXG4gICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAga2V5PVwib2tcIlxyXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcclxuICAgICAgICAgICAgICAgIHByb3BzLnN1Ym1pdCgpO1xyXG4gICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgdHlwZT1cInByaW1hcnlcIlxyXG4gICAgICAgICAgICAgIHN0eWxlPXt7IHdpZHRoOiAnMTAwJScgfX1cclxuICAgICAgICAgICAgICBsb2FkaW5nPXtzdWJtaXR0aW5nfVxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgxJDhu5NuZyDDvVxyXG4gICAgICAgICAgICA8L0J1dHRvbj4sXHJcbiAgICAgICAgICBdO1xyXG4gICAgICAgIH0sXHJcbiAgICAgIH19XHJcbiAgICAgIGF1dG9Gb2N1c0ZpcnN0SW5wdXRcclxuICAgICAgZ3JpZFxyXG4gICAgICBzdWJtaXRUaW1lb3V0PXsyMDAwfVxyXG4gICAgICBpbml0aWFsVmFsdWVzPXt7XHJcbiAgICAgICAgcG9zdGluZ19kYXRlOiBtb21lbnQoKS5mb3JtYXQoREVGQVVMVF9EQVRFX0FORF9ISF9NTV9GT1JNQVQpLFxyXG4gICAgICB9fVxyXG4gICAgICBvbkZpbmlzaD17aGFuZGxlRmluaXNofVxyXG4gICAgPlxyXG4gICAgICA8Rm9ybVRyZWVTZWxlY3RTZWFyY2hcclxuICAgICAgICBuYW1lPXsnY3JlYXRlZF9pdGVtJ31cclxuICAgICAgICBmaWVsZFByb3BzPXt7XHJcbiAgICAgICAgICB0cmVlRGF0YTogdHJlZURhdGEsXHJcblxyXG4gICAgICAgICAgb25DaGFuZ2U6ICh2YWx1ZTogYW55KSA9PiB7XHJcbiAgICAgICAgICAgIHdoaWxlICh2YWx1ZT8ubGVuZ3RoID4gMSkge1xyXG4gICAgICAgICAgICAgIHZhbHVlLnNoaWZ0KCk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgaGFuZGxlQ2hvc2VDcmVhdGVkSXRlbSh2YWx1ZS5hdCgwKSk7XHJcbiAgICAgICAgICB9LFxyXG4gICAgICAgICAgLy8gb25DaGFuZ2U9eyh2YWx1ZTogYW55KSA9PiB7XHJcbiAgICAgICAgICAvLyAgIGlmICh2YWx1ZT8ubGVuZ3RoID4gMSkge1xyXG4gICAgICAgICAgLy8gICAgIHZhbHVlLnNoaWZ0KCk7XHJcbiAgICAgICAgICAvLyAgIH1cclxuICAgICAgICAgIC8vIH19XHJcbiAgICAgICAgfX1cclxuICAgICAgICBsYWJlbD17PEZvcm1hdHRlZE1lc3NhZ2UgaWQ9eydib20tbWFuYWdlbWVudC5jcmVhdGVkX2l0ZW0nfSAvPn1cclxuICAgICAgICBjb2xQcm9wcz17eyBzcGFuOiA4IH19XHJcbiAgICAgIC8+XHJcbiAgICAgIDxQcm9Gb3JtRGlnaXRcclxuICAgICAgICBsYWJlbD17PEZvcm1hdHRlZE1lc3NhZ2UgaWQ9eydib20tbWFuYWdlbWVudC5jcmVhdGVkX2l0ZW1fcXVhbnRpdHknfSAvPn1cclxuICAgICAgICBuYW1lPVwiY3JlYXRlZF9pdGVtX3F1YW50aXR5XCJcclxuICAgICAgICBmaWVsZFByb3BzPXt7XHJcbiAgICAgICAgICBmb3JtYXR0ZXI6ICh2YWx1ZSkgPT4gYCR7dmFsdWV9YC5yZXBsYWNlKC9cXEIoPz0oXFxkezN9KSsoPyFcXGQpKS9nLCAnLCcpLFxyXG4gICAgICAgICAgcGFyc2VyOiAodmFsdWU6IGFueSkgPT4gdmFsdWUhLnJlcGxhY2UoL1xcJFxccz98KCwqKS9nLCAnJyksXHJcbiAgICAgICAgICBzdGVwOiAnMC4wMScsXHJcbiAgICAgICAgfX1cclxuICAgICAgICBjb2xQcm9wcz17eyBzcGFuOiA4IH19XHJcbiAgICAgIC8+XHJcbiAgICAgIHtjcmVhdGVkSXRlbSA/IChcclxuICAgICAgICA8UHJvRm9ybVNlbGVjdFxyXG4gICAgICAgICAgbmFtZT17J2NyZWF0ZWRfaXRlbV91bml0J31cclxuICAgICAgICAgIGxhYmVsPXs8Rm9ybWF0dGVkTWVzc2FnZSBpZD17J2JvbS1tYW5hZ2VtZW50LmNyZWF0ZWRfaXRlbV91bml0J30gLz59XHJcbiAgICAgICAgICBmb3JtSXRlbVByb3BzPXt7XHJcbiAgICAgICAgICAgIHN0eWxlOiB7XHJcbiAgICAgICAgICAgICAgbWFyZ2luQm90dG9tOiAwLFxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgfX1cclxuICAgICAgICAgIGRpc2FibGVkPXshY3JlYXRlZEl0ZW19XHJcbiAgICAgICAgICBrZXk9eydjb252ZXJzaW9uX2ZhY3Rvcid9XHJcbiAgICAgICAgICBwbGFjZWhvbGRlcj1cIsSQxqFuIHbhu4tcIlxyXG4gICAgICAgICAgcmVxdWlyZWRcclxuICAgICAgICAgIHJ1bGVzPXtbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogPEZvcm1hdHRlZE1lc3NhZ2UgaWQ9eydjb21tb24ucmVxdWlyZWRfZXJyb3InfSAvPiB9XX1cclxuICAgICAgICAgIHJlcXVlc3Q9e2FzeW5jICgpID0+IHtcclxuICAgICAgICAgICAgY29uc3QgcmVzID1cclxuICAgICAgICAgICAgICAoYXdhaXQgZ2V0RGV0YWlsc1Byb2R1Y3RJdGVtVjMoe1xyXG4gICAgICAgICAgICAgICAgbmFtZTogY3JlYXRlZEl0ZW0/Lm5hbWUgfHwgJycsXHJcbiAgICAgICAgICAgICAgfSkpIHx8IFtdO1xyXG4gICAgICAgICAgICBjb25zdCBmb3JtYXR0ZWRfcmVzID1cclxuICAgICAgICAgICAgICByZXMuZGF0YS51b21zPy5tYXAoKGl0ZW0pID0+ICh7XHJcbiAgICAgICAgICAgICAgICBsYWJlbDogaXRlbS51b21fbmFtZSxcclxuICAgICAgICAgICAgICAgIHZhbHVlOiBpdGVtLmNvbnZlcnNpb25fZmFjdG9yLFxyXG4gICAgICAgICAgICAgIH0pKSB8fCBbXTtcclxuICAgICAgICAgICAgY29uc3Qgb3B0aW9ucyA9IFtcclxuICAgICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgICBsYWJlbDogY3JlYXRlZEl0ZW0udW9tX2xhYmVsLFxyXG4gICAgICAgICAgICAgICAgdmFsdWU6IDEsXHJcbiAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgXTtcclxuICAgICAgICAgICAgcmV0dXJuIFsuLi5vcHRpb25zLCAuLi5mb3JtYXR0ZWRfcmVzXTtcclxuICAgICAgICAgIH19XHJcbiAgICAgICAgICBjb2xQcm9wcz17eyBzcGFuOiA4IH19XHJcbiAgICAgICAgLz5cclxuICAgICAgKSA6IChcclxuICAgICAgICA8UHJvRm9ybVNlbGVjdFxyXG4gICAgICAgICAgbmFtZT17J2NyZWF0ZWRfaXRlbV91bml0J31cclxuICAgICAgICAgIGxhYmVsPXs8Rm9ybWF0dGVkTWVzc2FnZSBpZD17J2JvbS1tYW5hZ2VtZW50LmNyZWF0ZWRfaXRlbV91bml0J30gLz59XHJcbiAgICAgICAgICBmb3JtSXRlbVByb3BzPXt7XHJcbiAgICAgICAgICAgIHN0eWxlOiB7XHJcbiAgICAgICAgICAgICAgbWFyZ2luQm90dG9tOiAwLFxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgfX1cclxuICAgICAgICAgIGRpc2FibGVkPXshY3JlYXRlZEl0ZW19XHJcbiAgICAgICAgICBrZXk9eydjb252ZXJzaW9uX2ZhY3Rvcid9XHJcbiAgICAgICAgICBwbGFjZWhvbGRlcj1cIkNo4buNbiBow6BuZyBow7NhXCJcclxuICAgICAgICAgIC8vIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlU2VsZWN0VW5pdChlbnRpdHkua2V5LCArZSl9XHJcbiAgICAgICAgICBjb2xQcm9wcz17eyBzcGFuOiA4IH19XHJcbiAgICAgICAgLz5cclxuICAgICAgKX1cclxuXHJcbiAgICAgIDxQcm9Gb3JtVGV4dEFyZWFcclxuICAgICAgICBuYW1lPVwiYm9tX2Rlc2NyaXB0aW9uXCJcclxuICAgICAgICBsYWJlbD17PEZvcm1hdHRlZE1lc3NhZ2UgaWQ9eydjb21tb24uZm9ybS5kZXNjcmlwdGlvbid9IC8+fVxyXG4gICAgICAgIGNvbFByb3BzPXt7IHNwYW46IDI0IH19XHJcbiAgICAgICAgLy8gd2lkdGg9eydtZCd9XHJcbiAgICAgIC8+XHJcbiAgICAgIDxQcm9Gb3JtLkdyb3VwXHJcbiAgICAgICAgdGl0bGU9e1xyXG4gICAgICAgICAgPGRpdiBzdHlsZT17eyBtYXJnaW5Cb3R0b206ICctMjBweCcgfX0+XHJcbiAgICAgICAgICAgIDxGb3JtYXR0ZWRNZXNzYWdlIGlkPXsnYm9tLW1hbmFnZW1lbnQuaXRlbV9saXN0J30gLz5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIH1cclxuICAgICAgPlxyXG4gICAgICAgIDxGb3JtVHJlZVNlbGVjdFNlYXJjaFxyXG4gICAgICAgICAgbmFtZT17J2l0ZW1zJ31cclxuICAgICAgICAgIGZpZWxkUHJvcHM9e3tcclxuICAgICAgICAgICAgdHJlZURhdGE6IHRyZWVEYXRhLFxyXG4gICAgICAgICAgfX1cclxuICAgICAgICAgIGxhYmVsPXs8Rm9ybWF0dGVkTWVzc2FnZSBpZD17J2JvbS1tYW5hZ2VtZW50LnNvdXJjZS1pdGVtcyd9IC8+fVxyXG4gICAgICAgICAgZHJvcGRvd25Cb3R0b209e1xyXG4gICAgICAgICAgICA8Q3JlYXRlUHJvZHVjdFdpdGhUcmlnZ2VyXHJcbiAgICAgICAgICAgICAgYnV0dG9uVHlwZT1cImxpbmtcIlxyXG4gICAgICAgICAgICAgIG9uU3VjY2Vzcz17aGFuZGxlQ3JlYXRlTmV3SXRlbX1cclxuICAgICAgICAgICAgICBrZXk9XCJjcmVhdGVcIlxyXG4gICAgICAgICAgICAvPlxyXG4gICAgICAgICAgfVxyXG4gICAgICAgICAgY29sUHJvcHM9e3sgc3BhbjogOCB9fVxyXG4gICAgICAgIC8+XHJcbiAgICAgIDwvUHJvRm9ybS5Hcm91cD5cclxuXHJcbiAgICAgIDxQcm9Gb3JtLkdyb3VwPlxyXG4gICAgICAgIDxSb3cgc3R5bGU9e3sgd2lkdGg6ICcxMDAlJyB9fT5cclxuICAgICAgICAgIDxDb2wgc3Bhbj17MTh9PlxyXG4gICAgICAgICAgICA8U3BhY2U+XHJcbiAgICAgICAgICAgICAgPEJ1dHRvbiB0eXBlPVwicHJpbWFyeVwiIGljb249ezxQbHVzT3V0bGluZWQgLz59IG9uQ2xpY2s9e2hhbmRsZUFkZEl0ZW1zfT5cclxuICAgICAgICAgICAgICAgIDxGb3JtYXR0ZWRNZXNzYWdlIGlkPXsnYm9tLW1hbmFnZW1lbnQuYWRkX2l0ZW0nfSAvPlxyXG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG5cclxuICAgICAgICAgICAgICB7LyogPEJ1dHRvblxyXG4gICAgICAgICAgICAgICAgdHlwZT1cImxpbmtcIlxyXG4gICAgICAgICAgICAgICAgZGlzYWJsZWRcclxuICAgICAgICAgICAgICAgIC8vIG9uQ2xpY2s9e2Rvd25sb2FkVGVtcGxhdGV9XHJcbiAgICAgICAgICAgICAgICAvLyBkaXNhYmxlZD17aXNEb3dubG9hZGluZ31cclxuICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICA8Rm9ybWF0dGVkTWVzc2FnZSBpZD17J2NvbW1vbi5mb3JtLmV4Y2VsX3RlbXBsYXRlJ30gLz5cclxuICAgICAgICAgICAgICA8L0J1dHRvbj4gKi99XHJcbiAgICAgICAgICAgIDwvU3BhY2U+XHJcbiAgICAgICAgICA8L0NvbD5cclxuICAgICAgICAgIHsvKiA8Q29sIHNwYW49ezZ9IHN0eWxlPXt7IGRpc3BsYXk6ICdmbGV4JywganVzdGlmeUNvbnRlbnQ6ICdjZW50ZXInLCBhbGlnbkl0ZW1zOiAnY2VudGVyJyB9fT5cclxuICAgICAgICAgICAgPFR5cG9ncmFwaHkuVGV4dFxyXG4gICAgICAgICAgICAgIHN0eWxlPXt7XHJcbiAgICAgICAgICAgICAgICBsaW5lSGVpZ2h0OiAnMTAwJScsXHJcbiAgICAgICAgICAgICAgICBmb250U2l6ZTogJzE2cHgnLFxyXG4gICAgICAgICAgICAgICAgZGlzcGxheTogJ2lubGluZS1ibG9jaycsXHJcbiAgICAgICAgICAgICAgICB2ZXJ0aWNhbEFsaWduOiAnbWlkZGxlJyxcclxuICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgPEZvcm1hdHRlZE1lc3NhZ2UgaWQ9eydib20tbWFuYWdlbWVudC5pbXBvcnQtdm91Y2hlci50b3RhbF9wcmljZSd9IC8+XHJcbiAgICAgICAgICAgICAgeyc6ICd9XHJcbiAgICAgICAgICAgICAge3NlbGVjdGVkSXRlbXMgJiZcclxuICAgICAgICAgICAgICAgIG51bWVyYWwoc2VsZWN0ZWRJdGVtcy5yZWR1Y2UoKGFjYywgZWxlKSA9PiBhY2MgKyAoZWxlLnRvdGFsX3ByaWNlIHx8IDApLCAwKSkuZm9ybWF0KFxyXG4gICAgICAgICAgICAgICAgICAnMCwwJyxcclxuICAgICAgICAgICAgICAgICl9eycgJ31cclxuICAgICAgICAgICAgICBWTsSQXHJcbiAgICAgICAgICAgIDwvVHlwb2dyYXBoeS5UZXh0PlxyXG4gICAgICAgICAgPC9Db2w+ICovfVxyXG4gICAgICAgIDwvUm93PlxyXG4gICAgICA8L1Byb0Zvcm0uR3JvdXA+XHJcbiAgICAgIDxEaXZpZGVyIC8+XHJcbiAgICAgIDxDcmVhdGVCb21Nb2RhbFRhYmxlIGRhdGE9e3NlbGVjdGVkSXRlbXN9IHNldERhdGE9e3NldFNlbGVjdGVkSXRlbXN9IC8+XHJcbiAgICA8L01vZGFsRm9ybT5cclxuICApO1xyXG59O1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgQ3JlYXRlQm9tTW9kYWw7XHJcbiIsImltcG9ydCB7IGRlbGV0ZUJvbSB9IGZyb20gJ0Avc2VydmljZXMvSW52ZW50b3J5TWFuYWdlbWVudFYzL2JvbSc7XHJcbmltcG9ydCB7IERlbGV0ZU91dGxpbmVkIH0gZnJvbSAnQGFudC1kZXNpZ24vaWNvbnMnO1xyXG5pbXBvcnQgeyB1c2VSZXF1ZXN0IH0gZnJvbSAnQHVtaWpzL21heCc7XHJcbmltcG9ydCB7IEFwcCwgQnV0dG9uIH0gZnJvbSAnYW50ZCc7XHJcbmltcG9ydCB7IEZDIH0gZnJvbSAncmVhY3QnO1xyXG4vLyBpbXBvcnQgdXNlRGVsZXRlUHJvamVjdCBmcm9tICcuLi9ob29rcy91c2VEZWxlbGV0ZVByb2plY3QnO1xyXG5cclxuaW50ZXJmYWNlIERlbGV0ZU1vZGFsUHJvcHMge1xyXG4gIG5hbWU6IHN0cmluZztcclxuICBvbkRlbGV0ZVN1Y2Nlc3M/OiAoKSA9PiB2b2lkO1xyXG4gIGxhYmVsOiBzdHJpbmc7IC8vIGJhY2tlbmQgcmVxdWlyZWRcclxufVxyXG5cclxuY29uc3QgRGVsZXRlQm9tOiBGQzxEZWxldGVNb2RhbFByb3BzPiA9ICh7IG5hbWUsIG9uRGVsZXRlU3VjY2VzcywgbGFiZWwgfSkgPT4ge1xyXG4gIGNvbnN0IHsgbW9kYWwgfSA9IEFwcC51c2VBcHAoKTtcclxuICAvLyBjb25zdCB7IHJ1biB9ID0gdXNlRGVsZXRlUHJvZHVjdEl0ZW1WMygpO1xyXG4gIGNvbnN0IHsgcnVuIH0gPSB1c2VSZXF1ZXN0KGRlbGV0ZUJvbSk7XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8QnV0dG9uXHJcbiAgICAgIC8vIHNpemU9XCJzbWFsbFwiXHJcbiAgICAgIGljb249ezxEZWxldGVPdXRsaW5lZCAvPn1cclxuICAgICAgZGFuZ2VyXHJcbiAgICAgIG9uQ2xpY2s9eyhlKSA9PiB7XHJcbiAgICAgICAgZS5zdG9wUHJvcGFnYXRpb24oKTtcclxuICAgICAgICBlLnByZXZlbnREZWZhdWx0KCk7XHJcbiAgICAgICAgbW9kYWwuY29uZmlybSh7XHJcbiAgICAgICAgICB0aXRsZTogYELhuqFuIGPDsyBjaOG6r2MgY2jhuq9jIG114buRbiB4w7NhIEJPTSBuw6B5IGPhu6dhIGjDoG5nIGjDs2EgJHtsYWJlbH0/YCxcclxuICAgICAgICAgIGNvbnRlbnQ6ICdIw6BuaCDEkeG7mW5nIG7DoHkga2jDtG5nIHRo4buDIGhvw6BuIHTDoWMhJyxcclxuICAgICAgICAgIG9rQnV0dG9uUHJvcHM6IHtcclxuICAgICAgICAgICAgZGFuZ2VyOiB0cnVlLFxyXG4gICAgICAgICAgfSxcclxuICAgICAgICAgIG9uT2s6IGFzeW5jICgpID0+IHtcclxuICAgICAgICAgICAgdHJ5IHtcclxuICAgICAgICAgICAgICBhd2FpdCBydW4obmFtZSk7XHJcbiAgICAgICAgICAgICAgb25EZWxldGVTdWNjZXNzPy4oKTtcclxuICAgICAgICAgICAgICByZXR1cm4gdHJ1ZTtcclxuICAgICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgICAgICByZXR1cm4gZmFsc2U7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH0sXHJcbiAgICAgICAgfSk7XHJcbiAgICAgIH19XHJcbiAgICAvPlxyXG4gICk7XHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBEZWxldGVCb207XHJcbiIsImltcG9ydCB7IGdldEJPTXMgfSBmcm9tICdAL3NlcnZpY2VzL0ludmVudG9yeU1hbmFnZW1lbnRWMy9ib20nO1xyXG5pbXBvcnQgeyBJQm9tIH0gZnJvbSAnQC90eXBlcy9ib20udHlwZSc7XHJcbmltcG9ydCB7IEV5ZU91dGxpbmVkIH0gZnJvbSAnQGFudC1kZXNpZ24vaWNvbnMnO1xyXG5pbXBvcnQgeyBBY3Rpb25UeXBlLCBQcm9Db2x1bW5zLCBQcm9UYWJsZSB9IGZyb20gJ0BhbnQtZGVzaWduL3Byby1jb21wb25lbnRzJztcclxuaW1wb3J0IHsgRm9ybWF0dGVkTWVzc2FnZSwgdXNlQWNjZXNzIH0gZnJvbSAnQHVtaWpzL21heCc7XHJcbmltcG9ydCB7IEJ1dHRvbiwgU3BhY2UgfSBmcm9tICdhbnRkJztcclxuaW1wb3J0IHsgdXNlUmVmLCB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcclxuaW1wb3J0IEJPTURldGFpbCBmcm9tICcuL0JPTURldGFpbCc7XHJcbmltcG9ydCBDcmVhdGVCb21Nb2RhbCBmcm9tICcuL0NyZWF0ZUJvbU1vZGFsJztcclxuaW1wb3J0IERlbGV0ZUJvbSBmcm9tICcuL0RlbGV0ZUJvbSc7XHJcblxyXG5jb25zdCBCT01NYW5hZ2VtZW50ID0gKCkgPT4ge1xyXG4gIGNvbnN0IGFjdGlvblJlZiA9IHVzZVJlZjxBY3Rpb25UeXBlPigpO1xyXG4gIGNvbnN0IFtpc01vZGFsT3Blbiwgc2V0SXNNb2RhbE9wZW5dID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtjdXJyZW50SXRlbSwgc2V0Q3VycmVudEl0ZW1dID0gdXNlU3RhdGU8SUJvbT4oKTtcclxuICBjb25zdCBoYW5kbGVQb3B1cERldGFpbCA9IChyZWNvcmQ6IGFueSkgPT4ge1xyXG4gICAgc2V0Q3VycmVudEl0ZW0ocmVjb3JkKTtcclxuICAgIHNldElzTW9kYWxPcGVuKHRydWUpO1xyXG4gIH07XHJcbiAgY29uc3QgYWNjZXNzID0gdXNlQWNjZXNzKCk7XHJcbiAgY29uc3QgdG9vbEJhclJlbmRlcjogYW55ID0gW107XHJcbiAgY29uc3QgY2FuRGVsZXRlQ2F0ZWdvcnkgPSBhY2Nlc3MuY2FuRGVsZXRlSW5DYXRlZ29yeU1hbmFnZW1lbnQoKTtcclxuICBpZiAoYWNjZXNzLmNhbkNyZWF0ZUluQ2F0ZWdvcnlJbnZlbnRvcnlNYW5hZ2VtZW50KCkpIHtcclxuICAgIHRvb2xCYXJSZW5kZXIucHVzaChcclxuICAgICAgPENyZWF0ZUJvbU1vZGFsXHJcbiAgICAgICAgb25TdWNjZXNzPXsoKSA9PiB7XHJcbiAgICAgICAgICBhY3Rpb25SZWYuY3VycmVudD8ucmVsb2FkKCk7XHJcbiAgICAgICAgfX1cclxuICAgICAgLz4sXHJcbiAgICApO1xyXG4gIH1cclxuICBjb25zdCByZWxvYWRUYWJsZSA9ICgpID0+IHtcclxuICAgIGFjdGlvblJlZi5jdXJyZW50Py5yZWxvYWQoKTtcclxuICB9O1xyXG4gIGNvbnN0IGNvbHVtbnM6IFByb0NvbHVtbnM8SUJvbT5bXSA9IFtcclxuICAgIHtcclxuICAgICAgdGl0bGU6IDxGb3JtYXR0ZWRNZXNzYWdlIGlkPVwiY29tbW9uLmluZGV4XCIgLz4sXHJcbiAgICAgIGRhdGFJbmRleDogJ2luZGV4JyxcclxuICAgICAgcmVuZGVyKGRvbSwgZW50aXR5LCBpbmRleCkge1xyXG4gICAgICAgIHJldHVybiA8ZGl2PntpbmRleCArIDF9PC9kaXY+O1xyXG4gICAgICB9LFxyXG4gICAgICBoaWRlSW5TZWFyY2g6IHRydWUsXHJcbiAgICAgIHdpZHRoOiA1LFxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgdGl0bGU6IDxGb3JtYXR0ZWRNZXNzYWdlIGlkPVwiY29tbW9uLml0ZW1fbGFiZWxcIiAvPixcclxuICAgICAgZGF0YUluZGV4OiAnaXRlbV9uYW1lJyxcclxuICAgICAgd2lkdGg6IDIwLFxyXG4gICAgICByZW5kZXI6IChfLCByZWNvcmQpID0+IChcclxuICAgICAgICA8PlxyXG4gICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICBpY29uPXs8RXllT3V0bGluZWQgLz59XHJcbiAgICAgICAgICAgIHN0eWxlPXt7IG1hcmdpblJpZ2h0OiAnOHB4JyB9fVxyXG4gICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVQb3B1cERldGFpbChyZWNvcmQpfVxyXG4gICAgICAgICAgLz5cclxuICAgICAgICAgIHtyZWNvcmQ/Lml0ZW1fbGFiZWx9XHJcbiAgICAgICAgPC8+XHJcbiAgICAgICksXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICB0aXRsZTogPEZvcm1hdHRlZE1lc3NhZ2UgaWQ9eydjb21tb24uZGVzY3JpcHRpb24nfSAvPixcclxuICAgICAgZGF0YUluZGV4OiAnYm9tX2Rlc2NyaXB0aW9uJyxcclxuICAgICAgLy8gcmVuZGVyKGRvbSwgZW50aXR5LCBpbmRleCwgYWN0aW9uLCBzY2hlbWEpIHtcclxuICAgICAgLy8gICByZXR1cm4gPGRpdj57Zm9ybWF0RGF0ZShlbnRpdHkuY3JlYXRpb24pfTwvZGl2PjtcclxuICAgICAgLy8gfSxcclxuICAgICAgaGlkZUluU2VhcmNoOiB0cnVlLFxyXG4gICAgICB3aWR0aDogNTAsXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICB0aXRsZTogPEZvcm1hdHRlZE1lc3NhZ2UgaWQ9XCJjb21tb24uY3JlYXRvclwiIC8+LFxyXG4gICAgICB3aWR0aDogMjAsXHJcbiAgICAgIGhpZGVJblNlYXJjaDogdHJ1ZSxcclxuICAgICAgcmVuZGVyKGRvbSwgZW50aXR5LCBpbmRleCwgYWN0aW9uLCBzY2hlbWEpIHtcclxuICAgICAgICByZXR1cm4gPGRpdj57YCR7ZW50aXR5LnVzZXJfZmlyc3RfbmFtZX0gJHtlbnRpdHkudXNlcl9sYXN0X25hbWV9YH08L2Rpdj47XHJcbiAgICAgIH0sXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICB0aXRsZTogJ0FjdGlvbicsXHJcbiAgICAgIGRhdGFJbmRleDogJ25hbWUnLFxyXG4gICAgICBzZWFyY2g6IGZhbHNlLFxyXG4gICAgICB3aWR0aDogMTAsXHJcbiAgICAgIHJlbmRlcjogKGRvbSwgZW50aXR5KSA9PiAoXHJcbiAgICAgICAgPFNwYWNlPlxyXG4gICAgICAgICAge2NhbkRlbGV0ZUNhdGVnb3J5ICYmIChcclxuICAgICAgICAgICAgPERlbGV0ZUJvbVxyXG4gICAgICAgICAgICAgIG9uRGVsZXRlU3VjY2Vzcz17cmVsb2FkVGFibGV9XHJcbiAgICAgICAgICAgICAgbmFtZT17ZW50aXR5Lm5hbWV9XHJcbiAgICAgICAgICAgICAgbGFiZWw9e2VudGl0eS5pdGVtX2xhYmVsIHx8ICcnfVxyXG4gICAgICAgICAgICAvPlxyXG4gICAgICAgICAgKX1cclxuICAgICAgICA8L1NwYWNlPlxyXG4gICAgICApLFxyXG4gICAgfSxcclxuICBdO1xyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPD5cclxuICAgICAge2N1cnJlbnRJdGVtICYmIChcclxuICAgICAgICA8Qk9NRGV0YWlsXHJcbiAgICAgICAgICBuYW1lPXtjdXJyZW50SXRlbS5uYW1lfVxyXG4gICAgICAgICAgaXNNb2RhbE9wZW49e2lzTW9kYWxPcGVufVxyXG4gICAgICAgICAgc2V0SXNNb2RhbE9wZW49e3NldElzTW9kYWxPcGVufVxyXG4gICAgICAgIC8+XHJcbiAgICAgICl9XHJcbiAgICAgIDxQcm9UYWJsZTxJQm9tPlxyXG4gICAgICAgIGFjdGlvblJlZj17YWN0aW9uUmVmfVxyXG4gICAgICAgIGNvbHVtbnM9e2NvbHVtbnN9XHJcbiAgICAgICAgc2VhcmNoPXt7IGxhYmVsV2lkdGg6ICdhdXRvJyB9fVxyXG4gICAgICAgIGNhcmRCb3JkZXJlZFxyXG4gICAgICAgIHNpemU9XCJzbWFsbFwiXHJcbiAgICAgICAgcGFnaW5hdGlvbj17e1xyXG4gICAgICAgICAgZGVmYXVsdFBhZ2VTaXplOiAyMCxcclxuICAgICAgICAgIHNob3dTaXplQ2hhbmdlcjogdHJ1ZSxcclxuICAgICAgICAgIHBhZ2VTaXplT3B0aW9uczogWycyMCcsICc1MCcsICcxMDAnXSxcclxuICAgICAgICB9fVxyXG4gICAgICAgIHJlcXVlc3Q9e2FzeW5jIChwYXJhbXMsIHNvcnQsIGZpbHRlcikgPT4ge1xyXG4gICAgICAgICAgdHJ5IHtcclxuICAgICAgICAgICAgY29uc3QgZmlsdGVyczogYW55W10gPSBbXTtcclxuICAgICAgICAgICAgaWYgKHBhcmFtcy5pdGVtX25hbWUpIHtcclxuICAgICAgICAgICAgICBmaWx0ZXJzLnB1c2goWydCT00nLCAnaXRlbV9uYW1lJywgJ2xpa2UnLCBwYXJhbXMuaXRlbV9uYW1lXSk7XHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IGdldEJPTXMoe1xyXG4gICAgICAgICAgICAgIHBhZ2U6IHBhcmFtcy5jdXJyZW50LFxyXG4gICAgICAgICAgICAgIHNpemU6IHBhcmFtcy5wYWdlU2l6ZSxcclxuICAgICAgICAgICAgICBvcmRlcl9ieTogJ2NyZWF0aW9uIERFU0MnLFxyXG4gICAgICAgICAgICAgIGZpbHRlcnMsXHJcbiAgICAgICAgICAgIH0pO1xyXG4gICAgICAgICAgICByZXR1cm4ge1xyXG4gICAgICAgICAgICAgIGRhdGE6IHJlcy5kYXRhLFxyXG4gICAgICAgICAgICAgIHN1Y2VzczogdHJ1ZSxcclxuICAgICAgICAgICAgICB0b3RhbDogcmVzLnBhZ2luYXRpb24udG90YWxFbGVtZW50cyxcclxuICAgICAgICAgICAgfTtcclxuICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICAgICAgZGF0YTogW10sXHJcbiAgICAgICAgICAgICAgc3VjZXNzOiBmYWxzZSxcclxuICAgICAgICAgICAgfTtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9fVxyXG4gICAgICAgIHRvb2xCYXJSZW5kZXI9eygpID0+IHRvb2xCYXJSZW5kZXJ9XHJcbiAgICAgIC8+XHJcbiAgICA8Lz5cclxuICApO1xyXG59O1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgQk9NTWFuYWdlbWVudDtcclxuIiwiaW1wb3J0IFBhZ2VDb250YWluZXJUYWJzV2l0aFNlYXJjaCBmcm9tICdAL2NvbXBvbmVudHMvUGFnZUNvbnRhaW5lclRhYnNXaXRoU2VhcmNoJztcclxuaW1wb3J0IHsgRGVzY3JpcHRpb25zU2tlbGV0b24gfSBmcm9tICdAYW50LWRlc2lnbi9wcm8tY29tcG9uZW50cyc7XHJcbmltcG9ydCB7IHVzZUludGwgfSBmcm9tICdAdW1panMvbWF4JztcclxuaW1wb3J0IEJPTU1hbmFnZW1lbnQgZnJvbSAnLi9jb21wb25lbnRzL0JPTU1hbmFnZW1lbnQnO1xyXG5cclxuY29uc3QgQk9NID0gKCkgPT4ge1xyXG4gIGNvbnN0IGludGwgPSB1c2VJbnRsKCk7XHJcbiAgcmV0dXJuIChcclxuICAgIDxQYWdlQ29udGFpbmVyVGFic1dpdGhTZWFyY2hcclxuICAgICAgdGFic0l0ZW1zPXtbXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgbGFiZWw6IGludGwuZm9ybWF0TWVzc2FnZSh7IGlkOiAnY29tbW9uLmJvbS1tYW5hZ2VtZW50JyB9KSxcclxuICAgICAgICAgIGZhbGxiYWNrOiA8RGVzY3JpcHRpb25zU2tlbGV0b24gYWN0aXZlIC8+LFxyXG4gICAgICAgICAgY29tcG9uZW50KCkge1xyXG4gICAgICAgICAgICByZXR1cm4gPEJPTU1hbmFnZW1lbnQgLz47XHJcbiAgICAgICAgICB9LFxyXG4gICAgICAgIH0sXHJcbiAgICAgIF19XHJcbiAgICAvPlxyXG4gICk7XHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBCT007XHJcbiJdLCJuYW1lcyI6WyJyZXF1ZXN0IiwiZ2VuZXJhdGVBUElQYXRoIiwiZ2V0UGFyYW1zUmVxTGlzdCIsImhhbmRsZUVycm9yIiwiZXJyb3IiLCJjb25zb2xlIiwibG9nIiwiY29uY2F0IiwiZ2V0Qk9NcyIsIl9yZWYiLCJfYXN5bmNUb0dlbmVyYXRvciIsIl9yZWdlbmVyYXRvclJ1bnRpbWUiLCJtYXJrIiwiX2NhbGxlZSIsInBhcmFtcyIsInJlcyIsIndyYXAiLCJfY2FsbGVlJCIsIl9jb250ZXh0IiwicHJldiIsIm5leHQiLCJzZW50IiwiYWJydXB0IiwicmVzdWx0Iiwic3RvcCIsIl94IiwiYXBwbHkiLCJhcmd1bWVudHMiLCJnZXRCT01EZXRhaWwiLCJfcmVmMyIsIl9jYWxsZWUyIiwiX3JlZjIiLCJuYW1lIiwiX2NhbGxlZTIkIiwiX2NvbnRleHQyIiwiZGF0YSIsImRvY3MiLCJhdCIsInQwIiwiX3gyIiwic2F2ZUJvbSIsIl9yZWY0IiwiX2NhbGxlZTMiLCJwYXlsb2FkIiwiX3JlcyRyZXN1bHQkZG9jcyIsIl9jYWxsZWUzJCIsIl9jb250ZXh0MyIsIm1ldGhvZCIsIl9vYmplY3RTcHJlYWQiLCJfeDMiLCJkZWxldGVCb20iLCJfcmVmNSIsIl9jYWxsZWU0IiwiX3JlcyRyZXN1bHQkZG9jczIiLCJfY2FsbGVlNCQiLCJfY29udGV4dDQiLCJfeDQiLCJERUZBVUxUX0RBVEVfRk9STUFUX1dJVEhPVVRfVElNRSIsImZvcm1hdE51bWVyYWwiLCJQcm9Gb3JtIiwiUHJvRm9ybURhdGVQaWNrZXIiLCJQcm9Gb3JtVGV4dCIsIlByb0Zvcm1UZXh0QXJlYSIsIlByb1RhYmxlIiwiRm9ybWF0dGVkTWVzc2FnZSIsInVzZUludGwiLCJ1c2VSZXF1ZXN0IiwiTW9kYWwiLCJ1c2VGb3JtIiwidXNlRWZmZWN0IiwidXNlUmVmIiwidXNlU3RhdGUiLCJqc3giLCJfanN4IiwianN4cyIsIl9qc3hzIiwiQk9NRGV0YWlsIiwiaXNNb2RhbE9wZW4iLCJzZXRJc01vZGFsT3BlbiIsImFjdGlvblJlZiIsIl91c2VGb3JtIiwiX3VzZUZvcm0yIiwiX3NsaWNlZFRvQXJyYXkiLCJmb3JtIiwiX3VzZVN0YXRlIiwiX3VzZVN0YXRlMiIsIml0ZW1zIiwic2V0SXRlbXMiLCJfdXNlUmVxdWVzdCIsIm1hbnVhbCIsIm9uRXJyb3IiLCJtZXNzYWdlIiwib25TdWNjZXNzIiwibG9hZGluZyIsInJlZnJlc2giLCJydW4iLCJoYW5kbGVSZWxvYWQiLCJfYWN0aW9uUmVmJGN1cnJlbnQiLCJjdXJyZW50IiwicmVsb2FkIiwiY29sdW1ucyIsInRpdGxlIiwiaWQiLCJkYXRhSW5kZXgiLCJyZW5kZXIiLCJkb20iLCJlbnRpdHkiLCJpbmRleCIsImNoaWxkcmVuIiwid2lkdGgiLCJ2YWx1ZSIsInJlY29yZCIsInN0eWxlIiwid2hpdGVTcGFjZSIsIml0ZW1fbGFiZWwiLCJhY3Rpb24iLCJzY2hlbWEiLCJzdG9ja19xdHkiLCJyZXNldEZpZWxkcyIsInNldEZpZWxkc1ZhbHVlIiwic2V0RmllbGRWYWx1ZSIsInVzZXJfZmlyc3RfbmFtZSIsInVzZXJfbGFzdF9uYW1lIiwiX3VzZUludGwiLCJmb3JtYXRNZXNzYWdlIiwib3BlbiIsIm9uQ2FuY2VsIiwiZm9vdGVyIiwic3VibWl0dGVyIiwiZGlzYWJsZWQiLCJsYXlvdXQiLCJncmlkIiwibGFiZWwiLCJjb2xQcm9wcyIsInNwYW4iLCJmaWVsZFByb3BzIiwiZm9ybWF0IiwiYXV0b1NpemUiLCJtaW5Sb3dzIiwibWF4Um93cyIsImNhcmRCb3JkZXJlZCIsInNpemUiLCJkYXRhU291cmNlIiwicm93S2V5Iiwic2VhcmNoIiwiZ2V0RGV0YWlsc1Byb2R1Y3RJdGVtVjMiLCJEZWxldGVPdXRsaW5lZCIsIkVkaXRhYmxlUHJvVGFibGUiLCJQcm9Gb3JtU2VsZWN0IiwiQnV0dG9uIiwiSW5wdXROdW1iZXIiLCJDcmVhdGVCb21Nb2RhbFRhYmxlIiwic2V0RGF0YSIsImhhbmRsZURlbGV0ZUl0ZW0iLCJuZXdEYXRhIiwiZmlsdGVyIiwiaXRlbSIsImtleSIsIm1hcCIsImVkaXRhYmxlS2V5cyIsInNldEVkaXRhYmxlUm93S2V5cyIsImhhbmRsZVNlbGVjdFVuaXQiLCJzdHJ1Y3R1cmVkQ2xvbmUiLCJyZWNvcmRJbmRleCIsImZpbmRJbmRleCIsImNvbnZlcnNpb25fZmFjdG9yIiwiZWRpdGFibGUiLCJkZWZhdWx0TWVzc2FnZSIsInJlbmRlckZvcm1JdGVtIiwiY29uZmlnIiwiZm9ybWF0dGVyIiwicmVwbGFjZSIsInBhcnNlciIsInN0ZXAiLCJmb3JtSXRlbVByb3BzIiwibWFyZ2luQm90dG9tIiwiZGVmYXVsdFZhbHVlIiwidW9tX2xhYmVsIiwicGxhY2Vob2xkZXIiLCJfcmVzJGRhdGEkdW9tcyIsImZvcm1hdHRlZF9yZXMiLCJvcHRpb25zIiwiaXRlbV9jb2RlIiwidW9tcyIsInVvbV9uYW1lIiwiX3RvQ29uc3VtYWJsZUFycmF5Iiwib25DaGFuZ2UiLCJlIiwiXyIsImljb24iLCJvbkNsaWNrIiwiX2FjdGlvblJlZiRjdXJyZW50MiIsIm1pbldpZHRoIiwic29ydCIsIl9hcmdzMiIsImxlbmd0aCIsInVuZGVmaW5lZCIsInN1Y2Nlc3MiLCJ0b3RhbCIsInR5cGUiLCJhY3Rpb25SZW5kZXIiLCJyb3ciLCJkZWZhdWx0RG9tcyIsIm9uVmFsdWVzQ2hhbmdlIiwicmVjb3JkTGlzdCIsInJlY29yZENyZWF0b3JQcm9wcyIsInNldHRpbmciLCJsaXN0c0hlaWdodCIsInBhZ2luYXRpb24iLCJkZWZhdWx0UGFnZVNpemUiLCJzaG93U2l6ZUNoYW5nZXIiLCJwYWdlU2l6ZU9wdGlvbnMiLCJ0b29sYmFyIiwibXVsdGlwbGVMaW5lIiwidG9vbEJhclJlbmRlciIsImRhdGVGb3JtYXR0ZXIiLCJERUZBVUxUX0RBVEVfQU5EX0hIX01NX0ZPUk1BVCIsIkZvcm1UcmVlU2VsZWN0U2VhcmNoIiwiQ3JlYXRlUHJvZHVjdFdpdGhUcmlnZ2VyIiwiZ2V0SXRlbUdyb3VwTGlzdCIsImdldEl0ZW1MaXN0IiwiUGx1c091dGxpbmVkIiwiTW9kYWxGb3JtIiwiUHJvRm9ybURpZ2l0IiwidXNlTW9kZWwiLCJBcHAiLCJDb2wiLCJEaXZpZGVyIiwiUm93IiwiU3BhY2UiLCJUeXBvZ3JhcGh5IiwibW9tZW50IiwiVGl0bGUiLCJDcmVhdGVCb21Nb2RhbCIsImltcG9ydEZyb21UYXNrIiwiZGF0YVByb3BzIiwidGFza0lEIiwic3VibWl0dGluZyIsInNldFN1Ym1pdHRpbmciLCJfdXNlU3RhdGUzIiwiX3VzZVN0YXRlNCIsInRyZWVEYXRhIiwic2V0VHJlZURhdGEiLCJfdXNlU3RhdGU1IiwiX3VzZVN0YXRlNiIsInNlbGVjdGVkSXRlbXMiLCJzZXRTZWxlY3RlZEl0ZW1zIiwiX3VzZVN0YXRlNyIsIl91c2VTdGF0ZTgiLCJfdXNlU3RhdGU5IiwiX3VzZVN0YXRlMTAiLCJjcmVhdGVkSXRlbSIsInNldENyZWF0ZWRJdGVtIiwiX3VzZU1vZGVsIiwiaW5pdGlhbFN0YXRlIiwiY3VyZW50VXNlciIsImN1cnJlbnRVc2VyIiwiX0FwcCR1c2VBcHAiLCJ1c2VBcHAiLCJnZXRJdGVtVHJlZURhdGEiLCJncm91cEJ5SXRlbUdyb3VwV2l0aEl0ZW1Hcm91cERhdGEiLCJkYXRhR3JvdXAiLCJkYXRhTWFwIiwiZ2VuZXJhdGVkRGF0YSIsIl9ncm91cEJ5SXRlbUdyb3VwV2l0aCIsInN0b2NrSXRlbXMiLCJzdG9ja0l0ZW1Hcm91cHMiLCJncm91cGVkSXRlbXMiLCJmb3JFYWNoIiwiaXRlbUdyb3VwIiwiaXRlbV9ncm91cCIsInB1c2giLCJfaXRlcmF0b3IiLCJfY3JlYXRlRm9yT2ZJdGVyYXRvckhlbHBlciIsIl9zdGVwIiwicyIsIm4iLCJkb25lIiwiaXRlbUdyb3VwTmFtZSIsIml0ZW1fZ3JvdXBfbmFtZSIsImVyciIsImYiLCJPYmplY3QiLCJlbnRyaWVzIiwiX2dyb3VwRGF0YSRpdGVtR3JvdXAiLCJncm91cERhdGEiLCJoYW5kbGVDcmVhdGVOZXdJdGVtIiwiaGFuZGxlQWRkSXRlbXMiLCJfcmVmNiIsIml0ZW1faWRzIiwiZmlsdGVyZWRfaXRlbXMiLCJmb3JtYXR0ZWRfaXRlbXMiLCJnZXRGaWVsZFZhbHVlIiwiaW5jbHVkZXMiLCJpdGVtX25hbWUiLCJxdHkiLCJEYXRlIiwibm93IiwiTWF0aCIsImZsb29yIiwicmFuZG9tIiwidG9TdHJpbmciLCJoYW5kbGVDaG9zZUNyZWF0ZWRJdGVtIiwiX3JlZjciLCJ2YWxpZGF0ZUl0ZW1zIiwiX2l0ZXJhdG9yMiIsIl9zdGVwMiIsImNyZWF0ZUJvbVBheWxvYWQiLCJ2YWx1ZXMiLCJjb21tb25GaWVsZHMiLCJib21fZGVzY3JpcHRpb24iLCJxdWFudGl0eSIsImNyZWF0ZWRfaXRlbV9xdWFudGl0eSIsImNyZWF0ZWRfaXRlbV91bml0IiwiZG9jc3RhdHVzIiwiZG9jdHlwZSIsIl9faXNsb2NhbCIsIl9fdW5zYXZlZCIsIm93bmVyIiwiY29tcGFueSIsImlzX2FjdGl2ZSIsImlzX2RlZmF1bHQiLCJzZXRfcmF0ZV9vZl9zdWJfYXNzZW1ibHlfaXRlbV9iYXNlZF9vbl9ib20iLCJzdWJzdHJpbmciLCJkb19ub3RfZXhwbG9kZSIsImFsbG93X2FsdGVybmF0aXZlX2l0ZW0iLCJwYXJlbnRmaWVsZCIsInBhcmVudHR5cGUiLCJoYW5kbGVGaW5pc2giLCJfcmVmOCIsIl9jYWxsZWU1IiwiYm9tUGF5bG9hZCIsIl9jYWxsZWU1JCIsIl9jb250ZXh0NSIsImZpbmlzaCIsImhhbmRsZUFib3J0IiwiX3JlZjkiLCJfY2FsbGVlNiIsIl9jYWxsZWU2JCIsIl9jb250ZXh0NiIsImZldGNoRGF0YSIsIl9yZWYxMCIsIl9jYWxsZWU3IiwiX2NhbGxlZTckIiwiX2NvbnRleHQ3IiwibW9kYWxQcm9wcyIsImRlc3Ryb3lPbkNsb3NlIiwidHJpZ2dlciIsInJvd1Byb3BzIiwiZ3V0dGVyIiwicHJvcHMiLCJzdWJtaXQiLCJhdXRvRm9jdXNGaXJzdElucHV0Iiwic3VibWl0VGltZW91dCIsImluaXRpYWxWYWx1ZXMiLCJwb3N0aW5nX2RhdGUiLCJvbkZpbmlzaCIsInNoaWZ0IiwicmVxdWlyZWQiLCJydWxlcyIsIl9jYWxsZWU4IiwiX2NhbGxlZTgkIiwiX2NvbnRleHQ4IiwiR3JvdXAiLCJkcm9wZG93bkJvdHRvbSIsImJ1dHRvblR5cGUiLCJEZWxldGVCb20iLCJvbkRlbGV0ZVN1Y2Nlc3MiLCJtb2RhbCIsImRhbmdlciIsInN0b3BQcm9wYWdhdGlvbiIsInByZXZlbnREZWZhdWx0IiwiY29uZmlybSIsImNvbnRlbnQiLCJva0J1dHRvblByb3BzIiwib25PayIsIl9vbk9rIiwiRXllT3V0bGluZWQiLCJ1c2VBY2Nlc3MiLCJGcmFnbWVudCIsIl9GcmFnbWVudCIsIkJPTU1hbmFnZW1lbnQiLCJjdXJyZW50SXRlbSIsInNldEN1cnJlbnRJdGVtIiwiaGFuZGxlUG9wdXBEZXRhaWwiLCJhY2Nlc3MiLCJjYW5EZWxldGVDYXRlZ29yeSIsImNhbkRlbGV0ZUluQ2F0ZWdvcnlNYW5hZ2VtZW50IiwiY2FuQ3JlYXRlSW5DYXRlZ29yeUludmVudG9yeU1hbmFnZW1lbnQiLCJyZWxvYWRUYWJsZSIsImhpZGVJblNlYXJjaCIsIm1hcmdpblJpZ2h0IiwibGFiZWxXaWR0aCIsImZpbHRlcnMiLCJwYWdlIiwicGFnZVNpemUiLCJvcmRlcl9ieSIsInN1Y2VzcyIsInRvdGFsRWxlbWVudHMiLCJQYWdlQ29udGFpbmVyVGFic1dpdGhTZWFyY2giLCJEZXNjcmlwdGlvbnNTa2VsZXRvbiIsIkJPTSIsImludGwiLCJ0YWJzSXRlbXMiLCJmYWxsYmFjayIsImFjdGl2ZSIsImNvbXBvbmVudCJdLCJzb3VyY2VSb290IjoiIn0=
//# sourceURL=webpack-internal:///17208
`)},58642:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   E6: function() { return /* binding */ getItemInventoryVouchers; },
/* harmony export */   Kd: function() { return /* binding */ getItemByGroup; },
/* harmony export */   T1: function() { return /* binding */ updateProductItemV3; },
/* harmony export */   Zk: function() { return /* binding */ getItemCustomerVouchersSum; },
/* harmony export */   eX: function() { return /* binding */ getDetailsProductItemV3; },
/* harmony export */   fu: function() { return /* binding */ deleteProductItemV3; },
/* harmony export */   hq: function() { return /* binding */ createProductItemV3; },
/* harmony export */   oD: function() { return /* binding */ getItemSupplierVouchersSum; },
/* harmony export */   yI: function() { return /* binding */ getProductItemV3; }
/* harmony export */ });
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(97857);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(7837);
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(467);





function getItemByGroup(_x) {
  return _getItemByGroup.apply(this, arguments);
}
function _getItemByGroup() {
  _getItemByGroup = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee9(_ref) {
    var _ref$page, page, _ref$size, size, _ref$fields, fields, _ref$filters, filters, _ref$or_filters, or_filters, params, result;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee9$(_context9) {
      while (1) switch (_context9.prev = _context9.next) {
        case 0:
          _ref$page = _ref.page, page = _ref$page === void 0 ? 1 : _ref$page, _ref$size = _ref.size, size = _ref$size === void 0 ? 10000 : _ref$size, _ref$fields = _ref.fields, fields = _ref$fields === void 0 ? ['*'] : _ref$fields, _ref$filters = _ref.filters, filters = _ref$filters === void 0 ? [] : _ref$filters, _ref$or_filters = _ref.or_filters, or_filters = _ref$or_filters === void 0 ? [] : _ref$or_filters;
          _context9.prev = 1;
          params = {
            page: page,
            size: size,
            fields: JSON.stringify(fields),
            filters: JSON.stringify(filters)
          };
          _context9.next = 5;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)("api/v2/item/itemGroupByGroup"), {
            method: 'GET',
            params: params
            // params: params,
            // queryParams: params,
          });
        case 5:
          result = _context9.sent;
          return _context9.abrupt("return", {
            data: result.result || []
          });
        case 9:
          _context9.prev = 9;
          _context9.t0 = _context9["catch"](1);
          return _context9.abrupt("return", {
            data: []
          });
        case 12:
        case "end":
          return _context9.stop();
      }
    }, _callee9, null, [[1, 9]]);
  }));
  return _getItemByGroup.apply(this, arguments);
}
var getProductItemV3 = /*#__PURE__*/function () {
  var _ref2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/item'), {
            params: (0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .getParamsReqList */ .vj)(params)
          });
        case 2:
          res = _context.sent;
          return _context.abrupt("return", res.result);
        case 4:
        case "end":
          return _context.stop();
      }
    }, _callee);
  }));
  return function getProductItemV3(_x2) {
    return _ref2.apply(this, arguments);
  };
}();
var createProductItemV3 = /*#__PURE__*/function () {
  var _ref3 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee2(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          _context2.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/item'), {
            method: 'POST',
            data: data
          });
        case 2:
          res = _context2.sent;
          return _context2.abrupt("return", {
            data: res.result
          });
        case 4:
        case "end":
          return _context2.stop();
      }
    }, _callee2);
  }));
  return function createProductItemV3(_x3) {
    return _ref3.apply(this, arguments);
  };
}();
var updateProductItemV3 = /*#__PURE__*/function () {
  var _ref4 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee3(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee3$(_context3) {
      while (1) switch (_context3.prev = _context3.next) {
        case 0:
          _context3.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/item'), {
            method: 'PUT',
            data: data
          });
        case 2:
          res = _context3.sent;
          return _context3.abrupt("return", {
            data: res.result
          });
        case 4:
        case "end":
          return _context3.stop();
      }
    }, _callee3);
  }));
  return function updateProductItemV3(_x4) {
    return _ref4.apply(this, arguments);
  };
}();
var deleteProductItemV3 = /*#__PURE__*/function () {
  var _ref5 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee4(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee4$(_context4) {
      while (1) switch (_context4.prev = _context4.next) {
        case 0:
          _context4.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/item'), {
            method: 'PUT',
            data: D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default()({}, data), {}, {
              is_deleted: 1
            })
          });
        case 2:
          res = _context4.sent;
          return _context4.abrupt("return", {
            data: res.result
          });
        case 4:
        case "end":
          return _context4.stop();
      }
    }, _callee4);
  }));
  return function deleteProductItemV3(_x5) {
    return _ref5.apply(this, arguments);
  };
}();
var getDetailsProductItemV3 = /*#__PURE__*/function () {
  var _ref6 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee5(params) {
    var _res$result$data;
    var res, data;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee5$(_context5) {
      while (1) switch (_context5.prev = _context5.next) {
        case 0:
          _context5.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/item/detail'), {
            params: params
          });
        case 2:
          res = _context5.sent;
          data = (_res$result$data = res.result.data) === null || _res$result$data === void 0 ? void 0 : _res$result$data[0];
          if (data) {
            _context5.next = 6;
            break;
          }
          throw new Error('Not found');
        case 6:
          return _context5.abrupt("return", {
            data: data
          });
        case 7:
        case "end":
          return _context5.stop();
      }
    }, _callee5);
  }));
  return function getDetailsProductItemV3(_x6) {
    return _ref6.apply(this, arguments);
  };
}();
var getItemInventoryVouchers = /*#__PURE__*/function () {
  var _ref7 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee6(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee6$(_context6) {
      while (1) switch (_context6.prev = _context6.next) {
        case 0:
          _context6.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/item/inventory-vouchers'), {
            params: params
          });
        case 2:
          res = _context6.sent;
          return _context6.abrupt("return", res.result);
        case 4:
        case "end":
          return _context6.stop();
      }
    }, _callee6);
  }));
  return function getItemInventoryVouchers(_x7) {
    return _ref7.apply(this, arguments);
  };
}();
var getItemCustomerVouchersSum = /*#__PURE__*/function () {
  var _ref8 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee7(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee7$(_context7) {
      while (1) switch (_context7.prev = _context7.next) {
        case 0:
          _context7.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/item/customer-vouchers'), {
            params: params
          });
        case 2:
          res = _context7.sent;
          return _context7.abrupt("return", res.result);
        case 4:
        case "end":
          return _context7.stop();
      }
    }, _callee7);
  }));
  return function getItemCustomerVouchersSum(_x8) {
    return _ref8.apply(this, arguments);
  };
}();
var getItemSupplierVouchersSum = /*#__PURE__*/function () {
  var _ref9 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee8(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee8$(_context8) {
      while (1) switch (_context8.prev = _context8.next) {
        case 0:
          _context8.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/item/supplier-vouchers'), {
            params: params
          });
        case 2:
          res = _context8.sent;
          return _context8.abrupt("return", res.result);
        case 4:
        case "end":
          return _context8.stop();
      }
    }, _callee8);
  }));
  return function getItemSupplierVouchersSum(_x9) {
    return _ref9.apply(this, arguments);
  };
}();//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///58642
`)},96876:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   cT: function() { return /* binding */ uploadFile; },
/* harmony export */   uy: function() { return /* binding */ uploadFileHome; }
/* harmony export */ });
/* unused harmony export removeFileAttachment */
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(7837);
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(467);




var uploadFile = /*#__PURE__*/function () {
  var _ref = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee(data) {
    var formData, res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          formData = new FormData();
          formData.append('doctype', data.docType || '');
          formData.append('docname', data.docName || '');
          formData.append('folder', data.folder || 'Home/Attachments');
          formData.append('is_private', data.isPrivate || '1');
          formData.append('optimize', data.optimize || '0');
          formData.append('file', data.file);
          _context.next = 9;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/file/upload'), {
            method: 'POST',
            data: formData
          });
        case 9:
          res = _context.sent;
          return _context.abrupt("return", {
            data: res.result
          });
        case 11:
        case "end":
          return _context.stop();
      }
    }, _callee);
  }));
  return function uploadFile(_x) {
    return _ref.apply(this, arguments);
  };
}();
var uploadFileHome = /*#__PURE__*/function () {
  var _ref2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee2(data) {
    var formData, res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          formData = new FormData();
          formData.append('folder', data.folder || 'Home/Attachments');
          formData.append('is_private', data.isPrivate || '0');
          formData.append('is_home_folder', data.isPrivate || '1');
          formData.append('doctype', data.docType || '');
          formData.append('docname', data.docName || '');
          formData.append('optimize', data.optimize || '0');
          formData.append('file', data.file);
          _context2.next = 10;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/file/upload'), {
            method: 'POST',
            data: formData
          });
        case 10:
          res = _context2.sent;
          return _context2.abrupt("return", {
            data: res.result
          });
        case 12:
        case "end":
          return _context2.stop();
      }
    }, _callee2);
  }));
  return function uploadFileHome(_x2) {
    return _ref2.apply(this, arguments);
  };
}();
var removeFileAttachment = /*#__PURE__*/(/* unused pure expression or super */ null && (function () {
  var _ref3 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee3(data) {
    var res;
    return _regeneratorRuntime().wrap(function _callee3$(_context3) {
      while (1) switch (_context3.prev = _context3.next) {
        case 0:
          _context3.next = 2;
          return request(generateAPIPath('api/v2/file'), {
            method: 'DELETE',
            data: data
          });
        case 2:
          res = _context3.sent;
          return _context3.abrupt("return", {
            data: res.result
          });
        case 4:
        case "end":
          return _context3.stop();
      }
    }, _callee3);
  }));
  return function removeFileAttachment(_x3) {
    return _ref3.apply(this, arguments);
  };
}()));//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///96876
`)},89436:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: function() { return /* binding */ getItemGroupList; },
/* harmony export */   m: function() { return /* binding */ getItemList; }
/* harmony export */ });
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(97857);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(7837);
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(467);





var handleError = function handleError(error) {
  console.log("Error in services/warehouse: \\n".concat(error));
};
var CRUD_PATH = {
  READ: 'item',
  READ_GROUP: 'itemGroup'
};
var getItemList = /*#__PURE__*/function () {
  var _ref = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.prev = 0;
          _context.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)("api/v2/".concat(CRUD_PATH.READ)), {
            method: 'GET',
            params: D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({
              page: 1,
              size: 100
            }, (0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .getParamsReqList */ .vj)(params))
          });
        case 3:
          res = _context.sent;
          return _context.abrupt("return", {
            data: res.result.data || []
          });
        case 7:
          _context.prev = 7;
          _context.t0 = _context["catch"](0);
          handleError(_context.t0);
          return _context.abrupt("return", {
            data: []
          });
        case 11:
        case "end":
          return _context.stop();
      }
    }, _callee, null, [[0, 7]]);
  }));
  return function getItemList(_x) {
    return _ref.apply(this, arguments);
  };
}();
var getItemGroupList = /*#__PURE__*/function () {
  var _ref2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee2(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          _context2.prev = 0;
          _context2.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)("api/v2/".concat(CRUD_PATH.READ_GROUP)), {
            method: 'GET',
            params: D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({
              page: 1,
              size: 100
            }, (0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .getParamsReqList */ .vj)(params))
          });
        case 3:
          res = _context2.sent;
          return _context2.abrupt("return", {
            data: res.result.data || []
          });
        case 7:
          _context2.prev = 7;
          _context2.t0 = _context2["catch"](0);
          handleError(_context2.t0);
          return _context2.abrupt("return", {
            data: []
          });
        case 11:
        case "end":
          return _context2.stop();
      }
    }, _callee2, null, [[0, 7]]);
  }));
  return function getItemGroupList(_x2) {
    return _ref2.apply(this, arguments);
  };
}();//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///89436
`)},7369:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   W: function() { return /* binding */ toFirstUpperCase; },
/* harmony export */   w: function() { return /* binding */ nonAccentVietnamese; }
/* harmony export */ });
function nonAccentVietnamese(str) {
  var _str$toString;
  var _str = str === null || str === void 0 || (_str$toString = str.toString) === null || _str$toString === void 0 ? void 0 : _str$toString.call(str);
  if (typeof _str !== 'string') return str;
  _str = _str.toLowerCase();
  //     We can also use this instead of from line 11 to line 17
  //     str = str.replace(/\\u00E0|\\u00E1|\\u1EA1|\\u1EA3|\\u00E3|\\u00E2|\\u1EA7|\\u1EA5|\\u1EAD|\\u1EA9|\\u1EAB|\\u0103|\\u1EB1|\\u1EAF|\\u1EB7|\\u1EB3|\\u1EB5/g, "a");
  //     str = str.replace(/\\u00E8|\\u00E9|\\u1EB9|\\u1EBB|\\u1EBD|\\u00EA|\\u1EC1|\\u1EBF|\\u1EC7|\\u1EC3|\\u1EC5/g, "e");
  //     str = str.replace(/\\u00EC|\\u00ED|\\u1ECB|\\u1EC9|\\u0129/g, "i");
  //     str = str.replace(/\\u00F2|\\u00F3|\\u1ECD|\\u1ECF|\\u00F5|\\u00F4|\\u1ED3|\\u1ED1|\\u1ED9|\\u1ED5|\\u1ED7|\\u01A1|\\u1EDD|\\u1EDB|\\u1EE3|\\u1EDF|\\u1EE1/g, "o");
  //     str = str.replace(/\\u00F9|\\u00FA|\\u1EE5|\\u1EE7|\\u0169|\\u01B0|\\u1EEB|\\u1EE9|\\u1EF1|\\u1EED|\\u1EEF/g, "u");
  //     str = str.replace(/\\u1EF3|\\u00FD|\\u1EF5|\\u1EF7|\\u1EF9/g, "y");
  //     str = str.replace(/\\u0111/g, "d");
  _str = _str.replace(/\xE0|\xE1|\u1EA1|\u1EA3|\xE3|\xE2|\u1EA7|\u1EA5|\u1EAD|\u1EA9|\u1EAB|\u0103|\u1EB1|\u1EAF|\u1EB7|\u1EB3|\u1EB5/g, 'a');
  _str = _str.replace(/\xE8|\xE9|\u1EB9|\u1EBB|\u1EBD|\xEA|\u1EC1|\u1EBF|\u1EC7|\u1EC3|\u1EC5/g, 'e');
  _str = _str.replace(/\xEC|\xED|\u1ECB|\u1EC9|\u0129/g, 'i');
  _str = _str.replace(/\xF2|\xF3|\u1ECD|\u1ECF|\xF5|\xF4|\u1ED3|\u1ED1|\u1ED9|\u1ED5|\u1ED7|\u01A1|\u1EDD|\u1EDB|\u1EE3|\u1EDF|\u1EE1/g, 'o');
  _str = _str.replace(/\xF9|\xFA|\u1EE5|\u1EE7|\u0169|\u01B0|\u1EEB|\u1EE9|\u1EF1|\u1EED|\u1EEF/g, 'u');
  _str = _str.replace(/\u1EF3|\xFD|\u1EF5|\u1EF7|\u1EF9/g, 'y');
  _str = _str.replace(/\u0111/g, 'd');
  // Some system encode vietnamese combining accent as individual utf-8 characters
  _str = _str.replace(/\\u0300|\\u0301|\\u0303|\\u0309|\\u0323/g, ''); // Huy\u1EC1n s\u1EAFc h\u1ECFi ng\xE3 n\u1EB7ng
  _str = _str.replace(/\\u02C6|\\u0306|\\u031B/g, ''); // \xC2, \xCA, \u0102, \u01A0, \u01AF
  return _str.split(',').join('');
}
var toFirstUpperCase = function toFirstUpperCase(str) {
  if (!str) return '';
  return str.charAt(0).toUpperCase() + str.slice(1);
};//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///7369
`)}}]);
