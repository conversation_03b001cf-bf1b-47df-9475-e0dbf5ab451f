"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[3660],{13490:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   W: function() { return /* binding */ DEFAULT_FALLBACK_IMG; }
/* harmony export */ });
var DEFAULT_FALLBACK_IMG = 'data:image/png;base64,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';//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///13490
`)},65573:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(29905);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(85893);


var FallbackComponent = function FallbackComponent() {
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .ZP, {
    status: "403",
    title: "Kh\\xF4ng th\\u1EC3 truy c\\u1EADp",
    subTitle: "Xin l\\u1ED7i, b\\u1EA1n kh\\xF4ng c\\xF3 quy\\u1EC1n xem n\\u1ED9i dung n\\xE0y."
    //   extra={
    //     <Button type="primary" href="/">
    //       Back Home
    //     </Button>
    //   }
  });
};
/* harmony default export */ __webpack_exports__.Z = (FallbackComponent);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNjU1NzMuanMiLCJtYXBwaW5ncyI6Ijs7QUFBOEI7QUFBQTtBQUU5QixJQUFNRyxpQkFBaUIsR0FBRyxTQUFwQkEsaUJBQWlCQSxDQUFBLEVBQVM7RUFDOUIsb0JBQ0VELHNEQUFBLENBQUNGLHNEQUFNO0lBQ0xJLE1BQU0sRUFBQyxLQUFLO0lBQ1pDLEtBQUssRUFBQyxpQ0FBb0I7SUFDMUJDLFFBQVEsRUFBQztJQUNUO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7RUFBQSxDQUNELENBQUM7QUFFTixDQUFDO0FBRUQsc0RBQWVILGlCQUFpQiIsInNvdXJjZXMiOlsid2VicGFjazovL2FudC1kZXNpZ24tcHJvLy4vc3JjL2NvbXBvbmVudHMvRmFsbGJhY2tDb250ZW50L2luZGV4LnRzeD9mZGRhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFJlc3VsdCB9IGZyb20gJ2FudGQnO1xyXG5cclxuY29uc3QgRmFsbGJhY2tDb21wb25lbnQgPSAoKSA9PiB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxSZXN1bHRcclxuICAgICAgc3RhdHVzPVwiNDAzXCJcclxuICAgICAgdGl0bGU9XCJLaMO0bmcgdGjhu4MgdHJ1eSBj4bqtcFwiXHJcbiAgICAgIHN1YlRpdGxlPVwiWGluIGzhu5dpLCBi4bqhbiBraMO0bmcgY8OzIHF1eeG7gW4geGVtIG7hu5lpIGR1bmcgbsOgeS5cIlxyXG4gICAgICAvLyAgIGV4dHJhPXtcclxuICAgICAgLy8gICAgIDxCdXR0b24gdHlwZT1cInByaW1hcnlcIiBocmVmPVwiL1wiPlxyXG4gICAgICAvLyAgICAgICBCYWNrIEhvbWVcclxuICAgICAgLy8gICAgIDwvQnV0dG9uPlxyXG4gICAgICAvLyAgIH1cclxuICAgIC8+XHJcbiAgKTtcclxufTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IEZhbGxiYWNrQ29tcG9uZW50O1xyXG4iXSwibmFtZXMiOlsiUmVzdWx0IiwianN4IiwiX2pzeCIsIkZhbGxiYWNrQ29tcG9uZW50Iiwic3RhdHVzIiwidGl0bGUiLCJzdWJUaXRsZSJdLCJzb3VyY2VSb290IjoiIn0=
//# sourceURL=webpack-internal:///65573
`)},27076:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(5574);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(97857);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _utils_string__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(7369);
/* harmony import */ var _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(6110);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(7837);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(67294);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(85893);







var PageContainerTabsWithSearch = function PageContainerTabsWithSearch(_ref) {
  var tabsItems = _ref.tabsItems,
    _ref$searchParamsUrlK = _ref.searchParamsUrlKey,
    searchParamsUrlKey = _ref$searchParamsUrlK === void 0 ? 'tab' : _ref$searchParamsUrlK,
    _onTabChange = _ref.onTabChange,
    pageTitle = _ref.pageTitle;
  var tabsItemsFormat = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(function () {
    return tabsItems === null || tabsItems === void 0 ? void 0 : tabsItems.map(function (item) {
      return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({}, item), {}, {
        tabKey: item.tabKey || (0,_utils_string__WEBPACK_IMPORTED_MODULE_5__/* .nonAccentVietnamese */ .w)(item.label)
      });
    });
  }, [tabsItems]);
  var _useSearchParams = (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)(),
    _useSearchParams2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_0___default()(_useSearchParams, 2),
    searchParams = _useSearchParams2[0],
    setSearchParams = _useSearchParams2[1];
  var tabActive = searchParamsUrlKey ? searchParams.get(searchParamsUrlKey) : undefined;
  console.log('tabActive', tabActive);
  var setTabActive = searchParamsUrlKey ? function (tabActiveVal) {
    searchParams.set(searchParamsUrlKey, tabActiveVal.toString());
    setSearchParams(searchParams);
  } : undefined;
  var tabList = tabsItemsFormat === null || tabsItemsFormat === void 0 ? void 0 : tabsItemsFormat.map(function (item) {
    return {
      tab: item.label,
      key: item.tabKey,
      tabKey: item.tabKey
    };
  });
  var tabPageActive = searchParamsUrlKey ? (tabsItemsFormat === null || tabsItemsFormat === void 0 ? void 0 : tabsItemsFormat.find(function (item) {
    return item.tabKey === tabActive;
  })) || (tabsItemsFormat === null || tabsItemsFormat === void 0 ? void 0 : tabsItemsFormat[0]) : undefined;
  var ComponentActive = tabPageActive === null || tabPageActive === void 0 ? void 0 : tabPageActive.component;
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_6__/* .PageContainer */ ._z, {
    fixedHeader: true,
    extra: tabPageActive === null || tabPageActive === void 0 ? void 0 : tabPageActive.extraPage,
    tabActiveKey: tabPageActive === null || tabPageActive === void 0 ? void 0 : tabPageActive.tabKey,
    tabList: tabList,
    onTabChange: function onTabChange(tabActiveVal) {
      _onTabChange === null || _onTabChange === void 0 || _onTabChange(tabActiveVal);
      if (searchParamsUrlKey) setTabActive === null || setTabActive === void 0 || setTabActive(tabActiveVal);
    },
    title: pageTitle,
    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(react__WEBPACK_IMPORTED_MODULE_3__.Suspense, {
      fallback: (tabPageActive === null || tabPageActive === void 0 ? void 0 : tabPageActive.fallback) || null,
      children: ComponentActive && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(ComponentActive, {})
    })
  });
};
/* harmony default export */ __webpack_exports__.Z = (PageContainerTabsWithSearch);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///27076
`)},46270:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`
// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  Z: function() { return /* binding */ DeviceCard_DeviceCard; }
});

// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/regeneratorRuntime.js
var regeneratorRuntime = __webpack_require__(15009);
var regeneratorRuntime_default = /*#__PURE__*/__webpack_require__.n(regeneratorRuntime);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(99289);
var asyncToGenerator_default = /*#__PURE__*/__webpack_require__.n(asyncToGenerator);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/slicedToArray.js
var slicedToArray = __webpack_require__(5574);
var slicedToArray_default = /*#__PURE__*/__webpack_require__.n(slicedToArray);
// EXTERNAL MODULE: ./src/common/contanst/img.ts
var img = __webpack_require__(13490);
// EXTERNAL MODULE: ./src/utils/file.ts
var file = __webpack_require__(80320);
// EXTERNAL MODULE: ./node_modules/antd/es/typography/index.js + 18 modules
var typography = __webpack_require__(25514);
// EXTERNAL MODULE: ./node_modules/antd/es/space/index.js + 2 modules
var space = __webpack_require__(42075);
// EXTERNAL MODULE: ./node_modules/antd/es/tag/index.js + 5 modules
var tag = __webpack_require__(66309);
// EXTERNAL MODULE: ./node_modules/antd/es/avatar/index.js + 4 modules
var avatar = __webpack_require__(7134);
// EXTERNAL MODULE: ./node_modules/antd/es/switch/index.js + 2 modules
var es_switch = __webpack_require__(72269);
// EXTERNAL MODULE: ./node_modules/antd-use-styles/dist/antd-use-styles.js
var antd_use_styles = __webpack_require__(38513);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./src/pages/IoTDeviceMangement/hooks/useControlDevice.ts
var useControlDevice = __webpack_require__(55224);
// EXTERNAL MODULE: ./src/pages/IoTDeviceMangement/hooks/useDeviceOnlineChange.ts
var useDeviceOnlineChange = __webpack_require__(52180);
// EXTERNAL MODULE: ./src/pages/IoTDeviceMangement/components/MonitorDevice/index.tsx + 2 modules
var MonitorDevice = __webpack_require__(75806);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/QrcodeOutlined.js + 1 modules
var QrcodeOutlined = __webpack_require__(77516);
// EXTERNAL MODULE: ./src/.umi-production/exports.ts + 34 modules
var _umi_production_exports = __webpack_require__(7837);
// EXTERNAL MODULE: ./node_modules/antd/es/form/index.js + 18 modules
var es_form = __webpack_require__(98138);
// EXTERNAL MODULE: ./node_modules/antd/es/button/index.js + 9 modules
var es_button = __webpack_require__(14726);
// EXTERNAL MODULE: ./node_modules/antd/es/modal/index.js + 1 modules
var modal = __webpack_require__(85576);
// EXTERNAL MODULE: ./node_modules/antd/es/qr-code/index.js + 3 modules
var qr_code = __webpack_require__(10397);
// EXTERNAL MODULE: ./node_modules/html2canvas/dist/html2canvas.js
var html2canvas = __webpack_require__(46020);
var html2canvas_default = /*#__PURE__*/__webpack_require__.n(html2canvas);
// EXTERNAL MODULE: ./node_modules/jspdf/dist/jspdf.es.min.js + 1 modules
var jspdf_es_min = __webpack_require__(84730);
// EXTERNAL MODULE: ./node_modules/react/jsx-runtime.js
var jsx_runtime = __webpack_require__(85893);
;// CONCATENATED MODULE: ./src/pages/IoTDeviceMangement/components/QRCodeModal.tsx










var Item = es_form/* default */.Z.Item;
var Text = typography/* default */.Z.Text;
var QRCodeModal = function QRCodeModal(_ref) {
  var deviceId = _ref.deviceId;
  var handleDownload = function handleDownload() {
    var doc = new jspdf_es_min/* default */.ZP({
      unit: 'px',
      format: [400, 400]
    });
    var contentElement = document.getElementById("myqrcode_".concat(deviceId));
    if (contentElement) {
      html2canvas_default()(contentElement).then(function (canvas) {
        var imgData = canvas.toDataURL('image/png');
        doc.addImage(imgData, 'PNG', 0, 0, 400, 400);
        doc.save("device_".concat(deviceId, "_qr_code.pdf"));
      });
    }
  };
  var _useState = (0,react.useState)(false),
    _useState2 = slicedToArray_default()(_useState, 2),
    isOpen = _useState2[0],
    setOpen = _useState2[1];
  var intl = (0,_umi_production_exports.useIntl)();
  var showModal = function showModal() {
    setOpen(true);
  };
  var hideModal = function hideModal() {
    setOpen(false);
  };
  var handleOk = function handleOk() {
    handleDownload();
  };
  var handleCancel = function handleCancel() {
    hideModal();
  };
  return /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
    children: [/*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
      icon: /*#__PURE__*/(0,jsx_runtime.jsx)(QrcodeOutlined/* default */.Z, {}),
      onClick: function onClick(e) {
        e.stopPropagation();
        // e.preventDefault();
        showModal();
        return false;
      }
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(modal/* default */.Z, {
      title: intl.formatMessage({
        id: 'common.qr_code_for_device'
      }),
      open: isOpen,
      onOk: function onOk(e) {
        e.stopPropagation();
        handleOk();
      },
      onCancel: function onCancel(e) {
        e.stopPropagation();
        handleCancel();
      },
      okText: 'T\u1EA3i xu\u1ED1ng',
      cancelText: '\u0110\xF3ng',
      children: /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        id: "myqrcode_".concat(deviceId),
        style: {
          width: '400px',
          height: '400px',
          position: 'relative',
          margin: 'auto'
        },
        onClick: function onClick(e) {
          e.stopPropagation();
          handleOk();
        },
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(qr_code/* default */.Z, {
          size: 400,
          value: "".concat(deviceId),
          bgColor: "#fff",
          style: {
            paddingBlock: 20
          }
        })
      })
    })]
  });
};
/* harmony default export */ var components_QRCodeModal = (QRCodeModal);
;// CONCATENATED MODULE: ./src/pages/IoTDeviceMangement/components/DeviceCard/DeviceCard.tsx















var useStyles = (0,antd_use_styles/* createStyles */.k)(function (_ref) {
  var token = _ref.token;
  return {
    wrapper: {
      borderRadius: 0,
      borderWidth: 1,
      borderStyle: 'solid',
      borderColor: token.colorBorderSecondary,
      padding: token.paddingMD,
      width: '100%',
      // Th\xEAm width 100%
      maxWidth: '300px',
      // Gi\u1EDBi h\u1EA1n maxWidth
      margin: '0 auto',
      // C\u0103n gi\u1EEFa card
      display: 'flex',
      flexDirection: 'column',
      gap: 25,
      cursor: 'pointer',
      '&:hover': {
        borderColor: token.colorSuccessHover
      }
    },
    headerWrapper: {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      gap: token.padding
    },
    title: {
      marginBottom: '0!important'
    },
    imgWrapper: {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between'
      // marginBlockStart: token.marginMD,
    }
  };
});
var DeviceCard = function DeviceCard(_ref2) {
  var children = _ref2.children,
    data = _ref2.data,
    onPowerChange = _ref2.onPowerChange,
    isSharing = _ref2.isSharing;
  var styles = useStyles();
  var _useState = (0,react.useState)(false),
    _useState2 = slicedToArray_default()(_useState, 2),
    openMonitor = _useState2[0],
    setOpenMonitor = _useState2[1];
  var isHaveControlPower = data.function_list.find(function (item) {
    return (item === null || item === void 0 ? void 0 : item.identifier) && ['power', 'power_0'].includes(item === null || item === void 0 ? void 0 : item.identifier);
  });
  var _useControlDevice = (0,useControlDevice/* default */.Z)(),
    run = _useControlDevice.run,
    loading = _useControlDevice.loading;
  var handleControlPower = /*#__PURE__*/function () {
    var _ref3 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(value) {
      return regeneratorRuntime_default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _context.next = 2;
            return run({
              device_id_thingsboard: data.device_id_thingsboard,
              method: 'set_state',
              params: {
                power: value
              }
            });
          case 2:
            onPowerChange === null || onPowerChange === void 0 || onPowerChange();
          case 3:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }));
    return function handleControlPower(_x) {
      return _ref3.apply(this, arguments);
    };
  }();
  // online
  var lastedDataOnline = !!data.latest_data.find(function (item) {
    return item.key === 'online' && item.value === true;
  });
  var _useDeviceOnlineChang = (0,useDeviceOnlineChange/* useDeviceOnlineChange */.P)({
      deviceId: data.device_id_thingsboard,
      initOnline: lastedDataOnline
    }),
    isOnline = _useDeviceOnlineChang.isOnline;
  // console.log('isOnlineCard: ', isOnline);

  return /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
    children: [/*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
      className: styles.wrapper,
      onClick: function onClick() {
        return setOpenMonitor(true);
      },
      children: [/*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
        className: styles.headerWrapper,
        children: [/*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
          children: [/*#__PURE__*/(0,jsx_runtime.jsx)(typography/* default */.Z.Title, {
            level: 5,
            className: styles.title,
            children: data === null || data === void 0 ? void 0 : data.label
          }), /*#__PURE__*/(0,jsx_runtime.jsx)(typography/* default */.Z.Text, {
            type: "secondary",
            children: data === null || data === void 0 ? void 0 : data.zone_label
          })]
        }), /*#__PURE__*/(0,jsx_runtime.jsxs)(space/* default */.Z, {
          direction: "vertical",
          align: "center",
          children: [/*#__PURE__*/(0,jsx_runtime.jsx)(components_QRCodeModal, {
            deviceId: data.device_id_thingsboard
          }), /*#__PURE__*/(0,jsx_runtime.jsx)(tag/* default */.Z, {
            color: isOnline ? 'success' : 'error',
            children: isOnline ? 'Online' : 'Offline'
          })]
        })]
      }), /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
        className: styles.imgWrapper,
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)(avatar/* default */.C, {
          size: 45,
          src: data !== null && data !== void 0 && data.device_profile_image ? (0,file/* genDownloadUrl */.h)(data.device_profile_image) : img/* DEFAULT_FALLBACK_IMG */.W
        }), isHaveControlPower && /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
          style: {
            width: '40%'
          },
          onClick: function onClick(e) {
            return e.stopPropagation();
          },
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_switch/* default */.Z, {
            loading: loading,
            onChange: handleControlPower,
            checkedChildren: "On",
            unCheckedChildren: "Off",
            disabled: !isOnline // n\u1EBFu \u0111ang off line th\xEC kh\xF4ng \u0111\u01B0\u1EE3c control
            ,
            checked: isOnline
          })
        })]
      })]
    }), openMonitor && /*#__PURE__*/(0,jsx_runtime.jsx)(MonitorDevice["default"], {
      open: openMonitor,
      deviceId: data.name,
      onOpenChange: setOpenMonitor,
      callBack: onPowerChange,
      isSharing: isSharing
    })]
  });
};
/* harmony default export */ var DeviceCard_DeviceCard = (DeviceCard);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///46270
`)},17721:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _common_contanst_constanst__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(86604);
/* harmony import */ var _services_devices__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(84081);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(43471);
/* harmony import */ var _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(64176);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(7837);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(31418);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(76216);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(14726);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(2487);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(67294);
/* harmony import */ var _DeviceCard_DeviceCard__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(46270);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(85893);











var ListDevice = function ListDevice(_ref) {
  var zoneId = _ref.zoneId,
    projectId = _ref.projectId,
    title = _ref.title;
  var _useIntl = (0,_umijs_max__WEBPACK_IMPORTED_MODULE_4__.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  var _App$useApp = antd__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .Z.useApp(),
    message = _App$useApp.message;
  var actionRef = (0,react__WEBPACK_IMPORTED_MODULE_5__.useRef)(null);
  var handleReload = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)(function () {
    var _actionRef$current;
    (_actionRef$current = actionRef.current) === null || _actionRef$current === void 0 || _actionRef$current.reload();
  }, []);
  (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(function () {
    handleReload();
  }, [zoneId, projectId]);
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsx)(antd__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Z, {
    title: title,
    style: {
      paddingInline: 0
    },
    extra: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsx)(antd__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .ZP, {
      icon: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .Z, {}),
      onClick: handleReload
    }),
    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsx)(_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_12__/* .ProList */ .Rs, {
      actionRef: actionRef,
      rowKey: 'name',
      renderItem: function renderItem(item) {
        return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsx)(antd__WEBPACK_IMPORTED_MODULE_13__/* ["default"] */ .Z.Item, {
          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsx)(_DeviceCard_DeviceCard__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Z, {
            onPowerChange: handleReload,
            data: item
          })
        });
      },
      grid: {
        gutter: [16, 16],
        // Gi\u1EA3m gutter xu\u1ED1ng
        xs: 1,
        // Mobile hi\u1EC3n th\u1ECB 1 c\u1ED9t
        sm: 2,
        // Tablet nh\u1ECF 2 c\u1ED9t
        md: 2,
        // Tablet 2 c\u1ED9t
        lg: 3,
        // Desktop 3 c\u1ED9t
        xl: 4,
        // Large desktop 4 c\u1ED9t
        xxl: 5 // Extra large 5 c\u1ED9t
      },
      request: ( /*#__PURE__*/function () {
        var _ref2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee(params) {
          var filters, res;
          return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee$(_context) {
            while (1) switch (_context.prev = _context.next) {
              case 0:
                _context.prev = 0;
                filters = [];
                if (zoneId) {
                  filters.push([_common_contanst_constanst__WEBPACK_IMPORTED_MODULE_2__/* .DOCTYPE_ERP */ .lH.iotDevice, 'zone_id', '=', zoneId]);
                }
                _context.next = 5;
                return (0,_services_devices__WEBPACK_IMPORTED_MODULE_3__/* .deviceInProjectList */ .pL)({
                  filters: filters,
                  page: params.current,
                  size: params.pageSize,
                  fields: ['*'],
                  order_by: 'creation',
                  project_id: projectId
                });
              case 5:
                res = _context.sent;
                return _context.abrupt("return", {
                  data: res,
                  total: res.length
                });
              case 9:
                _context.prev = 9;
                _context.t0 = _context["catch"](0);
                message.error(formatMessage({
                  id: 'common.error'
                }));
                return _context.abrupt("return", {
                  data: []
                });
              case 13:
              case "end":
                return _context.stop();
            }
          }, _callee, null, [[0, 9]]);
        }));
        return function (_x) {
          return _ref2.apply(this, arguments);
        };
      }()),
      pagination: {
        pageSize: 20
      }
    })
  });
};
/* harmony default export */ __webpack_exports__.Z = (ListDevice);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///17721
`)},55224:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var _services_devices__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(84081);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(7837);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(31418);



var useControlDevice = function useControlDevice() {
  var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},
    _onSuccess = _ref.onSuccess;
  var _App$useApp = antd__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z.useApp(),
    message = _App$useApp.message;
  var req = (0,_umijs_max__WEBPACK_IMPORTED_MODULE_1__.useRequest)(_services_devices__WEBPACK_IMPORTED_MODULE_0__/* .controlDevice */ .A2, {
    onError: function onError(e, params) {
      message.error('Kh\xF4ng th\xE0nh c\xF4ng');
    },
    onSuccess: function onSuccess(data, params) {
      message.success('Th\xE0nh c\xF4ng');
      _onSuccess === null || _onSuccess === void 0 || _onSuccess(data);
    },
    manual: true
  });
  return req;
};
/* harmony default export */ __webpack_exports__.Z = (useControlDevice);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///55224
`)},34009:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return /* binding */ IoTDeviceMangement; }
});

// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./src/components/FallbackContent/index.tsx
var FallbackContent = __webpack_require__(65573);
// EXTERNAL MODULE: ./src/components/PageContainerTabsWithSearch/index.tsx
var PageContainerTabsWithSearch = __webpack_require__(27076);
// EXTERNAL MODULE: ./src/.umi-production/exports.ts + 34 modules
var _umi_production_exports = __webpack_require__(7837);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/slicedToArray.js
var slicedToArray = __webpack_require__(5574);
var slicedToArray_default = /*#__PURE__*/__webpack_require__.n(slicedToArray);
// EXTERNAL MODULE: ./node_modules/antd/es/card/index.js + 3 modules
var card = __webpack_require__(76216);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/regeneratorRuntime.js
var regeneratorRuntime = __webpack_require__(15009);
var regeneratorRuntime_default = /*#__PURE__*/__webpack_require__.n(regeneratorRuntime);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(99289);
var asyncToGenerator_default = /*#__PURE__*/__webpack_require__.n(asyncToGenerator);
// EXTERNAL MODULE: ./src/services/projects.ts
var projects = __webpack_require__(78263);
// EXTERNAL MODULE: ./src/utils/file.ts
var file = __webpack_require__(80320);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/Select/index.js
var Select = __webpack_require__(64317);
// EXTERNAL MODULE: ./node_modules/antd/es/space/index.js + 2 modules
var space = __webpack_require__(42075);
// EXTERNAL MODULE: ./node_modules/antd/es/avatar/index.js + 4 modules
var avatar = __webpack_require__(7134);
// EXTERNAL MODULE: ./node_modules/nanoid/index.browser.js
var index_browser = __webpack_require__(53416);
// EXTERNAL MODULE: ./node_modules/react/jsx-runtime.js
var jsx_runtime = __webpack_require__(85893);
;// CONCATENATED MODULE: ./src/pages/IoTDeviceMangement/components/MyDevice/SelectProject.tsx











var allValue = (0,index_browser/* nanoid */.x0)();
var SelectProject = function SelectProject(_ref) {
  var onChange = _ref.onChange,
    valueProjectID = _ref.valueProjectID;
  var _useState = (0,react.useState)(allValue),
    _useState2 = slicedToArray_default()(_useState, 2),
    projectIdLocal = _useState2[0],
    setProjectIdLocal = _useState2[1];
  var optionRender = (0,react.useCallback)(function (item) {
    var _item$data, _item$data2;
    return /*#__PURE__*/(0,jsx_runtime.jsxs)(space/* default */.Z, {
      children: [!(item !== null && item !== void 0 && (_item$data = item.data) !== null && _item$data !== void 0 && _item$data.image) ? null : /*#__PURE__*/(0,jsx_runtime.jsx)(avatar/* default */.C, {
        shape: "square",
        size: 'small',
        src: (0,file/* genDownloadUrl */.h)(item === null || item === void 0 || (_item$data2 = item.data) === null || _item$data2 === void 0 ? void 0 : _item$data2.image)
      }), /*#__PURE__*/(0,jsx_runtime.jsx)("span", {
        children: item.label
      })]
    });
  }, []);
  var handleChange = function handleChange(newProjectId) {
    var newProjectIdSelected = newProjectId === allValue ? undefined : newProjectId;
    onChange === null || onChange === void 0 || onChange(newProjectIdSelected);
    setProjectIdLocal(newProjectId);
  };
  return /*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
    noStyle: true,
    width: 300,
    allowClear: false,
    placeholder: 'Ch\u1ECDn d\u01B0 \xE1n',
    onChange: handleChange,
    fieldProps: {
      optionItemRender: optionRender,
      value: projectIdLocal || undefined
    },
    request: /*#__PURE__*/asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
      var res, data;
      return regeneratorRuntime_default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _context.next = 2;
            return (0,projects/* projectList */.d9)({
              filters: [],
              page: 1,
              size: 1000,
              fields: ['*'],
              order_by: 'creation'
            });
          case 2:
            res = _context.sent;
            data = res.data.map(function (item) {
              return {
                label: "".concat(item.label),
                value: item.name,
                data: {
                  image: item.image,
                  description: item.name
                }
              };
            }); //
            // data.unshift({
            //   label: 'T\u1EA5t c\u1EA3 d\u1EF1 \xE1n',
            //   value: allValue,
            //   data: {
            //     image: null,
            //     description: allValue,
            //   },
            // });
            // auto select 0
            handleChange(data[0].value);
            return _context.abrupt("return", data);
          case 6:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }))
  });
};
/* harmony default export */ var MyDevice_SelectProject = (SelectProject);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/defineProperty.js
var defineProperty = __webpack_require__(9783);
var defineProperty_default = /*#__PURE__*/__webpack_require__.n(defineProperty);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/toConsumableArray.js
var toConsumableArray = __webpack_require__(19632);
var toConsumableArray_default = /*#__PURE__*/__webpack_require__.n(toConsumableArray);
// EXTERNAL MODULE: ./src/common/contanst/constanst.ts
var constanst = __webpack_require__(86604);
// EXTERNAL MODULE: ./src/services/zones.ts
var zones = __webpack_require__(95728);
// EXTERNAL MODULE: ./node_modules/antd/es/spin/index.js + 2 modules
var spin = __webpack_require__(75081);
// EXTERNAL MODULE: ./node_modules/antd/es/tabs/index.js + 30 modules
var tabs = __webpack_require__(48096);
// EXTERNAL MODULE: ./src/pages/IoTDeviceMangement/components/MyDevice/ListDevice.tsx
var ListDevice = __webpack_require__(17721);
;// CONCATENATED MODULE: ./src/pages/IoTDeviceMangement/components/MyDevice/ZoneTabs.tsx












var isAllKey = Symbol('all');
var ZoneTabs_allValue = (0,index_browser/* nanoid */.x0)();
var ZoneTabs = function ZoneTabs(_ref) {
  var _data$res;
  var projectId = _ref.projectId;
  var formatRender = (0,react.useCallback)(function (item) {
    return {
      label: item.label,
      key: item.name,
      tabKey: item.name,
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(ListDevice/* default */.Z, {
        projectId: projectId,
        zoneId: item !== null && item !== void 0 && item[isAllKey] ? undefined : item.name
      })
    };
  }, [projectId]);
  var _useRequest = (0,_umi_production_exports.useRequest)( /*#__PURE__*/asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
      var _pagination;
      var filters, res, totalElements, data;
      return regeneratorRuntime_default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            filters = [];
            if (projectId) {
              filters.push([constanst/* DOCTYPE_ERP */.lH.iotZone, 'project_id', '=', projectId]);
            }
            _context.next = 4;
            return (0,zones/* zoneList */.ly)({
              filters: filters,
              page: 1,
              size: 200,
              fields: ['*'],
              order_by: 'creation'
            });
          case 4:
            res = _context.sent;
            // ph\xE2n trang total \u0111ang tr\u1EA3 v\u1EC1 0 , n\xEAn \u0111\u1EC3 l\xE0 data.length
            totalElements = res.data.length || (res === null || res === void 0 || (_pagination = res.pagination) === null || _pagination === void 0 ? void 0 : _pagination.totalElements);
            data = [defineProperty_default()({
              // label: \`T\u1EA5t c\u1EA3 khu v\u1EF1c (\${totalElements})\`,
              label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
                id: 'common.all_zone'
              }),
              name: ZoneTabs_allValue
            }, isAllKey, true)].concat(toConsumableArray_default()(res.data));
            return _context.abrupt("return", {
              data: {
                res: data,
                total: totalElements
              }
            });
          case 8:
          case "end":
            return _context.stop();
        }
      }, _callee);
    })), {
      refreshDeps: [projectId]
    }),
    data = _useRequest.data,
    refresh = _useRequest.refresh,
    loading = _useRequest.loading;
  var dataTabItem = data === null || data === void 0 || (_data$res = data.res) === null || _data$res === void 0 ? void 0 : _data$res.map(formatRender);
  return /*#__PURE__*/(0,jsx_runtime.jsx)(spin/* default */.Z, {
    spinning: loading,
    children: /*#__PURE__*/(0,jsx_runtime.jsx)(tabs/* default */.Z, {
      defaultActiveKey: ZoneTabs_allValue,
      items: dataTabItem
    })
  });
};
/* harmony default export */ var MyDevice_ZoneTabs = (ZoneTabs);
;// CONCATENATED MODULE: ./src/pages/IoTDeviceMangement/components/MyDevice/DeviceManager.tsx






var DeviceManager = function DeviceManager(_ref) {
  var children = _ref.children;
  var _useState = (0,react.useState)(),
    _useState2 = slicedToArray_default()(_useState, 2),
    projectSelected = _useState2[0],
    setProjectSelect = _useState2[1];
  var onProjectIdChange = (0,react.useCallback)(function (projectId) {
    setProjectSelect(projectId);
  }, [setProjectSelect]);
  return /*#__PURE__*/(0,jsx_runtime.jsx)(card/* default */.Z, {
    extra: [/*#__PURE__*/(0,jsx_runtime.jsx)(MyDevice_SelectProject, {
      onChange: onProjectIdChange,
      valueProjectID: projectSelected
    }, "SelectProject")],
    children: /*#__PURE__*/(0,jsx_runtime.jsx)(MyDevice_ZoneTabs, {
      projectId: projectSelected
    })
  });
};
/* harmony default export */ var MyDevice_DeviceManager = (DeviceManager);
// EXTERNAL MODULE: ./src/services/sharing-device.ts
var sharing_device = __webpack_require__(65155);
;// CONCATENATED MODULE: ./src/pages/IoTDeviceMangement/components/SharedDevice/SelectProject.tsx











var SelectProject_allValue = (0,index_browser/* nanoid */.x0)();
var SelectProject_SelectProject = function SelectProject(_ref) {
  var onChange = _ref.onChange,
    valueProjectID = _ref.valueProjectID;
  var _useState = (0,react.useState)(SelectProject_allValue),
    _useState2 = slicedToArray_default()(_useState, 2),
    projectIdLocal = _useState2[0],
    setProjectIdLocal = _useState2[1];
  var optionRender = (0,react.useCallback)(function (item) {
    var _item$data, _item$data2;
    return /*#__PURE__*/(0,jsx_runtime.jsxs)(space/* default */.Z, {
      children: [!(item !== null && item !== void 0 && (_item$data = item.data) !== null && _item$data !== void 0 && _item$data.image) ? null : /*#__PURE__*/(0,jsx_runtime.jsx)(avatar/* default */.C, {
        shape: "square",
        size: 'small',
        src: (0,file/* genDownloadUrl */.h)(item === null || item === void 0 || (_item$data2 = item.data) === null || _item$data2 === void 0 ? void 0 : _item$data2.image)
      }), /*#__PURE__*/(0,jsx_runtime.jsx)("span", {
        children: item.label
      })]
    });
  }, []);
  var handleChange = function handleChange(newProjectId) {
    var newProjectIdSelected = newProjectId === SelectProject_allValue ? undefined : newProjectId;
    onChange === null || onChange === void 0 || onChange(newProjectIdSelected);
    setProjectIdLocal(newProjectId);
  };
  return /*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
    noStyle: true,
    width: 300,
    allowClear: false,
    placeholder: 'Ch\u1ECDn d\u01B0 \xE1n',
    onChange: handleChange,
    fieldProps: {
      optionItemRender: optionRender,
      value: projectIdLocal || undefined
    },
    request: /*#__PURE__*/asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
      var res, data;
      return regeneratorRuntime_default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _context.next = 2;
            return (0,sharing_device/* getSharingTree */.ar)();
          case 2:
            res = _context.sent;
            data = res.project_list.map(function (item) {
              return {
                label: "".concat(item.project_label),
                value: item.project_id
                // data: {
                //   image: item.image,
                //   description: item.name,
                // },
              };
            }); //
            // data.unshift({
            //   label: 'T\u1EA5t c\u1EA3 d\u1EF1 \xE1n',
            //   value: allValue,
            //   data: {
            //     image: null,
            //     description: allValue,
            //   },
            // });
            // auto select 0
            handleChange(data[0].value);
            return _context.abrupt("return", data);
          case 6:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }))
  });
};
/* harmony default export */ var SharedDevice_SelectProject = (SelectProject_SelectProject);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/ReloadOutlined.js
var ReloadOutlined = __webpack_require__(43471);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-list/es/index.js + 20 modules
var es = __webpack_require__(64176);
// EXTERNAL MODULE: ./node_modules/antd/es/app/index.js + 1 modules
var app = __webpack_require__(31418);
// EXTERNAL MODULE: ./node_modules/antd/es/button/index.js + 9 modules
var es_button = __webpack_require__(14726);
// EXTERNAL MODULE: ./node_modules/antd/es/list/index.js + 3 modules
var list = __webpack_require__(2487);
// EXTERNAL MODULE: ./src/pages/IoTDeviceMangement/components/DeviceCard/DeviceCard.tsx + 1 modules
var DeviceCard = __webpack_require__(46270);
;// CONCATENATED MODULE: ./src/pages/IoTDeviceMangement/components/SharedDevice/ListDevice.tsx











var ListDevice_ListDevice = function ListDevice(_ref) {
  var zoneId = _ref.zoneId,
    projectId = _ref.projectId,
    title = _ref.title;
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  var _App$useApp = app/* default */.Z.useApp(),
    message = _App$useApp.message;
  var actionRef = (0,react.useRef)(null);
  var handleReload = (0,react.useCallback)(function () {
    var _actionRef$current;
    (_actionRef$current = actionRef.current) === null || _actionRef$current === void 0 || _actionRef$current.reload();
  }, []);
  (0,react.useEffect)(function () {
    handleReload();
  }, [zoneId, projectId]);
  return /*#__PURE__*/(0,jsx_runtime.jsx)(card/* default */.Z, {
    title: title,
    style: {
      paddingInline: 0
    },
    extra: /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
      icon: /*#__PURE__*/(0,jsx_runtime.jsx)(ReloadOutlined/* default */.Z, {}),
      onClick: handleReload
    }),
    children: /*#__PURE__*/(0,jsx_runtime.jsx)(es/* ProList */.Rs, {
      actionRef: actionRef,
      rowKey: 'name',
      renderItem: function renderItem(item) {
        return /*#__PURE__*/(0,jsx_runtime.jsx)(list/* default */.Z.Item, {
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(DeviceCard/* default */.Z, {
            isSharing: true,
            onPowerChange: handleReload,
            data: item
          })
        });
      },
      grid: {
        gutter: [16, 16],
        // Gi\u1EA3m gutter xu\u1ED1ng
        xs: 1,
        // Mobile hi\u1EC3n th\u1ECB 1 c\u1ED9t
        sm: 2,
        // Tablet nh\u1ECF 2 c\u1ED9t
        md: 2,
        // Tablet 2 c\u1ED9t
        lg: 3,
        // Desktop 3 c\u1ED9t
        xl: 4,
        // Large desktop 4 c\u1ED9t
        xxl: 5 // Extra large 5 c\u1ED9t
      },
      request: ( /*#__PURE__*/function () {
        var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(params) {
          var filters, res;
          return regeneratorRuntime_default()().wrap(function _callee$(_context) {
            while (1) switch (_context.prev = _context.next) {
              case 0:
                _context.prev = 0;
                filters = [];
                if (zoneId) {
                  filters.push([constanst/* DOCTYPE_ERP */.lH.iotDevice, 'zone_id', '=', zoneId]);
                }
                _context.next = 5;
                return (0,sharing_device/* deviceSharedInProjectList */.kE)({
                  filters: filters,
                  page: params.current,
                  size: params.pageSize,
                  fields: ['*'],
                  order_by: 'creation',
                  project_id: projectId
                });
              case 5:
                res = _context.sent;
                return _context.abrupt("return", {
                  data: res,
                  total: res.length
                });
              case 9:
                _context.prev = 9;
                _context.t0 = _context["catch"](0);
                message.error(formatMessage({
                  id: 'common.error'
                }));
                return _context.abrupt("return", {
                  data: []
                });
              case 13:
              case "end":
                return _context.stop();
            }
          }, _callee, null, [[0, 9]]);
        }));
        return function (_x) {
          return _ref2.apply(this, arguments);
        };
      }()),
      pagination: {
        pageSize: 20
      }
    })
  });
};
/* harmony default export */ var SharedDevice_ListDevice = (ListDevice_ListDevice);
;// CONCATENATED MODULE: ./src/pages/IoTDeviceMangement/components/SharedDevice/ZoneTabs.tsx











var ZoneTabs_isAllKey = Symbol('all');
var SharedDevice_ZoneTabs_allValue = (0,index_browser/* nanoid */.x0)();
var ZoneTabs_ZoneTabs = function ZoneTabs(_ref) {
  var _data$res;
  var projectId = _ref.projectId;
  var formatRender = (0,react.useCallback)(function (item) {
    return {
      label: item.label,
      key: item.name,
      tabKey: item.name,
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(SharedDevice_ListDevice, {
        projectId: projectId,
        zoneId: item !== null && item !== void 0 && item[ZoneTabs_isAllKey] ? undefined : item.name
      })
    };
  }, [projectId]);
  var _useRequest = (0,_umi_production_exports.useRequest)( /*#__PURE__*/asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
      var _r$project_list$find;
      var r, zoneList, total, zoneListFormat, data;
      return regeneratorRuntime_default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _context.next = 2;
            return (0,sharing_device/* getSharingTree */.ar)();
          case 2:
            r = _context.sent;
            zoneList = ((_r$project_list$find = r.project_list.find(function (item) {
              return item.project_id === projectId;
            })) === null || _r$project_list$find === void 0 ? void 0 : _r$project_list$find.zone_list) || [];
            total = zoneList.length;
            zoneListFormat = zoneList.map(function (item) {
              return {
                label: item.zone_label,
                name: item.zone_id,
                zone_name: item.zone_id
              };
            });
            data = [defineProperty_default()({
              // label: \`T\u1EA5t c\u1EA3 khu v\u1EF1c (\${totalElements})\`,
              label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
                id: 'common.all_zone'
              }),
              name: SharedDevice_ZoneTabs_allValue
            }, ZoneTabs_isAllKey, true)].concat(toConsumableArray_default()(zoneListFormat));
            return _context.abrupt("return", {
              data: {
                res: data,
                total: total
              }
            });
          case 8:
          case "end":
            return _context.stop();
        }
      }, _callee);
    })), {
      refreshDeps: [projectId]
    }),
    data = _useRequest.data,
    refresh = _useRequest.refresh,
    loading = _useRequest.loading;
  var handleReload = function handleReload() {
    refresh();
  };
  var dataTabItem = data === null || data === void 0 || (_data$res = data.res) === null || _data$res === void 0 ? void 0 : _data$res.map(formatRender);
  return /*#__PURE__*/(0,jsx_runtime.jsx)(spin/* default */.Z, {
    spinning: loading,
    children: /*#__PURE__*/(0,jsx_runtime.jsx)(tabs/* default */.Z, {
      defaultActiveKey: SharedDevice_ZoneTabs_allValue,
      items: dataTabItem
    })
  });
};
/* harmony default export */ var SharedDevice_ZoneTabs = (ZoneTabs_ZoneTabs);
;// CONCATENATED MODULE: ./src/pages/IoTDeviceMangement/components/SharedDevice/DeviceManager.tsx






var DeviceSharedManager = function DeviceSharedManager(_ref) {
  var children = _ref.children;
  var _useState = (0,react.useState)(),
    _useState2 = slicedToArray_default()(_useState, 2),
    projectSelected = _useState2[0],
    setProjectSelect = _useState2[1];
  var _useState3 = (0,react.useState)(),
    _useState4 = slicedToArray_default()(_useState3, 2),
    zoneSelected = _useState4[0],
    setZoneSelected = _useState4[1];
  var onProjectIdChange = (0,react.useCallback)(function (projectId) {
    setProjectSelect(projectId);
    // reset zone selected
    setZoneSelected(undefined);
  }, [setProjectSelect]);
  return /*#__PURE__*/(0,jsx_runtime.jsx)(card/* default */.Z, {
    extra: [/*#__PURE__*/(0,jsx_runtime.jsx)(SharedDevice_SelectProject, {
      onChange: onProjectIdChange,
      valueProjectID: projectSelected
    }, "SelectProject")],
    children: /*#__PURE__*/(0,jsx_runtime.jsx)(SharedDevice_ZoneTabs, {
      projectId: projectSelected
    })
  });
};
/* harmony default export */ var SharedDevice_DeviceManager = (DeviceSharedManager);
;// CONCATENATED MODULE: ./src/pages/IoTDeviceMangement/index.tsx

// import SelectDevice from './components/SelectDevice';






var ItemManagement = function ItemManagement() {
  var access = (0,_umi_production_exports.useAccess)();
  var canRead = access.canAccessPageIotDeviceManagement();
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  return /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.Access, {
    accessible: canRead,
    fallback: /*#__PURE__*/(0,jsx_runtime.jsx)(FallbackContent/* default */.Z, {}),
    children: /*#__PURE__*/(0,jsx_runtime.jsx)(PageContainerTabsWithSearch/* default */.Z, {
      tabsItems: [{
        label: formatMessage({
          id: 'common.my_devices'
        }),
        component: function component() {
          return /*#__PURE__*/(0,jsx_runtime.jsx)(MyDevice_DeviceManager, {});
        }
      }, {
        label: formatMessage({
          id: 'common.shared_devices'
        }),
        component: function component() {
          return /*#__PURE__*/(0,jsx_runtime.jsx)(SharedDevice_DeviceManager, {});
        }
      }]
    })
  });
};
/* harmony default export */ var IoTDeviceMangement = (ItemManagement);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///34009
`)},80320:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   h: function() { return /* binding */ genDownloadUrl; }
/* harmony export */ });
/* harmony import */ var _common_contanst__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(95028);

var genDownloadUrl = function genDownloadUrl(fileUrl) {
  return "".concat(_common_contanst__WEBPACK_IMPORTED_MODULE_0__/* .API_URL_DEV */ .v, "/api/v2/file/download?file_url=").concat(fileUrl);
};//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiODAzMjAuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFnRDtBQUV6QyxJQUFNQyxjQUFjLEdBQUcsU0FBakJBLGNBQWNBLENBQUlDLE9BQWUsRUFBSztFQUNqRCxVQUFBQyxNQUFBLENBQVVILGtFQUFXLHFDQUFBRyxNQUFBLENBQWtDRCxPQUFPO0FBQ2hFLENBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hbnQtZGVzaWduLXByby8uL3NyYy91dGlscy9maWxlLnRzP2FlNDQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQVBJX1VSTF9ERVYgfSBmcm9tICdAL2NvbW1vbi9jb250YW5zdCc7XHJcblxyXG5leHBvcnQgY29uc3QgZ2VuRG93bmxvYWRVcmwgPSAoZmlsZVVybDogc3RyaW5nKSA9PiB7XHJcbiAgcmV0dXJuIGAke0FQSV9VUkxfREVWfS9hcGkvdjIvZmlsZS9kb3dubG9hZD9maWxlX3VybD0ke2ZpbGVVcmx9YDtcclxufTtcclxuIl0sIm5hbWVzIjpbIkFQSV9VUkxfREVWIiwiZ2VuRG93bmxvYWRVcmwiLCJmaWxlVXJsIiwiY29uY2F0Il0sInNvdXJjZVJvb3QiOiIifQ==
//# sourceURL=webpack-internal:///80320
`)},7369:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   W: function() { return /* binding */ toFirstUpperCase; },
/* harmony export */   w: function() { return /* binding */ nonAccentVietnamese; }
/* harmony export */ });
function nonAccentVietnamese(str) {
  var _str$toString;
  var _str = str === null || str === void 0 || (_str$toString = str.toString) === null || _str$toString === void 0 ? void 0 : _str$toString.call(str);
  if (typeof _str !== 'string') return str;
  _str = _str.toLowerCase();
  //     We can also use this instead of from line 11 to line 17
  //     str = str.replace(/\\u00E0|\\u00E1|\\u1EA1|\\u1EA3|\\u00E3|\\u00E2|\\u1EA7|\\u1EA5|\\u1EAD|\\u1EA9|\\u1EAB|\\u0103|\\u1EB1|\\u1EAF|\\u1EB7|\\u1EB3|\\u1EB5/g, "a");
  //     str = str.replace(/\\u00E8|\\u00E9|\\u1EB9|\\u1EBB|\\u1EBD|\\u00EA|\\u1EC1|\\u1EBF|\\u1EC7|\\u1EC3|\\u1EC5/g, "e");
  //     str = str.replace(/\\u00EC|\\u00ED|\\u1ECB|\\u1EC9|\\u0129/g, "i");
  //     str = str.replace(/\\u00F2|\\u00F3|\\u1ECD|\\u1ECF|\\u00F5|\\u00F4|\\u1ED3|\\u1ED1|\\u1ED9|\\u1ED5|\\u1ED7|\\u01A1|\\u1EDD|\\u1EDB|\\u1EE3|\\u1EDF|\\u1EE1/g, "o");
  //     str = str.replace(/\\u00F9|\\u00FA|\\u1EE5|\\u1EE7|\\u0169|\\u01B0|\\u1EEB|\\u1EE9|\\u1EF1|\\u1EED|\\u1EEF/g, "u");
  //     str = str.replace(/\\u1EF3|\\u00FD|\\u1EF5|\\u1EF7|\\u1EF9/g, "y");
  //     str = str.replace(/\\u0111/g, "d");
  _str = _str.replace(/\xE0|\xE1|\u1EA1|\u1EA3|\xE3|\xE2|\u1EA7|\u1EA5|\u1EAD|\u1EA9|\u1EAB|\u0103|\u1EB1|\u1EAF|\u1EB7|\u1EB3|\u1EB5/g, 'a');
  _str = _str.replace(/\xE8|\xE9|\u1EB9|\u1EBB|\u1EBD|\xEA|\u1EC1|\u1EBF|\u1EC7|\u1EC3|\u1EC5/g, 'e');
  _str = _str.replace(/\xEC|\xED|\u1ECB|\u1EC9|\u0129/g, 'i');
  _str = _str.replace(/\xF2|\xF3|\u1ECD|\u1ECF|\xF5|\xF4|\u1ED3|\u1ED1|\u1ED9|\u1ED5|\u1ED7|\u01A1|\u1EDD|\u1EDB|\u1EE3|\u1EDF|\u1EE1/g, 'o');
  _str = _str.replace(/\xF9|\xFA|\u1EE5|\u1EE7|\u0169|\u01B0|\u1EEB|\u1EE9|\u1EF1|\u1EED|\u1EEF/g, 'u');
  _str = _str.replace(/\u1EF3|\xFD|\u1EF5|\u1EF7|\u1EF9/g, 'y');
  _str = _str.replace(/\u0111/g, 'd');
  // Some system encode vietnamese combining accent as individual utf-8 characters
  _str = _str.replace(/\\u0300|\\u0301|\\u0303|\\u0309|\\u0323/g, ''); // Huy\u1EC1n s\u1EAFc h\u1ECFi ng\xE3 n\u1EB7ng
  _str = _str.replace(/\\u02C6|\\u0306|\\u031B/g, ''); // \xC2, \xCA, \u0102, \u01A0, \u01AF
  return _str.split(',').join('');
}
var toFirstUpperCase = function toFirstUpperCase(str) {
  if (!str) return '';
  return str.charAt(0).toUpperCase() + str.slice(1);
};//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///7369
`)}}]);
