"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[2118],{13490:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   W: function() { return /* binding */ DEFAULT_FALLBACK_IMG; }
/* harmony export */ });
var DEFAULT_FALLBACK_IMG = 'data:image/png;base64,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';//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiMTM0OTAuanMiLCJtYXBwaW5ncyI6Ijs7O0FBQU8sSUFBTUEsb0JBQW9CLEdBQy9CLGdxR0FBZ3FHIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9zcmMvY29tbW9uL2NvbnRhbnN0L2ltZy50cz9iNjliIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBERUZBVUxUX0ZBTExCQUNLX0lNRyA9XG4gICdkYXRhOmltYWdlL3BuZztiYXNlNjQsaVZCT1J3MEtHZ29BQUFBTlNVaEVVZ0FBQU1JQUFBRERDQVlBQUFEUXZjNlVBQUFCUldsRFExQkpRME1nVUhKdlptbHNaUUFBS0pGallHQVNTU3dveUdGaFlHREl6U3NwQ25KM1VvaUlqRkpnZjhMQXdTRENJTW9nd01DY21GeGM0QmdRNEFOVXdnQ2pVY0czYXd5TUlQcXlMc2lzN1BQT3EzUWRERmN2alYzak9EMWJvUVZUUFFyZ1Nra3RUZ2JTZjRBNExibWdxSVNCZ1RFRnlGWXVMeWtBc1R1QWJKRWlvS09BN0RrZ2RqcUV2UUhFVG9Ld2o0RFZoQVE1QTlrM2dHeUI1SXhFb0JtTUw0QnNuU1FrOFhRa050UmVFT0J4Y2ZYeFVRZzFNamMwZHlIZ1hOSkJTV3BGQ1loMnppK29MTXBNenloUmNBU0dVcXFDWjE2eW5vNkNrWUdSQVFNREtNd2hxai9mQUljbG94Z0hRcXhBaklIQkV1Z3c1c1VJc1NRcEJvYnRRUGRMY2lMRVZKWXpNUEJITURCc2F5aElMRXFFTzREeEcwdHhtckVSaE0yOW5ZR0JkZHIvLzUvREdSallOUmtZL2w3Ly8vLzM5di8vL3k0RG1uK0xnZUhBTndEcmtsMUF1TytwbWdBQUFEaGxXRWxtVFUwQUtnQUFBQWdBQVlkcEFBUUFBQUFCQUFBQUdnQUFBQUFBQXFBQ0FBUUFBQUFCQUFBQXdxQURBQVFBQUFBQkFBQUF3d0FBQUFEOWIvSG5BQUFIbGtsRVFWUjRBZTNkUDNQVFdCU0djYkd6TTZHQ0txbElCUlYwZEhSSkZhclEwZVVUOExINEJuUlUwTkhSMFVFRlZkSWxGUlY3VHpSa3NvbVBZOHV5a1RrL3pld1FmS3cvOXpudjR5dkp5bkx2NHVMaVYyZEJvRGlCZjRxUDMvQVJ1Q1JBQkVGQW9CRWdnaGdnUUFRWlFLQW5ZRWFRQkFRYUFTS0lBUUpFa0FFRWVnSm1CRWxBb0JFZ2doZ2dRQVFaUUtBbllFYVFCQVFhQVNLSUFRSkVrQUVFZWdKbUJFbEFvQkVnZ2hnZ1FBUVpRS0FuWUVhUUJBUWFBU0tJQVFKRWtBRUVlZ0ptQkVsQW9CRWdnaGdnUUFRWlFLQW5ZRWFRQkFRYUFTS0lBUUpFa0FFRWVnSm1CRWxBb0JFZ2doZ2dRQVFaUUtBbllFYVFCQVFhQVNLSUFRSkVrQUVFZWdKbUJFbEFvQkVnZ2hnZ1FBUVpRS0FuWUVhUUJBUWFBU0tJQVFKRWtBRUVlZ0ptQkVsQW9CRWdnaGdnUUFRWlFLQW5ZRWFRQkFRYUFTS0lBUUpFa0FFRWVnSm1CRWxBb0JFZ2doZ2dRQVFaUUtBbllFYVFCQVFhQVNLSUFRSkVrQUVFZWdKbUJFbEFvQkVnZ2hnZ1FBUVpRS0FuWUVhUUJBUWFBU0tJQVFKRWtBRUVlZ0ptQkVsQW9CRWdnaGdnUUFRWlFLQW5ZRWFRQkFRYUFTS0lBUUpFa0FFRWVnSm1CRWxBb0JFZ2doZ2dRQVFaUUtBbllFYVFCQVFhQVNLSUFRSkVrQUVFZWdKbUJFbEFvQkVnZ2hnZ1FBUVpRS0FuWUVhUUJBUWFBU0tJQVFKRWtBRUVlZ0ptQkVsQW9CRWdnaGdnMEFqOGkwSk80T3pzclB2NjlXditoaTJxUEhyMHFOdmYzOStpSTk3c29SSWg0ZjN6NTgvdTdkdTNTWFg3WHQ3WjJlbmV2SG16ZlFlK29TTjJhcFNBUGowOVRTcmIrWEtJL2YzNzkrMDgrQTBjTlJFMkFOa3VwaytBQ05QdmtTUGNBQUVpYkFDeVhVeWZBQkdtM3lOSHVBRUNSTmdBWkx1WVBnRWlyS2xIdTd1N1hkeXl0R3dIQWQ4ampOeW5nNE9EN3ZuejUxZGJQVDgvN3o1OCtOQjkrL2J0NmpVL1RJK0FHV0hFbnJ4NDhlSi9Fc1NtSHp4NDBMMTgrZkx5enhGM1pWTWpFeURDaUVEak1ZWlpTNXdpUFhueVpGYkpheE1oUUlRUkd6SHZXUjdYQ3lPQ1hzT21pREFpMUhtUE1NUWpEcGJwRWlEQ2lMMzU4ZU5IdXJXLzVTbldkSUJiWGlEQ2lBMzgvUG56cmNlMll5WjQvLzU5RjNlUExOTWw0UGJwaUwySjBMOTc5Kzd5RHRIRGh3OHZ0enp2ZEduRVhkdlVpZ1NJc0NMQVdhdkhwLytxTTBCY1hNZC9xMjVuMXZGNTdUWUJwMGEzbVV6aWxlUGo0KzdrNUtTTGI2Z3Q2eWRBaFBVelhub1BSMGRIbDc5V0dUTkNmQm5uMXV2U0NKZGVnUWhMSTF2dkNrK2ZQdTJlUFh0MnRaT1lFVjYvZm4zMWR6K3Nod0FSMXNQMWNxdkxudGJFTjlNeEE5eGNZanN4UzFqV1I0QUlhMkliengwdGM0NGZZWC8xNmxWNk5ERkxYSCtZTDMyandpQUNSQmlFYmY1S2NYb1RJc1FTcHpYeDROMjhKYTRCUW9LN3JnWGl5ZGJIangvUDI1VGFRQUpFR0FndVd5MCsyUThQRDYvS2k0UjhFVmwrYnpCT25aWTk1ZnE5cmo5ekFrVEkyU3hkaWRCSHFHOStza2R3NDNib3JDWE8vWmNKZHJhUFdkdjIydUlFaUxBNHE3bnZ2Q3VnOFdUcXpRdmVPSDI2Zm9kbzdnNnVGZS9hMTdXMytuRkJBa1JZRU5SZGIxdmtrejFDSDljUHNWeS9qcmhyMjdQcU1ZdkVOWU5sSEFJZXNSaUJZd1J5MFYrOGlYUDgrL2Z2WDExTXI3TDdFQ3VlYi9yNDhlTXFtN0Z1STJCR1dERUc4Y20rN0czTkVPZm1kY1RRdzRoOS81NWxobTdEZWtSWUtRUFpGMkFyYlhUQXl1NGtEWUIyWXhVendnMGdpLzQxenRIbmZRRzI2SGJHZWwvY3JWcm03dE5ZKy8xYnRrT0VBWjJNMDVyNEZCN3I5R2JBSWR4YVpZckhkT3NnSi93Q0VRWTBKNzRUbU9LbmJ4eFQ5bjNGZ0dHV1dzVmRvd0h0anQ5Tm52Zjd5UU0yYVpVL1RJQUlBeHJ3NmRPbkFXdFpaY29FbkJwTnVUdU9iV01FaUxBeDFIWTBaUUpFbUhKM0hOdkdDQkJoWTZqdGFNb0VpSkIwWjI5dkw2bHM1OHZ4UGNPOC96ZnJkbzVxdktPK2QzRng4V3U4emYxZFc0cC9jUHpMbHkvZHR2OVRzL0ViY3ZHQUhoSHlmQkloWjZOU2lJQlRvMExOTnRTY0FCRnlOaXFGQ0JDaFVMTU5OU2RBaEp5TlNpRUNSQ2pVYkVQTkNSQWhaNk5TaUFBUkNqWGJVSE1DUk1qWnFCUWlRSVJDelRiVW5BQVJjallxaFFnUW9WQ3pEVFVuUUlTY2pVb2hBa1FvMUd4RHpRa1FJV2VqVW9nQUVRbzEyMUJ6QWtUSTJhZ1VJa0NFUXMwMjFKd0FFWEkyS29VSUVLRlFzdzAxSjBDRW5JMUtJUUpFS05Sc1E4MEpFQ0ZubzFLSUFCRUtOZHRRY3dKRXlObW9GQ0pBaEVMTk50U2NBQkZ5TmlxRkNCQ2hVTE1OTlNkQWhKeU5TaUVDUkNqVWJFUE5DUkFoWjZOU2lBQVJDalhiVUhNQ1JNalpxQlFpUUlSQ3pUYlVuQUFSY2pZcWhRZ1FvVkN6RFRVblFJU2NqVW9oQWtRbzFHeER6UWtRSVdlalVvZ0FFUW8xMjFCekFrVEkyYWdVSWtDRVFzMDIxSndBRVhJMktvVUlFS0ZRc3cwMUowQ0VuSTFLSVFKRUtOUnNRODBKRUNGbm8xS0lBQkVLTmR0UWN3SkV5Tm1vRkNKQWhFTE5OdFNjQUJGeU5pcUZDQkNoVUxNTk5TZEFoSnlOU2lFQ1JDalViRVBOQ1JBaFo2TlNpQUFSQ2pYYlVITUNSTWpacUJRaVFJUkN6VGJVbkFBUmNqWXFoUWdRb1ZDekRUVW5RSVNjalVvaEFrUW8xR3hEelFrUUlXZWpVb2dBRVFvMTIxQnpBa1RJMmFnVUlrQ0VRczAyMUp3QUVYSTJLb1VJRUtGUXN3MDFKMENFbkkxS0lRSkVLTlJzUTgwSkVDRm5vMUtJQUJFS05kdFFjd0pFeU5tb0ZDSkFoRUxOTnRTY0FCRnlOaXFGQ0JDaFVMTU5OU2RBaEp5TlNpRUMvd0dnS0tDNFlNQTRUQUFBQUFCSlJVNUVya0pnZ2c9PSc7XG4iXSwibmFtZXMiOlsiREVGQVVMVF9GQUxMQkFDS19JTUciXSwic291cmNlUm9vdCI6IiJ9
//# sourceURL=webpack-internal:///13490
`)},65573:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(29905);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(85893);


var FallbackComponent = function FallbackComponent() {
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .ZP, {
    status: "403",
    title: "Kh\\xF4ng th\\u1EC3 truy c\\u1EADp",
    subTitle: "Xin l\\u1ED7i, b\\u1EA1n kh\\xF4ng c\\xF3 quy\\u1EC1n xem n\\u1ED9i dung n\\xE0y."
    //   extra={
    //     <Button type="primary" href="/">
    //       Back Home
    //     </Button>
    //   }
  });
};
/* harmony default export */ __webpack_exports__.Z = (FallbackComponent);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNjU1NzMuanMiLCJtYXBwaW5ncyI6Ijs7QUFBOEI7QUFBQTtBQUU5QixJQUFNRyxpQkFBaUIsR0FBRyxTQUFwQkEsaUJBQWlCQSxDQUFBLEVBQVM7RUFDOUIsb0JBQ0VELHNEQUFBLENBQUNGLHNEQUFNO0lBQ0xJLE1BQU0sRUFBQyxLQUFLO0lBQ1pDLEtBQUssRUFBQyxpQ0FBb0I7SUFDMUJDLFFBQVEsRUFBQztJQUNUO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7RUFBQSxDQUNELENBQUM7QUFFTixDQUFDO0FBRUQsc0RBQWVILGlCQUFpQiIsInNvdXJjZXMiOlsid2VicGFjazovL2FudC1kZXNpZ24tcHJvLy4vc3JjL2NvbXBvbmVudHMvRmFsbGJhY2tDb250ZW50L2luZGV4LnRzeD9mZGRhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFJlc3VsdCB9IGZyb20gJ2FudGQnO1xyXG5cclxuY29uc3QgRmFsbGJhY2tDb21wb25lbnQgPSAoKSA9PiB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxSZXN1bHRcclxuICAgICAgc3RhdHVzPVwiNDAzXCJcclxuICAgICAgdGl0bGU9XCJLaMO0bmcgdGjhu4MgdHJ1eSBj4bqtcFwiXHJcbiAgICAgIHN1YlRpdGxlPVwiWGluIGzhu5dpLCBi4bqhbiBraMO0bmcgY8OzIHF1eeG7gW4geGVtIG7hu5lpIGR1bmcgbsOgeS5cIlxyXG4gICAgICAvLyAgIGV4dHJhPXtcclxuICAgICAgLy8gICAgIDxCdXR0b24gdHlwZT1cInByaW1hcnlcIiBocmVmPVwiL1wiPlxyXG4gICAgICAvLyAgICAgICBCYWNrIEhvbWVcclxuICAgICAgLy8gICAgIDwvQnV0dG9uPlxyXG4gICAgICAvLyAgIH1cclxuICAgIC8+XHJcbiAgKTtcclxufTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IEZhbGxiYWNrQ29tcG9uZW50O1xyXG4iXSwibmFtZXMiOlsiUmVzdWx0IiwianN4IiwiX2pzeCIsIkZhbGxiYWNrQ29tcG9uZW50Iiwic3RhdHVzIiwidGl0bGUiLCJzdWJUaXRsZSJdLCJzb3VyY2VSb290IjoiIn0=
//# sourceURL=webpack-internal:///65573
`)},97679:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(97857);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(19632);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(5574);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var _common_contanst_constanst__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(86604);
/* harmony import */ var _services_fileUpload__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(96876);
/* harmony import */ var _services_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(467);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(51042);
/* harmony import */ var _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(27068);
/* harmony import */ var _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(34994);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(98138);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(45360);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(78367);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(85576);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(67294);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(85893);















var getBase64 = function getBase64(file) {
  return new Promise(function (resolve, reject) {
    var reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = function () {
      return resolve(reader.result);
    };
    reader.onerror = function (error) {
      return reject(error);
    };
  });
};
var FormUploadsPreviewable = function FormUploadsPreviewable(_ref) {
  var formItemName = _ref.formItemName,
    fileLimit = _ref.fileLimit,
    label = _ref.label,
    initialImages = _ref.initialImages,
    docType = _ref.docType,
    isRequired = _ref.isRequired,
    hideUploadButton = _ref.hideUploadButton,
    hideDeleteIcon = _ref.hideDeleteIcon,
    hidePreviewIcon = _ref.hidePreviewIcon,
    _ref$uploadButtonLayo = _ref.uploadButtonLayout,
    uploadButtonLayout = _ref$uploadButtonLayo === void 0 ? 'vertical' : _ref$uploadButtonLayo,
    _ref$uploadButtonIcon = _ref.uploadButtonIcon,
    uploadButtonIcon = _ref$uploadButtonIcon === void 0 ? /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .Z, {}) : _ref$uploadButtonIcon,
    _ref$uploadButtonText = _ref.uploadButtonText,
    uploadButtonText = _ref$uploadButtonText === void 0 ? 'T\u1EA3i l\xEAn' : _ref$uploadButtonText,
    uploadButtonStyle = _ref.uploadButtonStyle,
    readOnly = _ref.readOnly;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(false),
    _useState2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_4___default()(_useState, 2),
    previewOpen = _useState2[0],
    setPreviewOpen = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(''),
    _useState4 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_4___default()(_useState3, 2),
    previewImage = _useState4[0],
    setPreviewImage = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(''),
    _useState6 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_4___default()(_useState5, 2),
    previewTitle = _useState6[0],
    setPreviewTitle = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(initialImages),
    _useState8 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_4___default()(_useState7, 2),
    imageList = _useState8[0],
    setImageList = _useState8[1];
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)([]),
    _useState10 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_4___default()(_useState9, 2),
    fileList = _useState10[0],
    setFileList = _useState10[1];
  var form = antd__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .Z.useFormInstance();
  (0,_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_12__/* .useDeepCompareEffect */ .KW)(function () {
    var listImg = (0,_services_utils__WEBPACK_IMPORTED_MODULE_7__/* .getListFileUrlFromStringV2 */ .JJ)({
      arrUrlString: initialImages
    }).map(function (url, index) {
      return {
        name: "\\u1EA2nh ".concat((index + 1).toString()),
        url: url || '',
        uid: (-index).toString(),
        status: url ? 'done' : 'error'
      };
    });
    setFileList(listImg);
  }, [initialImages]);
  var handlePreview = /*#__PURE__*/function () {
    var _ref2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee(file) {
      return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            if (!(!file.url && !file.preview)) {
              _context.next = 4;
              break;
            }
            _context.next = 3;
            return getBase64(file.originFileObj);
          case 3:
            file.preview = _context.sent;
          case 4:
            setPreviewImage(file.url || file.preview);
            setPreviewOpen(true);
            setPreviewTitle(file.name || file.url.substring(file.url.lastIndexOf('/') + 1));
          case 7:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }));
    return function handlePreview(_x) {
      return _ref2.apply(this, arguments);
    };
  }();
  var handleChange = /*#__PURE__*/function () {
    var _ref4 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee3(_ref3) {
      var newFileList, oldFileList, uploadListRes, checkUploadFailed, arrFileUrl;
      return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee3$(_context3) {
        while (1) switch (_context3.prev = _context3.next) {
          case 0:
            newFileList = _ref3.fileList;
            oldFileList = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_1___default()(fileList);
            if (!readOnly) {
              _context3.next = 4;
              break;
            }
            return _context3.abrupt("return");
          case 4:
            setFileList(newFileList);
            _context3.next = 7;
            return Promise.allSettled(newFileList.map( /*#__PURE__*/function () {
              var _ref5 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee2(item) {
                var _item$lastModified;
                return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee2$(_context2) {
                  while (1) switch (_context2.prev = _context2.next) {
                    case 0:
                      if (!item.url) {
                        _context2.next = 2;
                        break;
                      }
                      return _context2.abrupt("return", {
                        data: {
                          message: {
                            file_url: item.url.split('file_url=').at(-1)
                          }
                        }
                      });
                    case 2:
                      _context2.next = 4;
                      return (0,_services_fileUpload__WEBPACK_IMPORTED_MODULE_6__/* .uploadFile */ .cT)({
                        docType: docType || _common_contanst_constanst__WEBPACK_IMPORTED_MODULE_5__/* .DOCTYPE_ERP */ .lH.iotPlant,
                        docName: item.name + Math.random().toString(4) + ((_item$lastModified = item.lastModified) === null || _item$lastModified === void 0 ? void 0 : _item$lastModified.toString(4)),
                        file: item.originFileObj
                      });
                    case 4:
                      return _context2.abrupt("return", _context2.sent);
                    case 5:
                    case "end":
                      return _context2.stop();
                  }
                }, _callee2);
              }));
              return function (_x3) {
                return _ref5.apply(this, arguments);
              };
            }()));
          case 7:
            uploadListRes = _context3.sent;
            checkUploadFailed = uploadListRes.find(function (item) {
              return item.status === 'rejected';
            });
            if (checkUploadFailed) {
              antd__WEBPACK_IMPORTED_MODULE_13__/* ["default"] */ .ZP.error({
                content: "upload \\u1EA3nh kh\\xF4ng th\\xE0nh c\\xF4ng"
              });
              setFileList(oldFileList);
            }
            arrFileUrl = uploadListRes.reduce(function (prev, item) {
              var _item$value, _item$value2;
              return item.status === 'fulfilled' && item !== null && item !== void 0 && (_item$value = item.value) !== null && _item$value !== void 0 && (_item$value = _item$value.data) !== null && _item$value !== void 0 && (_item$value = _item$value.message) !== null && _item$value !== void 0 && _item$value.file_url ? [].concat(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_1___default()(prev), [item === null || item === void 0 || (_item$value2 = item.value) === null || _item$value2 === void 0 || (_item$value2 = _item$value2.data) === null || _item$value2 === void 0 || (_item$value2 = _item$value2.message) === null || _item$value2 === void 0 ? void 0 : _item$value2.file_url]) : prev;
            }, []).filter(function (item) {
              return typeof item === 'string';
            });
            if (arrFileUrl) {
              setImageList(arrFileUrl.join(','));
              form.setFieldValue(formItemName, arrFileUrl.join(','));
            }
          case 12:
          case "end":
            return _context3.stop();
        }
      }, _callee3);
    }));
    return function handleChange(_x2) {
      return _ref4.apply(this, arguments);
    };
  }();
  var uploadButton = /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxs)("div", {
    style: D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default()({
      display: 'flex',
      flexDirection: uploadButtonLayout === 'vertical' ? 'column' : 'row',
      alignItems: 'center',
      gap: uploadButtonLayout === 'vertical' ? '8px' : '4px'
    }, uploadButtonStyle),
    children: [uploadButtonIcon, /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)("div", {
      children: uploadButtonText
    })]
  });
  var handleCancel = function handleCancel() {
    return setPreviewOpen(false);
  };
  var normFile = function normFile(e) {
    if (Array.isArray(e)) {
      return e;
    }
    return e && e.fileList;
  };
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.Fragment, {
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_14__/* .ProForm */ .A.Item, {
      name: formItemName,
      initialValue: imageList,
      label: label,
      rules: D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_1___default()(isRequired ? [{
        required: isRequired
      }] : []),
      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(antd__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .Z, {
        listType: "picture-card",
        fileList: fileList,
        onPreview: handlePreview,
        maxCount: fileLimit,
        onChange: handleChange,
        multiple: true,
        showUploadList: {
          showRemoveIcon: !hideDeleteIcon,
          showPreviewIcon: !hidePreviewIcon
        },
        children: !hideUploadButton && (fileList.length >= fileLimit ? null : uploadButton)
      })
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(antd__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .Z, {
      open: previewOpen,
      title: previewTitle,
      footer: null,
      onCancel: handleCancel,
      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)("img", {
        alt: "example",
        style: {
          width: '100%'
        },
        src: previewImage
      })
    })]
  });
};
/* harmony default export */ __webpack_exports__.Z = (FormUploadsPreviewable);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///97679
`)},47378:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return /* binding */ Docs_CropLibrary; }
});

// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/slicedToArray.js
var slicedToArray = __webpack_require__(5574);
var slicedToArray_default = /*#__PURE__*/__webpack_require__.n(slicedToArray);
// EXTERNAL MODULE: ./src/components/FallbackContent/index.tsx
var FallbackContent = __webpack_require__(65573);
// EXTERNAL MODULE: ./src/services/utils.ts
var utils = __webpack_require__(467);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/ReloadOutlined.js
var ReloadOutlined = __webpack_require__(43471);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-layout/es/components/PageContainer/index.js + 18 modules
var PageContainer = __webpack_require__(6110);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-skeleton/es/components/Descriptions/index.js
var Descriptions = __webpack_require__(44688);
// EXTERNAL MODULE: ./src/.umi-production/exports.ts + 34 modules
var _umi_production_exports = __webpack_require__(7837);
// EXTERNAL MODULE: ./node_modules/antd/es/space/index.js + 2 modules
var space = __webpack_require__(42075);
// EXTERNAL MODULE: ./node_modules/antd/es/card/index.js + 3 modules
var card = __webpack_require__(76216);
// EXTERNAL MODULE: ./node_modules/antd/es/row/index.js
var row = __webpack_require__(71230);
// EXTERNAL MODULE: ./node_modules/antd/es/col/index.js
var col = __webpack_require__(15746);
// EXTERNAL MODULE: ./node_modules/antd/es/input/index.js + 4 modules
var input = __webpack_require__(96365);
// EXTERNAL MODULE: ./node_modules/antd/es/button/index.js + 9 modules
var es_button = __webpack_require__(14726);
// EXTERNAL MODULE: ./node_modules/antd/es/spin/index.js + 2 modules
var spin = __webpack_require__(75081);
// EXTERNAL MODULE: ./node_modules/antd/es/list/index.js + 3 modules
var list = __webpack_require__(2487);
// EXTERNAL MODULE: ./node_modules/lodash/lodash.js
var lodash = __webpack_require__(96486);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/regeneratorRuntime.js
var regeneratorRuntime = __webpack_require__(15009);
var regeneratorRuntime_default = /*#__PURE__*/__webpack_require__.n(regeneratorRuntime);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(99289);
var asyncToGenerator_default = /*#__PURE__*/__webpack_require__.n(asyncToGenerator);
// EXTERNAL MODULE: ./src/common/contanst/img.ts
var img = __webpack_require__(13490);
// EXTERNAL MODULE: ./src/services/plants.ts
var plants = __webpack_require__(18275);
// EXTERNAL MODULE: ./src/utils/file.ts
var file = __webpack_require__(80320);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/EditOutlined.js
var EditOutlined = __webpack_require__(47389);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/DeleteOutlined.js
var DeleteOutlined = __webpack_require__(82061);
// EXTERNAL MODULE: ./node_modules/antd/es/typography/index.js + 18 modules
var typography = __webpack_require__(25514);
// EXTERNAL MODULE: ./node_modules/antd/es/message/index.js
var message = __webpack_require__(45360);
// EXTERNAL MODULE: ./node_modules/antd/es/popconfirm/index.js + 2 modules
var popconfirm = __webpack_require__(86738);
// EXTERNAL MODULE: ./node_modules/antd/es/avatar/index.js + 4 modules
var avatar = __webpack_require__(7134);
// EXTERNAL MODULE: ./node_modules/antd/es/card/Meta.js
var Meta = __webpack_require__(46256);
// EXTERNAL MODULE: ./src/common/contanst/constanst.ts
var constanst = __webpack_require__(86604);
// EXTERNAL MODULE: ./src/components/FormUploadsPreviewable/index.tsx
var FormUploadsPreviewable = __webpack_require__(97679);
// EXTERNAL MODULE: ./src/services/sscript.ts
var sscript = __webpack_require__(39750);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/layouts/ModalForm/index.js
var ModalForm = __webpack_require__(37476);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/Text/index.js
var Text = __webpack_require__(5966);
// EXTERNAL MODULE: ./node_modules/antd/es/app/index.js + 1 modules
var app = __webpack_require__(31418);
// EXTERNAL MODULE: ./node_modules/antd/es/form/index.js + 18 modules
var es_form = __webpack_require__(98138);
// EXTERNAL MODULE: ./src/pages/Docs/CropLibrary/_util.ts
var _util = __webpack_require__(58061);
// EXTERNAL MODULE: ./node_modules/react/jsx-runtime.js
var jsx_runtime = __webpack_require__(85893);
;// CONCATENATED MODULE: ./src/pages/Docs/CropLibrary/components/EditCard.tsx















var getBase64 = function getBase64(file) {
  return new Promise(function (resolve, reject) {
    var reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = function () {
      return resolve(reader.result);
    };
    reader.onerror = function (error) {
      return reject(error);
    };
  });
};
var EditDocCard = function EditDocCard(_ref) {
  var onSuccess = _ref.onSuccess,
    open = _ref.open,
    openChange = _ref.openChange,
    id = _ref.id;
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  var _App$useApp = app/* default */.Z.useApp(),
    message = _App$useApp.message;
  var _Form$useForm = es_form/* default */.Z.useForm(),
    _Form$useForm2 = slicedToArray_default()(_Form$useForm, 1),
    form = _Form$useForm2[0];
  var _useModel = (0,_umi_production_exports.useModel)('Docs'),
    reload = _useModel.reload;
  var _useState = (0,react.useState)(false),
    _useState2 = slicedToArray_default()(_useState, 2),
    submitting = _useState2[0],
    setSubmitting = _useState2[1];
  var handleFinish = /*#__PURE__*/function () {
    var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(values) {
      return regeneratorRuntime_default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _context.prev = 0;
            setSubmitting(true);
            _context.next = 4;
            return (0,sscript/* generalUpdate */.I6)(constanst/* DOCTYPE_ERP */.lH.iotPlant, id, {
              data: {
                label: values.label,
                image: values.image,
                type: _util/* documentType */.c.type
              }
            });
          case 4:
            reload();
            onSuccess === null || onSuccess === void 0 || onSuccess();
            message.success(formatMessage({
              id: 'common.success'
            }));
            return _context.abrupt("return", true);
          case 10:
            _context.prev = 10;
            _context.t0 = _context["catch"](0);
            message.error({
              content: "L\\u1ED7i khi c\\u1EADp nh\\u1EADt t\\xE0i li\\u1EC7u: ".concat(_context.t0),
              duration: 5
            });
          case 13:
            _context.prev = 13;
            setSubmitting(false);
            return _context.finish(13);
          case 16:
          case "end":
            return _context.stop();
        }
      }, _callee, null, [[0, 10, 13, 16]]);
    }));
    return function handleFinish(_x) {
      return _ref2.apply(this, arguments);
    };
  }();
  var _useRequest = (0,_umi_production_exports.useRequest)( /*#__PURE__*/asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2() {
      var _res$data;
      var res, data;
      return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            _context2.next = 2;
            return (0,sscript/* sscriptGeneralList */.RB)({
              doc_name: 'iot_plant',
              filters: [['iot_plant', 'name', '=', id], ['iot_plant', 'type', '=', _util/* documentType */.c.type]],
              page: 1,
              size: 1
              // fields: ['label', 'name', 'image'],
              // order_by:"creation desc"
            });
          case 2:
            res = _context2.sent;
            data = (_res$data = res.data) === null || _res$data === void 0 ? void 0 : _res$data[0];
            if (data) {
              _context2.next = 6;
              break;
            }
            throw new Error('Kh\xF4ng t\xECm th\u1EA5y t\xE0i li\u1EC7u');
          case 6:
            return _context2.abrupt("return", {
              data: data
            });
          case 7:
          case "end":
            return _context2.stop();
        }
      }, _callee2);
    })), {
      onSuccess: function onSuccess(data) {
        form.setFieldsValue({
          label: data.label,
          image: data.image
        });
      },
      onError: function onError(err) {
        message.error(err.message);
      }
    }),
    data = _useRequest.data,
    loading = _useRequest.loading;
  return /*#__PURE__*/(0,jsx_runtime.jsxs)(ModalForm/* ModalForm */.Y, {
    width: "400px",
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.create_doc"
    }),
    open: open,
    onOpenChange: openChange,
    autoFocusFirstInput: true,
    modalProps: {
      destroyOnClose: true
    },
    form: form,
    submitter: {
      render: function render(props, defaultDoms) {
        return [/*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
          onClick: function onClick() {
            props.submit();
          },
          type: "primary",
          style: {
            width: '100%'
          },
          loading: submitting,
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
            id: "action.save"
          })
        }, "ok")];
      }
    },
    submitTimeout: 2000,
    onFinish: handleFinish,
    loading: loading || undefined,
    children: [/*#__PURE__*/(0,jsx_runtime.jsx)(Text/* default */.Z, {
      rules: [{
        required: true
      }],
      required: true,
      width: "lg",
      name: "label",
      label: formatMessage({
        id: 'common.name'
      })
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(FormUploadsPreviewable/* default */.Z, {
      formItemName: 'image',
      label: formatMessage({
        id: 'common.form.image'
      }),
      fileLimit: 1,
      initialImages: data === null || data === void 0 ? void 0 : data.image
    })]
  });
};
/* harmony default export */ var EditCard = (EditDocCard);
;// CONCATENATED MODULE: ./src/pages/Docs/CropLibrary/components/ImgCard.tsx















var ImgCard_Text = typography/* default */.Z.Text;
// ! Currently tailored specificly for crop plant
var ImgCard = function ImgCard(_ref) {
  var id = _ref.id,
    image = _ref.image,
    title = _ref.title,
    onDeleteSuccess = _ref.onDeleteSuccess;
  var _useModel = (0,_umi_production_exports.useModel)('Docs'),
    setMyPlant = _useModel.setMyPlant,
    isAccessEditDocs = _useModel.isAccessEditDocs;
  function handleDelete() {
    return _handleDelete.apply(this, arguments);
  }
  function _handleDelete() {
    _handleDelete = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2() {
      return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            _context2.next = 2;
            return (0,plants/* deletePlantAllResources */.Ew)(id).then(function (res) {
              setMyPlant(function (prev) {
                return prev.filter(function (item) {
                  return item.name !== id;
                });
              });
              message/* default */.ZP.success("Xo\\xE1  th\\xE0nh c\\xF4ng");
            })["catch"](function (error) {
              message/* default */.ZP.error("L\\u1ED7i khi xo\\xE1  ".concat(title, ": ").concat(error));
            });
          case 2:
          case "end":
            return _context2.stop();
        }
      }, _callee2);
    }));
    return _handleDelete.apply(this, arguments);
  }
  var access = (0,_umi_production_exports.useAccess)();
  var _useState = (0,react.useState)(false),
    _useState2 = slicedToArray_default()(_useState, 2),
    open = _useState2[0],
    setOpen = _useState2[1];
  var canDelete = access.canDeleteAllInPageAccess() && isAccessEditDocs;
  return /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
    children: [open && /*#__PURE__*/(0,jsx_runtime.jsx)(EditCard, {
      id: id,
      open: open,
      openChange: setOpen
    }, "edit"), /*#__PURE__*/(0,jsx_runtime.jsx)(card/* default */.Z, {
      style: {
        overflow: 'hidden'
      },
      hoverable: true
      // TODO move actions to props?
      ,
      actions: !isAccessEditDocs ? undefined : [/*#__PURE__*/(0,jsx_runtime.jsx)(react.Fragment, {
        children: isAccessEditDocs && /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
          type: "text",
          icon: /*#__PURE__*/(0,jsx_runtime.jsx)(EditOutlined/* default */.Z, {}),
          onClick: function onClick(e) {
            return setOpen(true);
          }
        }, "edit")
      }, "edit"), /*#__PURE__*/(0,jsx_runtime.jsx)(react.Fragment, {
        children: canDelete && /*#__PURE__*/(0,jsx_runtime.jsx)(popconfirm/* default */.Z, {
          title: "Xo\\xE1 t\\xE0i li\\u1EC7u",
          description: "B\\u1EA1n c\\xF3 mu\\u1ED1n xo\\xE1 ".concat(title, "?"),
          onConfirm: /*#__PURE__*/asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
            return regeneratorRuntime_default()().wrap(function _callee$(_context) {
              while (1) switch (_context.prev = _context.next) {
                case 0:
                  _context.next = 2;
                  return handleDelete();
                case 2:
                  onDeleteSuccess === null || onDeleteSuccess === void 0 || onDeleteSuccess();
                case 3:
                case "end":
                  return _context.stop();
              }
            }, _callee);
          })),
          onPopupClick: function onPopupClick(e) {
            e.stopPropagation();
          },
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(DeleteOutlined/* default */.Z, {
            onClick: function onClick(e) {
              e.stopPropagation();
            }
          }, "delete")
        }, "delete")
      }, "delete")],
      children: /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        onClick: function onClick() {
          _umi_production_exports.history.push("/documents/".concat(id, "/detail"));
        },
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(Meta/* default */.Z, {
          avatar: /*#__PURE__*/(0,jsx_runtime.jsx)(avatar/* default */.C, {
            shape: "square",
            size: 64,
            src: image ? (0,file/* genDownloadUrl */.h)(image) : img/* DEFAULT_FALLBACK_IMG */.W
          }),
          title: /*#__PURE__*/(0,jsx_runtime.jsx)(ImgCard_Text, {
            style: {
              whiteSpace: 'normal'
            },
            children: title
          })
        })
      })
    })]
  });
};
/* harmony default export */ var components_ImgCard = (ImgCard);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/toConsumableArray.js
var toConsumableArray = __webpack_require__(19632);
var toConsumableArray_default = /*#__PURE__*/__webpack_require__.n(toConsumableArray);
// EXTERNAL MODULE: ./src/services/fileUpload.ts
var services_fileUpload = __webpack_require__(96876);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/PlusOutlined.js
var PlusOutlined = __webpack_require__(51042);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/layouts/ProForm/index.js + 3 modules
var ProForm = __webpack_require__(34994);
// EXTERNAL MODULE: ./node_modules/antd/es/upload/index.js + 26 modules
var upload = __webpack_require__(78367);
// EXTERNAL MODULE: ./node_modules/antd/es/modal/index.js + 1 modules
var modal = __webpack_require__(85576);
// EXTERNAL MODULE: ./node_modules/antd-img-crop/dist/antd-img-crop.esm.js + 4 modules
var antd_img_crop_esm = __webpack_require__(9146);
;// CONCATENATED MODULE: ./src/pages/Docs/CropLibrary/CreateCrop/index.tsx



















var CreateCrop_getBase64 = function getBase64(file) {
  return new Promise(function (resolve, reject) {
    var reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = function () {
      return resolve(reader.result);
    };
    reader.onerror = function (error) {
      return reject(error);
    };
  });
};
var CreateCrop = function CreateCrop(_ref) {
  var onSuccess = _ref.onSuccess;
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  var _Form$useForm = es_form/* default */.Z.useForm(),
    _Form$useForm2 = slicedToArray_default()(_Form$useForm, 1),
    form = _Form$useForm2[0];
  var _useModel = (0,_umi_production_exports.useModel)('MyPlant'),
    myPlant = _useModel.myPlant,
    setMyPlant = _useModel.setMyPlant;
  var _useState = (0,react.useState)(false),
    _useState2 = slicedToArray_default()(_useState, 2),
    submitting = _useState2[0],
    setSubmitting = _useState2[1];
  var _useState3 = (0,react.useState)(false),
    _useState4 = slicedToArray_default()(_useState3, 2),
    previewOpen = _useState4[0],
    setPreviewOpen = _useState4[1];
  var _useState5 = (0,react.useState)(''),
    _useState6 = slicedToArray_default()(_useState5, 2),
    previewImage = _useState6[0],
    setPreviewImage = _useState6[1];
  var _useState7 = (0,react.useState)(''),
    _useState8 = slicedToArray_default()(_useState7, 2),
    previewTitle = _useState8[0],
    setPreviewTitle = _useState8[1];
  var handlePreview = /*#__PURE__*/function () {
    var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(file) {
      return regeneratorRuntime_default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            if (!(!file.url && !file.preview)) {
              _context.next = 4;
              break;
            }
            _context.next = 3;
            return CreateCrop_getBase64(file.originFileObj);
          case 3:
            file.preview = _context.sent;
          case 4:
            setPreviewImage(file.url || file.preview);
            setPreviewOpen(true);
            setPreviewTitle(file.name || file.url.substring(file.url.lastIndexOf('/') + 1));
          case 7:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }));
    return function handlePreview(_x) {
      return _ref2.apply(this, arguments);
    };
  }();
  var handleChange = function handleChange(_ref3) {
    var newFileList = _ref3.fileList;
    form.setFieldValue('image', newFileList);
  };
  var handleCancel = function handleCancel() {
    return setPreviewOpen(false);
  };
  var handleFinish = /*#__PURE__*/function () {
    var _ref4 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee3(values) {
      var name, image, img;
      return regeneratorRuntime_default()().wrap(function _callee3$(_context3) {
        while (1) switch (_context3.prev = _context3.next) {
          case 0:
            setSubmitting(true);
            name = values.name, image = values.image;
            img = image.at(0);
            _context3.next = 5;
            return (0,plants/* createPlant */.aE)({
              label: name
              // type: documentType.type,
            }).then( /*#__PURE__*/function () {
              var _ref5 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2(newPlant) {
                var uploadStatus, updatedPlant;
                return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
                  while (1) switch (_context2.prev = _context2.next) {
                    case 0:
                      if (newPlant) {
                        _context2.next = 2;
                        break;
                      }
                      throw new Error('L\u1ED7i h\u1EC7 th\u1ED1ng khi t\u1EA1o t\xE0i li\u1EC7u m\u1EDBi');
                    case 2:
                      _context2.next = 4;
                      return (0,services_fileUpload/* uploadFile */.cT)({
                        docType: constanst/* DOCTYPE_ERP */.lH.iotPlant,
                        docName: newPlant.name || (img === null || img === void 0 ? void 0 : img.fileName) || (0,lodash.uniqueId)(),
                        file: img === null || img === void 0 ? void 0 : img.originFileObj
                      });
                    case 4:
                      uploadStatus = _context2.sent;
                      if (uploadStatus.data) {
                        _context2.next = 7;
                        break;
                      }
                      throw new Error('L\u1ED7i trong qu\xE1 tr\xECnh up \u1EA3nh');
                    case 7:
                      _context2.next = 9;
                      return (0,plants/* updatePlant */.Vn)({
                        name: newPlant.name,
                        image: uploadStatus.data.message.file_url,
                        type: _util/* documentType */.c.type
                      });
                    case 9:
                      updatedPlant = _context2.sent;
                      setMyPlant([updatedPlant].concat(toConsumableArray_default()(myPlant)));
                      onSuccess === null || onSuccess === void 0 || onSuccess();
                      message/* default */.ZP.success(formatMessage({
                        id: 'common.success'
                      }));
                    case 13:
                    case "end":
                      return _context2.stop();
                  }
                }, _callee2);
              }));
              return function (_x3) {
                return _ref5.apply(this, arguments);
              };
            }())["catch"](function (error) {
              message/* default */.ZP.error({
                content: "L\\u1ED7i khi t\\u1EA1o t\\xE0i li\\u1EC7u: ".concat(error),
                duration: 5
              });
            });
          case 5:
            setSubmitting(false);
            return _context3.abrupt("return", true);
          case 7:
          case "end":
            return _context3.stop();
        }
      }, _callee3);
    }));
    return function handleFinish(_x2) {
      return _ref4.apply(this, arguments);
    };
  }();
  return /*#__PURE__*/(0,jsx_runtime.jsxs)(ModalForm/* ModalForm */.Y, {
    width: "400px",
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.create_doc"
    }),
    trigger: /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
      type: "primary",
      icon: /*#__PURE__*/(0,jsx_runtime.jsx)(PlusOutlined/* default */.Z, {}),
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
        id: "common.create_doc"
      })
    }),
    autoFocusFirstInput: true,
    modalProps: {
      destroyOnClose: true
    },
    form: form,
    submitter: {
      render: function render(props, defaultDoms) {
        return [/*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
          onClick: function onClick() {
            props.submit();
          },
          type: "primary",
          style: {
            width: '100%'
          },
          loading: submitting,
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
            id: "action.save"
          })
        }, "ok")];
      }
    },
    submitTimeout: 2000,
    onFinish: handleFinish,
    children: [/*#__PURE__*/(0,jsx_runtime.jsx)(Text/* default */.Z, {
      rules: [{
        required: true
      }],
      required: true,
      width: "lg",
      name: "name",
      label: formatMessage({
        id: 'common.name'
      })
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(ProForm/* ProForm */.A.Item, {
      name: "image",
      required: true,
      label: formatMessage({
        id: 'common.form.image'
      }),
      rules: [{
        required: true
      }],
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(antd_img_crop_esm/* default */.Z, {
        rotationSlider: true,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(upload/* default */.Z, {
          listType: "picture-card",
          onChange: handleChange,
          onPreview: handlePreview,
          maxCount: 1,
          accept: "image/x-png,image/jpeg,image/png",
          children: "Upload"
        })
      })
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(modal/* default */.Z, {
      open: previewOpen,
      title: previewTitle,
      footer: null,
      onCancel: handleCancel,
      children: /*#__PURE__*/(0,jsx_runtime.jsx)("img", {
        alt: "example",
        style: {
          width: '100%'
        },
        src: previewImage
      })
    })]
  });
};
/* harmony default export */ var CropLibrary_CreateCrop = (CreateCrop);
;// CONCATENATED MODULE: ./src/pages/Docs/CropLibrary/index.tsx













var CropLibrary = function CropLibrary(_ref) {
  var children = _ref.children;
  var _useModel = (0,_umi_production_exports.useModel)('Docs'),
    myPlant = _useModel.myPlant,
    reload = _useModel.reload,
    loadingResource = _useModel.loadingResource,
    isAccessEditDocs = _useModel.isAccessEditDocs;
  var _useState = (0,react.useState)([]),
    _useState2 = slicedToArray_default()(_useState, 2),
    filteredPlants = _useState2[0],
    setFilteredPlants = _useState2[1];
  var intl = (0,_umi_production_exports.useIntl)();
  (0,react.useEffect)(function () {
    reload();
  }, []);
  (0,react.useEffect)(function () {
    if ((0,lodash.isArray)(myPlant)) {
      setFilteredPlants(myPlant.sort(function (a, b) {
        return ((a === null || a === void 0 ? void 0 : a.label) || '').localeCompare((b === null || b === void 0 ? void 0 : b.label) || '');
      }));
    }
  }, [myPlant]);
  var debounceSearch = (0,lodash.debounce)(function (searchQuery) {
    setFilteredPlants(myPlant.filter(function (plant) {
      return (0,utils/* toLowerCaseNonAccentVietnamese */.HO)(plant.label || '').includes((0,utils/* toLowerCaseNonAccentVietnamese */.HO)(searchQuery));
    }));
  }, 400);
  var handleSearch = function handleSearch(e) {
    var searchQuery = e.target.value;
    debounceSearch(searchQuery);
  };
  var access = (0,_umi_production_exports.useAccess)();
  var canCreatePlant = isAccessEditDocs;
  return /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.Access, {
    accessible: true,
    fallback: /*#__PURE__*/(0,jsx_runtime.jsx)(FallbackContent/* default */.Z, {}),
    children: /*#__PURE__*/(0,jsx_runtime.jsx)(PageContainer/* PageContainer */._z, {
      children: /*#__PURE__*/(0,jsx_runtime.jsxs)(space/* default */.Z, {
        direction: "vertical",
        size: "middle",
        style: {
          display: 'flex'
        },
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)(card/* default */.Z, {
          bordered: true,
          children: /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
            justify: 'space-between',
            gutter: 16,
            align: "middle",
            children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
              span: 8,
              flex: '1 0 25%',
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(input/* default */.Z, {
                addonBefore: intl.formatMessage({
                  id: 'common.docs'
                }),
                onChange: handleSearch
              })
            }), /*#__PURE__*/(0,jsx_runtime.jsxs)(space/* default */.Z, {
              children: [canCreatePlant && /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
                span: 8,
                style: {
                  textAlign: 'right'
                },
                children: /*#__PURE__*/(0,jsx_runtime.jsx)(CropLibrary_CreateCrop, {
                  onSuccess: reload
                })
              }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
                children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
                  icon: /*#__PURE__*/(0,jsx_runtime.jsx)(ReloadOutlined/* default */.Z, {}),
                  onClick: reload
                })
              })]
            })]
          })
        }), /*#__PURE__*/(0,jsx_runtime.jsx)(react.Suspense, {
          fallback: /*#__PURE__*/(0,jsx_runtime.jsx)(Descriptions/* TableSkeleton */.hM, {
            active: true
          }),
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(spin/* default */.Z, {
            spinning: loadingResource,
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(list/* default */.Z, {
              grid: {
                column: 3,
                gutter: 10,
                md: 2,
                sm: 2,
                xs: 1
              },
              dataSource: filteredPlants,
              renderItem: function renderItem(item) {
                return /*#__PURE__*/(0,jsx_runtime.jsx)(list/* default */.Z.Item, {
                  children: /*#__PURE__*/(0,jsx_runtime.jsx)(components_ImgCard, {
                    onDeleteSuccess: reload,
                    image: item.image,
                    title: item.label,
                    id: item.name
                  })
                });
              }
            })
          })
        })]
      })
    })
  });
};
/* harmony default export */ var Docs_CropLibrary = (CropLibrary);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///47378
`)},96876:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   cT: function() { return /* binding */ uploadFile; },
/* harmony export */   uy: function() { return /* binding */ uploadFileHome; }
/* harmony export */ });
/* unused harmony export removeFileAttachment */
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(7837);
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(467);




var uploadFile = /*#__PURE__*/function () {
  var _ref = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee(data) {
    var formData, res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          formData = new FormData();
          formData.append('doctype', data.docType || '');
          formData.append('docname', data.docName || '');
          formData.append('folder', data.folder || 'Home/Attachments');
          formData.append('is_private', data.isPrivate || '1');
          formData.append('optimize', data.optimize || '0');
          formData.append('file', data.file);
          _context.next = 9;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/file/upload'), {
            method: 'POST',
            data: formData
          });
        case 9:
          res = _context.sent;
          return _context.abrupt("return", {
            data: res.result
          });
        case 11:
        case "end":
          return _context.stop();
      }
    }, _callee);
  }));
  return function uploadFile(_x) {
    return _ref.apply(this, arguments);
  };
}();
var uploadFileHome = /*#__PURE__*/function () {
  var _ref2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee2(data) {
    var formData, res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          formData = new FormData();
          formData.append('folder', data.folder || 'Home/Attachments');
          formData.append('is_private', data.isPrivate || '0');
          formData.append('is_home_folder', data.isPrivate || '1');
          formData.append('doctype', data.docType || '');
          formData.append('docname', data.docName || '');
          formData.append('optimize', data.optimize || '0');
          formData.append('file', data.file);
          _context2.next = 10;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/file/upload'), {
            method: 'POST',
            data: formData
          });
        case 10:
          res = _context2.sent;
          return _context2.abrupt("return", {
            data: res.result
          });
        case 12:
        case "end":
          return _context2.stop();
      }
    }, _callee2);
  }));
  return function uploadFileHome(_x2) {
    return _ref2.apply(this, arguments);
  };
}();
var removeFileAttachment = /*#__PURE__*/(/* unused pure expression or super */ null && (function () {
  var _ref3 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee3(data) {
    var res;
    return _regeneratorRuntime().wrap(function _callee3$(_context3) {
      while (1) switch (_context3.prev = _context3.next) {
        case 0:
          _context3.next = 2;
          return request(generateAPIPath('api/v2/file'), {
            method: 'DELETE',
            data: data
          });
        case 2:
          res = _context3.sent;
          return _context3.abrupt("return", {
            data: res.result
          });
        case 4:
        case "end":
          return _context3.stop();
      }
    }, _callee3);
  }));
  return function removeFileAttachment(_x3) {
    return _ref3.apply(this, arguments);
  };
}()));//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///96876
`)},80320:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   h: function() { return /* binding */ genDownloadUrl; }
/* harmony export */ });
/* harmony import */ var _common_contanst__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(95028);

var genDownloadUrl = function genDownloadUrl(fileUrl) {
  return "".concat(_common_contanst__WEBPACK_IMPORTED_MODULE_0__/* .API_URL_DEV */ .v, "/api/v2/file/download?file_url=").concat(fileUrl);
};//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiODAzMjAuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFnRDtBQUV6QyxJQUFNQyxjQUFjLEdBQUcsU0FBakJBLGNBQWNBLENBQUlDLE9BQWUsRUFBSztFQUNqRCxVQUFBQyxNQUFBLENBQVVILGtFQUFXLHFDQUFBRyxNQUFBLENBQWtDRCxPQUFPO0FBQ2hFLENBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hbnQtZGVzaWduLXByby8uL3NyYy91dGlscy9maWxlLnRzP2FlNDQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQVBJX1VSTF9ERVYgfSBmcm9tICdAL2NvbW1vbi9jb250YW5zdCc7XHJcblxyXG5leHBvcnQgY29uc3QgZ2VuRG93bmxvYWRVcmwgPSAoZmlsZVVybDogc3RyaW5nKSA9PiB7XHJcbiAgcmV0dXJuIGAke0FQSV9VUkxfREVWfS9hcGkvdjIvZmlsZS9kb3dubG9hZD9maWxlX3VybD0ke2ZpbGVVcmx9YDtcclxufTtcclxuIl0sIm5hbWVzIjpbIkFQSV9VUkxfREVWIiwiZ2VuRG93bmxvYWRVcmwiLCJmaWxlVXJsIiwiY29uY2F0Il0sInNvdXJjZVJvb3QiOiIifQ==
//# sourceURL=webpack-internal:///80320
`)}}]);
