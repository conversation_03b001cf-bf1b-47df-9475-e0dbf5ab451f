"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[7043],{81568:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(5574);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(7837);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(45360);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(86738);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(14726);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(67294);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(85893);







var ActionPopConfirm = function ActionPopConfirm(_ref) {
  var actionCall = _ref.actionCall,
    refreshData = _ref.refreshData,
    buttonType = _ref.buttonType,
    text = _ref.text,
    danger = _ref.danger,
    size = _ref.size;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false),
    _useState2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2___default()(_useState, 2),
    loading = _useState2[0],
    setLoading = _useState2[1];
  var onConfirm = /*#__PURE__*/function () {
    var _ref2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee() {
      return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _context.prev = 0;
            setLoading(true);
            if (!actionCall) {
              _context.next = 5;
              break;
            }
            _context.next = 5;
            return actionCall();
          case 5:
            if (!refreshData) {
              _context.next = 8;
              break;
            }
            _context.next = 8;
            return refreshData();
          case 8:
            _context.next = 14;
            break;
          case 10:
            _context.prev = 10;
            _context.t0 = _context["catch"](0);
            antd__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .ZP.error(_context.t0.toString());
            console.log(_context.t0);
          case 14:
            _context.prev = 14;
            setLoading(false);
            return _context.finish(14);
          case 17:
          case "end":
            return _context.stop();
        }
      }, _callee, null, [[0, 10, 14, 17]]);
    }));
    return function onConfirm() {
      return _ref2.apply(this, arguments);
    };
  }();
  var onCancel = /*#__PURE__*/function () {
    var _ref3 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee2() {
      return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee2$(_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
          case "end":
            return _context2.stop();
        }
      }, _callee2);
    }));
    return function onCancel() {
      return _ref3.apply(this, arguments);
    };
  }();
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(antd__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Z, {
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_3__.FormattedMessage, {
      id: "action.confirm"
    }),
    description: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_3__.FormattedMessage, {
      id: "action.verify"
    }),
    onConfirm: onConfirm,
    onCancel: onCancel,
    okText: "\\u0110\\u1ED3ng \\xFD",
    cancelText: "Hu\\u1EF7",
    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(antd__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .ZP, {
      style: {
        display: 'flex',
        alignItems: 'center',
        borderRadius: 0 // Thi\u1EBFt l\u1EADp g\xF3c vu\xF4ng cho button
      },
      size: size || 'middle',
      danger: danger,
      type: buttonType,
      loading: loading,
      children: text
    })
  });
};
/* harmony default export */ __webpack_exports__.Z = (ActionPopConfirm);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///81568
`)},65573:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(29905);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(85893);


var FallbackComponent = function FallbackComponent() {
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .ZP, {
    status: "403",
    title: "Kh\\xF4ng th\\u1EC3 truy c\\u1EADp",
    subTitle: "Xin l\\u1ED7i, b\\u1EA1n kh\\xF4ng c\\xF3 quy\\u1EC1n xem n\\u1ED9i dung n\\xE0y."
    //   extra={
    //     <Button type="primary" href="/">
    //       Back Home
    //     </Button>
    //   }
  });
};
/* harmony default export */ __webpack_exports__.Z = (FallbackComponent);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNjU1NzMuanMiLCJtYXBwaW5ncyI6Ijs7QUFBOEI7QUFBQTtBQUU5QixJQUFNRyxpQkFBaUIsR0FBRyxTQUFwQkEsaUJBQWlCQSxDQUFBLEVBQVM7RUFDOUIsb0JBQ0VELHNEQUFBLENBQUNGLHNEQUFNO0lBQ0xJLE1BQU0sRUFBQyxLQUFLO0lBQ1pDLEtBQUssRUFBQyxpQ0FBb0I7SUFDMUJDLFFBQVEsRUFBQztJQUNUO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7RUFBQSxDQUNELENBQUM7QUFFTixDQUFDO0FBRUQsc0RBQWVILGlCQUFpQiIsInNvdXJjZXMiOlsid2VicGFjazovL2FudC1kZXNpZ24tcHJvLy4vc3JjL2NvbXBvbmVudHMvRmFsbGJhY2tDb250ZW50L2luZGV4LnRzeD9mZGRhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFJlc3VsdCB9IGZyb20gJ2FudGQnO1xyXG5cclxuY29uc3QgRmFsbGJhY2tDb21wb25lbnQgPSAoKSA9PiB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxSZXN1bHRcclxuICAgICAgc3RhdHVzPVwiNDAzXCJcclxuICAgICAgdGl0bGU9XCJLaMO0bmcgdGjhu4MgdHJ1eSBj4bqtcFwiXHJcbiAgICAgIHN1YlRpdGxlPVwiWGluIGzhu5dpLCBi4bqhbiBraMO0bmcgY8OzIHF1eeG7gW4geGVtIG7hu5lpIGR1bmcgbsOgeS5cIlxyXG4gICAgICAvLyAgIGV4dHJhPXtcclxuICAgICAgLy8gICAgIDxCdXR0b24gdHlwZT1cInByaW1hcnlcIiBocmVmPVwiL1wiPlxyXG4gICAgICAvLyAgICAgICBCYWNrIEhvbWVcclxuICAgICAgLy8gICAgIDwvQnV0dG9uPlxyXG4gICAgICAvLyAgIH1cclxuICAgIC8+XHJcbiAgKTtcclxufTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IEZhbGxiYWNrQ29tcG9uZW50O1xyXG4iXSwibmFtZXMiOlsiUmVzdWx0IiwianN4IiwiX2pzeCIsIkZhbGxiYWNrQ29tcG9uZW50Iiwic3RhdHVzIiwidGl0bGUiLCJzdWJUaXRsZSJdLCJzb3VyY2VSb290IjoiIn0=
//# sourceURL=webpack-internal:///65573
`)},10051:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`
// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  Z: function() { return /* binding */ TaskTodoTableEditer; }
});

// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/toConsumableArray.js
var toConsumableArray = __webpack_require__(19632);
var toConsumableArray_default = /*#__PURE__*/__webpack_require__.n(toConsumableArray);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/regeneratorRuntime.js
var regeneratorRuntime = __webpack_require__(15009);
var regeneratorRuntime_default = /*#__PURE__*/__webpack_require__.n(regeneratorRuntime);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(99289);
var asyncToGenerator_default = /*#__PURE__*/__webpack_require__.n(asyncToGenerator);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/slicedToArray.js
var slicedToArray = __webpack_require__(5574);
var slicedToArray_default = /*#__PURE__*/__webpack_require__.n(slicedToArray);
// EXTERNAL MODULE: ./src/services/sscript.ts
var sscript = __webpack_require__(39750);
// EXTERNAL MODULE: ./src/services/TaskAndTodo/index.ts
var TaskAndTodo = __webpack_require__(90705);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-table/es/Table.js + 76 modules
var Table = __webpack_require__(4894);
// EXTERNAL MODULE: ./src/.umi-production/exports.ts + 34 modules
var _umi_production_exports = __webpack_require__(7837);
// EXTERNAL MODULE: ./node_modules/antd/es/message/index.js
var message = __webpack_require__(45360);
// EXTERNAL MODULE: ./node_modules/antd/es/checkbox/index.js + 3 modules
var es_checkbox = __webpack_require__(84567);
// EXTERNAL MODULE: ./node_modules/dayjs/dayjs.min.js
var dayjs_min = __webpack_require__(27484);
var dayjs_min_default = /*#__PURE__*/__webpack_require__.n(dayjs_min);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./src/services/customerUser.ts
var customerUser = __webpack_require__(40063);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/PlusOutlined.js
var PlusOutlined = __webpack_require__(51042);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/layouts/ProForm/index.js + 3 modules
var ProForm = __webpack_require__(34994);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/Select/index.js
var Select = __webpack_require__(64317);
// EXTERNAL MODULE: ./node_modules/antd/es/form/index.js + 18 modules
var es_form = __webpack_require__(98138);
// EXTERNAL MODULE: ./node_modules/antd/es/button/index.js + 9 modules
var es_button = __webpack_require__(14726);
// EXTERNAL MODULE: ./node_modules/antd/es/modal/index.js + 1 modules
var modal = __webpack_require__(85576);
// EXTERNAL MODULE: ./node_modules/antd/es/row/index.js
var row = __webpack_require__(71230);
// EXTERNAL MODULE: ./node_modules/antd/es/col/index.js
var col = __webpack_require__(15746);
// EXTERNAL MODULE: ./node_modules/antd/es/input/index.js + 4 modules
var input = __webpack_require__(96365);
// EXTERNAL MODULE: ./node_modules/react/jsx-runtime.js
var jsx_runtime = __webpack_require__(85893);
;// CONCATENATED MODULE: ./src/components/Task/TaskTodo/CreateTodoForTask.tsx













var Item = es_form/* default */.Z.Item;
var CreateTodoForTask = function CreateTodoForTask(params) {
  var _useState = (0,react.useState)(false),
    _useState2 = slicedToArray_default()(_useState, 2),
    loading = _useState2[0],
    setLoading = _useState2[1];
  var _useState3 = (0,react.useState)(false),
    _useState4 = slicedToArray_default()(_useState3, 2),
    isOpen = _useState4[0],
    setOpen = _useState4[1];
  var _ProForm$useForm = ProForm/* ProForm */.A.useForm(),
    _ProForm$useForm2 = slicedToArray_default()(_ProForm$useForm, 1),
    form = _ProForm$useForm2[0];
  var intl = (0,_umi_production_exports.useIntl)();
  var showModal = function showModal() {
    setOpen(true);
  };
  var hideModal = function hideModal() {
    setOpen(false);
  };
  var handleOk = function handleOk() {
    form.submit();
  };
  var handleCancel = function handleCancel() {
    hideModal();
    form.resetFields();
  };
  return /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
    children: [/*#__PURE__*/(0,jsx_runtime.jsxs)(es_button/* default */.ZP, {
      type: "primary",
      onClick: showModal,
      style: {
        display: 'flex',
        alignItems: 'center'
      },
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)(PlusOutlined/* default */.Z, {}), " ", /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
        id: "common.add_sub_task"
      })]
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(modal/* default */.Z, {
      title: intl.formatMessage({
        id: 'common.add_sub_task'
      }),
      open: isOpen,
      onOk: handleOk,
      onCancel: handleCancel,
      confirmLoading: loading,
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(ProForm/* ProForm */.A, {
        submitter: false,
        layout: "horizontal",
        labelCol: {
          span: 24
        },
        labelAlign: "left",
        form: form,
        onFinish: ( /*#__PURE__*/function () {
          var _ref = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(value) {
            var result;
            return regeneratorRuntime_default()().wrap(function _callee$(_context) {
              while (1) switch (_context.prev = _context.next) {
                case 0:
                  _context.prev = 0;
                  value.farming_plan_task = params.task_id;
                  _context.next = 4;
                  return (0,sscript/* generalCreate */.UD)('iot_todo', {
                    data: value
                  });
                case 4:
                  result = _context.sent;
                  form.resetFields();
                  hideModal();
                  message/* default */.ZP.success(intl.formatMessage({
                    id: 'common.success'
                  }));
                  if (!params.refreshFnc) {
                    _context.next = 11;
                    break;
                  }
                  _context.next = 11;
                  return params.refreshFnc();
                case 11:
                  _context.next = 16;
                  break;
                case 13:
                  _context.prev = 13;
                  _context.t0 = _context["catch"](0);
                  message/* default */.ZP.error(_context.t0.toString());
                case 16:
                  _context.prev = 16;
                  setLoading(false);
                  return _context.finish(16);
                case 19:
                case "end":
                  return _context.stop();
              }
            }, _callee, null, [[0, 13, 16, 19]]);
          }));
          return function (_x) {
            return _ref.apply(this, arguments);
          };
        }()),
        children: /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
          gutter: 5,
          children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
            className: "gutter-row",
            md: 24,
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(Item, {
              label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
                id: "task.task_name"
              }),
              labelCol: {
                span: 24
              },
              rules: [{
                required: true,
                message: intl.formatMessage({
                  id: 'common.required'
                })
              }],
              name: "label",
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(input/* default */.Z, {})
            })
          }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
            className: "gutter-row",
            md: 24,
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
              label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
                id: "task.executor"
              }),
              showSearch: true,
              name: "customer_user_id",
              request: /*#__PURE__*/asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2() {
                var result;
                return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
                  while (1) switch (_context2.prev = _context2.next) {
                    case 0:
                      _context2.next = 2;
                      return (0,customerUser/* getCustomerUserList */.J9)();
                    case 2:
                      result = _context2.sent;
                      return _context2.abrupt("return", result.data.map(function (item) {
                        return {
                          label: item.last_name + ' ' + item.first_name,
                          value: item.name
                        };
                      }));
                    case 4:
                    case "end":
                      return _context2.stop();
                  }
                }, _callee2);
              }))
            })
          }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
            className: "gutter-row",
            md: 24,
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(Item, {
              label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
                id: "common.description"
              }),
              labelCol: {
                span: 24
              },
              name: "description",
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(input/* default */.Z.TextArea, {})
            })
          })]
        })
      })
    })]
  });
};
/* harmony default export */ var TaskTodo_CreateTodoForTask = (CreateTodoForTask);
// EXTERNAL MODULE: ./src/components/ActionPopConfirm/index.tsx
var ActionPopConfirm = __webpack_require__(81568);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/DeleteFilled.js + 1 modules
var DeleteFilled = __webpack_require__(27704);
;// CONCATENATED MODULE: ./src/components/Task/TaskTodo/DeleteTodoForTask.tsx








var DeleteTodoForTask_Item = es_form/* default */.Z.Item;
var DeleteTodoForTask = function DeleteTodoForTask(params) {
  var removeData = /*#__PURE__*/function () {
    var _ref = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
      return regeneratorRuntime_default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _context.prev = 0;
            _context.next = 3;
            return (0,sscript/* generalDelete */.ID)('iot_todo', params.value);
          case 3:
            _context.next = 8;
            break;
          case 5:
            _context.prev = 5;
            _context.t0 = _context["catch"](0);
            message/* default */.ZP.error(_context.t0.toString());
          case 8:
          case "end":
            return _context.stop();
        }
      }, _callee, null, [[0, 5]]);
    }));
    return function removeData() {
      return _ref.apply(this, arguments);
    };
  }();
  var access = (0,_umi_production_exports.useAccess)();
  if (!access.canDeleteAllInPageAccess()) {
    return null;
  }
  return /*#__PURE__*/(0,jsx_runtime.jsx)(ActionPopConfirm/* default */.Z, {
    actionCall: removeData,
    refreshData: params.refreshFnc,
    text: /*#__PURE__*/(0,jsx_runtime.jsx)(DeleteFilled/* default */.Z, {})
    //buttonType={'dashed'}
    ,
    danger: true,
    size: "small"
  });
};
/* harmony default export */ var TaskTodo_DeleteTodoForTask = (DeleteTodoForTask);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/EditOutlined.js
var EditOutlined = __webpack_require__(47389);
;// CONCATENATED MODULE: ./src/components/Task/TaskTodo/UpdateTodoForTask.tsx













var UpdateTodoForTask_Item = es_form/* default */.Z.Item;
var UpdateTodoForTask = function UpdateTodoForTask(params) {
  var _useState = (0,react.useState)(false),
    _useState2 = slicedToArray_default()(_useState, 2),
    loading = _useState2[0],
    setLoading = _useState2[1];
  var _useState3 = (0,react.useState)(false),
    _useState4 = slicedToArray_default()(_useState3, 2),
    isOpen = _useState4[0],
    setOpen = _useState4[1];
  var _ProForm$useForm = ProForm/* ProForm */.A.useForm(),
    _ProForm$useForm2 = slicedToArray_default()(_ProForm$useForm, 1),
    form = _ProForm$useForm2[0];
  var showModal = function showModal() {
    form.setFieldsValue(params.data);
    setOpen(true);
  };
  var hideModal = function hideModal() {
    setOpen(false);
  };
  var handleOk = function handleOk() {
    form.submit();
  };
  var handleCancel = function handleCancel() {
    hideModal();
    form.resetFields();
  };
  return /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
    children: [/*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
      size: "small"
      //type="dashed"
      ,
      style: {
        display: 'flex',
        alignItems: 'center'
      },
      onClick: showModal,
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(EditOutlined/* default */.Z, {})
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(modal/* default */.Z, {
      title: "Ch\\u1EC9nh s\\u1EEDa c\\xF4ng vi\\u1EC7c con",
      open: isOpen,
      onOk: handleOk,
      onCancel: handleCancel,
      confirmLoading: loading,
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(ProForm/* ProForm */.A, {
        submitter: false,
        layout: "horizontal",
        labelCol: {
          span: 24
        },
        labelAlign: "left",
        form: form,
        onFinish: ( /*#__PURE__*/function () {
          var _ref = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(value) {
            var result;
            return regeneratorRuntime_default()().wrap(function _callee$(_context) {
              while (1) switch (_context.prev = _context.next) {
                case 0:
                  _context.prev = 0;
                  value.farming_plan_task = params.task_id;
                  value.name = params.data.name;
                  _context.next = 5;
                  return (0,sscript/* generalUpdate */.I6)('iot_todo', params.data.name, {
                    data: value
                  });
                case 5:
                  result = _context.sent;
                  form.resetFields();
                  hideModal();
                  message/* default */.ZP.success('Success!');
                  if (!params.refreshFnc) {
                    _context.next = 12;
                    break;
                  }
                  _context.next = 12;
                  return params.refreshFnc();
                case 12:
                  _context.next = 17;
                  break;
                case 14:
                  _context.prev = 14;
                  _context.t0 = _context["catch"](0);
                  message/* default */.ZP.error(_context.t0.toString());
                case 17:
                  _context.prev = 17;
                  setLoading(false);
                  return _context.finish(17);
                case 20:
                case "end":
                  return _context.stop();
              }
            }, _callee, null, [[0, 14, 17, 20]]);
          }));
          return function (_x) {
            return _ref.apply(this, arguments);
          };
        }()),
        children: /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
          gutter: 5,
          children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
            className: "gutter-row",
            md: 24,
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(UpdateTodoForTask_Item, {
              label: "T\\xEAn c\\xF4ng vi\\u1EC7c",
              labelCol: {
                span: 24
              },
              rules: [{
                required: true,
                message: 'B\u1EAFt bu\u1ED9c \u0111i\u1EC1n'
              }],
              name: "label",
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(input/* default */.Z, {})
            })
          }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
            className: "gutter-row",
            md: 24,
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
              label: 'Ng\u01B0\u1EDDi th\u1EF1c hi\u1EC7n',
              showSearch: true,
              name: "customer_user_id",
              request: /*#__PURE__*/asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2() {
                var result;
                return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
                  while (1) switch (_context2.prev = _context2.next) {
                    case 0:
                      _context2.next = 2;
                      return (0,customerUser/* getCustomerUserList */.J9)();
                    case 2:
                      result = _context2.sent;
                      return _context2.abrupt("return", result.data.map(function (item) {
                        return {
                          label: item.last_name + ' ' + item.first_name,
                          value: item.name
                        };
                      }));
                    case 4:
                    case "end":
                      return _context2.stop();
                  }
                }, _callee2);
              }))
            })
          }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
            className: "gutter-row",
            md: 24,
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(UpdateTodoForTask_Item, {
              label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
                id: 'common.form.description'
              }),
              labelCol: {
                span: 24
              },
              name: "description",
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(input/* default */.Z.TextArea, {})
            })
          })]
        })
      })
    })]
  });
};
/* harmony default export */ var TaskTodo_UpdateTodoForTask = (UpdateTodoForTask);
;// CONCATENATED MODULE: ./src/components/Task/TaskTodo/TaskTodoTableEditer.tsx

















/* harmony default export */ var TaskTodoTableEditer = (function (_ref) {
  var task_id = _ref.task_id,
    _ref$showToolbar = _ref.showToolbar,
    showToolbar = _ref$showToolbar === void 0 ? true : _ref$showToolbar;
  var _useState = (0,react.useState)([]),
    _useState2 = slicedToArray_default()(_useState, 2),
    dataSource = _useState2[0],
    setDataSource = _useState2[1];
  var _useState3 = (0,react.useState)(),
    _useState4 = slicedToArray_default()(_useState3, 2),
    editableKeys = _useState4[0],
    setEditableRowKeys = _useState4[1];
  var _useState5 = (0,react.useState)(false),
    _useState6 = slicedToArray_default()(_useState5, 2),
    loading = _useState6[0],
    setLoading = _useState6[1];
  var intl = (0,_umi_production_exports.useIntl)();
  var getTaskTodo = /*#__PURE__*/function () {
    var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
      var resData;
      return regeneratorRuntime_default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _context.prev = 0;
            setLoading(true);
            _context.next = 4;
            return (0,TaskAndTodo/* getAllTodo */.IE)(task_id);
          case 4:
            resData = _context.sent;
            setDataSource(resData.data.map(function (d) {
              d.start_date = dayjs_min_default()(d.start_date).isValid() ? dayjs_min_default()(d.start_date) : null;
              d.end_date = dayjs_min_default()(d.end_date).isValid() ? dayjs_min_default()(d.end_date) : null;
              return d;
            }));
            _context.next = 11;
            break;
          case 8:
            _context.prev = 8;
            _context.t0 = _context["catch"](0);
            message/* default */.ZP.error(_context.t0.toString());
          case 11:
            _context.prev = 11;
            setLoading(false);
            return _context.finish(11);
          case 14:
          case "end":
            return _context.stop();
        }
      }, _callee, null, [[0, 8, 11, 14]]);
    }));
    return function getTaskTodo() {
      return _ref2.apply(this, arguments);
    };
  }();
  (0,react.useEffect)(function () {
    getTaskTodo();
  }, []);
  var access = (0,_umi_production_exports.useAccess)();
  var canCreateTask = access.canCreateInWorkFlowManagement();
  var canUpdateTask = access.canUpdateInWorkFlowManagement();
  var canDeleteTask = access.canDeleteInWorkFlowManagement();
  var columns = [{
    title: intl.formatMessage({
      id: 'workflowTab.complete'
    }),
    dataIndex: 'is_completed',
    width: 100,
    render: function render(dom, entity) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)(es_checkbox/* default */.Z, {
        onChange: ( /*#__PURE__*/function () {
          var _ref3 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2(v) {
            return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
              while (1) switch (_context2.prev = _context2.next) {
                case 0:
                  _context2.prev = 0;
                  _context2.next = 3;
                  return (0,sscript/* generalUpdate */.I6)('iot_todo', entity.name, {
                    data: {
                      is_completed: v.target.checked ? 1 : 0
                    }
                  });
                case 3:
                  _context2.next = 5;
                  return getTaskTodo();
                case 5:
                  _context2.next = 10;
                  break;
                case 7:
                  _context2.prev = 7;
                  _context2.t0 = _context2["catch"](0);
                  message/* default */.ZP.error(_context2.t0.toString());
                case 10:
                case "end":
                  return _context2.stop();
              }
            }, _callee2, null, [[0, 7]]);
          }));
          return function (_x) {
            return _ref3.apply(this, arguments);
          };
        }()),
        checked: entity.is_completed ? true : false
      });
    }
  }, {
    title: intl.formatMessage({
      id: 'workflowTab.work_name'
    }),
    dataIndex: 'label',
    width: 250
  }, {
    title: intl.formatMessage({
      id: 'workflowTab.executor'
    }),
    dataIndex: 'user_full_name',
    width: 250,
    render: function render(dom, entity) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)(jsx_runtime.Fragment, {
        children: "".concat(entity.user_last_name, " ").concat(entity.user_first_name)
      });
    }
  }, {
    title: intl.formatMessage({
      id: 'common.description'
    }),
    dataIndex: 'description',
    formItemProps: {
      rules: [{
        required: true,
        whitespace: true,
        message: intl.formatMessage({
          id: 'common.description_required'
        })
      }]
    }
  }, {
    title: intl.formatMessage({
      id: 'common.action'
    }),
    dataIndex: 'name',
    width: 100,
    render: function render(dom, entity) {
      return /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
        children: [canUpdateTask && /*#__PURE__*/(0,jsx_runtime.jsx)(TaskTodo_UpdateTodoForTask, {
          refreshFnc: getTaskTodo,
          task_id: task_id,
          data: entity
        }), '  ', canDeleteTask && /*#__PURE__*/(0,jsx_runtime.jsx)(TaskTodo_DeleteTodoForTask, {
          refreshFnc: getTaskTodo,
          value: dom
        })]
      });
    }
  }];
  var toolBarRenderButton = [];
  if (canCreateTask || canUpdateTask) {
    toolBarRenderButton.push( /*#__PURE__*/(0,jsx_runtime.jsx)(TaskTodo_CreateTodoForTask, {
      refreshFnc: getTaskTodo,
      task_id: task_id
    }));
  }
  return /*#__PURE__*/(0,jsx_runtime.jsx)(jsx_runtime.Fragment, {
    children: /*#__PURE__*/(0,jsx_runtime.jsx)(Table/* default */.Z, {
      headerTitle: intl.formatMessage({
        id: 'common.sub_task'
      }),
      columns: columns,
      rowKey: "name",
      dataSource: toConsumableArray_default()(dataSource),
      toolBarRender: showToolbar && function () {
        return toolBarRenderButton;
      },
      search: false,
      tooltip: false,
      pagination: false
    })
  });
});//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///10051
`)},7976:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_defineProperty_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(9783);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_defineProperty_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_defineProperty_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(5574);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _assets_img_icons_tree_green_svg__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(43032);
/* harmony import */ var _common_contanst_constanst__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(86604);
/* harmony import */ var _services_farming_plan__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(74459);
/* harmony import */ var _services_sscript__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(39750);
/* harmony import */ var _services_utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(467);
/* harmony import */ var _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(34994);
/* harmony import */ var _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(64176);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(7837);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(66309);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(42075);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(7134);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(71230);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(15746);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(25514);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(83863);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(45360);
/* harmony import */ var antd_use_styles__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(38513);
/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(27484);
/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(moment__WEBPACK_IMPORTED_MODULE_11__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(67294);
/* harmony import */ var _Task_TaskTodo_TaskTodoTableEditer__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(10051);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(85893);



















var useStyles = (0,antd_use_styles__WEBPACK_IMPORTED_MODULE_10__/* .createStyles */ .k)(function () {
  return {
    table: {
      '& .ant-pro-table-list-toolbar-left': {
        flex: 'none'
      }
    }
  };
});
var dateRangeFilterKey = 'dateRange';
var getStatus = function getStatus(key) {
  switch (key) {
    case 'Plan':
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsx)(antd__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .Z, {
        color: "success",
        children: "K\\u1EBF ho\\u1EA1ch"
      });
    case 'Done':
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsx)(antd__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .Z, {
        color: "success",
        children: "\\u0110\\xE3 ho\\xE0n th\\xE0nh"
      });
    case 'In progress':
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsx)(antd__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .Z, {
        color: "blue",
        children: "\\u0110ang th\\u1EF1c hi\\u1EC7n"
      });
    case 'Pending':
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsx)(antd__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .Z, {
        color: "warning",
        children: "Tr\\xEC ho\\xE3n"
      });
    default:
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsx)(antd__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .Z, {
        children: key
      });
  }
};
var TodayTasks = function TodayTasks(_ref) {
  var cropId = _ref.cropId;
  var styles = useStyles();
  var intl = (0,_umijs_max__WEBPACK_IMPORTED_MODULE_9__.useIntl)();
  // const [searchParams, setSearchParams] = useSearchParams();
  // const farmingPlanState = searchParams.get('pl_state_name');
  // const dateRangeFilter = searchParams.get(dateRangeFilterKey);
  var _ProForm$useForm = _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_16__/* .ProForm */ .A.useForm(),
    _ProForm$useForm2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_3___default()(_ProForm$useForm, 1),
    formFilter = _ProForm$useForm2[0];
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_12__.useState)([]),
    _useState2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_3___default()(_useState, 2),
    dataSource = _useState2[0],
    setDataSource = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_12__.useState)(false),
    _useState4 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_3___default()(_useState3, 2),
    loading = _useState4[0],
    setLoading = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_12__.useState)([]),
    _useState6 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_3___default()(_useState5, 2),
    expandedRowKeys = _useState6[0],
    setExpandedRowKeys = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_12__.useState)([]),
    _useState8 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_3___default()(_useState7, 2),
    selectedRowKeys = _useState8[0],
    setSelectedRowKeys = _useState8[1];
  var rowSelection = {
    selectedRowKeys: selectedRowKeys,
    onChange: function onChange(keys) {
      return setSelectedRowKeys(keys);
    }
  };
  var actionRef = (0,react__WEBPACK_IMPORTED_MODULE_12__.useRef)();
  // reload when form filter change
  // useEffect(() => {
  //   if (actionRef.current) {
  //     actionRef.current.reload();
  //   }
  // }, [dateRangeFilter]);

  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.Fragment, {
    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsx)(_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_17__/* .ProList */ .Rs, {
      actionRef: actionRef,
      request: ( /*#__PURE__*/function () {
        var _ref2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee(params) {
          var today, filters, res;
          return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee$(_context) {
            while (1) switch (_context.prev = _context.next) {
              case 0:
                // const today = moment().toISOString();
                // const startTime  = dayjs().startOf('day').toISOString();
                // const endTime = dayjs().endOf('day').toISOString();
                today = moment__WEBPACK_IMPORTED_MODULE_11___default()().format('YYYY-MM-DD'); // const startDate = moment().startOf('date').toISOString();
                // const endDate = moment().endOf('date').toISOString();
                filters = [[_common_contanst_constanst__WEBPACK_IMPORTED_MODULE_5__/* .DOCTYPE_ERP */ .lH.iotFarmingPlanTask, 'end_date', '>=', today], [_common_contanst_constanst__WEBPACK_IMPORTED_MODULE_5__/* .DOCTYPE_ERP */ .lH.iotFarmingPlanTask, 'start_date', '<=', today]];
                if (cropId) {
                  filters.push([_common_contanst_constanst__WEBPACK_IMPORTED_MODULE_5__/* .DOCTYPE_ERP */ .lH.iotCrop, 'name', 'like', cropId]);
                }
                _context.next = 5;
                return (0,_services_farming_plan__WEBPACK_IMPORTED_MODULE_6__/* .getTaskManagerList */ .UM)({
                  page: params.current,
                  size: params.pageSize,
                  filters: filters,
                  or_filters: []
                });
              case 5:
                res = _context.sent;
                return _context.abrupt("return", {
                  data: res.data,
                  total: res.pagination.totalElements
                });
              case 7:
              case "end":
                return _context.stop();
            }
          }, _callee);
        }));
        return function (_x) {
          return _ref2.apply(this, arguments);
        };
      }()),
      metas: {
        title: D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_defineProperty_js__WEBPACK_IMPORTED_MODULE_0___default()({
          dataIndex: 'label',
          search: false,
          render: function render(dom, entity) {
            return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_9__.Link, {
              target: "_blank",
              to: '/farming-management/workflow-management/detail/' + entity.name,
              children: dom
            });
          }
        }, "search", false),
        subTitle: {
          dataIndex: 'status',
          render: function render(dom, entity) {
            return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_18__/* ["default"] */ .Z, {
              size: 0,
              children: [getStatus(dom), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .Z, {
                color: "blue",
                children: [' ', intl.formatMessage({
                  id: 'workflowTab.executionTime'
                }), ' ', moment__WEBPACK_IMPORTED_MODULE_11___default()(entity.start_date).format('HH:mm DD/MM/YYYY'), ' ', intl.formatMessage({
                  id: 'common.to'
                }), ' ', moment__WEBPACK_IMPORTED_MODULE_11___default()(entity.end_date).format('HH:mm DD/MM/YYYY')]
              })]
            });
          },
          search: false
        },
        type: {},
        description: {
          render: function render(value, record) {
            var _record$assigned_to_i;
            var info = (_record$assigned_to_i = record.assigned_to_info) === null || _record$assigned_to_i === void 0 ? void 0 : _record$assigned_to_i[0];
            if (!info) {
              return null;
            }
            var involveInArr = record.involve_in_users || [];
            var userNames = (involveInArr === null || involveInArr === void 0 ? void 0 : involveInArr.map(function (data) {
              return "".concat(data.last_name, " ").concat(data.first_name, " ");
            })) || [];
            return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_18__/* ["default"] */ .Z, {
              children: [info.user_avatar && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsx)(antd__WEBPACK_IMPORTED_MODULE_19__/* ["default"] */ .C, {
                size: 'small',
                src: (0,_services_utils__WEBPACK_IMPORTED_MODULE_8__/* .getFileUrlV2 */ .mT)({
                  src: info.user_avatar
                })
              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsx)(antd__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .Z, {
                children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsxs)("span", {
                  children: ["Ng\\u01B0\\u1EDDi th\\u1EF1c hi\\u1EC7n: ", info.last_name || '', " ", "".concat(info.first_name || '', " ")]
                })
              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsx)(antd__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .Z, {
                children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsxs)("span", {
                  children: ["Ng\\u01B0\\u1EDDi li\\xEAn quan: ", userNames.join(', ')]
                })
              })]
            });
          }
        },
        avatar: {
          render: function render() {
            return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.Fragment, {
              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsx)("img", {
                src: _assets_img_icons_tree_green_svg__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z
              })
            });
          }
        },
        content: {
          render: function render(dom, entity) {
            return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_20__/* ["default"] */ .Z, {
              gutter: [20, 20],
              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsx)(antd__WEBPACK_IMPORTED_MODULE_21__/* ["default"] */ .Z, {
                md: 18,
                children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsx)(_Task_TaskTodo_TaskTodoTableEditer__WEBPACK_IMPORTED_MODULE_13__/* ["default"] */ .Z, {
                  task_id: entity.name,
                  showToolbar: false
                })
              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsx)(antd__WEBPACK_IMPORTED_MODULE_21__/* ["default"] */ .Z, {
                md: 6,
                children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_20__/* ["default"] */ .Z, {
                  gutter: [10, 10],
                  children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsx)(antd__WEBPACK_IMPORTED_MODULE_21__/* ["default"] */ .Z, {
                    md: 24,
                    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_22__/* ["default"] */ .Z.Text, {
                      children: ["M\\xF9a v\\u1EE5: ", entity.crop_name]
                    })
                  }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsx)(antd__WEBPACK_IMPORTED_MODULE_21__/* ["default"] */ .Z, {
                    md: 24,
                    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_22__/* ["default"] */ .Z.Text, {
                      children: ["Giai \\u0111o\\u1EA1n: ", entity.state_name]
                    })
                  }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsx)(antd__WEBPACK_IMPORTED_MODULE_21__/* ["default"] */ .Z, {
                    md: 24,
                    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsx)(antd__WEBPACK_IMPORTED_MODULE_23__/* ["default"] */ .Z, {
                      style: {
                        width: '100%'
                      },
                      options: [{
                        label: 'L\xEAn k\u1EBF ho\u1EA1ch',
                        value: 'Plan'
                      }, {
                        label: '\u0110ang th\u1EF1c hi\u1EC7n',
                        value: 'In progress'
                      }, {
                        label: 'Ho\xE0n t\u1EA5t',
                        value: 'Done'
                      }, {
                        label: 'Tr\xEC ho\xE3n',
                        value: 'Pending'
                      }],
                      onChange: ( /*#__PURE__*/function () {
                        var _ref3 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee2(v) {
                          var _actionRef$current, _error$toString;
                          return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee2$(_context2) {
                            while (1) switch (_context2.prev = _context2.next) {
                              case 0:
                                _context2.prev = 0;
                                _context2.next = 3;
                                return (0,_services_sscript__WEBPACK_IMPORTED_MODULE_7__/* .generalUpdate */ .I6)('iot_farming_plan_task', entity.name, {
                                  data: {
                                    name: entity.name,
                                    status: v
                                  }
                                });
                              case 3:
                                (_actionRef$current = actionRef.current) === null || _actionRef$current === void 0 || _actionRef$current.reload();
                                _context2.next = 9;
                                break;
                              case 6:
                                _context2.prev = 6;
                                _context2.t0 = _context2["catch"](0);
                                antd__WEBPACK_IMPORTED_MODULE_24__/* ["default"] */ .ZP.error(_context2.t0 === null || _context2.t0 === void 0 || (_error$toString = _context2.t0.toString) === null || _error$toString === void 0 ? void 0 : _error$toString.call(_context2.t0));
                              case 9:
                              case "end":
                                return _context2.stop();
                            }
                          }, _callee2, null, [[0, 6]]);
                        }));
                        return function (_x2) {
                          return _ref3.apply(this, arguments);
                        };
                      }()),
                      value: entity.status
                    })
                  }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsx)(antd__WEBPACK_IMPORTED_MODULE_21__/* ["default"] */ .Z, {
                    md: 24,
                    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_9__.Link, {
                      target: "_blank",
                      to: '/farming-management/workflow-management/detail/' + entity.name,
                      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_9__.FormattedMessage, {
                        id: "workflowTab.detail"
                      })
                    })
                  })]
                })
              })]
            });
          }
        }
      },
      rowKey: "name",
      headerTitle: intl.formatMessage({
        id: 'workflowTab.workList'
      }),
      itemLayout: "vertical",
      pagination: {}
    })
  });
};
/* harmony default export */ __webpack_exports__.Z = (TodayTasks);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///7976
`)},85694:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _components_ActionPopConfirm__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(81568);
/* harmony import */ var _services_TaskAndTodo__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(90705);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(7837);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(45360);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(85893);







var DeleteMultiTask = function DeleteMultiTask(params) {
  var handlerDeleteData = /*#__PURE__*/function () {
    var _ref = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee() {
      var i;
      return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _context.prev = 0;
            i = 0;
          case 2:
            if (!(i < params.tasks_id.length)) {
              _context.next = 8;
              break;
            }
            _context.next = 5;
            return (0,_services_TaskAndTodo__WEBPACK_IMPORTED_MODULE_3__/* .removeTask */ .F6)(params.tasks_id[i]);
          case 5:
            i++;
            _context.next = 2;
            break;
          case 8:
            _context.next = 13;
            break;
          case 10:
            _context.prev = 10;
            _context.t0 = _context["catch"](0);
            antd__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .ZP.error(_context.t0.toString());
          case 13:
            _context.prev = 13;
            if (!params.refreshFnc) {
              _context.next = 17;
              break;
            }
            _context.next = 17;
            return params.refreshFnc();
          case 17:
            return _context.finish(13);
          case 18:
          case "end":
            return _context.stop();
        }
      }, _callee, null, [[0, 10, 13, 18]]);
    }));
    return function handlerDeleteData() {
      return _ref.apply(this, arguments);
    };
  }();
  var access = (0,_umijs_max__WEBPACK_IMPORTED_MODULE_4__.useAccess)();
  if (!access.canDeleteAllInPageAccess()) {
    return null;
  }
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(_components_ActionPopConfirm__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z, {
    actionCall: handlerDeleteData,
    refreshData: params.refreshFnc,
    text: 'Delete',
    buttonType: 'primary',
    danger: true
  });
};
/* harmony default export */ __webpack_exports__.Z = (DeleteMultiTask);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///85694
`)},96409:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectWithoutProperties_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(13769);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectWithoutProperties_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectWithoutProperties_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(5574);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _assets_img_icons_tree_green_svg__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(43032);
/* harmony import */ var _services_cropManager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(77890);
/* harmony import */ var _services_farming_plan__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(74459);
/* harmony import */ var _services_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(467);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(51042);
/* harmony import */ var _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(34994);
/* harmony import */ var _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(64317);
/* harmony import */ var _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(4894);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(7837);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(42075);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(66309);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(7134);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(83062);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(14726);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(71230);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(15746);
/* harmony import */ var antd_use_styles__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(38513);
/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(27484);
/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(moment__WEBPACK_IMPORTED_MODULE_10__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(67294);
/* harmony import */ var react_router_dom__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(13854);
/* harmony import */ var _WorkflowManagement_Create__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(22864);
/* harmony import */ var _DeleteMultiTaskConfirm__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(85694);
/* harmony import */ var _UpdateStatusMultiTask__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(38790);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(85893);




var _excluded = ["defaultRender"],
  _excluded2 = ["defaultRender"];


















var useStyles = (0,antd_use_styles__WEBPACK_IMPORTED_MODULE_9__/* .createStyles */ .k)(function () {
  return {
    table: {
      '& .ant-pro-table-list-toolbar-left': {
        flex: 'none'
      }
    }
  };
});
var dateRangeFilterKey = 'dateRange';
var TableCropPlanDetail = function TableCropPlanDetail(_ref) {
  var cropId = _ref.cropId,
    createNewTaskInModal = _ref.createNewTaskInModal;
  var styles = useStyles();
  var _useSearchParams = (0,react_router_dom__WEBPACK_IMPORTED_MODULE_16__/* .useSearchParams */ .lr)(),
    _useSearchParams2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_3___default()(_useSearchParams, 2),
    searchParams = _useSearchParams2[0],
    setSearchParams = _useSearchParams2[1];
  var farmingPlanState = searchParams.get('pl_state_name');
  var dateRangeFilter = searchParams.get(dateRangeFilterKey);
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_11__.useState)(''),
    _useState2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_3___default()(_useState, 2),
    planId = _useState2[0],
    setPlanId = _useState2[1];
  var _ProForm$useForm = _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_17__/* .ProForm */ .A.useForm(),
    _ProForm$useForm2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_3___default()(_ProForm$useForm, 1),
    formFilter = _ProForm$useForm2[0];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_11__.useState)([]),
    _useState4 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_3___default()(_useState3, 2),
    selectedRowKeys = _useState4[0],
    setSelectedRowKeys = _useState4[1];
  var tableRef = (0,react__WEBPACK_IMPORTED_MODULE_11__.useRef)();
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_11__.useState)({}),
    _useState6 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_3___default()(_useState5, 2),
    enumCropValues = _useState6[0],
    setEnumCropValues = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_11__.useState)({}),
    _useState8 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_3___default()(_useState7, 2),
    enumStateValues = _useState8[0],
    setEnumStateValues = _useState8[1];
  var intl = (0,_umijs_max__WEBPACK_IMPORTED_MODULE_8__.useIntl)();
  (0,react__WEBPACK_IMPORTED_MODULE_11__.useEffect)(function () {
    // G\u1ECDi API \u0111\u1EC3 l\u1EA5y danh s\xE1ch gi\xE1 tr\u1ECB cho valueEnum
    var fetchData = /*#__PURE__*/function () {
      var _ref2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee() {
        var response, data, enumCropObject, filters, plan, _planId, responseState, dataState, enumStateObject;
        return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee$(_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              _context.prev = 0;
              _context.next = 3;
              return (0,_services_cropManager__WEBPACK_IMPORTED_MODULE_5__/* .getCropList */ .TQ)({
                page: 1,
                size: 1000
              });
            case 3:
              response = _context.sent;
              data = response.data;
              enumCropObject = {};
              data.forEach(function (item) {
                enumCropObject[item.name] = item.label;
              });
              setEnumCropValues(enumCropObject);
              //get plan_id
              filters = [['iot_farming_plan', 'crop', 'like', cropId]];
              _context.next = 11;
              return (0,_services_farming_plan__WEBPACK_IMPORTED_MODULE_6__/* .getFarmingPlan */ .j1)('', filters);
            case 11:
              plan = _context.sent;
              console.log('planId', plan);
              _planId = plan.data.name;
              console.log('planId 2', _planId);
              setPlanId(_planId);
              //similar for state
              _context.next = 18;
              return (0,_services_farming_plan__WEBPACK_IMPORTED_MODULE_6__/* .getFarmingPlanState */ .jY)({
                page: 1,
                size: 1000
              });
            case 18:
              responseState = _context.sent;
              dataState = responseState.data;
              enumStateObject = {};
              dataState.forEach(function (item) {
                enumStateObject[item.label] = item.label;
              });
              setEnumStateValues(enumStateObject);
              _context.next = 28;
              break;
            case 25:
              _context.prev = 25;
              _context.t0 = _context["catch"](0);
              console.error('Error fetching enum values:', _context.t0);
            case 28:
            case "end":
              return _context.stop();
          }
        }, _callee, null, [[0, 25]]);
      }));
      return function fetchData() {
        return _ref2.apply(this, arguments);
      };
    }();
    fetchData();
  }, []); // useEffect ch\u1EC9 ch\u1EA1y m\u1ED9t l\u1EA7n khi component \u0111\u01B0\u1EE3c mount

  var columns = [{
    title: intl.formatMessage({
      id: 'common.index'
    }),
    renderText: function renderText(text, record, index, action) {
      return index + 1;
    },
    search: false
  }, {
    title: intl.formatMessage({
      id: 'workflowTab.workName'
    }),
    dataIndex: 'label',
    renderText: function renderText(_text, record, index, action) {
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_8__.Link, {
        to: "/farming-management/seasonal-management/detail/".concat(cropId, "/workflow-management/detail/").concat(record.name),
        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_18__/* ["default"] */ .Z, {
          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.jsx)("img", {
            src: _assets_img_icons_tree_green_svg__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z
          }), " ", _text]
        })
      });
    },
    sorter: true
  }, {
    title: intl.formatMessage({
      id: 'common.status'
    }),
    dataIndex: 'status',
    valueEnum: {
      Plan: {
        text: 'K\u1EBF ho\u1EA1ch',
        status: 'Default'
      },
      Done: {
        text: '\u0110\xE3 ho\xE0n th\xE0nh',
        status: 'Success'
      },
      'In progress': {
        text: '\u0110ang th\u1EF1c hi\u1EC7n',
        status: 'Warning'
      },
      Pending: {
        text: 'Tr\xEC ho\xE3n',
        status: 'Default'
      }
    },
    render: function render(dom, entity, index) {
      switch (entity.status) {
        case 'Plan':
          return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.jsx)(antd__WEBPACK_IMPORTED_MODULE_19__/* ["default"] */ .Z, {
            color: "cyan",
            children: "K\\u1EBF ho\\u1EA1ch"
          });
        case 'Done':
          return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.jsx)(antd__WEBPACK_IMPORTED_MODULE_19__/* ["default"] */ .Z, {
            color: "success",
            children: "\\u0110\\xE3 ho\\xE0n th\\xE0nh"
          });
        case 'In progress':
          return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.jsx)(antd__WEBPACK_IMPORTED_MODULE_19__/* ["default"] */ .Z, {
            color: "warning",
            children: "\\u0110ang th\\u1EF1c hi\\u1EC7n"
          });
        case 'Pending':
          return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.jsx)(antd__WEBPACK_IMPORTED_MODULE_19__/* ["default"] */ .Z, {
            color: "default",
            children: "Tr\\xEC ho\\xE3n"
          });
        default:
          return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.jsx)(antd__WEBPACK_IMPORTED_MODULE_19__/* ["default"] */ .Z, {});
      }
    },
    sorter: true
  }, {
    title: intl.formatMessage({
      id: 'common.start_date'
    }),
    dataIndex: 'start_date',
    render: function render(dom, entity) {
      return moment__WEBPACK_IMPORTED_MODULE_10___default()(entity.start_date).format('YYYY-MM-DD HH:mm'); // Format date to 'YYYY-MM-DD HH:mm'
    },
    sortDirections: ['ascend', 'descend', 'ascend'],
    sorter: true,
    defaultSortOrder: 'descend',
    valueType: 'dateTime'
  }, {
    title: intl.formatMessage({
      id: 'common.end_date'
    }),
    dataIndex: 'end_date',
    render: function render(dom, entity) {
      return moment__WEBPACK_IMPORTED_MODULE_10___default()(entity.end_date).format('YYYY-MM-DD HH:mm'); // Format date to 'YYYY-MM-DD HH:mm'
    },
    sortDirections: ['ascend', 'descend', 'ascend'],
    sorter: true,
    valueType: 'dateTime'
  }, {
    title: intl.formatMessage({
      id: 'workflowTab.executor'
    }),
    dataIndex: 'assigned_to',
    render: function render(value, record) {
      var _record$assigned_to_i;
      var info = (_record$assigned_to_i = record.assigned_to_info) === null || _record$assigned_to_i === void 0 ? void 0 : _record$assigned_to_i[0];
      if (!info) {
        return null;
      }
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_18__/* ["default"] */ .Z, {
        children: [info.user_avatar && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.jsx)(antd__WEBPACK_IMPORTED_MODULE_20__/* ["default"] */ .C, {
          size: 'small',
          src: (0,_services_utils__WEBPACK_IMPORTED_MODULE_7__/* .getFileUrlV2 */ .mT)({
            src: info.user_avatar
          })
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.jsx)("span", {
          children: "".concat(info.first_name || '', " ").concat(info.last_name || '', " ")
        })]
      });
    },
    search: false,
    sorter: true
  }, {
    title: 'Th\xE0nh vi\xEAn li\xEAn quan',
    hideInTable: true,
    dataIndex: 'involve_in_users',
    render: function render(value, record) {
      try {
        var involveInArr = record.involve_in_users;
        var userNames = involveInArr.map(function (data) {
          return "".concat(data.first_name, " ").concat(data.last_name);
        });
        return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.Fragment, {
          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.jsx)(antd__WEBPACK_IMPORTED_MODULE_20__/* ["default"].Group */ .C.Group, {
            children: involveInArr.map(function (data, index) {
              return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.jsx)(antd__WEBPACK_IMPORTED_MODULE_21__/* ["default"] */ .Z, {
                title: "".concat(data.full_name ? data.full_name : "".concat(data.last_name || '', " ").concat(data.first_name || '')),
                placement: "top",
                children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.jsx)(antd__WEBPACK_IMPORTED_MODULE_20__/* ["default"] */ .C, {
                  style: {
                    backgroundColor: '#87d068'
                  },
                  children: data.first_name
                })
              }, 'avt' + index);
            })
          })
        });
      } catch (error) {
        return null;
      }
    },
    search: false,
    width: 100
  }, {
    title: 'V\u1EE5 m\xF9a',
    hideInTable: true,
    dataIndex: 'crop_id',
    valueEnum: enumCropValues,
    renderFormItem: function renderFormItem(_, _ref3) {
      var defaultRender = _ref3.defaultRender,
        rest = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectWithoutProperties_js__WEBPACK_IMPORTED_MODULE_0___default()(_ref3, _excluded);
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.jsx)(_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_22__/* ["default"] */ .Z, {
        options: rest.options,
        showSearch: true
      });
    },
    search: cropId ? false : undefined,
    sorter: true
  }, {
    title: 'Giai \u0111o\u1EA1n',
    hideInTable: true,
    hideInSearch: true,
    dataIndex: 'state_name',
    valueEnum: enumStateValues,
    sorter: true,
    renderFormItem: function renderFormItem(_, _ref4) {
      var defaultRender = _ref4.defaultRender,
        rest = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectWithoutProperties_js__WEBPACK_IMPORTED_MODULE_0___default()(_ref4, _excluded2);
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.jsx)(_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_22__/* ["default"] */ .Z, {
        options: rest.options,
        showSearch: true
      });
    }
  }, {
    title: intl.formatMessage({
      id: 'common.completion_level'
    }),
    dataIndex: 'todo_done',
    search: false,
    sorter: true,
    render: function render(value, record) {
      return "".concat(record.todo_done || 0, "/").concat(record.todo_total || 0);
    }
  }];
  var actionRef = (0,react__WEBPACK_IMPORTED_MODULE_11__.useRef)();
  // reload when form filter change
  (0,react__WEBPACK_IMPORTED_MODULE_11__.useEffect)(function () {
    if (actionRef.current) {
      actionRef.current.reload();
    }
  }, [dateRangeFilter]);
  var reloadTable = /*#__PURE__*/function () {
    var _ref5 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee2() {
      var _actionRef$current;
      return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee2$(_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            (_actionRef$current = actionRef.current) === null || _actionRef$current === void 0 || _actionRef$current.reload();
            setSelectedRowKeys([]); // Clear the selection
          case 2:
          case "end":
            return _context2.stop();
        }
      }, _callee2);
    }));
    return function reloadTable() {
      return _ref5.apply(this, arguments);
    };
  }();
  // modal create new task
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_11__.useState)(false),
    _useState10 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_3___default()(_useState9, 2),
    openModalCreateNewTask = _useState10[0],
    setOpenModalCreateNewTask = _useState10[1];
  var access = (0,_umijs_max__WEBPACK_IMPORTED_MODULE_8__.useAccess)();
  var canCreate = access.canCreateInWorkFlowManagement();
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.Fragment, {
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.jsx)(_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_23__/* ["default"] */ .Z, {
      actionRef: actionRef,
      className: styles.table,
      form: {
        syncToUrl: true,
        defaultCollapsed: false,
        initialValues: {
          pl_state_name: farmingPlanState
        }
      },
      search: {
        labelWidth: 'auto',
        span: {
          xs: 24,
          sm: 24,
          md: 12,
          lg: 12,
          xl: 12,
          xxl: 12
        }
      },
      toolbar: {
        filter: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.jsx)(_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_17__/* .ProForm */ .A, {
          form: formFilter,
          name: "crop-detail:table-filter",
          onValuesChange: function onValuesChange(changeValue) {
            if (changeValue.dateRange) {
              searchParams.set(dateRangeFilterKey, JSON.stringify(changeValue.dateRange));
            } else {
              searchParams["delete"](dateRangeFilterKey);
            }
            setSearchParams(searchParams);
          },
          layout: "inline",
          submitter: false,
          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.jsx)(antd__WEBPACK_IMPORTED_MODULE_18__/* ["default"] */ .Z, {
            size: 'large',
            children: canCreate && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.jsx)(antd__WEBPACK_IMPORTED_MODULE_24__/* ["default"] */ .ZP, {
              icon: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_25__/* ["default"] */ .Z, {}),
              onClick: function onClick() {
                if (createNewTaskInModal) {
                  setOpenModalCreateNewTask(true);
                  return;
                }
                var urlParams = new URLSearchParams(window.location.search);
                var currentFarmingPlanState = urlParams.get('pl_state_name');
                _umijs_max__WEBPACK_IMPORTED_MODULE_8__.history.push("/farming-management/workflow-management/create".concat(currentFarmingPlanState ? "?farming_plan_state=".concat(currentFarmingPlanState) : ''));
              },
              children: intl.formatMessage({
                id: 'workflowTab.createWork'
              })
            })
          })
        })
      },
      rowSelection: {
        selectedRowKeys: selectedRowKeys,
        onChange: function onChange(selectedKeys) {
          return setSelectedRowKeys(selectedKeys);
        }
      },
      tableAlertOptionRender: function tableAlertOptionRender(_ref6) {
        var selectedRowKeys = _ref6.selectedRowKeys,
          selectedRows = _ref6.selectedRows,
          onCleanSelected = _ref6.onCleanSelected;
        return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.Fragment, {
          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_26__/* ["default"] */ .Z, {
            justify: "space-between",
            gutter: 16,
            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.jsx)(antd__WEBPACK_IMPORTED_MODULE_27__/* ["default"] */ .Z, {
              className: "gutter-row",
              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.jsx)(_UpdateStatusMultiTask__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .Z, {
                refreshFnc: reloadTable,
                tasks_id: selectedRows.map(function (d) {
                  return d.name;
                })
              })
            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.jsx)(antd__WEBPACK_IMPORTED_MODULE_27__/* ["default"] */ .Z, {
              className: "gutter-row",
              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.jsx)(_DeleteMultiTaskConfirm__WEBPACK_IMPORTED_MODULE_13__/* ["default"] */ .Z, {
                refreshFnc: reloadTable,
                tasks_id: selectedRows.map(function (d) {
                  return d.name;
                })
              })
            })]
          })
        });
      },
      request: ( /*#__PURE__*/function () {
        var _ref7 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee3(params, sort, filter) {
          var startDateRange, startDateFrom, startDateTo, endDateRange, endDateFrom, endDateTo, direction, _direction, _direction2, _direction3, planState, filterParams, res, data;
          return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee3$(_context3) {
            while (1) switch (_context3.prev = _context3.next) {
              case 0:
                console.log('params', params);
                startDateRange = params === null || params === void 0 ? void 0 : params.start_date_range;
                startDateFrom = startDateRange === null || startDateRange === void 0 ? void 0 : startDateRange[0];
                startDateTo = startDateRange === null || startDateRange === void 0 ? void 0 : startDateRange[1];
                params.start_date_from = startDateFrom;
                params.start_date_to = startDateTo;
                endDateRange = params === null || params === void 0 ? void 0 : params.end_date_range;
                endDateFrom = endDateRange === null || endDateRange === void 0 ? void 0 : endDateRange[0];
                endDateTo = endDateRange === null || endDateRange === void 0 ? void 0 : endDateRange[1];
                params.end_date_from = endDateFrom;
                params.end_date_to = endDateTo;
                if (sort !== null && sort !== void 0 && sort.start_date) {
                  params.order_by = "start_date ".concat((sort === null || sort === void 0 ? void 0 : sort.start_date) === 'ascend' ? 'asc' : 'desc', " ");
                } else if (sort !== null && sort !== void 0 && sort.end_date) {
                  params.order_by = "end_date ".concat((sort === null || sort === void 0 ? void 0 : sort.end_date) === 'ascend' ? 'asc' : 'desc', " ");
                } else if (sort !== null && sort !== void 0 && sort.label) {
                  params.order_by = "label ".concat((sort === null || sort === void 0 ? void 0 : sort.label) === 'ascend' ? 'asc' : 'desc', " ");
                } else if (sort !== null && sort !== void 0 && sort.status) {
                  params.order_by = "status ".concat((sort === null || sort === void 0 ? void 0 : sort.status) === 'ascend' ? 'asc' : 'desc', " ");
                } else if (sort !== null && sort !== void 0 && sort.assigned_to) {
                  direction = (sort === null || sort === void 0 ? void 0 : sort.assigned_to) === 'ascend' ? 'asc' : 'desc';
                  params.order_by = "assigned_to ".concat(direction);
                } else if (sort !== null && sort !== void 0 && sort.crop_id) {
                  _direction = (sort === null || sort === void 0 ? void 0 : sort.crop_id) === 'ascend' ? 'asc' : 'desc';
                  params.order_by = "crop_id ".concat(_direction);
                } else if (sort !== null && sort !== void 0 && sort.state_name) {
                  _direction2 = (sort === null || sort === void 0 ? void 0 : sort.state_name) === 'ascend' ? 'asc' : 'desc';
                  params.order_by = "state_name ".concat(_direction2);
                } else if (sort !== null && sort !== void 0 && sort.todo_done) {
                  _direction3 = (sort === null || sort === void 0 ? void 0 : sort.todo_done) === 'ascend' ? 'asc' : 'desc';
                  params.order_by = "todo_done ".concat(_direction3);
                } else {
                  params.order_by = 'start_date desc';
                }
                if (cropId) {
                  params.crop_id = cropId;
                }
                planState = params.pl_state_name;
                filterParams = getFilterTaskAll(params);
                _context3.next = 17;
                return (0,_services_farming_plan__WEBPACK_IMPORTED_MODULE_6__/* .getTaskManagerList */ .UM)(filterParams);
              case 17:
                res = _context3.sent;
                data = res.data;
                return _context3.abrupt("return", {
                  data: data,
                  success: true,
                  total: res.pagination.totalElements
                });
              case 20:
              case "end":
                return _context3.stop();
            }
          }, _callee3);
        }));
        return function (_x, _x2, _x3) {
          return _ref7.apply(this, arguments);
        };
      }()),
      pagination: {
        showSizeChanger: true,
        pageSizeOptions: [10, 20, 50, 100]
      },
      headerTitle: intl.formatMessage({
        id: 'workflowTab.workList'
      }),
      columns: columns,
      rowKey: 'name',
      scroll: {
        x: 'max-content'
      }
    }), openModalCreateNewTask && createNewTaskInModal && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.jsx)(_WorkflowManagement_Create__WEBPACK_IMPORTED_MODULE_12__["default"], {
      mode: "modal",
      open: openModalCreateNewTask,
      onOpenChange: setOpenModalCreateNewTask,
      onCreateSuccess: reloadTable,
      planId: planId,
      cropId: cropId
    })]
  });
};
/* harmony default export */ __webpack_exports__.Z = (TableCropPlanDetail);
var getLikeFilter = function getLikeFilter(params) {
  return "%".concat(params, "%");
};
function getFilterTaskAll(params) {
  var filterArr = [];
  if (params.label) {
    filterArr.push(['iot_farming_plan_task', 'label', 'like', getLikeFilter(params.label)]);
  }
  if (params.start_date_from) {
    filterArr.push(['iot_farming_plan_task', 'start_date', '>=', params.start_date_from]);
  }
  if (params.start_date_to) {
    filterArr.push(['iot_farming_plan_task', 'start_date', '<=', params.start_date_to]);
  }
  if (params.end_date_from) {
    filterArr.push(['iot_farming_plan_task', 'end_date', '>=', params.end_date_from]);
  }
  if (params.end_date_to) {
    filterArr.push(['iot_farming_plan_task', 'end_date', '<=', params.end_date_to]);
  }
  if (params.start_date && params.end_date) {
    var _sort = [new Date(params.start_date), new Date(params.end_date)].sort(function (a, b) {
        return a - b;
      }),
      _sort2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_3___default()(_sort, 2),
      _startDate = _sort2[0],
      _endDate = _sort2[1];
    filterArr.push(['iot_farming_plan_task', 'start_date', '<=', _endDate]);
    filterArr.push(['iot_farming_plan_task', 'end_date', '>=', _startDate]);
  } else {
    if (params.start_date) {
      filterArr.push(['iot_farming_plan_task', 'start_date', '<=', params.start_date]);
    }
    if (params.end_date) {
      filterArr.push(['iot_farming_plan_task', 'end_date', '>=', params.end_date]);
    }
  }
  if (params.status) {
    switch (params.status) {
      case 'Plan':
        filterArr.push(['iot_farming_plan_task', 'status', 'like', 'Plan']);
        break;
      case 'Done':
        filterArr.push(['iot_farming_plan_task', 'status', 'like', 'Done']);
        break;
      case 'In progress':
        filterArr.push(['iot_farming_plan_task', 'status', 'like', 'In progress']);
        break;
      case 'Pending':
        filterArr.push(['iot_farming_plan_task', 'status', 'like', 'Pending']);
        break;
      default:
        break;
    }
  }
  if (params.project_name) {
    filterArr.push(['iot_project', 'label', 'like', getLikeFilter(params.project_name)]);
  }
  if (params.zone_name) {
    filterArr.push(['iot_zone', 'label', 'like', getLikeFilter(params.zone_name)]);
  }
  if (params.state_name) {
    filterArr.push(['iot_farming_plan_state', 'label', 'like', getLikeFilter(params.state_name)]);
  }
  if (params.plan_name) {
    filterArr.push(['iot_farming_plan', 'label', 'like', getLikeFilter(params.plan_name)]);
  }
  if (params.crop_name) {
    filterArr.push(['iot_crop', 'label', 'like', params.crop_name]);
  }
  if (params.crop_id) {
    filterArr.push(['iot_crop', 'name', 'like', params.crop_id]);
  }
  var returnObj = {
    page: params.current,
    size: params.pageSize,
    filters: JSON.stringify(filterArr),
    order_by: params.order_by
  };
  return returnObj;
}//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///96409
`)},59629:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectWithoutProperties_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(13769);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectWithoutProperties_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectWithoutProperties_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(5574);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _assets_img_icons_tree_green_svg__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(43032);
/* harmony import */ var _services_cropManager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(77890);
/* harmony import */ var _services_farming_plan__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(74459);
/* harmony import */ var _services_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(467);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(51042);
/* harmony import */ var _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(34994);
/* harmony import */ var _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(64317);
/* harmony import */ var _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(4894);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(7837);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(42075);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(66309);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(7134);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(83062);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(14726);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(71230);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(15746);
/* harmony import */ var antd_use_styles__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(38513);
/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(27484);
/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(moment__WEBPACK_IMPORTED_MODULE_10__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(67294);
/* harmony import */ var react_router_dom__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(13854);
/* harmony import */ var _WorkflowManagement_Create__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(22864);
/* harmony import */ var _DeleteMultiTaskConfirm__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(85694);
/* harmony import */ var _UpdateStatusMultiTask__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(38790);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(85893);




var _excluded = ["defaultRender"],
  _excluded2 = ["defaultRender"];


















var useStyles = (0,antd_use_styles__WEBPACK_IMPORTED_MODULE_9__/* .createStyles */ .k)(function () {
  return {
    table: {
      '& .ant-pro-table-list-toolbar-left': {
        flex: 'none'
      }
    }
  };
});
var dateRangeFilterKey = 'dateRange';
var TableTaskTemplateTableDetail = function TableTaskTemplateTableDetail(_ref) {
  var cropId = _ref.cropId,
    createNewTaskInModal = _ref.createNewTaskInModal;
  var styles = useStyles();
  var _useSearchParams = (0,react_router_dom__WEBPACK_IMPORTED_MODULE_16__/* .useSearchParams */ .lr)(),
    _useSearchParams2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_3___default()(_useSearchParams, 2),
    searchParams = _useSearchParams2[0],
    setSearchParams = _useSearchParams2[1];
  var farmingPlanState = searchParams.get('pl_state_name');
  var dateRangeFilter = searchParams.get(dateRangeFilterKey);
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_11__.useState)(''),
    _useState2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_3___default()(_useState, 2),
    planId = _useState2[0],
    setPlanId = _useState2[1];
  var _ProForm$useForm = _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_17__/* .ProForm */ .A.useForm(),
    _ProForm$useForm2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_3___default()(_ProForm$useForm, 1),
    formFilter = _ProForm$useForm2[0];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_11__.useState)([]),
    _useState4 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_3___default()(_useState3, 2),
    selectedRowKeys = _useState4[0],
    setSelectedRowKeys = _useState4[1];
  var tableRef = (0,react__WEBPACK_IMPORTED_MODULE_11__.useRef)();
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_11__.useState)({}),
    _useState6 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_3___default()(_useState5, 2),
    enumCropValues = _useState6[0],
    setEnumCropValues = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_11__.useState)({}),
    _useState8 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_3___default()(_useState7, 2),
    enumStateValues = _useState8[0],
    setEnumStateValues = _useState8[1];
  var intl = (0,_umijs_max__WEBPACK_IMPORTED_MODULE_8__.useIntl)();
  (0,react__WEBPACK_IMPORTED_MODULE_11__.useEffect)(function () {
    // G\u1ECDi API \u0111\u1EC3 l\u1EA5y danh s\xE1ch gi\xE1 tr\u1ECB cho valueEnum
    var fetchData = /*#__PURE__*/function () {
      var _ref2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee() {
        var response, data, enumCropObject, filters, plan, _planId, responseState, dataState, enumStateObject;
        return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee$(_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              _context.prev = 0;
              _context.next = 3;
              return (0,_services_cropManager__WEBPACK_IMPORTED_MODULE_5__/* .getTemplateCropList */ .LY)({
                page: 1,
                size: 1000
              });
            case 3:
              response = _context.sent;
              data = response.data;
              enumCropObject = {};
              data.forEach(function (item) {
                enumCropObject[item.name] = item.label;
              });
              setEnumCropValues(enumCropObject);
              //get plan_id
              filters = [['iot_farming_plan', 'crop', 'like', cropId]];
              _context.next = 11;
              return (0,_services_farming_plan__WEBPACK_IMPORTED_MODULE_6__/* .getFarmingPlan */ .j1)('', filters);
            case 11:
              plan = _context.sent;
              _planId = plan.data.name;
              setPlanId(_planId);
              //similar for state
              _context.next = 16;
              return (0,_services_farming_plan__WEBPACK_IMPORTED_MODULE_6__/* .getFarmingPlanState */ .jY)({
                page: 1,
                size: 1000
              });
            case 16:
              responseState = _context.sent;
              dataState = responseState.data;
              enumStateObject = {};
              dataState.forEach(function (item) {
                enumStateObject[item.label] = item.label;
              });
              setEnumStateValues(enumStateObject);
              _context.next = 26;
              break;
            case 23:
              _context.prev = 23;
              _context.t0 = _context["catch"](0);
              console.error('Error fetching enum values:', _context.t0);
            case 26:
            case "end":
              return _context.stop();
          }
        }, _callee, null, [[0, 23]]);
      }));
      return function fetchData() {
        return _ref2.apply(this, arguments);
      };
    }();
    fetchData();
  }, []); // useEffect ch\u1EC9 ch\u1EA1y m\u1ED9t l\u1EA7n khi component \u0111\u01B0\u1EE3c mount

  var columns = [{
    title: intl.formatMessage({
      id: 'common.index'
    }),
    renderText: function renderText(text, record, index, action) {
      return index + 1;
    },
    search: false
  }, {
    title: intl.formatMessage({
      id: 'workflowTab.workName'
    }),
    dataIndex: 'label',
    renderText: function renderText(_text, record, index, action) {
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_8__.Link, {
        to: "/farming-management/seasonal-management/detail/".concat(cropId, "/workflow-management/detail/").concat(record.name),
        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_18__/* ["default"] */ .Z, {
          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.jsx)("img", {
            src: _assets_img_icons_tree_green_svg__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z
          }), " ", _text]
        })
      });
    },
    sorter: true
  }, {
    title: intl.formatMessage({
      id: 'common.status'
    }),
    dataIndex: 'status',
    valueEnum: {
      Plan: {
        text: 'K\u1EBF ho\u1EA1ch',
        status: 'Default'
      },
      Done: {
        text: '\u0110\xE3 ho\xE0n th\xE0nh',
        status: 'Success'
      },
      'In progress': {
        text: '\u0110ang th\u1EF1c hi\u1EC7n',
        status: 'Warning'
      },
      Pending: {
        text: 'Tr\xEC ho\xE3n',
        status: 'Default'
      }
    },
    render: function render(dom, entity, index) {
      switch (entity.status) {
        case 'Plan':
          return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.jsx)(antd__WEBPACK_IMPORTED_MODULE_19__/* ["default"] */ .Z, {
            color: "cyan",
            children: "K\\u1EBF ho\\u1EA1ch"
          });
        case 'Done':
          return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.jsx)(antd__WEBPACK_IMPORTED_MODULE_19__/* ["default"] */ .Z, {
            color: "success",
            children: "\\u0110\\xE3 ho\\xE0n th\\xE0nh"
          });
        case 'In progress':
          return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.jsx)(antd__WEBPACK_IMPORTED_MODULE_19__/* ["default"] */ .Z, {
            color: "warning",
            children: "\\u0110ang th\\u1EF1c hi\\u1EC7n"
          });
        case 'Pending':
          return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.jsx)(antd__WEBPACK_IMPORTED_MODULE_19__/* ["default"] */ .Z, {
            color: "default",
            children: "Tr\\xEC ho\\xE3n"
          });
        default:
          return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.jsx)(antd__WEBPACK_IMPORTED_MODULE_19__/* ["default"] */ .Z, {});
      }
    },
    sorter: true
  }, {
    title: intl.formatMessage({
      id: 'common.start_date'
    }),
    dataIndex: 'start_date',
    render: function render(dom, entity) {
      return moment__WEBPACK_IMPORTED_MODULE_10___default()(entity.start_date).format('YYYY-MM-DD HH:mm'); // Format date to 'YYYY-MM-DD HH:mm'
    },
    sortDirections: ['ascend', 'descend', 'ascend'],
    sorter: true,
    defaultSortOrder: 'descend',
    valueType: 'dateTime'
  }, {
    title: intl.formatMessage({
      id: 'common.end_date'
    }),
    dataIndex: 'end_date',
    render: function render(dom, entity) {
      return moment__WEBPACK_IMPORTED_MODULE_10___default()(entity.end_date).format('YYYY-MM-DD HH:mm'); // Format date to 'YYYY-MM-DD HH:mm'
    },
    sortDirections: ['ascend', 'descend', 'ascend'],
    sorter: true,
    valueType: 'dateTime'
  }, {
    title: intl.formatMessage({
      id: 'workflowTab.executor'
    }),
    dataIndex: 'assigned_to',
    render: function render(value, record) {
      var _record$assigned_to_i;
      var info = (_record$assigned_to_i = record.assigned_to_info) === null || _record$assigned_to_i === void 0 ? void 0 : _record$assigned_to_i[0];
      if (!info) {
        return null;
      }
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_18__/* ["default"] */ .Z, {
        children: [info.user_avatar && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.jsx)(antd__WEBPACK_IMPORTED_MODULE_20__/* ["default"] */ .C, {
          size: 'small',
          src: (0,_services_utils__WEBPACK_IMPORTED_MODULE_7__/* .getFileUrlV2 */ .mT)({
            src: info.user_avatar
          })
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.jsx)("span", {
          children: "".concat(info.first_name || '', " ").concat(info.last_name || '', " ")
        })]
      });
    },
    search: false,
    sorter: true
  }, {
    title: 'Th\xE0nh vi\xEAn li\xEAn quan',
    hideInTable: true,
    dataIndex: 'involve_in_users',
    render: function render(value, record) {
      try {
        var involveInArr = record.involve_in_users;
        var userNames = involveInArr.map(function (data) {
          return "".concat(data.first_name, " ").concat(data.last_name);
        });
        return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.Fragment, {
          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.jsx)(antd__WEBPACK_IMPORTED_MODULE_20__/* ["default"].Group */ .C.Group, {
            children: involveInArr.map(function (data, index) {
              return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.jsx)(antd__WEBPACK_IMPORTED_MODULE_21__/* ["default"] */ .Z, {
                title: "".concat(data.full_name ? data.full_name : "".concat(data.last_name || '', " ").concat(data.first_name || '')),
                placement: "top",
                children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.jsx)(antd__WEBPACK_IMPORTED_MODULE_20__/* ["default"] */ .C, {
                  style: {
                    backgroundColor: '#87d068'
                  },
                  children: data.first_name
                })
              }, 'avt' + index);
            })
          })
        });
      } catch (error) {
        return null;
      }
    },
    search: false,
    width: 100
  }, {
    title: 'V\u1EE5 m\xF9a',
    hideInTable: true,
    dataIndex: 'crop_id',
    valueEnum: enumCropValues,
    renderFormItem: function renderFormItem(_, _ref3) {
      var defaultRender = _ref3.defaultRender,
        rest = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectWithoutProperties_js__WEBPACK_IMPORTED_MODULE_0___default()(_ref3, _excluded);
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.jsx)(_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_22__/* ["default"] */ .Z, {
        options: rest.options,
        showSearch: true
      });
    },
    search: cropId ? false : undefined,
    sorter: true
  }, {
    title: 'Giai \u0111o\u1EA1n',
    hideInTable: true,
    hideInSearch: true,
    dataIndex: 'state_name',
    valueEnum: enumStateValues,
    sorter: true,
    renderFormItem: function renderFormItem(_, _ref4) {
      var defaultRender = _ref4.defaultRender,
        rest = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectWithoutProperties_js__WEBPACK_IMPORTED_MODULE_0___default()(_ref4, _excluded2);
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.jsx)(_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_22__/* ["default"] */ .Z, {
        options: rest.options,
        showSearch: true
      });
    }
  }, {
    title: intl.formatMessage({
      id: 'common.completion_level'
    }),
    dataIndex: 'todo_done',
    search: false,
    sorter: true,
    render: function render(value, record) {
      return "".concat(record.todo_done || 0, "/").concat(record.todo_total || 0);
    }
  }];
  var actionRef = (0,react__WEBPACK_IMPORTED_MODULE_11__.useRef)();
  // reload when form filter change
  (0,react__WEBPACK_IMPORTED_MODULE_11__.useEffect)(function () {
    if (actionRef.current) {
      actionRef.current.reload();
    }
  }, [dateRangeFilter]);
  var reloadTable = /*#__PURE__*/function () {
    var _ref5 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee2() {
      var _actionRef$current;
      return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee2$(_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            (_actionRef$current = actionRef.current) === null || _actionRef$current === void 0 || _actionRef$current.reload();
            setSelectedRowKeys([]); // Clear the selection
          case 2:
          case "end":
            return _context2.stop();
        }
      }, _callee2);
    }));
    return function reloadTable() {
      return _ref5.apply(this, arguments);
    };
  }();
  // modal create new task
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_11__.useState)(false),
    _useState10 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_3___default()(_useState9, 2),
    openModalCreateNewTask = _useState10[0],
    setOpenModalCreateNewTask = _useState10[1];
  var access = (0,_umijs_max__WEBPACK_IMPORTED_MODULE_8__.useAccess)();
  var canCreate = access.canCreateInWorkFlowManagement();
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.Fragment, {
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.jsx)(_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_23__/* ["default"] */ .Z, {
      actionRef: actionRef,
      className: styles.table,
      form: {
        syncToUrl: true,
        defaultCollapsed: false,
        initialValues: {
          pl_state_name: farmingPlanState
        }
      },
      search: {
        labelWidth: 'auto',
        span: {
          xs: 24,
          sm: 24,
          md: 12,
          lg: 12,
          xl: 12,
          xxl: 12
        }
      },
      toolbar: {
        filter: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.jsx)(_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_17__/* .ProForm */ .A, {
          form: formFilter,
          name: "crop-detail:table-filter",
          onValuesChange: function onValuesChange(changeValue) {
            if (changeValue.dateRange) {
              searchParams.set(dateRangeFilterKey, JSON.stringify(changeValue.dateRange));
            } else {
              searchParams["delete"](dateRangeFilterKey);
            }
            setSearchParams(searchParams);
          },
          layout: "inline",
          submitter: false,
          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.jsx)(antd__WEBPACK_IMPORTED_MODULE_18__/* ["default"] */ .Z, {
            size: 'large',
            children: canCreate && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.jsx)(antd__WEBPACK_IMPORTED_MODULE_24__/* ["default"] */ .ZP, {
              icon: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_25__/* ["default"] */ .Z, {}),
              onClick: function onClick() {
                if (createNewTaskInModal) {
                  setOpenModalCreateNewTask(true);
                  return;
                }
                var urlParams = new URLSearchParams(window.location.search);
                var currentFarmingPlanState = urlParams.get('pl_state_name');
                _umijs_max__WEBPACK_IMPORTED_MODULE_8__.history.push("/farming-management/workflow-management/create".concat(currentFarmingPlanState ? "?farming_plan_state=".concat(currentFarmingPlanState) : ''));
              },
              children: intl.formatMessage({
                id: 'workflowTab.createWork'
              })
            })
          })
        })
      },
      rowSelection: {
        selectedRowKeys: selectedRowKeys,
        onChange: function onChange(selectedKeys) {
          return setSelectedRowKeys(selectedKeys);
        }
      },
      tableAlertOptionRender: function tableAlertOptionRender(_ref6) {
        var selectedRowKeys = _ref6.selectedRowKeys,
          selectedRows = _ref6.selectedRows,
          onCleanSelected = _ref6.onCleanSelected;
        return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.Fragment, {
          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_26__/* ["default"] */ .Z, {
            justify: "space-between",
            gutter: 16,
            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.jsx)(antd__WEBPACK_IMPORTED_MODULE_27__/* ["default"] */ .Z, {
              className: "gutter-row",
              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.jsx)(_UpdateStatusMultiTask__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .Z, {
                refreshFnc: reloadTable,
                tasks_id: selectedRows.map(function (d) {
                  return d.name;
                })
              })
            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.jsx)(antd__WEBPACK_IMPORTED_MODULE_27__/* ["default"] */ .Z, {
              className: "gutter-row",
              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.jsx)(_DeleteMultiTaskConfirm__WEBPACK_IMPORTED_MODULE_13__/* ["default"] */ .Z, {
                refreshFnc: reloadTable,
                tasks_id: selectedRows.map(function (d) {
                  return d.name;
                })
              })
            })]
          })
        });
      },
      request: ( /*#__PURE__*/function () {
        var _ref7 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee3(params, sort, filter) {
          var startDateRange, startDateFrom, startDateTo, endDateRange, endDateFrom, endDateTo, direction, _direction, _direction2, _direction3, planState, filterParams, res, data;
          return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee3$(_context3) {
            while (1) switch (_context3.prev = _context3.next) {
              case 0:
                console.log('params', params);
                startDateRange = params === null || params === void 0 ? void 0 : params.start_date_range;
                startDateFrom = startDateRange === null || startDateRange === void 0 ? void 0 : startDateRange[0];
                startDateTo = startDateRange === null || startDateRange === void 0 ? void 0 : startDateRange[1];
                params.start_date_from = startDateFrom;
                params.start_date_to = startDateTo;
                endDateRange = params === null || params === void 0 ? void 0 : params.end_date_range;
                endDateFrom = endDateRange === null || endDateRange === void 0 ? void 0 : endDateRange[0];
                endDateTo = endDateRange === null || endDateRange === void 0 ? void 0 : endDateRange[1];
                params.end_date_from = endDateFrom;
                params.end_date_to = endDateTo;
                if (sort !== null && sort !== void 0 && sort.start_date) {
                  params.order_by = "start_date ".concat((sort === null || sort === void 0 ? void 0 : sort.start_date) === 'ascend' ? 'asc' : 'desc', " ");
                } else if (sort !== null && sort !== void 0 && sort.end_date) {
                  params.order_by = "end_date ".concat((sort === null || sort === void 0 ? void 0 : sort.end_date) === 'ascend' ? 'asc' : 'desc', " ");
                } else if (sort !== null && sort !== void 0 && sort.label) {
                  params.order_by = "label ".concat((sort === null || sort === void 0 ? void 0 : sort.label) === 'ascend' ? 'asc' : 'desc', " ");
                } else if (sort !== null && sort !== void 0 && sort.status) {
                  params.order_by = "status ".concat((sort === null || sort === void 0 ? void 0 : sort.status) === 'ascend' ? 'asc' : 'desc', " ");
                } else if (sort !== null && sort !== void 0 && sort.assigned_to) {
                  direction = (sort === null || sort === void 0 ? void 0 : sort.assigned_to) === 'ascend' ? 'asc' : 'desc';
                  params.order_by = "assigned_to ".concat(direction);
                } else if (sort !== null && sort !== void 0 && sort.crop_id) {
                  _direction = (sort === null || sort === void 0 ? void 0 : sort.crop_id) === 'ascend' ? 'asc' : 'desc';
                  params.order_by = "crop_id ".concat(_direction);
                } else if (sort !== null && sort !== void 0 && sort.state_name) {
                  _direction2 = (sort === null || sort === void 0 ? void 0 : sort.state_name) === 'ascend' ? 'asc' : 'desc';
                  params.order_by = "state_name ".concat(_direction2);
                } else if (sort !== null && sort !== void 0 && sort.todo_done) {
                  _direction3 = (sort === null || sort === void 0 ? void 0 : sort.todo_done) === 'ascend' ? 'asc' : 'desc';
                  params.order_by = "todo_done ".concat(_direction3);
                } else {
                  params.order_by = 'start_date desc';
                }
                if (cropId) {
                  params.crop_id = cropId;
                }
                planState = params.pl_state_name;
                filterParams = getFilterTaskAll(params);
                _context3.next = 17;
                return (0,_services_farming_plan__WEBPACK_IMPORTED_MODULE_6__/* .getTemplateTaskManagerList */ .dQ)(filterParams);
              case 17:
                res = _context3.sent;
                data = res.data;
                return _context3.abrupt("return", {
                  data: data,
                  success: true,
                  total: res.pagination.totalElements
                });
              case 20:
              case "end":
                return _context3.stop();
            }
          }, _callee3);
        }));
        return function (_x, _x2, _x3) {
          return _ref7.apply(this, arguments);
        };
      }()),
      pagination: {
        showSizeChanger: true,
        pageSizeOptions: [10, 20, 50, 100]
      },
      headerTitle: intl.formatMessage({
        id: 'workflowTab.workList'
      }),
      columns: columns,
      rowKey: 'name',
      scroll: {
        x: 'max-content'
      }
    }), openModalCreateNewTask && createNewTaskInModal && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__.jsx)(_WorkflowManagement_Create__WEBPACK_IMPORTED_MODULE_12__["default"], {
      mode: "modal",
      open: openModalCreateNewTask,
      onOpenChange: setOpenModalCreateNewTask,
      onCreateSuccess: reloadTable,
      planId: planId,
      cropId: cropId,
      isTemplateTask: true
    })]
  });
};
/* harmony default export */ __webpack_exports__.Z = (TableTaskTemplateTableDetail);
var getLikeFilter = function getLikeFilter(params) {
  return "%".concat(params, "%");
};
function getFilterTaskAll(params) {
  var filterArr = [];
  if (params.label) {
    filterArr.push(['iot_farming_plan_task', 'label', 'like', getLikeFilter(params.label)]);
  }
  if (params.start_date_from) {
    filterArr.push(['iot_farming_plan_task', 'start_date', '>=', params.start_date_from]);
  }
  if (params.start_date_to) {
    filterArr.push(['iot_farming_plan_task', 'start_date', '<=', params.start_date_to]);
  }
  if (params.end_date_from) {
    filterArr.push(['iot_farming_plan_task', 'end_date', '>=', params.end_date_from]);
  }
  if (params.end_date_to) {
    filterArr.push(['iot_farming_plan_task', 'end_date', '<=', params.end_date_to]);
  }
  if (params.start_date && params.end_date) {
    var _sort = [new Date(params.start_date), new Date(params.end_date)].sort(function (a, b) {
        return a - b;
      }),
      _sort2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_3___default()(_sort, 2),
      _startDate = _sort2[0],
      _endDate = _sort2[1];
    filterArr.push(['iot_farming_plan_task', 'start_date', '<=', _endDate]);
    filterArr.push(['iot_farming_plan_task', 'end_date', '>=', _startDate]);
  } else {
    if (params.start_date) {
      filterArr.push(['iot_farming_plan_task', 'start_date', '<=', params.start_date]);
    }
    if (params.end_date) {
      filterArr.push(['iot_farming_plan_task', 'end_date', '>=', params.end_date]);
    }
  }
  if (params.status) {
    switch (params.status) {
      case 'Plan':
        filterArr.push(['iot_farming_plan_task', 'status', 'like', 'Plan']);
        break;
      case 'Done':
        filterArr.push(['iot_farming_plan_task', 'status', 'like', 'Done']);
        break;
      case 'In progress':
        filterArr.push(['iot_farming_plan_task', 'status', 'like', 'In progress']);
        break;
      case 'Pending':
        filterArr.push(['iot_farming_plan_task', 'status', 'like', 'Pending']);
        break;
      default:
        break;
    }
  }
  if (params.project_name) {
    filterArr.push(['iot_project', 'label', 'like', getLikeFilter(params.project_name)]);
  }
  if (params.zone_name) {
    filterArr.push(['iot_zone', 'label', 'like', getLikeFilter(params.zone_name)]);
  }
  if (params.state_name) {
    filterArr.push(['iot_farming_plan_state', 'label', 'like', getLikeFilter(params.state_name)]);
  }
  if (params.plan_name) {
    filterArr.push(['iot_farming_plan', 'label', 'like', getLikeFilter(params.plan_name)]);
  }
  if (params.crop_name) {
    filterArr.push(['iot_crop', 'label', 'like', params.crop_name]);
  }
  if (params.crop_id) {
    filterArr.push(['iot_crop', 'name', 'like', params.crop_id]);
  }
  var returnObj = {
    page: params.current,
    size: params.pageSize,
    filters: JSON.stringify(filterArr),
    order_by: params.order_by
  };
  return returnObj;
}//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///59629
`)},38790:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _services_TaskAndTodo__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(90705);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(45360);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(83863);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(85893);





var UpdateStatusMultiTask = function UpdateStatusMultiTask(params) {
  var onChange = /*#__PURE__*/function () {
    var _ref = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee(statusValue) {
      var taskStatusArray, updateStatusMultiTask;
      return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _context.prev = 0;
            taskStatusArray = params.tasks_id.map(function (task_id) {
              return {
                name: task_id,
                status: statusValue
              };
            });
            console.log('taskStatusArray', taskStatusArray);
            _context.next = 5;
            return (0,_services_TaskAndTodo__WEBPACK_IMPORTED_MODULE_2__/* .updateTaskStatusArray */ .Rb)({
              tasks: taskStatusArray
            });
          case 5:
            updateStatusMultiTask = _context.sent;
            antd__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .ZP.success('C\u1EADp nh\u1EADt tr\u1EA1ng th\xE1i th\xE0nh c\xF4ng');
            _context.next = 12;
            break;
          case 9:
            _context.prev = 9;
            _context.t0 = _context["catch"](0);
            antd__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .ZP.error(_context.t0.toString());
          case 12:
            _context.prev = 12;
            if (!params.refreshFnc) {
              _context.next = 16;
              break;
            }
            _context.next = 16;
            return params.refreshFnc();
          case 16:
            return _context.finish(12);
          case 17:
          case "end":
            return _context.stop();
        }
      }, _callee, null, [[0, 9, 12, 17]]);
    }));
    return function onChange(_x) {
      return _ref.apply(this, arguments);
    };
  }();
  var onSearch = function onSearch(value) {
    console.log('search:', value);
  };

  // Filter \`option.label\` match the user type \`input\`
  var filterOption = function filterOption(input, option) {
    var _option$label;
    return ((_option$label = option === null || option === void 0 ? void 0 : option.label) !== null && _option$label !== void 0 ? _option$label : '').toLowerCase().includes(input.toLowerCase());
  };
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(antd__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z, {
    showSearch: true,
    placeholder: "C\\u1EADp nh\\u1EADt tr\\u1EA1ng th\\xE1i",
    style: {
      width: 120
    },
    optionFilterProp: "children",
    onChange: onChange,
    onSearch: onSearch,
    filterOption: filterOption,
    options: [{
      label: 'L\xEAn k\u1EBF ho\u1EA1ch',
      value: 'Plan'
    }, {
      label: '\u0110ang x\u1EED l\xFD',
      value: 'In progress'
    }, {
      label: 'Ho\xE0n t\u1EA5t',
      value: 'Done'
    }, {
      label: 'Tr\xEC ho\xE3n',
      value: 'Pending'
    }]
  });
};
/* harmony default export */ __webpack_exports__.Z = (UpdateStatusMultiTask);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///38790
`)},90705:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   F6: function() { return /* binding */ removeTask; },
/* harmony export */   IE: function() { return /* binding */ getAllTodo; },
/* harmony export */   Qj: function() { return /* binding */ getAllTaskProduction; },
/* harmony export */   Rb: function() { return /* binding */ updateTaskStatusArray; },
/* harmony export */   i7: function() { return /* binding */ getAllTaskItemUsed; }
/* harmony export */ });
/* unused harmony exports getSingleTask, getAssignedTask, getAllTaskWorkTime */
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _common_contanst_constanst__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(86604);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(7837);
/* harmony import */ var _sscript__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(39750);
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(467);






function getSingleTask(_x) {
  return _getSingleTask.apply(this, arguments);
}
function _getSingleTask() {
  _getSingleTask = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee(task_id) {
    var result;
    return _regeneratorRuntime().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.next = 2;
          return sscriptGeneralList({
            doc_name: 'iot_farming_plan_task',
            filters: [['iot_farming_plan_task', 'name', 'like', task_id]],
            page: 1,
            size: DEFAULT_PAGE_SIZE_ALL,
            fields: ['*'],
            order_by: ''
          });
        case 2:
          result = _context.sent;
          return _context.abrupt("return", result);
        case 4:
        case "end":
          return _context.stop();
      }
    }, _callee);
  }));
  return _getSingleTask.apply(this, arguments);
}
function getAssignedTask(_x2) {
  return _getAssignedTask.apply(this, arguments);
}
function _getAssignedTask() {
  _getAssignedTask = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee2(task_id) {
    var result;
    return _regeneratorRuntime().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          _context2.next = 2;
          return sscriptGeneralList({
            doc_name: 'iot_assign_user',
            filters: [['iot_assign_user', 'task', 'like', task_id]],
            page: 1,
            size: DEFAULT_PAGE_SIZE_ALL,
            fields: ['*'],
            order_by: ''
          });
        case 2:
          result = _context2.sent;
          return _context2.abrupt("return", result);
        case 4:
        case "end":
          return _context2.stop();
      }
    }, _callee2);
  }));
  return _getAssignedTask.apply(this, arguments);
}
function getAllTodo(_x3) {
  return _getAllTodo.apply(this, arguments);
}
function _getAllTodo() {
  _getAllTodo = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee3(task_id) {
    var result;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee3$(_context3) {
      while (1) switch (_context3.prev = _context3.next) {
        case 0:
          _context3.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_5__/* .generateAPIPath */ .rH)("api/v2/farmingPlan/todo"), {
            method: 'GET',
            params: {
              filters: JSON.stringify([['iot_todo', 'farming_plan_task', 'like', task_id]]),
              page: 1,
              size: _common_contanst_constanst__WEBPACK_IMPORTED_MODULE_2__/* .DEFAULT_PAGE_SIZE_ALL */ .YY,
              fields: ['*'],
              order_by: 'creation ASC'
            }
          });
        case 2:
          result = _context3.sent;
          return _context3.abrupt("return", result.result);
        case 4:
        case "end":
          return _context3.stop();
      }
    }, _callee3);
  }));
  return _getAllTodo.apply(this, arguments);
}
function getAllTaskItemUsed(_x4) {
  return _getAllTaskItemUsed.apply(this, arguments);
}
function _getAllTaskItemUsed() {
  _getAllTaskItemUsed = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee4(task_id) {
    var result;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee4$(_context4) {
      while (1) switch (_context4.prev = _context4.next) {
        case 0:
          _context4.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_5__/* .generateAPIPath */ .rH)("api/v2/taskItem"), {
            method: 'GET',
            params: {
              filters: JSON.stringify([['iot_warehouse_item_task_used', 'task_id', 'like', task_id]]),
              page: 1,
              size: _common_contanst_constanst__WEBPACK_IMPORTED_MODULE_2__/* .DEFAULT_PAGE_SIZE_ALL */ .YY,
              fields: ['*'],
              order_by: 'creation ASC'
            }
          });
        case 2:
          result = _context4.sent;
          return _context4.abrupt("return", result.result);
        case 4:
        case "end":
          return _context4.stop();
      }
    }, _callee4);
  }));
  return _getAllTaskItemUsed.apply(this, arguments);
}
function getAllTaskProduction(_x5) {
  return _getAllTaskProduction.apply(this, arguments);
}
function _getAllTaskProduction() {
  _getAllTaskProduction = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee5(task_id) {
    var result;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee5$(_context5) {
      while (1) switch (_context5.prev = _context5.next) {
        case 0:
          _context5.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_5__/* .generateAPIPath */ .rH)("api/v2/taskProduction"), {
            method: 'GET',
            params: {
              filters: JSON.stringify([['iot_production_quantity', 'task_id', 'like', task_id]]),
              page: 1,
              size: _common_contanst_constanst__WEBPACK_IMPORTED_MODULE_2__/* .DEFAULT_PAGE_SIZE_ALL */ .YY,
              fields: ['*'],
              order_by: 'creation ASC'
            }
          });
        case 2:
          result = _context5.sent;
          return _context5.abrupt("return", result.result);
        case 4:
        case "end":
          return _context5.stop();
      }
    }, _callee5);
  }));
  return _getAllTaskProduction.apply(this, arguments);
}
function getAllTaskWorkTime(_x6) {
  return _getAllTaskWorkTime.apply(this, arguments);
}
function _getAllTaskWorkTime() {
  _getAllTaskWorkTime = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee6(task_id) {
    var result;
    return _regeneratorRuntime().wrap(function _callee6$(_context6) {
      while (1) switch (_context6.prev = _context6.next) {
        case 0:
          _context6.next = 2;
          return sscriptGeneralList({
            doc_name: 'iot_farming_plan_task_worksheet',
            filters: [['iot_farming_plan_task_worksheet', 'task_id', 'like', task_id]],
            page: 1,
            size: DEFAULT_PAGE_SIZE_ALL,
            fields: ['*'],
            order_by: 'creation asc'
          });
        case 2:
          result = _context6.sent;
          return _context6.abrupt("return", result);
        case 4:
        case "end":
          return _context6.stop();
      }
    }, _callee6);
  }));
  return _getAllTaskWorkTime.apply(this, arguments);
}
function removeTask(_x7) {
  return _removeTask.apply(this, arguments);
}
function _removeTask() {
  _removeTask = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee7(task_id) {
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee7$(_context7) {
      while (1) switch (_context7.prev = _context7.next) {
        case 0:
          return _context7.abrupt("return", (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_5__/* .generateAPIPath */ .rH)('api/v2/farmingPlan/task/all?name=' + task_id), {
            method: 'DELETE'
          }));
        case 1:
        case "end":
          return _context7.stop();
      }
    }, _callee7);
  }));
  return _removeTask.apply(this, arguments);
}
function updateTaskStatusArray(_x8) {
  return _updateTaskStatusArray.apply(this, arguments);
}
function _updateTaskStatusArray() {
  _updateTaskStatusArray = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee8(taskStatusArray) {
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee8$(_context8) {
      while (1) switch (_context8.prev = _context8.next) {
        case 0:
          _context8.prev = 0;
          return _context8.abrupt("return", (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_5__/* .generateAPIPath */ .rH)('api/v2/farmingPlan/task/array/status'), {
            method: 'PUT',
            data: taskStatusArray
          }));
        case 4:
          _context8.prev = 4;
          _context8.t0 = _context8["catch"](0);
          throw _context8.t0;
        case 7:
        case "end":
          return _context8.stop();
      }
    }, _callee8, null, [[0, 4]]);
  }));
  return _updateTaskStatusArray.apply(this, arguments);
}//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///90705
`)}}]);
