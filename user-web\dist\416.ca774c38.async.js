"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[416],{19676:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(97857);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(19632);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(5574);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_createForOfIteratorHelper_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(64599);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_createForOfIteratorHelper_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_createForOfIteratorHelper_js__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(7837);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(96365);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(25514);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(71230);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(15746);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(66593);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(67294);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(85893);









var Search = antd__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Z.Search;
var Title = antd__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .Z.Title;
var getParentKey = function getParentKey(key, tree) {
  var _iterator = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_createForOfIteratorHelper_js__WEBPACK_IMPORTED_MODULE_3___default()(tree),
    _step;
  try {
    for (_iterator.s(); !(_step = _iterator.n()).done;) {
      var node = _step.value;
      if (node.children) {
        if (node.children.some(function (item) {
          return item.key === key;
        })) {
          return node.key;
        }
        var foundKey = getParentKey(key, node.children);
        if (foundKey) {
          return foundKey;
        }
      }
    }
  } catch (err) {
    _iterator.e(err);
  } finally {
    _iterator.f();
  }
  return undefined;
};
var SliceSearchableTreeSelect = function SliceSearchableTreeSelect(_ref) {
  var defaultData = _ref.defaultData,
    onCheck = _ref.onCheck;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([]),
    _useState2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2___default()(_useState, 2),
    expandedKeys = _useState2[0],
    setExpandedKeys = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(''),
    _useState4 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2___default()(_useState3, 2),
    searchValue = _useState4[0],
    setSearchValue = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(true),
    _useState6 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2___default()(_useState5, 2),
    autoExpandParent = _useState6[0],
    setAutoExpandParent = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([]),
    _useState8 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2___default()(_useState7, 2),
    dataList = _useState8[0],
    setDataList = _useState8[1];
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([]),
    _useState10 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2___default()(_useState9, 2),
    checkedKeysWithBOM = _useState10[0],
    setCheckedKeysWithBOM = _useState10[1];
  var _useState11 = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([]),
    _useState12 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2___default()(_useState11, 2),
    checkedKeysWithoutBOM = _useState12[0],
    setCheckedKeysWithoutBOM = _useState12[1];
  var onExpand = function onExpand(newExpandedKeys) {
    setExpandedKeys(newExpandedKeys);
    setAutoExpandParent(false);
  };
  var handleOnCheckWithBOM = function handleOnCheckWithBOM(checkedKeys) {
    setCheckedKeysWithBOM(checkedKeys);
    onCheck(checkedKeys, checkedKeysWithoutBOM);
  };
  var handleOnCheckWithoutBOM = function handleOnCheckWithoutBOM(checkedKeys) {
    setCheckedKeysWithoutBOM(checkedKeys);
    onCheck(checkedKeysWithBOM, checkedKeys);
  };
  (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(function () {
    var generateList = function generateList(data) {
      var newDataList = [];
      var _iterator2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_createForOfIteratorHelper_js__WEBPACK_IMPORTED_MODULE_3___default()(data),
        _step2;
      try {
        for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {
          var node = _step2.value;
          newDataList.push({
            key: node.key,
            title: node.title,
            normalized_title: node.normalized_title
          });
          if (node.children) {
            newDataList.push.apply(newDataList, D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_1___default()(generateList(node.children)));
          }
        }
      } catch (err) {
        _iterator2.e(err);
      } finally {
        _iterator2.f();
      }
      return newDataList;
    };
    setDataList(generateList(defaultData));
  }, [defaultData]);
  var onChange = function onChange(e) {
    var value = e.target.value;
    var normalized_value = value.toLowerCase().normalize('NFD').replace(/[\\u0300-\\u036f]/g, '');
    var newExpandedKeys = dataList.map(function (item) {
      if (item.normalized_title.includes(normalized_value)) {
        return getParentKey(item.key, defaultData);
      }
      return null;
    }).filter(function (item, i, self) {
      return !!item && self.indexOf(item) === i;
    });
    setExpandedKeys(newExpandedKeys);
    setSearchValue(value);
    setAutoExpandParent(true);
  };
  var loop = function loop(data) {
    return data.map(function (item) {
      var normalizedSearchValue = searchValue.toLowerCase().normalize('NFD').replace(/[\\u0300-\\u036f]/g, '');
      var index = item.normalized_title.indexOf(normalizedSearchValue);
      var beforeStr = item.title.substring(0, index);
      var str = item.title.substring(index, index + searchValue.length);
      var afterStr = item.title.slice(index + searchValue.length);
      var titleText = item.fullObject && item.fullObject.bom && item.fullObject.bom.length > 0 ? "".concat(item.title) : item.title;
      var title = searchValue === '' ? /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)("span", {
        children: titleText
      }) : index > -1 ? /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxs)("span", {
        children: [beforeStr, /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)("span", {
          style: {
            color: 'white',
            backgroundColor: 'green'
          },
          children: str
        }), afterStr]
      }) : /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)("span", {
        children: titleText
      });
      if (item.children) {
        return {
          title: title,
          key: item.key,
          children: loop(item.children)
        };
      }
      return {
        title: title,
        key: item.key
      };
    });
  };
  var _useMemo = (0,react__WEBPACK_IMPORTED_MODULE_5__.useMemo)(function () {
      var withBOM = [];
      var withoutBOM = [];
      defaultData.forEach(function (group) {
        var _group$children, _group$children2;
        var childrenWithBOM = (_group$children = group.children) === null || _group$children === void 0 ? void 0 : _group$children.filter(function (item) {
          var _item$fullObject;
          return ((_item$fullObject = item.fullObject) === null || _item$fullObject === void 0 || (_item$fullObject = _item$fullObject.bom) === null || _item$fullObject === void 0 ? void 0 : _item$fullObject.length) > 0;
        });
        var childrenWithoutBOM = (_group$children2 = group.children) === null || _group$children2 === void 0 ? void 0 : _group$children2.filter(function (item) {
          var _item$fullObject2;
          return !((_item$fullObject2 = item.fullObject) !== null && _item$fullObject2 !== void 0 && _item$fullObject2.bom) || item.fullObject.bom.length === 0;
        });
        if (childrenWithBOM && childrenWithBOM.length > 0) {
          withBOM.push(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default()({}, group), {}, {
            children: childrenWithBOM
          }));
        }
        if (childrenWithoutBOM && childrenWithoutBOM.length > 0) {
          withoutBOM.push(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default()({}, group), {}, {
            children: childrenWithoutBOM
          }));
        }
      });
      return {
        dataWithBOM: withBOM,
        dataWithoutBOM: withoutBOM
      };
    }, [defaultData]),
    dataWithBOM = _useMemo.dataWithBOM,
    dataWithoutBOM = _useMemo.dataWithoutBOM;
  var treeDataWithBOM = (0,react__WEBPACK_IMPORTED_MODULE_5__.useMemo)(function () {
    return loop(dataWithBOM);
  }, [searchValue, dataWithBOM]);
  var treeDataWithoutBOM = (0,react__WEBPACK_IMPORTED_MODULE_5__.useMemo)(function () {
    return loop(dataWithoutBOM);
  }, [searchValue, dataWithoutBOM]);
  var intl = (0,_umijs_max__WEBPACK_IMPORTED_MODULE_4__.useIntl)();
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxs)("div", {
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Search, {
      style: {
        marginBottom: 8
      },
      placeholder: "Search",
      onChange: onChange
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Z, {
      gutter: 16,
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .Z, {
        span: 12,
        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Title, {
          level: 5,
          children: intl.formatMessage({
            id: 'common.item_list'
          })
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(antd__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .Z, {
          onExpand: onExpand,
          expandedKeys: expandedKeys,
          autoExpandParent: autoExpandParent,
          treeData: treeDataWithoutBOM,
          checkable: true,
          onCheck: handleOnCheckWithoutBOM,
          checkedKeys: checkedKeysWithoutBOM
        })]
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .Z, {
        span: 12,
        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(Title, {
          level: 5,
          children: intl.formatMessage({
            id: 'common.item_list_with_bom'
          })
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(antd__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .Z, {
          onExpand: onExpand,
          expandedKeys: expandedKeys,
          autoExpandParent: autoExpandParent,
          treeData: treeDataWithBOM,
          checkable: true,
          onCheck: handleOnCheckWithBOM,
          checkedKeys: checkedKeysWithBOM
        })]
      })]
    })]
  });
};
/* harmony default export */ __webpack_exports__.Z = (SliceSearchableTreeSelect);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///19676
`)},11371:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`
// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  Z: function() { return /* binding */ TaskItemUsed_CategoryQuantitySelector; }
});

// EXTERNAL MODULE: ./src/components/SliceSearchableTreeSelect/index.tsx
var SliceSearchableTreeSelect = __webpack_require__(19676);
// EXTERNAL MODULE: ./src/.umi-production/exports.ts + 34 modules
var _umi_production_exports = __webpack_require__(7837);
// EXTERNAL MODULE: ./node_modules/antd/es/divider/index.js + 1 modules
var divider = __webpack_require__(96074);
// EXTERNAL MODULE: ./node_modules/antd/es/row/index.js
var row = __webpack_require__(71230);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/objectSpread2.js
var objectSpread2 = __webpack_require__(97857);
var objectSpread2_default = /*#__PURE__*/__webpack_require__.n(objectSpread2);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/slicedToArray.js
var slicedToArray = __webpack_require__(5574);
var slicedToArray_default = /*#__PURE__*/__webpack_require__.n(slicedToArray);
// EXTERNAL MODULE: ./src/stores/TaskItemUsedCreateStore.tsx
var TaskItemUsedCreateStore = __webpack_require__(14682);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-table/es/Table.js + 76 modules
var Table = __webpack_require__(4894);
// EXTERNAL MODULE: ./node_modules/antd/es/form/index.js + 18 modules
var es_form = __webpack_require__(98138);
// EXTERNAL MODULE: ./node_modules/antd/es/input-number/index.js + 16 modules
var input_number = __webpack_require__(9735);
// EXTERNAL MODULE: ./node_modules/antd/es/select/index.js + 30 modules
var es_select = __webpack_require__(83863);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./node_modules/react/jsx-runtime.js
var jsx_runtime = __webpack_require__(85893);
;// CONCATENATED MODULE: ./src/components/Task/BomInTask/InputQtyForItemTaskUsed.tsx








var flattenTaskItems = function flattenTaskItems(taskItems) {
  var flattenedItems = new Map();
  taskItems.forEach(function (item) {
    flattenedItems.set(item.iot_category_id, item);
    if (item.bom && item.bom.length > 0) {
      item.bom.forEach(function (bomItem) {
        bomItem.bom_items.forEach(function (bom_item) {
          var flattenedItem = {
            exp_quantity: bom_item.exp_quantity,
            iot_category_id: bom_item.item_code,
            item_name: bom_item.item_name,
            label: bom_item.item_label,
            ratio: bom_item.ratio,
            parentId: item.iot_category_id,
            uom: bom_item.uom,
            uom_label: bom_item.uom_label,
            conversion_factor: bom_item.conversion_factor,
            uoms: bom_item.uoms
          };
          flattenedItems.set(flattenedItem.iot_category_id, flattenedItem);
        });
      });
    }
  });
  return Array.from(flattenedItems.values());
};
var QuantityInputList = function QuantityInputList() {
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  var _useTaskItemUsedCreat = (0,TaskItemUsedCreateStore/* useTaskItemUsedCreateStore */.W)(),
    taskItemUsed = _useTaskItemUsedCreat.taskItemUsed,
    setTaskItemUsed = _useTaskItemUsedCreat.setTaskItemUsed;
  var _useState = (0,react.useState)(taskItemUsed),
    _useState2 = slicedToArray_default()(_useState, 2),
    localTaskItemUsed = _useState2[0],
    setLocalTaskItemUsed = _useState2[1];
  var form = es_form/* default */.Z.useFormInstance();
  var handleQuantityChange = function handleQuantityChange(itemName, exp_quantity) {
    var expQuantity = exp_quantity || 0;
    var newItems = taskItemUsed.map(function (item) {
      // n\u1EBFu \u0111\xFAng v\u1EDBi item th\xEC c\u1EADp nh\u1EADt l\u1EA1i s\u1ED1 l\u01B0\u1EE3ng, c\u1EADp nh\u1EADt l\u1EA1i s\u1ED1 l\u01B0\u1EE3ng cho c\xE1c item con
      if (item.iot_category_id === itemName) {
        var updatedItem = objectSpread2_default()(objectSpread2_default()({}, item), {}, {
          exp_quantity: expQuantity
        });
        if (updatedItem.bom && updatedItem.bom.length > 0) {
          updatedItem.bom.forEach(function (bomItem) {
            bomItem.bom_items.forEach(function (bom_item) {
              var newQuantity = expQuantity * bom_item.ratio;
              bom_item.exp_quantity = parseFloat(newQuantity.toFixed(2));
            });
          });
        }
        return updatedItem;
      }

      // n\u1EBFu l\xE0 item con th\xEC c\u1EADp nh\u1EADt l\u1EA1i s\u1ED1 l\u01B0\u1EE3ng theo t\u1EC9 l\u1EC7
      if (item.parentId === itemName) {
        var parentItem = taskItemUsed.find(function (parent) {
          return parent.iot_category_id === item.parentId;
        });
        var parentConversionFactor = parentItem ? parentItem.conversion_factor || 1 : 1;
        var newQuantity = expQuantity * (item.ratio || 1) * parentConversionFactor;
        return objectSpread2_default()(objectSpread2_default()({}, item), {}, {
          exp_quantity: parseFloat(newQuantity.toFixed(2))
        });
      }
      return item;
    });
    var flattenItems = flattenTaskItems(newItems);
    setTaskItemUsed(flattenItems);
    setLocalTaskItemUsed(flattenItems);
    form.setFieldsValue({
      categories: flattenItems
    });
  };
  var handleUOMChange = function handleUOMChange(itemName, newUOM) {
    console.log({
      itemName: itemName,
      newUOM: newUOM
    });
    var updatedParentItem;
    var newItems = taskItemUsed.map(function (item) {
      // n\u1EBFu \u0111\xFAng v\u1EDBi item th\xEC c\u1EADp nh\u1EADt l\u1EA1i uom, c\u1EADp nh\u1EADt l\u1EA1i uom cho c\xE1c item con
      if (item.iot_category_id === itemName) {
        console.log('item', item);
        var uomObj = item.uoms.find(function (u) {
          return u.uom === newUOM;
        });
        if (uomObj) {
          var updatedItem = objectSpread2_default()(objectSpread2_default()({}, item), {}, {
            uom: uomObj.uom,
            uom_label: uomObj.uom_label,
            conversion_factor: uomObj.conversion_factor
          });
          if (updatedItem.bom && updatedItem.bom.length > 0) {
            updatedItem.bom.forEach(function (bomItem) {
              bomItem.bom_items.forEach(function (bom_item) {
                var newQuantity = updatedItem.exp_quantity * uomObj.conversion_factor * bom_item.ratio;
                bom_item.exp_quantity = parseFloat(newQuantity.toFixed(2));
              });
            });
          }
          console.log('updatedItem', updatedItem);
          updatedParentItem = updatedItem; // L\u01B0u l\u1EA1i parent item \u0111\xE3 \u0111\u01B0\u1EE3c c\u1EADp nh\u1EADt
          return updatedItem;
        }
      }

      // N\u1EBFu c\xF3 item con th\xEC c\u1EADp nh\u1EADt l\u1EA1i uom v\xE0 qty cho item con
      if (item.parentId === itemName && updatedParentItem) {
        var parentUomObj = updatedParentItem.uoms.find(function (u) {
          return u.uom === updatedParentItem.uom;
        });
        console.log('parentUomObj', parentUomObj);
        if (parentUomObj) {
          var parentConversionFactor = parentUomObj.conversion_factor;
          var childUomObj = item.uoms[0]; //l\u1EA5y uom c\u01A1 b\u1EA3n, v\xEC newUOM l\xE0 uom c\u1EE7a item parent \u0111ang \u0111\u01B0\u1EE3c ch\u1ECDn
          console.log('childUomObj', childUomObj);
          if (childUomObj) {
            var newQuantity = updatedParentItem.exp_quantity * parentConversionFactor * (item.ratio || 1) * childUomObj.conversion_factor;
            console.log('newQuantity', newQuantity);
            return objectSpread2_default()(objectSpread2_default()({}, item), {}, {
              exp_quantity: parseFloat(newQuantity.toFixed(2)),
              uom: childUomObj.uom,
              uom_label: childUomObj.uom_label,
              conversion_factor: childUomObj.conversion_factor
            });
          }
        }
      }
      return item;
    });
    console.log('newItems when change UOM', newItems);
    var flattenItems = flattenTaskItems(newItems);
    setTaskItemUsed(flattenItems);
    setLocalTaskItemUsed(flattenItems);
    form.setFieldsValue({
      categories: flattenItems
    });
  };
  (0,react.useEffect)(function () {
    var flattenItems = flattenTaskItems(taskItemUsed);
    setLocalTaskItemUsed(flattenItems);
    form.setFieldsValue({
      categories: flattenItems
    });
  }, [taskItemUsed]);
  var columns = [{
    title: formatMessage({
      id: 'common.label'
    }),
    dataIndex: 'label',
    key: 'label'
  }, {
    title: formatMessage({
      id: 'common.expected_qty'
    }),
    dataIndex: 'exp_quantity',
    key: 'exp_quantity',
    render: function render(dom, entity, index, action, schema) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)(input_number/* default */.Z, {
        min: 0,
        value: entity.exp_quantity,
        onChange: function onChange(value) {
          return handleQuantityChange(entity.iot_category_id, value);
        }
      });
    }
  }, {
    title: formatMessage({
      id: 'common.unit'
    }),
    dataIndex: 'uom_label',
    render: function render(dom, entity, index, action, schema) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)(es_select/* default */.Z, {
        defaultValue: entity.uom,
        style: {
          width: 120
        },
        options: entity.uoms && entity.uoms.map(function (uom) {
          return {
            label: uom.uom_label,
            value: uom.uom
          };
        }),
        onChange: function onChange(value) {
          return handleUOMChange(entity.iot_category_id, value);
        }
      });
    },
    key: 'uom_label'
  }];
  return /*#__PURE__*/(0,jsx_runtime.jsx)(Table/* default */.Z, {
    search: false,
    dataSource: localTaskItemUsed,
    columns: columns,
    rowKey: "iot_category_id",
    pagination: false,
    scroll: {
      y: 400
    }
  });
};
/* harmony default export */ var InputQtyForItemTaskUsed = (QuantityInputList);
;// CONCATENATED MODULE: ./src/components/Task/TaskItemUsed/CategoryQuantitySelector.tsx






var CategoryQuantitySelector = function CategoryQuantitySelector(_ref) {
  var treeData = _ref.treeData,
    onCheck = _ref.onCheck;
  var intl = (0,_umi_production_exports.useIntl)();
  var handleCheck = function handleCheck(selectedWithBOM, selectedWithoutBOM) {
    onCheck(selectedWithBOM, selectedWithoutBOM);
  };
  return /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
    children: [/*#__PURE__*/(0,jsx_runtime.jsx)(SliceSearchableTreeSelect/* default */.Z, {
      defaultData: treeData || [],
      fieldName: "categories",
      onCheck: handleCheck
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(divider/* default */.Z, {
      plain: true,
      children: intl.formatMessage({
        id: 'common.added_item_qty'
      })
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(row/* default */.Z, {
      gutter: 5,
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(InputQtyForItemTaskUsed, {})
    })]
  });
};
/* harmony default export */ var TaskItemUsed_CategoryQuantitySelector = (CategoryQuantitySelector);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///11371
`)},90956:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`
// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  Z: function() { return /* binding */ TaskProductionNew_ProductionQuantitySelector; }
});

// EXTERNAL MODULE: ./src/components/SliceSearchableTreeSelect/index.tsx
var SliceSearchableTreeSelect = __webpack_require__(19676);
// EXTERNAL MODULE: ./src/.umi-production/exports.ts + 34 modules
var _umi_production_exports = __webpack_require__(7837);
// EXTERNAL MODULE: ./node_modules/antd/es/divider/index.js + 1 modules
var divider = __webpack_require__(96074);
// EXTERNAL MODULE: ./node_modules/antd/es/row/index.js
var row = __webpack_require__(71230);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/objectSpread2.js
var objectSpread2 = __webpack_require__(97857);
var objectSpread2_default = /*#__PURE__*/__webpack_require__.n(objectSpread2);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/slicedToArray.js
var slicedToArray = __webpack_require__(5574);
var slicedToArray_default = /*#__PURE__*/__webpack_require__.n(slicedToArray);
// EXTERNAL MODULE: ./src/stores/TaskProductionCreateStore.tsx
var TaskProductionCreateStore = __webpack_require__(55059);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-table/es/Table.js + 76 modules
var Table = __webpack_require__(4894);
// EXTERNAL MODULE: ./node_modules/antd/es/form/index.js + 18 modules
var es_form = __webpack_require__(98138);
// EXTERNAL MODULE: ./node_modules/antd/es/input-number/index.js + 16 modules
var input_number = __webpack_require__(9735);
// EXTERNAL MODULE: ./node_modules/antd/es/select/index.js + 30 modules
var es_select = __webpack_require__(83863);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./node_modules/react/jsx-runtime.js
var jsx_runtime = __webpack_require__(85893);
;// CONCATENATED MODULE: ./src/components/Task/BomInTask/InputQtyForItemProduction.tsx








var flattenTaskItems = function flattenTaskItems(taskItems) {
  var flattenedItems = new Map();
  taskItems.forEach(function (item) {
    flattenedItems.set(item.product_id, item);
    if (item.bom && item.bom.length > 0) {
      item.bom.forEach(function (bomItem) {
        bomItem.bom_items.forEach(function (bom_item) {
          var flattenedItem = {
            exp_quantity: bom_item.exp_quantity,
            product_id: bom_item.item_code,
            item_name: bom_item.item_name,
            label: bom_item.item_label,
            ratio: bom_item.ratio,
            parentId: item.product_id,
            uom: bom_item.uom,
            uom_label: bom_item.uom_label,
            conversion_factor: bom_item.conversion_factor,
            uoms: bom_item.uoms
          };
          flattenedItems.set(flattenedItem.product_id, flattenedItem);
        });
      });
    }
  });
  return Array.from(flattenedItems.values());
};
var QuantityInputList = function QuantityInputList() {
  var _useTaskProductionCre = (0,TaskProductionCreateStore/* useTaskProductionCreateStore */.N)(),
    taskProduction = _useTaskProductionCre.taskProduction,
    setTaskProduction = _useTaskProductionCre.setTaskProduction;
  var _useState = (0,react.useState)(taskProduction),
    _useState2 = slicedToArray_default()(_useState, 2),
    localTaskProduction = _useState2[0],
    setLocalTaskProduction = _useState2[1];
  var form = es_form/* default */.Z.useFormInstance();
  var handleQuantityChange = function handleQuantityChange(itemName, exp_quantity) {
    var expQuantity = exp_quantity || 0;
    var newItems = taskProduction.map(function (item) {
      if (item.product_id === itemName) {
        var updatedItem = objectSpread2_default()(objectSpread2_default()({}, item), {}, {
          exp_quantity: expQuantity
        });
        if (updatedItem.bom) {
          updatedItem.bom.forEach(function (bomItem) {
            bomItem.bom_items.forEach(function (bom_item) {
              var newQuantity = expQuantity * bom_item.ratio;
              bom_item.exp_quantity = parseFloat(newQuantity.toFixed(2));
            });
          });
        }
        return updatedItem;
      }
      if (item.parentId === itemName) {
        var parentItem = taskProduction.find(function (parent) {
          return parent.product_id === item.parentId;
        });
        var parentConversionFactor = parentItem ? parentItem.conversion_factor || 1 : 1;
        var newQuantity = expQuantity * (item.ratio || 1) * parentConversionFactor;
        return objectSpread2_default()(objectSpread2_default()({}, item), {}, {
          exp_quantity: parseFloat(newQuantity.toFixed(2))
        });
      }
      return item;
    });
    var flattenItems = flattenTaskItems(newItems);
    setTaskProduction(flattenItems);
    setLocalTaskProduction(flattenItems);
    form.setFieldsValue({
      categories: flattenItems
    });
  };
  var handleUOMChange = function handleUOMChange(itemName, newUOM) {
    console.log({
      itemName: itemName,
      newUOM: newUOM
    });
    var updatedParentItem;
    var newItems = taskProduction.map(function (item) {
      if (item.product_id === itemName) {
        console.log('item', item);
        var uomObj = item.uoms.find(function (u) {
          return u.uom === newUOM;
        });
        if (uomObj) {
          var updatedItem = objectSpread2_default()(objectSpread2_default()({}, item), {}, {
            uom: uomObj.uom,
            uom_label: uomObj.uom_label,
            conversion_factor: uomObj.conversion_factor
          });
          if (updatedItem.bom) {
            updatedItem.bom.forEach(function (bomItem) {
              bomItem.bom_items.forEach(function (bom_item) {
                var newQuantity = updatedItem.exp_quantity * uomObj.conversion_factor * bom_item.ratio;
                bom_item.exp_quantity = parseFloat(newQuantity.toFixed(2));
              });
            });
          }
          console.log('updatedItem', updatedItem);
          updatedParentItem = updatedItem;
          return updatedItem;
        }
      }
      if (item.parentId === itemName && updatedParentItem) {
        var parentUomObj = updatedParentItem.uoms.find(function (u) {
          return u.uom === updatedParentItem.uom;
        });
        console.log('parentUomObj', parentUomObj);
        if (parentUomObj) {
          var parentConversionFactor = parentUomObj.conversion_factor;
          var childUomObj = item.uoms[0];
          console.log('childUomObj', childUomObj);
          if (childUomObj) {
            var newQuantity = updatedParentItem.exp_quantity * parentConversionFactor * (item.ratio || 1) * childUomObj.conversion_factor;
            console.log('newQuantity', newQuantity);
            return objectSpread2_default()(objectSpread2_default()({}, item), {}, {
              exp_quantity: parseFloat(newQuantity.toFixed(2)),
              uom: childUomObj.uom,
              uom_label: childUomObj.uom_label,
              conversion_factor: childUomObj.conversion_factor
            });
          }
        }
      }
      return item;
    });
    console.log('newItems when change UOM', newItems);
    var flattenItems = flattenTaskItems(newItems);
    setTaskProduction(flattenItems);
    setLocalTaskProduction(flattenItems);
    form.setFieldsValue({
      categories: flattenItems
    });
  };
  (0,react.useEffect)(function () {
    var flattenItems = flattenTaskItems(taskProduction);
    setLocalTaskProduction(flattenItems);
    form.setFieldsValue({
      categories: flattenItems
    });
  }, [taskProduction]);
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  var columns = [{
    title: formatMessage({
      id: 'common.label'
    }),
    dataIndex: 'label',
    key: 'label'
  }, {
    title: formatMessage({
      id: 'common.expected_qty'
    }),
    dataIndex: 'exp_quantity',
    key: 'exp_quantity',
    render: function render(text, record) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)(input_number/* default */.Z, {
        min: 0,
        value: text,
        onChange: function onChange(value) {
          return handleQuantityChange(record.product_id, value);
        }
      });
    }
  }, {
    title: formatMessage({
      id: 'common.unit'
    }),
    dataIndex: 'uom_label',
    render: function render(dom, entity, index, action, schema) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)(es_select/* default */.Z, {
        defaultValue: entity.uom,
        style: {
          width: 120
        },
        options: entity.uoms && entity.uoms.map(function (uom) {
          return {
            label: uom.uom_label,
            value: uom.uom
          };
        }),
        onChange: function onChange(value) {
          return handleUOMChange(entity.product_id, value);
        }
      });
    },
    key: 'uom_label'
  }];
  return /*#__PURE__*/(0,jsx_runtime.jsx)(Table/* default */.Z, {
    search: false,
    dataSource: localTaskProduction,
    columns: columns,
    rowKey: "product_id",
    pagination: false,
    scroll: {
      y: 400
    }
  });
};
/* harmony default export */ var InputQtyForItemProduction = (QuantityInputList);
;// CONCATENATED MODULE: ./src/components/Task/TaskProductionNew/ProductionQuantitySelector.tsx






var ProductionQuantitySelector = function ProductionQuantitySelector(_ref) {
  var treeData = _ref.treeData,
    onCheck = _ref.onCheck;
  var intl = (0,_umi_production_exports.useIntl)();
  var handleCheck = function handleCheck(selectedWithBOM, selectedWithoutBOM) {
    console.log({
      selectedWithBOM: selectedWithBOM,
      selectedWithoutBOM: selectedWithoutBOM
    });
    onCheck(selectedWithBOM, selectedWithoutBOM);
  };
  return /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
    children: [/*#__PURE__*/(0,jsx_runtime.jsx)(SliceSearchableTreeSelect/* default */.Z, {
      defaultData: treeData || [],
      fieldName: "products",
      onCheck: handleCheck
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(divider/* default */.Z, {
      plain: true,
      children: intl.formatMessage({
        id: 'common.added_item_qty'
      })
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(row/* default */.Z, {
      gutter: 5,
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(InputQtyForItemProduction, {})
    })]
  });
};
/* harmony default export */ var TaskProductionNew_ProductionQuantitySelector = (ProductionQuantitySelector);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///90956
`)},97035:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`
// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  Z: function() { return /* binding */ TagManager_ProFormTagSelect; }
});

// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/regeneratorRuntime.js
var regeneratorRuntime = __webpack_require__(15009);
var regeneratorRuntime_default = /*#__PURE__*/__webpack_require__.n(regeneratorRuntime);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/toConsumableArray.js
var toConsumableArray = __webpack_require__(19632);
var toConsumableArray_default = /*#__PURE__*/__webpack_require__.n(toConsumableArray);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(99289);
var asyncToGenerator_default = /*#__PURE__*/__webpack_require__.n(asyncToGenerator);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/slicedToArray.js
var slicedToArray = __webpack_require__(5574);
var slicedToArray_default = /*#__PURE__*/__webpack_require__.n(slicedToArray);
// EXTERNAL MODULE: ./src/.umi-production/exports.ts + 34 modules
var _umi_production_exports = __webpack_require__(7837);
// EXTERNAL MODULE: ./src/services/utils.ts
var utils = __webpack_require__(467);
;// CONCATENATED MODULE: ./src/services/tag.ts




var getTags = /*#__PURE__*/function () {
  var _ref = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(params) {
    var res;
    return regeneratorRuntime_default()().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.next = 2;
          return (0,_umi_production_exports.request)((0,utils/* generateAPIPath */.rH)('api/v2/tag'), {
            method: 'GET',
            params: (0,utils/* getParamsReqList */.vj)(params)
          });
        case 2:
          res = _context.sent;
          return _context.abrupt("return", res.result);
        case 4:
        case "end":
          return _context.stop();
      }
    }, _callee);
  }));
  return function getTags(_x) {
    return _ref.apply(this, arguments);
  };
}();
var addTag = /*#__PURE__*/function () {
  var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2(data) {
    var res;
    return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          _context2.next = 2;
          return (0,_umi_production_exports.request)((0,utils/* generateAPIPath */.rH)('api/v2/tag'), {
            method: 'POST',
            data: data
          });
        case 2:
          res = _context2.sent;
          return _context2.abrupt("return", res.result);
        case 4:
        case "end":
          return _context2.stop();
      }
    }, _callee2);
  }));
  return function addTag(_x2) {
    return _ref2.apply(this, arguments);
  };
}();
var editTag = /*#__PURE__*/function () {
  var _ref3 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee3(data) {
    var res;
    return regeneratorRuntime_default()().wrap(function _callee3$(_context3) {
      while (1) switch (_context3.prev = _context3.next) {
        case 0:
          _context3.next = 2;
          return (0,_umi_production_exports.request)((0,utils/* generateAPIPath */.rH)('api/v2/tag'), {
            method: 'PUT',
            data: data
          });
        case 2:
          res = _context3.sent;
          return _context3.abrupt("return", res.result);
        case 4:
        case "end":
          return _context3.stop();
      }
    }, _callee3);
  }));
  return function editTag(_x3) {
    return _ref3.apply(this, arguments);
  };
}();
var deleteTag = /*#__PURE__*/function () {
  var _ref4 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee4(id) {
    var res;
    return regeneratorRuntime_default()().wrap(function _callee4$(_context4) {
      while (1) switch (_context4.prev = _context4.next) {
        case 0:
          _context4.next = 2;
          return (0,_umi_production_exports.request)((0,utils/* generateAPIPath */.rH)('api/v2/tag'), {
            method: 'DELETE',
            params: {
              name: encodeURIComponent(id)
            }
          });
        case 2:
          res = _context4.sent;
          return _context4.abrupt("return", res.result);
        case 4:
        case "end":
          return _context4.stop();
      }
    }, _callee4);
  }));
  return function deleteTag(_x4) {
    return _ref4.apply(this, arguments);
  };
}();
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/Select/index.js
var Select = __webpack_require__(64317);
// EXTERNAL MODULE: ./node_modules/antd/es/space/index.js + 2 modules
var space = __webpack_require__(42075);
// EXTERNAL MODULE: ./node_modules/antd/es/divider/index.js + 1 modules
var divider = __webpack_require__(96074);
// EXTERNAL MODULE: ./node_modules/antd/es/button/index.js + 9 modules
var es_button = __webpack_require__(14726);
// EXTERNAL MODULE: ./node_modules/nanoid/index.browser.js
var index_browser = __webpack_require__(53416);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/defineProperty.js
var defineProperty = __webpack_require__(9783);
var defineProperty_default = /*#__PURE__*/__webpack_require__.n(defineProperty);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/EditOutlined.js
var EditOutlined = __webpack_require__(47389);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/DeleteOutlined.js
var DeleteOutlined = __webpack_require__(82061);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/ColorPicker/index.js
var ColorPicker = __webpack_require__(56981);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-table/es/components/EditableTable/index.js + 1 modules
var EditableTable = __webpack_require__(88280);
// EXTERNAL MODULE: ./node_modules/antd/es/app/index.js + 1 modules
var app = __webpack_require__(31418);
// EXTERNAL MODULE: ./node_modules/antd/es/color-picker/index.js + 36 modules
var color_picker = __webpack_require__(80542);
// EXTERNAL MODULE: ./node_modules/antd/es/modal/index.js + 1 modules
var modal = __webpack_require__(85576);
// EXTERNAL MODULE: ./node_modules/react/jsx-runtime.js
var jsx_runtime = __webpack_require__(85893);
;// CONCATENATED MODULE: ./src/pages/FarmingManagement/WorkflowManagement/TagManager/index.tsx














var isClientKey = Symbol["for"]((0,index_browser/* nanoid */.x0)());
var TagManager = function TagManager(_ref) {
  var children = _ref.children,
    open = _ref.open,
    onOpenChange = _ref.onOpenChange,
    onSuccess = _ref.onSuccess;
  var _useState = (0,react.useState)([]),
    _useState2 = slicedToArray_default()(_useState, 2),
    editableKeys = _useState2[0],
    setEditableRowKeys = _useState2[1];
  var _useState3 = (0,react.useState)([]),
    _useState4 = slicedToArray_default()(_useState3, 2),
    dataSource = _useState4[0],
    setDataSource = _useState4[1];
  var _App$useApp = app/* default */.Z.useApp(),
    message = _App$useApp.message;
  var columns = [{
    title: 'Nh\xE3n',
    dataIndex: 'label',
    formItemProps: {
      rules: [{
        required: true
      }]
    }
  }, {
    title: 'M\xE0u s\u1EAFc',
    dataIndex: 'color',
    valueType: 'color',
    formItemProps: {
      rules: [{
        required: true
      }]
    },
    render: function render(dom, entity, index, action, schema) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)(color_picker/* default */.Z, {
        disabled: true,
        showText: true,
        value: entity.color
      });
    },
    renderFormItem: function renderFormItem(schema, config, form, action) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)(jsx_runtime.Fragment, {
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(ColorPicker/* default */.Z, {
          name: "color",
          noStyle: true
        })
      });
    }
  }, {
    valueType: 'option',
    width: 100,
    render: function render(text, record, _, action) {
      return [/*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
        size: "small",
        onClick: function onClick() {
          var _action$startEditable;
          action === null || action === void 0 || (_action$startEditable = action.startEditable) === null || _action$startEditable === void 0 || _action$startEditable.call(action, record.name);
        },
        icon: /*#__PURE__*/(0,jsx_runtime.jsx)(EditOutlined/* default */.Z, {})
      }, "editable"), /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
        size: "small",
        onClick: /*#__PURE__*/asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
          return regeneratorRuntime_default()().wrap(function _callee$(_context) {
            while (1) switch (_context.prev = _context.next) {
              case 0:
                if (record[isClientKey]) {
                  _context.next = 13;
                  break;
                }
                _context.prev = 1;
                _context.next = 4;
                return deleteTag(record.name);
              case 4:
                message.success('Th\xE0nh c\xF4ng');
                onSuccess === null || onSuccess === void 0 || onSuccess();
                _context.next = 13;
                break;
              case 8:
                _context.prev = 8;
                _context.t0 = _context["catch"](1);
                console.log('error: ', _context.t0);
                message.error('X\xF3a kh\xF4ng th\xE0nh c\xF4ng');
                return _context.abrupt("return", false);
              case 13:
                setDataSource(dataSource.filter(function (item) {
                  return item.name !== record.name;
                }));
                return _context.abrupt("return", true);
              case 15:
              case "end":
                return _context.stop();
            }
          }, _callee, null, [[1, 8]]);
        })),
        icon: /*#__PURE__*/(0,jsx_runtime.jsx)(DeleteOutlined/* default */.Z, {}),
        danger: true,
        type: "primary"
      }, "delete")];
    }
  }];
  return /*#__PURE__*/(0,jsx_runtime.jsx)(modal/* default */.Z, {
    title: "Qu\\u1EA3n l\\xED tag",
    open: open,
    onCancel: function onCancel() {
      onOpenChange === null || onOpenChange === void 0 || onOpenChange(false);
    },
    children: /*#__PURE__*/(0,jsx_runtime.jsx)(EditableTable/* default */.Z, {
      columns: columns,
      rowKey: 'name',
      scroll: {
        y: 500
      },
      recordCreatorProps: {
        position: 'top',
        record: function record() {
          return defineProperty_default()({
            name: (0,index_browser/* nanoid */.x0)()
          }, isClientKey, true);
        },
        // https://github.com/ant-design/pro-components/issues/1726#issuecomment-765814817
        // th\xEAm nhi\u1EC1u d\xF2ng 1 l\xFAc
        // newRecordType: 'dataSource',
        // th\xEAm 1 dong 1 l\xFAc
        newRecordType: 'cache'
      },
      options: {
        search: true,
        density: false,
        fullScreen: false,
        densityIcon: false,
        setting: false
      },
      pagination: {
        pageSize: 50
      },
      request: ( /*#__PURE__*/function () {
        var _ref4 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2(params) {
          var res;
          return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
            while (1) switch (_context2.prev = _context2.next) {
              case 0:
                _context2.next = 2;
                return getTags({
                  page: params.current,
                  size: params.pageSize,
                  fields: ['*'],
                  filters: toConsumableArray_default()(params.keyword ? [['iot_tag', 'label', 'like', "%".concat(params.keyword, "%")]] : [])
                });
              case 2:
                res = _context2.sent;
                return _context2.abrupt("return", {
                  data: res.data,
                  total: res.pagination.totalElements
                });
              case 4:
              case "end":
                return _context2.stop();
            }
          }, _callee2);
        }));
        return function (_x) {
          return _ref4.apply(this, arguments);
        };
      }()),
      value: dataSource,
      onChange: setDataSource,
      editable: {
        type: 'multiple',
        saveText: 'L\u01B0u',
        editableKeys: editableKeys,
        //  \u0111\u1ED5i ch\u1EEF trung qu\u1ED1c khi validate th\xEAm 1 ho\u1EB7c nhi\u1EC1u d\xF2ng
        onlyAddOneLineAlertMessage: 'Ch\u1EC9 c\xF3 th\u1EC3 ch\u1EC9nh s\u1EEDa m\u1ED9t h\xE0ng c\xF9ng m\u1ED9t l\xFAc',
        onlyOneLineEditorAlertMessage: 'Ch\u1EC9 c\xF3 th\u1EC3 ch\u1EC9nh s\u1EEDa m\u1ED9t h\xE0ng c\xF9ng m\u1ED9t l\xFAc',
        actionRender: function actionRender(row, config, defaultDom) {
          return [/*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
            children: [defaultDom.save, defaultDom.cancel]
          })];
        },
        onSave: function () {
          var _onSave = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee3(rowKey, data, row) {
            var _data$color, _data$color$toHexStri;
            var color, dataCreateRes, dataEditRes;
            return regeneratorRuntime_default()().wrap(function _callee3$(_context3) {
              while (1) switch (_context3.prev = _context3.next) {
                case 0:
                  color = (_data$color = data.color) === null || _data$color === void 0 || (_data$color = _data$color.metaColor) === null || _data$color === void 0 || (_data$color$toHexStri = _data$color.toHexString) === null || _data$color$toHexStri === void 0 ? void 0 : _data$color$toHexStri.call(_data$color);
                  if (!row[isClientKey]) {
                    _context3.next = 17;
                    break;
                  }
                  _context3.prev = 2;
                  _context3.next = 5;
                  return addTag({
                    label: data.label,
                    color: color
                  });
                case 5:
                  dataCreateRes = _context3.sent;
                  setDataSource(function (prev) {
                    return prev.map(function (item) {
                      return item.name === row.name ? dataCreateRes.data : item;
                    });
                  });
                  message.success('Th\xE0nh c\xF4ng');
                  onSuccess === null || onSuccess === void 0 || onSuccess();
                  return _context3.abrupt("return", true);
                case 12:
                  _context3.prev = 12;
                  _context3.t0 = _context3["catch"](2);
                  console.log('error: ', _context3.t0);
                  message.error('\u0110\xE3 c\xF3 l\u1ED7i x\u1EA3y ra, vui l\xF2ng th\u1EED l\u1EA1i');
                  throw new Error(_context3.t0 === null || _context3.t0 === void 0 ? void 0 : _context3.t0.message);
                case 17:
                  _context3.prev = 17;
                  _context3.next = 20;
                  return editTag({
                    name: row.name,
                    label: data.label,
                    color: color
                  });
                case 20:
                  dataEditRes = _context3.sent;
                  setDataSource(function (prev) {
                    return prev.map(function (item) {
                      return item.name === row.name ? dataEditRes.data : item;
                    });
                  });
                  message.success('Th\xE0nh c\xF4ng');
                  onSuccess === null || onSuccess === void 0 || onSuccess();
                  return _context3.abrupt("return", true);
                case 27:
                  _context3.prev = 27;
                  _context3.t1 = _context3["catch"](17);
                  console.log('error: ', _context3.t1);
                  message.error('\u0110\xE3 c\xF3 l\u1ED7i x\u1EA3y ra, vui l\xF2ng th\u1EED l\u1EA1i');
                  throw new Error(_context3.t1 === null || _context3.t1 === void 0 ? void 0 : _context3.t1.message);
                case 32:
                case "end":
                  return _context3.stop();
              }
            }, _callee3, null, [[2, 12], [17, 27]]);
          }));
          function onSave(_x2, _x3, _x4) {
            return _onSave.apply(this, arguments);
          }
          return onSave;
        }(),
        onChange: setEditableRowKeys
      }
    })
  });
};
/* harmony default export */ var WorkflowManagement_TagManager = (TagManager);
;// CONCATENATED MODULE: ./src/pages/FarmingManagement/WorkflowManagement/TagManager/ProFormTagSelect.tsx














var ProFormTagSelect = function ProFormTagSelect(_ref) {
  var children = _ref.children,
    onEditTagSuccess = _ref.onEditTagSuccess,
    initTag = _ref.initTag;
  var _useState = (0,react.useState)(false),
    _useState2 = slicedToArray_default()(_useState, 2),
    openTagManager = _useState2[0],
    setOpenTagManager = _useState2[1];
  // reload select khi s\u1EEDa \u0111\u1ED5i Tag
  var _useState3 = (0,react.useState)((0,index_browser/* nanoid */.x0)()),
    _useState4 = slicedToArray_default()(_useState3, 2),
    uuid = _useState4[0],
    setUuid = _useState4[1];
  var reloadOptions = function reloadOptions() {
    onEditTagSuccess === null || onEditTagSuccess === void 0 || onEditTagSuccess();
    setUuid((0,index_browser/* nanoid */.x0)());
  };
  var intl = (0,_umi_production_exports.useIntl)();
  return /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
    children: [openTagManager && /*#__PURE__*/(0,jsx_runtime.jsx)(WorkflowManagement_TagManager, {
      open: openTagManager,
      onOpenChange: setOpenTagManager,
      onSuccess: reloadOptions
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
      label: intl.formatMessage({
        id: 'common.tag'
      }),
      name: 'tag',
      initialValue: initTag,
      fieldProps: {
        // onSra
        open: openTagManager ? false : undefined,
        optionItemRender: function optionItemRender(item) {
          return /*#__PURE__*/(0,jsx_runtime.jsxs)(space/* default */.Z, {
            children: [/*#__PURE__*/(0,jsx_runtime.jsx)("div", {
              style: {
                width: 18,
                height: 18,
                borderRadius: 100,
                backgroundColor: item.color
              }
            }), /*#__PURE__*/(0,jsx_runtime.jsx)("span", {
              children: item.label
            })]
          });
        },
        dropdownRender: function dropdownRender(menu) {
          return /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
            children: [menu, /*#__PURE__*/(0,jsx_runtime.jsx)(divider/* default */.Z, {
              style: {
                margin: '8px 0'
              }
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
              type: "link",
              onClick: function onClick() {
                setOpenTagManager(true);
              },
              children: "Qu\\u1EA3n l\\xED nh\\xE3n"
            })]
          });
        }
      },
      showSearch: true,
      request: ( /*#__PURE__*/function () {
        var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(params) {
          var res;
          return regeneratorRuntime_default()().wrap(function _callee$(_context) {
            while (1) switch (_context.prev = _context.next) {
              case 0:
                _context.next = 2;
                return getTags({
                  page: 1,
                  size: 50,
                  filters: toConsumableArray_default()(params.keyword ? [['iot_tag', 'label', 'like', "%".concat(params.keyword, "%")]] : []),
                  fields: ['*']
                });
              case 2:
                res = _context.sent;
                return _context.abrupt("return", res.data.map(function (item) {
                  return {
                    label: item.label,
                    value: item.name,
                    color: item.color
                  };
                }));
              case 4:
              case "end":
                return _context.stop();
            }
          }, _callee);
        }));
        return function (_x) {
          return _ref2.apply(this, arguments);
        };
      }())
    }, uuid)]
  });
};
/* harmony default export */ var TagManager_ProFormTagSelect = (ProFormTagSelect);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///97035
`)},58642:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   E6: function() { return /* binding */ getItemInventoryVouchers; },
/* harmony export */   Kd: function() { return /* binding */ getItemByGroup; },
/* harmony export */   T1: function() { return /* binding */ updateProductItemV3; },
/* harmony export */   Zk: function() { return /* binding */ getItemCustomerVouchersSum; },
/* harmony export */   eX: function() { return /* binding */ getDetailsProductItemV3; },
/* harmony export */   fu: function() { return /* binding */ deleteProductItemV3; },
/* harmony export */   hq: function() { return /* binding */ createProductItemV3; },
/* harmony export */   oD: function() { return /* binding */ getItemSupplierVouchersSum; },
/* harmony export */   yI: function() { return /* binding */ getProductItemV3; }
/* harmony export */ });
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(97857);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(7837);
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(467);





function getItemByGroup(_x) {
  return _getItemByGroup.apply(this, arguments);
}
function _getItemByGroup() {
  _getItemByGroup = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee9(_ref) {
    var _ref$page, page, _ref$size, size, _ref$fields, fields, _ref$filters, filters, _ref$or_filters, or_filters, params, result;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee9$(_context9) {
      while (1) switch (_context9.prev = _context9.next) {
        case 0:
          _ref$page = _ref.page, page = _ref$page === void 0 ? 1 : _ref$page, _ref$size = _ref.size, size = _ref$size === void 0 ? 10000 : _ref$size, _ref$fields = _ref.fields, fields = _ref$fields === void 0 ? ['*'] : _ref$fields, _ref$filters = _ref.filters, filters = _ref$filters === void 0 ? [] : _ref$filters, _ref$or_filters = _ref.or_filters, or_filters = _ref$or_filters === void 0 ? [] : _ref$or_filters;
          _context9.prev = 1;
          params = {
            page: page,
            size: size,
            fields: JSON.stringify(fields),
            filters: JSON.stringify(filters)
          };
          _context9.next = 5;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)("api/v2/item/itemGroupByGroup"), {
            method: 'GET',
            params: params
            // params: params,
            // queryParams: params,
          });
        case 5:
          result = _context9.sent;
          return _context9.abrupt("return", {
            data: result.result || []
          });
        case 9:
          _context9.prev = 9;
          _context9.t0 = _context9["catch"](1);
          return _context9.abrupt("return", {
            data: []
          });
        case 12:
        case "end":
          return _context9.stop();
      }
    }, _callee9, null, [[1, 9]]);
  }));
  return _getItemByGroup.apply(this, arguments);
}
var getProductItemV3 = /*#__PURE__*/function () {
  var _ref2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/item'), {
            params: (0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .getParamsReqList */ .vj)(params)
          });
        case 2:
          res = _context.sent;
          return _context.abrupt("return", res.result);
        case 4:
        case "end":
          return _context.stop();
      }
    }, _callee);
  }));
  return function getProductItemV3(_x2) {
    return _ref2.apply(this, arguments);
  };
}();
var createProductItemV3 = /*#__PURE__*/function () {
  var _ref3 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee2(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          _context2.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/item'), {
            method: 'POST',
            data: data
          });
        case 2:
          res = _context2.sent;
          return _context2.abrupt("return", {
            data: res.result
          });
        case 4:
        case "end":
          return _context2.stop();
      }
    }, _callee2);
  }));
  return function createProductItemV3(_x3) {
    return _ref3.apply(this, arguments);
  };
}();
var updateProductItemV3 = /*#__PURE__*/function () {
  var _ref4 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee3(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee3$(_context3) {
      while (1) switch (_context3.prev = _context3.next) {
        case 0:
          _context3.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/item'), {
            method: 'PUT',
            data: data
          });
        case 2:
          res = _context3.sent;
          return _context3.abrupt("return", {
            data: res.result
          });
        case 4:
        case "end":
          return _context3.stop();
      }
    }, _callee3);
  }));
  return function updateProductItemV3(_x4) {
    return _ref4.apply(this, arguments);
  };
}();
var deleteProductItemV3 = /*#__PURE__*/function () {
  var _ref5 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee4(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee4$(_context4) {
      while (1) switch (_context4.prev = _context4.next) {
        case 0:
          _context4.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/item'), {
            method: 'PUT',
            data: D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default()({}, data), {}, {
              is_deleted: 1
            })
          });
        case 2:
          res = _context4.sent;
          return _context4.abrupt("return", {
            data: res.result
          });
        case 4:
        case "end":
          return _context4.stop();
      }
    }, _callee4);
  }));
  return function deleteProductItemV3(_x5) {
    return _ref5.apply(this, arguments);
  };
}();
var getDetailsProductItemV3 = /*#__PURE__*/function () {
  var _ref6 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee5(params) {
    var _res$result$data;
    var res, data;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee5$(_context5) {
      while (1) switch (_context5.prev = _context5.next) {
        case 0:
          _context5.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/item/detail'), {
            params: params
          });
        case 2:
          res = _context5.sent;
          data = (_res$result$data = res.result.data) === null || _res$result$data === void 0 ? void 0 : _res$result$data[0];
          if (data) {
            _context5.next = 6;
            break;
          }
          throw new Error('Not found');
        case 6:
          return _context5.abrupt("return", {
            data: data
          });
        case 7:
        case "end":
          return _context5.stop();
      }
    }, _callee5);
  }));
  return function getDetailsProductItemV3(_x6) {
    return _ref6.apply(this, arguments);
  };
}();
var getItemInventoryVouchers = /*#__PURE__*/function () {
  var _ref7 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee6(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee6$(_context6) {
      while (1) switch (_context6.prev = _context6.next) {
        case 0:
          _context6.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/item/inventory-vouchers'), {
            params: params
          });
        case 2:
          res = _context6.sent;
          return _context6.abrupt("return", res.result);
        case 4:
        case "end":
          return _context6.stop();
      }
    }, _callee6);
  }));
  return function getItemInventoryVouchers(_x7) {
    return _ref7.apply(this, arguments);
  };
}();
var getItemCustomerVouchersSum = /*#__PURE__*/function () {
  var _ref8 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee7(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee7$(_context7) {
      while (1) switch (_context7.prev = _context7.next) {
        case 0:
          _context7.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/item/customer-vouchers'), {
            params: params
          });
        case 2:
          res = _context7.sent;
          return _context7.abrupt("return", res.result);
        case 4:
        case "end":
          return _context7.stop();
      }
    }, _callee7);
  }));
  return function getItemCustomerVouchersSum(_x8) {
    return _ref8.apply(this, arguments);
  };
}();
var getItemSupplierVouchersSum = /*#__PURE__*/function () {
  var _ref9 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee8(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee8$(_context8) {
      while (1) switch (_context8.prev = _context8.next) {
        case 0:
          _context8.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/item/supplier-vouchers'), {
            params: params
          });
        case 2:
          res = _context8.sent;
          return _context8.abrupt("return", res.result);
        case 4:
        case "end":
          return _context8.stop();
      }
    }, _callee8);
  }));
  return function getItemSupplierVouchersSum(_x9) {
    return _ref9.apply(this, arguments);
  };
}();//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///58642
`)},40063:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   J9: function() { return /* binding */ getCustomerUserList; },
/* harmony export */   Lf: function() { return /* binding */ listDynamicRoleAllSection; },
/* harmony export */   cb: function() { return /* binding */ updateCustomerUser; },
/* harmony export */   f6: function() { return /* binding */ createDynamicRole; },
/* harmony export */   fh: function() { return /* binding */ updateDynamicRole; },
/* harmony export */   jt: function() { return /* binding */ customerUserListAll; },
/* harmony export */   rX: function() { return /* binding */ removeDynamicRole; },
/* harmony export */   w: function() { return /* binding */ getDynamicRole; },
/* harmony export */   y_: function() { return /* binding */ createCustomerUser; }
/* harmony export */ });
/* unused harmony exports IIotDynamicRole, getCustomerUserIndividualList, deleteCustomerUser, deleteCustomerUserCredential */
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_createClass_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(72004);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_createClass_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_createClass_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_classCallCheck_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(12444);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_classCallCheck_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_classCallCheck_js__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_defineProperty_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(9783);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_defineProperty_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_defineProperty_js__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(7837);
/* harmony import */ var _sscript__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(39750);
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(467);








var IIotDynamicRole = /*#__PURE__*/(/* unused pure expression or super */ null && (_createClass(function IIotDynamicRole() {
  _classCallCheck(this, IIotDynamicRole);
  _defineProperty(this, "name", void 0);
  _defineProperty(this, "label", void 0);
  // Data
  _defineProperty(this, "role", void 0);
  // Data
  _defineProperty(this, "iot_customer", void 0);
  // Link
  _defineProperty(this, "sections", void 0);
} // Data
)));
var createCustomerUser = /*#__PURE__*/function () {
  var _ref = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_5__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_7__/* .generateAPIPath */ .rH)('api/v2/register/customer-user-with-role'), {
            method: 'POST',
            data: data
          });
        case 2:
          res = _context.sent;
          return _context.abrupt("return", res);
        case 4:
        case "end":
          return _context.stop();
      }
    }, _callee);
  }));
  return function createCustomerUser(_x) {
    return _ref.apply(this, arguments);
  };
}();
var getCustomerUserList = /*#__PURE__*/function () {
  var _ref2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee2(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          _context2.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_5__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_7__/* .generateAPIPath */ .rH)('api/v2/customerUser/user'), {
            params: (0,_utils__WEBPACK_IMPORTED_MODULE_7__/* .getParamsReqList */ .vj)(params)
          });
        case 2:
          res = _context2.sent;
          return _context2.abrupt("return", res.result);
        case 4:
        case "end":
          return _context2.stop();
      }
    }, _callee2);
  }));
  return function getCustomerUserList(_x2) {
    return _ref2.apply(this, arguments);
  };
}();
var getCustomerUserIndividualList = /*#__PURE__*/(/* unused pure expression or super */ null && (function () {
  var _ref3 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee3(params) {
    var res;
    return _regeneratorRuntime().wrap(function _callee3$(_context3) {
      while (1) switch (_context3.prev = _context3.next) {
        case 0:
          _context3.next = 2;
          return request(generateAPIPath('api/v2/customerUser/user/individual'), {
            params: getParamsReqList(params)
          });
        case 2:
          res = _context3.sent;
          return _context3.abrupt("return", res.result);
        case 4:
        case "end":
          return _context3.stop();
      }
    }, _callee3);
  }));
  return function getCustomerUserIndividualList(_x3) {
    return _ref3.apply(this, arguments);
  };
}()));
function customerUserListAll() {
  return _customerUserListAll.apply(this, arguments);
}

//update customer user
function _customerUserListAll() {
  _customerUserListAll = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee7() {
    var result;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee7$(_context7) {
      while (1) switch (_context7.prev = _context7.next) {
        case 0:
          _context7.prev = 0;
          _context7.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_5__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_7__/* .generateAPIPath */ .rH)("api/v2/customerUser/user"), {
            method: 'GET',
            params: {
              fields: ['*']
            }
          });
        case 3:
          result = _context7.sent;
          return _context7.abrupt("return", result.result);
        case 7:
          _context7.prev = 7;
          _context7.t0 = _context7["catch"](0);
          console.log(_context7.t0);
          throw _context7.t0;
        case 11:
        case "end":
          return _context7.stop();
      }
    }, _callee7, null, [[0, 7]]);
  }));
  return _customerUserListAll.apply(this, arguments);
}
var updateCustomerUser = /*#__PURE__*/function () {
  var _ref4 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee4(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee4$(_context4) {
      while (1) switch (_context4.prev = _context4.next) {
        case 0:
          _context4.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_5__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_7__/* .generateAPIPath */ .rH)('api/v2/customerUser/user'), {
            method: 'PUT',
            data: data
          });
        case 2:
          res = _context4.sent;
          return _context4.abrupt("return", res);
        case 4:
        case "end":
          return _context4.stop();
      }
    }, _callee4);
  }));
  return function updateCustomerUser(_x4) {
    return _ref4.apply(this, arguments);
  };
}();

//delete customer user
var deleteCustomerUser = /*#__PURE__*/(/* unused pure expression or super */ null && (function () {
  var _ref5 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee5(name) {
    var res;
    return _regeneratorRuntime().wrap(function _callee5$(_context5) {
      while (1) switch (_context5.prev = _context5.next) {
        case 0:
          _context5.next = 2;
          return request(generateAPIPath('api/v2/customerUser/user'), {
            method: 'DELETE',
            params: {
              name: name
            }
          });
        case 2:
          res = _context5.sent;
          return _context5.abrupt("return", res);
        case 4:
        case "end":
          return _context5.stop();
      }
    }, _callee5);
  }));
  return function deleteCustomerUser(_x5) {
    return _ref5.apply(this, arguments);
  };
}()));

//delete customer user credential
var deleteCustomerUserCredential = /*#__PURE__*/(/* unused pure expression or super */ null && (function () {
  var _ref6 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee6(name) {
    var res;
    return _regeneratorRuntime().wrap(function _callee6$(_context6) {
      while (1) switch (_context6.prev = _context6.next) {
        case 0:
          _context6.next = 2;
          return request(generateAPIPath('api/v2/customerUser/user-credential'), {
            method: 'DELETE',
            params: {
              name: name
            }
          });
        case 2:
          res = _context6.sent;
          return _context6.abrupt("return", res);
        case 4:
        case "end":
          return _context6.stop();
      }
    }, _callee6);
  }));
  return function deleteCustomerUserCredential(_x6) {
    return _ref6.apply(this, arguments);
  };
}()));
/**\r
 *\r
 * DYNAMIC ROLE APIs\r
 */
function listDynamicRoleAllSection() {
  return _listDynamicRoleAllSection.apply(this, arguments);
}
function _listDynamicRoleAllSection() {
  _listDynamicRoleAllSection = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee8() {
    var result;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee8$(_context8) {
      while (1) switch (_context8.prev = _context8.next) {
        case 0:
          _context8.prev = 0;
          _context8.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_5__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_7__/* .generateAPIPath */ .rH)("api/v2/dynamicRole/listAllSection"), {
            method: 'GET'
          });
        case 3:
          result = _context8.sent;
          return _context8.abrupt("return", result.result);
        case 7:
          _context8.prev = 7;
          _context8.t0 = _context8["catch"](0);
          console.log(_context8.t0);
          throw _context8.t0;
        case 11:
        case "end":
          return _context8.stop();
      }
    }, _callee8, null, [[0, 7]]);
  }));
  return _listDynamicRoleAllSection.apply(this, arguments);
}
function getDynamicRole() {
  return _getDynamicRole.apply(this, arguments);
}
function _getDynamicRole() {
  _getDynamicRole = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee9() {
    var result;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee9$(_context9) {
      while (1) switch (_context9.prev = _context9.next) {
        case 0:
          _context9.prev = 0;
          _context9.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_5__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_7__/* .generateAPIPath */ .rH)("api/v2/dynamicRole"), {
            method: 'GET',
            params: {
              page: 1,
              size: 100
            }
          });
        case 3:
          result = _context9.sent;
          return _context9.abrupt("return", result.result.data);
        case 7:
          _context9.prev = 7;
          _context9.t0 = _context9["catch"](0);
          console.log(_context9.t0);
          throw _context9.t0;
        case 11:
        case "end":
          return _context9.stop();
      }
    }, _callee9, null, [[0, 7]]);
  }));
  return _getDynamicRole.apply(this, arguments);
}
function createDynamicRole(_x7) {
  return _createDynamicRole.apply(this, arguments);
}
function _createDynamicRole() {
  _createDynamicRole = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee10(data) {
    var result;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee10$(_context10) {
      while (1) switch (_context10.prev = _context10.next) {
        case 0:
          _context10.prev = 0;
          _context10.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_5__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_7__/* .generateAPIPath */ .rH)("api/v2/dynamicRole"), {
            method: 'POST',
            data: data
          });
        case 3:
          result = _context10.sent;
          return _context10.abrupt("return", result.result);
        case 7:
          _context10.prev = 7;
          _context10.t0 = _context10["catch"](0);
          console.log(_context10.t0);
          throw _context10.t0;
        case 11:
        case "end":
          return _context10.stop();
      }
    }, _callee10, null, [[0, 7]]);
  }));
  return _createDynamicRole.apply(this, arguments);
}
function updateDynamicRole(_x8) {
  return _updateDynamicRole.apply(this, arguments);
}
function _updateDynamicRole() {
  _updateDynamicRole = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee11(data) {
    var result;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee11$(_context11) {
      while (1) switch (_context11.prev = _context11.next) {
        case 0:
          _context11.prev = 0;
          _context11.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_5__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_7__/* .generateAPIPath */ .rH)("api/v2/dynamicRole"), {
            method: 'PUT',
            data: data
          });
        case 3:
          result = _context11.sent;
          return _context11.abrupt("return", result.result);
        case 7:
          _context11.prev = 7;
          _context11.t0 = _context11["catch"](0);
          console.log(_context11.t0);
          throw _context11.t0;
        case 11:
        case "end":
          return _context11.stop();
      }
    }, _callee11, null, [[0, 7]]);
  }));
  return _updateDynamicRole.apply(this, arguments);
}
function removeDynamicRole(_x9) {
  return _removeDynamicRole.apply(this, arguments);
}
function _removeDynamicRole() {
  _removeDynamicRole = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee12(data) {
    var name, result;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee12$(_context12) {
      while (1) switch (_context12.prev = _context12.next) {
        case 0:
          _context12.prev = 0;
          name = data.name ? data.name : '';
          _context12.next = 4;
          return (0,_sscript__WEBPACK_IMPORTED_MODULE_6__/* .generalDelete */ .ID)('iot_dynamic_role', name);
        case 4:
          result = _context12.sent;
          return _context12.abrupt("return", result);
        case 8:
          _context12.prev = 8;
          _context12.t0 = _context12["catch"](0);
          throw _context12.t0;
        case 11:
        case "end":
          return _context12.stop();
      }
    }, _callee12, null, [[0, 8]]);
  }));
  return _removeDynamicRole.apply(this, arguments);
}//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///40063
`)},96876:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   cT: function() { return /* binding */ uploadFile; },
/* harmony export */   uy: function() { return /* binding */ uploadFileHome; }
/* harmony export */ });
/* unused harmony export removeFileAttachment */
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(7837);
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(467);




var uploadFile = /*#__PURE__*/function () {
  var _ref = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee(data) {
    var formData, res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          formData = new FormData();
          formData.append('doctype', data.docType || '');
          formData.append('docname', data.docName || '');
          formData.append('folder', data.folder || 'Home/Attachments');
          formData.append('is_private', data.isPrivate || '1');
          formData.append('optimize', data.optimize || '0');
          formData.append('file', data.file);
          _context.next = 9;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/file/upload'), {
            method: 'POST',
            data: formData
          });
        case 9:
          res = _context.sent;
          return _context.abrupt("return", {
            data: res.result
          });
        case 11:
        case "end":
          return _context.stop();
      }
    }, _callee);
  }));
  return function uploadFile(_x) {
    return _ref.apply(this, arguments);
  };
}();
var uploadFileHome = /*#__PURE__*/function () {
  var _ref2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee2(data) {
    var formData, res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          formData = new FormData();
          formData.append('folder', data.folder || 'Home/Attachments');
          formData.append('is_private', data.isPrivate || '0');
          formData.append('is_home_folder', data.isPrivate || '1');
          formData.append('doctype', data.docType || '');
          formData.append('docname', data.docName || '');
          formData.append('optimize', data.optimize || '0');
          formData.append('file', data.file);
          _context2.next = 10;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/file/upload'), {
            method: 'POST',
            data: formData
          });
        case 10:
          res = _context2.sent;
          return _context2.abrupt("return", {
            data: res.result
          });
        case 12:
        case "end":
          return _context2.stop();
      }
    }, _callee2);
  }));
  return function uploadFileHome(_x2) {
    return _ref2.apply(this, arguments);
  };
}();
var removeFileAttachment = /*#__PURE__*/(/* unused pure expression or super */ null && (function () {
  var _ref3 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee3(data) {
    var res;
    return _regeneratorRuntime().wrap(function _callee3$(_context3) {
      while (1) switch (_context3.prev = _context3.next) {
        case 0:
          _context3.next = 2;
          return request(generateAPIPath('api/v2/file'), {
            method: 'DELETE',
            data: data
          });
        case 2:
          res = _context3.sent;
          return _context3.abrupt("return", {
            data: res.result
          });
        case 4:
        case "end":
          return _context3.stop();
      }
    }, _callee3);
  }));
  return function removeFileAttachment(_x3) {
    return _ref3.apply(this, arguments);
  };
}()));//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///96876
`)},14682:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   W: function() { return /* binding */ useTaskItemUsedCreateStore; }
/* harmony export */ });
/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(64529);

var useTaskItemUsedCreateStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__/* .create */ .Ue)(function (set, get) {
  return {
    taskItemUsed: [],
    setTaskItemUsed: function setTaskItemUsed(taskItemUsed) {
      console.log('Setting taskItemUsed:', taskItemUsed); // Log the new state
      set({
        taskItemUsed: taskItemUsed
      });
    }
  };
});//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///14682
`)},55059:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   N: function() { return /* binding */ useTaskProductionCreateStore; }
/* harmony export */ });
/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(64529);

var useTaskProductionCreateStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__/* .create */ .Ue)(function (set, get) {
  return {
    taskProduction: [],
    setTaskProduction: function setTaskProduction(taskProduction) {
      console.log('Setting taskProduction:', taskProduction); // Log the new state
      set({
        taskProduction: taskProduction
      });
    }
  };
});//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///55059
`)}}]);
