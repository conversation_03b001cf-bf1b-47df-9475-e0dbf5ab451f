import React from 'react';
import { Card, Typography, Space, Tag, Alert } from 'antd';

const { Title, Text, Paragraph } = Typography;

/**
 * Demo component để minh họa cách active_uom và active_conversion_factor được sử dụng trong Create Workflow
 */
const CreateWorkflowActiveUOMDemo: React.FC = () => {
  // Ví dụ dữ liệu TaskItemUsed trước khi cập nhật
  const beforeTaskItemUsed = {
    iot_category_id: 'fertilizer-npk-001',
    item_name: 'Phân bón NPK 16-16-8',
    label: 'Phân NPK cao cấp',
    uom: 'kg',
    uom_label: 'Kilogram',
    conversion_factor: 1,
    exp_quantity: 10,
    uoms: [
      { uom: 'kg', uom_label: 'Kilogram', conversion_factor: 1 },
      { uom: 'g', uom_label: 'Gram', conversion_factor: 0.001 },
      { uom: 'ton', uom_label: 'Tấn', conversion_factor: 1000 },
    ],
    // Chưa có active_uom và active_conversion_factor
  };

  // Ví dụ dữ liệu TaskItemUsed sau khi cập nhật
  const afterTaskItemUsed = {
    iot_category_id: 'fertilizer-npk-001',
    item_name: 'Phân bón NPK 16-16-8',
    label: 'Phân NPK cao cấp',
    uom: 'kg',
    uom_label: 'Kilogram',
    conversion_factor: 1,
    exp_quantity: 10,
    uoms: [
      { uom: 'kg', uom_label: 'Kilogram', conversion_factor: 1 },
      { uom: 'g', uom_label: 'Gram', conversion_factor: 0.001 },
      { uom: 'ton', uom_label: 'Tấn', conversion_factor: 1000 },
    ],
    active_uom: 'kg', // Thêm active_uom
    active_conversion_factor: 1, // Thêm active_conversion_factor
  };

  // Ví dụ khi người dùng thay đổi UOM từ kg sang g
  const afterUOMChange = {
    iot_category_id: 'fertilizer-npk-001',
    item_name: 'Phân bón NPK 16-16-8',
    label: 'Phân NPK cao cấp',
    uom: 'kg',
    uom_label: 'Gram', // Hiển thị label mới
    conversion_factor: 1,
    exp_quantity: 10,
    uoms: [
      { uom: 'kg', uom_label: 'Kilogram', conversion_factor: 1 },
      { uom: 'g', uom_label: 'Gram', conversion_factor: 0.001 },
      { uom: 'ton', uom_label: 'Tấn', conversion_factor: 1000 },
    ],
    active_uom: 'g', // Cập nhật active_uom
    active_conversion_factor: 0.001, // Cập nhật active_conversion_factor
  };

  // Ví dụ request được gửi đến API createTaskandResourceArray
  const apiRequest = {
    tasks: [
      {
        farming_plan_state: 'state-id',
        label: 'Bón phân NPK',
        description: 'Bón phân NPK cho cây trồng',
        item_list: [
          {
            quantity: 0,
            description: '',
            iot_category_id: 'fertilizer-npk-001',
            exp_quantity: 10, // Giá trị đã được tính toán
            loss_quantity: 0,
            active_uom: 'g', // Gửi active_uom
            active_conversion_factor: 0.001, // Gửi active_conversion_factor
          },
        ],
        prod_quantity_list: [
          {
            quantity: 0,
            description: '',
            product_id: 'rice-001',
            exp_quantity: 500,
            lost_quantity: 0,
            active_uom: 'kg', // Gửi active_uom cho production
            active_conversion_factor: 1, // Gửi active_conversion_factor cho production
          },
        ],
      },
    ],
  };

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>Demo: Active UOM trong Create Workflow</Title>
      
      <Paragraph>
        Tính năng này cho phép người dùng chọn đơn vị đo lường khác nhau khi tạo task 
        và gửi thông tin về đơn vị hiện tại (active_uom) và hệ số chuyển đổi (active_conversion_factor) 
        đến API createTaskandResourceArray.
      </Paragraph>

      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <Alert
          message="Tính năng mới"
          description="API createTaskandResourceArray đã hỗ trợ nhận active_uom và active_conversion_factor cho cả item_list và prod_quantity_list"
          type="success"
          showIcon
        />

        <Card title="1. TaskItemUsed trước khi cập nhật" size="small">
          <pre style={{ background: '#f5f5f5', padding: '12px', borderRadius: '4px' }}>
            {JSON.stringify(beforeTaskItemUsed, null, 2)}
          </pre>
          <Text type="secondary">Chưa có active_uom và active_conversion_factor</Text>
        </Card>

        <Card title="2. TaskItemUsed sau khi khởi tạo" size="small">
          <pre style={{ background: '#f5f5f5', padding: '12px', borderRadius: '4px' }}>
            {JSON.stringify(afterTaskItemUsed, null, 2)}
          </pre>
          <Text type="secondary">
            Đã thêm active_uom và active_conversion_factor với giá trị mặc định
          </Text>
        </Card>

        <Card title="3. TaskItemUsed sau khi người dùng thay đổi UOM" size="small">
          <pre style={{ background: '#f5f5f5', padding: '12px', borderRadius: '4px' }}>
            {JSON.stringify(afterUOMChange, null, 2)}
          </pre>
          <Text type="secondary">
            active_uom và active_conversion_factor được cập nhật theo lựa chọn của người dùng
          </Text>
        </Card>

        <Card title="4. Request gửi đến API" size="small">
          <pre style={{ background: '#f5f5f5', padding: '12px', borderRadius: '4px' }}>
            {JSON.stringify(apiRequest, null, 2)}
          </pre>
          <Text type="secondary">
            API nhận được active_uom và active_conversion_factor cho cả item_list và prod_quantity_list
          </Text>
        </Card>

        <Card title="Các thay đổi đã thực hiện" size="small">
          <ul>
            <li>
              <Text strong>TaskItemUsedCreateStore:</Text> Thêm active_uom và active_conversion_factor vào type TaskItemUsed
            </li>
            <li>
              <Text strong>TaskProductionCreateStore:</Text> Thêm active_uom và active_conversion_factor vào type TaskProduction
            </li>
            <li>
              <Text strong>CreateItemCreateView:</Text> Khởi tạo active fields với giá trị mặc định
            </li>
            <li>
              <Text strong>CreateProductionCreateView:</Text> Khởi tạo active fields với giá trị mặc định
            </li>
            <li>
              <Text strong>ItemUsedTableCreateView:</Text> Thêm Select để chọn UOM và cập nhật active fields
            </li>
            <li>
              <Text strong>ProductionTableCreateView:</Text> Thêm Select để chọn UOM và cập nhật active fields
            </li>
            <li>
              <Text strong>Create/index.tsx:</Text> Gửi active_uom và active_conversion_factor trong request
            </li>
          </ul>
        </Card>

        <Card title="Lợi ích" size="small">
          <ul>
            <li>
              <Tag color="green">Linh hoạt</Tag> Người dùng có thể chọn đơn vị đo lường phù hợp
            </li>
            <li>
              <Tag color="blue">Chính xác</Tag> API biết được đơn vị hiện tại để xử lý đúng
            </li>
            <li>
              <Tag color="orange">Tương thích</Tag> Tương thích ngược với dữ liệu cũ
            </li>
            <li>
              <Tag color="purple">Nhất quán</Tag> Cùng cách thức với các component khác
            </li>
          </ul>
        </Card>
      </Space>
    </div>
  );
};

export default CreateWorkflowActiveUOMDemo;
