"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[4045],{48455:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return /* binding */ Create; }
});

// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/toConsumableArray.js
var toConsumableArray = __webpack_require__(19632);
var toConsumableArray_default = /*#__PURE__*/__webpack_require__.n(toConsumableArray);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/regeneratorRuntime.js
var regeneratorRuntime = __webpack_require__(15009);
var regeneratorRuntime_default = /*#__PURE__*/__webpack_require__.n(regeneratorRuntime);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(99289);
var asyncToGenerator_default = /*#__PURE__*/__webpack_require__.n(asyncToGenerator);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/slicedToArray.js
var slicedToArray = __webpack_require__(5574);
var slicedToArray_default = /*#__PURE__*/__webpack_require__.n(slicedToArray);
// EXTERNAL MODULE: ./src/common/contanst/constanst.ts
var constanst = __webpack_require__(86604);
// EXTERNAL MODULE: ./src/services/cropManager.ts
var cropManager = __webpack_require__(77890);
// EXTERNAL MODULE: ./src/services/farming-plan.ts
var farming_plan = __webpack_require__(74459);
// EXTERNAL MODULE: ./src/services/fileUpload.ts
var services_fileUpload = __webpack_require__(96876);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/layouts/ProForm/index.js + 3 modules
var ProForm = __webpack_require__(34994);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-layout/es/components/PageContainer/index.js + 18 modules
var PageContainer = __webpack_require__(6110);
// EXTERNAL MODULE: ./src/.umi-production/exports.ts + 34 modules
var _umi_production_exports = __webpack_require__(7837);
// EXTERNAL MODULE: ./node_modules/antd/es/app/index.js + 1 modules
var app = __webpack_require__(31418);
// EXTERNAL MODULE: ./node_modules/antd/es/button/index.js + 9 modules
var es_button = __webpack_require__(14726);
// EXTERNAL MODULE: ./node_modules/antd/es/space/index.js + 2 modules
var space = __webpack_require__(42075);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/objectSpread2.js
var objectSpread2 = __webpack_require__(97857);
var objectSpread2_default = /*#__PURE__*/__webpack_require__.n(objectSpread2);
// EXTERNAL MODULE: ./src/services/plantRefAndUserOwner.ts
var plantRefAndUserOwner = __webpack_require__(44045);
// EXTERNAL MODULE: ./src/services/zones.ts
var zones = __webpack_require__(95728);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/CameraFilled.js + 1 modules
var CameraFilled = __webpack_require__(9890);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/UploadButton/index.js + 1 modules
var UploadButton = __webpack_require__(77636);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/Checkbox/index.js
var Checkbox = __webpack_require__(63434);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/Text/index.js
var Text = __webpack_require__(5966);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/Select/index.js
var Select = __webpack_require__(64317);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/DateRangePicker/index.js
var DateRangePicker = __webpack_require__(34540);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/Digit/index.js
var Digit = __webpack_require__(31199);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/TextArea/index.js
var TextArea = __webpack_require__(90672);
// EXTERNAL MODULE: ./node_modules/antd/es/card/index.js + 3 modules
var card = __webpack_require__(76216);
// EXTERNAL MODULE: ./node_modules/antd/es/row/index.js
var row = __webpack_require__(71230);
// EXTERNAL MODULE: ./node_modules/antd/es/col/index.js
var col = __webpack_require__(15746);
// EXTERNAL MODULE: ./node_modules/antd/es/divider/index.js + 1 modules
var divider = __webpack_require__(96074);
// EXTERNAL MODULE: ./node_modules/react/jsx-runtime.js
var jsx_runtime = __webpack_require__(85893);
;// CONCATENATED MODULE: ./src/pages/FarmingManagement/SeasonalManagement/Create/DetailedInfo.tsx













var DetailedInfo = function DetailedInfo(_ref) {
  var children = _ref.children;
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  return /*#__PURE__*/(0,jsx_runtime.jsxs)(card/* default */.Z, {
    title: formatMessage({
      id: 'common.detail'
    }),
    children: [/*#__PURE__*/(0,jsx_runtime.jsx)(ProForm/* ProForm */.A.Group, {
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(UploadButton/* default */.Z, {
        label: formatMessage({
          id: 'common.avatar'
        }),
        accept: "image/*",
        listType: "picture-card",
        icon: /*#__PURE__*/(0,jsx_runtime.jsx)(CameraFilled/* default */.Z, {}),
        title: "",
        name: "avatar",
        max: 1 // Set maximum files to 1
      })
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(ProForm/* ProForm */.A.Group, {
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(Checkbox/* default */.Z, {
        name: "is_template",
        label: formatMessage({
          id: 'common.is_template'
        })
      })
    }), /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
      gutter: 24,
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 12,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(Text/* default */.Z, {
          label: 'T\xEAn v\u1EE5 m\xF9a',
          rules: [{
            required: true
          }],
          name: "label"
        })
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 12,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
          label: "Ch\\u1ECDn khu v\\u1EF1c",
          rules: [{
            required: true
          }],
          showSearch: true,
          request: /*#__PURE__*/asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
            var res;
            return regeneratorRuntime_default()().wrap(function _callee$(_context) {
              while (1) switch (_context.prev = _context.next) {
                case 0:
                  _context.next = 2;
                  return (0,zones/* zoneList */.ly)({
                    size: Number.MAX_SAFE_INTEGER,
                    page: 1
                  });
                case 2:
                  res = _context.sent;
                  return _context.abrupt("return", res.data.map(function (item) {
                    return {
                      label: item.label,
                      value: item.name
                    };
                  }));
                case 4:
                case "end":
                  return _context.stop();
              }
            }, _callee);
          })),
          name: "zone_id"
        })
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 12,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(DateRangePicker/* default */.Z, {
          width: 'xl',
          label: "Th\\u1EDDi gian ho\\xE0n th\\xE0nh",
          rules: [{
            required: true
          }],
          name: "date_range",
          fieldProps: {
            format: constanst/* DEFAULT_DATE_FORMAT_WITHOUT_TIME */.ug
          }
        })
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 12,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(Digit/* default */.Z, {
          label: "Di\\u1EC7n t\\xEDch canh t\\xE1c (m2)",
          min: 0,
          name: "square"
        })
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 12,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(Digit/* default */.Z, {
          label: "S\\u1EA3n l\\u01B0\\u1EE3ng d\\u1EF1 ki\\u1EBFn - Kg",
          min: 0,
          name: "quantity_estimate"
        })
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 12,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
          label: "Ch\\u1ECDn lo\\u1EA1i c\\xE2y tr\\u1ED3ng",
          request: /*#__PURE__*/asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2() {
            var res;
            return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
              while (1) switch (_context2.prev = _context2.next) {
                case 0:
                  _context2.next = 2;
                  return (0,plantRefAndUserOwner/* getPlantUserOwnerAllResources */.A)({
                    size: constanst/* DEFAULT_PAGE_SIZE_ALL */.YY
                  });
                case 2:
                  res = _context2.sent;
                  return _context2.abrupt("return", res.data.map(function (item) {
                    return {
                      label: item.label,
                      value: item.name
                    };
                  }));
                case 4:
                case "end":
                  return _context2.stop();
              }
            }, _callee2);
          })),
          name: "plant_id"
        })
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 12,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
          rules: [{
            required: true
          }],
          label: "Tr\\u1EA1ng th\\xE1i",
          name: "status",
          options: [{
            label: '\u0110ang di\u1EC5n ra',
            value: 'In progress'
          }, {
            label: 'Ho\xE0n t\u1EA5t',
            value: 'Done'
          }],
          initialValue: 'In progress'
        })
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 12,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(TextArea/* default */.Z, {
          width: 'xl',
          name: "description",
          label: "Ghi ch\\xFA",
          placeholder: "Please enter a name"
        })
      })]
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(divider/* default */.Z, {
      type: "horizontal",
      orientation: "left",
      orientationMargin: "0",
      children: formatMessage({
        id: 'common.copy-from-other-crop'
      })
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(row/* default */.Z, {
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 12,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
          allowClear: true,
          showSearch: true
          // label="Sao ch\xE9p t\u1EEB v\u1EE5 m\xF9a kh\xE1c"
          ,
          name: "copy_plan_id",
          request: ( /*#__PURE__*/function () {
            var _ref5 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee3(_ref4) {
              var keyWords, res;
              return regeneratorRuntime_default()().wrap(function _callee3$(_context3) {
                while (1) switch (_context3.prev = _context3.next) {
                  case 0:
                    keyWords = _ref4.keyWords;
                    _context3.next = 3;
                    return (0,farming_plan/* getFarmingPlanFromTemplateCropList */.ag)(objectSpread2_default()({
                      page: 1,
                      size: constanst/* DEFAULT_PAGE_SIZE_ALL */.YY
                    }, keyWords && {
                      filters: "[[\\"".concat(constanst/* DOCTYPE_ERP */.lH.iotFarmingPlan, "\\", \\"label\\", \\"like\\", \\"%").concat(keyWords, "%\\"]]")
                    }));
                  case 3:
                    res = _context3.sent;
                    return _context3.abrupt("return", res.data.map(function (item) {
                      return {
                        label: item.label,
                        value: item.name
                      };
                    }));
                  case 5:
                  case "end":
                    return _context3.stop();
                }
              }, _callee3);
            }));
            return function (_x) {
              return _ref5.apply(this, arguments);
            };
          }())
        })
      })
    })]
  });
};
/* harmony default export */ var Create_DetailedInfo = (DetailedInfo);
;// CONCATENATED MODULE: ./src/pages/FarmingManagement/SeasonalManagement/Create/index.tsx














var CreateCrop = function CreateCrop(_ref) {
  var onSuccess = _ref.onSuccess;
  var _App$useApp = app/* default */.Z.useApp(),
    message = _App$useApp.message;
  var _useState = (0,react.useState)(false),
    _useState2 = slicedToArray_default()(_useState, 2),
    submitting = _useState2[0],
    setSubmitting = _useState2[1];
  var onFinish = /*#__PURE__*/function () {
    var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2(values) {
      var dataCreate, filesUploaded, filesNotUpload, uploadListRes, checkUploadFailed, arrFileUrl, cropId, plan_label, copy_plan_id, dataCreatePlan;
      return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            setSubmitting(true);
            _context2.prev = 1;
            console.log('values,', values);
            // create
            _context2.next = 5;
            return (0,cropManager/* createCrop */.mP)({
              label: values.label,
              zone_id: values.zone_id,
              square: values.square,
              plant_id: values.plant_id,
              start_date: values.date_range[0],
              end_date: values.date_range[1],
              description: values.description,
              status: values.status,
              quantity_estimate: values.quantity_estimate,
              is_template: values.is_template
            });
          case 5:
            dataCreate = _context2.sent;
            if (!(values.avatar && (values.avatar || []).length > 0)) {
              _context2.next = 18;
              break;
            }
            // ki\u1EC3m tra c\xE1c file \u0111\xE3 upload
            filesUploaded = values.avatar.filter(function (item) {
              return !item.originFileObj;
            });
            filesNotUpload = values.avatar.filter(function (item) {
              return item.originFileObj;
            }); // upload b\u1EA5t k\u1EC3 th\xE0nh c\xF4ng hay ko
            _context2.next = 11;
            return Promise.allSettled(filesNotUpload.map( /*#__PURE__*/function () {
              var _ref3 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(item) {
                return regeneratorRuntime_default()().wrap(function _callee$(_context) {
                  while (1) switch (_context.prev = _context.next) {
                    case 0:
                      _context.next = 2;
                      return (0,services_fileUpload/* uploadFile */.cT)({
                        docType: constanst/* DOCTYPE_ERP */.lH.iotCrop,
                        docName: dataCreate.data.name,
                        file: item.originFileObj
                      });
                    case 2:
                      return _context.abrupt("return", _context.sent);
                    case 3:
                    case "end":
                      return _context.stop();
                  }
                }, _callee);
              }));
              return function (_x2) {
                return _ref3.apply(this, arguments);
              };
            }()));
          case 11:
            uploadListRes = _context2.sent;
            // check if() 1 v\xE0i upload failed
            checkUploadFailed = uploadListRes.find(function (item) {
              return item.status === 'rejected';
            });
            if (checkUploadFailed) {
              message.error({
                content: 'Some file upload failed'
              });
            }

            // update avatar path
            arrFileUrl = uploadListRes.reduce(function (prev, item) {
              var _item$value;
              return item.status === 'fulfilled' ? [].concat(toConsumableArray_default()(prev), [item === null || item === void 0 || (_item$value = item.value) === null || _item$value === void 0 || (_item$value = _item$value.data) === null || _item$value === void 0 || (_item$value = _item$value.message) === null || _item$value === void 0 ? void 0 : _item$value.file_url]) : prev;
            }, []).filter(function (item) {
              return typeof item === 'string';
            })
            // th\xEAm file \u0111\xE3 upload
            .concat(filesUploaded.map(function (item) {
              return item.url;
            }));
            if (!(arrFileUrl.length > 0)) {
              _context2.next = 18;
              break;
            }
            _context2.next = 18;
            return (0,cropManager/* updateCrop */.Ak)({
              name: dataCreate.data.name,
              avatar: arrFileUrl[0],
              zone_id: dataCreate.data.zone_id
            });
          case 18:
            /**\r
             * CREATE PLAN\r
             */
            cropId = dataCreate.data.name ? dataCreate.data.name : '';
            plan_label = values.label; // t\xEAn k\u1EBF ho\u1EA1ch tr\xF9ng v\u1EDBi t\xEAn v\u1EE5 m\xF9a
            if (!cropId) {
              _context2.next = 38;
              break;
            }
            copy_plan_id = values.copy_plan_id;
            dataCreatePlan = [];
            if (!copy_plan_id) {
              _context2.next = 35;
              break;
            }
            _context2.prev = 24;
            _context2.next = 27;
            return (0,farming_plan/* createFarmingPlanFromCopy */.Xz)({
              copy_plan_id: copy_plan_id,
              label: plan_label,
              crop: cropId,
              start_date: values.date_range[0],
              end_date: values.date_range[1]
            });
          case 27:
            dataCreatePlan = _context2.sent;
            _context2.next = 33;
            break;
          case 30:
            _context2.prev = 30;
            _context2.t0 = _context2["catch"](24);
            message.warning('V\u1EE5 m\xF9a \u0111\u01B0\u1EE3c t\u1EA1o th\xE0nh c\xF4ng nh\u01B0ng k\u1EBF ho\u1EA1ch ch\u01B0a \u0111\u01B0\u1EE3c t\u1EA1o');
          case 33:
            _context2.next = 38;
            break;
          case 35:
            _context2.next = 37;
            return (0,farming_plan/* createFarmingPlan */.JM)({
              label: plan_label,
              crop: cropId,
              start_date: values.date_range[0],
              end_date: values.date_range[1]
            });
          case 37:
            dataCreatePlan = _context2.sent;
          case 38:
            message.success({
              content: 'Created successfully'
            });
            onSuccess === null || onSuccess === void 0 || onSuccess();
            _umi_production_exports.history.push('/farming-management/seasonal-management');
            return _context2.abrupt("return", true);
          case 44:
            _context2.prev = 44;
            _context2.t1 = _context2["catch"](1);
            return _context2.abrupt("return", false);
          case 47:
            _context2.prev = 47;
            setSubmitting(false);
            return _context2.finish(47);
          case 50:
          case "end":
            return _context2.stop();
        }
      }, _callee2, null, [[1, 44, 47, 50], [24, 30]]);
    }));
    return function onFinish(_x) {
      return _ref2.apply(this, arguments);
    };
  }();
  var _ProForm$useForm = ProForm/* ProForm */.A.useForm(),
    _ProForm$useForm2 = slicedToArray_default()(_ProForm$useForm, 1),
    form = _ProForm$useForm2[0];
  return /*#__PURE__*/(0,jsx_runtime.jsx)(PageContainer/* PageContainer */._z, {
    fixedHeader: true
    // extra={[
    //   <Button key="cancel">H\u1EE7y</Button>,
    //   <Button key="save" type="primary">
    //     L\u01B0u
    //   </Button>,
    // ]}
    ,
    footer: [/*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
      onClick: function onClick() {
        _umi_production_exports.history.back();
      },
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
        id: "common.cancel"
      })
    }, "cancel"), /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
      loading: submitting,
      type: "primary",
      onClick: function onClick() {
        form.submit();
      },
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
        id: "common.save"
      })
    }, "save")],
    children: /*#__PURE__*/(0,jsx_runtime.jsx)(ProForm/* ProForm */.A, {
      onFinish: onFinish,
      submitter: false,
      form: form,
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(space/* default */.Z, {
        size: 'large',
        direction: "vertical",
        style: {
          width: '100%'
        },
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(Create_DetailedInfo, {})
      })
    })
  });
};
/* harmony default export */ var Create = (CreateCrop);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///48455
`)},77890:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Ak: function() { return /* binding */ updateCrop; },
/* harmony export */   Gz: function() { return /* binding */ getCropManagementInfoList; },
/* harmony export */   Ir: function() { return /* binding */ deleteCropNote; },
/* harmony export */   JB: function() { return /* binding */ addParticipantInCrop; },
/* harmony export */   LY: function() { return /* binding */ getTemplateCropList; },
/* harmony export */   No: function() { return /* binding */ getParticipantsInCrop; },
/* harmony export */   TQ: function() { return /* binding */ getCropList; },
/* harmony export */   Tq: function() { return /* binding */ deleteParticipantsInCrop; },
/* harmony export */   WP: function() { return /* binding */ getStatisticNoteList; },
/* harmony export */   bx: function() { return /* binding */ updateCropNote; },
/* harmony export */   mP: function() { return /* binding */ createCrop; },
/* harmony export */   rC: function() { return /* binding */ createCropNote; },
/* harmony export */   vW: function() { return /* binding */ getCurrentStateOfCrop; },
/* harmony export */   xu: function() { return /* binding */ getCropNoteList; }
/* harmony export */ });
/* unused harmony exports updateParticipantsInCrop, getStatisticPestList */
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(7837);
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(467);




var getCropList = /*#__PURE__*/function () {
  var _ref = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/crop'), {
            method: 'GET',
            params: (0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .getParamsReqList */ .vj)(params)
          });
        case 2:
          res = _context.sent;
          return _context.abrupt("return", res.result);
        case 4:
        case "end":
          return _context.stop();
      }
    }, _callee);
  }));
  return function getCropList(_x) {
    return _ref.apply(this, arguments);
  };
}();
var getTemplateCropList = /*#__PURE__*/function () {
  var _ref2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee2(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          _context2.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/crop/template'), {
            method: 'GET',
            params: (0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .getParamsReqList */ .vj)(params)
          });
        case 2:
          res = _context2.sent;
          return _context2.abrupt("return", res.result);
        case 4:
        case "end":
          return _context2.stop();
      }
    }, _callee2);
  }));
  return function getTemplateCropList(_x2) {
    return _ref2.apply(this, arguments);
  };
}();
var getCropManagementInfoList = /*#__PURE__*/function () {
  var _ref3 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee3(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee3$(_context3) {
      while (1) switch (_context3.prev = _context3.next) {
        case 0:
          _context3.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/crop-management-info'), {
            method: 'GET',
            params: params
          });
        case 2:
          res = _context3.sent;
          return _context3.abrupt("return", res.result);
        case 4:
        case "end":
          return _context3.stop();
      }
    }, _callee3);
  }));
  return function getCropManagementInfoList(_x3) {
    return _ref3.apply(this, arguments);
  };
}();
var getCurrentStateOfCrop = /*#__PURE__*/function () {
  var _ref4 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee4(cropId) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee4$(_context4) {
      while (1) switch (_context4.prev = _context4.next) {
        case 0:
          _context4.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/crop-current-state'), {
            method: 'GET',
            params: {
              crop_id: cropId
            }
          });
        case 2:
          res = _context4.sent;
          return _context4.abrupt("return", res.result);
        case 4:
        case "end":
          return _context4.stop();
      }
    }, _callee4);
  }));
  return function getCurrentStateOfCrop(_x4) {
    return _ref4.apply(this, arguments);
  };
}();
var createCrop = /*#__PURE__*/function () {
  var _ref5 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee5(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee5$(_context5) {
      while (1) switch (_context5.prev = _context5.next) {
        case 0:
          _context5.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/crop'), {
            method: 'POST',
            data: data
          });
        case 2:
          res = _context5.sent;
          return _context5.abrupt("return", res.result);
        case 4:
        case "end":
          return _context5.stop();
      }
    }, _callee5);
  }));
  return function createCrop(_x5) {
    return _ref5.apply(this, arguments);
  };
}();
var updateCrop = /*#__PURE__*/function () {
  var _ref6 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee6(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee6$(_context6) {
      while (1) switch (_context6.prev = _context6.next) {
        case 0:
          _context6.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/crop'), {
            method: 'PUT',
            data: data
          });
        case 2:
          res = _context6.sent;
          return _context6.abrupt("return", res.result);
        case 4:
        case "end":
          return _context6.stop();
      }
    }, _callee6);
  }));
  return function updateCrop(_x6) {
    return _ref6.apply(this, arguments);
  };
}();
var getCropNoteList = /*#__PURE__*/function () {
  var _ref7 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee7(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee7$(_context7) {
      while (1) switch (_context7.prev = _context7.next) {
        case 0:
          _context7.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/note'), {
            method: 'GET',
            params: (0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .getParamsReqList */ .vj)(params)
          });
        case 2:
          res = _context7.sent;
          return _context7.abrupt("return", res.result);
        case 4:
        case "end":
          return _context7.stop();
      }
    }, _callee7);
  }));
  return function getCropNoteList(_x7) {
    return _ref7.apply(this, arguments);
  };
}();
var createCropNote = /*#__PURE__*/function () {
  var _ref8 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee8(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee8$(_context8) {
      while (1) switch (_context8.prev = _context8.next) {
        case 0:
          _context8.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/note'), {
            method: 'POST',
            data: data
          });
        case 2:
          res = _context8.sent;
          return _context8.abrupt("return", res.result);
        case 4:
        case "end":
          return _context8.stop();
      }
    }, _callee8);
  }));
  return function createCropNote(_x8) {
    return _ref8.apply(this, arguments);
  };
}();
var updateCropNote = /*#__PURE__*/function () {
  var _ref9 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee9(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee9$(_context9) {
      while (1) switch (_context9.prev = _context9.next) {
        case 0:
          _context9.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/note'), {
            method: 'PUT',
            data: data
          });
        case 2:
          res = _context9.sent;
          return _context9.abrupt("return", res.result);
        case 4:
        case "end":
          return _context9.stop();
      }
    }, _callee9);
  }));
  return function updateCropNote(_x9) {
    return _ref9.apply(this, arguments);
  };
}();
var deleteCropNote = /*#__PURE__*/function () {
  var _ref11 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee10(_ref10) {
    var name, res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee10$(_context10) {
      while (1) switch (_context10.prev = _context10.next) {
        case 0:
          name = _ref10.name;
          _context10.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)("api/v2/cropManage/note?name=".concat(name)), {
            method: 'DELETE'
          });
        case 3:
          res = _context10.sent;
          return _context10.abrupt("return", res.result);
        case 5:
        case "end":
          return _context10.stop();
      }
    }, _callee10);
  }));
  return function deleteCropNote(_x10) {
    return _ref11.apply(this, arguments);
  };
}();
// Participants
var getParticipantsInCrop = /*#__PURE__*/function () {
  var _ref12 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee11(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee11$(_context11) {
      while (1) switch (_context11.prev = _context11.next) {
        case 0:
          _context11.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/iot_employee_in_crop'), {
            method: 'GET',
            params: (0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .getParamsReqList */ .vj)(params)
          });
        case 2:
          res = _context11.sent;
          return _context11.abrupt("return", res.result);
        case 4:
        case "end":
          return _context11.stop();
      }
    }, _callee11);
  }));
  return function getParticipantsInCrop(_x11) {
    return _ref12.apply(this, arguments);
  };
}();
var addParticipantInCrop = /*#__PURE__*/function () {
  var _ref13 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee12(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee12$(_context12) {
      while (1) switch (_context12.prev = _context12.next) {
        case 0:
          _context12.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/iot_employee_in_crop'), {
            method: 'POST',
            data: data
          });
        case 2:
          res = _context12.sent;
          return _context12.abrupt("return", res);
        case 4:
        case "end":
          return _context12.stop();
      }
    }, _callee12);
  }));
  return function addParticipantInCrop(_x12) {
    return _ref13.apply(this, arguments);
  };
}();
var updateParticipantsInCrop = /*#__PURE__*/(/* unused pure expression or super */ null && (function () {
  var _ref14 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee13(data) {
    var res;
    return _regeneratorRuntime().wrap(function _callee13$(_context13) {
      while (1) switch (_context13.prev = _context13.next) {
        case 0:
          _context13.next = 2;
          return request(generateAPIPath('api/v2/cropManage/iot_employee_in_crop'), {
            method: 'PUT',
            data: data
          });
        case 2:
          res = _context13.sent;
          return _context13.abrupt("return", res);
        case 4:
        case "end":
          return _context13.stop();
      }
    }, _callee13);
  }));
  return function updateParticipantsInCrop(_x13) {
    return _ref14.apply(this, arguments);
  };
}()));
var deleteParticipantsInCrop = /*#__PURE__*/function () {
  var _ref15 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee14(name) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee14$(_context14) {
      while (1) switch (_context14.prev = _context14.next) {
        case 0:
          _context14.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/iot_employee_in_crop'), {
            method: 'DELETE',
            params: {
              name: name
            }
          });
        case 2:
          res = _context14.sent;
          return _context14.abrupt("return", res);
        case 4:
        case "end":
          return _context14.stop();
      }
    }, _callee14);
  }));
  return function deleteParticipantsInCrop(_x14) {
    return _ref15.apply(this, arguments);
  };
}();
var getStatisticPestList = /*#__PURE__*/(/* unused pure expression or super */ null && (function () {
  var _ref16 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee15(params) {
    var res;
    return _regeneratorRuntime().wrap(function _callee15$(_context15) {
      while (1) switch (_context15.prev = _context15.next) {
        case 0:
          _context15.next = 2;
          return request(generateAPIPath("api/v2/cropManage/statisticPestList?crop_id=".concat(params === null || params === void 0 ? void 0 : params.cropId)), {
            method: 'GET',
            params: getParamsReqList(params)
          });
        case 2:
          res = _context15.sent;
          return _context15.abrupt("return", res.result);
        case 4:
        case "end":
          return _context15.stop();
      }
    }, _callee15);
  }));
  return function getStatisticPestList(_x15) {
    return _ref16.apply(this, arguments);
  };
}()));
var getStatisticNoteList = /*#__PURE__*/function () {
  var _ref17 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee16(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee16$(_context16) {
      while (1) switch (_context16.prev = _context16.next) {
        case 0:
          _context16.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)("api/v2/cropManage/statisticNoteList?crop_id=".concat(params === null || params === void 0 ? void 0 : params.cropId)), {
            method: 'GET',
            params: (0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .getParamsReqList */ .vj)(params)
          });
        case 2:
          res = _context16.sent;
          return _context16.abrupt("return", res.result);
        case 4:
        case "end":
          return _context16.stop();
      }
    }, _callee16);
  }));
  return function getStatisticNoteList(_x16) {
    return _ref17.apply(this, arguments);
  };
}();//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///77890
`)},96876:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   cT: function() { return /* binding */ uploadFile; },
/* harmony export */   uy: function() { return /* binding */ uploadFileHome; }
/* harmony export */ });
/* unused harmony export removeFileAttachment */
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(7837);
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(467);




var uploadFile = /*#__PURE__*/function () {
  var _ref = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee(data) {
    var formData, res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          formData = new FormData();
          formData.append('doctype', data.docType || '');
          formData.append('docname', data.docName || '');
          formData.append('folder', data.folder || 'Home/Attachments');
          formData.append('is_private', data.isPrivate || '1');
          formData.append('optimize', data.optimize || '0');
          formData.append('file', data.file);
          _context.next = 9;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/file/upload'), {
            method: 'POST',
            data: formData
          });
        case 9:
          res = _context.sent;
          return _context.abrupt("return", {
            data: res.result
          });
        case 11:
        case "end":
          return _context.stop();
      }
    }, _callee);
  }));
  return function uploadFile(_x) {
    return _ref.apply(this, arguments);
  };
}();
var uploadFileHome = /*#__PURE__*/function () {
  var _ref2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee2(data) {
    var formData, res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          formData = new FormData();
          formData.append('folder', data.folder || 'Home/Attachments');
          formData.append('is_private', data.isPrivate || '0');
          formData.append('is_home_folder', data.isPrivate || '1');
          formData.append('doctype', data.docType || '');
          formData.append('docname', data.docName || '');
          formData.append('optimize', data.optimize || '0');
          formData.append('file', data.file);
          _context2.next = 10;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/file/upload'), {
            method: 'POST',
            data: formData
          });
        case 10:
          res = _context2.sent;
          return _context2.abrupt("return", {
            data: res.result
          });
        case 12:
        case "end":
          return _context2.stop();
      }
    }, _callee2);
  }));
  return function uploadFileHome(_x2) {
    return _ref2.apply(this, arguments);
  };
}();
var removeFileAttachment = /*#__PURE__*/(/* unused pure expression or super */ null && (function () {
  var _ref3 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee3(data) {
    var res;
    return _regeneratorRuntime().wrap(function _callee3$(_context3) {
      while (1) switch (_context3.prev = _context3.next) {
        case 0:
          _context3.next = 2;
          return request(generateAPIPath('api/v2/file'), {
            method: 'DELETE',
            data: data
          });
        case 2:
          res = _context3.sent;
          return _context3.abrupt("return", {
            data: res.result
          });
        case 4:
        case "end":
          return _context3.stop();
      }
    }, _callee3);
  }));
  return function removeFileAttachment(_x3) {
    return _ref3.apply(this, arguments);
  };
}()));//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///96876
`)},44045:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: function() { return /* binding */ getPlantUserOwnerAllResources; }
/* harmony export */ });
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(97857);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectWithoutProperties_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(13769);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectWithoutProperties_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectWithoutProperties_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(7837);
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(467);




var _excluded = ["plant_id"];


var getPlantUserOwnerAllResources = /*#__PURE__*/function () {
  var _ref2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee(_ref) {
    var plant_id, params, res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          plant_id = _ref.plant_id, params = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectWithoutProperties_js__WEBPACK_IMPORTED_MODULE_2___default()(_ref, _excluded);
          _context.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_4__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_5__/* .generateAPIPath */ .rH)("api/v2/plantManage/plant/allResources"), {
            method: 'GET',
            params: D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({
              plant_id: plant_id
            }, (0,_utils__WEBPACK_IMPORTED_MODULE_5__/* .getParamsReqList */ .vj)(params))
          });
        case 3:
          res = _context.sent;
          return _context.abrupt("return", {
            data: res.result.data,
            pagination: res.result.pagination
          });
        case 5:
        case "end":
          return _context.stop();
      }
    }, _callee);
  }));
  return function getPlantUserOwnerAllResources(_x) {
    return _ref2.apply(this, arguments);
  };
}();//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///44045
`)}}]);
