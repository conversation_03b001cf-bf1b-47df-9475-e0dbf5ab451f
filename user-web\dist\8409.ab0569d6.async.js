"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[8409],{58409:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`
// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  Z: function() { return /* binding */ components_ExportVoucherDetailEnhanced; }
});

// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/objectSpread2.js
var objectSpread2 = __webpack_require__(97857);
var objectSpread2_default = /*#__PURE__*/__webpack_require__.n(objectSpread2);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/defineProperty.js
var defineProperty = __webpack_require__(9783);
var defineProperty_default = /*#__PURE__*/__webpack_require__.n(defineProperty);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/regeneratorRuntime.js
var regeneratorRuntime = __webpack_require__(15009);
var regeneratorRuntime_default = /*#__PURE__*/__webpack_require__.n(regeneratorRuntime);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(99289);
var asyncToGenerator_default = /*#__PURE__*/__webpack_require__.n(asyncToGenerator);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/slicedToArray.js
var slicedToArray = __webpack_require__(5574);
var slicedToArray_default = /*#__PURE__*/__webpack_require__.n(slicedToArray);
// EXTERNAL MODULE: ./src/components/ConfirmMdal/index.tsx
var ConfirmMdal = __webpack_require__(60461);
// EXTERNAL MODULE: ./src/services/stock/deliveryNote.ts
var deliveryNote = __webpack_require__(14329);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/layouts/ModalForm/index.js
var ModalForm = __webpack_require__(37476);
// EXTERNAL MODULE: ./src/.umi-production/exports.ts + 34 modules
var _umi_production_exports = __webpack_require__(7837);
// EXTERNAL MODULE: ./node_modules/antd/es/form/index.js + 18 modules
var es_form = __webpack_require__(98138);
// EXTERNAL MODULE: ./node_modules/antd/es/button/index.js + 9 modules
var es_button = __webpack_require__(14726);
// EXTERNAL MODULE: ./node_modules/antd/es/spin/index.js + 2 modules
var spin = __webpack_require__(75081);
// EXTERNAL MODULE: ./node_modules/dayjs/dayjs.min.js
var dayjs_min = __webpack_require__(27484);
var dayjs_min_default = /*#__PURE__*/__webpack_require__.n(dayjs_min);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./src/pages/WarehouseManagementV3/Inventory/components/StockActionButton/index.tsx
var StockActionButton = __webpack_require__(858);
// EXTERNAL MODULE: ./src/pages/WarehouseManagementV3/Inventory/components/StockActionButton/voucherActions.ts
var voucherActions = __webpack_require__(24234);
// EXTERNAL MODULE: ./src/common/contanst/constanst.ts
var constanst = __webpack_require__(86604);
// EXTERNAL MODULE: ./node_modules/antd/es/tag/index.js + 5 modules
var tag = __webpack_require__(66309);
// EXTERNAL MODULE: ./node_modules/react/jsx-runtime.js
var jsx_runtime = __webpack_require__(85893);
;// CONCATENATED MODULE: ./src/pages/WarehouseManagementV3/Inventory/ExportHistory/components/ExportVoucherDetailEnhanced/components/DocStatusTag.tsx




var DocStatusTag = function DocStatusTag(_ref) {
  var status = _ref.status;
  var tagColor = constanst/* DOC_STATUS_COLOR */.IZ[status];
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  var docStatusLabel = formatMessage({
    id: "common.doc_status.".concat(status)
  });
  return /*#__PURE__*/(0,jsx_runtime.jsx)(tag/* default */.Z, {
    color: tagColor,
    children: docStatusLabel
  });
};
/* harmony default export */ var components_DocStatusTag = (DocStatusTag);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/toConsumableArray.js
var toConsumableArray = __webpack_require__(19632);
var toConsumableArray_default = /*#__PURE__*/__webpack_require__.n(toConsumableArray);
// EXTERNAL MODULE: ./src/services/stock/item.ts
var item = __webpack_require__(89436);
// EXTERNAL MODULE: ./node_modules/antd/es/message/index.js
var message = __webpack_require__(45360);
// EXTERNAL MODULE: ./node_modules/zustand/esm/index.mjs + 1 modules
var esm = __webpack_require__(64529);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/createForOfIteratorHelper.js
var createForOfIteratorHelper = __webpack_require__(64599);
var createForOfIteratorHelper_default = /*#__PURE__*/__webpack_require__.n(createForOfIteratorHelper);
// EXTERNAL MODULE: ./src/services/customerUser.ts
var customerUser = __webpack_require__(40063);
// EXTERNAL MODULE: ./src/services/InventoryManagementV3/customer.ts
var customer = __webpack_require__(23079);
// EXTERNAL MODULE: ./src/services/stock/warehouse.ts
var stock_warehouse = __webpack_require__(18327);
;// CONCATENATED MODULE: ./src/pages/WarehouseManagementV3/Inventory/ExportHistory/components/ExportVoucherDetailEnhanced/utils/helpers.ts



// utils/helpers.ts






var fetchWarehouseList = /*#__PURE__*/function () {
  var _ref = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
    var warehouse;
    return regeneratorRuntime_default()().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.next = 2;
          return (0,stock_warehouse/* getWarehouseList */.Aq)();
        case 2:
          warehouse = _context.sent;
          return _context.abrupt("return", warehouse.data.map(function (storage) {
            return {
              label: storage.label,
              value: storage.name
            };
          }));
        case 4:
        case "end":
          return _context.stop();
      }
    }, _callee);
  }));
  return function fetchWarehouseList() {
    return _ref.apply(this, arguments);
  };
}();
var fetchCustomerUserList = /*#__PURE__*/function () {
  var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2() {
    var listUser;
    return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          _context2.next = 2;
          return (0,customerUser/* getCustomerUserList */.J9)();
        case 2:
          listUser = _context2.sent;
          return _context2.abrupt("return", listUser.data.map(function (item) {
            return {
              label: "".concat(item.first_name, " ").concat(item.last_name),
              value: item.name
            };
          }));
        case 4:
        case "end":
          return _context2.stop();
      }
    }, _callee2);
  }));
  return function fetchCustomerUserList() {
    return _ref2.apply(this, arguments);
  };
}();
var fetchCustomerV3 = /*#__PURE__*/function () {
  var _ref3 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee3() {
    var supplier;
    return regeneratorRuntime_default()().wrap(function _callee3$(_context3) {
      while (1) switch (_context3.prev = _context3.next) {
        case 0:
          _context3.next = 2;
          return (0,customer/* getCustomerV3 */.o1)();
        case 2:
          supplier = _context3.sent;
          return _context3.abrupt("return", supplier);
        case 4:
        case "end":
          return _context3.stop();
      }
    }, _callee3);
  }));
  return function fetchCustomerV3() {
    return _ref3.apply(this, arguments);
  };
}();

/**\r
 * Gets posting date and time from the form date value\r
 */
var getPostingDateTime = function getPostingDateTime(formDate) {
  var date = moment(formDate, DEFAULT_DATE_AND_HH_MM_FORMAT);
  var posting_date = date.format('YYYY-MM-DD');

  // Get hours and minutes from the date
  var hoursAndMinutes = date.format('HH:mm');

  // Get current seconds
  var currentSeconds = moment().format('ss');

  // Combine hours, minutes and seconds for posting_time
  var posting_time = "".concat(hoursAndMinutes, ":").concat(currentSeconds);
  return {
    posting_date: posting_date,
    posting_time: posting_time
  };
};

/**\r
 * Creates initial export voucher object\r
 */
var createExportVoucher = function createExportVoucher(values, selectedItems, posting_date, posting_time) {
  return {
    __isLocal: 1,
    __unsaved: 1,
    posting_date: posting_date,
    posting_time: posting_time,
    set_posting_time: 1,
    company: 'VIIS',
    description: values.description,
    iot_customer_user: values.employee,
    file_path: values.file_path,
    doctype: 'Delivery Note',
    customer: values.customer,
    set_warehouse: values.warehouse,
    items: selectedItems.map(function (item) {
      return {
        doctype: 'Delivery Note Item',
        name: 'new-delivery-note-item' + generateRandomString(),
        warehouse: values.warehouse,
        conversion_factor: item.conversion_factor,
        item_code: item.item_code,
        qty: item.qty,
        rate: item.rate,
        uom: item.uom
      };
    }),
    add_taxes: values.add_taxes,
    other_charges: values.other_charges,
    paid_amount: values.paid_amount,
    discount: values.discount,
    have_transaction: true
  };
};

/**\r
 * Creates submitting export voucher object from saved response\r
 */
var createSubmittingExportVoucher = function createSubmittingExportVoucher(saveRes) {
  return {
    set_posting_time: 1,
    name: saveRes.name,
    owner: saveRes.owner,
    creation: saveRes.creation,
    modified: saveRes.modified,
    modified_by: saveRes.modified_by,
    naming_series: saveRes.naming_series,
    posting_date: saveRes.posting_date,
    posting_time: saveRes.posting_time,
    status: 'Draft',
    company: saveRes.company,
    description: saveRes.description,
    file_path: saveRes.file_path,
    have_transaction: saveRes.have_transaction,
    add_taxes: saveRes.add_taxes,
    other_charges: saveRes.other_charges,
    paid_amount: saveRes.paid_amount,
    discount: saveRes.discount,
    doctype: 'Delivery Note',
    customer: saveRes.customer,
    customer_name: saveRes.customer_name,
    selling_price_list: saveRes.selling_price_list,
    price_list_currency: saveRes.price_list_currency,
    plc_conversion_rate: saveRes.plc_conversion_rate,
    set_warehouse: saveRes.set_warehouse,
    items: saveRes.items.map(function (item) {
      return {
        name: item.name,
        item_code: item.item_code,
        qty: item.qty,
        conversion_factor: item.conversion_factor,
        rate: item.rate,
        warehouse: item.warehouse,
        s_warehouse: item.s_warehouse,
        doctype: item.doctype,
        uom: item.uom
      };
    })
  };
};

// Helper function to generate random string
var generateRandomString = function generateRandomString() {
  return Math.random().toString(36).substring(7);
};
function groupByItemGroupWithItemGroupData(stockItems, stockItemGroups) {
  var groupedItems = {};
  stockItems.forEach(function (item) {
    var itemGroup = item.item_group;
    if (!groupedItems[itemGroup]) {
      groupedItems[itemGroup] = {
        items: [],
        itemGroup: {}
      };
    }
    groupedItems[itemGroup].items.push(item);
  });

  // Now add item group data
  var _iterator = createForOfIteratorHelper_default()(stockItemGroups),
    _step;
  try {
    for (_iterator.s(); !(_step = _iterator.n()).done;) {
      var itemGroup = _step.value;
      var itemGroupName = itemGroup.item_group_name;
      if (groupedItems[itemGroupName]) {
        groupedItems[itemGroupName].itemGroup = itemGroup;
      }
    }
  } catch (err) {
    _iterator.e(err);
  } finally {
    _iterator.f();
  }
  return groupedItems;
}
;// CONCATENATED MODULE: ./src/pages/WarehouseManagementV3/Inventory/ExportHistory/components/ExportVoucherDetailEnhanced/stores/exportVoucherDetailStore.tsx





// stores/exportVoucherStore.ts





var initialState = {
  // Form state
  form: null,
  submitting: false,
  // Items state
  selectedItems: [],
  treeData: [],
  items: [],
  // Selection state
  selectedWarehouse: '',
  selectedCustomer: '',
  // Customer state
  customerList: [],
  customerTotalOutstandingAmount: 0,
  remainOutstandingAmount: 0,
  // Financial state
  addTaxes: 0,
  otherCharges: 0,
  paidAmount: 0,
  discountAmount: 0,
  discountType: 'amount',
  taxType: 'amount',
  voucherAmount: 0,
  totalAmount: 0,
  // Data fetching state
  isItemTreeDataLoaded: false,
  isInitializing: false,
  isSaved: false,
  savedVoucherData: null,
  docstatus: 0
};
var useExportVoucherDetailStore = (0,esm/* create */.Ue)()(function (set, get) {
  return objectSpread2_default()(objectSpread2_default()({}, initialState), {}, {
    // Basic actions
    setForm: function setForm(form) {
      return set({
        form: form
      });
    },
    setSubmitting: function setSubmitting(submitting) {
      return set({
        submitting: submitting
      });
    },
    setSelectedItems: function setSelectedItems(items) {
      return set({
        selectedItems: items
      });
    },
    setTreeData: function setTreeData(data) {
      return set({
        treeData: data
      });
    },
    setItems: function setItems(items) {
      return set({
        items: items
      });
    },
    // Enhanced Warehouse Selection
    setSelectedWarehouse: function setSelectedWarehouse(warehouse) {
      var state = get();
      if (warehouse !== state.selectedWarehouse) {
        set({
          selectedWarehouse: warehouse,
          selectedItems: [],
          isItemTreeDataLoaded: false
        });
        state.fetchItemTreeData(warehouse);
      }
    },
    // Enhanced Customer Management
    setCustomerList: function setCustomerList(customers) {
      console.log('start to set customer list', customers);
      return set({
        customerList: customers
      });
    },
    setSelectedCustomer: function setSelectedCustomer(customer) {
      console.log('start set selected customer', customer);
      set({
        selectedCustomer: customer
      });
      get().updateCustomerTotalOutstandingAmount(customer);
    },
    updateCustomerTotalOutstandingAmount: function updateCustomerTotalOutstandingAmount(customerId) {
      try {
        var state = get();
        var _customer = state.customerList.find(function (item) {
          return item.name === customerId;
        });
        if (_customer) {
          set({
            customerTotalOutstandingAmount: _customer.total_outstanding_amount,
            remainOutstandingAmount: _customer.total_outstanding_amount
          });
        } else {
          console.warn("Customer ".concat(customerId, " not found in customerList"));
          set({
            customerTotalOutstandingAmount: 0,
            remainOutstandingAmount: 0
          });
        }
      } catch (error) {
        console.error('Error updating customer total outstanding amount:', error);
        set({
          customerTotalOutstandingAmount: 0,
          remainOutstandingAmount: 0
        });
      }
    },
    // Enhanced Item Management
    addItem: function addItem(item) {
      var state = get();
      var existingItem = state.selectedItems.find(function (i) {
        return i.item_code === item.item_code;
      });
      if (!existingItem) {
        set({
          selectedItems: [].concat(toConsumableArray_default()(state.selectedItems), [item])
        });
      } else {
        message/* default */.ZP.warning('Item already exists in the list');
      }
    },
    removeItem: function removeItem(itemCode) {
      var state = get();
      set({
        selectedItems: state.selectedItems.filter(function (item) {
          return item.item_code !== itemCode;
        })
      });
    },
    updateItemQuantity: function updateItemQuantity(itemCode, quantity) {
      var state = get();
      var updatedItems = state.selectedItems.map(function (item) {
        if (item.item_code === itemCode) {
          var total_price = quantity * item.rate;
          return objectSpread2_default()(objectSpread2_default()({}, item), {}, {
            qty: quantity,
            total_price: total_price
          });
        }
        return item;
      });
      set({
        selectedItems: updatedItems
      });
    },
    updateItemPrice: function updateItemPrice(itemCode, price) {
      var state = get();
      var updatedItems = state.selectedItems.map(function (item) {
        if (item.item_code === itemCode) {
          var total_price = item.qty * price;
          return objectSpread2_default()(objectSpread2_default()({}, item), {}, {
            rate: price,
            total_price: total_price
          });
        }
        return item;
      });
      set({
        selectedItems: updatedItems
      });
    },
    // Financial actions
    setCustomerTotalOutstandingAmount: function setCustomerTotalOutstandingAmount(amount) {
      return set({
        customerTotalOutstandingAmount: amount
      });
    },
    setRemainOutstandingAmount: function setRemainOutstandingAmount(amount) {
      return set({
        remainOutstandingAmount: amount
      });
    },
    setAddTaxes: function setAddTaxes(amount) {
      return set({
        addTaxes: amount
      });
    },
    setOtherCharges: function setOtherCharges(amount) {
      return set({
        otherCharges: amount
      });
    },
    setPaidAmount: function setPaidAmount(amount) {
      return set({
        paidAmount: amount
      });
    },
    setDiscountAmount: function setDiscountAmount(amount) {
      return set({
        discountAmount: amount
      });
    },
    setDiscountType: function setDiscountType(type) {
      return set({
        discountType: type
      });
    },
    setTaxType: function setTaxType(type) {
      return set({
        taxType: type
      });
    },
    setTotalAmount: function setTotalAmount(amount) {
      return set({
        totalAmount: amount
      });
    },
    setVoucherAmount: function setVoucherAmount(amount) {
      return set({
        voucherAmount: amount
      });
    },
    // Status actions
    setIsItemTreeDataLoaded: function setIsItemTreeDataLoaded(loaded) {
      return set({
        isItemTreeDataLoaded: loaded
      });
    },
    setSaved: function setSaved(isSaved) {
      return set({
        isSaved: isSaved
      });
    },
    setSavedVoucherData: function setSavedVoucherData(data) {
      console.log('start to set saved voucher data', data);
      return set({
        savedVoucherData: data
      });
    },
    setDocstatus: function setDocstatus(status) {
      return set({
        docstatus: status
      });
    },
    // Enhanced fetch data action
    fetchItemTreeData: function () {
      var _fetchItemTreeData = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(warehouseId) {
        var state, _yield$Promise$all, _yield$Promise$all2, itemsResponse, groupsResponse, dataMap, generatedData;
        return regeneratorRuntime_default()().wrap(function _callee$(_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              state = get();
              if (!(state.isInitializing || state.isItemTreeDataLoaded)) {
                _context.next = 3;
                break;
              }
              return _context.abrupt("return");
            case 3:
              set({
                isInitializing: true
              });
              _context.prev = 4;
              _context.next = 7;
              return Promise.all([(0,item/* getItemList */.m)({}), (0,item/* getItemGroupList */.A)({})]);
            case 7:
              _yield$Promise$all = _context.sent;
              _yield$Promise$all2 = slicedToArray_default()(_yield$Promise$all, 2);
              itemsResponse = _yield$Promise$all2[0];
              groupsResponse = _yield$Promise$all2[1];
              dataMap = groupByItemGroupWithItemGroupData(itemsResponse.data, groupsResponse.data);
              if (dataMap) {
                generatedData = Object.entries(dataMap).map(function (_ref) {
                  var _groupData$itemGroup;
                  var _ref2 = slicedToArray_default()(_ref, 2),
                    itemGroup = _ref2[0],
                    groupData = _ref2[1];
                  return {
                    title: ((_groupData$itemGroup = groupData.itemGroup) === null || _groupData$itemGroup === void 0 ? void 0 : _groupData$itemGroup.label) || '',
                    value: itemGroup,
                    key: itemGroup,
                    children: groupData.items.map(function (item) {
                      return {
                        title: item.label || '',
                        value: item.name || '',
                        key: item.name || ''
                      };
                    })
                  };
                });
                set({
                  treeData: generatedData,
                  items: itemsResponse.data,
                  isItemTreeDataLoaded: true,
                  selectedWarehouse: warehouseId
                });
              }
              _context.next = 19;
              break;
            case 15:
              _context.prev = 15;
              _context.t0 = _context["catch"](4);
              message/* default */.ZP.error('Failed to fetch item data');
              set({
                isItemTreeDataLoaded: false,
                selectedWarehouse: '',
                treeData: [],
                items: []
              });
            case 19:
              _context.prev = 19;
              set({
                isInitializing: false
              });
              return _context.finish(19);
            case 22:
            case "end":
              return _context.stop();
          }
        }, _callee, null, [[4, 15, 19, 22]]);
      }));
      function fetchItemTreeData(_x) {
        return _fetchItemTreeData.apply(this, arguments);
      }
      return fetchItemTreeData;
    }(),
    // Enhanced calculation methods
    calculateTotalBill: function calculateTotalBill() {
      var state = get();
      var itemsTotal = state.selectedItems.reduce(function (acc, item) {
        return acc + (item.total_price || 0);
      }, 0);
      console.log('debug itemsTotal', itemsTotal);
      console.log('debug totalbill', itemsTotal, state.addTaxes, state.otherCharges, state.discountAmount);
      return itemsTotal + state.addTaxes + state.otherCharges - state.discountAmount;
    },
    calculateTaxAmount: function calculateTaxAmount() {
      var state = get();
      if (state.taxType === 'percentage') {
        var itemsTotal = state.selectedItems.reduce(function (acc, item) {
          return acc + (item.total_price || 0);
        }, 0);
        return state.addTaxes / 100 * itemsTotal;
      }
      return state.addTaxes;
    },
    calculateRemainOutstandingAmount: function calculateRemainOutstandingAmount() {
      var state = get();
      var totalBill = state.calculateTotalBill();
      return state.customerTotalOutstandingAmount + totalBill - state.paidAmount;
    },
    // Enhanced reset
    reset: function reset() {
      return set(objectSpread2_default()(objectSpread2_default()({}, initialState), {}, {
        isItemTreeDataLoaded: false,
        isInitializing: false,
        isSaved: false,
        savedVoucherData: null,
        customerList: [] // Clear customer list on reset
      }));
    }
  });
});
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/DateTimePicker/index.js
var DateTimePicker = __webpack_require__(22452);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/Select/index.js
var Select = __webpack_require__(64317);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/TextArea/index.js
var TextArea = __webpack_require__(90672);
// EXTERNAL MODULE: ./node_modules/antd/es/row/index.js
var row = __webpack_require__(71230);
// EXTERNAL MODULE: ./node_modules/antd/es/col/index.js
var col = __webpack_require__(15746);
// EXTERNAL MODULE: ./src/services/InventoryManagementV3/inventory.ts
var InventoryManagementV3_inventory = __webpack_require__(17322);
;// CONCATENATED MODULE: ./src/pages/WarehouseManagementV3/Inventory/ExportHistory/components/ExportVoucherDetailEnhanced/utils/formatters.ts
// utils/formatters.ts
var formatCurrency = function formatCurrency(amount) {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND'
  }).format(amount);
};
;// CONCATENATED MODULE: ./src/pages/WarehouseManagementV3/Inventory/ExportHistory/components/ExportVoucherDetailEnhanced/hooks/useExportVoucherDetailLogic.ts





// hooks/useExportVoucherLogic.ts









var useExportVoucherLogic = function useExportVoucherLogic(onSuccess) {
  var store = useExportVoucherDetailStore();

  // Handle warehouse selection
  var handleSelectWarehouse = (0,react.useCallback)(function (warehouseId) {
    if (warehouseId !== store.selectedWarehouse) {
      store.setSelectedWarehouse(warehouseId);
      store.setSelectedItems([]);
      // Reset flag khi \u0111\u1ED5i warehouse
      store.setIsItemTreeDataLoaded(false);
    }
  }, []);

  // Handle unit selection
  var handleSelectUnit = (0,react.useCallback)( /*#__PURE__*/function () {
    var _ref = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(itemCode, conversionFactor, uomId) {
      var newData, recordIndex;
      return regeneratorRuntime_default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            newData = toConsumableArray_default()(store.selectedItems);
            recordIndex = newData.findIndex(function (item) {
              return item.item_code === itemCode;
            });
            if (recordIndex !== -1) {
              newData[recordIndex].actual_qty = newData[recordIndex].actual_qty * newData[recordIndex].conversion_factor / conversionFactor;
              newData[recordIndex].conversion_factor = conversionFactor;
              newData[recordIndex].uom = uomId;
              store.setSelectedItems(newData);
            }
          case 3:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }));
    return function (_x, _x2, _x3) {
      return _ref.apply(this, arguments);
    };
  }(), [store.selectedItems]);

  // Handle financial calculations
  var handleChangePaidAmount = (0,react.useCallback)(function (value, field) {
    var parsedValue = value !== null && value !== void 0 ? value : 0;
    var totalBill = store.selectedItems.reduce(function (acc, ele) {
      return acc + (ele.total_price || 0);
    }, 0);
    switch (field) {
      case 'add_taxes':
        if (store.taxType === 'amount') {
          store.setAddTaxes(parsedValue);
        } else {
          store.setAddTaxes(parsedValue / 100 * totalBill);
        }
        break;
      case 'other_charges':
        store.setOtherCharges(parsedValue);
        break;
      case 'paid_amount':
        console.log('change paid amount', parsedValue);
        store.setPaidAmount(parsedValue);
        break;
      case 'discount':
        if (store.discountType === 'amount') {
          store.setDiscountAmount(parsedValue);
        } else {
          store.setDiscountAmount(parsedValue / 100 * totalBill);
        }
        break;
      case 'voucher_amount':
        store.setVoucherAmount(parsedValue);
        break;
      case 'total_amount':
        store.setTotalAmount(parsedValue);
        break;
    }

    // C\u1EADp nh\u1EADt remainOutstandingAmount sau m\u1ED7i thay \u0111\u1ED5i
    var newRemainOutstanding = store.calculateRemainOutstandingAmount();
    store.setRemainOutstandingAmount(newRemainOutstanding);
  }, [store.taxType, store.discountType, store.selectedItems, store.remainOutstandingAmount]);

  // Handle adding items
  var handleAddItems = (0,react.useCallback)( /*#__PURE__*/asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2() {
    var item_ids, filtered_items, inventory, formattedItems;
    return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          console.log('start to add items', store);
          if (store.form) {
            _context2.next = 3;
            break;
          }
          return _context2.abrupt("return");
        case 3:
          item_ids = store.form.getFieldValue('items');
          if (item_ids) {
            _context2.next = 7;
            break;
          }
          message/* default */.ZP.error('Vui l\xF2ng ch\u1ECDn h\xE0ng h\xF3a');
          return _context2.abrupt("return");
        case 7:
          // Check for duplicates
          filtered_items = store.items.filter(function (item) {
            return item_ids.includes(item.name) && !store.selectedItems.some(function (selected) {
              return selected.item_code === item.name;
            });
          });
          if (!(filtered_items.length === 0)) {
            _context2.next = 11;
            break;
          }
          message/* default */.ZP.error('H\xE0ng h\xF3a \u0111\xE3 t\u1ED3n t\u1EA1i trong danh s\xE1ch');
          return _context2.abrupt("return");
        case 11:
          _context2.next = 13;
          return (0,InventoryManagementV3_inventory/* getInventoryV3 */.R)({
            warehouse: store.selectedWarehouse,
            page: '1',
            size: '1000'
          });
        case 13:
          inventory = _context2.sent;
          // Format items with inventory data
          formattedItems = filtered_items.map(function (item) {
            var inventoryItem = inventory.data.find(function (inv) {
              return inv.item_code === item.item_code;
            });
            return {
              conversion_factor: 1,
              item_code: item.name,
              item_name: item.item_name,
              actual_qty: (inventoryItem === null || inventoryItem === void 0 ? void 0 : inventoryItem.actual_qty) || 0,
              qty: 0,
              rate: item.valuation_rate,
              total_price: 0,
              warehouse: store.selectedWarehouse,
              item_label: item.label,
              key: item.name,
              uom_label: item.uom_label
            };
          });
          console.log('items to add', formattedItems);
          store.setSelectedItems([].concat(toConsumableArray_default()(formattedItems), toConsumableArray_default()(store.selectedItems)));
          store.form.setFieldValue('items', []);
        case 18:
        case "end":
          return _context2.stop();
      }
    }, _callee2);
  })), [store.form, store.items, store.selectedItems, store.selectedWarehouse]);

  // Handle Excel import
  var handleExcelImport = (0,react.useCallback)( /*#__PURE__*/function () {
    var _ref3 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee3(excelData) {
      var inventory, formattedItems;
      return regeneratorRuntime_default()().wrap(function _callee3$(_context3) {
        while (1) switch (_context3.prev = _context3.next) {
          case 0:
            _context3.prev = 0;
            _context3.next = 3;
            return (0,InventoryManagementV3_inventory/* getInventoryV3 */.R)({
              warehouse: store.selectedWarehouse,
              page: '1',
              size: '1000'
            });
          case 3:
            inventory = _context3.sent;
            formattedItems = excelData.map(function (row) {
              var item = store.items.find(function (i) {
                return i.item_name === row.item_name;
              });
              if (!item) return null;
              var existingItem = store.selectedItems.find(function (selected) {
                return selected.item_code === item.name;
              });
              if (existingItem) return null;
              var inventoryItem = inventory.data.find(function (inv) {
                return inv.item_code === item.item_code;
              });
              return {
                conversion_factor: 1,
                item_code: item.name,
                item_name: item.item_name,
                actual_qty: (inventoryItem === null || inventoryItem === void 0 ? void 0 : inventoryItem.actual_qty) || 0,
                qty: row.quantity || 0,
                rate: row.valuation_rate || item.valuation_rate,
                total_price: (row.quantity || 0) * (row.valuation_rate || item.valuation_rate),
                warehouse: store.selectedWarehouse,
                item_label: item.label,
                key: generateRandomString(),
                uom_label: item.uom_label
              };
            }).filter(Boolean);
            store.setSelectedItems([].concat(toConsumableArray_default()(store.selectedItems), toConsumableArray_default()(formattedItems)));
            _context3.next = 11;
            break;
          case 8:
            _context3.prev = 8;
            _context3.t0 = _context3["catch"](0);
            message/* default */.ZP.error('Failed to import Excel data');
          case 11:
          case "end":
            return _context3.stop();
        }
      }, _callee3, null, [[0, 8]]);
    }));
    return function (_x4) {
      return _ref3.apply(this, arguments);
    };
  }(), [store.selectedWarehouse, store.items, store.selectedItems]);

  // Validate items before submission
  var validateItems = (0,react.useCallback)(function (items) {
    var _iterator = createForOfIteratorHelper_default()(items),
      _step;
    try {
      for (_iterator.s(); !(_step = _iterator.n()).done;) {
        var item = _step.value;
        if (item.qty === null || item.rate === null) {
          message/* default */.ZP.error('H\xE3y cung c\u1EA5p \u0111\u1EA7y \u0111\u1EE7 s\u1ED1 l\u01B0\u1EE3ng v\xE0 \u0111\u01A1n gi\xE1 c\u1EE7a h\xE0ng h\xF3a');
          return false;
        }
        if (item.qty <= 0 || item.rate <= 0) {
          message/* default */.ZP.error('S\u1ED1 l\u01B0\u1EE3ng v\xE0 \u0111\u01A1n gi\xE1 c\u1EE7a h\xE0ng h\xF3a ph\u1EA3i l\u1EDBn h\u01A1n 0');
          return false;
        }
        if (item.qty > item.actual_qty) {
          message/* default */.ZP.error("S\\u1ED1 l\\u01B0\\u1EE3ng xu\\u1EA5t c\\u1EE7a ".concat(item.item_name, " v\\u01B0\\u1EE3t qu\\xE1 s\\u1ED1 l\\u01B0\\u1EE3ng t\\u1ED3n kho"));
          return false;
        }
      }
    } catch (err) {
      _iterator.e(err);
    } finally {
      _iterator.f();
    }
    return true;
  }, []);
  var validatePayment = (0,react.useCallback)(function () {
    var totalBill = store.calculateTotalBill();
    console.log('total bill', totalBill);
    console.log('paid amount', store.paidAmount);
    if (store.paidAmount > totalBill) {
      message/* default */.ZP.error("S\\u1ED1 ti\\u1EC1n thanh to\\xE1n kh\\xF4ng \\u0111\\u01B0\\u1EE3c l\\u1EDBn h\\u01A1n t\\u1ED5ng ti\\u1EC1n h\\xF3a \\u0111\\u01A1n (".concat(formatCurrency(totalBill), ")"));
      return false;
    }
    return true;
  }, [store.selectedItems, store.addTaxes, store.otherCharges, store.discountAmount, store.paidAmount]);

  // Handle form submission
  var handleFinish = (0,react.useCallback)( /*#__PURE__*/function () {
    var _ref4 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee4(values) {
      var date, posting_date, posting_time, exportVoucher, saveRes;
      return regeneratorRuntime_default()().wrap(function _callee4$(_context4) {
        while (1) switch (_context4.prev = _context4.next) {
          case 0:
            store.setSubmitting(true);
            _context4.prev = 1;
            if (validateItems(store.selectedItems)) {
              _context4.next = 4;
              break;
            }
            return _context4.abrupt("return", false);
          case 4:
            if (!(store.selectedItems.length > 20)) {
              _context4.next = 7;
              break;
            }
            message/* default */.ZP.error('S\u1ED1 l\u01B0\u1EE3ng h\xE0ng h\xF3a kh\xF4ng \u0111\u01B0\u1EE3c v\u01B0\u1EE3t qu\xE1 20.');
            return _context4.abrupt("return", false);
          case 7:
            // Validate payment
            // hi\u1EC7n t\u1EA1i ch\u01B0a c\u1EA7n
            // if (!validatePayment()) {
            //     return false;
            // }
            // Format date and time
            date = dayjs_min_default()(values.posting_date, constanst/* DEFAULT_DATE_AND_HH_MM_FORMAT */.dD);
            posting_date = date.format('YYYY-MM-DD');
            posting_time = "".concat(date.format('HH:mm'), ":").concat(dayjs_min_default()().format('ss')); // Create export voucher
            exportVoucher = {
              __isLocal: 1,
              __unsaved: 1,
              posting_date: posting_date,
              posting_time: posting_time,
              set_posting_time: 1,
              company: 'VIIS',
              description: values.description,
              iot_customer_user: values.employee,
              file_path: values.file_path,
              doctype: 'Delivery Note',
              customer: values.customer,
              set_warehouse: values.warehouse,
              add_taxes: store.addTaxes,
              other_charges: values.other_charges,
              paid_amount: store.paidAmount,
              discount: store.discountAmount,
              have_transaction: true,
              items: store.selectedItems.map(function (item) {
                return {
                  doctype: 'Delivery Note Item',
                  name: 'new-delivery-note-item' + generateRandomString(),
                  warehouse: values.warehouse,
                  conversion_factor: item.conversion_factor,
                  item_code: item.item_code,
                  qty: item.qty,
                  rate: item.rate,
                  uom: item.uom
                };
              })
            };
            console.log('exportVoucher', exportVoucher);
            // Save and submit
            _context4.next = 14;
            return (0,deliveryNote/* saveExportVoucher */.lm)(exportVoucher);
          case 14:
            saveRes = _context4.sent;
            _context4.next = 17;
            return (0,deliveryNote/* submitExportVoucher */.a1)(objectSpread2_default()(objectSpread2_default()({}, saveRes), {}, {
              paid_amount: store.paidAmount,
              set_posting_time: 1,
              status: 'Draft',
              have_transaction: true
            }));
          case 17:
            message/* default */.ZP.success('Success');
            onSuccess === null || onSuccess === void 0 || onSuccess();
            store.reset();
            return _context4.abrupt("return", true);
          case 23:
            _context4.prev = 23;
            _context4.t0 = _context4["catch"](1);
            message/* default */.ZP.error(JSON.stringify(_context4.t0));
            return _context4.abrupt("return", false);
          case 27:
            _context4.prev = 27;
            store.setSubmitting(false);
            return _context4.finish(27);
          case 30:
          case "end":
            return _context4.stop();
        }
      }, _callee4, null, [[1, 23, 27, 30]]);
    }));
    return function (_x5) {
      return _ref4.apply(this, arguments);
    };
  }(), [store.selectedItems, store.addTaxes, store.discountAmount, store.paidAmount]);
  var handleSave = (0,react.useCallback)( /*#__PURE__*/function () {
    var _ref5 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee5(values) {
      var date, currentDate, posting_date, posting_time, exportVoucher, saveRes;
      return regeneratorRuntime_default()().wrap(function _callee5$(_context5) {
        while (1) switch (_context5.prev = _context5.next) {
          case 0:
            store.setSubmitting(true);
            _context5.prev = 1;
            if (validateItems(store.selectedItems)) {
              _context5.next = 4;
              break;
            }
            return _context5.abrupt("return", false);
          case 4:
            if (!(store.selectedItems.length > 20)) {
              _context5.next = 7;
              break;
            }
            message/* default */.ZP.error('S\u1ED1 l\u01B0\u1EE3ng h\xE0ng h\xF3a kh\xF4ng \u0111\u01B0\u1EE3c v\u01B0\u1EE3t qu\xE1 20.');
            return _context5.abrupt("return", false);
          case 7:
            date = dayjs_min_default()(values.posting_date, constanst/* DEFAULT_DATE_AND_HH_MM_FORMAT */.dD);
            currentDate = dayjs_min_default()().endOf('day'); // Check if posting date is in the future
            if (!date.isAfter(currentDate)) {
              _context5.next = 12;
              break;
            }
            message/* default */.ZP.error('Ng\xE0y xu\u1EA5t kho kh\xF4ng th\u1EC3 l\u1EDBn h\u01A1n ng\xE0y hi\u1EC7n t\u1EA1i.');
            return _context5.abrupt("return", false);
          case 12:
            posting_date = date.format('YYYY-MM-DD');
            posting_time = "".concat(date.format('HH:mm'), ":").concat(dayjs_min_default()().format('ss')); //format creation and modifided field format UTC/GMT to add 7 hours
            // const creation = moment(store.savedVoucherData.creation).utc().add(17, 'hours').format('YYYY-MM-DD HH:mm:ss')
            // const modified = moment(store.savedVoucherData.modified).utc().add(7, 'hours').format('YYYY-MM-DD HH:mm:ss')
            // Create export voucher
            exportVoucher = objectSpread2_default()(objectSpread2_default()({}, store.savedVoucherData), {}, {
              __islocal: store.savedVoucherData.name ? 0 : 1,
              __unsaved: 1,
              posting_date: posting_date,
              posting_time: posting_time,
              set_posting_time: 1,
              company: 'VIIS',
              description: values.description,
              iot_customer_user: values.employee,
              file_path: values.file_path,
              doctype: 'Delivery Note',
              customer: values.customer,
              set_warehouse: values.warehouse,
              add_taxes: store.addTaxes,
              other_charges: values.other_charges,
              paid_amount: store.paidAmount,
              discount: store.discountAmount,
              have_transaction: true,
              items: store.selectedItems.map(function (item) {
                return {
                  doctype: 'Delivery Note Item',
                  docstatus: 0,
                  __islocal: item.name ? 0 : 1,
                  __unsaved: 1,
                  parent: store.savedVoucherData.name,
                  parentfield: 'items',
                  parenttype: 'Delivery Note',
                  name: item.name || 'new-delivery-note-item' + generateRandomString(),
                  warehouse: values.warehouse,
                  conversion_factor: item.conversion_factor,
                  item_code: item.item_code,
                  qty: item.qty,
                  rate: item.rate,
                  uom: item.uom
                };
              })
            });
            _context5.next = 17;
            return (0,deliveryNote/* saveExportVoucher */.lm)(exportVoucher);
          case 17:
            saveRes = _context5.sent;
            store.setSavedVoucherData(saveRes);
            store.setSaved(true);
            onSuccess === null || onSuccess === void 0 || onSuccess();
            message/* default */.ZP.success('L\u01B0u th\xE0nh c\xF4ng');
            return _context5.abrupt("return", true);
          case 25:
            _context5.prev = 25;
            _context5.t0 = _context5["catch"](1);
            message/* default */.ZP.error('L\u01B0u th\u1EA5t b\u1EA1i');
            return _context5.abrupt("return", false);
          case 29:
            _context5.prev = 29;
            store.setSubmitting(false);
            return _context5.finish(29);
          case 32:
          case "end":
            return _context5.stop();
        }
      }, _callee5, null, [[1, 25, 29, 32]]);
    }));
    return function (_x6) {
      return _ref5.apply(this, arguments);
    };
  }(), [store.selectedItems, store.addTaxes, store.discountAmount, store.paidAmount]);
  var handleSubmit = /*#__PURE__*/function () {
    var _ref6 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee6() {
      return regeneratorRuntime_default()().wrap(function _callee6$(_context6) {
        while (1) switch (_context6.prev = _context6.next) {
          case 0:
            store.setSubmitting(true);
            _context6.prev = 1;
            if (store.savedVoucherData) {
              _context6.next = 5;
              break;
            }
            message/* default */.ZP.error('Vui l\xF2ng l\u01B0u phi\u1EBFu tr\u01B0\u1EDBc khi ho\xE0n th\xE0nh');
            return _context6.abrupt("return", false);
          case 5:
            _context6.next = 7;
            return (0,deliveryNote/* submitExportVoucher */.a1)(objectSpread2_default()(objectSpread2_default()({}, store.savedVoucherData), {}, {
              doctype: 'Delivery Note',
              paid_amount: store.paidAmount,
              set_posting_time: 1,
              status: 'Draft',
              have_transaction: true
            }));
          case 7:
            message/* default */.ZP.success('Ho\xE0n th\xE0nh phi\u1EBFu th\xE0nh c\xF4ng');
            onSuccess === null || onSuccess === void 0 || onSuccess();
            // store.reset()
            return _context6.abrupt("return", true);
          case 12:
            _context6.prev = 12;
            _context6.t0 = _context6["catch"](1);
            message/* default */.ZP.error('Ho\xE0n th\xE0nh phi\u1EBFu th\u1EA5t b\u1EA1i');
            return _context6.abrupt("return", false);
          case 16:
            _context6.prev = 16;
            store.setSubmitting(false);
            return _context6.finish(16);
          case 19:
          case "end":
            return _context6.stop();
        }
      }, _callee6, null, [[1, 12, 16, 19]]);
    }));
    return function handleSubmit() {
      return _ref6.apply(this, arguments);
    };
  }();

  //handle delete voucher
  var handleDelete = /*#__PURE__*/function () {
    var _ref7 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee7() {
      return regeneratorRuntime_default()().wrap(function _callee7$(_context7) {
        while (1) switch (_context7.prev = _context7.next) {
          case 0:
            store.setSubmitting(true);
            _context7.prev = 1;
            if (!(store.docstatus !== 0)) {
              _context7.next = 5;
              break;
            }
            message/* default */.ZP.error('Ch\u1EC9 c\xF3 th\u1EC3 xo\xE1 phi\u1EBFu \u1EDF tr\u1EA1ng th\xE1i nh\xE1p');
            return _context7.abrupt("return", false);
          case 5:
            console.log('store.savedVoucherData', store.savedVoucherData);
            _context7.next = 8;
            return (0,deliveryNote/* deleteDeliveryNote */.f4)(store.savedVoucherData.name);
          case 8:
            message/* default */.ZP.success('X\xF3a phi\u1EBFu th\xE0nh c\xF4ng');
            onSuccess === null || onSuccess === void 0 || onSuccess();
            // store.reset()
            return _context7.abrupt("return", true);
          case 13:
            _context7.prev = 13;
            _context7.t0 = _context7["catch"](1);
            message/* default */.ZP.error('X\xF3a phi\u1EBFu th\u1EA5t b\u1EA1i');
            return _context7.abrupt("return", false);
          case 17:
            _context7.prev = 17;
            store.setSubmitting(false);
            return _context7.finish(17);
          case 20:
          case "end":
            return _context7.stop();
        }
      }, _callee7, null, [[1, 13, 17, 20]]);
    }));
    return function handleDelete() {
      return _ref7.apply(this, arguments);
    };
  }();

  //handle cancel voucher
  var handleCancel = /*#__PURE__*/function () {
    var _ref8 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee8() {
      return regeneratorRuntime_default()().wrap(function _callee8$(_context8) {
        while (1) switch (_context8.prev = _context8.next) {
          case 0:
            store.setSubmitting(true);
            _context8.prev = 1;
            if (!(store.docstatus !== 1)) {
              _context8.next = 5;
              break;
            }
            message/* default */.ZP.error('Vui l\xF2ng l\u01B0u phi\u1EBFu tr\u01B0\u1EDBc khi h\u1EE7y');
            return _context8.abrupt("return", false);
          case 5:
            _context8.next = 7;
            return (0,deliveryNote/* cancelDeliveryNote */.sF)(store.savedVoucherData.name);
          case 7:
            message/* default */.ZP.success('H\u1EE7y phi\u1EBFu th\xE0nh c\xF4ng');
            onSuccess === null || onSuccess === void 0 || onSuccess();
            // store.reset()
            return _context8.abrupt("return", true);
          case 12:
            _context8.prev = 12;
            _context8.t0 = _context8["catch"](1);
            message/* default */.ZP.error('H\u1EE7y phi\u1EBFu th\u1EA5t b\u1EA1i');
            return _context8.abrupt("return", false);
          case 16:
            _context8.prev = 16;
            store.setSubmitting(false);
            return _context8.finish(16);
          case 19:
          case "end":
            return _context8.stop();
        }
      }, _callee8, null, [[1, 12, 16, 19]]);
    }));
    return function handleCancel() {
      return _ref8.apply(this, arguments);
    };
  }();

  // Handle warehouse change, need to reload item tree data and reset selected items
  var handleWarehouseChange = (0,react.useCallback)(function (warehouseId) {
    store.setSelectedWarehouse(warehouseId);
    store.setSelectedItems([]);
    store.setIsItemTreeDataLoaded(false);
  }, []);
  return {
    handleSelectWarehouse: handleSelectWarehouse,
    handleSelectUnit: handleSelectUnit,
    handleChangePaidAmount: handleChangePaidAmount,
    handleAddItems: handleAddItems,
    handleExcelImport: handleExcelImport,
    handleFinish: handleFinish,
    handleSave: handleSave,
    handleSubmit: handleSubmit,
    handleDelete: handleDelete,
    handleCancel: handleCancel,
    validateItems: validateItems,
    handleWarehouseChange: handleWarehouseChange
  };
};
;// CONCATENATED MODULE: ./src/pages/WarehouseManagementV3/Inventory/ExportHistory/components/ExportVoucherDetailEnhanced/components/BasicInfoSection.tsx
// components/BasicInfoSection.tsx




// Kh\xF4ng c\u1EA7n useState v\xE0 useEffect n\u1EEFa






var BasicInfoSection = function BasicInfoSection() {
  var _useExportVoucherDeta = useExportVoucherDetailStore(),
    setSelectedCustomer = _useExportVoucherDeta.setSelectedCustomer,
    customerList = _useExportVoucherDeta.customerList;
  var _useExportVoucherLogi = useExportVoucherLogic(),
    handleWarehouseChange = _useExportVoucherLogi.handleWarehouseChange;

  //current user
  var _useModel = (0,_umi_production_exports.useModel)("@@initialState"),
    initialState = _useModel.initialState;
  var curentUser = initialState === null || initialState === void 0 ? void 0 : initialState.currentUser;
  return /*#__PURE__*/(0,jsx_runtime.jsx)(jsx_runtime.Fragment, {
    children: /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
      gutter: [0, 0],
      children: [/*#__PURE__*/(0,jsx_runtime.jsxs)(col/* default */.Z, {
        span: 8,
        children: [' ', /*#__PURE__*/(0,jsx_runtime.jsx)(DateTimePicker/* default */.Z, {
          name: "posting_date",
          required: true,
          rules: [{
            required: true,
            message: 'Vui l\xF2ng ch\u1ECDn ng\xE0y ho\u1EA1ch to\xE1n'
          }],
          label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
            id: "warehouse-management.export-voucher.transaction_date"
          }),
          width: "md",
          fieldProps: {
            format: constanst/* DEFAULT_DATE_AND_HH_MM_FORMAT */.dD
          }
        })]
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 8,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
          showSearch: true,
          name: "warehouse",
          rules: [{
            required: true,
            message: 'Vui l\xF2ng ch\u1ECDn kho'
          }],
          required: true,
          label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
            id: "warehouse-management.warehouse-name"
          }),
          onChange: handleWarehouseChange,
          request: fetchWarehouseList,
          width: 'md'
        })
      }), /*#__PURE__*/(0,jsx_runtime.jsxs)(col/* default */.Z, {
        span: 8,
        children: [' ', /*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
          showSearch: true,
          required: true,
          label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
            id: "warehouse-management.export-voucher.employee"
          }),
          request: fetchCustomerUserList,
          initialValue: curentUser === null || curentUser === void 0 ? void 0 : curentUser.user_id,
          name: "employee",
          disabled: true,
          width: 'md'
        })]
      }), /*#__PURE__*/(0,jsx_runtime.jsxs)(col/* default */.Z, {
        span: 8,
        children: [' ', /*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
          required: true,
          label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
            id: "warehouse-management.export-voucher.customer"
          })
          // request={async () => {
          //   const res = await fetchCustomerV3();
          //   setCustomerList(res.data); // L\u01B0u customer list v\xE0o store
          //   return res.data.map((item) => ({
          //     label: item.label,
          //     value: item.name,
          //   }));
          // }}
          ,
          options: customerList.map(function (customer) {
            return {
              label: customer.label,
              value: customer.name
            };
          }),
          onChange: setSelectedCustomer // S\u1EED d\u1EE5ng tr\u1EF1c ti\u1EBFp action t\u1EEB store
          ,
          name: "customer",
          width: 'md'
        })]
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 16,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(TextArea/* default */.Z, {
          name: "description",
          label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
            id: 'common.form.description'
          }),
          width: 'md'
        })
      })]
    })
  });
};
// EXTERNAL MODULE: ./src/components/DownloadFileButton/index.tsx
var DownloadFileButton = __webpack_require__(62735);
// EXTERNAL MODULE: ./src/components/Form/FormTreeSelectSearch/index.tsx
var FormTreeSelectSearch = __webpack_require__(30653);
// EXTERNAL MODULE: ./src/components/UploadExcelFile/index.tsx
var UploadExcelFile = __webpack_require__(41155);
// EXTERNAL MODULE: ./src/components/UploadFIles/index.tsx
var UploadFIles = __webpack_require__(75508);
// EXTERNAL MODULE: ./src/pages/WarehouseManagementV3/hooks/useUpdateDeliveryNote.ts
var useUpdateDeliveryNote = __webpack_require__(16380);
// EXTERNAL MODULE: ./src/services/utils.ts
var utils = __webpack_require__(467);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/PlusOutlined.js
var PlusOutlined = __webpack_require__(51042);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/PrinterOutlined.js + 1 modules
var PrinterOutlined = __webpack_require__(30019);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/layouts/ProForm/index.js + 3 modules
var ProForm = __webpack_require__(34994);
// EXTERNAL MODULE: ./node_modules/antd/es/space/index.js + 2 modules
var space = __webpack_require__(42075);
// EXTERNAL MODULE: ./node_modules/antd/es/divider/index.js + 1 modules
var divider = __webpack_require__(96074);
// EXTERNAL MODULE: ./src/services/InventoryManagementV3/product-item.ts
var product_item = __webpack_require__(58642);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/DeleteOutlined.js
var DeleteOutlined = __webpack_require__(82061);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-table/es/components/EditableTable/index.js + 1 modules
var EditableTable = __webpack_require__(88280);
// EXTERNAL MODULE: ./node_modules/antd/es/input-number/index.js + 16 modules
var input_number = __webpack_require__(9735);
// EXTERNAL MODULE: ./node_modules/numeral/numeral.js
var numeral = __webpack_require__(92077);
var numeral_default = /*#__PURE__*/__webpack_require__.n(numeral);
;// CONCATENATED MODULE: ./src/pages/WarehouseManagementV3/Inventory/ExportHistory/components/ExportVoucherDetailEnhanced/components/ExportVoucherTable.tsx












var ExportVoucherTable = function ExportVoucherTable(_ref) {
  var data = _ref.data,
    setData = _ref.setData;
  var actionRef = (0,react.useRef)();
  var handleDeleteItem = function handleDeleteItem(index) {
    var newData = data.filter(function (item) {
      return item.key !== index;
    });
    setData(newData);
  };
  var _useState = (0,react.useState)(function () {
      return data.map(function (item) {
        return item.key;
      });
    }),
    _useState2 = slicedToArray_default()(_useState, 2),
    editableKeys = _useState2[0],
    setEditableRowKeys = _useState2[1];
  var handleUpdateQuantity = function handleUpdateQuantity(record) {
    record.total_price = record.qty * record.rate;
  };
  var handleSelectUnit = function handleSelectUnit(index, value, uom_id) {
    var _actionRef$current;
    console.log('index', index);
    console.log('value', value);
    console.log('uom_id', uom_id);
    var newData = structuredClone(data);
    console.log('newData', newData);
    var recordIndex = newData.findIndex(function (item) {
      return item.item_code === index;
    });
    console.log('recordIndex', recordIndex);
    newData[recordIndex].actual_qty = newData[recordIndex].actual_qty * newData[recordIndex].conversion_factor / value;
    console.log('newData[recordIndex].actual_qty', newData[recordIndex].actual_qty);
    newData[recordIndex].conversion_factor = value;
    console.log('newData[recordIndex].conversion_factor', newData[recordIndex].conversion_factor);
    newData[recordIndex].uom = uom_id;
    setData(newData);
    (_actionRef$current = actionRef.current) === null || _actionRef$current === void 0 || _actionRef$current.reload();
  };
  var columns = [{
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: 'common.index'
    }),
    dataIndex: 'index',
    editable: false,
    render: function render(dom, entity, index, action, schema) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        children: index + 1
      });
    },
    width: 40
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: 'warehouse-management.export-voucher.item_code'
    }),
    dataIndex: 'item_name',
    editable: false,
    width: 80
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "warehouse-management.export-voucher.item_name",
      defaultMessage: "unknown"
    }),
    dataIndex: 'item_label',
    editable: false,
    width: 80
  },
  // {
  //   title: 'T\u1ED3n kho',
  //   dataIndex: 'current_quantity',
  //   search: false,
  //   editable: false,
  //   hideInTable: type === 'import',
  //   renderFormItem(schema, config, form, action) {
  //     return (
  //       <InputNumber
  //         style={{ width: '100%' }}
  //         formatter={(value) => {
  //           const formatNum = numeral(value).format('0,0.00');
  //           return formatNum;
  //         }}
  //       />
  //     );
  //   },
  //   width: 80,
  // },
  // {
  //   title: <FormattedMessage id="warehouse-management.export-voucher.producer" defaultMessage="unknown" />,
  //   dataIndex: 'supplier',
  //   width: 80,
  // },
  {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "warehouse-management.export-voucher.actual_qty"
    }),
    dataIndex: 'actual_qty',
    search: false,
    editable: false,
    renderFormItem: function renderFormItem(schema, config, form, action) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)(input_number/* default */.Z, {
        style: {
          width: '100%'
        },
        formatter: function formatter(value) {
          var formatNum = numeral_default()(value).format('0,0.00');
          return formatNum;
        }
      });
    },
    width: 80
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "warehouse-management.export-voucher.quantity",
      defaultMessage: "unknown"
    }),
    dataIndex: 'qty',
    search: false,
    renderFormItem: function renderFormItem(schema, config, form, action) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)(input_number/* default */.Z, {
        style: {
          width: '100%'
        },
        formatter: function formatter(value) {
          return "".concat(value).replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');
        },
        parser: function parser(value) {
          return value.replace(/\\$\\s?|(,*)/g, '');
        },
        step: "0.01"
      });
    },
    width: 80
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.unit"
    }),
    editable: false,
    dataIndex: 'conversion_factor',
    render: function render(dom, entity, index, action, schema) {
      return [/*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
        formItemProps: {
          style: {
            marginBottom: 0
          }
        },
        fieldProps: {
          defaultValue: entity.uom_label
        },
        placeholder: "Lo\\u1EA1i \\u0111\\u1EBFm",
        request: /*#__PURE__*/asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
          var _res$data$uoms;
          var res, formatted_res, options;
          return regeneratorRuntime_default()().wrap(function _callee$(_context) {
            while (1) switch (_context.prev = _context.next) {
              case 0:
                _context.next = 2;
                return (0,product_item/* getDetailsProductItemV3 */.eX)({
                  name: entity.item_code || ''
                });
              case 2:
                _context.t0 = _context.sent;
                if (_context.t0) {
                  _context.next = 5;
                  break;
                }
                _context.t0 = [];
              case 5:
                res = _context.t0;
                formatted_res = ((_res$data$uoms = res.data.uoms) === null || _res$data$uoms === void 0 ? void 0 : _res$data$uoms.map(function (item) {
                  return {
                    label: item.uom_name,
                    value: item.conversion_factor,
                    uom_id: item.uom
                  };
                })) || [];
                options = [{
                  label: entity.uom_label,
                  value: 1,
                  uom_id: res.data.stock_uom
                }];
                return _context.abrupt("return", [].concat(options, toConsumableArray_default()(formatted_res)));
              case 9:
              case "end":
                return _context.stop();
            }
          }, _callee);
        })),
        onChange: function onChange(value, option) {
          return handleSelectUnit(entity.item_code, +value, option.uom_id);
        }
      }, 'conversion_factor')];
    },
    width: 80
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "warehouse-management.export-voucher.rate",
      defaultMessage: "unknown"
    }),
    dataIndex: 'rate',
    search: false,
    width: 80,
    renderFormItem: function renderFormItem(schema, config, form, action) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)(input_number/* default */.Z, {
        style: {
          width: '100%'
        },
        formatter: function formatter(value) {
          return "".concat(value).replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');
        },
        parser: function parser(value) {
          return value.replace(/\\$\\s?|(,*)/g, '');
        },
        step: "0.01"
      });
    }
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "warehouse-management.export-voucher.price",
      defaultMessage: "unknown"
    }),
    dataIndex: 'total_price',
    editable: false,
    render: function render(text, record) {
      return numeral_default()(record.total_price).format('0,0');
    },
    // \u0110\u1ECBnh d\u1EA1ng s\u1ED1 \u1EDF \u0111\xE2y
    width: 80
  }, {
    title: 'Action',
    dataIndex: 'key',
    editable: false,
    render: function render(index, record, _, action) {
      return [/*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
        icon: /*#__PURE__*/(0,jsx_runtime.jsx)(DeleteOutlined/* default */.Z, {}),
        onClick: function onClick() {
          return handleDeleteItem(index);
        }
      }, "delete")];
    },
    width: 80
  }];
  (0,react.useEffect)(function () {
    setEditableRowKeys(data.map(function (item) {
      return item.key;
    }));
  }, [data]);
  (0,react.useEffect)(function () {
    var _actionRef$current2;
    (_actionRef$current2 = actionRef.current) === null || _actionRef$current2 === void 0 || _actionRef$current2.reload();
  }, [data, editableKeys]);
  return /*#__PURE__*/(0,jsx_runtime.jsx)(EditableTable/* default */.Z, {
    style: {
      minWidth: '100%'
    },
    columns: columns,
    actionRef: actionRef,
    cardBordered: true,
    request: /*#__PURE__*/asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2() {
      var params,
        sort,
        filter,
        _args2 = arguments;
      return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            params = _args2.length > 0 && _args2[0] !== undefined ? _args2[0] : {};
            sort = _args2.length > 1 ? _args2[1] : undefined;
            filter = _args2.length > 2 ? _args2[2] : undefined;
            return _context2.abrupt("return", {
              data: data,
              success: true,
              total: data.length
            });
          case 4:
          case "end":
            return _context2.stop();
        }
      }, _callee2);
    })),
    editable: {
      type: 'multiple',
      editableKeys: editableKeys,
      actionRender: function actionRender(row, config, defaultDoms) {
        return [defaultDoms["delete"]];
      },
      onValuesChange: function onValuesChange(record, recordList) {
        handleUpdateQuantity(record);
        setData(recordList);
      },
      onChange: setEditableRowKeys
    },
    recordCreatorProps: false,
    rowKey: "key",
    search: false,
    options: {
      setting: {
        listsHeight: 400
      }
    },
    pagination: {
      defaultPageSize: 20,
      showSizeChanger: true,
      pageSizeOptions: ['20', '50', '100']
    },
    toolbar: {
      multipleLine: false
    },
    toolBarRender: false,
    size: "small",
    dateFormatter: "string"
  });
};
/* harmony default export */ var components_ExportVoucherTable = (ExportVoucherTable);
;// CONCATENATED MODULE: ./src/pages/WarehouseManagementV3/Inventory/ExportHistory/components/ExportVoucherDetailEnhanced/components/ItemSection.tsx


// components/ItemSection.tsx
















var ItemSection = function ItemSection() {
  var _useExportVoucherDeta = useExportVoucherDetailStore(),
    selectedWarehouse = _useExportVoucherDeta.selectedWarehouse,
    treeData = _useExportVoucherDeta.treeData,
    selectedItems = _useExportVoucherDeta.selectedItems,
    setSelectedItems = _useExportVoucherDeta.setSelectedItems,
    savedVoucherData = _useExportVoucherDeta.savedVoucherData;
  var _useUpdateDeliveryNot = (0,useUpdateDeliveryNote/* useUpdateDeliveryNote */.h)(),
    update = _useUpdateDeliveryNot.run,
    updating = _useUpdateDeliveryNot.loading;
  var _useExportVoucherLogi = useExportVoucherLogic(),
    handleAddItems = _useExportVoucherLogi.handleAddItems,
    handleExcelImport = _useExportVoucherLogi.handleExcelImport;
  // T\u1EA1o wrapper function
  var handleSetData = function handleSetData(value) {
    if (typeof value === 'function') {
      setSelectedItems(selectedItems);
    } else {
      setSelectedItems(value);
    }
  };
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  return /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
    children: [/*#__PURE__*/(0,jsx_runtime.jsxs)(ProForm/* ProForm */.A.Group, {
      title: /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        style: {
          marginBottom: '-20px'
        },
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
          id: 'warehouse-management.import-voucher.item_list'
        })
      }),
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)(FormTreeSelectSearch/* default */.Z, {
        name: 'items',
        fieldProps: {
          treeData: treeData
        },
        label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
          id: 'warehouse-management.export-voucher.item_name'
        }),
        colProps: {
          span: 8
        }
      }), /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        style: {
          marginLeft: 5
        },
        children: /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
          align: "middle",
          justify: "start",
          children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(UploadExcelFile/* default */.Z, {
              formItemName: "item_list",
              label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
                id: "action.import-from-excel"
              }),
              onExcelDataLoaded: handleExcelImport
            })
          }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
            style: {
              marginLeft: 5,
              paddingTop: 5
            },
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(DownloadFileButton/* default */.Z, {
              filePath: "/private/files/stock_voucher.xlsx",
              buttonName: "common.form.excel_template"
            })
          })]
        })
      })]
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(ProForm/* ProForm */.A.Group, {
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(row/* default */.Z, {
        style: {
          width: '100%',
          marginLeft: '4px'
        },
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
          span: 18,
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(space/* default */.Z, {
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
              type: "primary",
              icon: /*#__PURE__*/(0,jsx_runtime.jsx)(PlusOutlined/* default */.Z, {}),
              onClick: handleAddItems,
              disabled: !selectedWarehouse,
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
                id: 'warehouse-management.export-voucher.add_item'
              })
            })
          })
        })
      })
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(row/* default */.Z, {
      gutter: [0, 0],
      style: {
        marginTop: '4vh',
        marginLeft: '1.2vh'
      },
      children: /*#__PURE__*/(0,jsx_runtime.jsxs)(space/* default */.Z, {
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)(spin/* default */.Z, {
          spinning: updating,
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(UploadFIles/* default */.Z, {
            maxSize: 10,
            isReadonly: false,
            initialImages: savedVoucherData && savedVoucherData.file_path ? savedVoucherData.file_path : '',
            formItemName: 'file_path'
            // label={formatMessage({
            //   id: 'common.form.document',
            // })}
            ,
            fileLimit: 20,
            onValueChange: ( /*#__PURE__*/function () {
              var _ref = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(value) {
                return regeneratorRuntime_default()().wrap(function _callee$(_context) {
                  while (1) switch (_context.prev = _context.next) {
                    case 0:
                      _context.next = 2;
                      return update({
                        name: savedVoucherData.name,
                        file_path: value
                      });
                    case 2:
                    case "end":
                      return _context.stop();
                  }
                }, _callee);
              }));
              return function (_x) {
                return _ref.apply(this, arguments);
              };
            }())
          })
        }), /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
          style: {
            marginBottom: '3.5vh'
          },
          disabled: false,
          icon: /*#__PURE__*/(0,jsx_runtime.jsx)(PrinterOutlined/* default */.Z, {
            style: {
              marginRight: 4
            }
          }),
          onClick: function onClick() {
            return (0,utils/* openInNewTab */.YQ)("/warehouse-management-v3/to-pdf?type=export&id=".concat(savedVoucherData.name));
          },
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
            id: 'common.print_receipt'
          })
        }, 'download'), /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
          style: {
            marginBottom: '3.5vh'
          },
          disabled: false,
          icon: /*#__PURE__*/(0,jsx_runtime.jsx)(PrinterOutlined/* default */.Z, {
            style: {
              marginRight: 4
            }
          }),
          onClick: function onClick() {
            return (0,utils/* openInNewTab */.YQ)("/warehouse-management-v3/to-pdf?type=exportQtyOnly&id=".concat(savedVoucherData.name));
          },
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
            id: 'common.print_receipt_qty_only'
          })
        }, 'download-qty-only')]
      })
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(divider/* default */.Z, {}), /*#__PURE__*/(0,jsx_runtime.jsx)(components_ExportVoucherTable, {
      data: selectedItems,
      setData: handleSetData
    })]
  });
};
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/Digit/index.js
var Digit = __webpack_require__(31199);
// EXTERNAL MODULE: ./node_modules/antd/es/typography/index.js + 18 modules
var typography = __webpack_require__(25514);
;// CONCATENATED MODULE: ./src/pages/WarehouseManagementV3/Inventory/ExportHistory/components/ExportVoucherDetailEnhanced/components/PaymentSection.tsx
// components/PaymentSection.tsx









var PaymentSection = function PaymentSection() {
  var _useExportVoucherDeta = useExportVoucherDetailStore(),
    customerTotalOutstandingAmount = _useExportVoucherDeta.customerTotalOutstandingAmount,
    selectedItems = _useExportVoucherDeta.selectedItems,
    remainOutstandingAmount = _useExportVoucherDeta.remainOutstandingAmount,
    taxType = _useExportVoucherDeta.taxType,
    discountType = _useExportVoucherDeta.discountType,
    setTaxType = _useExportVoucherDeta.setTaxType,
    setDiscountType = _useExportVoucherDeta.setDiscountType,
    addTaxes = _useExportVoucherDeta.addTaxes,
    otherCharges = _useExportVoucherDeta.otherCharges,
    discountAmount = _useExportVoucherDeta.discountAmount,
    paidAmount = _useExportVoucherDeta.paidAmount,
    calculateRemainOutstandingAmount = _useExportVoucherDeta.calculateRemainOutstandingAmount,
    calculateTotalBill = _useExportVoucherDeta.calculateTotalBill,
    setRemainOutstandingAmount = _useExportVoucherDeta.setRemainOutstandingAmount,
    docstatus = _useExportVoucherDeta.docstatus,
    voucherAmount = _useExportVoucherDeta.voucherAmount,
    totalAmount = _useExportVoucherDeta.totalAmount;

  // L\u1EA5y handleChangePaidAmount t\u1EEB logic hook
  var _useExportVoucherLogi = useExportVoucherLogic(),
    handleChangePaidAmount = _useExportVoucherLogi.handleChangePaidAmount;

  // Effect \u0111\u1EC3 theo d\xF5i c\xE1c thay \u0111\u1ED5i v\xE0 c\u1EADp nh\u1EADt remainOutstandingAmount
  (0,react.useEffect)(function () {
    var newRemainOutstanding = calculateRemainOutstandingAmount();
    setRemainOutstandingAmount(newRemainOutstanding);
  }, [selectedItems, customerTotalOutstandingAmount, addTaxes, otherCharges, discountAmount, paidAmount]);
  return /*#__PURE__*/(0,jsx_runtime.jsxs)(ProForm/* ProForm */.A.Group, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: 'common.payment'
    }),
    children: [/*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
      gutter: 16,
      style: {
        width: '100%',
        paddingBottom: '16px'
      },
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 6,
        children: /*#__PURE__*/(0,jsx_runtime.jsxs)(typography/* default */.Z.Text, {
          children: [/*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
            id: 'common.customer_total_outstanding_amount'
          }), ":", ' ', formatCurrency(customerTotalOutstandingAmount)]
        })
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 6,
        children: /*#__PURE__*/(0,jsx_runtime.jsxs)(typography/* default */.Z.Text, {
          children: [/*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
            id: 'warehouse-management.import-voucher.total_price'
          }), ":", ' ', formatCurrency(calculateTotalBill())]
        })
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 6,
        children: /*#__PURE__*/(0,jsx_runtime.jsxs)(typography/* default */.Z.Text, {
          children: [/*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
            id: 'common.outstanding_amount'
          }), ":", ' ', /*#__PURE__*/(0,jsx_runtime.jsxs)("span", {
            style: {
              color: remainOutstandingAmount <= 0 ? 'green' : 'red'
            },
            children: [formatCurrency(remainOutstandingAmount), remainOutstandingAmount <= 0 ? ' (H\u1EBFt n\u1EE3)' : '']
          })]
        })
      })]
    }), /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
      gutter: 16,
      style: {
        width: '100%'
      },
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 6,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(ProForm/* ProForm */.A.Item, {
          label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
            id: 'common.add_taxes'
          }),
          name: "add_taxes",
          style: {
            marginBottom: 0
          },
          children: /*#__PURE__*/(0,jsx_runtime.jsxs)(space/* default */.Z, {
            direction: "horizontal",
            size: [0, 0],
            children: [/*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
              name: 'taxType',
              onChange: setTaxType,
              options: [{
                label: 'S\u1ED1 ti\u1EC1n',
                value: 'amount'
              }, {
                label: 'Ph\u1EA7n tr\u0103m',
                value: 'percentage'
              }],
              style: {
                width: '100%'
              }
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(Digit/* default */.Z, {
              name: "taxesValue",
              fieldProps: {
                onChange: function onChange(value) {
                  return handleChangePaidAmount(value, 'add_taxes');
                },
                formatter: function formatter(value) {
                  return "".concat(value).replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');
                },
                addonAfter: taxType === 'percentage' ? '%' : 'VN\u0110',
                style: {
                  width: '100%'
                }
              }
            })]
          })
        })
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 6,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(Digit/* default */.Z, {
          name: "other_charges",
          label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
            id: 'common.other_charges'
          }),
          fieldProps: {
            onChange: function onChange(value) {
              return handleChangePaidAmount(value, 'other_charges');
            },
            formatter: function formatter(value) {
              return "".concat(value).replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');
            }
          }
        })
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 6,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(ProForm/* ProForm */.A.Item, {
          label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
            id: 'common.discount'
          }),
          name: "discount",
          style: {
            marginBottom: 0
          },
          children: /*#__PURE__*/(0,jsx_runtime.jsxs)(space/* default */.Z, {
            direction: "horizontal",
            size: [0, 0],
            children: [/*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
              name: "discountType",
              onChange: setDiscountType,
              options: [{
                label: 'S\u1ED1 ti\u1EC1n',
                value: 'amount'
              }, {
                label: 'Ph\u1EA7n tr\u0103m',
                value: 'percentage'
              }],
              style: {
                width: '100%'
              }
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(Digit/* default */.Z, {
              name: "discountValue",
              fieldProps: {
                onChange: function onChange(value) {
                  return handleChangePaidAmount(value, 'discount');
                },
                formatter: function formatter(value) {
                  return "".concat(value).replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');
                },
                addonAfter: discountType === 'percentage' ? '%' : 'VN\u0110',
                style: {
                  width: '100%'
                }
              }
            })]
          })
        })
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 6,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(Digit/* default */.Z, {
          name: "paid_amount",
          initialValue: 0,
          fieldProps: {
            onChange: function onChange(value) {
              return handleChangePaidAmount(value, 'paid_amount');
            },
            formatter: function formatter(value) {
              return "".concat(value).replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');
            }
          },
          label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
            id: 'common.paid_amount'
          })
        })
      })]
    }), /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
      gutter: 16,
      style: {
        width: '100%'
      },
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 6,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(ProForm/* ProForm */.A.Item, {
          label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
            id: 'common.voucher_amount'
          }),
          name: "add_taxes",
          style: {
            marginBottom: 0
          },
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(Digit/* default */.Z, {
            name: "voucher_amount",
            fieldProps: {
              placeholder: "".concat(voucherAmount),
              onChange: function onChange(value) {
                return handleChangePaidAmount(value, 'voucher_amount');
              },
              formatter: function formatter(value) {
                return "".concat(value).replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');
              }
            }
          })
        })
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 6,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(Digit/* default */.Z, {
          name: "total_amount",
          label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
            id: 'common.total_amount'
          }),
          fieldProps: {
            placeholder: "".concat(totalAmount),
            onChange: function onChange(value) {
              return handleChangePaidAmount(value, 'total_amount');
            },
            formatter: function formatter(value) {
              return "".concat(value).replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');
            }
          }
        })
      })]
    })]
  });
};
;// CONCATENATED MODULE: ./src/pages/WarehouseManagementV3/Inventory/ExportHistory/components/ExportVoucherDetailEnhanced/components/ExportVoucherForm.tsx
// components/ExportVoucherForm.tsx








var ExportVoucherForm = function ExportVoucherForm() {
  var store = useExportVoucherDetailStore();
  return /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
    children: [/*#__PURE__*/(0,jsx_runtime.jsx)(BasicInfoSection, {}), /*#__PURE__*/(0,jsx_runtime.jsx)(ItemSection, {}), /*#__PURE__*/(0,jsx_runtime.jsx)(PaymentSection, {})]
  });
};
;// CONCATENATED MODULE: ./src/pages/WarehouseManagementV3/Inventory/ExportHistory/components/ExportVoucherDetailEnhanced/types/index.ts
// types/index.ts

var ModalAction = /*#__PURE__*/function (ModalAction) {
  ModalAction["SUBMIT"] = "submit";
  ModalAction["CANCEL"] = "cancel";
  ModalAction["DELETE"] = "delete";
  return ModalAction;
}({});
;// CONCATENATED MODULE: ./src/pages/WarehouseManagementV3/Inventory/ExportHistory/components/ExportVoucherDetailEnhanced/hooks/useMainModalState.ts



// hooks/useModalState.ts


var useModalState = function useModalState() {
  var _useState = (0,react.useState)(defineProperty_default()(defineProperty_default()(defineProperty_default()({}, ModalAction.SUBMIT, false), ModalAction.CANCEL, false), ModalAction.DELETE, false)),
    _useState2 = slicedToArray_default()(_useState, 2),
    modalStates = _useState2[0],
    setModalStates = _useState2[1];
  var showModal = (0,react.useCallback)(function (action) {
    setModalStates(function (prev) {
      return objectSpread2_default()(objectSpread2_default()({}, prev), {}, defineProperty_default()({}, action, true));
    });
  }, []);
  var hideModal = (0,react.useCallback)(function (action) {
    setModalStates(function (prev) {
      return objectSpread2_default()(objectSpread2_default()({}, prev), {}, defineProperty_default()({}, action, false));
    });
  }, []);
  return {
    modalStates: modalStates,
    showModal: showModal,
    hideModal: hideModal
  };
};
;// CONCATENATED MODULE: ./src/pages/WarehouseManagementV3/Inventory/ExportHistory/components/ExportVoucherDetailEnhanced/index.tsx
























var ExportVoucherDetailEnhanced = function ExportVoucherDetailEnhanced(_ref) {
  var _store$savedVoucherDa;
  var name = _ref.name,
    _onSuccess = _ref.onSuccess,
    isModalOpen = _ref.isModalOpen,
    setIsModalOpen = _ref.setIsModalOpen;
  var _useExportVoucherLogi = useExportVoucherLogic(_onSuccess),
    handleSave = _useExportVoucherLogi.handleSave,
    handleSubmit = _useExportVoucherLogi.handleSubmit,
    handleCancel = _useExportVoucherLogi.handleCancel,
    handleDelete = _useExportVoucherLogi.handleDelete;
  var store = useExportVoucherDetailStore();
  var _Form$useForm = es_form/* default */.Z.useForm(),
    _Form$useForm2 = slicedToArray_default()(_Form$useForm, 1),
    form = _Form$useForm2[0];
  var _useModalState = useModalState(),
    modalStates = _useModalState.modalStates,
    showModal = _useModalState.showModal,
    hideModal = _useModalState.hideModal;
  var _useState = (0,react.useState)(null),
    _useState2 = slicedToArray_default()(_useState, 2),
    selectedActionComponent = _useState2[0],
    setSelectedActionComponent = _useState2[1];
  var handleActionSelect = function handleActionSelect(Component, initialData) {
    setIsModalOpen(false); // Close current modal
    setSelectedActionComponent( /*#__PURE__*/(0,jsx_runtime.jsx)(Component, {
      onSuccess: function onSuccess() {
        _onSuccess === null || _onSuccess === void 0 || _onSuccess();
        setSelectedActionComponent(null); // Clear after success
      },
      onClose: function onClose() {
        return setSelectedActionComponent(null);
      },
      initialData: initialData,
      autoOpen: true
    }));
  };
  var handleActionSuccess = /*#__PURE__*/function () {
    var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(action) {
      return regeneratorRuntime_default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _context.prev = 0;
            _context.next = 3;
            return action();
          case 3:
            setIsModalOpen(false);
            _onSuccess === null || _onSuccess === void 0 || _onSuccess();
            _context.next = 10;
            break;
          case 7:
            _context.prev = 7;
            _context.t0 = _context["catch"](0);
            console.error('Action failed:', _context.t0);
          case 10:
          case "end":
            return _context.stop();
        }
      }, _callee, null, [[0, 7]]);
    }));
    return function handleActionSuccess(_x) {
      return _ref2.apply(this, arguments);
    };
  }();
  var _useRequest = (0,_umi_production_exports.useRequest)(deliveryNote/* getDeliveryNoteDetail */.r7, {
      manual: true,
      onError: function onError(error) {
        console.error('Error fetching delivery note:', error.message);
      },
      onSuccess: function onSuccess(data) {
        return asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2() {
          var formattedPostingDate, customers, transformedItems;
          return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
            while (1) switch (_context2.prev = _context2.next) {
              case 0:
                formattedPostingDate = dayjs_min_default()(data.posting_date);
                form.setFieldsValue({
                  posting_date: formattedPostingDate,
                  warehouse: data.set_warehouse,
                  employee: data.iot_customer_user,
                  customer: data.customer,
                  description: data.description,
                  taxType: 'amount',
                  taxesValue: data.add_taxes,
                  discountType: 'amount',
                  discountValue: data.discount,
                  other_charges: data.other_charges,
                  paid_amount: data.paid_amount,
                  voucher_amount: data.voucher_amount,
                  total_amount: data.total_amount
                });
                _context2.next = 4;
                return fetchCustomerV3();
              case 4:
                customers = _context2.sent;
                store.setCustomerList(customers.data);
                store.setSavedVoucherData(data);
                store.setDocstatus(data.docstatus);
                store.setSelectedWarehouse(data.set_warehouse);
                store.setSelectedCustomer(data.customer);
                store.setTaxType('amount');
                store.setDiscountType('amount');
                store.setAddTaxes(data.add_taxes);
                store.setOtherCharges(data.other_charges);
                store.setDiscountAmount(data.discount);
                store.setPaidAmount(data.paid_amount);
                store.setVoucherAmount(data.voucher_amount);
                store.setTotalAmount(data.total_amount);
                if (data.items) {
                  transformedItems = data.items.map(function (item) {
                    return {
                      conversion_factor: item.conversion_factor,
                      item_code: item.item_code,
                      item_name: item.item_name,
                      qty: item.qty,
                      actual_qty: item.actual_qty,
                      rate: item.rate,
                      warehouse: item.warehouse,
                      total_price: item.amount,
                      item_label: item.item_label,
                      key: item.name,
                      uom_label: item.uom_name,
                      uom: item.uom,
                      name: item.name,
                      so_detail: item.so_detail
                    };
                  });
                  store.setSelectedItems(transformedItems);
                }
              case 19:
              case "end":
                return _context2.stop();
            }
          }, _callee2);
        }))();
      }
    }),
    run = _useRequest.run;
  (0,react.useEffect)(function () {
    store.setForm(form);
  }, [store.form]);
  (0,react.useEffect)(function () {
    if (isModalOpen) {
      run({
        name: name
      });
    }
  }, [name, isModalOpen]);
  var modalConfig = (0,react.useMemo)(function () {
    return defineProperty_default()(defineProperty_default()(defineProperty_default()({}, ModalAction.SUBMIT, {
      title: 'Ho\xE0n th\xE0nh phi\u1EBFu',
      message: 'B\u1EA1n c\xF3 ch\u1EAFc ch\u1EAFn mu\u1ED1n ho\xE0n th\xE0nh phi\u1EBFu?',
      description: 'Sau khi ho\xE0n th\xE0nh, phi\u1EBFu s\u1EBD kh\xF4ng th\u1EC3 ch\u1EC9nh s\u1EEDa.',
      action: function action() {
        return handleActionSuccess(handleSubmit);
      }
    }), ModalAction.CANCEL, {
      title: 'H\u1EE7y phi\u1EBFu',
      message: 'B\u1EA1n c\xF3 ch\u1EAFc ch\u1EAFn mu\u1ED1n h\u1EE7y phi\u1EBFu?',
      description: 'Sau khi h\u1EE7y, phi\u1EBFu s\u1EBD kh\xF4ng th\u1EC3 kh\xF4i ph\u1EE5c.',
      type: 'danger',
      action: function action() {
        return handleActionSuccess(handleCancel);
      }
    }), ModalAction.DELETE, {
      title: 'X\xF3a phi\u1EBFu',
      message: 'B\u1EA1n c\xF3 ch\u1EAFc ch\u1EAFn mu\u1ED1n x\xF3a phi\u1EBFu?',
      description: 'Phi\u1EBFu s\u1EBD b\u1ECB x\xF3a v\u0129nh vi\u1EC5n v\xE0 kh\xF4ng th\u1EC3 kh\xF4i ph\u1EE5c.',
      type: 'danger',
      action: function action() {
        return handleActionSuccess(handleDelete);
      }
    });
  }, [handleSubmit, handleCancel, handleDelete]);
  var renderActionButtons = function renderActionButtons() {
    var isEditable = store.docstatus === 0;
    var isSubmitted = store.docstatus === 1;
    return /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
      style: {
        display: 'flex',
        justifyContent: 'center',
        gap: '8px',
        width: '100%'
      },
      children: [isEditable && /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
          type: "primary",
          onClick: function onClick() {
            return handleActionSuccess( /*#__PURE__*/asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee3() {
              var values;
              return regeneratorRuntime_default()().wrap(function _callee3$(_context3) {
                while (1) switch (_context3.prev = _context3.next) {
                  case 0:
                    _context3.next = 2;
                    return form.validateFields();
                  case 2:
                    values = _context3.sent;
                    _context3.next = 5;
                    return handleSave(values);
                  case 5:
                  case "end":
                    return _context3.stop();
                }
              }, _callee3);
            })));
          },
          loading: store.submitting,
          style: {
            width: '150px'
          },
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
            id: "common.save"
          })
        }, "save"), /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
          type: "primary",
          onClick: function onClick() {
            return showModal(ModalAction.SUBMIT);
          },
          style: {
            width: '150px'
          },
          loading: store.submitting,
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
            id: "common.submit"
          })
        }, "submit"), /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
          danger: true,
          onClick: function onClick() {
            return showModal(ModalAction.DELETE);
          },
          style: {
            width: '150px'
          },
          loading: store.submitting,
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
            id: "common.delete"
          })
        }, "delete")]
      }), isSubmitted && /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
          danger: true,
          onClick: function onClick() {
            return showModal(ModalAction.CANCEL);
          },
          style: {
            width: '150px'
          },
          disabled: store.submitting,
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
            id: "common.cancel"
          })
        }, "cancel"), /*#__PURE__*/(0,jsx_runtime.jsx)(StockActionButton/* default */.Z, {
          voucherData: store.savedVoucherData,
          actions: voucherActions/* voucherActionConfigs */.A['Export Voucher'].map(function (action) {
            return objectSpread2_default()(objectSpread2_default()({}, action), {}, {
              onSelect: function onSelect() {
                return handleActionSelect(action.createComponent, action.mapData(store.savedVoucherData));
              }
            });
          }),
          onActionSuccess: _onSuccess,
          closeCurrentModal: function closeCurrentModal() {
            console.log('Closing current modal in ExportVoucherDetailEnhanced');
            setIsModalOpen(false);
          }
        })]
      })]
    });
  };
  return /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
    children: [/*#__PURE__*/(0,jsx_runtime.jsxs)(ModalForm/* ModalForm */.Y, {
      disabled: store.docstatus !== 0,
      open: isModalOpen,
      title: /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
          id: "warehouse-management.export-voucher"
        }), ' ', (_store$savedVoucherDa = store.savedVoucherData) === null || _store$savedVoucherDa === void 0 ? void 0 : _store$savedVoucherDa.name, " ", ' - ', /*#__PURE__*/(0,jsx_runtime.jsx)(components_DocStatusTag, {
          status: store.docstatus
        })]
      }),
      modalProps: {
        onCancel: function onCancel() {
          store.reset();
          setIsModalOpen(false);
        },
        destroyOnClose: true
      },
      width: 1600,
      form: form,
      layout: "vertical",
      rowProps: {
        gutter: [16, 0]
      },
      submitter: {
        render: renderActionButtons
      },
      autoFocusFirstInput: true,
      grid: true,
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)(spin/* default */.Z, {
        spinning: store.submitting,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(ExportVoucherForm, {})
      }), Object.entries(modalConfig).map(function (_ref5) {
        var _ref6 = slicedToArray_default()(_ref5, 2),
          action = _ref6[0],
          config = _ref6[1];
        return /*#__PURE__*/(0,jsx_runtime.jsx)(ConfirmMdal/* default */.Z, objectSpread2_default()(objectSpread2_default()({
          open: modalStates[action]
        }, config), {}, {
          onConfirm: /*#__PURE__*/asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee4() {
            return regeneratorRuntime_default()().wrap(function _callee4$(_context4) {
              while (1) switch (_context4.prev = _context4.next) {
                case 0:
                  hideModal(action);
                  _context4.next = 3;
                  return config.action();
                case 3:
                case "end":
                  return _context4.stop();
              }
            }, _callee4);
          })),
          onCancel: function onCancel() {
            return hideModal(action);
          },
          confirmLoading: store.submitting,
          maskClosable: false
        }), action);
      })]
    }), selectedActionComponent]
  });
};
/* harmony default export */ var components_ExportVoucherDetailEnhanced = (ExportVoucherDetailEnhanced);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///58409
`)}}]);
