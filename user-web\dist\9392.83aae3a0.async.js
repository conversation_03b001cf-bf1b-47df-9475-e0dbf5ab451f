"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[9392],{34804:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(1413);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(67294);
/* harmony import */ var _ant_design_icons_svg_es_asn_DownOutlined__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(66023);
/* harmony import */ var _components_AntdIcon__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(89099);

// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY



var DownOutlined = function DownOutlined(props, ref) {
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(_components_AntdIcon__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Z, (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z)((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z)({}, props), {}, {
    ref: ref,
    icon: _ant_design_icons_svg_es_asn_DownOutlined__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z
  }));
};
DownOutlined.displayName = 'DownOutlined';
/* harmony default export */ __webpack_exports__.Z = (/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(DownOutlined));//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiMzQ4MDQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFxRTtBQUNyRTtBQUNBO0FBQytCO0FBQ3lDO0FBQzFCO0FBQzlDO0FBQ0Esc0JBQXNCLGdEQUFtQixDQUFDLHFFQUFRLEVBQUUsNkZBQWEsQ0FBQyw2RkFBYSxHQUFHLFlBQVk7QUFDOUY7QUFDQSxVQUFVLDBGQUFlO0FBQ3pCLEdBQUc7QUFDSDtBQUNBO0FBQ0EsbUVBQTRCLDZDQUFnQixjQUFjIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvQGFudC1kZXNpZ24vaWNvbnMvZXMvaWNvbnMvRG93bk91dGxpbmVkLmpzP2VhNzAiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL29iamVjdFNwcmVhZDJcIjtcbi8vIEdFTkVSQVRFIEJZIC4vc2NyaXB0cy9nZW5lcmF0ZS50c1xuLy8gRE9OIE5PVCBFRElUIElUIE1BTlVBTExZXG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgRG93bk91dGxpbmVkU3ZnIGZyb20gXCJAYW50LWRlc2lnbi9pY29ucy1zdmcvZXMvYXNuL0Rvd25PdXRsaW5lZFwiO1xuaW1wb3J0IEFudGRJY29uIGZyb20gJy4uL2NvbXBvbmVudHMvQW50ZEljb24nO1xudmFyIERvd25PdXRsaW5lZCA9IGZ1bmN0aW9uIERvd25PdXRsaW5lZChwcm9wcywgcmVmKSB7XG4gIHJldHVybiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChBbnRkSWNvbiwgX29iamVjdFNwcmVhZChfb2JqZWN0U3ByZWFkKHt9LCBwcm9wcyksIHt9LCB7XG4gICAgcmVmOiByZWYsXG4gICAgaWNvbjogRG93bk91dGxpbmVkU3ZnXG4gIH0pKTtcbn07XG5Eb3duT3V0bGluZWQuZGlzcGxheU5hbWUgPSAnRG93bk91dGxpbmVkJztcbmV4cG9ydCBkZWZhdWx0IC8qI19fUFVSRV9fKi9SZWFjdC5mb3J3YXJkUmVmKERvd25PdXRsaW5lZCk7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9
//# sourceURL=webpack-internal:///34804
`)},69753:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(1413);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(67294);
/* harmony import */ var _ant_design_icons_svg_es_asn_DownloadOutlined__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(49495);
/* harmony import */ var _components_AntdIcon__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(89099);

// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY



var DownloadOutlined = function DownloadOutlined(props, ref) {
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(_components_AntdIcon__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Z, (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z)((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z)({}, props), {}, {
    ref: ref,
    icon: _ant_design_icons_svg_es_asn_DownloadOutlined__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z
  }));
};
DownloadOutlined.displayName = 'DownloadOutlined';
/* harmony default export */ __webpack_exports__.Z = (/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(DownloadOutlined));//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNjk3NTMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFxRTtBQUNyRTtBQUNBO0FBQytCO0FBQ2lEO0FBQ2xDO0FBQzlDO0FBQ0Esc0JBQXNCLGdEQUFtQixDQUFDLHFFQUFRLEVBQUUsNkZBQWEsQ0FBQyw2RkFBYSxHQUFHLFlBQVk7QUFDOUY7QUFDQSxVQUFVLDhGQUFtQjtBQUM3QixHQUFHO0FBQ0g7QUFDQTtBQUNBLG1FQUE0Qiw2Q0FBZ0Isa0JBQWtCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvQGFudC1kZXNpZ24vaWNvbnMvZXMvaWNvbnMvRG93bmxvYWRPdXRsaW5lZC5qcz85NzgxIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBfb2JqZWN0U3ByZWFkIGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9vYmplY3RTcHJlYWQyXCI7XG4vLyBHRU5FUkFURSBCWSAuL3NjcmlwdHMvZ2VuZXJhdGUudHNcbi8vIERPTiBOT1QgRURJVCBJVCBNQU5VQUxMWVxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IERvd25sb2FkT3V0bGluZWRTdmcgZnJvbSBcIkBhbnQtZGVzaWduL2ljb25zLXN2Zy9lcy9hc24vRG93bmxvYWRPdXRsaW5lZFwiO1xuaW1wb3J0IEFudGRJY29uIGZyb20gJy4uL2NvbXBvbmVudHMvQW50ZEljb24nO1xudmFyIERvd25sb2FkT3V0bGluZWQgPSBmdW5jdGlvbiBEb3dubG9hZE91dGxpbmVkKHByb3BzLCByZWYpIHtcbiAgcmV0dXJuIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KEFudGRJY29uLCBfb2JqZWN0U3ByZWFkKF9vYmplY3RTcHJlYWQoe30sIHByb3BzKSwge30sIHtcbiAgICByZWY6IHJlZixcbiAgICBpY29uOiBEb3dubG9hZE91dGxpbmVkU3ZnXG4gIH0pKTtcbn07XG5Eb3dubG9hZE91dGxpbmVkLmRpc3BsYXlOYW1lID0gJ0Rvd25sb2FkT3V0bGluZWQnO1xuZXhwb3J0IGRlZmF1bHQgLyojX19QVVJFX18qL1JlYWN0LmZvcndhcmRSZWYoRG93bmxvYWRPdXRsaW5lZCk7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9
//# sourceURL=webpack-internal:///69753
`)},11475:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`
// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  Z: function() { return /* binding */ icons_ExclamationCircleOutlined; }
});

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectSpread2.js
var objectSpread2 = __webpack_require__(1413);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
;// CONCATENATED MODULE: ./node_modules/@ant-design/icons-svg/es/asn/ExclamationCircleOutlined.js
// This icon file is generated automatically.
var ExclamationCircleOutlined = { "icon": { "tag": "svg", "attrs": { "viewBox": "64 64 896 896", "focusable": "false" }, "children": [{ "tag": "path", "attrs": { "d": "M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z" } }, { "tag": "path", "attrs": { "d": "M464 688a48 48 0 1096 0 48 48 0 10-96 0zm24-112h48c4.4 0 8-3.6 8-8V296c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8z" } }] }, "name": "exclamation-circle", "theme": "outlined" };
/* harmony default export */ var asn_ExclamationCircleOutlined = (ExclamationCircleOutlined);

// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/components/AntdIcon.js + 5 modules
var AntdIcon = __webpack_require__(89099);
;// CONCATENATED MODULE: ./node_modules/@ant-design/icons/es/icons/ExclamationCircleOutlined.js

// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY



var ExclamationCircleOutlined_ExclamationCircleOutlined = function ExclamationCircleOutlined(props, ref) {
  return /*#__PURE__*/react.createElement(AntdIcon/* default */.Z, (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, props), {}, {
    ref: ref,
    icon: asn_ExclamationCircleOutlined
  }));
};
ExclamationCircleOutlined_ExclamationCircleOutlined.displayName = 'ExclamationCircleOutlined';
/* harmony default export */ var icons_ExclamationCircleOutlined = (/*#__PURE__*/react.forwardRef(ExclamationCircleOutlined_ExclamationCircleOutlined));//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///11475
`)},58638:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`
// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  Z: function() { return /* binding */ icons_ExportOutlined; }
});

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectSpread2.js
var objectSpread2 = __webpack_require__(1413);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
;// CONCATENATED MODULE: ./node_modules/@ant-design/icons-svg/es/asn/ExportOutlined.js
// This icon file is generated automatically.
var ExportOutlined = { "icon": { "tag": "svg", "attrs": { "fill-rule": "evenodd", "viewBox": "64 64 896 896", "focusable": "false" }, "children": [{ "tag": "path", "attrs": { "d": "M880 912H144c-17.7 0-32-14.3-32-32V144c0-17.7 14.3-32 32-32h360c4.4 0 8 3.6 8 8v56c0 4.4-3.6 8-8 8H184v656h656V520c0-4.4 3.6-8 8-8h56c4.4 0 8 3.6 8 8v360c0 17.7-14.3 32-32 32zM770.87 199.13l-52.2-52.2a8.01 8.01 0 014.7-13.6l179.4-21c5.1-.6 9.5 3.7 8.9 8.9l-21 179.4c-.8 6.6-8.9 9.4-13.6 4.7l-52.4-52.4-256.2 256.2a8.03 8.03 0 01-11.3 0l-42.4-42.4a8.03 8.03 0 010-11.3l256.1-256.3z" } }] }, "name": "export", "theme": "outlined" };
/* harmony default export */ var asn_ExportOutlined = (ExportOutlined);

// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/components/AntdIcon.js + 5 modules
var AntdIcon = __webpack_require__(89099);
;// CONCATENATED MODULE: ./node_modules/@ant-design/icons/es/icons/ExportOutlined.js

// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY



var ExportOutlined_ExportOutlined = function ExportOutlined(props, ref) {
  return /*#__PURE__*/react.createElement(AntdIcon/* default */.Z, (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, props), {}, {
    ref: ref,
    icon: asn_ExportOutlined
  }));
};
ExportOutlined_ExportOutlined.displayName = 'ExportOutlined';
/* harmony default export */ var icons_ExportOutlined = (/*#__PURE__*/react.forwardRef(ExportOutlined_ExportOutlined));//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///58638
`)},55287:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(1413);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(67294);
/* harmony import */ var _ant_design_icons_svg_es_asn_EyeOutlined__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(5717);
/* harmony import */ var _components_AntdIcon__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(89099);

// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY



var EyeOutlined = function EyeOutlined(props, ref) {
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(_components_AntdIcon__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Z, (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z)((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z)({}, props), {}, {
    ref: ref,
    icon: _ant_design_icons_svg_es_asn_EyeOutlined__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z
  }));
};
EyeOutlined.displayName = 'EyeOutlined';
/* harmony default export */ __webpack_exports__.Z = (/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(EyeOutlined));//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNTUyODcuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFxRTtBQUNyRTtBQUNBO0FBQytCO0FBQ3VDO0FBQ3hCO0FBQzlDO0FBQ0Esc0JBQXNCLGdEQUFtQixDQUFDLHFFQUFRLEVBQUUsNkZBQWEsQ0FBQyw2RkFBYSxHQUFHLFlBQVk7QUFDOUY7QUFDQSxVQUFVLHlGQUFjO0FBQ3hCLEdBQUc7QUFDSDtBQUNBO0FBQ0EsbUVBQTRCLDZDQUFnQixhQUFhIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvQGFudC1kZXNpZ24vaWNvbnMvZXMvaWNvbnMvRXllT3V0bGluZWQuanM/OWM2ZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgX29iamVjdFNwcmVhZCBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vb2JqZWN0U3ByZWFkMlwiO1xuLy8gR0VORVJBVEUgQlkgLi9zY3JpcHRzL2dlbmVyYXRlLnRzXG4vLyBET04gTk9UIEVESVQgSVQgTUFOVUFMTFlcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCBFeWVPdXRsaW5lZFN2ZyBmcm9tIFwiQGFudC1kZXNpZ24vaWNvbnMtc3ZnL2VzL2Fzbi9FeWVPdXRsaW5lZFwiO1xuaW1wb3J0IEFudGRJY29uIGZyb20gJy4uL2NvbXBvbmVudHMvQW50ZEljb24nO1xudmFyIEV5ZU91dGxpbmVkID0gZnVuY3Rpb24gRXllT3V0bGluZWQocHJvcHMsIHJlZikge1xuICByZXR1cm4gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoQW50ZEljb24sIF9vYmplY3RTcHJlYWQoX29iamVjdFNwcmVhZCh7fSwgcHJvcHMpLCB7fSwge1xuICAgIHJlZjogcmVmLFxuICAgIGljb246IEV5ZU91dGxpbmVkU3ZnXG4gIH0pKTtcbn07XG5FeWVPdXRsaW5lZC5kaXNwbGF5TmFtZSA9ICdFeWVPdXRsaW5lZCc7XG5leHBvcnQgZGVmYXVsdCAvKiNfX1BVUkVfXyovUmVhY3QuZm9yd2FyZFJlZihFeWVPdXRsaW5lZCk7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9
//# sourceURL=webpack-internal:///55287
`)},57546:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`
// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  Z: function() { return /* binding */ icons_ImportOutlined; }
});

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectSpread2.js
var objectSpread2 = __webpack_require__(1413);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
;// CONCATENATED MODULE: ./node_modules/@ant-design/icons-svg/es/asn/ImportOutlined.js
// This icon file is generated automatically.
var ImportOutlined = { "icon": { "tag": "svg", "attrs": { "fill-rule": "evenodd", "viewBox": "64 64 896 896", "focusable": "false" }, "children": [{ "tag": "path", "attrs": { "d": "M880 912H144c-17.7 0-32-14.3-32-32V144c0-17.7 14.3-32 32-32h360c4.4 0 8 3.6 8 8v56c0 4.4-3.6 8-8 8H184v656h656V520c0-4.4 3.6-8 8-8h56c4.4 0 8 3.6 8 8v360c0 17.7-14.3 32-32 32zM653.3 424.6l52.2 52.2a8.01 8.01 0 01-4.7 13.6l-179.4 21c-5.1.6-9.5-3.7-8.9-8.9l21-179.4c.8-6.6 8.9-9.4 13.6-4.7l52.4 52.4 256.2-256.2c3.1-3.1 8.2-3.1 11.3 0l42.4 42.4c3.1 3.1 3.1 8.2 0 11.3L653.3 424.6z" } }] }, "name": "import", "theme": "outlined" };
/* harmony default export */ var asn_ImportOutlined = (ImportOutlined);

// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/components/AntdIcon.js + 5 modules
var AntdIcon = __webpack_require__(89099);
;// CONCATENATED MODULE: ./node_modules/@ant-design/icons/es/icons/ImportOutlined.js

// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY



var ImportOutlined_ImportOutlined = function ImportOutlined(props, ref) {
  return /*#__PURE__*/react.createElement(AntdIcon/* default */.Z, (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, props), {}, {
    ref: ref,
    icon: asn_ImportOutlined
  }));
};
ImportOutlined_ImportOutlined.displayName = 'ImportOutlined';
/* harmony default export */ var icons_ImportOutlined = (/*#__PURE__*/react.forwardRef(ImportOutlined_ImportOutlined));//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///57546
`)},56717:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(1413);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(67294);
/* harmony import */ var _ant_design_icons_svg_es_asn_InfoCircleOutlined__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(93696);
/* harmony import */ var _components_AntdIcon__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(89099);

// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY



var InfoCircleOutlined = function InfoCircleOutlined(props, ref) {
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(_components_AntdIcon__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Z, (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z)((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z)({}, props), {}, {
    ref: ref,
    icon: _ant_design_icons_svg_es_asn_InfoCircleOutlined__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z
  }));
};
InfoCircleOutlined.displayName = 'InfoCircleOutlined';
/* harmony default export */ __webpack_exports__.Z = (/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(InfoCircleOutlined));//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNTY3MTcuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFxRTtBQUNyRTtBQUNBO0FBQytCO0FBQ3FEO0FBQ3RDO0FBQzlDO0FBQ0Esc0JBQXNCLGdEQUFtQixDQUFDLHFFQUFRLEVBQUUsNkZBQWEsQ0FBQyw2RkFBYSxHQUFHLFlBQVk7QUFDOUY7QUFDQSxVQUFVLGdHQUFxQjtBQUMvQixHQUFHO0FBQ0g7QUFDQTtBQUNBLG1FQUE0Qiw2Q0FBZ0Isb0JBQW9CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvQGFudC1kZXNpZ24vaWNvbnMvZXMvaWNvbnMvSW5mb0NpcmNsZU91dGxpbmVkLmpzP2M5MTMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL29iamVjdFNwcmVhZDJcIjtcbi8vIEdFTkVSQVRFIEJZIC4vc2NyaXB0cy9nZW5lcmF0ZS50c1xuLy8gRE9OIE5PVCBFRElUIElUIE1BTlVBTExZXG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgSW5mb0NpcmNsZU91dGxpbmVkU3ZnIGZyb20gXCJAYW50LWRlc2lnbi9pY29ucy1zdmcvZXMvYXNuL0luZm9DaXJjbGVPdXRsaW5lZFwiO1xuaW1wb3J0IEFudGRJY29uIGZyb20gJy4uL2NvbXBvbmVudHMvQW50ZEljb24nO1xudmFyIEluZm9DaXJjbGVPdXRsaW5lZCA9IGZ1bmN0aW9uIEluZm9DaXJjbGVPdXRsaW5lZChwcm9wcywgcmVmKSB7XG4gIHJldHVybiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChBbnRkSWNvbiwgX29iamVjdFNwcmVhZChfb2JqZWN0U3ByZWFkKHt9LCBwcm9wcyksIHt9LCB7XG4gICAgcmVmOiByZWYsXG4gICAgaWNvbjogSW5mb0NpcmNsZU91dGxpbmVkU3ZnXG4gIH0pKTtcbn07XG5JbmZvQ2lyY2xlT3V0bGluZWQuZGlzcGxheU5hbWUgPSAnSW5mb0NpcmNsZU91dGxpbmVkJztcbmV4cG9ydCBkZWZhdWx0IC8qI19fUFVSRV9fKi9SZWFjdC5mb3J3YXJkUmVmKEluZm9DaXJjbGVPdXRsaW5lZCk7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9
//# sourceURL=webpack-internal:///56717
`)},30723:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(1413);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(67294);
/* harmony import */ var _ant_design_icons_svg_es_asn_PlusSquareOutlined__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(43114);
/* harmony import */ var _components_AntdIcon__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(89099);

// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY



var PlusSquareOutlined = function PlusSquareOutlined(props, ref) {
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(_components_AntdIcon__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Z, (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z)((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z)({}, props), {}, {
    ref: ref,
    icon: _ant_design_icons_svg_es_asn_PlusSquareOutlined__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z
  }));
};
PlusSquareOutlined.displayName = 'PlusSquareOutlined';
/* harmony default export */ __webpack_exports__.Z = (/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(PlusSquareOutlined));//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiMzA3MjMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFxRTtBQUNyRTtBQUNBO0FBQytCO0FBQ3FEO0FBQ3RDO0FBQzlDO0FBQ0Esc0JBQXNCLGdEQUFtQixDQUFDLHFFQUFRLEVBQUUsNkZBQWEsQ0FBQyw2RkFBYSxHQUFHLFlBQVk7QUFDOUY7QUFDQSxVQUFVLGdHQUFxQjtBQUMvQixHQUFHO0FBQ0g7QUFDQTtBQUNBLG1FQUE0Qiw2Q0FBZ0Isb0JBQW9CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvQGFudC1kZXNpZ24vaWNvbnMvZXMvaWNvbnMvUGx1c1NxdWFyZU91dGxpbmVkLmpzP2JkZGEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL29iamVjdFNwcmVhZDJcIjtcbi8vIEdFTkVSQVRFIEJZIC4vc2NyaXB0cy9nZW5lcmF0ZS50c1xuLy8gRE9OIE5PVCBFRElUIElUIE1BTlVBTExZXG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgUGx1c1NxdWFyZU91dGxpbmVkU3ZnIGZyb20gXCJAYW50LWRlc2lnbi9pY29ucy1zdmcvZXMvYXNuL1BsdXNTcXVhcmVPdXRsaW5lZFwiO1xuaW1wb3J0IEFudGRJY29uIGZyb20gJy4uL2NvbXBvbmVudHMvQW50ZEljb24nO1xudmFyIFBsdXNTcXVhcmVPdXRsaW5lZCA9IGZ1bmN0aW9uIFBsdXNTcXVhcmVPdXRsaW5lZChwcm9wcywgcmVmKSB7XG4gIHJldHVybiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChBbnRkSWNvbiwgX29iamVjdFNwcmVhZChfb2JqZWN0U3ByZWFkKHt9LCBwcm9wcyksIHt9LCB7XG4gICAgcmVmOiByZWYsXG4gICAgaWNvbjogUGx1c1NxdWFyZU91dGxpbmVkU3ZnXG4gIH0pKTtcbn07XG5QbHVzU3F1YXJlT3V0bGluZWQuZGlzcGxheU5hbWUgPSAnUGx1c1NxdWFyZU91dGxpbmVkJztcbmV4cG9ydCBkZWZhdWx0IC8qI19fUFVSRV9fKi9SZWFjdC5mb3J3YXJkUmVmKFBsdXNTcXVhcmVPdXRsaW5lZCk7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9
//# sourceURL=webpack-internal:///30723
`)},30019:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`
// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  Z: function() { return /* binding */ icons_PrinterOutlined; }
});

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectSpread2.js
var objectSpread2 = __webpack_require__(1413);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
;// CONCATENATED MODULE: ./node_modules/@ant-design/icons-svg/es/asn/PrinterOutlined.js
// This icon file is generated automatically.
var PrinterOutlined = { "icon": { "tag": "svg", "attrs": { "viewBox": "64 64 896 896", "focusable": "false" }, "children": [{ "tag": "path", "attrs": { "d": "M820 436h-40c-4.4 0-8 3.6-8 8v40c0 4.4 3.6 8 8 8h40c4.4 0 8-3.6 8-8v-40c0-4.4-3.6-8-8-8zm32-104H732V120c0-4.4-3.6-8-8-8H300c-4.4 0-8 3.6-8 8v212H172c-44.2 0-80 35.8-80 80v328c0 17.7 14.3 32 32 32h168v132c0 4.4 3.6 8 8 8h424c4.4 0 8-3.6 8-8V772h168c17.7 0 32-14.3 32-32V412c0-44.2-35.8-80-80-80zM360 180h304v152H360V180zm304 664H360V568h304v276zm200-140H732V500H292v204H160V412c0-6.6 5.4-12 12-12h680c6.6 0 12 5.4 12 12v292z" } }] }, "name": "printer", "theme": "outlined" };
/* harmony default export */ var asn_PrinterOutlined = (PrinterOutlined);

// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/components/AntdIcon.js + 5 modules
var AntdIcon = __webpack_require__(89099);
;// CONCATENATED MODULE: ./node_modules/@ant-design/icons/es/icons/PrinterOutlined.js

// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY



var PrinterOutlined_PrinterOutlined = function PrinterOutlined(props, ref) {
  return /*#__PURE__*/react.createElement(AntdIcon/* default */.Z, (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, props), {}, {
    ref: ref,
    icon: asn_PrinterOutlined
  }));
};
PrinterOutlined_PrinterOutlined.displayName = 'PrinterOutlined';
/* harmony default export */ var icons_PrinterOutlined = (/*#__PURE__*/react.forwardRef(PrinterOutlined_PrinterOutlined));//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///30019
`)},58267:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`
// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  Z: function() { return /* binding */ icons_ReconciliationOutlined; }
});

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectSpread2.js
var objectSpread2 = __webpack_require__(1413);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
;// CONCATENATED MODULE: ./node_modules/@ant-design/icons-svg/es/asn/ReconciliationOutlined.js
// This icon file is generated automatically.
var ReconciliationOutlined = { "icon": { "tag": "svg", "attrs": { "viewBox": "64 64 896 896", "focusable": "false" }, "children": [{ "tag": "path", "attrs": { "d": "M676 565c-50.8 0-92 41.2-92 92s41.2 92 92 92 92-41.2 92-92-41.2-92-92-92zm0 126c-18.8 0-34-15.2-34-34s15.2-34 34-34 34 15.2 34 34-15.2 34-34 34zm204-523H668c0-30.9-25.1-56-56-56h-80c-30.9 0-56 25.1-56 56H264c-17.7 0-32 14.3-32 32v200h-88c-17.7 0-32 14.3-32 32v448c0 17.7 14.3 32 32 32h336c17.7 0 32-14.3 32-32v-16h368c17.7 0 32-14.3 32-32V200c0-17.7-14.3-32-32-32zm-412 64h72v-56h64v56h72v48H468v-48zm-20 616H176V616h272v232zm0-296H176v-88h272v88zm392 240H512V432c0-17.7-14.3-32-32-32H304V240h100v104h336V240h100v552zM704 408v96c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8v-96c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8zM592 512h48c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8z" } }] }, "name": "reconciliation", "theme": "outlined" };
/* harmony default export */ var asn_ReconciliationOutlined = (ReconciliationOutlined);

// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/components/AntdIcon.js + 5 modules
var AntdIcon = __webpack_require__(89099);
;// CONCATENATED MODULE: ./node_modules/@ant-design/icons/es/icons/ReconciliationOutlined.js

// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY



var ReconciliationOutlined_ReconciliationOutlined = function ReconciliationOutlined(props, ref) {
  return /*#__PURE__*/react.createElement(AntdIcon/* default */.Z, (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, props), {}, {
    ref: ref,
    icon: asn_ReconciliationOutlined
  }));
};
ReconciliationOutlined_ReconciliationOutlined.displayName = 'ReconciliationOutlined';
/* harmony default export */ var icons_ReconciliationOutlined = (/*#__PURE__*/react.forwardRef(ReconciliationOutlined_ReconciliationOutlined));//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///58267
`)},75517:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`
// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  Z: function() { return /* binding */ icons_TransactionOutlined; }
});

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectSpread2.js
var objectSpread2 = __webpack_require__(1413);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
;// CONCATENATED MODULE: ./node_modules/@ant-design/icons-svg/es/asn/TransactionOutlined.js
// This icon file is generated automatically.
var TransactionOutlined = { "icon": { "tag": "svg", "attrs": { "viewBox": "64 64 896 896", "focusable": "false" }, "children": [{ "tag": "path", "attrs": { "d": "M668.6 320c0-4.4-3.6-8-8-8h-54.5c-3 0-5.8 1.7-7.1 4.4l-84.7 168.8H511l-84.7-168.8a8 8 0 00-7.1-4.4h-55.7c-1.3 0-2.6.3-3.8 1-3.9 2.1-5.3 7-3.2 10.8l103.9 191.6h-57c-4.4 0-8 3.6-8 8v27.1c0 4.4 3.6 8 8 8h76v39h-76c-4.4 0-8 3.6-8 8v27.1c0 4.4 3.6 8 8 8h76V704c0 4.4 3.6 8 8 8h49.9c4.4 0 8-3.6 8-8v-63.5h76.3c4.4 0 8-3.6 8-8v-27.1c0-4.4-3.6-8-8-8h-76.3v-39h76.3c4.4 0 8-3.6 8-8v-27.1c0-4.4-3.6-8-8-8H564l103.7-191.6c.5-1.1.9-2.4.9-3.7zM157.9 504.2a352.7 352.7 0 01103.5-242.4c32.5-32.5 70.3-58.1 112.4-75.9 43.6-18.4 89.9-27.8 137.6-27.8 47.8 0 94.1 9.3 137.6 27.8 42.1 17.8 79.9 43.4 112.4 75.9 10 10 19.3 20.5 27.9 31.4l-50 39.1a8 8 0 003 14.1l156.8 38.3c5 1.2 9.9-2.6 9.9-7.7l.8-161.5c0-6.7-7.7-10.5-12.9-6.3l-47.8 37.4C770.7 146.3 648.6 82 511.5 82 277 82 86.3 270.1 82 503.8a8 8 0 008 8.2h60c4.3 0 7.8-3.5 7.9-7.8zM934 512h-60c-4.3 0-7.9 3.5-8 7.8a352.7 352.7 0 01-103.5 242.4 352.57 352.57 0 01-112.4 75.9c-43.6 18.4-89.9 27.8-137.6 27.8s-94.1-9.3-137.6-27.8a352.57 352.57 0 01-112.4-75.9c-10-10-19.3-20.5-27.9-31.4l49.9-39.1a8 8 0 00-3-14.1l-156.8-38.3c-5-1.2-9.9 2.6-9.9 7.7l-.8 161.7c0 6.7 7.7 10.5 12.9 6.3l47.8-37.4C253.3 877.7 375.4 942 512.5 942 747 942 937.7 753.9 942 520.2a8 8 0 00-8-8.2z" } }] }, "name": "transaction", "theme": "outlined" };
/* harmony default export */ var asn_TransactionOutlined = (TransactionOutlined);

// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/components/AntdIcon.js + 5 modules
var AntdIcon = __webpack_require__(89099);
;// CONCATENATED MODULE: ./node_modules/@ant-design/icons/es/icons/TransactionOutlined.js

// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY



var TransactionOutlined_TransactionOutlined = function TransactionOutlined(props, ref) {
  return /*#__PURE__*/react.createElement(AntdIcon/* default */.Z, (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, props), {}, {
    ref: ref,
    icon: asn_TransactionOutlined
  }));
};
TransactionOutlined_TransactionOutlined.displayName = 'TransactionOutlined';
/* harmony default export */ var icons_TransactionOutlined = (/*#__PURE__*/react.forwardRef(TransactionOutlined_TransactionOutlined));//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///75517
`)},26859:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(1413);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(67294);
/* harmony import */ var _ant_design_icons_svg_es_asn_UploadOutlined__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(85170);
/* harmony import */ var _components_AntdIcon__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(89099);

// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY



var UploadOutlined = function UploadOutlined(props, ref) {
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(_components_AntdIcon__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Z, (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z)((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z)({}, props), {}, {
    ref: ref,
    icon: _ant_design_icons_svg_es_asn_UploadOutlined__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z
  }));
};
UploadOutlined.displayName = 'UploadOutlined';
/* harmony default export */ __webpack_exports__.Z = (/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(UploadOutlined));//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiMjY4NTkuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFxRTtBQUNyRTtBQUNBO0FBQytCO0FBQzZDO0FBQzlCO0FBQzlDO0FBQ0Esc0JBQXNCLGdEQUFtQixDQUFDLHFFQUFRLEVBQUUsNkZBQWEsQ0FBQyw2RkFBYSxHQUFHLFlBQVk7QUFDOUY7QUFDQSxVQUFVLDRGQUFpQjtBQUMzQixHQUFHO0FBQ0g7QUFDQTtBQUNBLG1FQUE0Qiw2Q0FBZ0IsZ0JBQWdCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvQGFudC1kZXNpZ24vaWNvbnMvZXMvaWNvbnMvVXBsb2FkT3V0bGluZWQuanM/MDE3NSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgX29iamVjdFNwcmVhZCBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vb2JqZWN0U3ByZWFkMlwiO1xuLy8gR0VORVJBVEUgQlkgLi9zY3JpcHRzL2dlbmVyYXRlLnRzXG4vLyBET04gTk9UIEVESVQgSVQgTUFOVUFMTFlcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCBVcGxvYWRPdXRsaW5lZFN2ZyBmcm9tIFwiQGFudC1kZXNpZ24vaWNvbnMtc3ZnL2VzL2Fzbi9VcGxvYWRPdXRsaW5lZFwiO1xuaW1wb3J0IEFudGRJY29uIGZyb20gJy4uL2NvbXBvbmVudHMvQW50ZEljb24nO1xudmFyIFVwbG9hZE91dGxpbmVkID0gZnVuY3Rpb24gVXBsb2FkT3V0bGluZWQocHJvcHMsIHJlZikge1xuICByZXR1cm4gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoQW50ZEljb24sIF9vYmplY3RTcHJlYWQoX29iamVjdFNwcmVhZCh7fSwgcHJvcHMpLCB7fSwge1xuICAgIHJlZjogcmVmLFxuICAgIGljb246IFVwbG9hZE91dGxpbmVkU3ZnXG4gIH0pKTtcbn07XG5VcGxvYWRPdXRsaW5lZC5kaXNwbGF5TmFtZSA9ICdVcGxvYWRPdXRsaW5lZCc7XG5leHBvcnQgZGVmYXVsdCAvKiNfX1BVUkVfXyovUmVhY3QuZm9yd2FyZFJlZihVcGxvYWRPdXRsaW5lZCk7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9
//# sourceURL=webpack-internal:///26859
`)},50335:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`
// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  Z: function() { return /* binding */ components_DatePicker; }
});

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectSpread2.js
var objectSpread2 = __webpack_require__(1413);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js + 1 modules
var objectWithoutProperties = __webpack_require__(91);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/FieldContext.js
var FieldContext = __webpack_require__(66758);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/Field/index.js + 190 modules
var Field = __webpack_require__(265);
// EXTERNAL MODULE: ./node_modules/react/jsx-runtime.js
var jsx_runtime = __webpack_require__(85893);
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-form/es/components/DatePicker/DatePicker.js


var _excluded = ["proFieldProps", "fieldProps"];




var valueType = 'date';
/**
 * \u65E5\u671F\u9009\u62E9\u7EC4\u4EF6
 *
 * @param
 */
var ProFormDatePicker = /*#__PURE__*/react.forwardRef(function (_ref, ref) {
  var proFieldProps = _ref.proFieldProps,
    fieldProps = _ref.fieldProps,
    rest = (0,objectWithoutProperties/* default */.Z)(_ref, _excluded);
  var context = (0,react.useContext)(FieldContext/* default */.Z);
  return /*#__PURE__*/(0,jsx_runtime.jsx)(Field/* default */.Z, (0,objectSpread2/* default */.Z)({
    ref: ref,
    valueType: valueType,
    fieldProps: (0,objectSpread2/* default */.Z)({
      getPopupContainer: context.getPopupContainer
    }, fieldProps),
    proFieldProps: proFieldProps,
    filedConfig: {
      valueType: valueType,
      customLightMode: true
    }
  }, rest));
});
/* harmony default export */ var DatePicker = (ProFormDatePicker);
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-form/es/components/DatePicker/MonthPicker.js


var MonthPicker_excluded = ["proFieldProps", "fieldProps"];




var MonthPicker_valueType = 'dateMonth';
/**
 * \u5468\u9009\u62E9\u7EC4\u4EF6
 *
 * @param
 */
var ProFormDatePickerMonth = /*#__PURE__*/react.forwardRef(function (_ref, ref) {
  var proFieldProps = _ref.proFieldProps,
    fieldProps = _ref.fieldProps,
    rest = (0,objectWithoutProperties/* default */.Z)(_ref, MonthPicker_excluded);
  var context = (0,react.useContext)(FieldContext/* default */.Z);
  return /*#__PURE__*/(0,jsx_runtime.jsx)(Field/* default */.Z, (0,objectSpread2/* default */.Z)({
    ref: ref,
    valueType: MonthPicker_valueType,
    fieldProps: (0,objectSpread2/* default */.Z)({
      getPopupContainer: context.getPopupContainer
    }, fieldProps),
    proFieldProps: proFieldProps,
    filedConfig: {
      valueType: MonthPicker_valueType,
      customLightMode: true
    }
  }, rest));
});
/* harmony default export */ var MonthPicker = (ProFormDatePickerMonth);
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-form/es/components/DatePicker/QuarterPicker.js


var QuarterPicker_excluded = ["fieldProps"];




var QuarterPicker_valueType = 'dateQuarter';
/**
 * \u5468\u9009\u62E9\u7EC4\u4EF6
 *
 * @param
 */
var ProFormDatePickerQuarter = /*#__PURE__*/react.forwardRef(function (_ref, ref) {
  var fieldProps = _ref.fieldProps,
    rest = (0,objectWithoutProperties/* default */.Z)(_ref, QuarterPicker_excluded);
  var context = (0,react.useContext)(FieldContext/* default */.Z);
  return /*#__PURE__*/(0,jsx_runtime.jsx)(Field/* default */.Z, (0,objectSpread2/* default */.Z)({
    ref: ref,
    valueType: QuarterPicker_valueType,
    fieldProps: (0,objectSpread2/* default */.Z)({
      getPopupContainer: context.getPopupContainer
    }, fieldProps),
    filedConfig: {
      valueType: QuarterPicker_valueType,
      customLightMode: true
    }
  }, rest));
});
/* harmony default export */ var QuarterPicker = (ProFormDatePickerQuarter);
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-form/es/components/DatePicker/WeekPicker.js


var WeekPicker_excluded = ["proFieldProps", "fieldProps"];




var WeekPicker_valueType = 'dateWeek';
/**
 * \u5468\u9009\u62E9\u7EC4\u4EF6
 *
 * @param
 */
var ProFormDatePickerWeek = /*#__PURE__*/react.forwardRef(function (_ref, ref) {
  var proFieldProps = _ref.proFieldProps,
    fieldProps = _ref.fieldProps,
    rest = (0,objectWithoutProperties/* default */.Z)(_ref, WeekPicker_excluded);
  var context = (0,react.useContext)(FieldContext/* default */.Z);
  return /*#__PURE__*/(0,jsx_runtime.jsx)(Field/* default */.Z, (0,objectSpread2/* default */.Z)({
    ref: ref,
    valueType: WeekPicker_valueType,
    fieldProps: (0,objectSpread2/* default */.Z)({
      getPopupContainer: context.getPopupContainer
    }, fieldProps),
    proFieldProps: proFieldProps,
    filedConfig: {
      valueType: WeekPicker_valueType,
      customLightMode: true
    }
  }, rest));
});
/* harmony default export */ var WeekPicker = (ProFormDatePickerWeek);
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-form/es/components/DatePicker/YearPicker.js


var YearPicker_excluded = ["proFieldProps", "fieldProps"];




var YearPicker_valueType = 'dateYear';
/**
 * \u5468\u9009\u62E9\u7EC4\u4EF6
 *
 * @param
 */
var ProFormDatePickerYear = /*#__PURE__*/react.forwardRef(function (_ref, ref) {
  var proFieldProps = _ref.proFieldProps,
    fieldProps = _ref.fieldProps,
    rest = (0,objectWithoutProperties/* default */.Z)(_ref, YearPicker_excluded);
  var context = (0,react.useContext)(FieldContext/* default */.Z);
  return /*#__PURE__*/(0,jsx_runtime.jsx)(Field/* default */.Z, (0,objectSpread2/* default */.Z)({
    ref: ref,
    valueType: YearPicker_valueType,
    fieldProps: (0,objectSpread2/* default */.Z)({
      getPopupContainer: context.getPopupContainer
    }, fieldProps),
    proFieldProps: proFieldProps,
    filedConfig: {
      valueType: YearPicker_valueType,
      customLightMode: true
    }
  }, rest));
});
/* harmony default export */ var YearPicker = (ProFormDatePickerYear);
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-form/es/components/DatePicker/index.js





var ExportComponent = DatePicker;
ExportComponent.Week = WeekPicker;
ExportComponent.Month = MonthPicker;
ExportComponent.Quarter = QuarterPicker;
ExportComponent.Year = YearPicker;
// @ts-ignore
// eslint-disable-next-line no-param-reassign
ExportComponent.displayName = 'ProFormComponent';
/* harmony default export */ var components_DatePicker = (ExportComponent);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///50335
`)},22452:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(1413);
/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(91);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(67294);
/* harmony import */ var _FieldContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(66758);
/* harmony import */ var _Field__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(265);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(85893);


var _excluded = ["fieldProps", "proFieldProps"];




var valueType = 'dateTime';

/**
 * \u65F6\u95F4\u65E5\u671F\u9009\u62E9\u7EC4\u4EF6
 *
 * @param
 */
var ProFormDateTimePicker = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function (_ref, ref) {
  var fieldProps = _ref.fieldProps,
    proFieldProps = _ref.proFieldProps,
    rest = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z)(_ref, _excluded);
  var context = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_FieldContext__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z);
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_Field__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z, (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z)({
    ref: ref,
    fieldProps: (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z)({
      getPopupContainer: context.getPopupContainer
    }, fieldProps),
    valueType: valueType,
    proFieldProps: proFieldProps,
    filedConfig: {
      valueType: valueType,
      customLightMode: true
    }
  }, rest));
});
/* harmony default export */ __webpack_exports__.Z = (ProFormDateTimePicker);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///22452
`)},31199:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(1413);
/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(91);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(67294);
/* harmony import */ var _Field__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(265);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(85893);


var _excluded = ["fieldProps", "min", "proFieldProps", "max"];



/**
 * \u6570\u7EC4\u9009\u62E9\u7EC4\u4EF6
 *
 * @param
 */
var ProFormDigit = function ProFormDigit(_ref, ref) {
  var fieldProps = _ref.fieldProps,
    min = _ref.min,
    proFieldProps = _ref.proFieldProps,
    max = _ref.max,
    rest = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z)(_ref, _excluded);
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_Field__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z, (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z)({
    valueType: "digit",
    fieldProps: (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z)({
      min: min,
      max: max
    }, fieldProps),
    ref: ref,
    filedConfig: {
      defaultProps: {
        width: '100%'
      }
    },
    proFieldProps: proFieldProps
  }, rest));
};
var ForwardRefProFormDigit = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(ProFormDigit);
/* harmony default export */ __webpack_exports__.Z = (ForwardRefProFormDigit);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///31199
`)},19054:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(1413);
/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(91);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(67294);
/* harmony import */ var _Field__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(265);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(85893);


var _excluded = ["fieldProps", "request", "params", "proFieldProps"];



/**
 * \u7EA7\u8054\u9009\u62E9\u6846
 *
 * @param
 */
var ProFormTreeSelect = function ProFormTreeSelect(_ref, ref) {
  var fieldProps = _ref.fieldProps,
    request = _ref.request,
    params = _ref.params,
    proFieldProps = _ref.proFieldProps,
    rest = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z)(_ref, _excluded);
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_Field__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z, (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z)({
    valueType: "treeSelect",
    fieldProps: fieldProps,
    ref: ref,
    request: request,
    params: params,
    filedConfig: {
      customLightMode: true
    },
    proFieldProps: proFieldProps
  }, rest));
};
var WarpProFormTreeSelect = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(ProFormTreeSelect);
/* harmony default export */ __webpack_exports__.Z = (WarpProFormTreeSelect);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiMTkwNTQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBcUU7QUFDcUI7QUFDMUY7QUFDMEI7QUFDVTtBQUNZO0FBQ2hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyx1R0FBd0I7QUFDbkMsc0JBQXNCLHNEQUFJLENBQUMsdURBQVksRUFBRSw2RkFBYTtBQUN0RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBLEdBQUc7QUFDSDtBQUNBLHlDQUF5Qyw2Q0FBZ0I7QUFDekQsc0RBQWUscUJBQXFCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvQGFudC1kZXNpZ24vcHJvLWZvcm0vZXMvY29tcG9uZW50cy9UcmVlU2VsZWN0L2luZGV4LmpzPzdhZGUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL29iamVjdFNwcmVhZDJcIjtcbmltcG9ydCBfb2JqZWN0V2l0aG91dFByb3BlcnRpZXMgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL29iamVjdFdpdGhvdXRQcm9wZXJ0aWVzXCI7XG52YXIgX2V4Y2x1ZGVkID0gW1wiZmllbGRQcm9wc1wiLCBcInJlcXVlc3RcIiwgXCJwYXJhbXNcIiwgXCJwcm9GaWVsZFByb3BzXCJdO1xuaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCBQcm9Gb3JtRmllbGQgZnJvbSBcIi4uL0ZpZWxkXCI7XG5pbXBvcnQgeyBqc3ggYXMgX2pzeCB9IGZyb20gXCJyZWFjdC9qc3gtcnVudGltZVwiO1xuLyoqXG4gKiDnuqfogZTpgInmi6nmoYZcbiAqXG4gKiBAcGFyYW1cbiAqL1xudmFyIFByb0Zvcm1UcmVlU2VsZWN0ID0gZnVuY3Rpb24gUHJvRm9ybVRyZWVTZWxlY3QoX3JlZiwgcmVmKSB7XG4gIHZhciBmaWVsZFByb3BzID0gX3JlZi5maWVsZFByb3BzLFxuICAgIHJlcXVlc3QgPSBfcmVmLnJlcXVlc3QsXG4gICAgcGFyYW1zID0gX3JlZi5wYXJhbXMsXG4gICAgcHJvRmllbGRQcm9wcyA9IF9yZWYucHJvRmllbGRQcm9wcyxcbiAgICByZXN0ID0gX29iamVjdFdpdGhvdXRQcm9wZXJ0aWVzKF9yZWYsIF9leGNsdWRlZCk7XG4gIHJldHVybiAvKiNfX1BVUkVfXyovX2pzeChQcm9Gb3JtRmllbGQsIF9vYmplY3RTcHJlYWQoe1xuICAgIHZhbHVlVHlwZTogXCJ0cmVlU2VsZWN0XCIsXG4gICAgZmllbGRQcm9wczogZmllbGRQcm9wcyxcbiAgICByZWY6IHJlZixcbiAgICByZXF1ZXN0OiByZXF1ZXN0LFxuICAgIHBhcmFtczogcGFyYW1zLFxuICAgIGZpbGVkQ29uZmlnOiB7XG4gICAgICBjdXN0b21MaWdodE1vZGU6IHRydWVcbiAgICB9LFxuICAgIHByb0ZpZWxkUHJvcHM6IHByb0ZpZWxkUHJvcHNcbiAgfSwgcmVzdCkpO1xufTtcbnZhciBXYXJwUHJvRm9ybVRyZWVTZWxlY3QgPSAvKiNfX1BVUkVfXyovUmVhY3QuZm9yd2FyZFJlZihQcm9Gb3JtVHJlZVNlbGVjdCk7XG5leHBvcnQgZGVmYXVsdCBXYXJwUHJvRm9ybVRyZWVTZWxlY3Q7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9
//# sourceURL=webpack-internal:///19054
`)},30653:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(97857);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(5574);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectWithoutProperties_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(13769);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectWithoutProperties_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectWithoutProperties_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _utils_string__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(7369);
/* harmony import */ var _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(33983);
/* harmony import */ var _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(19054);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(96074);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(67294);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(85893);



var _excluded = ["dropdownBottom"];






var toLowerCase = function toLowerCase() {
  var input = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';
  return (0,_utils_string__WEBPACK_IMPORTED_MODULE_5__/* .nonAccentVietnamese */ .w)(input.toLowerCase().normalize('NFD').replace(/[\\u0300-\\u036f]/g, ''));
};
var FormTreeSelectSearch = function FormTreeSelectSearch(_ref) {
  var _props$fieldProps;
  var dropdownBottom = _ref.dropdownBottom,
    props = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectWithoutProperties_js__WEBPACK_IMPORTED_MODULE_2___default()(_ref, _excluded);
  // eslint-disable-next-line @typescript-eslint/no-non-null-asserted-optional-chain
  var treeData = (_props$fieldProps = props.fieldProps) === null || _props$fieldProps === void 0 ? void 0 : _props$fieldProps.treeData;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(''),
    _useState2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_1___default()(_useState, 2),
    searchValue = _useState2[0],
    setSearchValue = _useState2[1];
  var searchValueDebounce = (0,_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_6__/* .useDebounceValue */ .n)(searchValue || '', 100);
  var _treeData = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(function () {
    var loop = function loop() {
      var data = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];
      return (data || []).map(function (item) {
        var normalizedSearchValue = toLowerCase(searchValueDebounce);
        var itemTitle = (item.title || '').toString();
        var strTitle = (0,_utils_string__WEBPACK_IMPORTED_MODULE_5__/* .nonAccentVietnamese */ .w)(itemTitle);
        var index = strTitle.indexOf(normalizedSearchValue);
        var beforeStr = itemTitle.substring(0, index);
        var str = itemTitle.substring(index, index + searchValueDebounce.length);
        var afterStr = itemTitle.slice(index + searchValueDebounce.length);
        var title = searchValueDebounce === '' ? /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)("span", {
          children: itemTitle
        }) : index > -1 ? /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)("span", {
          children: [beforeStr, /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)("span", {
            style: {
              color: 'white',
              backgroundColor: 'green'
            },
            children: str
          }), afterStr]
        }) : /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)("span", {
          children: itemTitle
        });
        if (item.children) {
          return {
            title: title,
            key: item.value,
            children: loop(item.children),
            value: item.value,
            _title: itemTitle
          };
        }
        return {
          title: title,
          key: item.value,
          value: item.value,
          _title: itemTitle
        };
      });
    };
    return loop(treeData);
  }, [treeData, searchValueDebounce]);
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Z, D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default()({}, props), {}, {
    fieldProps: D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default()({}, props.fieldProps || {}), {}, {
      treeData: _treeData,
      onSearch: function onSearch(value) {
        setSearchValue(value);
      },
      filterTreeNode: function filterTreeNode(input, treeNode) {
        var treeNodeChildrenArr = treeNode.children || [];
        var normalizedInput = toLowerCase(input);
        var normalizedLabel = toLowerCase(treeNode._title);
        var childrenMatch = false;
        for (var i = 0; i < treeNodeChildrenArr.length; i++) {
          var _normalizedLabel = toLowerCase(treeNodeChildrenArr[i]._title);
          if (_normalizedLabel.includes(normalizedInput)) {
            childrenMatch = true;
            return true;
          }
        }
        if (normalizedLabel.includes(normalizedInput)) {
          return true;
        }
        return childrenMatch;
      },
      dropdownRender: !dropdownBottom ? undefined : function (menu) {
        return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)("div", {
          children: [menu, /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(antd__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .Z, {
            style: {
              margin: '4px 0'
            }
          }), dropdownBottom]
        });
      },
      showSearch: true,
      multiple: true,
      autoClearSearchValue: true,
      treeCheckable: true,
      treeDefaultExpandAll: true,
      showCheckedStrategy: 'SHOW_CHILD'
    })
  }));
};
/* harmony default export */ __webpack_exports__.Z = (FormTreeSelectSearch);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///30653
`)},15345:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(66309);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(85893);


var RealityColumnRender = function RealityColumnRender(_ref) {
  var children = _ref.children;
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Z, {
    color: "success",
    children: children
  });
};
/* harmony default export */ __webpack_exports__.Z = (RealityColumnRender);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiMTUzNDUuanMiLCJtYXBwaW5ncyI6Ijs7QUFBMkI7QUFBQTtBQU8zQixJQUFNRyxtQkFBaUQsR0FBRyxTQUFwREEsbUJBQWlEQSxDQUFBQyxJQUFBLEVBQXFCO0VBQUEsSUFBZkMsUUFBUSxHQUFBRCxJQUFBLENBQVJDLFFBQVE7RUFDbkUsb0JBQU9ILHNEQUFBLENBQUNGLHFEQUFHO0lBQUNNLEtBQUssRUFBQyxTQUFTO0lBQUFELFFBQUEsRUFBRUE7RUFBUSxDQUFNLENBQUM7QUFDOUMsQ0FBQztBQUVELHNEQUFlRixtQkFBbUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hbnQtZGVzaWduLXByby8uL3NyYy9jb21wb25lbnRzL1JlYWxpdHlDb2x1bW5SZW5kZXIudHN4P2I0NmYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgVGFnIH0gZnJvbSAnYW50ZCc7XHJcbmltcG9ydCB7IEZDLCBSZWFjdE5vZGUgfSBmcm9tICdyZWFjdCc7XHJcblxyXG5pbnRlcmZhY2UgUmVhbGl0eUNvbHVtblJlbmRlclByb3BzIHtcclxuICBjaGlsZHJlbj86IFJlYWN0Tm9kZTtcclxufVxyXG5cclxuY29uc3QgUmVhbGl0eUNvbHVtblJlbmRlcjogRkM8UmVhbGl0eUNvbHVtblJlbmRlclByb3BzPiA9ICh7IGNoaWxkcmVuIH0pID0+IHtcclxuICByZXR1cm4gPFRhZyBjb2xvcj1cInN1Y2Nlc3NcIj57Y2hpbGRyZW59PC9UYWc+O1xyXG59O1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgUmVhbGl0eUNvbHVtblJlbmRlcjtcclxuIl0sIm5hbWVzIjpbIlRhZyIsImpzeCIsIl9qc3giLCJSZWFsaXR5Q29sdW1uUmVuZGVyIiwiX3JlZiIsImNoaWxkcmVuIiwiY29sb3IiXSwic291cmNlUm9vdCI6IiJ9
//# sourceURL=webpack-internal:///15345
`)},62557:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return /* binding */ Detail_GeneralInfo; }
});

// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/toConsumableArray.js
var toConsumableArray = __webpack_require__(19632);
var toConsumableArray_default = /*#__PURE__*/__webpack_require__.n(toConsumableArray);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/regeneratorRuntime.js
var regeneratorRuntime = __webpack_require__(15009);
var regeneratorRuntime_default = /*#__PURE__*/__webpack_require__.n(regeneratorRuntime);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(99289);
var asyncToGenerator_default = /*#__PURE__*/__webpack_require__.n(asyncToGenerator);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/slicedToArray.js
var slicedToArray = __webpack_require__(5574);
var slicedToArray_default = /*#__PURE__*/__webpack_require__.n(slicedToArray);
// EXTERNAL MODULE: ./src/common/contanst/constanst.ts
var constanst = __webpack_require__(86604);
// EXTERNAL MODULE: ./src/services/cropManager.ts
var cropManager = __webpack_require__(77890);
// EXTERNAL MODULE: ./src/services/fileUpload.ts
var services_fileUpload = __webpack_require__(96876);
// EXTERNAL MODULE: ./src/services/utils.ts
var utils = __webpack_require__(467);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/layouts/ProForm/index.js + 3 modules
var ProForm = __webpack_require__(34994);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-layout/es/components/FooterToolbar/index.js + 2 modules
var FooterToolbar = __webpack_require__(2236);
// EXTERNAL MODULE: ./src/.umi-production/exports.ts + 34 modules
var _umi_production_exports = __webpack_require__(7837);
// EXTERNAL MODULE: ./node_modules/antd/es/app/index.js + 1 modules
var app = __webpack_require__(31418);
// EXTERNAL MODULE: ./node_modules/antd/es/spin/index.js + 2 modules
var spin = __webpack_require__(75081);
// EXTERNAL MODULE: ./node_modules/antd/es/space/index.js + 2 modules
var space = __webpack_require__(42075);
// EXTERNAL MODULE: ./node_modules/antd/es/card/index.js + 3 modules
var card = __webpack_require__(76216);
// EXTERNAL MODULE: ./node_modules/antd/es/divider/index.js + 1 modules
var divider = __webpack_require__(96074);
// EXTERNAL MODULE: ./node_modules/antd/es/button/index.js + 9 modules
var es_button = __webpack_require__(14726);
// EXTERNAL MODULE: ./node_modules/nanoid/index.browser.js
var index_browser = __webpack_require__(53416);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/objectSpread2.js
var objectSpread2 = __webpack_require__(97857);
var objectSpread2_default = /*#__PURE__*/__webpack_require__.n(objectSpread2);
// EXTERNAL MODULE: ./src/pages/WarehouseManagementV3/Inventory/InventoryListTable/components/ViewDetail.tsx + 3 modules
var ViewDetail = __webpack_require__(43370);
// EXTERNAL MODULE: ./src/services/stock/stockEntry.ts
var stockEntry = __webpack_require__(1631);
// EXTERNAL MODULE: ./src/services/stock/warehouse.ts
var warehouse = __webpack_require__(18327);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-table/es/Table.js + 76 modules
var Table = __webpack_require__(4894);
// EXTERNAL MODULE: ./node_modules/antd/es/tag/index.js + 5 modules
var tag = __webpack_require__(66309);
;// CONCATENATED MODULE: ./src/pages/FarmingManagement/SeasonalManagement/Detail/GeneralInfo/components/CropTransactionTable.less
// extracted by mini-css-extract-plugin

// EXTERNAL MODULE: ./node_modules/react/jsx-runtime.js
var jsx_runtime = __webpack_require__(85893);
;// CONCATENATED MODULE: ./src/pages/FarmingManagement/SeasonalManagement/Detail/GeneralInfo/components/CropTransactionTables.tsx













var CropItemTransactionTable = function CropItemTransactionTable(_ref) {
  var cropId = _ref.cropId;
  var columns = [{
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.voucher_code"
    }),
    dataIndex: 'item_code',
    width: 130,
    render: function render(__dom, entity, index, action, schema) {
      var voucherNo = entity.se_id;
      var voucherType = entity.purpose;
      return /*#__PURE__*/(0,jsx_runtime.jsx)(ViewDetail/* default */.Z, {
        voucherNo: voucherNo,
        voucherType: voucherType
      }, voucherNo);
    }
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.date"
    }),
    dataIndex: 'posting_date_filter',
    hideInTable: true,
    valueType: 'dateRange',
    fieldProps: {
      format: constanst/* DEFAULT_DATE_FORMAT_WITHOUT_TIME */.ug
    },
    width: 66
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.date"
    }),
    dataIndex: 'posting_date',
    valueType: 'date',
    hideInSearch: true,
    width: 66
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.time"
    }),
    dataIndex: 'posting_time',
    valueType: 'time',
    hideInSearch: true,
    width: 55
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.purpose"
    }),
    dataIndex: 'purpose',
    valueType: 'text',
    fieldProps: {
      showSearch: true
    },
    request: function () {
      var _request = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
        return regeneratorRuntime_default()().wrap(function _callee$(_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              return _context.abrupt("return", [{
                label: 'Material Receipt',
                value: 'Material Receipt'
              }, {
                label: 'Material Issue',
                value: 'Material Issue'
              }, {
                label: 'Material Transfer',
                value: 'Material Transfer'
              }]);
            case 1:
            case "end":
              return _context.stop();
          }
        }, _callee);
      }));
      function request() {
        return _request.apply(this, arguments);
      }
      return request;
    }(),
    render: function render(dom, entity, index, action, schema) {
      if (entity.purpose === 'Material Issue') {
        return /*#__PURE__*/(0,jsx_runtime.jsx)(tag/* default */.Z, {
          style: {
            maxWidth: '100%'
          },
          color: "volcano",
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
            id: "common.material_issue"
          })
        });
      } else if (entity.purpose === 'Material Receipt') {
        return /*#__PURE__*/(0,jsx_runtime.jsx)(tag/* default */.Z, {
          style: {
            maxWidth: '100%'
          },
          color: "purple",
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
            id: "common.material_receipt"
          })
        });
      } else if (entity.purpose === 'Material Transfer') {
        return /*#__PURE__*/(0,jsx_runtime.jsx)(tag/* default */.Z, {
          style: {
            maxWidth: '100%'
          },
          color: "cyan",
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
            id: "common.material-transfer"
          })
        });
      }
    },
    width: 100
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.task_label"
    }),
    dataIndex: 'task_label',
    valueType: 'text',
    width: 100
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.s_wh_label"
    }),
    dataIndex: 's_warehouse',
    valueType: 'select',
    request: function () {
      var _request2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2() {
        var res;
        return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
          while (1) switch (_context2.prev = _context2.next) {
            case 0:
              _context2.next = 2;
              return (0,warehouse/* getWarehouseList */.Aq)({
                or_filters: JSON.stringify([['Warehouse', 'name', '=', 'Work In Progress - V']])
              });
            case 2:
              res = _context2.sent;
              return _context2.abrupt("return", res.data.map(function (item) {
                return {
                  label: item.label,
                  value: item.name
                };
              }));
            case 4:
            case "end":
              return _context2.stop();
          }
        }, _callee2);
      }));
      function request() {
        return _request2.apply(this, arguments);
      }
      return request;
    }(),
    fieldProps: {
      showSearch: true
    },
    width: 100
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.t_wh_label"
    }),
    dataIndex: 't_warehouse',
    valueType: 'select',
    request: function () {
      var _request3 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee3() {
        var res;
        return regeneratorRuntime_default()().wrap(function _callee3$(_context3) {
          while (1) switch (_context3.prev = _context3.next) {
            case 0:
              _context3.next = 2;
              return (0,warehouse/* getWarehouseList */.Aq)({
                or_filters: JSON.stringify([['Warehouse', 'name', '=', 'Work In Progress - V']])
              });
            case 2:
              res = _context3.sent;
              return _context3.abrupt("return", res.data.map(function (item) {
                return {
                  label: item.label,
                  value: item.name
                };
              }));
            case 4:
            case "end":
              return _context3.stop();
          }
        }, _callee3);
      }));
      function request() {
        return _request3.apply(this, arguments);
      }
      return request;
    }(),
    fieldProps: {
      showSearch: true
    },
    width: 100
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.item_code"
    }),
    dataIndex: 'item_code',
    valueType: 'text',
    width: 100
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.item_label"
    }),
    dataIndex: 'item_label',
    hideInSearch: true,
    valueType: 'text',
    width: 100
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.qty"
    }),
    dataIndex: 'qty',
    valueType: 'digit',
    hideInSearch: true,
    width: 60
  },
  // {
  //   title: <FormattedMessage id="common.actual_qty" />,
  //   dataIndex: 'actual_qty',
  //   valueType: 'digit',
  //   hideInSearch: true,
  //   width: 100,
  // },
  // {
  //   title: <FormattedMessage id="common.balance" />,
  //   dataIndex: 'balance',
  //   valueType: 'digit',
  //   hideInSearch: true,
  //   width: 100,
  // },
  {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.basic_rate"
    }),
    dataIndex: 'basic_rate',
    valueType: 'digit',
    hideInSearch: true,
    width: 80
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.basic_amount"
    }),
    dataIndex: 'basic_amount',
    valueType: 'digit',
    hideInSearch: true,
    width: 100
  }];
  return /*#__PURE__*/(0,jsx_runtime.jsx)(Table/* default */.Z, {
    className: "table_crop_transaction",
    scroll: {
      x: 1700,
      y: 700
    },
    style: {
      marginTop: '-16px'
    }
    // pagination={{
    //   defaultCurrent: 10,
    //   pageSizeOptions: [10, 20, 50, 100],
    //   showSizeChanger: true,
    // }}
    ,
    columns: columns,
    size: "small",
    search: {
      labelWidth: 'auto',
      layout: 'vertical'
    },
    rowKey: function rowKey(record) {
      return "".concat(record.se_id, "-").concat(record.item_code);
    },
    request: ( /*#__PURE__*/function () {
      var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee4(params, sort, filter) {
        var start_date, end_date, _params$posting_date_, res;
        return regeneratorRuntime_default()().wrap(function _callee4$(_context4) {
          while (1) switch (_context4.prev = _context4.next) {
            case 0:
              if (params.posting_date_filter) {
                _params$posting_date_ = slicedToArray_default()(params.posting_date_filter, 2);
                start_date = _params$posting_date_[0];
                end_date = _params$posting_date_[1];
              }
              params.start_date = start_date;
              params.end_date = end_date;
              _context4.next = 5;
              return (0,stockEntry/* getStockEntryCropReport */.wK)(objectSpread2_default()({
                iot_crop: cropId
              }, params));
            case 5:
              res = _context4.sent;
              return _context4.abrupt("return", {
                data: res.data,
                total: res.pagination.totalElements
              });
            case 7:
            case "end":
              return _context4.stop();
          }
        }, _callee4);
      }));
      return function (_x, _x2, _x3) {
        return _ref2.apply(this, arguments);
      };
    }())
  });
};
/* harmony default export */ var CropTransactionTables = (CropItemTransactionTable);
// EXTERNAL MODULE: ./src/access.ts
var access = __webpack_require__(44862);
// EXTERNAL MODULE: ./src/components/RealityColumnRender.tsx
var RealityColumnRender = __webpack_require__(15345);
// EXTERNAL MODULE: ./src/services/crop.ts
var crop = __webpack_require__(52662);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/InfoCircleOutlined.js
var InfoCircleOutlined = __webpack_require__(56717);
// EXTERNAL MODULE: ./node_modules/antd/es/tooltip/index.js + 3 modules
var tooltip = __webpack_require__(83062);
// EXTERNAL MODULE: ./node_modules/numeral/numeral.js
var numeral = __webpack_require__(92077);
var numeral_default = /*#__PURE__*/__webpack_require__.n(numeral);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/EyeOutlined.js
var EyeOutlined = __webpack_require__(55287);
// EXTERNAL MODULE: ./node_modules/antd/es/modal/index.js + 1 modules
var modal = __webpack_require__(85576);
;// CONCATENATED MODULE: ./src/pages/FarmingManagement/SeasonalManagement/Detail/GeneralInfo/components/TaskItemsModal.tsx














var TaskItemsModal = function TaskItemsModal(_ref) {
  var category_id = _ref.category_id,
    category_label = _ref.category_label,
    crop_id = _ref.crop_id;
  var _useState = (0,react.useState)(false),
    _useState2 = slicedToArray_default()(_useState, 2),
    isOpen = _useState2[0],
    setOpen = _useState2[1];
  var showModal = function showModal() {
    setOpen(true);
  };
  var _App$useApp = app/* default */.Z.useApp(),
    message = _App$useApp.message;
  var hideModal = function hideModal() {
    setOpen(false);
  };
  var handleCancel = function handleCancel() {
    hideModal();
  };
  var intl = (0,_umi_production_exports.useIntl)();
  var columns = [{
    title: intl.formatMessage({
      id: 'common.task'
    }),
    dataIndex: 'task_label',
    render: function render(dom, entity, index, action, schema) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)("a", {
        href: "/farming-management/workflow-management/detail/".concat(entity.task_id),
        target: "_blank",
        rel: "noopener noreferrer",
        children: entity.task_label
      });
    }
  }, {
    title: intl.formatMessage({
      id: 'category.material-management.unit'
    }),
    dataIndex: 'unit_label'
  }, {
    title: intl.formatMessage({
      id: 'seasonalTab.expected'
    }),
    dataIndex: 'total_exp_quantity',
    render: function render(dom, entity, index, action, schema) {
      return numeral_default()(entity.total_exp_quantity).format('0,0.00');
    }
  }, {
    title: intl.formatMessage({
      id: 'storage-management.category-management.draft_quantity'
    }),
    dataIndex: 'total_draft_quantity',
    render: function render(dom, entity, index, action, schema) {
      return numeral_default()(entity.total_draft_quantity).format('0,0.00');
    }
  }, {
    title: intl.formatMessage({
      id: 'seasonalTab.used'
    }),
    dataIndex: 'total_quantity',
    render: function render(dom, entity, index, action, schema) {
      return numeral_default()(entity.total_quantity).format('0,0.00');
    },
    hideInTable: !(0,access/* isSubscribedStock */.ak)()
  },
  // {
  //   title: intl.formatMessage({ id: 'seasonalTab.reality' }),
  //   dataIndex: 'total_real_quantity',
  //   render(dom, entity, index, action, schema) {
  //     return numeral(entity.total_real_quantity).format('0,0.00');
  //   },
  // },
  {
    title: intl.formatMessage({
      id: 'seasonalTab.issue'
    }),
    dataIndex: 'total_issued_quantity',
    render: function render(dom, entity, index, action, schema) {
      return numeral_default()(entity.total_issued_quantity).format('0,0.00');
    },
    hideInTable: !(0,access/* isSubscribedStock */.ak)()
  }];
  return /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
    children: [/*#__PURE__*/(0,jsx_runtime.jsxs)(space/* default */.Z, {
      size: 'small',
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
        icon: /*#__PURE__*/(0,jsx_runtime.jsx)(EyeOutlined/* default */.Z, {}),
        onClick: showModal
      }), /*#__PURE__*/(0,jsx_runtime.jsx)("span", {
        children: category_label
      })]
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(modal/* default */.Z, {
      title: "Chi ti\\u1EBFt s\\u1EED d\\u1EE5ng v\\u1EADt t\\u01B0 ".concat(category_label, " trong v\\u1EE5 m\\xF9a"),
      open: isOpen,
      onCancel: handleCancel,
      footer: null,
      width: 1200,
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(Table/* default */.Z, {
        columns: columns,
        search: false,
        pagination: {
          pageSizeOptions: [10, 20, 50, 100],
          showSizeChanger: true,
          defaultPageSize: 10
        },
        request: ( /*#__PURE__*/function () {
          var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(params, sorter, filter) {
            var res;
            return regeneratorRuntime_default()().wrap(function _callee$(_context) {
              while (1) switch (_context.prev = _context.next) {
                case 0:
                  _context.prev = 0;
                  _context.next = 3;
                  return (0,crop/* getCropItemStatisticDetailTask */.vx)({
                    page: params.current,
                    size: params.pageSize,
                    crop_id: crop_id,
                    category_id: category_id
                  });
                case 3:
                  res = _context.sent;
                  return _context.abrupt("return", {
                    data: res.data,
                    success: true
                  });
                case 7:
                  _context.prev = 7;
                  _context.t0 = _context["catch"](0);
                  message.error("L\\u1ED7i khi k\\xE9o d\\u1EEF li\\u1EC7u: ".concat(_context.t0.message));
                  return _context.abrupt("return", {
                    success: false
                  });
                case 11:
                case "end":
                  return _context.stop();
              }
            }, _callee, null, [[0, 7]]);
          }));
          return function (_x, _x2, _x3) {
            return _ref2.apply(this, arguments);
          };
        }()),
        rowKey: 'task_id'
      })
    })]
  });
};
/* harmony default export */ var components_TaskItemsModal = (TaskItemsModal);
;// CONCATENATED MODULE: ./src/pages/FarmingManagement/SeasonalManagement/Detail/GeneralInfo/components/ItemsStatistic.tsx

















var ItemsStatistic = function ItemsStatistic(_ref) {
  var cropId = _ref.cropId;
  var _App$useApp = app/* default */.Z.useApp(),
    message = _App$useApp.message;
  var _useState = (0,react.useState)([]),
    _useState2 = slicedToArray_default()(_useState, 2),
    data = _useState2[0],
    setData = _useState2[1];
  var intl = (0,_umi_production_exports.useIntl)();
  var columns = [{
    title: intl.formatMessage({
      id: 'common.category_group'
    }),
    dataIndex: 'category_group_label',
    width: 80,
    render: function render(text, record, index) {
      if (index > 0 && data[index - 1].category_group_label === text) {
        return {
          children: null,
          props: {
            rowSpan: 0
          }
        };
      }
      var rowSpan = 1;
      while (index + rowSpan < data.length && data[index + rowSpan].category_group_label === text) {
        rowSpan++;
      }
      return {
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(jsx_runtime.Fragment, {
          children: text
        }),
        props: {
          rowSpan: rowSpan
        }
      };
    }
  }, {
    title: intl.formatMessage({
      id: 'category.material-management.category_list'
    }),
    dataIndex: 'category_label',
    width: 80,
    render: function render(dom, entity, index, action, schema) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)(components_TaskItemsModal, {
        category_id: entity.category_name,
        category_label: entity.category_label,
        crop_id: cropId
      });
    }
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "category.material-management.unit",
      defaultMessage: "unknown"
    }),
    dataIndex: 'unit_label',
    width: 60
  }, {
    // title: intl.formatMessage({ id: 'seasonalTab.totalExpected' }),
    title: /*#__PURE__*/(0,jsx_runtime.jsxs)(tooltip/* default */.Z, {
      color: constanst/* COLOR_HEX */.wE.GREEN_TOOLTIP,
      title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
        id: "tooltips.total_exp_quantity"
      }),
      children: [intl.formatMessage({
        id: 'seasonalTab.totalExpected'
      }), " ", /*#__PURE__*/(0,jsx_runtime.jsx)(InfoCircleOutlined/* default */.Z, {})]
    }, constanst/* COLOR_HEX */.wE.GREEN_TOOLTIP),
    dataIndex: 'total_exp_quantity',
    width: 100,
    render: function render(dom, entity, index, action, schema) {
      return numeral_default()(entity.total_exp_quantity).format('0,0.00');
    }
  }, {
    // title: intl.formatMessage({ id: 'seasonalTab.totalExpected' }),
    title: /*#__PURE__*/(0,jsx_runtime.jsxs)(tooltip/* default */.Z, {
      color: constanst/* COLOR_HEX */.wE.GREEN_TOOLTIP,
      title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
        id: "tooltips.total_draft_quantity"
      }),
      children: [intl.formatMessage({
        id: 'seasonalTab.totalDraft'
      }), " ", /*#__PURE__*/(0,jsx_runtime.jsx)(InfoCircleOutlined/* default */.Z, {})]
    }, constanst/* COLOR_HEX */.wE.GREEN_TOOLTIP),
    dataIndex: 'total_draft_quantity',
    width: 100,
    render: function render(dom, entity, index, action, schema) {
      return numeral_default()(entity.total_draft_quantity).format('0,0.00');
    },
    hideInTable: (0,access/* isSubscribedStock */.ak)()
  }, {
    // title: intl.formatMessage({ id: 'seasonalTab.totalUsed' }),
    title: /*#__PURE__*/(0,jsx_runtime.jsxs)(tooltip/* default */.Z, {
      color: constanst/* COLOR_HEX */.wE.GREEN_TOOLTIP,
      title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
        id: "tooltips.total_quantity"
      }),
      children: [intl.formatMessage({
        id: 'seasonalTab.totalUsed'
      }), " ", /*#__PURE__*/(0,jsx_runtime.jsx)(InfoCircleOutlined/* default */.Z, {})]
    }, constanst/* COLOR_HEX */.wE.GREEN_TOOLTIP),
    dataIndex: 'total_quantity',
    width: 100,
    render: function render(dom, entity, index, action, schema) {
      return numeral_default()(entity.total_quantity).format('0,0.00');
    },
    hideInTable: !(0,access/* isSubscribedStock */.ak)()
  }, {
    // title: intl.formatMessage({ id: 'seasonalTab.totalIssue' }),
    title: /*#__PURE__*/(0,jsx_runtime.jsxs)(tooltip/* default */.Z, {
      color: constanst/* COLOR_HEX */.wE.GREEN_TOOLTIP,
      title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
        id: "tooltips.total_issued_quantity"
      }),
      children: [intl.formatMessage({
        id: 'seasonalTab.totalIssue'
      }), " ", /*#__PURE__*/(0,jsx_runtime.jsx)(InfoCircleOutlined/* default */.Z, {})]
    }, constanst/* COLOR_HEX */.wE.GREEN_TOOLTIP),
    dataIndex: 'total_issued_quantity',
    width: 100,
    render: function render(dom, entity, index, action, schema) {
      return numeral_default()(dom).format('0,0.00');
    },
    hideInTable: !(0,access/* isSubscribedStock */.ak)()
  }, {
    // title: intl.formatMessage({ id: 'seasonalTab.totalReality' }),
    title: /*#__PURE__*/(0,jsx_runtime.jsxs)(tooltip/* default */.Z, {
      color: constanst/* COLOR_HEX */.wE.GREEN_TOOLTIP,
      title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
        id: "tooltips.total_real_quantity"
      }),
      children: [intl.formatMessage({
        id: 'seasonalTab.totalReality'
      }), " ", /*#__PURE__*/(0,jsx_runtime.jsx)(InfoCircleOutlined/* default */.Z, {})]
    }, constanst/* COLOR_HEX */.wE.GREEN_TOOLTIP),
    dataIndex: 'total_real_quantity',
    width: 100,
    render: function render(dom, entity, index, action, schema) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)(RealityColumnRender/* default */.Z, {
        children: numeral_default()(entity.total_real_quantity).format('0,0.00')
      });
    },
    hideInTable: !(0,access/* isSubscribedStock */.ak)()
  }
  //valuation_rate
  // {
  //   title: intl.formatMessage({ id: 'common.purchase-price' }),
  //   dataIndex: 'valuation_rate',
  //   width: 80,
  //   render(dom, entity, index, action, schema) {
  //     const valuation_rate = entity.valuation_rate ? entity.valuation_rate : 0;
  //     return numeral(entity.valuation_rate).format('0,000');
  //   },
  // },

  // {
  //   title: intl.formatMessage({ id: 'seasonalTab.remaining' }),
  //   dataIndex: 'remain_quantity',
  // },
  ];
  return /*#__PURE__*/(0,jsx_runtime.jsx)(Table/* default */.Z, {
    bordered: true,
    headerTitle: intl.formatMessage({
      id: 'seasonalTab.suppliesStatistics'
    }),
    columns: columns,
    search: false,
    pagination: {
      pageSizeOptions: [10, 20, 50, 100],
      showSizeChanger: true
    },
    request: ( /*#__PURE__*/function () {
      var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(params, sorter, filter) {
        var res;
        return regeneratorRuntime_default()().wrap(function _callee$(_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              _context.prev = 0;
              _context.next = 3;
              return (0,crop/* getCropItemStatistic */._R)({
                page: params.current,
                size: params.pageSize,
                crop_id: cropId
              });
            case 3:
              res = _context.sent;
              setData(res.data);
              return _context.abrupt("return", {
                data: res.data,
                success: true
              });
            case 8:
              _context.prev = 8;
              _context.t0 = _context["catch"](0);
              message.error("Error when getting Crop Items Statistic: ".concat(_context.t0.message));
              return _context.abrupt("return", {
                success: false
              });
            case 12:
            case "end":
              return _context.stop();
          }
        }, _callee, null, [[0, 8]]);
      }));
      return function (_x, _x2, _x3) {
        return _ref2.apply(this, arguments);
      };
    }()),
    rowKey: 'category_name'
  });
};
/* harmony default export */ var components_ItemsStatistic = (ItemsStatistic);
;// CONCATENATED MODULE: ./src/pages/FarmingManagement/SeasonalManagement/Detail/GeneralInfo/components/TaskProductionsModal.tsx














var TaskProductionsModal = function TaskProductionsModal(_ref) {
  var product_id = _ref.product_id,
    product_label = _ref.product_label,
    crop_id = _ref.crop_id;
  var _useState = (0,react.useState)(false),
    _useState2 = slicedToArray_default()(_useState, 2),
    isOpen = _useState2[0],
    setOpen = _useState2[1];
  var showModal = function showModal() {
    setOpen(true);
  };
  var _App$useApp = app/* default */.Z.useApp(),
    message = _App$useApp.message;
  var hideModal = function hideModal() {
    setOpen(false);
  };
  var handleCancel = function handleCancel() {
    hideModal();
  };
  var intl = (0,_umi_production_exports.useIntl)();
  var columns = [{
    title: intl.formatMessage({
      id: 'common.task'
    }),
    dataIndex: 'task_label',
    render: function render(dom, entity, index, action, schema) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)("a", {
        href: "/farming-management/workflow-management/detail/".concat(entity.task_id),
        target: "_blank",
        rel: "noopener noreferrer",
        children: entity.task_label
      });
    }
  }, {
    title: intl.formatMessage({
      id: 'category.material-management.unit'
    }),
    dataIndex: 'unit_label'
  }, {
    title: intl.formatMessage({
      id: 'seasonalTab.expected'
    }),
    dataIndex: 'total_exp_quantity',
    render: function render(dom, entity, index, action, schema) {
      return numeral_default()(entity.total_exp_quantity).format('0,0.00');
    }
  }, {
    title: intl.formatMessage({
      id: 'storage-management.category-management.draft_quantity'
    }),
    dataIndex: 'total_draft_quantity',
    render: function render(dom, entity, index, action, schema) {
      return numeral_default()(entity.total_draft_quantity).format('0,0.00');
    }
  },
  // {
  //   title: intl.formatMessage({ id: 'seasonalTab.used' }),
  //   dataIndex: 'total_quantity',
  //   render(dom, entity, index, action, schema) {
  //     return numeral(entity.total_quantity).format('0,0.00');
  //   },
  // },
  // {
  //   title: intl.formatMessage({ id: 'seasonalTab.reality' }),
  //   dataIndex: 'total_real_quantity',
  //   render(dom, entity, index, action, schema) {
  //     return numeral(entity.total_real_quantity).format('0,0.00');
  //   },
  // },
  {
    title: intl.formatMessage({
      id: 'seasonalTab.issue'
    }),
    dataIndex: 'total_issued_quantity',
    render: function render(dom, entity, index, action, schema) {
      return numeral_default()(entity.total_issued_quantity).format('0,0.00');
    },
    hideInTable: !(0,access/* isSubscribedStock */.ak)()
  }, {
    title: intl.formatMessage({
      id: 'common.harvested_quantity'
    }),
    dataIndex: 'total_finished_quantity',
    render: function render(dom, entity, index, action, schema) {
      return numeral_default()(entity.total_finished_quantity).format('0,0.00');
    },
    hideInTable: !(0,access/* isSubscribedStock */.ak)()
  }];
  return /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
    children: [/*#__PURE__*/(0,jsx_runtime.jsxs)(space/* default */.Z, {
      size: 'small',
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
        icon: /*#__PURE__*/(0,jsx_runtime.jsx)(EyeOutlined/* default */.Z, {}),
        onClick: showModal
      }), /*#__PURE__*/(0,jsx_runtime.jsx)("span", {
        children: product_label
      })]
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(modal/* default */.Z, {
      title: "Chi ti\\u1EBFt s\\u1EA3n l\\u01B0\\u1EE3ng ".concat(product_label, " trong v\\u1EE5 m\\xF9a"),
      open: isOpen,
      onCancel: handleCancel,
      footer: null,
      width: 1200,
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(Table/* default */.Z, {
        columns: columns,
        search: false,
        pagination: {
          pageSizeOptions: [10, 20, 50, 100],
          showSizeChanger: true,
          defaultPageSize: 10
        },
        request: ( /*#__PURE__*/function () {
          var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(params, sorter, filter) {
            var res;
            return regeneratorRuntime_default()().wrap(function _callee$(_context) {
              while (1) switch (_context.prev = _context.next) {
                case 0:
                  _context.prev = 0;
                  _context.next = 3;
                  return (0,crop/* getCropProductionStatisticDetailTask */.qQ)({
                    page: params.current,
                    size: params.pageSize,
                    crop_id: crop_id,
                    product_id: product_id
                  });
                case 3:
                  res = _context.sent;
                  return _context.abrupt("return", {
                    data: res.data,
                    success: true
                  });
                case 7:
                  _context.prev = 7;
                  _context.t0 = _context["catch"](0);
                  message.error("L\\u1ED7i khi k\\xE9o d\\u1EEF li\\u1EC7u: ".concat(_context.t0.message));
                  return _context.abrupt("return", {
                    success: false
                  });
                case 11:
                case "end":
                  return _context.stop();
              }
            }, _callee, null, [[0, 7]]);
          }));
          return function (_x, _x2, _x3) {
            return _ref2.apply(this, arguments);
          };
        }()),
        rowKey: 'task_id'
      })
    })]
  });
};
/* harmony default export */ var components_TaskProductionsModal = (TaskProductionsModal);
;// CONCATENATED MODULE: ./src/pages/FarmingManagement/SeasonalManagement/Detail/GeneralInfo/components/ProductionQuantityStatistic.tsx

















var ProductionQuantityStatistic = function ProductionQuantityStatistic(_ref) {
  var cropId = _ref.cropId;
  var _useState = (0,react.useState)([]),
    _useState2 = slicedToArray_default()(_useState, 2),
    data = _useState2[0],
    setData = _useState2[1];
  var _App$useApp = app/* default */.Z.useApp(),
    message = _App$useApp.message;
  var intl = (0,_umi_production_exports.useIntl)();
  var columns = [{
    title: intl.formatMessage({
      id: 'common.production_group'
    }),
    dataIndex: 'agri_product_group_label',
    width: 80,
    render: function render(text, record, index) {
      if (index > 0 && data[index - 1].agri_product_group_label === text) {
        return {
          children: null,
          props: {
            rowSpan: 0
          }
        };
      }
      var rowSpan = 1;
      while (index + rowSpan < data.length && data[index + rowSpan].agri_product_group_label === text) {
        rowSpan++;
      }
      return {
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(jsx_runtime.Fragment, {
          children: text
        }),
        props: {
          rowSpan: rowSpan
        }
      };
    }
  }, {
    title: intl.formatMessage({
      id: 'category.product.product_list'
    }),
    dataIndex: 'agri_product_label',
    width: 80,
    render: function render(dom, entity, index, action, schema) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)(components_TaskProductionsModal, {
        product_id: entity.agri_product_name,
        product_label: entity.agri_product_label,
        crop_id: cropId
      });
    }
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "category.material-management.unit",
      defaultMessage: "unknown"
    }),
    dataIndex: 'unit_label',
    width: 60
  }, {
    // title: intl.formatMessage({ id: 'seasonalTab.totalExpected' }),
    title: /*#__PURE__*/(0,jsx_runtime.jsxs)(tooltip/* default */.Z, {
      color: constanst/* COLOR_HEX */.wE.GREEN_TOOLTIP,
      title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
        id: "tooltips.total_exp_quantity"
      }),
      children: [intl.formatMessage({
        id: 'seasonalTab.totalExpected'
      }), " ", /*#__PURE__*/(0,jsx_runtime.jsx)(InfoCircleOutlined/* default */.Z, {})]
    }, constanst/* COLOR_HEX */.wE.GREEN_TOOLTIP),
    dataIndex: 'total_exp_quantity',
    render: function render(dom, entity, index, action, schema) {
      return numeral_default()(entity.total_exp_quantity).format('0,0.00');
    },
    width: 100
  }, {
    // title: intl.formatMessage({ id: 'seasonalTab.totalExpected' }),
    title: /*#__PURE__*/(0,jsx_runtime.jsxs)(tooltip/* default */.Z, {
      color: constanst/* COLOR_HEX */.wE.GREEN_TOOLTIP,
      title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
        id: "tooltips.total_draft_quantity"
      }),
      children: [intl.formatMessage({
        id: 'seasonalTab.totalDraft'
      }), " ", /*#__PURE__*/(0,jsx_runtime.jsx)(InfoCircleOutlined/* default */.Z, {})]
    }, constanst/* COLOR_HEX */.wE.GREEN_TOOLTIP),
    dataIndex: 'total_draft_quantity',
    width: 100,
    render: function render(dom, entity, index, action, schema) {
      return numeral_default()(entity.total_draft_quantity).format('0,0.00');
    },
    hideInTable: (0,access/* isSubscribedStock */.ak)()
  },
  // {
  //   title: intl.formatMessage({ id: 'seasonalTab.totalUsed' }),
  //   dataIndex: 'total_quantity',
  //   render(dom, entity, index, action, schema) {
  //     return numeral(entity.total_quantity).format('0,0.00');
  //   },
  //   width: 80,
  // },

  {
    // title: intl.formatMessage({ id: 'seasonalTab.totalIssue' }),
    title: /*#__PURE__*/(0,jsx_runtime.jsxs)(tooltip/* default */.Z, {
      color: constanst/* COLOR_HEX */.wE.GREEN_TOOLTIP,
      title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
        id: "tooltips.total_issued_quantity"
      }),
      children: [intl.formatMessage({
        id: 'seasonalTab.totalIssue'
      }), " ", /*#__PURE__*/(0,jsx_runtime.jsx)(InfoCircleOutlined/* default */.Z, {})]
    }, constanst/* COLOR_HEX */.wE.GREEN_TOOLTIP),
    dataIndex: 'total_issued_quantity',
    render: function render(dom, entity, index, action, schema) {
      return numeral_default()(dom).format('0,0.00');
    },
    width: 100,
    hideInTable: !(0,access/* isSubscribedStock */.ak)()
  }, {
    // title: intl.formatMessage({ id: 'common.total_harvested_quantity' }),
    title: /*#__PURE__*/(0,jsx_runtime.jsxs)(tooltip/* default */.Z, {
      color: constanst/* COLOR_HEX */.wE.GREEN_TOOLTIP,
      title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
        id: "tooltips.total_finished_quantity"
      }),
      children: [intl.formatMessage({
        id: 'common.total_harvested_quantity'
      }), " ", /*#__PURE__*/(0,jsx_runtime.jsx)(InfoCircleOutlined/* default */.Z, {})]
    }, constanst/* COLOR_HEX */.wE.GREEN_TOOLTIP),
    dataIndex: 'total_finished_quantity',
    render: function render(dom, entity, index, action, schema) {
      return numeral_default()(entity.total_finished_quantity).format('0,0.00');
    },
    width: 120,
    hideInTable: !(0,access/* isSubscribedStock */.ak)()
  }, {
    // title: intl.formatMessage({ id: 'seasonalTab.totalReality' }),
    title: /*#__PURE__*/(0,jsx_runtime.jsxs)(tooltip/* default */.Z, {
      color: constanst/* COLOR_HEX */.wE.GREEN_TOOLTIP,
      title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
        id: "tooltips.total_real_quantity_for_production"
      }),
      children: [intl.formatMessage({
        id: 'seasonalTab.totalReality'
      }), " ", /*#__PURE__*/(0,jsx_runtime.jsx)(InfoCircleOutlined/* default */.Z, {})]
    }, constanst/* COLOR_HEX */.wE.GREEN_TOOLTIP),
    dataIndex: 'total_real_quantity',
    render: function render(dom, entity, index, action, schema) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)(RealityColumnRender/* default */.Z, {
        children: numeral_default()(entity.total_real_quantity).format('0,0.00')
      });
    },
    width: 100,
    hideInTable: !(0,access/* isSubscribedStock */.ak)()
  }
  // {
  //   title: intl.formatMessage({ id: 'common.purchase-price' }),
  //   dataIndex: 'valuation_rate',
  //   render(dom, entity, index, action, schema) {
  //     const valuation_rate = entity.valuation_rate ? entity.valuation_rate : 0;
  //     return numeral(entity.valuation_rate).format('0,000');
  //   },
  //   width: 80,
  // },
  ];
  return /*#__PURE__*/(0,jsx_runtime.jsx)(Table/* default */.Z, {
    headerTitle: intl.formatMessage({
      id: 'seasonalTab.outputStatistics'
    }),
    columns: columns,
    search: false,
    pagination: {
      pageSizeOptions: [10, 20, 50, 100],
      showSizeChanger: true
    },
    request: ( /*#__PURE__*/function () {
      var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(params, sorter, filter) {
        var res;
        return regeneratorRuntime_default()().wrap(function _callee$(_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              _context.prev = 0;
              _context.next = 3;
              return (0,crop/* getCropProductionQuantityStatistic */.su)({
                page: params.current,
                size: params.pageSize,
                crop_id: cropId
              });
            case 3:
              res = _context.sent;
              setData(res.data);
              return _context.abrupt("return", {
                data: res.data,
                success: true
              });
            case 8:
              _context.prev = 8;
              _context.t0 = _context["catch"](0);
              message.error("Error when getting Crop Production Quantity: ".concat(_context.t0.message));
              return _context.abrupt("return", {
                success: false
              });
            case 12:
            case "end":
              return _context.stop();
          }
        }, _callee, null, [[0, 8]]);
      }));
      return function (_x, _x2, _x3) {
        return _ref2.apply(this, arguments);
      };
    }()),
    rowKey: 'name'
  });
};
/* harmony default export */ var components_ProductionQuantityStatistic = (ProductionQuantityStatistic);
// EXTERNAL MODULE: ./src/services/plantRefAndUserOwner.ts
var plantRefAndUserOwner = __webpack_require__(44045);
// EXTERNAL MODULE: ./src/services/zones.ts
var zones = __webpack_require__(95728);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/DeleteOutlined.js
var DeleteOutlined = __webpack_require__(82061);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/CameraFilled.js + 1 modules
var CameraFilled = __webpack_require__(9890);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/UploadButton/index.js + 1 modules
var UploadButton = __webpack_require__(77636);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/layouts/index.js
var layouts = __webpack_require__(24739);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/Text/index.js
var Text = __webpack_require__(5966);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/Select/index.js
var Select = __webpack_require__(64317);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/DateRangePicker/index.js
var DateRangePicker = __webpack_require__(34540);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/Digit/index.js
var Digit = __webpack_require__(31199);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/TextArea/index.js
var TextArea = __webpack_require__(90672);
// EXTERNAL MODULE: ./node_modules/antd/es/form/index.js + 18 modules
var es_form = __webpack_require__(98138);
// EXTERNAL MODULE: ./node_modules/antd/es/message/index.js
var message = __webpack_require__(45360);
// EXTERNAL MODULE: ./node_modules/antd/es/row/index.js
var row = __webpack_require__(71230);
// EXTERNAL MODULE: ./node_modules/antd/es/col/index.js
var col = __webpack_require__(15746);
// EXTERNAL MODULE: ./node_modules/antd/es/typography/index.js + 18 modules
var typography = __webpack_require__(25514);
// EXTERNAL MODULE: ./node_modules/antd/es/qr-code/index.js + 3 modules
var qr_code = __webpack_require__(10397);
// EXTERNAL MODULE: ./node_modules/html2canvas/dist/html2canvas.js
var html2canvas = __webpack_require__(46020);
var html2canvas_default = /*#__PURE__*/__webpack_require__.n(html2canvas);
// EXTERNAL MODULE: ./node_modules/jspdf/dist/jspdf.es.min.js + 1 modules
var jspdf_es_min = __webpack_require__(84730);
;// CONCATENATED MODULE: ./src/pages/FarmingManagement/SeasonalManagement/Detail/GeneralInfo/components/QRCodeModal.tsx









var Item = es_form/* default */.Z.Item;
var QRCodeModal_Text = typography/* default */.Z.Text;
var QRCodeModal = function QRCodeModal(_ref) {
  var form = _ref.form,
    cropId = _ref.cropId;
  var _useState = (0,react.useState)(false),
    _useState2 = slicedToArray_default()(_useState, 2),
    isOpen = _useState2[0],
    setOpen = _useState2[1];
  var handleDownload = function handleDownload() {
    var doc = new jspdf_es_min/* default */.ZP({
      unit: 'px',
      format: [400, 600]
    });
    var contentElement = document.getElementById('myqrcode');
    if (contentElement) {
      html2canvas_default()(contentElement).then(function (canvas) {
        var imgData = canvas.toDataURL('image/png');
        doc.addImage(imgData, 'PNG', 0, 0, 400, 600);
        doc.save("QrCode_[".concat(form.getFieldValue('label'), "].pdf"));
      });
    }
  };
  var showModal = function showModal() {
    setOpen(true);
  };
  var hideModal = function hideModal() {
    setOpen(false);
  };
  var handleOk = function handleOk() {
    handleDownload();
  };
  var handleCancel = function handleCancel() {
    hideModal();
  };
  return /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
    children: [/*#__PURE__*/(0,jsx_runtime.jsx)("a", {
      onClick: showModal,
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
        id: "common.download_qr_code"
      })
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(modal/* default */.Z, {
      title: "M\\xE3 QR m\\xF9a v\\u1EE5",
      open: isOpen,
      onOk: handleOk,
      onCancel: handleCancel,
      okText: 'T\u1EA3i xu\u1ED1ng',
      cancelText: '\u0110\xF3ng',
      children: /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        id: "myqrcode",
        style: {
          width: '400px',
          height: '600px',
          position: 'relative',
          margin: 'auto'
        },
        children: /*#__PURE__*/(0,jsx_runtime.jsxs)(card/* default */.Z, {
          bordered: true,
          style: {
            width: 400,
            height: 600
          }
          // cover={
          //   <QRCode size={200} value={\`crop,\${cropId}\`} bgColor="#fff" style={{ padding: 20 }} />
          // }
          ,
          children: [/*#__PURE__*/(0,jsx_runtime.jsx)(qr_code/* default */.Z, {
            size: 300,
            value: "crop,".concat(cropId),
            bgColor: "#fff",
            style: {
              padding: 20,
              margin: 'auto'
            }
          }), /*#__PURE__*/(0,jsx_runtime.jsx)(QRCodeModal_Text, {
            style: {
              fontSize: 40
            },
            children: "T\\xEAn m\\xF9a v\\u1EE5:"
          }), /*#__PURE__*/(0,jsx_runtime.jsx)("br", {}), /*#__PURE__*/(0,jsx_runtime.jsx)(QRCodeModal_Text, {
            style: {
              fontSize: 40
            },
            children: /*#__PURE__*/(0,jsx_runtime.jsx)("strong", {
              children: form.getFieldValue('label')
            })
          }), /*#__PURE__*/(0,jsx_runtime.jsx)("br", {})]
        })
      })
    })]
  });
};
/* harmony default export */ var components_QRCodeModal = (QRCodeModal);
;// CONCATENATED MODULE: ./src/pages/FarmingManagement/SeasonalManagement/Detail/GeneralInfo/DetailedInfo.tsx
















var DetailedInfo = function DetailedInfo(_ref) {
  var children = _ref.children,
    cropId = _ref.cropId;
  var componentRef = (0,react.useRef)(null);
  var intl = (0,_umi_production_exports.useIntl)();
  var access = (0,_umi_production_exports.useAccess)();
  var canDelete = access.canDeleteInSeasonalManagement();
  var form = es_form/* default */.Z.useFormInstance();
  var onDeleteCrop = function onDeleteCrop() {
    modal/* default */.Z.confirm({
      content: 'B\u1EA1n c\xF3 ch\u1EAFc ch\u1EAFn mu\u1ED1n xo\xE1 v\u1EE5 m\xF9a n\xE0y?',
      onOk: function () {
        var _onOk = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
          var updateBody;
          return regeneratorRuntime_default()().wrap(function _callee$(_context) {
            while (1) switch (_context.prev = _context.next) {
              case 0:
                _context.prev = 0;
                // await deletePlan({
                //   name: planId,
                // });
                updateBody = {
                  name: cropId,
                  is_deleted: 1
                };
                _context.next = 4;
                return (0,cropManager/* updateCrop */.Ak)(updateBody);
              case 4:
                message/* default */.ZP.success({
                  content: 'Delete successfully'
                });
                _umi_production_exports.history.push("/farming-management/seasonal-management");

                // await refresh();
                return _context.abrupt("return", true);
              case 9:
                _context.prev = 9;
                _context.t0 = _context["catch"](0);
                message/* default */.ZP.error({
                  content: 'Delete error, please try again'
                });
                return _context.abrupt("return", false);
              case 13:
              case "end":
                return _context.stop();
            }
          }, _callee, null, [[0, 9]]);
        }));
        function onOk() {
          return _onOk.apply(this, arguments);
        }
        return onOk;
      }(),
      okButtonProps: {
        danger: true
      }
    });
  };
  return /*#__PURE__*/(0,jsx_runtime.jsx)(card/* default */.Z, {
    title: intl.formatMessage({
      id: 'seasonalTab.detailInformation'
    }),
    extra: /*#__PURE__*/(0,jsx_runtime.jsx)(jsx_runtime.Fragment, {
      children: canDelete && /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
        size: "small",
        icon: /*#__PURE__*/(0,jsx_runtime.jsx)(DeleteOutlined/* default */.Z, {}),
        danger: true,
        onClick: onDeleteCrop,
        children: intl.formatMessage({
          id: 'common.delete'
        })
      }, 'delete')
    }),
    children: /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
      gutter: 10,
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        md: 3,
        sm: 24,
        children: /*#__PURE__*/(0,jsx_runtime.jsxs)(space/* default */.Z, {
          align: "center",
          direction: "vertical",
          size: 'small',
          children: [/*#__PURE__*/(0,jsx_runtime.jsx)(UploadButton/* default */.Z, {
            label: intl.formatMessage({
              id: 'common.form.image'
            }),
            accept: "image/*",
            listType: "picture-card",
            icon: /*#__PURE__*/(0,jsx_runtime.jsx)(CameraFilled/* default */.Z, {}),
            title: "",
            name: "avatar",
            max: 1 // Set maximum files to 1
            ,
            style: {
              width: 80
            }
          }), /*#__PURE__*/(0,jsx_runtime.jsx)(components_QRCodeModal, {
            form: form,
            cropId: cropId
          })]
        })
      }), /*#__PURE__*/(0,jsx_runtime.jsxs)(col/* default */.Z, {
        md: 14,
        sm: 24,
        children: [/*#__PURE__*/(0,jsx_runtime.jsxs)(layouts/* ProFormGroup */.UW, {
          children: [/*#__PURE__*/(0,jsx_runtime.jsx)(Text/* default */.Z, {
            label: intl.formatMessage({
              id: 'seasonalTab.seasonName'
            }),
            rules: [{
              required: true
            }],
            name: "label",
            colProps: {
              span: 12
            }
          }), /*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
            label: intl.formatMessage({
              id: 'common.area'
            }),
            rules: [{
              required: true
            }],
            request: /*#__PURE__*/asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2() {
              var res;
              return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
                while (1) switch (_context2.prev = _context2.next) {
                  case 0:
                    _context2.next = 2;
                    return (0,zones/* zoneList */.ly)({
                      size: Number.MAX_SAFE_INTEGER,
                      page: 1
                    });
                  case 2:
                    res = _context2.sent;
                    return _context2.abrupt("return", res.data.map(function (item) {
                      return {
                        label: item.label,
                        value: item.name
                      };
                    }));
                  case 4:
                  case "end":
                    return _context2.stop();
                }
              }, _callee2);
            })),
            name: "zone_id"
            // disabled
            ,
            colProps: {
              span: 12
            }
          })]
        }), /*#__PURE__*/(0,jsx_runtime.jsxs)(layouts/* ProFormGroup */.UW, {
          children: [/*#__PURE__*/(0,jsx_runtime.jsx)(DateRangePicker/* default */.Z, {
            fieldProps: {
              format: constanst/* DEFAULT_DATE_FORMAT_WITHOUT_TIME */.ug
            },
            label: intl.formatMessage({
              id: 'seasonalTab.time_completed'
            }),
            rules: [{
              required: true
            }],
            name: "date_range",
            colProps: {
              span: 12
            }
          }), /*#__PURE__*/(0,jsx_runtime.jsx)(Digit/* default */.Z, {
            label: intl.formatMessage({
              id: 'seasonalTab.cultivation_area'
            }),
            min: 0,
            name: "square",
            colProps: {
              span: 12
            }
          })]
        }), /*#__PURE__*/(0,jsx_runtime.jsxs)(layouts/* ProFormGroup */.UW, {
          children: [/*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
            label: intl.formatMessage({
              id: 'seasonalTab.selectTypeOfPlant'
            }),
            request: /*#__PURE__*/asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee3() {
              var res;
              return regeneratorRuntime_default()().wrap(function _callee3$(_context3) {
                while (1) switch (_context3.prev = _context3.next) {
                  case 0:
                    _context3.next = 2;
                    return (0,plantRefAndUserOwner/* getPlantUserOwnerAllResources */.A)({
                      size: constanst/* DEFAULT_PAGE_SIZE_ALL */.YY
                    });
                  case 2:
                    res = _context3.sent;
                    return _context3.abrupt("return", res.data.map(function (item) {
                      return {
                        label: item.label,
                        value: item.name
                      };
                    }));
                  case 4:
                  case "end":
                    return _context3.stop();
                }
              }, _callee3);
            })),
            name: "plant_id",
            colProps: {
              span: 12
            }
          }), /*#__PURE__*/(0,jsx_runtime.jsx)(Digit/* default */.Z, {
            label: intl.formatMessage({
              id: 'seasonalTab.expectedOutputInKg'
            }),
            min: 0,
            name: "quantity_estimate",
            colProps: {
              span: 12
            }
          })]
        })]
      }), /*#__PURE__*/(0,jsx_runtime.jsxs)(col/* default */.Z, {
        md: 7,
        sm: 24,
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
          rules: [{
            required: true
          }],
          label: intl.formatMessage({
            id: 'common.status'
          }),
          name: "status",
          options: [{
            label: '\u0110ang di\u1EC5n ra',
            value: 'In progress'
          }, {
            label: 'Ho\xE0n t\u1EA5t',
            value: 'Done'
          }],
          initialValue: 'Plan',
          colProps: {
            span: 24
          }
        }), /*#__PURE__*/(0,jsx_runtime.jsx)(TextArea/* default */.Z, {
          label: intl.formatMessage({
            id: 'common.note'
          }),
          name: "description",
          colProps: {
            span: 24
          }
        })]
      })]
    })
  });
};
/* harmony default export */ var GeneralInfo_DetailedInfo = (DetailedInfo);
;// CONCATENATED MODULE: ./src/pages/FarmingManagement/SeasonalManagement/Detail/GeneralInfo/index.tsx




















var GeneralInfo = function GeneralInfo(_ref) {
  var onSuccess = _ref.onSuccess,
    cropId = _ref.cropId;
  var _App$useApp = app/* default */.Z.useApp(),
    message = _App$useApp.message;
  var _useState = (0,react.useState)(false),
    _useState2 = slicedToArray_default()(_useState, 2),
    submitting = _useState2[0],
    setSubmitting = _useState2[1];
  var intl = (0,_umi_production_exports.useIntl)();
  var onFinish = /*#__PURE__*/function () {
    var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee3(values) {
      var dataUpdated, filesUploaded, filesNotUpload, uploadListRes, checkUploadFailed, arrFileUrl, _filesUploaded, _filesNotUpload, _uploadListRes, _checkUploadFailed, _arrFileUrl;
      return regeneratorRuntime_default()().wrap(function _callee3$(_context3) {
        while (1) switch (_context3.prev = _context3.next) {
          case 0:
            setSubmitting(true);
            _context3.prev = 1;
            _context3.next = 4;
            return (0,cropManager/* updateCrop */.Ak)({
              name: cropId,
              label: values.label,
              zone_id: values.zone_id,
              square: values.square,
              plant_id: values.plant_id,
              start_date: values.date_range[0],
              end_date: values.date_range[1],
              description: values.description,
              image: null,
              avatar: null,
              status: values.status,
              quantity_estimate: values.quantity_estimate
            });
          case 4:
            dataUpdated = _context3.sent;
            if (!(values.img && (values.img || []).length > 0)) {
              _context3.next = 17;
              break;
            }
            // ki\u1EC3m tra c\xE1c file \u0111\xE3 upload
            filesUploaded = values.img.filter(function (item) {
              return !item.originFileObj;
            });
            filesNotUpload = values.img.filter(function (item) {
              return item.originFileObj;
            }); // upload b\u1EA5t k\u1EC3 th\xE0nh c\xF4ng hay ko
            _context3.next = 10;
            return Promise.allSettled(filesNotUpload.map( /*#__PURE__*/function () {
              var _ref3 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(item) {
                return regeneratorRuntime_default()().wrap(function _callee$(_context) {
                  while (1) switch (_context.prev = _context.next) {
                    case 0:
                      _context.next = 2;
                      return (0,services_fileUpload/* uploadFile */.cT)({
                        docType: constanst/* DOCTYPE_ERP */.lH.iotCrop,
                        docName: dataUpdated.data.name,
                        file: item.originFileObj
                      });
                    case 2:
                      return _context.abrupt("return", _context.sent);
                    case 3:
                    case "end":
                      return _context.stop();
                  }
                }, _callee);
              }));
              return function (_x2) {
                return _ref3.apply(this, arguments);
              };
            }()));
          case 10:
            uploadListRes = _context3.sent;
            // check if() 1 v\xE0i upload failed
            checkUploadFailed = uploadListRes.find(function (item) {
              return item.status === 'rejected';
            });
            if (checkUploadFailed) {
              message.error({
                content: 'Some file upload failed'
              });
            }

            // update img path
            arrFileUrl = uploadListRes.reduce(function (prev, item) {
              var _item$value;
              return item.status === 'fulfilled' ? [].concat(toConsumableArray_default()(prev), [item === null || item === void 0 || (_item$value = item.value) === null || _item$value === void 0 || (_item$value = _item$value.data) === null || _item$value === void 0 || (_item$value = _item$value.message) === null || _item$value === void 0 ? void 0 : _item$value.file_url]) : prev;
            }, []).filter(function (item) {
              return typeof item === 'string';
            })
            // th\xEAm file \u0111\xE3 upload
            .concat(filesUploaded.map(function (item) {
              return item.url;
            }));
            if (!(arrFileUrl.length > 0)) {
              _context3.next = 17;
              break;
            }
            _context3.next = 17;
            return (0,cropManager/* updateCrop */.Ak)({
              name: dataUpdated.data.name,
              image: arrFileUrl.join(','),
              zone_id: dataUpdated.data.zone_id
            });
          case 17:
            if (!(values.avatar && (values.avatar || []).length > 0)) {
              _context3.next = 29;
              break;
            }
            // ki\u1EC3m tra c\xE1c file \u0111\xE3 upload
            _filesUploaded = values.avatar.filter(function (item) {
              return !item.originFileObj;
            });
            _filesNotUpload = values.avatar.filter(function (item) {
              return item.originFileObj;
            }); // upload b\u1EA5t k\u1EC3 th\xE0nh c\xF4ng hay ko
            _context3.next = 22;
            return Promise.allSettled(_filesNotUpload.map( /*#__PURE__*/function () {
              var _ref4 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2(item) {
                return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
                  while (1) switch (_context2.prev = _context2.next) {
                    case 0:
                      _context2.next = 2;
                      return (0,services_fileUpload/* uploadFile */.cT)({
                        docType: constanst/* DOCTYPE_ERP */.lH.iotCrop,
                        docName: dataUpdated.data.name,
                        file: item.originFileObj
                      });
                    case 2:
                      return _context2.abrupt("return", _context2.sent);
                    case 3:
                    case "end":
                      return _context2.stop();
                  }
                }, _callee2);
              }));
              return function (_x3) {
                return _ref4.apply(this, arguments);
              };
            }()));
          case 22:
            _uploadListRes = _context3.sent;
            // check if() 1 v\xE0i upload failed
            _checkUploadFailed = _uploadListRes.find(function (item) {
              return item.status === 'rejected';
            });
            if (_checkUploadFailed) {
              message.error({
                content: 'Some file upload failed'
              });
            }

            // update avatar path
            _arrFileUrl = _uploadListRes.reduce(function (prev, item) {
              var _item$value2;
              return item.status === 'fulfilled' ? [].concat(toConsumableArray_default()(prev), [item === null || item === void 0 || (_item$value2 = item.value) === null || _item$value2 === void 0 || (_item$value2 = _item$value2.data) === null || _item$value2 === void 0 || (_item$value2 = _item$value2.message) === null || _item$value2 === void 0 ? void 0 : _item$value2.file_url]) : prev;
            }, []).filter(function (item) {
              return typeof item === 'string';
            })
            // th\xEAm file \u0111\xE3 upload
            .concat(_filesUploaded.map(function (item) {
              return item.url;
            }));
            if (!(_arrFileUrl.length > 0)) {
              _context3.next = 29;
              break;
            }
            _context3.next = 29;
            return (0,cropManager/* updateCrop */.Ak)({
              name: dataUpdated.data.name,
              avatar: _arrFileUrl[0],
              zone_id: dataUpdated.data.zone_id
            });
          case 29:
            message.success({
              content: 'Updated successfully'
            });
            onSuccess === null || onSuccess === void 0 || onSuccess();
            // history.push('/farming-management/seasonal-management');
            // window.location.reload();
            return _context3.abrupt("return", true);
          case 34:
            _context3.prev = 34;
            _context3.t0 = _context3["catch"](1);
            message.error({
              content: 'Error, please try again'
            });
            return _context3.abrupt("return", false);
          case 38:
            _context3.prev = 38;
            setSubmitting(false);
            return _context3.finish(38);
          case 41:
          case "end":
            return _context3.stop();
        }
      }, _callee3, null, [[1, 34, 38, 41]]);
    }));
    return function onFinish(_x) {
      return _ref2.apply(this, arguments);
    };
  }();
  var _ProForm$useForm = ProForm/* ProForm */.A.useForm(),
    _ProForm$useForm2 = slicedToArray_default()(_ProForm$useForm, 1),
    form = _ProForm$useForm2[0];
  var _useRequest = (0,_umi_production_exports.useRequest)(function () {
      return (0,cropManager/* getCropList */.TQ)({
        size: 1,
        page: 1,
        filters: [[constanst/* DOCTYPE_ERP */.lH.iotCrop, 'name', '=', cropId]]
      });
    }, {
      manual: true,
      onError: function onError() {
        message.error({
          content: 'Can not get crop information, please try again'
        });
      },
      onSuccess: function onSuccess(res) {
        var dataFound = res[0];
        if (!dataFound) return;
        form.setFieldsValue({
          label: dataFound.label,
          zone_id: dataFound.zone_id,
          date_range: [dataFound.start_date, dataFound.end_date],
          square: dataFound.square,
          plant_id: dataFound.plant_id,
          status: dataFound.status,
          description: dataFound.description,
          quantity_estimate: dataFound.quantity_estimate,
          img: (0,utils/* getListFileUrlFromString */.Id)({
            arrUrlString: dataFound.image
          }).map(function (item) {
            return {
              uid: (0,index_browser/* nanoid */.x0)(),
              status: 'done',
              url: item,
              type: 'image/*'
            };
          }),
          avatar: (0,utils/* getListFileUrlFromString */.Id)({
            arrUrlString: dataFound.avatar
          }).map(function (item) {
            return {
              uid: (0,index_browser/* nanoid */.x0)(),
              status: 'done',
              url: item,
              type: 'image/*'
            };
          })
        });
      }
    }),
    loading = _useRequest.loading,
    getDetail = _useRequest.run;
  (0,react.useEffect)(function () {
    if (cropId) {
      getDetail();
    }
  }, [cropId]);
  var access = (0,_umi_production_exports.useAccess)();
  var canUpdate = access.canUpdateInSeasonalManagement();
  return /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
    children: [/*#__PURE__*/(0,jsx_runtime.jsx)(spin/* default */.Z, {
      spinning: loading,
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(ProForm/* ProForm */.A, {
        onFinish: onFinish,
        form: form,
        submitter: false,
        grid: true,
        children: /*#__PURE__*/(0,jsx_runtime.jsxs)(space/* default */.Z, {
          size: 'large',
          direction: "vertical",
          style: {
            width: '100%'
          },
          children: [/*#__PURE__*/(0,jsx_runtime.jsx)(GeneralInfo_DetailedInfo, {
            cropId: cropId,
            form: form
          }), /*#__PURE__*/(0,jsx_runtime.jsxs)(card/* default */.Z, {
            title: intl.formatMessage({
              id: 'seasonalTab.basicStatistics'
            }),
            children: [/*#__PURE__*/(0,jsx_runtime.jsx)(components_ItemsStatistic, {
              cropId: cropId
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(divider/* default */.Z, {}), /*#__PURE__*/(0,jsx_runtime.jsx)(components_ProductionQuantityStatistic, {
              cropId: cropId
            })]
          }), /*#__PURE__*/(0,jsx_runtime.jsxs)(card/* default */.Z, {
            title: intl.formatMessage({
              id: 'common.crop_item_transactions_table'
            }),
            children: [' ', /*#__PURE__*/(0,jsx_runtime.jsx)(CropTransactionTables, {
              cropId: cropId
            })]
          })]
        })
      })
    }), /*#__PURE__*/(0,jsx_runtime.jsxs)(FooterToolbar/* FooterToolbar */.S, {
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
        onClick: function onClick() {
          _umi_production_exports.history.back();
        },
        children: intl.formatMessage({
          id: 'common.cancel'
        })
      }), canUpdate && /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
        loading: loading || submitting,
        type: "primary",
        onClick: function onClick() {
          form.submit();
        },
        children: intl.formatMessage({
          id: 'common.save'
        })
      })]
    })]
  });
};
/* harmony default export */ var Detail_GeneralInfo = (GeneralInfo);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///62557
`)},89436:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: function() { return /* binding */ getItemGroupList; },
/* harmony export */   m: function() { return /* binding */ getItemList; }
/* harmony export */ });
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(97857);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(7837);
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(467);





var handleError = function handleError(error) {
  console.log("Error in services/warehouse: \\n".concat(error));
};
var CRUD_PATH = {
  READ: 'item',
  READ_GROUP: 'itemGroup'
};
var getItemList = /*#__PURE__*/function () {
  var _ref = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.prev = 0;
          _context.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)("api/v2/".concat(CRUD_PATH.READ)), {
            method: 'GET',
            params: D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({
              page: 1,
              size: 100
            }, (0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .getParamsReqList */ .vj)(params))
          });
        case 3:
          res = _context.sent;
          return _context.abrupt("return", {
            data: res.result.data || []
          });
        case 7:
          _context.prev = 7;
          _context.t0 = _context["catch"](0);
          handleError(_context.t0);
          return _context.abrupt("return", {
            data: []
          });
        case 11:
        case "end":
          return _context.stop();
      }
    }, _callee, null, [[0, 7]]);
  }));
  return function getItemList(_x) {
    return _ref.apply(this, arguments);
  };
}();
var getItemGroupList = /*#__PURE__*/function () {
  var _ref2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee2(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          _context2.prev = 0;
          _context2.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)("api/v2/".concat(CRUD_PATH.READ_GROUP)), {
            method: 'GET',
            params: D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({
              page: 1,
              size: 100
            }, (0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .getParamsReqList */ .vj)(params))
          });
        case 3:
          res = _context2.sent;
          return _context2.abrupt("return", {
            data: res.result.data || []
          });
        case 7:
          _context2.prev = 7;
          _context2.t0 = _context2["catch"](0);
          handleError(_context2.t0);
          return _context2.abrupt("return", {
            data: []
          });
        case 11:
        case "end":
          return _context2.stop();
      }
    }, _callee2, null, [[0, 7]]);
  }));
  return function getItemGroupList(_x2) {
    return _ref2.apply(this, arguments);
  };
}();//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///89436
`)}}]);
