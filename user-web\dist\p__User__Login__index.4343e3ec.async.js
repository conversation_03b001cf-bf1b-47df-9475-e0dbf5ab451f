"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[8727],{27050:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return /* binding */ User_Login; }
});

// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/regeneratorRuntime.js
var regeneratorRuntime = __webpack_require__(15009);
var regeneratorRuntime_default = /*#__PURE__*/__webpack_require__.n(regeneratorRuntime);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/objectSpread2.js
var objectSpread2 = __webpack_require__(97857);
var objectSpread2_default = /*#__PURE__*/__webpack_require__.n(objectSpread2);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(99289);
var asyncToGenerator_default = /*#__PURE__*/__webpack_require__.n(asyncToGenerator);
;// CONCATENATED MODULE: ./src/assets/img/login-bg.png
var login_bg_namespaceObject = __webpack_require__.p + "static/login-bg.80065b13.png";
// EXTERNAL MODULE: ./src/assets/img/logo-text-white.svg
var logo_text_white = __webpack_require__(65625);
// EXTERNAL MODULE: ./src/components/Footer/index.tsx
var Footer = __webpack_require__(99702);
// EXTERNAL MODULE: ./src/services/auth.ts
var auth = __webpack_require__(27203);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/UserOutlined.js + 1 modules
var UserOutlined = __webpack_require__(87547);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectSpread2.js
var esm_objectSpread2 = __webpack_require__(1413);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
;// CONCATENATED MODULE: ./node_modules/@ant-design/icons-svg/es/asn/LockOutlined.js
// This icon file is generated automatically.
var LockOutlined = { "icon": { "tag": "svg", "attrs": { "viewBox": "64 64 896 896", "focusable": "false" }, "children": [{ "tag": "path", "attrs": { "d": "M832 464h-68V240c0-70.7-57.3-128-128-128H388c-70.7 0-128 57.3-128 128v224h-68c-17.7 0-32 14.3-32 32v384c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V496c0-17.7-14.3-32-32-32zM332 240c0-30.9 25.1-56 56-56h248c30.9 0 56 25.1 56 56v224H332V240zm460 600H232V536h560v304zM484 701v53c0 4.4 3.6 8 8 8h40c4.4 0 8-3.6 8-8v-53a48.01 48.01 0 10-56 0z" } }] }, "name": "lock", "theme": "outlined" };
/* harmony default export */ var asn_LockOutlined = (LockOutlined);

// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/components/AntdIcon.js + 5 modules
var AntdIcon = __webpack_require__(89099);
;// CONCATENATED MODULE: ./node_modules/@ant-design/icons/es/icons/LockOutlined.js

// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY



var LockOutlined_LockOutlined = function LockOutlined(props, ref) {
  return /*#__PURE__*/react.createElement(AntdIcon/* default */.Z, (0,esm_objectSpread2/* default */.Z)((0,esm_objectSpread2/* default */.Z)({}, props), {}, {
    ref: ref,
    icon: asn_LockOutlined
  }));
};
LockOutlined_LockOutlined.displayName = 'LockOutlined';
/* harmony default export */ var icons_LockOutlined = (/*#__PURE__*/react.forwardRef(LockOutlined_LockOutlined));
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/layouts/LoginForm/index.js + 1 modules
var LoginForm = __webpack_require__(68262);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/Text/index.js
var Text = __webpack_require__(5966);
// EXTERNAL MODULE: ./src/.umi-production/exports.ts + 34 modules
var _umi_production_exports = __webpack_require__(7837);
// EXTERNAL MODULE: ./node_modules/antd/es/message/index.js
var message = __webpack_require__(45360);
// EXTERNAL MODULE: ./node_modules/antd/es/row/index.js
var row = __webpack_require__(71230);
// EXTERNAL MODULE: ./node_modules/antd/es/col/index.js
var col = __webpack_require__(15746);
// EXTERNAL MODULE: ./node_modules/antd-use-styles/dist/antd-use-styles.js
var antd_use_styles = __webpack_require__(38513);
;// CONCATENATED MODULE: ./src/pages/User/Login/index.less?modules
// extracted by mini-css-extract-plugin
/* harmony default export */ var Loginmodules = ({"container":"container___REQAy","side":"side___TiHNa","bg":"bg___mO5CL","content":"content___IkUx1"});
// EXTERNAL MODULE: ./node_modules/react/jsx-runtime.js
var jsx_runtime = __webpack_require__(85893);
;// CONCATENATED MODULE: ./src/pages/User/Login/index.tsx

















var useStyles = (0,antd_use_styles/* createStyles */.k)(function (_ref) {
  var token = _ref.token;
  return {
    slideFooter: {
      color: 'white'
    },
    textLarge: {
      fontSize: token.fontSizeHeading1 + 10,
      fontWeight: 700
    },
    textMedium: {
      fontSize: token.fontSizeHeading3,
      fontWeight: token.fontWeightStrong
    },
    text: {
      fontSize: token.fontSizeHeading4,
      fontWeight: token.fontWeightStrong,
      lineHeight: 1.5
    },
    textUppercase: {
      textTransform: 'uppercase'
    }
  };
});
var Login = function Login() {
  var _initialState$current2;
  var style = useStyles();
  var _useModel = (0,_umi_production_exports.useModel)('@@initialState'),
    initialState = _useModel.initialState,
    setInitialState = _useModel.setInitialState;
  var fetchUserInfo = /*#__PURE__*/function () {
    var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(onSuccess) {
      var _initialState$fetchUs;
      var userInfo;
      return regeneratorRuntime_default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _context.next = 2;
            return initialState === null || initialState === void 0 || (_initialState$fetchUs = initialState.fetchUserInfo) === null || _initialState$fetchUs === void 0 ? void 0 : _initialState$fetchUs.call(initialState);
          case 2:
            userInfo = _context.sent;
            if (!userInfo) {
              _context.next = 7;
              break;
            }
            _context.next = 6;
            return setInitialState(function (s) {
              return objectSpread2_default()(objectSpread2_default()({}, s), {}, {
                currentUser: userInfo
              });
            });
          case 6:
            onSuccess === null || onSuccess === void 0 || onSuccess();
          case 7:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }));
    return function fetchUserInfo(_x) {
      return _ref2.apply(this, arguments);
    };
  }();
  var handleSubmit = /*#__PURE__*/function () {
    var _ref3 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2(values) {
      var _loginResp$result, loginResp, userdata;
      return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            _context2.prev = 0;
            _context2.next = 3;
            return (0,auth/* login */.x4)(objectSpread2_default()({}, values));
          case 3:
            loginResp = _context2.sent;
            userdata = loginResp === null || loginResp === void 0 || (_loginResp$result = loginResp.result) === null || _loginResp$result === void 0 ? void 0 : _loginResp$result.user; // console.log(userdata, "userdata");
            localStorage.setItem('token', JSON.stringify(loginResp === null || loginResp === void 0 ? void 0 : loginResp.result));
            _context2.next = 8;
            return setInitialState(function (s) {
              return objectSpread2_default()(objectSpread2_default()({}, s), {}, {
                currentUser: userdata
              });
            });
          case 8:
            window.location.href = window.location.origin;
            _context2.next = 14;
            break;
          case 11:
            _context2.prev = 11;
            _context2.t0 = _context2["catch"](0);
            message/* default */.ZP.error('Login Failed, Try Again!');
          case 14:
          case "end":
            return _context2.stop();
        }
      }, _callee2, null, [[0, 11]]);
    }));
    return function handleSubmit(_x2) {
      return _ref3.apply(this, arguments);
    };
  }();
  (0,react.useEffect)(function () {
    var _initialState$current;
    if (!(initialState !== null && initialState !== void 0 && (_initialState$current = initialState.currentUser) !== null && _initialState$current !== void 0 && _initialState$current.user_id)) fetchUserInfo();
    var urlParams = new URL(window.location.href).searchParams;
    // history.push(urlParams.get('redirect') || '/');
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [initialState === null || initialState === void 0 || (_initialState$current2 = initialState.currentUser) === null || _initialState$current2 === void 0 ? void 0 : _initialState$current2.user_id]);
  var mainternanceMode = initialState === null || initialState === void 0 ? void 0 : initialState.maintenanceMode;
  return /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
    className: Loginmodules.container,
    children: [/*#__PURE__*/(0,jsx_runtime.jsxs)(col/* default */.Z, {
      md: 12,
      xs: 0,
      className: Loginmodules.side,
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        style: {
          paddingBlock: 45,
          paddingInline: 30
        },
        children: /*#__PURE__*/(0,jsx_runtime.jsx)("img", {
          src: logo_text_white/* default */.Z
        })
      }), /*#__PURE__*/(0,jsx_runtime.jsx)("img", {
        className: Loginmodules.bg,
        src: login_bg_namespaceObject,
        alt: "bg"
      }), /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        className: style.slideFooter,
        style: {
          paddingBlock: 45,
          paddingInline: 30
        },
        children: /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
          children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
            span: 13,
            children: /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
              className: style.textUppercase,
              children: [/*#__PURE__*/(0,jsx_runtime.jsx)("div", {
                className: style.textLarge,
                children: "Qu\\u1EA3n l\\xFD "
              }), /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
                className: style.textLarge,
                style: {
                  marginBlockEnd: 10
                },
                children: "th\\xF4ng minh"
              }), /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
                className: style.textMedium,
                children: "Trong n\\xF4ng nghi\\u1EC7p"
              })]
            })
          }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
            span: 9,
            children: /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
              className: style.text,
              style: {
                textAlign: 'right'
              },
              children: "V\\u1EDBi VIIS b\\u1EA1n c\\xF3 th\\u1EC3 d\\u1EC5 d\\xE0ng \\u0111i\\u1EC1u khi\\u1EC3n, nhanh ch\\xF3ng, ti\\u1EBFt ki\\u1EC7m th\\u1EDDi gian v\\xE0 c\\xF4ng s\\u1EE9c. Qu\\u1EA3n l\\xFD c\\xF4ng vi\\u1EC7c d\\u1EC5 d\\xE0ng v\\xE0 hi\\u1EC7u qu\\u1EA3."
            })
          })]
        })
      })]
    }), /*#__PURE__*/(0,jsx_runtime.jsxs)(col/* default */.Z, {
      md: 12,
      xs: 24,
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        className: Loginmodules.content,
        children: /*#__PURE__*/(0,jsx_runtime.jsxs)(LoginForm/* LoginForm */.U, {
          logo: /*#__PURE__*/(0,jsx_runtime.jsx)("img", {
            style: {
              width: '150px'
            },
            alt: "logo",
            src: "/viis_logo.svg"
          }),
          title: /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
            style: {
              marginTop: '20px'
            },
            children: "\\u0110\\u0102NG NH\\u1EACP V\\xC0O H\\u1EC6 TH\\u1ED0NG"
          }),
          subTitle: /*#__PURE__*/(0,jsx_runtime.jsx)(jsx_runtime.Fragment, {}),
          initialValues: {
            autoLogin: true
          },
          onFinish: ( /*#__PURE__*/function () {
            var _ref4 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee3(values) {
              return regeneratorRuntime_default()().wrap(function _callee3$(_context3) {
                while (1) switch (_context3.prev = _context3.next) {
                  case 0:
                    _context3.next = 2;
                    return handleSubmit(values);
                  case 2:
                  case "end":
                    return _context3.stop();
                }
              }, _callee3);
            }));
            return function (_x3) {
              return _ref4.apply(this, arguments);
            };
          }()),
          submitter: {
            searchConfig: {
              submitText: mainternanceMode ? '\u0110ang b\u1EA3o tr\xEC' : '\u0110\u0103ng nh\u1EADp'
            }
          },
          children: [/*#__PURE__*/(0,jsx_runtime.jsx)(Text/* default */.Z, {
            name: "usr",
            fieldProps: {
              size: 'large',
              prefix: /*#__PURE__*/(0,jsx_runtime.jsx)(UserOutlined/* default */.Z, {
                className: Loginmodules.prefixIcon
              })
            },
            placeholder: 'Email',
            rules: [{
              required: true,
              message: 'field required'
            }]
          }), /*#__PURE__*/(0,jsx_runtime.jsx)(Text/* default */.Z.Password, {
            name: "pwd",
            fieldProps: {
              size: 'large',
              prefix: /*#__PURE__*/(0,jsx_runtime.jsx)(icons_LockOutlined, {
                className: Loginmodules.prefixIcon
              })
            },
            placeholder: 'M\u1EADt kh\u1EA9u',
            rules: [{
              required: true,
              message: 'field required'
            }]
          }), /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
            style: {
              marginBottom: 24
            },
            children: /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
              style: {
                "float": 'right'
              },
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.Link, {
                to: '/user/forgot-password',
                children: "B\\u1EA1n qu\\xEAn m\\u1EADt kh\\u1EA9u?"
              })
            })
          })]
        })
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(Footer/* default */.Z, {})]
    })]
  });
};
/* harmony default export */ var User_Login = (Login);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiMjcwNTAuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQTtBQUNBLHFCQUFxQixVQUFVLHlCQUF5QixrREFBa0QsaUJBQWlCLDBCQUEwQiwrVkFBK1YsR0FBRztBQUN2ZixxREFBZSxZQUFZLEVBQUM7Ozs7O0FDRnlDO0FBQ3JFO0FBQ0E7QUFDK0I7QUFDeUM7QUFDMUI7QUFDOUMsSUFBSSx5QkFBWTtBQUNoQixzQkFBc0IsbUJBQW1CLENBQUMsdUJBQVEsRUFBRSxvQ0FBYSxDQUFDLG9DQUFhLEdBQUcsWUFBWTtBQUM5RjtBQUNBLFVBQVUsZ0JBQWU7QUFDekIsR0FBRztBQUNIO0FBQ0EseUJBQVk7QUFDWixvRUFBNEIsZ0JBQWdCLENBQUMseUJBQVksQ0FBQyxFOzs7Ozs7Ozs7Ozs7Ozs7O0FDYjFEO0FBQ0EsaURBQWUsQ0FBQyxvR0FBb0csRTs7Ozs7OztBQ0RwRTtBQUNhO0FBQ3BCO0FBQ0Q7QUFDdUI7QUFDSztBQUN4QjtBQUNIO0FBQ007QUFDTjtBQUNQO0FBQUE7QUFBQTtBQUFBO0FBRWxDLElBQU11QixTQUFTLEdBQUdWLHVDQUFZLENBQUMsVUFBQVcsSUFBQTtFQUFBLElBQUdDLEtBQUssR0FBQUQsSUFBQSxDQUFMQyxLQUFLO0VBQUEsT0FBUTtJQUM3Q0MsV0FBVyxFQUFFO01BQ1hDLEtBQUssRUFBRTtJQUNULENBQUM7SUFDREMsU0FBUyxFQUFFO01BQ1RDLFFBQVEsRUFBRUosS0FBSyxDQUFDSyxnQkFBZ0IsR0FBRyxFQUFFO01BQ3JDQyxVQUFVLEVBQUU7SUFDZCxDQUFDO0lBQ0RDLFVBQVUsRUFBRTtNQUNWSCxRQUFRLEVBQUVKLEtBQUssQ0FBQ1EsZ0JBQWdCO01BQ2hDRixVQUFVLEVBQUVOLEtBQUssQ0FBQ1M7SUFDcEIsQ0FBQztJQUNEQyxJQUFJLEVBQUU7TUFDSk4sUUFBUSxFQUFFSixLQUFLLENBQUNXLGdCQUFnQjtNQUNoQ0wsVUFBVSxFQUFFTixLQUFLLENBQUNTLGdCQUFnQjtNQUNsQ0csVUFBVSxFQUFFO0lBQ2QsQ0FBQztJQUNEQyxhQUFhLEVBQUU7TUFDYkMsYUFBYSxFQUFFO0lBQ2pCO0VBQ0YsQ0FBQztBQUFBLENBQUMsQ0FBQztBQUVILElBQU1DLEtBQWUsR0FBRyxTQUFsQkEsS0FBZUEsQ0FBQSxFQUFTO0VBQUEsSUFBQUMsc0JBQUE7RUFDNUIsSUFBTUMsS0FBSyxHQUFHbkIsU0FBUyxDQUFDLENBQUM7RUFDekIsSUFBQW9CLFNBQUEsR0FBMENsQyxvQ0FBUSxDQUFDLGdCQUFnQixDQUFDO0lBQTVEbUMsWUFBWSxHQUFBRCxTQUFBLENBQVpDLFlBQVk7SUFBRUMsZUFBZSxHQUFBRixTQUFBLENBQWZFLGVBQWU7RUFFckMsSUFBTUMsYUFBYTtJQUFBLElBQUFDLEtBQUEsR0FBQUMsMEJBQUEsZUFBQUMsNEJBQUEsR0FBQUMsSUFBQSxDQUFHLFNBQUFDLFFBQU9DLFNBQWU7TUFBQSxJQUFBQyxxQkFBQTtNQUFBLElBQUFDLFFBQUE7TUFBQSxPQUFBTCw0QkFBQSxHQUFBTSxJQUFBLFVBQUFDLFNBQUFDLFFBQUE7UUFBQSxrQkFBQUEsUUFBQSxDQUFBQyxJQUFBLEdBQUFELFFBQUEsQ0FBQUUsSUFBQTtVQUFBO1lBQUFGLFFBQUEsQ0FBQUUsSUFBQTtZQUFBLE9BQ25CZixZQUFZLGFBQVpBLFlBQVksZ0JBQUFTLHFCQUFBLEdBQVpULFlBQVksQ0FBRUUsYUFBYSxjQUFBTyxxQkFBQSx1QkFBM0JBLHFCQUFBLENBQUFPLElBQUEsQ0FBQWhCLFlBQThCLENBQUM7VUFBQTtZQUFoRFUsUUFBUSxHQUFBRyxRQUFBLENBQUFJLElBQUE7WUFBQSxLQUNWUCxRQUFRO2NBQUFHLFFBQUEsQ0FBQUUsSUFBQTtjQUFBO1lBQUE7WUFBQUYsUUFBQSxDQUFBRSxJQUFBO1lBQUEsT0FDSmQsZUFBZSxDQUFDLFVBQUNpQixDQUFNO2NBQUEsT0FBQUMsdUJBQUEsQ0FBQUEsdUJBQUEsS0FBV0QsQ0FBQztnQkFBRUUsV0FBVyxFQUFFVjtjQUFRO1lBQUEsQ0FBRyxDQUFDO1VBQUE7WUFDcEVGLFNBQVMsYUFBVEEsU0FBUyxlQUFUQSxTQUFTLENBQUcsQ0FBQztVQUFDO1VBQUE7WUFBQSxPQUFBSyxRQUFBLENBQUFRLElBQUE7UUFBQTtNQUFBLEdBQUFkLE9BQUE7SUFBQSxDQUVqQjtJQUFBLGdCQU5LTCxhQUFhQSxDQUFBb0IsRUFBQTtNQUFBLE9BQUFuQixLQUFBLENBQUFvQixLQUFBLE9BQUFDLFNBQUE7SUFBQTtFQUFBLEdBTWxCO0VBRUQsSUFBTUMsWUFBWTtJQUFBLElBQUFDLEtBQUEsR0FBQXRCLDBCQUFBLGVBQUFDLDRCQUFBLEdBQUFDLElBQUEsQ0FBRyxTQUFBcUIsU0FBT0MsTUFBd0I7TUFBQSxJQUFBQyxpQkFBQSxFQUFBQyxTQUFBLEVBQUFDLFFBQUE7TUFBQSxPQUFBMUIsNEJBQUEsR0FBQU0sSUFBQSxVQUFBcUIsVUFBQUMsU0FBQTtRQUFBLGtCQUFBQSxTQUFBLENBQUFuQixJQUFBLEdBQUFtQixTQUFBLENBQUFsQixJQUFBO1VBQUE7WUFBQWtCLFNBQUEsQ0FBQW5CLElBQUE7WUFBQW1CLFNBQUEsQ0FBQWxCLElBQUE7WUFBQSxPQUd4QnhELHNCQUFLLENBQUE0RCx1QkFBQSxLQUFNUyxNQUFNLENBQUUsQ0FBQztVQUFBO1lBQXRDRSxTQUFTLEdBQUFHLFNBQUEsQ0FBQWhCLElBQUE7WUFDVGMsUUFBUSxHQUFHRCxTQUFTLGFBQVRBLFNBQVMsZ0JBQUFELGlCQUFBLEdBQVRDLFNBQVMsQ0FBRUksTUFBTSxjQUFBTCxpQkFBQSx1QkFBakJBLGlCQUFBLENBQW1CTSxJQUFJLEVBQ3hDO1lBQ0FDLFlBQVksQ0FBQ0MsT0FBTyxDQUFDLE9BQU8sRUFBRUMsSUFBSSxDQUFDQyxTQUFTLENBQUNULFNBQVMsYUFBVEEsU0FBUyx1QkFBVEEsU0FBUyxDQUFFSSxNQUFNLENBQUMsQ0FBQztZQUFDRCxTQUFBLENBQUFsQixJQUFBO1lBQUEsT0FDM0RkLGVBQWUsQ0FBQyxVQUFDaUIsQ0FBTTtjQUFBLE9BQUFDLHVCQUFBLENBQUFBLHVCQUFBLEtBQVdELENBQUM7Z0JBQUVFLFdBQVcsRUFBRVc7Y0FBUTtZQUFBLENBQUcsQ0FBQztVQUFBO1lBQ3BFUyxNQUFNLENBQUNDLFFBQVEsQ0FBQ0MsSUFBSSxHQUFHRixNQUFNLENBQUNDLFFBQVEsQ0FBQ0UsTUFBTTtZQUFDVixTQUFBLENBQUFsQixJQUFBO1lBQUE7VUFBQTtZQUFBa0IsU0FBQSxDQUFBbkIsSUFBQTtZQUFBbUIsU0FBQSxDQUFBVyxFQUFBLEdBQUFYLFNBQUE7WUFFOUNsRSx1QkFBTyxDQUFDOEUsS0FBSyxDQUFDLDBCQUEwQixDQUFDO1VBQUM7VUFBQTtZQUFBLE9BQUFaLFNBQUEsQ0FBQVosSUFBQTtRQUFBO01BQUEsR0FBQU0sUUFBQTtJQUFBLENBRTdDO0lBQUEsZ0JBWktGLFlBQVlBLENBQUFxQixHQUFBO01BQUEsT0FBQXBCLEtBQUEsQ0FBQUgsS0FBQSxPQUFBQyxTQUFBO0lBQUE7RUFBQSxHQVlqQjtFQUVEckQsbUJBQVMsQ0FBQyxZQUFNO0lBQUEsSUFBQTRFLHFCQUFBO0lBQ2QsSUFBSSxFQUFDL0MsWUFBWSxhQUFaQSxZQUFZLGdCQUFBK0MscUJBQUEsR0FBWi9DLFlBQVksQ0FBRW9CLFdBQVcsY0FBQTJCLHFCQUFBLGVBQXpCQSxxQkFBQSxDQUEyQkMsT0FBTyxHQUFFOUMsYUFBYSxDQUFDLENBQUM7SUFDeEQsSUFBTStDLFNBQVMsR0FBRyxJQUFJQyxHQUFHLENBQUNWLE1BQU0sQ0FBQ0MsUUFBUSxDQUFDQyxJQUFJLENBQUMsQ0FBQ1MsWUFBWTtJQUM1RDtJQUNBO0VBQ0YsQ0FBQyxFQUFFLENBQUNuRCxZQUFZLGFBQVpBLFlBQVksZ0JBQUFILHNCQUFBLEdBQVpHLFlBQVksQ0FBRW9CLFdBQVcsY0FBQXZCLHNCQUFBLHVCQUF6QkEsc0JBQUEsQ0FBMkJtRCxPQUFPLENBQUMsQ0FBQztFQUN4QyxJQUFNSSxnQkFBZ0IsR0FBR3BELFlBQVksYUFBWkEsWUFBWSx1QkFBWkEsWUFBWSxDQUFFcUQsZUFBZTtFQUN0RCxvQkFDRTdFLG9CQUFBLENBQUNSLGtCQUFHO0lBQUNzRixTQUFTLEVBQUVsRixZQUFNLENBQUNtRixTQUFVO0lBQUFDLFFBQUEsZ0JBQy9CaEYsb0JBQUEsQ0FBQ1Ysa0JBQUc7TUFBQzJGLEVBQUUsRUFBRSxFQUFHO01BQUNDLEVBQUUsRUFBRSxDQUFFO01BQUNKLFNBQVMsRUFBRWxGLFlBQU0sQ0FBQ3VGLElBQUs7TUFBQUgsUUFBQSxnQkFDekNsRixtQkFBQTtRQUNFd0IsS0FBSyxFQUFFO1VBQ0w4RCxZQUFZLEVBQUUsRUFBRTtVQUNoQkMsYUFBYSxFQUFFO1FBQ2pCLENBQUU7UUFBQUwsUUFBQSxlQUVGbEYsbUJBQUE7VUFBS3dGLEdBQUcsRUFBRXpHLDhCQUFhQTtRQUFDLENBQUU7TUFBQyxDQUN4QixDQUFDLGVBQ05pQixtQkFBQTtRQUFLZ0YsU0FBUyxFQUFFbEYsWUFBTSxDQUFDMkYsRUFBRztRQUFDRCxHQUFHLEVBQUUxRyx3QkFBUTtRQUFDNEcsR0FBRyxFQUFDO01BQUksQ0FBRSxDQUFDLGVBQ3BEMUYsbUJBQUE7UUFDRWdGLFNBQVMsRUFBRXhELEtBQUssQ0FBQ2hCLFdBQVk7UUFDN0JnQixLQUFLLEVBQUU7VUFDTDhELFlBQVksRUFBRSxFQUFFO1VBQ2hCQyxhQUFhLEVBQUU7UUFDakIsQ0FBRTtRQUFBTCxRQUFBLGVBRUZoRixvQkFBQSxDQUFDUixrQkFBRztVQUFBd0YsUUFBQSxnQkFDRmxGLG1CQUFBLENBQUNSLGtCQUFHO1lBQUNtRyxJQUFJLEVBQUUsRUFBRztZQUFBVCxRQUFBLGVBQ1poRixvQkFBQTtjQUFLOEUsU0FBUyxFQUFFeEQsS0FBSyxDQUFDSixhQUFjO2NBQUE4RCxRQUFBLGdCQUNsQ2xGLG1CQUFBO2dCQUFLZ0YsU0FBUyxFQUFFeEQsS0FBSyxDQUFDZCxTQUFVO2dCQUFBd0UsUUFBQSxFQUFDO2NBQVEsQ0FBSyxDQUFDLGVBQy9DbEYsbUJBQUE7Z0JBQ0VnRixTQUFTLEVBQUV4RCxLQUFLLENBQUNkLFNBQVU7Z0JBQzNCYyxLQUFLLEVBQUU7a0JBQ0xvRSxjQUFjLEVBQUU7Z0JBQ2xCLENBQUU7Z0JBQUFWLFFBQUEsRUFDSDtjQUVELENBQUssQ0FBQyxlQUNObEYsbUJBQUE7Z0JBQUtnRixTQUFTLEVBQUV4RCxLQUFLLENBQUNWLFVBQVc7Z0JBQUFvRSxRQUFBLEVBQUM7Y0FBaUIsQ0FBSyxDQUFDO1lBQUEsQ0FDdEQ7VUFBQyxDQUNILENBQUMsZUFDTmxGLG1CQUFBLENBQUNSLGtCQUFHO1lBQUNtRyxJQUFJLEVBQUUsQ0FBRTtZQUFBVCxRQUFBLGVBQ1hsRixtQkFBQTtjQUNFZ0YsU0FBUyxFQUFFeEQsS0FBSyxDQUFDUCxJQUFLO2NBQ3RCTyxLQUFLLEVBQUU7Z0JBQ0xxRSxTQUFTLEVBQUU7Y0FDYixDQUFFO2NBQUFYLFFBQUEsRUFDSDtZQUdELENBQUs7VUFBQyxDQUNILENBQUM7UUFBQSxDQUNIO01BQUMsQ0FDSCxDQUFDO0lBQUEsQ0FDSCxDQUFDLGVBQ05oRixvQkFBQSxDQUFDVixrQkFBRztNQUFDMkYsRUFBRSxFQUFFLEVBQUc7TUFBQ0MsRUFBRSxFQUFFLEVBQUc7TUFBQUYsUUFBQSxnQkFDbEJsRixtQkFBQTtRQUFLZ0YsU0FBUyxFQUFFbEYsWUFBTSxDQUFDZ0csT0FBUTtRQUFBWixRQUFBLGVBQzdCaEYsb0JBQUEsQ0FBQ2QsMEJBQVM7VUFDUjJHLElBQUksZUFBRS9GLG1CQUFBO1lBQUt3QixLQUFLLEVBQUU7Y0FBRXdFLEtBQUssRUFBRTtZQUFRLENBQUU7WUFBQ04sR0FBRyxFQUFDLE1BQU07WUFBQ0YsR0FBRyxFQUFDO1VBQWdCLENBQUUsQ0FBRTtVQUN6RVMsS0FBSyxlQUFFakcsbUJBQUE7WUFBS3dCLEtBQUssRUFBRTtjQUFFMEUsU0FBUyxFQUFFO1lBQU8sQ0FBRTtZQUFBaEIsUUFBQSxFQUFDO1VBQXNCLENBQUssQ0FBRTtVQUN2RWlCLFFBQVEsZUFDTm5HLG1CQUFBLENBQUFJLG9CQUFBLElBR0UsQ0FDSDtVQUNEZ0csYUFBYSxFQUFFO1lBQ2JDLFNBQVMsRUFBRTtVQUNiLENBQUU7VUFDRkMsUUFBUTtZQUFBLElBQUFDLEtBQUEsR0FBQXpFLDBCQUFBLGVBQUFDLDRCQUFBLEdBQUFDLElBQUEsQ0FBRSxTQUFBd0UsU0FBT2xELE1BQU07Y0FBQSxPQUFBdkIsNEJBQUEsR0FBQU0sSUFBQSxVQUFBb0UsVUFBQUMsU0FBQTtnQkFBQSxrQkFBQUEsU0FBQSxDQUFBbEUsSUFBQSxHQUFBa0UsU0FBQSxDQUFBakUsSUFBQTtrQkFBQTtvQkFBQWlFLFNBQUEsQ0FBQWpFLElBQUE7b0JBQUEsT0FDZlUsWUFBWSxDQUFDRyxNQUEwQixDQUFDO2tCQUFBO2tCQUFBO29CQUFBLE9BQUFvRCxTQUFBLENBQUEzRCxJQUFBO2dCQUFBO2NBQUEsR0FBQXlELFFBQUE7WUFBQSxDQUMvQztZQUFBLGlCQUFBRyxHQUFBO2NBQUEsT0FBQUosS0FBQSxDQUFBdEQsS0FBQSxPQUFBQyxTQUFBO1lBQUE7VUFBQSxJQUFDO1VBQ0YwRCxTQUFTLEVBQUU7WUFDVEMsWUFBWSxFQUFFO2NBQ1pDLFVBQVUsRUFBRWhDLGdCQUFnQixHQUFHLGNBQWMsR0FBRztZQUNsRDtVQUNGLENBQUU7VUFBQUksUUFBQSxnQkFFRmxGLG1CQUFBLENBQUNYLG1CQUFXO1lBQ1YwSCxJQUFJLEVBQUMsS0FBSztZQUNWQyxVQUFVLEVBQUU7Y0FDVkMsSUFBSSxFQUFFLE9BQU87Y0FDYkMsTUFBTSxlQUFFbEgsbUJBQUEsQ0FBQ2IsMkJBQVk7Z0JBQUM2RixTQUFTLEVBQUVsRixZQUFNLENBQUNxSDtjQUFXLENBQUU7WUFDdkQsQ0FBRTtZQUNGQyxXQUFXLEVBQUUsT0FBUTtZQUNyQkMsS0FBSyxFQUFFLENBQ0w7Y0FDRUMsUUFBUSxFQUFFLElBQUk7Y0FDZDdILE9BQU8sRUFBRTtZQUNYLENBQUM7VUFDRCxDQUNILENBQUMsZUFDRk8sbUJBQUEsQ0FBQ1gsbUJBQVcsQ0FBQ2tJLFFBQVE7WUFDbkJSLElBQUksRUFBQyxLQUFLO1lBQ1ZDLFVBQVUsRUFBRTtjQUNWQyxJQUFJLEVBQUUsT0FBTztjQUNiQyxNQUFNLGVBQUVsSCxtQkFBQSxDQUFDZCxrQkFBWTtnQkFBQzhGLFNBQVMsRUFBRWxGLFlBQU0sQ0FBQ3FIO2NBQVcsQ0FBRTtZQUN2RCxDQUFFO1lBQ0ZDLFdBQVcsRUFBRSxVQUFXO1lBQ3hCQyxLQUFLLEVBQUUsQ0FDTDtjQUNFQyxRQUFRLEVBQUUsSUFBSTtjQUNkN0gsT0FBTyxFQUFFO1lBQ1gsQ0FBQztVQUNELENBQ0gsQ0FBQyxlQUVGTyxtQkFBQTtZQUNFd0IsS0FBSyxFQUFFO2NBQ0xnRyxZQUFZLEVBQUU7WUFDaEIsQ0FBRTtZQUFBdEMsUUFBQSxlQUVGbEYsbUJBQUE7Y0FDRXdCLEtBQUssRUFBRTtnQkFDTCxTQUFPO2NBQ1QsQ0FBRTtjQUFBMEQsUUFBQSxlQUVGbEYsbUJBQUEsQ0FBQ1YsNEJBQUk7Z0JBQUNtSSxFQUFFLEVBQUUsdUJBQXdCO2dCQUFBdkMsUUFBQSxFQUFDO2NBQWtCLENBQU07WUFBQyxDQUN6RDtVQUFDLENBQ0gsQ0FBQztRQUFBLENBQ0c7TUFBQyxDQUNULENBQUMsZUFDTmxGLG1CQUFBLENBQUNoQixxQkFBTSxJQUFFLENBQUM7SUFBQSxDQUNQLENBQUM7RUFBQSxDQUNILENBQUM7QUFFVixDQUFDO0FBRUQsK0NBQWVzQyxLQUFLIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvQGFudC1kZXNpZ24vaWNvbnMtc3ZnL2VzL2Fzbi9Mb2NrT3V0bGluZWQuanM/ZTQwYyIsIndlYnBhY2s6Ly9hbnQtZGVzaWduLXByby8uL25vZGVfbW9kdWxlcy9AYW50LWRlc2lnbi9pY29ucy9lcy9pY29ucy9Mb2NrT3V0bGluZWQuanM/MWI3MCIsIndlYnBhY2s6Ly9hbnQtZGVzaWduLXByby8uL3NyYy9wYWdlcy9Vc2VyL0xvZ2luL2luZGV4Lmxlc3M/NDE1NyIsIndlYnBhY2s6Ly9hbnQtZGVzaWduLXByby8uL3NyYy9wYWdlcy9Vc2VyL0xvZ2luL2luZGV4LnRzeD9kNDViIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIFRoaXMgaWNvbiBmaWxlIGlzIGdlbmVyYXRlZCBhdXRvbWF0aWNhbGx5LlxudmFyIExvY2tPdXRsaW5lZCA9IHsgXCJpY29uXCI6IHsgXCJ0YWdcIjogXCJzdmdcIiwgXCJhdHRyc1wiOiB7IFwidmlld0JveFwiOiBcIjY0IDY0IDg5NiA4OTZcIiwgXCJmb2N1c2FibGVcIjogXCJmYWxzZVwiIH0sIFwiY2hpbGRyZW5cIjogW3sgXCJ0YWdcIjogXCJwYXRoXCIsIFwiYXR0cnNcIjogeyBcImRcIjogXCJNODMyIDQ2NGgtNjhWMjQwYzAtNzAuNy01Ny4zLTEyOC0xMjgtMTI4SDM4OGMtNzAuNyAwLTEyOCA1Ny4zLTEyOCAxMjh2MjI0aC02OGMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2Mzg0YzAgMTcuNyAxNC4zIDMyIDMyIDMyaDY0MGMxNy43IDAgMzItMTQuMyAzMi0zMlY0OTZjMC0xNy43LTE0LjMtMzItMzItMzJ6TTMzMiAyNDBjMC0zMC45IDI1LjEtNTYgNTYtNTZoMjQ4YzMwLjkgMCA1NiAyNS4xIDU2IDU2djIyNEgzMzJWMjQwem00NjAgNjAwSDIzMlY1MzZoNTYwdjMwNHpNNDg0IDcwMXY1M2MwIDQuNCAzLjYgOCA4IDhoNDBjNC40IDAgOC0zLjYgOC04di01M2E0OC4wMSA0OC4wMSAwIDEwLTU2IDB6XCIgfSB9XSB9LCBcIm5hbWVcIjogXCJsb2NrXCIsIFwidGhlbWVcIjogXCJvdXRsaW5lZFwiIH07XG5leHBvcnQgZGVmYXVsdCBMb2NrT3V0bGluZWQ7XG4iLCJpbXBvcnQgX29iamVjdFNwcmVhZCBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vb2JqZWN0U3ByZWFkMlwiO1xuLy8gR0VORVJBVEUgQlkgLi9zY3JpcHRzL2dlbmVyYXRlLnRzXG4vLyBET04gTk9UIEVESVQgSVQgTUFOVUFMTFlcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCBMb2NrT3V0bGluZWRTdmcgZnJvbSBcIkBhbnQtZGVzaWduL2ljb25zLXN2Zy9lcy9hc24vTG9ja091dGxpbmVkXCI7XG5pbXBvcnQgQW50ZEljb24gZnJvbSAnLi4vY29tcG9uZW50cy9BbnRkSWNvbic7XG52YXIgTG9ja091dGxpbmVkID0gZnVuY3Rpb24gTG9ja091dGxpbmVkKHByb3BzLCByZWYpIHtcbiAgcmV0dXJuIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KEFudGRJY29uLCBfb2JqZWN0U3ByZWFkKF9vYmplY3RTcHJlYWQoe30sIHByb3BzKSwge30sIHtcbiAgICByZWY6IHJlZixcbiAgICBpY29uOiBMb2NrT3V0bGluZWRTdmdcbiAgfSkpO1xufTtcbkxvY2tPdXRsaW5lZC5kaXNwbGF5TmFtZSA9ICdMb2NrT3V0bGluZWQnO1xuZXhwb3J0IGRlZmF1bHQgLyojX19QVVJFX18qL1JlYWN0LmZvcndhcmRSZWYoTG9ja091dGxpbmVkKTsiLCIvLyBleHRyYWN0ZWQgYnkgbWluaS1jc3MtZXh0cmFjdC1wbHVnaW5cbmV4cG9ydCBkZWZhdWx0IHtcImNvbnRhaW5lclwiOlwiY29udGFpbmVyX19fUkVRQXlcIixcInNpZGVcIjpcInNpZGVfX19UaUhOYVwiLFwiYmdcIjpcImJnX19fbU81Q0xcIixcImNvbnRlbnRcIjpcImNvbnRlbnRfX19Ja1V4MVwifTsiLCJpbXBvcnQgbG9naW5CZyBmcm9tICdAL2Fzc2V0cy9pbWcvbG9naW4tYmcucG5nJztcclxuaW1wb3J0IGxvZ29UZXh0V2hpdGUgZnJvbSAnQC9hc3NldHMvaW1nL2xvZ28tdGV4dC13aGl0ZS5zdmcnO1xyXG5pbXBvcnQgRm9vdGVyIGZyb20gJ0AvY29tcG9uZW50cy9Gb290ZXInO1xyXG5pbXBvcnQgeyBsb2dpbiB9IGZyb20gJ0Avc2VydmljZXMvYXV0aCc7XHJcbmltcG9ydCB7IExvY2tPdXRsaW5lZCwgVXNlck91dGxpbmVkIH0gZnJvbSAnQGFudC1kZXNpZ24vaWNvbnMnO1xyXG5pbXBvcnQgeyBMb2dpbkZvcm0sIFByb0Zvcm1UZXh0IH0gZnJvbSAnQGFudC1kZXNpZ24vcHJvLWNvbXBvbmVudHMnO1xyXG5pbXBvcnQgeyBMaW5rLCB1c2VNb2RlbCB9IGZyb20gJ0B1bWlqcy9tYXgnO1xyXG5pbXBvcnQgeyBDb2wsIG1lc3NhZ2UsIFJvdyB9IGZyb20gJ2FudGQnO1xyXG5pbXBvcnQgeyBjcmVhdGVTdHlsZXMgfSBmcm9tICdhbnRkLXVzZS1zdHlsZXMnO1xyXG5pbXBvcnQgUmVhY3QsIHsgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgc3R5bGVzIGZyb20gJy4vaW5kZXgubGVzcyc7XHJcblxyXG5jb25zdCB1c2VTdHlsZXMgPSBjcmVhdGVTdHlsZXMoKHsgdG9rZW4gfSkgPT4gKHtcclxuICBzbGlkZUZvb3Rlcjoge1xyXG4gICAgY29sb3I6ICd3aGl0ZScsXHJcbiAgfSxcclxuICB0ZXh0TGFyZ2U6IHtcclxuICAgIGZvbnRTaXplOiB0b2tlbi5mb250U2l6ZUhlYWRpbmcxICsgMTAsXHJcbiAgICBmb250V2VpZ2h0OiA3MDAsXHJcbiAgfSxcclxuICB0ZXh0TWVkaXVtOiB7XHJcbiAgICBmb250U2l6ZTogdG9rZW4uZm9udFNpemVIZWFkaW5nMyxcclxuICAgIGZvbnRXZWlnaHQ6IHRva2VuLmZvbnRXZWlnaHRTdHJvbmcsXHJcbiAgfSxcclxuICB0ZXh0OiB7XHJcbiAgICBmb250U2l6ZTogdG9rZW4uZm9udFNpemVIZWFkaW5nNCxcclxuICAgIGZvbnRXZWlnaHQ6IHRva2VuLmZvbnRXZWlnaHRTdHJvbmcsXHJcbiAgICBsaW5lSGVpZ2h0OiAxLjUsXHJcbiAgfSxcclxuICB0ZXh0VXBwZXJjYXNlOiB7XHJcbiAgICB0ZXh0VHJhbnNmb3JtOiAndXBwZXJjYXNlJyxcclxuICB9LFxyXG59KSk7XHJcblxyXG5jb25zdCBMb2dpbjogUmVhY3QuRkMgPSAoKSA9PiB7XHJcbiAgY29uc3Qgc3R5bGUgPSB1c2VTdHlsZXMoKTtcclxuICBjb25zdCB7IGluaXRpYWxTdGF0ZSwgc2V0SW5pdGlhbFN0YXRlIH0gPSB1c2VNb2RlbCgnQEBpbml0aWFsU3RhdGUnKTtcclxuXHJcbiAgY29uc3QgZmV0Y2hVc2VySW5mbyA9IGFzeW5jIChvblN1Y2Nlc3M/OiBhbnkpID0+IHtcclxuICAgIGNvbnN0IHVzZXJJbmZvID0gYXdhaXQgaW5pdGlhbFN0YXRlPy5mZXRjaFVzZXJJbmZvPy4oKTtcclxuICAgIGlmICh1c2VySW5mbykge1xyXG4gICAgICBhd2FpdCBzZXRJbml0aWFsU3RhdGUoKHM6IGFueSkgPT4gKHsgLi4ucywgY3VycmVudFVzZXI6IHVzZXJJbmZvIH0pKTtcclxuICAgICAgb25TdWNjZXNzPy4oKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVTdWJtaXQgPSBhc3luYyAodmFsdWVzOiBBUEkuUmVxdWVzdExvZ2luKSA9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICAvLyDnmbvlvZVcclxuICAgICAgY29uc3QgbG9naW5SZXNwID0gYXdhaXQgbG9naW4oeyAuLi52YWx1ZXMgfSk7XHJcbiAgICAgIGNvbnN0IHVzZXJkYXRhID0gbG9naW5SZXNwPy5yZXN1bHQ/LnVzZXI7XHJcbiAgICAgIC8vIGNvbnNvbGUubG9nKHVzZXJkYXRhLCBcInVzZXJkYXRhXCIpO1xyXG4gICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgndG9rZW4nLCBKU09OLnN0cmluZ2lmeShsb2dpblJlc3A/LnJlc3VsdCkpO1xyXG4gICAgICBhd2FpdCBzZXRJbml0aWFsU3RhdGUoKHM6IGFueSkgPT4gKHsgLi4ucywgY3VycmVudFVzZXI6IHVzZXJkYXRhIH0pKTtcclxuICAgICAgd2luZG93LmxvY2F0aW9uLmhyZWYgPSB3aW5kb3cubG9jYXRpb24ub3JpZ2luO1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgbWVzc2FnZS5lcnJvcignTG9naW4gRmFpbGVkLCBUcnkgQWdhaW4hJyk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGlmICghaW5pdGlhbFN0YXRlPy5jdXJyZW50VXNlcj8udXNlcl9pZCkgZmV0Y2hVc2VySW5mbygpO1xyXG4gICAgY29uc3QgdXJsUGFyYW1zID0gbmV3IFVSTCh3aW5kb3cubG9jYXRpb24uaHJlZikuc2VhcmNoUGFyYW1zO1xyXG4gICAgLy8gaGlzdG9yeS5wdXNoKHVybFBhcmFtcy5nZXQoJ3JlZGlyZWN0JykgfHwgJy8nKTtcclxuICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSByZWFjdC1ob29rcy9leGhhdXN0aXZlLWRlcHNcclxuICB9LCBbaW5pdGlhbFN0YXRlPy5jdXJyZW50VXNlcj8udXNlcl9pZF0pO1xyXG4gIGNvbnN0IG1haW50ZXJuYW5jZU1vZGUgPSBpbml0aWFsU3RhdGU/Lm1haW50ZW5hbmNlTW9kZTtcclxuICByZXR1cm4gKFxyXG4gICAgPFJvdyBjbGFzc05hbWU9e3N0eWxlcy5jb250YWluZXJ9PlxyXG4gICAgICA8Q29sIG1kPXsxMn0geHM9ezB9IGNsYXNzTmFtZT17c3R5bGVzLnNpZGV9PlxyXG4gICAgICAgIDxkaXZcclxuICAgICAgICAgIHN0eWxlPXt7XHJcbiAgICAgICAgICAgIHBhZGRpbmdCbG9jazogNDUsXHJcbiAgICAgICAgICAgIHBhZGRpbmdJbmxpbmU6IDMwLFxyXG4gICAgICAgICAgfX1cclxuICAgICAgICA+XHJcbiAgICAgICAgICA8aW1nIHNyYz17bG9nb1RleHRXaGl0ZX0gLz5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8aW1nIGNsYXNzTmFtZT17c3R5bGVzLmJnfSBzcmM9e2xvZ2luQmd9IGFsdD1cImJnXCIgLz5cclxuICAgICAgICA8ZGl2XHJcbiAgICAgICAgICBjbGFzc05hbWU9e3N0eWxlLnNsaWRlRm9vdGVyfVxyXG4gICAgICAgICAgc3R5bGU9e3tcclxuICAgICAgICAgICAgcGFkZGluZ0Jsb2NrOiA0NSxcclxuICAgICAgICAgICAgcGFkZGluZ0lubGluZTogMzAsXHJcbiAgICAgICAgICB9fVxyXG4gICAgICAgID5cclxuICAgICAgICAgIDxSb3c+XHJcbiAgICAgICAgICAgIDxDb2wgc3Bhbj17MTN9PlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtzdHlsZS50ZXh0VXBwZXJjYXNlfT5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtzdHlsZS50ZXh0TGFyZ2V9PlF14bqjbiBsw70gPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17c3R5bGUudGV4dExhcmdlfVxyXG4gICAgICAgICAgICAgICAgICBzdHlsZT17e1xyXG4gICAgICAgICAgICAgICAgICAgIG1hcmdpbkJsb2NrRW5kOiAxMCxcclxuICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgdGjDtG5nIG1pbmhcclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e3N0eWxlLnRleHRNZWRpdW19PlRyb25nIG7DtG5nIG5naGnhu4dwPC9kaXY+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDwvQ29sPlxyXG4gICAgICAgICAgICA8Q29sIHNwYW49ezl9PlxyXG4gICAgICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17c3R5bGUudGV4dH1cclxuICAgICAgICAgICAgICAgIHN0eWxlPXt7XHJcbiAgICAgICAgICAgICAgICAgIHRleHRBbGlnbjogJ3JpZ2h0JyxcclxuICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgVuG7m2kgVklJUyBi4bqhbiBjw7MgdGjhu4MgZOG7hSBkw6BuZyDEkWnhu4F1IGtoaeG7g24sIG5oYW5oIGNow7NuZywgdGnhur90IGtp4buHbSB0aOG7nWkgZ2lhbiB2w6AgY8O0bmdcclxuICAgICAgICAgICAgICAgIHPhu6ljLiBRdeG6o24gbMO9IGPDtG5nIHZp4buHYyBk4buFIGTDoG5nIHbDoCBoaeG7h3UgcXXhuqMuXHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDwvQ29sPlxyXG4gICAgICAgICAgPC9Sb3c+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvQ29sPlxyXG4gICAgICA8Q29sIG1kPXsxMn0geHM9ezI0fT5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLmNvbnRlbnR9PlxyXG4gICAgICAgICAgPExvZ2luRm9ybVxyXG4gICAgICAgICAgICBsb2dvPXs8aW1nIHN0eWxlPXt7IHdpZHRoOiAnMTUwcHgnIH19IGFsdD1cImxvZ29cIiBzcmM9XCIvdmlpc19sb2dvLnN2Z1wiIC8+fVxyXG4gICAgICAgICAgICB0aXRsZT17PGRpdiBzdHlsZT17eyBtYXJnaW5Ub3A6ICcyMHB4JyB9fT7EkMSCTkcgTkjhuqxQIFbDgE8gSOG7hiBUSOG7kE5HPC9kaXY+fVxyXG4gICAgICAgICAgICBzdWJUaXRsZT17XHJcbiAgICAgICAgICAgICAgPD5cclxuICAgICAgICAgICAgICAgIHsvKiA8cD5Qcm9mZXNzaW9uYWwgRW5nbGlzaCB0cmFpbmluZyBwYXJ0bmVyIGZvciBtYW55IGVudGVycHJpc2VzIGFuZCBsYXJnZTwvcD5cclxuICAgICAgICAgICAgICAgIDxwPm11bHRpbmF0aW9uYWwgY29ycG9yYXRpb25zIGluIFZpZXRuYW08L3A+ICovfVxyXG4gICAgICAgICAgICAgIDwvPlxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIGluaXRpYWxWYWx1ZXM9e3tcclxuICAgICAgICAgICAgICBhdXRvTG9naW46IHRydWUsXHJcbiAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgIG9uRmluaXNoPXthc3luYyAodmFsdWVzKSA9PiB7XHJcbiAgICAgICAgICAgICAgYXdhaXQgaGFuZGxlU3VibWl0KHZhbHVlcyBhcyBBUEkuUmVxdWVzdExvZ2luKTtcclxuICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgc3VibWl0dGVyPXt7XHJcbiAgICAgICAgICAgICAgc2VhcmNoQ29uZmlnOiB7XHJcbiAgICAgICAgICAgICAgICBzdWJtaXRUZXh0OiBtYWludGVybmFuY2VNb2RlID8gJ8SQYW5nIGLhuqNvIHRyw6wnIDogJ8SQxINuZyBuaOG6rXAnLFxyXG4gICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIH19XHJcbiAgICAgICAgICA+XHJcbiAgICAgICAgICAgIDxQcm9Gb3JtVGV4dFxyXG4gICAgICAgICAgICAgIG5hbWU9XCJ1c3JcIlxyXG4gICAgICAgICAgICAgIGZpZWxkUHJvcHM9e3tcclxuICAgICAgICAgICAgICAgIHNpemU6ICdsYXJnZScsXHJcbiAgICAgICAgICAgICAgICBwcmVmaXg6IDxVc2VyT3V0bGluZWQgY2xhc3NOYW1lPXtzdHlsZXMucHJlZml4SWNvbn0gLz4sXHJcbiAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICBwbGFjZWhvbGRlcj17J0VtYWlsJ31cclxuICAgICAgICAgICAgICBydWxlcz17W1xyXG4gICAgICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSxcclxuICAgICAgICAgICAgICAgICAgbWVzc2FnZTogJ2ZpZWxkIHJlcXVpcmVkJyxcclxuICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgXX1cclxuICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgPFByb0Zvcm1UZXh0LlBhc3N3b3JkXHJcbiAgICAgICAgICAgICAgbmFtZT1cInB3ZFwiXHJcbiAgICAgICAgICAgICAgZmllbGRQcm9wcz17e1xyXG4gICAgICAgICAgICAgICAgc2l6ZTogJ2xhcmdlJyxcclxuICAgICAgICAgICAgICAgIHByZWZpeDogPExvY2tPdXRsaW5lZCBjbGFzc05hbWU9e3N0eWxlcy5wcmVmaXhJY29ufSAvPixcclxuICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgIHBsYWNlaG9sZGVyPXsnTeG6rXQga2jhuql1J31cclxuICAgICAgICAgICAgICBydWxlcz17W1xyXG4gICAgICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSxcclxuICAgICAgICAgICAgICAgICAgbWVzc2FnZTogJ2ZpZWxkIHJlcXVpcmVkJyxcclxuICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgXX1cclxuICAgICAgICAgICAgLz5cclxuXHJcbiAgICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgICBzdHlsZT17e1xyXG4gICAgICAgICAgICAgICAgbWFyZ2luQm90dG9tOiAyNCxcclxuICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgc3R5bGU9e3tcclxuICAgICAgICAgICAgICAgICAgZmxvYXQ6ICdyaWdodCcsXHJcbiAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIDxMaW5rIHRvPXsnL3VzZXIvZm9yZ290LXBhc3N3b3JkJ30+QuG6oW4gcXXDqm4gbeG6rXQga2jhuql1PzwvTGluaz5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L0xvZ2luRm9ybT5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8Rm9vdGVyIC8+XHJcbiAgICAgIDwvQ29sPlxyXG4gICAgPC9Sb3c+XHJcbiAgKTtcclxufTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IExvZ2luO1xyXG4iXSwibmFtZXMiOlsibG9naW5CZyIsImxvZ29UZXh0V2hpdGUiLCJGb290ZXIiLCJsb2dpbiIsIkxvY2tPdXRsaW5lZCIsIlVzZXJPdXRsaW5lZCIsIkxvZ2luRm9ybSIsIlByb0Zvcm1UZXh0IiwiTGluayIsInVzZU1vZGVsIiwiQ29sIiwibWVzc2FnZSIsIlJvdyIsImNyZWF0ZVN0eWxlcyIsIlJlYWN0IiwidXNlRWZmZWN0Iiwic3R5bGVzIiwianN4IiwiX2pzeCIsImpzeHMiLCJfanN4cyIsIkZyYWdtZW50IiwiX0ZyYWdtZW50IiwidXNlU3R5bGVzIiwiX3JlZiIsInRva2VuIiwic2xpZGVGb290ZXIiLCJjb2xvciIsInRleHRMYXJnZSIsImZvbnRTaXplIiwiZm9udFNpemVIZWFkaW5nMSIsImZvbnRXZWlnaHQiLCJ0ZXh0TWVkaXVtIiwiZm9udFNpemVIZWFkaW5nMyIsImZvbnRXZWlnaHRTdHJvbmciLCJ0ZXh0IiwiZm9udFNpemVIZWFkaW5nNCIsImxpbmVIZWlnaHQiLCJ0ZXh0VXBwZXJjYXNlIiwidGV4dFRyYW5zZm9ybSIsIkxvZ2luIiwiX2luaXRpYWxTdGF0ZSRjdXJyZW50MiIsInN0eWxlIiwiX3VzZU1vZGVsIiwiaW5pdGlhbFN0YXRlIiwic2V0SW5pdGlhbFN0YXRlIiwiZmV0Y2hVc2VySW5mbyIsIl9yZWYyIiwiX2FzeW5jVG9HZW5lcmF0b3IiLCJfcmVnZW5lcmF0b3JSdW50aW1lIiwibWFyayIsIl9jYWxsZWUiLCJvblN1Y2Nlc3MiLCJfaW5pdGlhbFN0YXRlJGZldGNoVXMiLCJ1c2VySW5mbyIsIndyYXAiLCJfY2FsbGVlJCIsIl9jb250ZXh0IiwicHJldiIsIm5leHQiLCJjYWxsIiwic2VudCIsInMiLCJfb2JqZWN0U3ByZWFkIiwiY3VycmVudFVzZXIiLCJzdG9wIiwiX3giLCJhcHBseSIsImFyZ3VtZW50cyIsImhhbmRsZVN1Ym1pdCIsIl9yZWYzIiwiX2NhbGxlZTIiLCJ2YWx1ZXMiLCJfbG9naW5SZXNwJHJlc3VsdCIsImxvZ2luUmVzcCIsInVzZXJkYXRhIiwiX2NhbGxlZTIkIiwiX2NvbnRleHQyIiwicmVzdWx0IiwidXNlciIsImxvY2FsU3RvcmFnZSIsInNldEl0ZW0iLCJKU09OIiwic3RyaW5naWZ5Iiwid2luZG93IiwibG9jYXRpb24iLCJocmVmIiwib3JpZ2luIiwidDAiLCJlcnJvciIsIl94MiIsIl9pbml0aWFsU3RhdGUkY3VycmVudCIsInVzZXJfaWQiLCJ1cmxQYXJhbXMiLCJVUkwiLCJzZWFyY2hQYXJhbXMiLCJtYWludGVybmFuY2VNb2RlIiwibWFpbnRlbmFuY2VNb2RlIiwiY2xhc3NOYW1lIiwiY29udGFpbmVyIiwiY2hpbGRyZW4iLCJtZCIsInhzIiwic2lkZSIsInBhZGRpbmdCbG9jayIsInBhZGRpbmdJbmxpbmUiLCJzcmMiLCJiZyIsImFsdCIsInNwYW4iLCJtYXJnaW5CbG9ja0VuZCIsInRleHRBbGlnbiIsImNvbnRlbnQiLCJsb2dvIiwid2lkdGgiLCJ0aXRsZSIsIm1hcmdpblRvcCIsInN1YlRpdGxlIiwiaW5pdGlhbFZhbHVlcyIsImF1dG9Mb2dpbiIsIm9uRmluaXNoIiwiX3JlZjQiLCJfY2FsbGVlMyIsIl9jYWxsZWUzJCIsIl9jb250ZXh0MyIsIl94MyIsInN1Ym1pdHRlciIsInNlYXJjaENvbmZpZyIsInN1Ym1pdFRleHQiLCJuYW1lIiwiZmllbGRQcm9wcyIsInNpemUiLCJwcmVmaXgiLCJwcmVmaXhJY29uIiwicGxhY2Vob2xkZXIiLCJydWxlcyIsInJlcXVpcmVkIiwiUGFzc3dvcmQiLCJtYXJnaW5Cb3R0b20iLCJ0byJdLCJzb3VyY2VSb290IjoiIn0=
//# sourceURL=webpack-internal:///27050
`)}}]);
