"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[3739],{44688:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Yk: function() { return /* binding */ DescriptionsSkeleton; },
/* harmony export */   hM: function() { return /* binding */ TableSkeleton; }
/* harmony export */ });
/* unused harmony export TableItemSkeleton */
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(99559);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(75302);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(76216);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(67294);
/* harmony import */ var _List__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(56517);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(85893);






var MediaQueryKeyEnum = {
  xs: 1,
  sm: 2,
  md: 3,
  lg: 3,
  xl: 3,
  xxl: 4
};
var DescriptionsLargeItemSkeleton = function DescriptionsLargeItemSkeleton(_ref) {
  var active = _ref.active;
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)("div", {
    style: {
      marginBlockStart: 32
    },
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z.Button, {
      active: active,
      size: "small",
      style: {
        width: 100,
        marginBlockEnd: 16
      }
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)("div", {
      style: {
        width: '100%',
        justifyContent: 'space-between',
        display: 'flex'
      },
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)("div", {
        style: {
          flex: 1,
          marginInlineEnd: 24,
          maxWidth: 300
        },
        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z, {
          active: active,
          paragraph: false,
          title: {
            style: {
              marginBlockStart: 0
            }
          }
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z, {
          active: active,
          paragraph: false,
          title: {
            style: {
              marginBlockStart: 8
            }
          }
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z, {
          active: active,
          paragraph: false,
          title: {
            style: {
              marginBlockStart: 8
            }
          }
        })]
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)("div", {
        style: {
          flex: 1,
          alignItems: 'center',
          justifyContent: 'center'
        },
        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)("div", {
          style: {
            maxWidth: 300,
            margin: 'auto'
          },
          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z, {
            active: active,
            paragraph: false,
            title: {
              style: {
                marginBlockStart: 0
              }
            }
          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z, {
            active: active,
            paragraph: false,
            title: {
              style: {
                marginBlockStart: 8
              }
            }
          })]
        })
      })]
    })]
  });
};
var DescriptionsItemSkeleton = function DescriptionsItemSkeleton(_ref2) {
  var size = _ref2.size,
    active = _ref2.active;
  var defaultCol = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function () {
    return {
      lg: true,
      md: true,
      sm: false,
      xl: false,
      xs: false,
      xxl: false
    };
  }, []);
  var col = antd__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .ZP.useBreakpoint() || defaultCol;
  var colSize = Object.keys(col).filter(function (key) {
    return col[key] === true;
  })[0] || 'md';
  var arraySize = size === undefined ? MediaQueryKeyEnum[colSize] || 3 : size;
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)("div", {
    style: {
      width: '100%',
      justifyContent: 'space-between',
      display: 'flex'
    },
    children: new Array(arraySize).fill(null).map(function (_, index) {
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)("div", {
        style: {
          flex: 1,
          paddingInlineStart: index === 0 ? 0 : 24,
          paddingInlineEnd: index === arraySize - 1 ? 0 : 24
        },
        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z, {
          active: active,
          paragraph: false,
          title: {
            style: {
              marginBlockStart: 0
            }
          }
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z, {
          active: active,
          paragraph: false,
          title: {
            style: {
              marginBlockStart: 8
            }
          }
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z, {
          active: active,
          paragraph: false,
          title: {
            style: {
              marginBlockStart: 8
            }
          }
        })]
      }, index);
    })
  });
};

/**
 * Table \u7684\u5B50\u9879\u76EE\u9AA8\u67B6\u5C4F
 *
 * @param param0
 */
var TableItemSkeleton = function TableItemSkeleton(_ref3) {
  var active = _ref3.active,
    _ref3$header = _ref3.header,
    header = _ref3$header === void 0 ? false : _ref3$header;
  var defaultCol = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function () {
    return {
      lg: true,
      md: true,
      sm: false,
      xl: false,
      xs: false,
      xxl: false
    };
  }, []);
  var col = antd__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .ZP.useBreakpoint() || defaultCol;
  var colSize = Object.keys(col).filter(function (key) {
    return col[key] === true;
  })[0] || 'md';
  var arraySize = MediaQueryKeyEnum[colSize] || 3;
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)("div", {
      style: {
        display: 'flex',
        background: header ? 'rgba(0,0,0,0.02)' : 'none',
        padding: '24px 8px'
      },
      children: [new Array(arraySize).fill(null).map(function (_, index) {
        return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)("div", {
          style: {
            flex: 1,
            paddingInlineStart: header && index === 0 ? 0 : 20,
            paddingInlineEnd: 32
          },
          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z, {
            active: active,
            paragraph: false,
            title: {
              style: {
                margin: 0,
                height: 24,
                width: header ? '75px' : '100%'
              }
            }
          })
        }, index);
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)("div", {
        style: {
          flex: 3,
          paddingInlineStart: 32
        },
        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z, {
          active: active,
          paragraph: false,
          title: {
            style: {
              margin: 0,
              height: 24,
              width: header ? '75px' : '100%'
            }
          }
        })
      })]
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_List__WEBPACK_IMPORTED_MODULE_4__/* .Line */ .x1, {
      padding: "0px 0px"
    })]
  });
};

/**
 * Table \u9AA8\u67B6\u5C4F
 *
 * @param param0
 */
var TableSkeleton = function TableSkeleton(_ref4) {
  var active = _ref4.active,
    _ref4$size = _ref4.size,
    size = _ref4$size === void 0 ? 4 : _ref4$size;
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z, {
    bordered: false,
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z.Button, {
      active: active,
      size: "small",
      style: {
        width: 100,
        marginBlockEnd: 16
      }
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TableItemSkeleton, {
      header: true,
      active: active
    }), new Array(size).fill(null).map(function (_, index) {
      return (
        /*#__PURE__*/
        // eslint-disable-next-line react/no-array-index-key
        (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TableItemSkeleton, {
          active: active
        }, index)
      );
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)("div", {
      style: {
        display: 'flex',
        justifyContent: 'flex-end',
        paddingBlockStart: 16
      },
      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z, {
        active: active,
        paragraph: false,
        title: {
          style: {
            margin: 0,
            height: 32,
            float: 'right',
            maxWidth: '630px'
          }
        }
      })
    })]
  });
};
var DescriptionsSkeleton = function DescriptionsSkeleton(_ref5) {
  var active = _ref5.active;
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z, {
    bordered: false,
    style: {
      borderStartEndRadius: 0,
      borderTopLeftRadius: 0
    },
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z.Button, {
      active: active,
      size: "small",
      style: {
        width: 100,
        marginBlockEnd: 16
      }
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DescriptionsItemSkeleton, {
      active: active
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DescriptionsLargeItemSkeleton, {
      active: active
    })]
  });
};
var DescriptionsPageSkeleton = function DescriptionsPageSkeleton(_ref6) {
  var _ref6$active = _ref6.active,
    active = _ref6$active === void 0 ? true : _ref6$active,
    pageHeader = _ref6.pageHeader,
    list = _ref6.list;
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)("div", {
    style: {
      width: '100%'
    },
    children: [pageHeader !== false && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_List__WEBPACK_IMPORTED_MODULE_4__/* .PageHeaderSkeleton */ .SM, {
      active: active
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DescriptionsSkeleton, {
      active: active
    }), list !== false && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_List__WEBPACK_IMPORTED_MODULE_4__/* .Line */ .x1, {}), list !== false && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TableSkeleton, {
      active: active,
      size: list
    })]
  });
};
/* harmony default export */ __webpack_exports__.ZP = (DescriptionsPageSkeleton);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///44688
`)},56517:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SM: function() { return /* binding */ PageHeaderSkeleton; },
/* harmony export */   cg: function() { return /* binding */ ListSkeleton; },
/* harmony export */   x1: function() { return /* binding */ Line; }
/* harmony export */ });
/* unused harmony exports MediaQueryKeyEnum, ListSkeletonItem, ListToolbarSkeleton */
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(96074);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(75302);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(76216);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(99559);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(42075);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(67294);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(85893);



/** \u4E00\u6761\u5206\u5272\u7EBF */



var Line = function Line(_ref) {
  var padding = _ref.padding;
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)("div", {
    style: {
      padding: padding || '0 24px'
    },
    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z, {
      style: {
        margin: 0
      }
    })
  });
};
var MediaQueryKeyEnum = {
  xs: 2,
  sm: 2,
  md: 4,
  lg: 4,
  xl: 6,
  xxl: 6
};
var StatisticSkeleton = function StatisticSkeleton(_ref2) {
  var size = _ref2.size,
    active = _ref2.active;
  var defaultCol = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function () {
    return {
      lg: true,
      md: true,
      sm: false,
      xl: false,
      xs: false,
      xxl: false
    };
  }, []);
  var col = antd__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .ZP.useBreakpoint() || defaultCol;
  var colSize = Object.keys(col).filter(function (key) {
    return col[key] === true;
  })[0] || 'md';
  var arraySize = size === undefined ? MediaQueryKeyEnum[colSize] || 6 : size;
  var firstWidth = function firstWidth(index) {
    if (index === 0) {
      return 0;
    }
    if (arraySize > 2) {
      return 42;
    }
    return 16;
  };
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z, {
    bordered: false,
    style: {
      marginBlockEnd: 16
    },
    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)("div", {
      style: {
        width: '100%',
        justifyContent: 'space-between',
        display: 'flex'
      },
      children: new Array(arraySize).fill(null).map(function (_, index) {
        return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)("div", {
          style: {
            borderInlineStart: arraySize > 2 && index === 1 ? '1px solid rgba(0,0,0,0.06)' : undefined,
            paddingInlineStart: firstWidth(index),
            flex: 1,
            marginInlineEnd: index === 0 ? 16 : 0
          },
          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z, {
            active: active,
            paragraph: false,
            title: {
              width: 100,
              style: {
                marginBlockStart: 0
              }
            }
          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z.Button, {
            active: active,
            style: {
              height: 48
            }
          })]
        }, index);
      })
    })
  });
};

/** \u5217\u8868\u5B50\u9879\u76EE\u9AA8\u67B6\u5C4F */
var ListSkeletonItem = function ListSkeletonItem(_ref3) {
  var active = _ref3.active;
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z, {
      bordered: false
      // eslint-disable-next-line react/no-array-index-key
      ,
      style: {
        borderRadius: 0
      },
      bodyStyle: {
        padding: 24
      },
      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)("div", {
        style: {
          width: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        },
        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)("div", {
          style: {
            maxWidth: '100%',
            flex: 1
          },
          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z, {
            active: active,
            title: {
              width: 100,
              style: {
                marginBlockStart: 0
              }
            },
            paragraph: {
              rows: 1,
              style: {
                margin: 0
              }
            }
          })
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z.Button, {
          active: active,
          size: "small",
          style: {
            width: 165,
            marginBlockStart: 12
          }
        })]
      })
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Line, {})]
  });
};

/** \u5217\u8868\u9AA8\u67B6\u5C4F */
var ListSkeleton = function ListSkeleton(_ref4) {
  var size = _ref4.size,
    _ref4$active = _ref4.active,
    active = _ref4$active === void 0 ? true : _ref4$active,
    actionButton = _ref4.actionButton;
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z, {
    bordered: false,
    bodyStyle: {
      padding: 0
    },
    children: [new Array(size).fill(null).map(function (_, index) {
      return (
        /*#__PURE__*/
        // eslint-disable-next-line react/no-array-index-key
        (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ListSkeletonItem, {
          active: !!active
        }, index)
      );
    }), actionButton !== false && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z, {
      bordered: false,
      style: {
        borderStartEndRadius: 0,
        borderTopLeftRadius: 0
      },
      bodyStyle: {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      },
      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z.Button, {
        style: {
          width: 102
        },
        active: active,
        size: "small"
      })
    })]
  });
};

/**
 * \u9762\u5305\u5C51\u7684 \u9AA8\u67B6\u5C4F
 *
 * @param param0
 */
var PageHeaderSkeleton = function PageHeaderSkeleton(_ref5) {
  var active = _ref5.active;
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)("div", {
    style: {
      marginBlockEnd: 16
    },
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z, {
      paragraph: false,
      title: {
        width: 185
      }
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z.Button, {
      active: active,
      size: "small"
    })]
  });
};
/**
 * \u5217\u8868\u64CD\u4F5C\u680F\u7684\u9AA8\u67B6\u5C4F
 *
 * @param param0
 */
var ListToolbarSkeleton = function ListToolbarSkeleton(_ref6) {
  var active = _ref6.active;
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z, {
    bordered: false,
    style: {
      borderBottomRightRadius: 0,
      borderBottomLeftRadius: 0
    },
    bodyStyle: {
      paddingBlockEnd: 8
    },
    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Z, {
      style: {
        width: '100%',
        justifyContent: 'space-between'
      },
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z.Button, {
        active: active,
        style: {
          width: 200
        },
        size: "small"
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Z, {
        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z.Button, {
          active: active,
          size: "small",
          style: {
            width: 120
          }
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z.Button, {
          active: active,
          size: "small",
          style: {
            width: 80
          }
        })]
      })]
    })
  });
};
var ListPageSkeleton = function ListPageSkeleton(_ref7) {
  var _ref7$active = _ref7.active,
    active = _ref7$active === void 0 ? true : _ref7$active,
    statistic = _ref7.statistic,
    actionButton = _ref7.actionButton,
    toolbar = _ref7.toolbar,
    pageHeader = _ref7.pageHeader,
    _ref7$list = _ref7.list,
    list = _ref7$list === void 0 ? 5 : _ref7$list;
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)("div", {
    style: {
      width: '100%'
    },
    children: [pageHeader !== false && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PageHeaderSkeleton, {
      active: active
    }), statistic !== false && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(StatisticSkeleton, {
      size: statistic,
      active: active
    }), (toolbar !== false || list !== false) && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z, {
      bordered: false,
      bodyStyle: {
        padding: 0
      },
      children: [toolbar !== false && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ListToolbarSkeleton, {
        active: active
      }), list !== false && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ListSkeleton, {
        size: list,
        active: active,
        actionButton: actionButton
      })]
    })]
  });
};
/* harmony default export */ __webpack_exports__.ZP = (ListPageSkeleton);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///56517
`)},27076:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(5574);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(97857);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _utils_string__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(7369);
/* harmony import */ var _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(6110);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(7837);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(67294);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(85893);







var PageContainerTabsWithSearch = function PageContainerTabsWithSearch(_ref) {
  var tabsItems = _ref.tabsItems,
    _ref$searchParamsUrlK = _ref.searchParamsUrlKey,
    searchParamsUrlKey = _ref$searchParamsUrlK === void 0 ? 'tab' : _ref$searchParamsUrlK,
    _onTabChange = _ref.onTabChange,
    pageTitle = _ref.pageTitle;
  var tabsItemsFormat = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(function () {
    return tabsItems === null || tabsItems === void 0 ? void 0 : tabsItems.map(function (item) {
      return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({}, item), {}, {
        tabKey: item.tabKey || (0,_utils_string__WEBPACK_IMPORTED_MODULE_5__/* .nonAccentVietnamese */ .w)(item.label)
      });
    });
  }, [tabsItems]);
  var _useSearchParams = (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)(),
    _useSearchParams2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_0___default()(_useSearchParams, 2),
    searchParams = _useSearchParams2[0],
    setSearchParams = _useSearchParams2[1];
  var tabActive = searchParamsUrlKey ? searchParams.get(searchParamsUrlKey) : undefined;
  console.log('tabActive', tabActive);
  var setTabActive = searchParamsUrlKey ? function (tabActiveVal) {
    searchParams.set(searchParamsUrlKey, tabActiveVal.toString());
    setSearchParams(searchParams);
  } : undefined;
  var tabList = tabsItemsFormat === null || tabsItemsFormat === void 0 ? void 0 : tabsItemsFormat.map(function (item) {
    return {
      tab: item.label,
      key: item.tabKey,
      tabKey: item.tabKey
    };
  });
  var tabPageActive = searchParamsUrlKey ? (tabsItemsFormat === null || tabsItemsFormat === void 0 ? void 0 : tabsItemsFormat.find(function (item) {
    return item.tabKey === tabActive;
  })) || (tabsItemsFormat === null || tabsItemsFormat === void 0 ? void 0 : tabsItemsFormat[0]) : undefined;
  var ComponentActive = tabPageActive === null || tabPageActive === void 0 ? void 0 : tabPageActive.component;
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_6__/* .PageContainer */ ._z, {
    fixedHeader: true,
    extra: tabPageActive === null || tabPageActive === void 0 ? void 0 : tabPageActive.extraPage,
    tabActiveKey: tabPageActive === null || tabPageActive === void 0 ? void 0 : tabPageActive.tabKey,
    tabList: tabList,
    onTabChange: function onTabChange(tabActiveVal) {
      _onTabChange === null || _onTabChange === void 0 || _onTabChange(tabActiveVal);
      if (searchParamsUrlKey) setTabActive === null || setTabActive === void 0 || setTabActive(tabActiveVal);
    },
    title: pageTitle,
    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(react__WEBPACK_IMPORTED_MODULE_3__.Suspense, {
      fallback: (tabPageActive === null || tabPageActive === void 0 ? void 0 : tabPageActive.fallback) || null,
      children: ComponentActive && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(ComponentActive, {})
    })
  });
};
/* harmony default export */ __webpack_exports__.Z = (PageContainerTabsWithSearch);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///27076
`)},29097:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _components_PageContainerTabsWithSearch__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(27076);
/* harmony import */ var _utils_lazy__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(48576);
/* harmony import */ var _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(44688);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(7837);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(29905);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(67294);
/* harmony import */ var _hooks_useGetTimeSheetDetail__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(7706);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(85893);









var Approve = (0,_utils_lazy__WEBPACK_IMPORTED_MODULE_1__/* .myLazy */ .Q)(function () {
  return Promise.all(/* import() */[__webpack_require__.e(6), __webpack_require__.e(1499), __webpack_require__.e(5779), __webpack_require__.e(2788), __webpack_require__.e(3633), __webpack_require__.e(5277), __webpack_require__.e(5243), __webpack_require__.e(265), __webpack_require__.e(4908), __webpack_require__.e(4994), __webpack_require__.e(2783), __webpack_require__.e(5514), __webpack_require__.e(7839), __webpack_require__.e(8021), __webpack_require__.e(1403), __webpack_require__.e(4560), __webpack_require__.e(691), __webpack_require__.e(9775)]).then(__webpack_require__.bind(__webpack_require__, 84830));
});
var Detail = (0,_utils_lazy__WEBPACK_IMPORTED_MODULE_1__/* .myLazy */ .Q)(function () {
  return Promise.all(/* import() */[__webpack_require__.e(6), __webpack_require__.e(1499), __webpack_require__.e(5779), __webpack_require__.e(2788), __webpack_require__.e(3633), __webpack_require__.e(5277), __webpack_require__.e(5243), __webpack_require__.e(265), __webpack_require__.e(4908), __webpack_require__.e(4994), __webpack_require__.e(2783), __webpack_require__.e(5514), __webpack_require__.e(7839), __webpack_require__.e(8021), __webpack_require__.e(1403), __webpack_require__.e(4560), __webpack_require__.e(691), __webpack_require__.e(8047), __webpack_require__.e(6903), __webpack_require__.e(305), __webpack_require__.e(4459), __webpack_require__.e(7821), __webpack_require__.e(4595)]).then(__webpack_require__.bind(__webpack_require__, 20329));
});
var Index = function Index(_ref) {
  var children = _ref.children;
  var _useParams = (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.useParams)(),
    id = _useParams.id;
  var _useIntl = (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  var _useGetTimeSheetDetai = (0,_hooks_useGetTimeSheetDetail__WEBPACK_IMPORTED_MODULE_4__/* .useGetTimeSheetDetail */ .E)(),
    getDetail = _useGetTimeSheetDetai.run,
    loading = _useGetTimeSheetDetai.loading,
    data = _useGetTimeSheetDetai.data;
  (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(function () {
    if (id) {
      getDetail(id);
    }
  }, [id]);
  if (loading) {
    return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_6__/* .DescriptionsSkeleton */ .Yk, {
      active: true
    });
  }
  if (!data) {
    return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(antd__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .ZP, {
      status: "404",
      title: '404',
      subTitle: formatMessage({
        id: 'common.not_found'
      })
    });
  }
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(_components_PageContainerTabsWithSearch__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .Z, {
    tabsItems: [{
      label: formatMessage({
        id: 'common.details'
      }),
      component: function component() {
        return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.Fragment, {
          children: id ? /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(Detail, {
            timeSheetId: id
          }) : null
        });
      },
      // extraPage: [<Button key={'cancel'}>H\u1EE7y</Button>, <Button key={'save'}>L\u01B0u</Button>],
      fallback: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_6__/* .DescriptionsSkeleton */ .Yk, {
        active: true
      })
    }, {
      label: formatMessage({
        id: 'common.add_approval'
      }),
      component: function component() {
        return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(Approve, {
          timeSheetId: id
        });
      },
      // extraPage: [<Button key={'cancel'}>H\u1EE7y</Button>, <Button key={'save'}>L\u01B0u</Button>],
      fallback: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_6__/* .DescriptionsSkeleton */ .Yk, {
        active: true
      })
    }]
  });
};
/* harmony default export */ __webpack_exports__["default"] = (Index);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///29097
`)},7706:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   E: function() { return /* binding */ useGetTimeSheetDetail; }
/* harmony export */ });
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _common_contanst_constanst__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(86604);
/* harmony import */ var _services_timesheetsV2__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(62872);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(7837);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(31418);






function useGetTimeSheetDetail() {
  var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},
    _onSuccess = _ref.onSuccess;
  var _App$useApp = antd__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z.useApp(),
    message = _App$useApp.message;
  var _useIntl = (0,_umijs_max__WEBPACK_IMPORTED_MODULE_4__.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_4__.useRequest)( /*#__PURE__*/function () {
    var _ref2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee(id) {
      var _res$data;
      var res, data;
      return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _context.next = 2;
            return (0,_services_timesheetsV2__WEBPACK_IMPORTED_MODULE_3__/* .getTimeSheets */ .is)({
              page: 1,
              size: 1,
              filters: [[_common_contanst_constanst__WEBPACK_IMPORTED_MODULE_2__/* .DOCTYPE_ERP */ .lH.iotEmployeeTimesheet, 'name', '=', id]]
            });
          case 2:
            res = _context.sent;
            data = (_res$data = res.data) === null || _res$data === void 0 ? void 0 : _res$data[0];
            if (data) {
              _context.next = 6;
              break;
            }
            throw new Error();
          case 6:
            return _context.abrupt("return", {
              data: data
            });
          case 7:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }));
    return function (_x) {
      return _ref2.apply(this, arguments);
    };
  }(), {
    manual: true,
    onError: function onError(err) {
      message.error(err.message);
    },
    onSuccess: function onSuccess(data) {
      // message.success(
      //   formatMessage({
      //     id: 'common.success',
      //   }),
      // );
      _onSuccess === null || _onSuccess === void 0 || _onSuccess(data);
    }
  });
}//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///7706
`)},62872:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Kv: function() { return /* binding */ createTimeSeedTask; },
/* harmony export */   Mr: function() { return /* binding */ deleteTimesheetTask; },
/* harmony export */   OR: function() { return /* binding */ createTimeSheet; },
/* harmony export */   Qf: function() { return /* binding */ getTimeSheetTasks; },
/* harmony export */   ZS: function() { return /* binding */ getTimeSheetTaskRecords; },
/* harmony export */   d7: function() { return /* binding */ getTimeSheetReport; },
/* harmony export */   eQ: function() { return /* binding */ editTimeSheet; },
/* harmony export */   fB: function() { return /* binding */ createTimeSheetApprove; },
/* harmony export */   gr: function() { return /* binding */ getTimeSheetApproves; },
/* harmony export */   hs: function() { return /* binding */ editTimeSheetApprove; },
/* harmony export */   id: function() { return /* binding */ deleteTimesheet; },
/* harmony export */   is: function() { return /* binding */ getTimeSheets; },
/* harmony export */   j1: function() { return /* binding */ getAttendanceV2Report; },
/* harmony export */   v6: function() { return /* binding */ editTimesheet; }
/* harmony export */ });
/* unused harmony exports editTimeSheetApproveForRequest, copyTimeSheetTask */
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectWithoutProperties_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(13769);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectWithoutProperties_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectWithoutProperties_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(97857);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(7837);
/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(27484);
/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(467);




var _excluded = ["start_date", "end_date", "employee_id"];



var getTimeSheets = /*#__PURE__*/function () {
  var _ref = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_4__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_6__/* .generateAPIPath */ .rH)('api/v2/timesheet-table-v2/iot_employee_timesheet'), {
            params: (0,_utils__WEBPACK_IMPORTED_MODULE_6__/* .getParamsReqList */ .vj)(params)
          });
        case 2:
          res = _context.sent;
          return _context.abrupt("return", res.result);
        case 4:
        case "end":
          return _context.stop();
      }
    }, _callee);
  }));
  return function getTimeSheets(_x) {
    return _ref.apply(this, arguments);
  };
}();
var createTimeSheet = /*#__PURE__*/function () {
  var _ref2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee2(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          _context2.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_4__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_6__/* .generateAPIPath */ .rH)('api/v2/timesheet-table-v2/iot_employee_timesheet'), {
            method: 'POST',
            data: data
          });
        case 2:
          res = _context2.sent;
          return _context2.abrupt("return", res);
        case 4:
        case "end":
          return _context2.stop();
      }
    }, _callee2);
  }));
  return function createTimeSheet(_x2) {
    return _ref2.apply(this, arguments);
  };
}();
var editTimeSheet = /*#__PURE__*/function () {
  var _ref3 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee3(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee3$(_context3) {
      while (1) switch (_context3.prev = _context3.next) {
        case 0:
          _context3.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_4__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_6__/* .generateAPIPath */ .rH)('api/v2/timesheet-table-v2/iot_employee_timesheet'), {
            method: 'PUT',
            data: data
          });
        case 2:
          res = _context3.sent;
          return _context3.abrupt("return", res);
        case 4:
        case "end":
          return _context3.stop();
      }
    }, _callee3);
  }));
  return function editTimeSheet(_x3) {
    return _ref3.apply(this, arguments);
  };
}();
var deleteTimesheet = /*#__PURE__*/function () {
  var _ref4 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee4(id) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee4$(_context4) {
      while (1) switch (_context4.prev = _context4.next) {
        case 0:
          _context4.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_4__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_6__/* .generateAPIPath */ .rH)("api/v2/timesheet-table-v2/iot_employee_timesheet?name=".concat(id)), {
            method: 'DELETE'
          });
        case 2:
          res = _context4.sent;
          return _context4.abrupt("return", res);
        case 4:
        case "end":
          return _context4.stop();
      }
    }, _callee4);
  }));
  return function deleteTimesheet(_x4) {
    return _ref4.apply(this, arguments);
  };
}();

//===========================================

var getTimeSheetApproves = /*#__PURE__*/function () {
  var _ref5 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee5() {
    var _ref6,
      params,
      timesheet_id,
      name,
      res,
      _args5 = arguments;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee5$(_context5) {
      while (1) switch (_context5.prev = _context5.next) {
        case 0:
          _ref6 = _args5.length > 0 && _args5[0] !== undefined ? _args5[0] : {}, params = _ref6.params, timesheet_id = _ref6.timesheet_id, name = _ref6.name;
          _context5.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_4__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_6__/* .generateAPIPath */ .rH)('api/v2/timesheet-table-v2/iot_timesheet_approval'), {
            params: D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({}, (0,_utils__WEBPACK_IMPORTED_MODULE_6__/* .getParamsReqList */ .vj)(params)), {}, {
              timesheet_id: timesheet_id,
              name: name
            })
          });
        case 3:
          res = _context5.sent;
          console.log('res approval list', res);
          return _context5.abrupt("return", D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({}, res.result), {}, {
            data: res.result.data.map(function (item) {
              return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({}, item), {}, {
                approval_full_name: "".concat(item.approver_first_name, " ").concat(item.approver_last_name)
              });
            })
          }));
        case 6:
        case "end":
          return _context5.stop();
      }
    }, _callee5);
  }));
  return function getTimeSheetApproves() {
    return _ref5.apply(this, arguments);
  };
}();
var createTimeSheetApprove = /*#__PURE__*/function () {
  var _ref7 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee6(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee6$(_context6) {
      while (1) switch (_context6.prev = _context6.next) {
        case 0:
          _context6.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_4__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_6__/* .generateAPIPath */ .rH)('api/v2/timesheet-table-v2/iot_timesheet_approval'), {
            method: 'POST',
            data: D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({}, data), {}, {
              request_date: dayjs__WEBPACK_IMPORTED_MODULE_5___default()().toISOString()
            })
          });
        case 2:
          res = _context6.sent;
          return _context6.abrupt("return", res);
        case 4:
        case "end":
          return _context6.stop();
      }
    }, _callee6);
  }));
  return function createTimeSheetApprove(_x5) {
    return _ref7.apply(this, arguments);
  };
}();
var editTimeSheetApprove = /*#__PURE__*/function () {
  var _ref8 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee7(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee7$(_context7) {
      while (1) switch (_context7.prev = _context7.next) {
        case 0:
          _context7.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_4__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_6__/* .generateAPIPath */ .rH)('api/v2/timesheet-table-v2/iot_timesheet_approval'), {
            method: 'PUT',
            data: data
          });
        case 2:
          res = _context7.sent;
          return _context7.abrupt("return", res);
        case 4:
        case "end":
          return _context7.stop();
      }
    }, _callee7);
  }));
  return function editTimeSheetApprove(_x6) {
    return _ref8.apply(this, arguments);
  };
}();
var editTimeSheetApproveForRequest = /*#__PURE__*/(/* unused pure expression or super */ null && (function () {
  var _ref9 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee8(data) {
    var res;
    return _regeneratorRuntime().wrap(function _callee8$(_context8) {
      while (1) switch (_context8.prev = _context8.next) {
        case 0:
          _context8.next = 2;
          return request(generateAPIPath('api/v2/timesheet-table-v2/iot_timesheet_approval/request'), {
            method: 'PUT',
            data: data
          });
        case 2:
          res = _context8.sent;
          return _context8.abrupt("return", res);
        case 4:
        case "end":
          return _context8.stop();
      }
    }, _callee8);
  }));
  return function editTimeSheetApproveForRequest(_x7) {
    return _ref9.apply(this, arguments);
  };
}()));
//================================================================

var getTimeSheetTasks = /*#__PURE__*/function () {
  var _ref10 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee9() {
    var params,
      res,
      _args9 = arguments;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee9$(_context9) {
      while (1) switch (_context9.prev = _context9.next) {
        case 0:
          params = _args9.length > 0 && _args9[0] !== undefined ? _args9[0] : {};
          _context9.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_4__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_6__/* .generateAPIPath */ .rH)('api/v2/timesheet-table-v2/iot_timesheet_task'), {
            method: 'GET',
            params: D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({}, params)
          });
        case 3:
          res = _context9.sent;
          return _context9.abrupt("return", D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({}, res.result), {}, {
            data: res.result.data.map(function (item) {
              return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({}, item), {}, {
                label: item.timesheet_task_label
              });
            })
          }));
        case 5:
        case "end":
          return _context9.stop();
      }
    }, _callee9);
  }));
  return function getTimeSheetTasks() {
    return _ref10.apply(this, arguments);
  };
}();
var getTimeSheetTaskRecords = /*#__PURE__*/function () {
  var _ref11 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee10() {
    var params,
      res,
      _args10 = arguments;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee10$(_context10) {
      while (1) switch (_context10.prev = _context10.next) {
        case 0:
          params = _args10.length > 0 && _args10[0] !== undefined ? _args10[0] : {};
          _context10.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_4__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_6__/* .generateAPIPath */ .rH)('api/v2/timesheet-table-v2/iot_timesheet_task_record'), {
            method: 'GET',
            params: D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({}, params)
          });
        case 3:
          res = _context10.sent;
          return _context10.abrupt("return", D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({}, res.result), {}, {
            data: res.result.data.map(function (item) {
              return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({}, item), {}, {
                label: item.task_record_label
              });
            })
          }));
        case 5:
        case "end":
          return _context10.stop();
      }
    }, _callee10);
  }));
  return function getTimeSheetTaskRecords() {
    return _ref11.apply(this, arguments);
  };
}();
var createTimeSeedTask = /*#__PURE__*/function () {
  var _ref12 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee11(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee11$(_context11) {
      while (1) switch (_context11.prev = _context11.next) {
        case 0:
          _context11.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_4__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_6__/* .generateAPIPath */ .rH)('api/v2/timesheet-table-v2/iot_timesheet_task'), {
            method: 'POST',
            data: data
          });
        case 2:
          res = _context11.sent;
          return _context11.abrupt("return", res);
        case 4:
        case "end":
          return _context11.stop();
      }
    }, _callee11);
  }));
  return function createTimeSeedTask(_x8) {
    return _ref12.apply(this, arguments);
  };
}();
var editTimesheet = /*#__PURE__*/function () {
  var _ref13 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee12(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee12$(_context12) {
      while (1) switch (_context12.prev = _context12.next) {
        case 0:
          _context12.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_4__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_6__/* .generateAPIPath */ .rH)('api/v2/timesheet-table-v2/iot_timesheet_task'), {
            method: 'POST',
            data: data
          });
        case 2:
          res = _context12.sent;
          return _context12.abrupt("return", res);
        case 4:
        case "end":
          return _context12.stop();
      }
    }, _callee12);
  }));
  return function editTimesheet(_x9) {
    return _ref13.apply(this, arguments);
  };
}();
var deleteTimesheetTask = /*#__PURE__*/function () {
  var _ref14 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee13(id) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee13$(_context13) {
      while (1) switch (_context13.prev = _context13.next) {
        case 0:
          _context13.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_4__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_6__/* .generateAPIPath */ .rH)('api/v2/timesheet-table-v2/iot_timesheet_task'), {
            method: 'DELETE',
            params: {
              name: id
            }
          });
        case 2:
          res = _context13.sent;
          return _context13.abrupt("return", res);
        case 4:
        case "end":
          return _context13.stop();
      }
    }, _callee13);
  }));
  return function deleteTimesheetTask(_x10) {
    return _ref14.apply(this, arguments);
  };
}();
var copyTimeSheetTask = /*#__PURE__*/(/* unused pure expression or super */ null && (function () {
  var _ref15 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee14(data) {
    var res;
    return _regeneratorRuntime().wrap(function _callee14$(_context14) {
      while (1) switch (_context14.prev = _context14.next) {
        case 0:
          _context14.next = 2;
          return request(generateAPIPath('api/v2/timesheet-table-v2/copy_iot_timesheet_task'), {
            method: 'POST',
            data: data
          });
        case 2:
          res = _context14.sent;
          return _context14.abrupt("return", res);
        case 4:
        case "end":
          return _context14.stop();
      }
    }, _callee14);
  }));
  return function copyTimeSheetTask(_x11) {
    return _ref15.apply(this, arguments);
  };
}()));
var getTimeSheetReport = /*#__PURE__*/function () {
  var _ref16 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee15() {
    var params,
      res,
      _args15 = arguments;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee15$(_context15) {
      while (1) switch (_context15.prev = _context15.next) {
        case 0:
          params = _args15.length > 0 && _args15[0] !== undefined ? _args15[0] : {};
          _context15.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_4__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_6__/* .generateAPIPath */ .rH)('api/v2/timesheet-table-v2/report'), {
            method: 'GET',
            params: D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({}, params)
          });
        case 3:
          res = _context15.sent;
          return _context15.abrupt("return", D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({}, res.result), {}, {
            data: res.result.data.map(function (item) {
              return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({}, item);
            })
          }));
        case 5:
        case "end":
          return _context15.stop();
      }
    }, _callee15);
  }));
  return function getTimeSheetReport() {
    return _ref16.apply(this, arguments);
  };
}();
function getAttendanceV2Report(_x12) {
  return _getAttendanceV2Report.apply(this, arguments);
}
function _getAttendanceV2Report() {
  _getAttendanceV2Report = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee16(_ref17) {
    var start_date, end_date, employee_id, params, result;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee16$(_context16) {
      while (1) switch (_context16.prev = _context16.next) {
        case 0:
          start_date = _ref17.start_date, end_date = _ref17.end_date, employee_id = _ref17.employee_id, params = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectWithoutProperties_js__WEBPACK_IMPORTED_MODULE_0___default()(_ref17, _excluded);
          _context16.prev = 1;
          _context16.next = 4;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_4__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_6__/* .generateAPIPath */ .rH)('api/v2/timesheet-table-v2/report-no-group'), {
            method: 'GET',
            params: D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({
              page: 1,
              size: 10000
            }, (0,_utils__WEBPACK_IMPORTED_MODULE_6__/* .getParamsReqList */ .vj)(params)), {}, {
              start_date: start_date,
              end_date: end_date,
              employee_id: employee_id
            })
          });
        case 4:
          result = _context16.sent;
          return _context16.abrupt("return", {
            data: result.result.data || [],
            pagination: result.result.pagination
          });
        case 8:
          _context16.prev = 8;
          _context16.t0 = _context16["catch"](1);
          return _context16.abrupt("return", {
            data: []
          });
        case 11:
        case "end":
          return _context16.stop();
      }
    }, _callee16, null, [[1, 8]]);
  }));
  return _getAttendanceV2Report.apply(this, arguments);
}//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///62872
`)},7369:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   W: function() { return /* binding */ toFirstUpperCase; },
/* harmony export */   w: function() { return /* binding */ nonAccentVietnamese; }
/* harmony export */ });
function nonAccentVietnamese(str) {
  var _str$toString;
  var _str = str === null || str === void 0 || (_str$toString = str.toString) === null || _str$toString === void 0 ? void 0 : _str$toString.call(str);
  if (typeof _str !== 'string') return str;
  _str = _str.toLowerCase();
  //     We can also use this instead of from line 11 to line 17
  //     str = str.replace(/\\u00E0|\\u00E1|\\u1EA1|\\u1EA3|\\u00E3|\\u00E2|\\u1EA7|\\u1EA5|\\u1EAD|\\u1EA9|\\u1EAB|\\u0103|\\u1EB1|\\u1EAF|\\u1EB7|\\u1EB3|\\u1EB5/g, "a");
  //     str = str.replace(/\\u00E8|\\u00E9|\\u1EB9|\\u1EBB|\\u1EBD|\\u00EA|\\u1EC1|\\u1EBF|\\u1EC7|\\u1EC3|\\u1EC5/g, "e");
  //     str = str.replace(/\\u00EC|\\u00ED|\\u1ECB|\\u1EC9|\\u0129/g, "i");
  //     str = str.replace(/\\u00F2|\\u00F3|\\u1ECD|\\u1ECF|\\u00F5|\\u00F4|\\u1ED3|\\u1ED1|\\u1ED9|\\u1ED5|\\u1ED7|\\u01A1|\\u1EDD|\\u1EDB|\\u1EE3|\\u1EDF|\\u1EE1/g, "o");
  //     str = str.replace(/\\u00F9|\\u00FA|\\u1EE5|\\u1EE7|\\u0169|\\u01B0|\\u1EEB|\\u1EE9|\\u1EF1|\\u1EED|\\u1EEF/g, "u");
  //     str = str.replace(/\\u1EF3|\\u00FD|\\u1EF5|\\u1EF7|\\u1EF9/g, "y");
  //     str = str.replace(/\\u0111/g, "d");
  _str = _str.replace(/\xE0|\xE1|\u1EA1|\u1EA3|\xE3|\xE2|\u1EA7|\u1EA5|\u1EAD|\u1EA9|\u1EAB|\u0103|\u1EB1|\u1EAF|\u1EB7|\u1EB3|\u1EB5/g, 'a');
  _str = _str.replace(/\xE8|\xE9|\u1EB9|\u1EBB|\u1EBD|\xEA|\u1EC1|\u1EBF|\u1EC7|\u1EC3|\u1EC5/g, 'e');
  _str = _str.replace(/\xEC|\xED|\u1ECB|\u1EC9|\u0129/g, 'i');
  _str = _str.replace(/\xF2|\xF3|\u1ECD|\u1ECF|\xF5|\xF4|\u1ED3|\u1ED1|\u1ED9|\u1ED5|\u1ED7|\u01A1|\u1EDD|\u1EDB|\u1EE3|\u1EDF|\u1EE1/g, 'o');
  _str = _str.replace(/\xF9|\xFA|\u1EE5|\u1EE7|\u0169|\u01B0|\u1EEB|\u1EE9|\u1EF1|\u1EED|\u1EEF/g, 'u');
  _str = _str.replace(/\u1EF3|\xFD|\u1EF5|\u1EF7|\u1EF9/g, 'y');
  _str = _str.replace(/\u0111/g, 'd');
  // Some system encode vietnamese combining accent as individual utf-8 characters
  _str = _str.replace(/\\u0300|\\u0301|\\u0303|\\u0309|\\u0323/g, ''); // Huy\u1EC1n s\u1EAFc h\u1ECFi ng\xE3 n\u1EB7ng
  _str = _str.replace(/\\u02C6|\\u0306|\\u031B/g, ''); // \xC2, \xCA, \u0102, \u01A0, \u01AF
  return _str.split(',').join('');
}
var toFirstUpperCase = function toFirstUpperCase(str) {
  if (!str) return '';
  return str.charAt(0).toUpperCase() + str.slice(1);
};//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///7369
`)},75302:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var _hooks_useBreakpoint__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(25378);
"use client";




// Do not export params
function useBreakpoint() {
  return (0,_hooks_useBreakpoint__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .Z)();
}

/* harmony default export */ __webpack_exports__.ZP = ({
  useBreakpoint
});//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNzUzMDIuanMiLCJtYXBwaW5ncyI6IjtBQUFBOztBQUV3QjtBQUNrQztBQUNsQztBQUN4QjtBQUNBO0FBQ0EsU0FBUyx5RUFBcUI7QUFDOUI7QUFDb0I7QUFDcEIsdURBQWU7QUFDZjtBQUNBLENBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hbnQtZGVzaWduLXByby8uL25vZGVfbW9kdWxlcy9hbnRkL2VzL2dyaWQvaW5kZXguanM/YTQxNyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcblxuaW1wb3J0IENvbCBmcm9tICcuL2NvbCc7XG5pbXBvcnQgdXNlSW50ZXJuYWxCcmVha3BvaW50IGZyb20gJy4vaG9va3MvdXNlQnJlYWtwb2ludCc7XG5pbXBvcnQgUm93IGZyb20gJy4vcm93Jztcbi8vIERvIG5vdCBleHBvcnQgcGFyYW1zXG5mdW5jdGlvbiB1c2VCcmVha3BvaW50KCkge1xuICByZXR1cm4gdXNlSW50ZXJuYWxCcmVha3BvaW50KCk7XG59XG5leHBvcnQgeyBDb2wsIFJvdyB9O1xuZXhwb3J0IGRlZmF1bHQge1xuICB1c2VCcmVha3BvaW50XG59OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==
//# sourceURL=webpack-internal:///75302
`)}}]);
