"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[339],{89286:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(97857);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectWithoutProperties_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(13769);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectWithoutProperties_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectWithoutProperties_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(6110);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(7837);
/* harmony import */ var antd_use_styles__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(38513);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(67294);
/* harmony import */ var react_router_dom__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(96974);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(85893);


var _excluded = ["extraPage", "fallback", "children"];






var useStyles = (0,antd_use_styles__WEBPACK_IMPORTED_MODULE_3__/* .createStyles */ .k)(function (_ref) {
  var token = _ref.token;
  return {
    link: {
      color: 'inherit',
      '&:hover': {
        color: token.colorPrimaryTextHover
      }
    }
  };
});
var PageContainerTabsWithPath = function PageContainerTabsWithPath(_ref2) {
  var _matches$params, _tabActive$key;
  var tabItems = _ref2.tabItems,
    generalPath = _ref2.generalPath,
    onTabChange = _ref2.onTabChange,
    defaultTabActive = _ref2.defaultTabActive;
  var genUrl = function genUrl(path) {
    return "".concat(generalPath, "/").concat(path);
  };
  var styles = useStyles();
  var matches = (0,react_router_dom__WEBPACK_IMPORTED_MODULE_6__/* .useMatch */ .bS)(genUrl('*'));
  /**
   * matches : { *: "log/detail/ciY7e6Z7Kkv_iQR-HntBI"}
   * ['log', 'detail', 'ciY7e6Z7Kkv_iQR-HntBI']
   */
  var urlTabActive = (matches === null || matches === void 0 || (_matches$params = matches.params) === null || _matches$params === void 0 || (_matches$params = _matches$params['*']) === null || _matches$params === void 0 || (_matches$params = _matches$params.split('/').filter(function (segment) {
    return segment !== '';
  })) === null || _matches$params === void 0 ? void 0 : _matches$params[0]) || defaultTabActive;
  var tabItemsFormat = (0,react__WEBPACK_IMPORTED_MODULE_4__.useMemo)(function () {
    return tabItems === null || tabItems === void 0 ? void 0 : tabItems.map(function (_ref3) {
      var extraPage = _ref3.extraPage,
        fallback = _ref3.fallback,
        children = _ref3.children,
        rest = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectWithoutProperties_js__WEBPACK_IMPORTED_MODULE_1___default()(_ref3, _excluded);
      return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default()({}, rest), {}, {
        tab: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_2__.Link, {
          to: genUrl(rest.key),
          className: styles.link,
          children: rest.tab
        })
      });
    });
  }, [styles, genUrl]);
  var tabActive = (tabItems === null || tabItems === void 0 ? void 0 : tabItems.find(function (item) {
    return item.key === urlTabActive;
  })) || (tabItems === null || tabItems === void 0 ? void 0 : tabItems[0]);
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_7__/* .PageContainer */ ._z, {
    tabList: tabItemsFormat,
    tabActiveKey: tabActive === null || tabActive === void 0 || (_tabActive$key = tabActive.key) === null || _tabActive$key === void 0 ? void 0 : _tabActive$key.toString(),
    onTabChange: onTabChange,
    extra: tabActive === null || tabActive === void 0 ? void 0 : tabActive.extraPage,
    childrenContentStyle: {
      padding: '0px 32px'
    },
    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(react__WEBPACK_IMPORTED_MODULE_4__.Suspense, {
      fallback: tabActive === null || tabActive === void 0 ? void 0 : tabActive.fallback,
      children: (tabActive === null || tabActive === void 0 ? void 0 : tabActive.children) || /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_2__.Outlet, {})
    })
  });
};
/* harmony default export */ __webpack_exports__.Z = (PageContainerTabsWithPath);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///89286
`)},18253:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _components_FallbackContent__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(65573);
/* harmony import */ var _components_PageContainerTabsWithPath__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(89286);
/* harmony import */ var _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(44688);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(7837);
/* harmony import */ var _Approval__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(78872);
/* harmony import */ var _Checkin__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(4242);
/* harmony import */ var _Report__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(92595);
/* harmony import */ var _WorkShift__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(45110);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(85893);









var Timekeeping = function Timekeeping() {
  var _useIntl = (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  var access = (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.useAccess)();
  var canRead = access.canAccessPageTimeKeepingManagement();
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_2__.Access, {
    accessible: canRead,
    fallback: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsx)(_components_FallbackContent__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .Z, {}),
    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsx)(_components_PageContainerTabsWithPath__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Z, {
      tabItems: [{
        tab: formatMessage({
          id: 'common.history_in_and_out'
        }),
        key: 'checkin-history',
        fallback: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsx)(_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_8__/* .DescriptionsSkeleton */ .Yk, {
          active: true
        }),
        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsx)(_Checkin__WEBPACK_IMPORTED_MODULE_4__["default"], {})
      },
      // {
      //   tab: formatMessage({
      //     id: 'common.detail',
      //   }),
      //   key: 'general',
      //   fallback: <DescriptionsSkeleton active />,
      //   children: <General />,
      // },
      {
        tab: formatMessage({
          id: 'common.report'
        }),
        key: 'report',
        fallback: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsx)(_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_8__/* .DescriptionsSkeleton */ .Yk, {
          active: true
        }),
        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsx)(_Report__WEBPACK_IMPORTED_MODULE_5__["default"], {})
      }, {
        tab: formatMessage({
          id: 'common.work_shift'
        }),
        key: 'work-shift',
        fallback: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsx)(_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_8__/* .DescriptionsSkeleton */ .Yk, {
          active: true
        }),
        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsx)(_WorkShift__WEBPACK_IMPORTED_MODULE_6__["default"], {})
      }, {
        tab: formatMessage({
          id: 'common.approval_list_need_to_check'
        }),
        key: 'approval',
        fallback: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsx)(_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_8__/* .DescriptionsSkeleton */ .Yk, {
          active: true
        }),
        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsx)(_Approval__WEBPACK_IMPORTED_MODULE_3__["default"], {})
      }],
      generalPath: '/employee-management/timekeeping'
    })
  });
};
/* harmony default export */ __webpack_exports__["default"] = (Timekeeping);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///18253
`)}}]);
