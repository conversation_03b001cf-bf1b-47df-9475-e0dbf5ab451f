import { TaskItemUsed, useTaskItemUsedCreateStore } from '@/stores/TaskItemUsedCreateStore';
import { DeleteOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { EditableProTable } from '@ant-design/pro-components';
import { FormattedMessage, useIntl } from '@umijs/max';
import { useDebounceEffect } from 'ahooks';
import { Button, Select } from 'antd';
import React, { useRef, useState } from 'react';
import CreateItem from './CreateItemCreateView';

/**
 * GIAO DIỆN TẠO DANH SÁCH VẬT TƯ LIÊN QUAN TRONG GIAO DIỆN TẠO TASK
 */

const TaskItemTable = () => {
  const { taskItemUsed, setTaskItemUsed } = useTaskItemUsedCreateStore();
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  const [version, setVersion] = useState(0); // State variable to force re-render
  const actionRef = useRef<ActionType>();
  const intl = useIntl();

  const columns: ProColumns<TaskItemUsed>[] = [
    {
      title: <FormattedMessage id="category.material_management.category_code" />,
      dataIndex: 'iot_category_id',
      editable: false,
      render: (dom: any, entity) => {
        return <>{entity?.item_name}</>;
      },
    },
    {
      title: <FormattedMessage id="category.material_management.category_name" />,
      dataIndex: 'label',
      editable: false,
      render: (dom: any, entity) => {
        return <>{entity?.label}</>;
      },
    },
    {
      title: <FormattedMessage id="storage_management.category_management.expected_quantity" />,
      dataIndex: 'exp_quantity',
      valueType: 'digit',
    },
    {
      title: <FormattedMessage id="common.unit" />,
      dataIndex: 'uom_label',
      editable: false,
      renderFormItem: (_, { record }) => {
        const uoms = record?.uoms || [];
        return (
          <Select
            value={record?.active_uom || record?.uom}
            onChange={(value) => {
              const selectedUom = uoms.find((uom: any) => uom.uom === value);
              if (selectedUom) {
                // Cập nhật active_uom và active_conversion_factor
                const updatedRecord = {
                  ...record,
                  active_uom: selectedUom.uom,
                  active_conversion_factor: selectedUom.conversion_factor,
                  uom_label: selectedUom.uom_label,
                };

                // Cập nhật trong store
                const updatedTaskItems = taskItemUsed.map((item) =>
                  item.iot_category_id === record?.iot_category_id ? updatedRecord : item,
                );
                setTaskItemUsed(updatedTaskItems);
              }
            }}
            options={uoms.map((uom: any) => ({
              label: uom.uom_label,
              value: uom.uom,
            }))}
          />
        );
      },
      render: (_, record) => {
        return record?.uom_label || record?.active_uom;
      },
    },
    {
      title: <FormattedMessage id="common.action" />,
      editable: false,
      render: (dom: any, entity) => {
        return (
          <Button
            danger
            size="small"
            style={{ display: 'flex', alignItems: 'center' }}
            onClick={() => handlerRemove(entity.iot_category_id)}
          >
            <DeleteOutlined />
          </Button>
        );
      },
    },
  ];

  useDebounceEffect(() => {
    setEditableRowKeys(taskItemUsed.map((item: any) => item.iot_category_id));
    setVersion((prev) => prev + 1); // Force re-render by updating version
  }, [taskItemUsed]);

  const handlerRemove = async (id: any) => {
    try {
      const updatedDataSource = taskItemUsed.filter((item) => item.iot_category_id !== id);
      setTaskItemUsed(updatedDataSource);
    } catch (error) {
      console.log(error);
    }
  };

  return (
    <>
      <EditableProTable
        key={version} // Use version to force re-render
        actionRef={actionRef}
        headerTitle={intl.formatMessage({ id: 'seasonalTab.suppliesList' })}
        columns={columns}
        rowKey="iot_category_id"
        dataSource={taskItemUsed}
        value={taskItemUsed}
        editable={{
          type: 'multiple',
          editableKeys,
          actionRender: (row, config, defaultDoms) => {
            return [defaultDoms.delete];
          },
          onValuesChange: (record, recordList) => {
            setTaskItemUsed(recordList);
            setEditableRowKeys(recordList.map((item) => item.iot_category_id));
          },
          onChange: (key) => {
            setEditableRowKeys(key);
          },
        }}
        pagination={{
          defaultPageSize: 100,
          pageSizeOptions: ['20', '50', '100'],
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        recordCreatorProps={false}
        search={false}
      />
    </>
  );
};

const TaskManagement = () => {
  return (
    <>
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '16px',
        }}
      >
        <h2></h2>
        <CreateItem />
      </div>
      <TaskItemTable />
    </>
  );
};

export default TaskManagement;
