"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[6194],{13490:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   W: function() { return /* binding */ DEFAULT_FALLBACK_IMG; }
/* harmony export */ });
var DEFAULT_FALLBACK_IMG = 'data:image/png;base64,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';//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///13490
`)},65573:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(29905);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(85893);


var FallbackComponent = function FallbackComponent() {
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .ZP, {
    status: "403",
    title: "Kh\\xF4ng th\\u1EC3 truy c\\u1EADp",
    subTitle: "Xin l\\u1ED7i, b\\u1EA1n kh\\xF4ng c\\xF3 quy\\u1EC1n xem n\\u1ED9i dung n\\xE0y."
    //   extra={
    //     <Button type="primary" href="/">
    //       Back Home
    //     </Button>
    //   }
  });
};
/* harmony default export */ __webpack_exports__.Z = (FallbackComponent);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNjU1NzMuanMiLCJtYXBwaW5ncyI6Ijs7QUFBOEI7QUFBQTtBQUU5QixJQUFNRyxpQkFBaUIsR0FBRyxTQUFwQkEsaUJBQWlCQSxDQUFBLEVBQVM7RUFDOUIsb0JBQ0VELHNEQUFBLENBQUNGLHNEQUFNO0lBQ0xJLE1BQU0sRUFBQyxLQUFLO0lBQ1pDLEtBQUssRUFBQyxpQ0FBb0I7SUFDMUJDLFFBQVEsRUFBQztJQUNUO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7RUFBQSxDQUNELENBQUM7QUFFTixDQUFDO0FBRUQsc0RBQWVILGlCQUFpQiIsInNvdXJjZXMiOlsid2VicGFjazovL2FudC1kZXNpZ24tcHJvLy4vc3JjL2NvbXBvbmVudHMvRmFsbGJhY2tDb250ZW50L2luZGV4LnRzeD9mZGRhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFJlc3VsdCB9IGZyb20gJ2FudGQnO1xyXG5cclxuY29uc3QgRmFsbGJhY2tDb21wb25lbnQgPSAoKSA9PiB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxSZXN1bHRcclxuICAgICAgc3RhdHVzPVwiNDAzXCJcclxuICAgICAgdGl0bGU9XCJLaMO0bmcgdGjhu4MgdHJ1eSBj4bqtcFwiXHJcbiAgICAgIHN1YlRpdGxlPVwiWGluIGzhu5dpLCBi4bqhbiBraMO0bmcgY8OzIHF1eeG7gW4geGVtIG7hu5lpIGR1bmcgbsOgeS5cIlxyXG4gICAgICAvLyAgIGV4dHJhPXtcclxuICAgICAgLy8gICAgIDxCdXR0b24gdHlwZT1cInByaW1hcnlcIiBocmVmPVwiL1wiPlxyXG4gICAgICAvLyAgICAgICBCYWNrIEhvbWVcclxuICAgICAgLy8gICAgIDwvQnV0dG9uPlxyXG4gICAgICAvLyAgIH1cclxuICAgIC8+XHJcbiAgKTtcclxufTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IEZhbGxiYWNrQ29tcG9uZW50O1xyXG4iXSwibmFtZXMiOlsiUmVzdWx0IiwianN4IiwiX2pzeCIsIkZhbGxiYWNrQ29tcG9uZW50Iiwic3RhdHVzIiwidGl0bGUiLCJzdWJUaXRsZSJdLCJzb3VyY2VSb290IjoiIn0=
//# sourceURL=webpack-internal:///65573
`)},97679:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(97857);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(19632);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(5574);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var _common_contanst_constanst__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(86604);
/* harmony import */ var _services_fileUpload__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(96876);
/* harmony import */ var _services_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(467);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(51042);
/* harmony import */ var _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(27068);
/* harmony import */ var _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(34994);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(98138);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(45360);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(78367);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(85576);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(67294);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(85893);















var getBase64 = function getBase64(file) {
  return new Promise(function (resolve, reject) {
    var reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = function () {
      return resolve(reader.result);
    };
    reader.onerror = function (error) {
      return reject(error);
    };
  });
};
var FormUploadsPreviewable = function FormUploadsPreviewable(_ref) {
  var formItemName = _ref.formItemName,
    fileLimit = _ref.fileLimit,
    label = _ref.label,
    initialImages = _ref.initialImages,
    docType = _ref.docType,
    isRequired = _ref.isRequired,
    hideUploadButton = _ref.hideUploadButton,
    hideDeleteIcon = _ref.hideDeleteIcon,
    hidePreviewIcon = _ref.hidePreviewIcon,
    _ref$uploadButtonLayo = _ref.uploadButtonLayout,
    uploadButtonLayout = _ref$uploadButtonLayo === void 0 ? 'vertical' : _ref$uploadButtonLayo,
    _ref$uploadButtonIcon = _ref.uploadButtonIcon,
    uploadButtonIcon = _ref$uploadButtonIcon === void 0 ? /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .Z, {}) : _ref$uploadButtonIcon,
    _ref$uploadButtonText = _ref.uploadButtonText,
    uploadButtonText = _ref$uploadButtonText === void 0 ? 'T\u1EA3i l\xEAn' : _ref$uploadButtonText,
    uploadButtonStyle = _ref.uploadButtonStyle,
    readOnly = _ref.readOnly;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(false),
    _useState2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_4___default()(_useState, 2),
    previewOpen = _useState2[0],
    setPreviewOpen = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(''),
    _useState4 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_4___default()(_useState3, 2),
    previewImage = _useState4[0],
    setPreviewImage = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(''),
    _useState6 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_4___default()(_useState5, 2),
    previewTitle = _useState6[0],
    setPreviewTitle = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(initialImages),
    _useState8 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_4___default()(_useState7, 2),
    imageList = _useState8[0],
    setImageList = _useState8[1];
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)([]),
    _useState10 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_4___default()(_useState9, 2),
    fileList = _useState10[0],
    setFileList = _useState10[1];
  var form = antd__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .Z.useFormInstance();
  (0,_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_12__/* .useDeepCompareEffect */ .KW)(function () {
    var listImg = (0,_services_utils__WEBPACK_IMPORTED_MODULE_7__/* .getListFileUrlFromStringV2 */ .JJ)({
      arrUrlString: initialImages
    }).map(function (url, index) {
      return {
        name: "\\u1EA2nh ".concat((index + 1).toString()),
        url: url || '',
        uid: (-index).toString(),
        status: url ? 'done' : 'error'
      };
    });
    setFileList(listImg);
  }, [initialImages]);
  var handlePreview = /*#__PURE__*/function () {
    var _ref2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee(file) {
      return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            if (!(!file.url && !file.preview)) {
              _context.next = 4;
              break;
            }
            _context.next = 3;
            return getBase64(file.originFileObj);
          case 3:
            file.preview = _context.sent;
          case 4:
            setPreviewImage(file.url || file.preview);
            setPreviewOpen(true);
            setPreviewTitle(file.name || file.url.substring(file.url.lastIndexOf('/') + 1));
          case 7:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }));
    return function handlePreview(_x) {
      return _ref2.apply(this, arguments);
    };
  }();
  var handleChange = /*#__PURE__*/function () {
    var _ref4 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee3(_ref3) {
      var newFileList, oldFileList, uploadListRes, checkUploadFailed, arrFileUrl;
      return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee3$(_context3) {
        while (1) switch (_context3.prev = _context3.next) {
          case 0:
            newFileList = _ref3.fileList;
            oldFileList = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_1___default()(fileList);
            if (!readOnly) {
              _context3.next = 4;
              break;
            }
            return _context3.abrupt("return");
          case 4:
            setFileList(newFileList);
            _context3.next = 7;
            return Promise.allSettled(newFileList.map( /*#__PURE__*/function () {
              var _ref5 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee2(item) {
                var _item$lastModified;
                return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee2$(_context2) {
                  while (1) switch (_context2.prev = _context2.next) {
                    case 0:
                      if (!item.url) {
                        _context2.next = 2;
                        break;
                      }
                      return _context2.abrupt("return", {
                        data: {
                          message: {
                            file_url: item.url.split('file_url=').at(-1)
                          }
                        }
                      });
                    case 2:
                      _context2.next = 4;
                      return (0,_services_fileUpload__WEBPACK_IMPORTED_MODULE_6__/* .uploadFile */ .cT)({
                        docType: docType || _common_contanst_constanst__WEBPACK_IMPORTED_MODULE_5__/* .DOCTYPE_ERP */ .lH.iotPlant,
                        docName: item.name + Math.random().toString(4) + ((_item$lastModified = item.lastModified) === null || _item$lastModified === void 0 ? void 0 : _item$lastModified.toString(4)),
                        file: item.originFileObj
                      });
                    case 4:
                      return _context2.abrupt("return", _context2.sent);
                    case 5:
                    case "end":
                      return _context2.stop();
                  }
                }, _callee2);
              }));
              return function (_x3) {
                return _ref5.apply(this, arguments);
              };
            }()));
          case 7:
            uploadListRes = _context3.sent;
            checkUploadFailed = uploadListRes.find(function (item) {
              return item.status === 'rejected';
            });
            if (checkUploadFailed) {
              antd__WEBPACK_IMPORTED_MODULE_13__/* ["default"] */ .ZP.error({
                content: "upload \\u1EA3nh kh\\xF4ng th\\xE0nh c\\xF4ng"
              });
              setFileList(oldFileList);
            }
            arrFileUrl = uploadListRes.reduce(function (prev, item) {
              var _item$value, _item$value2;
              return item.status === 'fulfilled' && item !== null && item !== void 0 && (_item$value = item.value) !== null && _item$value !== void 0 && (_item$value = _item$value.data) !== null && _item$value !== void 0 && (_item$value = _item$value.message) !== null && _item$value !== void 0 && _item$value.file_url ? [].concat(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_1___default()(prev), [item === null || item === void 0 || (_item$value2 = item.value) === null || _item$value2 === void 0 || (_item$value2 = _item$value2.data) === null || _item$value2 === void 0 || (_item$value2 = _item$value2.message) === null || _item$value2 === void 0 ? void 0 : _item$value2.file_url]) : prev;
            }, []).filter(function (item) {
              return typeof item === 'string';
            });
            if (arrFileUrl) {
              setImageList(arrFileUrl.join(','));
              form.setFieldValue(formItemName, arrFileUrl.join(','));
            }
          case 12:
          case "end":
            return _context3.stop();
        }
      }, _callee3);
    }));
    return function handleChange(_x2) {
      return _ref4.apply(this, arguments);
    };
  }();
  var uploadButton = /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxs)("div", {
    style: D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default()({
      display: 'flex',
      flexDirection: uploadButtonLayout === 'vertical' ? 'column' : 'row',
      alignItems: 'center',
      gap: uploadButtonLayout === 'vertical' ? '8px' : '4px'
    }, uploadButtonStyle),
    children: [uploadButtonIcon, /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)("div", {
      children: uploadButtonText
    })]
  });
  var handleCancel = function handleCancel() {
    return setPreviewOpen(false);
  };
  var normFile = function normFile(e) {
    if (Array.isArray(e)) {
      return e;
    }
    return e && e.fileList;
  };
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.Fragment, {
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_14__/* .ProForm */ .A.Item, {
      name: formItemName,
      initialValue: imageList,
      label: label,
      rules: D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_1___default()(isRequired ? [{
        required: isRequired
      }] : []),
      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(antd__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .Z, {
        listType: "picture-card",
        fileList: fileList,
        onPreview: handlePreview,
        maxCount: fileLimit,
        onChange: handleChange,
        multiple: true,
        showUploadList: {
          showRemoveIcon: !hideDeleteIcon,
          showPreviewIcon: !hidePreviewIcon
        },
        children: !hideUploadButton && (fileList.length >= fileLimit ? null : uploadButton)
      })
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(antd__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .Z, {
      open: previewOpen,
      title: previewTitle,
      footer: null,
      onCancel: handleCancel,
      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)("img", {
        alt: "example",
        style: {
          width: '100%'
        },
        src: previewImage
      })
    })]
  });
};
/* harmony default export */ __webpack_exports__.Z = (FormUploadsPreviewable);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///97679
`)},89286:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(97857);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectWithoutProperties_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(13769);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectWithoutProperties_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectWithoutProperties_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(6110);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(7837);
/* harmony import */ var antd_use_styles__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(38513);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(67294);
/* harmony import */ var react_router_dom__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(96974);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(85893);


var _excluded = ["extraPage", "fallback", "children"];






var useStyles = (0,antd_use_styles__WEBPACK_IMPORTED_MODULE_3__/* .createStyles */ .k)(function (_ref) {
  var token = _ref.token;
  return {
    link: {
      color: 'inherit',
      '&:hover': {
        color: token.colorPrimaryTextHover
      }
    }
  };
});
var PageContainerTabsWithPath = function PageContainerTabsWithPath(_ref2) {
  var _matches$params, _tabActive$key;
  var tabItems = _ref2.tabItems,
    generalPath = _ref2.generalPath,
    onTabChange = _ref2.onTabChange,
    defaultTabActive = _ref2.defaultTabActive;
  var genUrl = function genUrl(path) {
    return "".concat(generalPath, "/").concat(path);
  };
  var styles = useStyles();
  var matches = (0,react_router_dom__WEBPACK_IMPORTED_MODULE_6__/* .useMatch */ .bS)(genUrl('*'));
  /**
   * matches : { *: "log/detail/ciY7e6Z7Kkv_iQR-HntBI"}
   * ['log', 'detail', 'ciY7e6Z7Kkv_iQR-HntBI']
   */
  var urlTabActive = (matches === null || matches === void 0 || (_matches$params = matches.params) === null || _matches$params === void 0 || (_matches$params = _matches$params['*']) === null || _matches$params === void 0 || (_matches$params = _matches$params.split('/').filter(function (segment) {
    return segment !== '';
  })) === null || _matches$params === void 0 ? void 0 : _matches$params[0]) || defaultTabActive;
  var tabItemsFormat = (0,react__WEBPACK_IMPORTED_MODULE_4__.useMemo)(function () {
    return tabItems === null || tabItems === void 0 ? void 0 : tabItems.map(function (_ref3) {
      var extraPage = _ref3.extraPage,
        fallback = _ref3.fallback,
        children = _ref3.children,
        rest = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectWithoutProperties_js__WEBPACK_IMPORTED_MODULE_1___default()(_ref3, _excluded);
      return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default()({}, rest), {}, {
        tab: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_2__.Link, {
          to: genUrl(rest.key),
          className: styles.link,
          children: rest.tab
        })
      });
    });
  }, [styles, genUrl]);
  var tabActive = (tabItems === null || tabItems === void 0 ? void 0 : tabItems.find(function (item) {
    return item.key === urlTabActive;
  })) || (tabItems === null || tabItems === void 0 ? void 0 : tabItems[0]);
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_7__/* .PageContainer */ ._z, {
    tabList: tabItemsFormat,
    tabActiveKey: tabActive === null || tabActive === void 0 || (_tabActive$key = tabActive.key) === null || _tabActive$key === void 0 ? void 0 : _tabActive$key.toString(),
    onTabChange: onTabChange,
    extra: tabActive === null || tabActive === void 0 ? void 0 : tabActive.extraPage,
    childrenContentStyle: {
      padding: '0px 32px'
    },
    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(react__WEBPACK_IMPORTED_MODULE_4__.Suspense, {
      fallback: tabActive === null || tabActive === void 0 ? void 0 : tabActive.fallback,
      children: (tabActive === null || tabActive === void 0 ? void 0 : tabActive.children) || /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_2__.Outlet, {})
    })
  });
};
/* harmony default export */ __webpack_exports__.Z = (PageContainerTabsWithPath);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///89286
`)},57131:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return /* binding */ WarehouseManagementV3_Warehouse; }
});

// EXTERNAL MODULE: ./src/components/FallbackContent/index.tsx
var FallbackContent = __webpack_require__(65573);
// EXTERNAL MODULE: ./src/components/PageContainerTabsWithPath/index.tsx
var PageContainerTabsWithPath = __webpack_require__(89286);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-skeleton/es/components/Descriptions/index.js
var Descriptions = __webpack_require__(44688);
// EXTERNAL MODULE: ./src/.umi-production/exports.ts + 34 modules
var _umi_production_exports = __webpack_require__(7837);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/objectSpread2.js
var objectSpread2 = __webpack_require__(97857);
var objectSpread2_default = /*#__PURE__*/__webpack_require__.n(objectSpread2);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/slicedToArray.js
var slicedToArray = __webpack_require__(5574);
var slicedToArray_default = /*#__PURE__*/__webpack_require__.n(slicedToArray);
// EXTERNAL MODULE: ./src/services/stock/warehouse.ts
var warehouse = __webpack_require__(18327);
// EXTERNAL MODULE: ./src/services/utils.ts
var utils = __webpack_require__(467);
// EXTERNAL MODULE: ./node_modules/antd/es/col/index.js
var col = __webpack_require__(15746);
// EXTERNAL MODULE: ./node_modules/antd/es/space/index.js + 2 modules
var space = __webpack_require__(42075);
// EXTERNAL MODULE: ./node_modules/antd/es/card/index.js + 3 modules
var card = __webpack_require__(76216);
// EXTERNAL MODULE: ./node_modules/antd/es/row/index.js
var row = __webpack_require__(71230);
// EXTERNAL MODULE: ./node_modules/antd/es/input/index.js + 4 modules
var input = __webpack_require__(96365);
// EXTERNAL MODULE: ./node_modules/antd/es/list/index.js + 3 modules
var list = __webpack_require__(2487);
// EXTERNAL MODULE: ./node_modules/lodash/lodash.js
var lodash = __webpack_require__(96486);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/regeneratorRuntime.js
var regeneratorRuntime = __webpack_require__(15009);
var regeneratorRuntime_default = /*#__PURE__*/__webpack_require__.n(regeneratorRuntime);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(99289);
var asyncToGenerator_default = /*#__PURE__*/__webpack_require__.n(asyncToGenerator);
// EXTERNAL MODULE: ./src/common/contanst/constanst.ts
var constanst = __webpack_require__(86604);
// EXTERNAL MODULE: ./src/services/fileUpload.ts
var services_fileUpload = __webpack_require__(96876);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/PlusOutlined.js
var PlusOutlined = __webpack_require__(51042);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/layouts/ModalForm/index.js
var ModalForm = __webpack_require__(37476);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/Text/index.js
var Text = __webpack_require__(5966);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/TextArea/index.js
var TextArea = __webpack_require__(90672);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/layouts/ProForm/index.js + 3 modules
var ProForm = __webpack_require__(34994);
// EXTERNAL MODULE: ./node_modules/antd/es/form/index.js + 18 modules
var es_form = __webpack_require__(98138);
// EXTERNAL MODULE: ./node_modules/antd/es/message/index.js
var message = __webpack_require__(45360);
// EXTERNAL MODULE: ./node_modules/antd/es/button/index.js + 9 modules
var es_button = __webpack_require__(14726);
// EXTERNAL MODULE: ./node_modules/antd/es/upload/index.js + 26 modules
var upload = __webpack_require__(78367);
// EXTERNAL MODULE: ./node_modules/antd/es/modal/index.js + 1 modules
var modal = __webpack_require__(85576);
// EXTERNAL MODULE: ./node_modules/antd-img-crop/dist/antd-img-crop.esm.js + 4 modules
var antd_img_crop_esm = __webpack_require__(9146);
// EXTERNAL MODULE: ./node_modules/react/jsx-runtime.js
var jsx_runtime = __webpack_require__(85893);
;// CONCATENATED MODULE: ./src/pages/WarehouseManagementV3/Warehouse/components/CreateWarehouse.tsx

















var getBase64 = function getBase64(file) {
  return new Promise(function (resolve, reject) {
    var reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = function () {
      return resolve(reader.result);
    };
    reader.onerror = function (error) {
      return reject(error);
    };
  });
};
var CreateWarehouse = function CreateWarehouse(_ref) {
  var onSuccess = _ref.onSuccess;
  var _Form$useForm = es_form/* default */.Z.useForm(),
    _Form$useForm2 = slicedToArray_default()(_Form$useForm, 1),
    form = _Form$useForm2[0];
  var _useState = (0,react.useState)(false),
    _useState2 = slicedToArray_default()(_useState, 2),
    submitting = _useState2[0],
    setSubmitting = _useState2[1];
  var _useState3 = (0,react.useState)(false),
    _useState4 = slicedToArray_default()(_useState3, 2),
    previewOpen = _useState4[0],
    setPreviewOpen = _useState4[1];
  var _useState5 = (0,react.useState)(''),
    _useState6 = slicedToArray_default()(_useState5, 2),
    previewImage = _useState6[0],
    setPreviewImage = _useState6[1];
  var _useState7 = (0,react.useState)(''),
    _useState8 = slicedToArray_default()(_useState7, 2),
    previewTitle = _useState8[0],
    setPreviewTitle = _useState8[1];
  var handlePreview = /*#__PURE__*/function () {
    var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(file) {
      return regeneratorRuntime_default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            if (!(!file.url && !file.preview)) {
              _context.next = 4;
              break;
            }
            _context.next = 3;
            return getBase64(file.originFileObj);
          case 3:
            file.preview = _context.sent;
          case 4:
            setPreviewImage(file.url || file.preview);
            setPreviewOpen(true);
            setPreviewTitle(file.name || file.url.substring(file.url.lastIndexOf('/') + 1));
          case 7:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }));
    return function handlePreview(_x) {
      return _ref2.apply(this, arguments);
    };
  }();
  var handleChange = function handleChange(_ref3) {
    var newFileList = _ref3.fileList;
    form.setFieldValue('image', newFileList);
  };
  var handleCancel = function handleCancel() {
    return setPreviewOpen(false);
  };
  var handleFinish = /*#__PURE__*/function () {
    var _ref4 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee3(values) {
      var name, image, description, img;
      return regeneratorRuntime_default()().wrap(function _callee3$(_context3) {
        while (1) switch (_context3.prev = _context3.next) {
          case 0:
            setSubmitting(true);
            name = values.name, image = values.image, description = values.description;
            img = image ? image.at(0) : null;
            _context3.next = 5;
            return (0,warehouse/* createWarehouse */.I7)({
              label: name,
              description: description
            }).then( /*#__PURE__*/function () {
              var _ref5 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2(newWarehouse) {
                var uploadStatus;
                return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
                  while (1) switch (_context2.prev = _context2.next) {
                    case 0:
                      if (newWarehouse) {
                        _context2.next = 2;
                        break;
                      }
                      throw new Error('L\u1ED7i h\u1EC7 th\u1ED1ng khi t\u1EA1o kho m\u1EDBi');
                    case 2:
                      if (!img) {
                        _context2.next = 10;
                        break;
                      }
                      _context2.next = 5;
                      return (0,services_fileUpload/* uploadFile */.cT)({
                        docType: constanst/* DOCTYPE_ERP */.lH.iotWarehouse,
                        docName: newWarehouse.name || (img === null || img === void 0 ? void 0 : img.fileName) || (0,lodash.uniqueId)(),
                        file: img === null || img === void 0 ? void 0 : img.originFileObj
                      });
                    case 5:
                      uploadStatus = _context2.sent;
                      if (uploadStatus.data) {
                        _context2.next = 8;
                        break;
                      }
                      throw new Error('L\u1ED7i trong qu\xE1 tr\xECnh up \u1EA3nh');
                    case 8:
                      _context2.next = 10;
                      return (0,warehouse/* updateWarehouse */.qL)({
                        name: newWarehouse.name,
                        label: newWarehouse.label,
                        image: uploadStatus.data.message.file_url
                      });
                    case 10:
                      _context2.next = 12;
                      return onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess();
                    case 12:
                      message/* default */.ZP.success('T\u1EA1o kho th\xE0nh c\xF4ng');
                    case 13:
                    case "end":
                      return _context2.stop();
                  }
                }, _callee2);
              }));
              return function (_x3) {
                return _ref5.apply(this, arguments);
              };
            }())["catch"](function (error) {
              // message.error({ content: \`L\u1ED7i khi t\u1EA1o kho: \${error.message}\`, duration: 5 });
            });
          case 5:
            setSubmitting(false);
            return _context3.abrupt("return", true);
          case 7:
          case "end":
            return _context3.stop();
        }
      }, _callee3);
    }));
    return function handleFinish(_x2) {
      return _ref4.apply(this, arguments);
    };
  }();
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  return /*#__PURE__*/(0,jsx_runtime.jsxs)(ModalForm/* ModalForm */.Y, {
    width: "400px",
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "warehouse-management.warehouse-list.add-warehouse"
    }),
    trigger: /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
      type: "primary",
      icon: /*#__PURE__*/(0,jsx_runtime.jsx)(PlusOutlined/* default */.Z, {}),
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
        id: "warehouse-management.warehouse-list.add-warehouse"
      })
    }),
    autoFocusFirstInput: true,
    modalProps: {
      destroyOnClose: true
    },
    form: form,
    submitter: {
      render: function render(props, defaultDoms) {
        return [/*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
          onClick: function onClick() {
            props.submit();
          },
          type: "primary",
          style: {
            width: '100%'
          },
          loading: submitting,
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
            id: 'action.save'
          })
        }, "ok")];
      }
    },
    submitTimeout: 2000,
    onFinish: handleFinish,
    children: [/*#__PURE__*/(0,jsx_runtime.jsx)(Text/* default */.Z, {
      rules: [{
        required: true,
        message: "Xin vui l\\xF2ng \\u0111i\\u1EC1n t\\xEAn kho"
      }],
      required: true,
      width: "lg",
      name: "name",
      label: formatMessage({
        id: 'storage-management.storage-detail.storage_name'
      })
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(TextArea/* default */.Z, {
      style: {
        width: '100%'
      },
      fieldProps: {
        autoSize: {
          minRows: 3
        }
      },
      name: 'description',
      label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
        id: 'common.form.description'
      })
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(ProForm/* ProForm */.A.Item, {
      name: "image",
      label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
        id: 'common.form.image'
      }),
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(antd_img_crop_esm/* default */.Z, {
        rotationSlider: true,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(upload/* default */.Z, {
          listType: "picture-card",
          onChange: handleChange,
          onPreview: handlePreview,
          maxCount: 1,
          accept: "image/x-png,image/jpeg,image/png",
          children: "Upload \\u1EA3nh"
        })
      })
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(modal/* default */.Z, {
      open: previewOpen,
      title: previewTitle,
      footer: null,
      onCancel: handleCancel,
      children: /*#__PURE__*/(0,jsx_runtime.jsx)("img", {
        alt: "example",
        style: {
          width: '100%'
        },
        src: previewImage
      })
    })]
  });
};
/* harmony default export */ var components_CreateWarehouse = (CreateWarehouse);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/DeleteOutlined.js
var DeleteOutlined = __webpack_require__(82061);
// EXTERNAL MODULE: ./node_modules/antd/es/typography/index.js + 18 modules
var typography = __webpack_require__(25514);
// EXTERNAL MODULE: ./node_modules/antd/es/app/index.js + 1 modules
var app = __webpack_require__(31418);
// EXTERNAL MODULE: ./node_modules/antd/es/popconfirm/index.js + 2 modules
var popconfirm = __webpack_require__(86738);
// EXTERNAL MODULE: ./node_modules/antd/es/avatar/index.js + 4 modules
var avatar = __webpack_require__(7134);
// EXTERNAL MODULE: ./node_modules/antd/es/card/Meta.js
var Meta = __webpack_require__(46256);
// EXTERNAL MODULE: ./src/common/contanst/img.ts
var img = __webpack_require__(13490);
// EXTERNAL MODULE: ./src/utils/file.ts
var file = __webpack_require__(80320);
// EXTERNAL MODULE: ./src/components/FormUploadsPreviewable/index.tsx
var FormUploadsPreviewable = __webpack_require__(97679);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/EditOutlined.js
var EditOutlined = __webpack_require__(47389);
;// CONCATENATED MODULE: ./src/pages/WarehouseManagementV3/Warehouse/components/EditWarehouse.tsx















var EditWarehouse = function EditWarehouse(_ref) {
  var onSuccess = _ref.onSuccess,
    data = _ref.data;
  var _Form$useForm = es_form/* default */.Z.useForm(),
    _Form$useForm2 = slicedToArray_default()(_Form$useForm, 1),
    form = _Form$useForm2[0];
  var _useState = (0,react.useState)(false),
    _useState2 = slicedToArray_default()(_useState, 2),
    submitting = _useState2[0],
    setSubmitting = _useState2[1];
  var _App$useApp = app/* default */.Z.useApp(),
    message = _App$useApp.message;
  var handleFinish = /*#__PURE__*/function () {
    var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2(values) {
      var label, image, description;
      return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            setSubmitting(true);
            label = values.label, image = values.image, description = values.description;
            _context2.next = 4;
            return (0,warehouse/* updateWarehouse */.qL)({
              name: data.name,
              label: label,
              description: description,
              image: image
            }).then( /*#__PURE__*/function () {
              var _ref3 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(newStorage) {
                return regeneratorRuntime_default()().wrap(function _callee$(_context) {
                  while (1) switch (_context.prev = _context.next) {
                    case 0:
                      if (newStorage) {
                        _context.next = 2;
                        break;
                      }
                      throw new Error('L\u1ED7i h\u1EC7 th\u1ED1ng khi c\u1EADp nh\u1EADt kho');
                    case 2:
                      _context.next = 4;
                      return onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess();
                    case 4:
                      message.success('C\u1EADp nh\u1EADt kho th\xE0nh c\xF4ng');
                    case 5:
                    case "end":
                      return _context.stop();
                  }
                }, _callee);
              }));
              return function (_x2) {
                return _ref3.apply(this, arguments);
              };
            }())["catch"](function (error) {
              message.error({
                content: "L\\u1ED7i khi c\\u1EADp nh\\u1EADt kho: ".concat(error.message),
                duration: 5
              });
            });
          case 4:
            setSubmitting(false);
            return _context2.abrupt("return", true);
          case 6:
          case "end":
            return _context2.stop();
        }
      }, _callee2);
    }));
    return function handleFinish(_x) {
      return _ref2.apply(this, arguments);
    };
  }();
  return /*#__PURE__*/(0,jsx_runtime.jsxs)(ModalForm/* ModalForm */.Y, {
    width: "400px",
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "warehouse-management.warehouse-list.edit-warehouse",
      defaultMessage: "Edit Storage"
    }),
    trigger: /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
      type: "text",
      icon: /*#__PURE__*/(0,jsx_runtime.jsx)(EditOutlined/* default */.Z, {})
    })
    // trigger={<EditOutlined />}
    ,
    autoFocusFirstInput: true,
    modalProps: {
      destroyOnClose: true
    },
    form: form,
    submitter: {
      render: function render(props, defaultDoms) {
        return [/*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
          onClick: function onClick() {
            props.submit();
          },
          type: "primary",
          style: {
            width: '100%'
          },
          loading: submitting,
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
            id: "action.save",
            defaultMessage: "Save"
          })
        }, "ok")];
      }
    },
    submitTimeout: 2000,
    onFinish: handleFinish,
    children: [/*#__PURE__*/(0,jsx_runtime.jsx)(Text/* default */.Z, {
      rules: [{
        required: true,
        message: "Xin vui l\\xF2ng \\u0111i\\u1EC1n t\\xEAn kho"
      }],
      initialValue: data.label,
      required: true,
      width: "lg",
      name: "label",
      label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
        id: "warehouse-management.warehouse-name"
      })
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(TextArea/* default */.Z, {
      initialValue: data.description,
      style: {
        width: '100%'
      },
      fieldProps: {
        autoSize: {
          minRows: 3
        }
      },
      name: 'description',
      label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
        id: "common.form.description"
      })
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(FormUploadsPreviewable/* default */.Z, {
      fileLimit: 1,
      initialImages: data.image || '',
      label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
        id: "common.form.image"
      }),
      formItemName: 'image',
      docType: constanst/* DOCTYPE_ERP */.lH.iotWarehouse
    })]
  });
};
/* harmony default export */ var components_EditWarehouse = (EditWarehouse);
;// CONCATENATED MODULE: ./src/pages/WarehouseManagementV3/Warehouse/components/GeneralWarehouseCard.tsx












var GeneralWarehouseCard_Text = typography/* default */.Z.Text;
var GeneralWarehouseCard = function GeneralWarehouseCard(_ref) {
  var label = _ref.label,
    description = _ref.description,
    image = _ref.image,
    onDeleteSuccess = _ref.onDeleteSuccess,
    onEditSuccess = _ref.onEditSuccess,
    name = _ref.name;
  var _App$useApp = app/* default */.Z.useApp(),
    message = _App$useApp.message;
  function handleDelete() {
    return _handleDelete.apply(this, arguments);
  }
  function _handleDelete() {
    _handleDelete = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
      return regeneratorRuntime_default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _context.prev = 0;
            if (!name) {
              _context.next = 6;
              break;
            }
            _context.next = 4;
            return (0,warehouse/* deleteWarehouse */.x0)({
              name: name,
              label: label
            });
          case 4:
            message.success("Success in deleting warehouse ".concat(label));
            onDeleteSuccess === null || onDeleteSuccess === void 0 || onDeleteSuccess();
          case 6:
            _context.next = 11;
            break;
          case 8:
            _context.prev = 8;
            _context.t0 = _context["catch"](0);
            message.error('Error when deleting warehouse.');
          case 11:
          case "end":
            return _context.stop();
        }
      }, _callee, null, [[0, 8]]);
    }));
    return _handleDelete.apply(this, arguments);
  }
  function handleEdit() {
    message.error('Function not yet implemented.');
  }
  var access = (0,_umi_production_exports.useAccess)();
  var canUpdateStorage = access.canUpdateInStorageNewManagement();
  var canDeleteStorage = access.canDeleteInStorageNewManagement();
  var canReadCategoryInventory = access.canAccessPageCategoryInventoryManagement();
  var canReadProductInventory = access.canAccessPageProductInventoryManagement();
  return /*#__PURE__*/(0,jsx_runtime.jsx)(card/* default */.Z, {
    size: "default"
    // style={{ maxHeight: 250 }}
    ,
    hoverable: true,
    actions: [/*#__PURE__*/(0,jsx_runtime.jsx)(space/* default */.Z, {
      direction: "vertical",
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(jsx_runtime.Fragment, {
        children: canUpdateStorage && /*#__PURE__*/(0,jsx_runtime.jsx)(components_EditWarehouse, {
          data: {
            label: label,
            description: description,
            image: image,
            name: name
          },
          onSuccess: onEditSuccess
        }, 'edit')
      })
    }, 'edit'), /*#__PURE__*/(0,jsx_runtime.jsx)(space/* default */.Z, {
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(jsx_runtime.Fragment, {
        children: canDeleteStorage && /*#__PURE__*/(0,jsx_runtime.jsx)(popconfirm/* default */.Z, {
          title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
            id: 'warehouse-management.warehouse-list.delete-warehouse'
          }),
          description: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
            id: 'action.verify'
          }),
          onConfirm: function onConfirm() {
            return handleDelete();
          },
          onPopupClick: function onPopupClick(e) {
            e.stopPropagation();
          },
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
            type: "text",
            icon: /*#__PURE__*/(0,jsx_runtime.jsx)(DeleteOutlined/* default */.Z, {}),
            onClick: function onClick(e) {
              e.stopPropagation();
            }
          }, "delete")
        }, "delete")
      })
    }, 'delete')],
    children: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.Link, {
      to: "/warehouse-management-v3/inventory",
      onClick: function onClick() {
        localStorage.setItem('selectedWarehouse', name);
      },
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(Meta/* default */.Z, {
        style: {
          minHeight: 100
        },
        avatar: /*#__PURE__*/(0,jsx_runtime.jsx)(avatar/* default */.C, {
          shape: "square",
          size: 54,
          src: image ? (0,file/* genDownloadUrl */.h)(image) : img/* DEFAULT_FALLBACK_IMG */.W
        }),
        title: /*#__PURE__*/(0,jsx_runtime.jsx)(GeneralWarehouseCard_Text, {
          style: {
            whiteSpace: 'normal'
          },
          children: label
        }),
        description: description
      })
    })
  });
};
/* harmony default export */ var components_GeneralWarehouseCard = (GeneralWarehouseCard);
;// CONCATENATED MODULE: ./src/pages/WarehouseManagementV3/Warehouse/components/WarehouseList.tsx














var WarehouseList = function WarehouseList() {
  var _useState = (0,react.useState)(true),
    _useState2 = slicedToArray_default()(_useState, 2),
    firstLoad = _useState2[0],
    setFirstLoad = _useState2[1];
  var _useState3 = (0,react.useState)([]),
    _useState4 = slicedToArray_default()(_useState3, 2),
    warehouseList = _useState4[0],
    setWarehouseList = _useState4[1];
  var _useState5 = (0,react.useState)([]),
    _useState6 = slicedToArray_default()(_useState5, 2),
    filteredWarehouse = _useState6[0],
    setFilteredWarehouse = _useState6[1];
  var _useState7 = (0,react.useState)(''),
    _useState8 = slicedToArray_default()(_useState7, 2),
    searchQuery = _useState8[0],
    setSearchQuery = _useState8[1];
  var _useRequest = (0,_umi_production_exports.useRequest)(function () {
      return (0,warehouse/* getWarehouseList */.Aq)({
        order_by: 'label ASC'
      });
    }, {
      onSuccess: function onSuccess(responseData) {
        if (responseData) {
          //filter out the warehouse that has name "Work In Progress - V"
          responseData = responseData.filter(function (warehouse) {
            return warehouse.name !== 'Work In Progress - V';
          });
          setWarehouseList(responseData);
          setFirstLoad(false);
        }
      },
      onError: function onError(error) {
        setFirstLoad(false);
      }
    }),
    data = _useRequest.data,
    loading = _useRequest.loading,
    refresh = _useRequest.refresh;
  (0,react.useEffect)(function () {
    if (warehouseList.length > 0) {
      var filtered = warehouseList.filter(function (obj) {
        return (0,utils/* toLowerCaseNonAccentVietnamese */.HO)(obj.label || '').includes((0,utils/* toLowerCaseNonAccentVietnamese */.HO)(searchQuery));
      });
      setFilteredWarehouse(filtered);
    }
  }, [warehouseList, searchQuery]);
  var debounceSearch = (0,lodash.debounce)(function (query) {
    setSearchQuery(query);
  }, 400);
  var handleSearch = function handleSearch(e) {
    var query = e.target.value;
    debounceSearch(query);
  };
  var access = (0,_umi_production_exports.useAccess)();
  var canCreateStorage = access.canCreateInStorageNewManagement();
  return /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.Access, {
    accessible: access.canAccessPageStorageNewManagement(),
    fallback: /*#__PURE__*/(0,jsx_runtime.jsx)(FallbackContent/* default */.Z, {}),
    children: /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
      children: /*#__PURE__*/(0,jsx_runtime.jsxs)(space/* default */.Z, {
        direction: "vertical",
        size: "middle",
        style: {
          display: 'flex'
        },
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)(card/* default */.Z, {
          bordered: true,
          children: /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
            justify: "space-between",
            gutter: 16,
            align: "middle",
            children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
              span: 8,
              flex: "1 0 25%",
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(input/* default */.Z, {
                addonBefore: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
                  id: 'warehouse-management.warehouse-list.warehouse-name'
                }),
                onChange: handleSearch
              })
            }), canCreateStorage && /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
              span: 8,
              style: {
                textAlign: 'right'
              },
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(components_CreateWarehouse, {
                onSuccess: refresh
              })
            })]
          })
        }), /*#__PURE__*/(0,jsx_runtime.jsx)(space/* default */.Z, {
          direction: "vertical",
          size: "middle",
          style: {
            display: 'flex'
          },
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(react.Suspense, {
            fallback: /*#__PURE__*/(0,jsx_runtime.jsx)(Descriptions/* TableSkeleton */.hM, {
              active: true
            }),
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(list/* default */.Z, {
              grid: {
                column: 3,
                gutter: 10
              },
              dataSource: filteredWarehouse.length > 0 ? filteredWarehouse : data,
              renderItem: function renderItem(item) {
                return /*#__PURE__*/(0,jsx_runtime.jsx)(list/* default */.Z.Item, {
                  children: /*#__PURE__*/(0,jsx_runtime.jsx)(components_GeneralWarehouseCard, objectSpread2_default()(objectSpread2_default()({}, item), {}, {
                    onDeleteSuccess: refresh,
                    onEditSuccess: refresh
                  }))
                });
              }
            })
          })
        })]
      })
    })
  });
};
/* harmony default export */ var components_WarehouseList = (WarehouseList);
;// CONCATENATED MODULE: ./src/pages/WarehouseManagementV3/Warehouse/index.tsx







var Warehouse = function Warehouse() {
  var access = (0,_umi_production_exports.useAccess)();
  return /*#__PURE__*/(0,jsx_runtime.jsxs)(_umi_production_exports.Access, {
    accessible: access.canAccessPageStorageNewManagement(),
    fallback: /*#__PURE__*/(0,jsx_runtime.jsx)(FallbackContent/* default */.Z, {}),
    children: [' ', /*#__PURE__*/(0,jsx_runtime.jsx)(PageContainerTabsWithPath/* default */.Z, {
      tabItems: [{
        tab: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
          id: "warehouse-management.warehouse-list.manage-warehouse",
          defaultMessage: "Storage List"
        }),
        key: 'details',
        fallback: /*#__PURE__*/(0,jsx_runtime.jsx)(Descriptions/* DescriptionsSkeleton */.Yk, {
          active: true
        }),
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(components_WarehouseList, {})
      }],
      generalPath: "/warehouse-management/warehouse-list"
    })]
  });
};
/* harmony default export */ var WarehouseManagementV3_Warehouse = (Warehouse);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///57131
`)},96876:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   cT: function() { return /* binding */ uploadFile; },
/* harmony export */   uy: function() { return /* binding */ uploadFileHome; }
/* harmony export */ });
/* unused harmony export removeFileAttachment */
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(7837);
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(467);




var uploadFile = /*#__PURE__*/function () {
  var _ref = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee(data) {
    var formData, res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          formData = new FormData();
          formData.append('doctype', data.docType || '');
          formData.append('docname', data.docName || '');
          formData.append('folder', data.folder || 'Home/Attachments');
          formData.append('is_private', data.isPrivate || '1');
          formData.append('optimize', data.optimize || '0');
          formData.append('file', data.file);
          _context.next = 9;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/file/upload'), {
            method: 'POST',
            data: formData
          });
        case 9:
          res = _context.sent;
          return _context.abrupt("return", {
            data: res.result
          });
        case 11:
        case "end":
          return _context.stop();
      }
    }, _callee);
  }));
  return function uploadFile(_x) {
    return _ref.apply(this, arguments);
  };
}();
var uploadFileHome = /*#__PURE__*/function () {
  var _ref2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee2(data) {
    var formData, res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          formData = new FormData();
          formData.append('folder', data.folder || 'Home/Attachments');
          formData.append('is_private', data.isPrivate || '0');
          formData.append('is_home_folder', data.isPrivate || '1');
          formData.append('doctype', data.docType || '');
          formData.append('docname', data.docName || '');
          formData.append('optimize', data.optimize || '0');
          formData.append('file', data.file);
          _context2.next = 10;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/file/upload'), {
            method: 'POST',
            data: formData
          });
        case 10:
          res = _context2.sent;
          return _context2.abrupt("return", {
            data: res.result
          });
        case 12:
        case "end":
          return _context2.stop();
      }
    }, _callee2);
  }));
  return function uploadFileHome(_x2) {
    return _ref2.apply(this, arguments);
  };
}();
var removeFileAttachment = /*#__PURE__*/(/* unused pure expression or super */ null && (function () {
  var _ref3 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee3(data) {
    var res;
    return _regeneratorRuntime().wrap(function _callee3$(_context3) {
      while (1) switch (_context3.prev = _context3.next) {
        case 0:
          _context3.next = 2;
          return request(generateAPIPath('api/v2/file'), {
            method: 'DELETE',
            data: data
          });
        case 2:
          res = _context3.sent;
          return _context3.abrupt("return", {
            data: res.result
          });
        case 4:
        case "end":
          return _context3.stop();
      }
    }, _callee3);
  }));
  return function removeFileAttachment(_x3) {
    return _ref3.apply(this, arguments);
  };
}()));//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///96876
`)},18327:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Aq: function() { return /* binding */ getWarehouseList; },
/* harmony export */   I7: function() { return /* binding */ createWarehouse; },
/* harmony export */   qL: function() { return /* binding */ updateWarehouse; },
/* harmony export */   x0: function() { return /* binding */ deleteWarehouse; }
/* harmony export */ });
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(97857);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(7837);
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(467);





var handleError = function handleError(error) {
  console.log("Error in services/warehouse: \\n".concat(error));
  throw error;
};
var CRUD_PATH = {
  READ: 'warehouse',
  CREATE: 'warehouse',
  UPDATE: 'warehouse',
  DELETE: 'warehouse'
};
var getWarehouseList = /*#__PURE__*/function () {
  var _ref = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.prev = 0;
          _context.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)("api/v2/".concat(CRUD_PATH.READ)), {
            method: 'GET',
            params: D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({
              page: 1,
              size: 100
            }, (0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .getParamsReqList */ .vj)(params))
          });
        case 3:
          res = _context.sent;
          return _context.abrupt("return", {
            data: res.result.data || []
          });
        case 7:
          _context.prev = 7;
          _context.t0 = _context["catch"](0);
          return _context.abrupt("return", {
            data: []
          });
        case 10:
        case "end":
          return _context.stop();
      }
    }, _callee, null, [[0, 7]]);
  }));
  return function getWarehouseList(_x) {
    return _ref.apply(this, arguments);
  };
}();
function createWarehouse(_x2) {
  return _createWarehouse.apply(this, arguments);
}
function _createWarehouse() {
  _createWarehouse = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee2(newStorage) {
    var result;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          _context2.prev = 0;
          _context2.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)("api/v2/".concat(CRUD_PATH.CREATE)), {
            method: 'POST',
            data: D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({}, newStorage)
          });
        case 3:
          result = _context2.sent;
          return _context2.abrupt("return", result.result.data);
        case 7:
          _context2.prev = 7;
          _context2.t0 = _context2["catch"](0);
          handleError(_context2.t0);
        case 10:
        case "end":
          return _context2.stop();
      }
    }, _callee2, null, [[0, 7]]);
  }));
  return _createWarehouse.apply(this, arguments);
}
function updateWarehouse(_x3) {
  return _updateWarehouse.apply(this, arguments);
}
function _updateWarehouse() {
  _updateWarehouse = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee3(updatedWarehouse) {
    var result;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee3$(_context3) {
      while (1) switch (_context3.prev = _context3.next) {
        case 0:
          _context3.prev = 0;
          _context3.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)("api/v2/".concat(CRUD_PATH.UPDATE)), {
            method: 'PUT',
            data: D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({}, updatedWarehouse)
          });
        case 3:
          result = _context3.sent;
          return _context3.abrupt("return", result.result.data);
        case 7:
          _context3.prev = 7;
          _context3.t0 = _context3["catch"](0);
          handleError(_context3.t0);
        case 10:
        case "end":
          return _context3.stop();
      }
    }, _callee3, null, [[0, 7]]);
  }));
  return _updateWarehouse.apply(this, arguments);
}
function deleteWarehouse(_x4) {
  return _deleteWarehouse.apply(this, arguments);
}
function _deleteWarehouse() {
  _deleteWarehouse = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee4(_ref2) {
    var name, label, result;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee4$(_context4) {
      while (1) switch (_context4.prev = _context4.next) {
        case 0:
          name = _ref2.name, label = _ref2.label;
          _context4.prev = 1;
          _context4.next = 4;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)("api/v2/".concat(CRUD_PATH.UPDATE)), {
            method: 'PUT',
            data: {
              name: name,
              label: label,
              is_deleted: 1
            }
          });
        case 4:
          result = _context4.sent;
          return _context4.abrupt("return", result.result.data);
        case 8:
          _context4.prev = 8;
          _context4.t0 = _context4["catch"](1);
          handleError(_context4.t0);
        case 11:
        case "end":
          return _context4.stop();
      }
    }, _callee4, null, [[1, 8]]);
  }));
  return _deleteWarehouse.apply(this, arguments);
}//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///18327
`)},80320:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   h: function() { return /* binding */ genDownloadUrl; }
/* harmony export */ });
/* harmony import */ var _common_contanst__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(95028);

var genDownloadUrl = function genDownloadUrl(fileUrl) {
  return "".concat(_common_contanst__WEBPACK_IMPORTED_MODULE_0__/* .API_URL_DEV */ .v, "/api/v2/file/download?file_url=").concat(fileUrl);
};//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiODAzMjAuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFnRDtBQUV6QyxJQUFNQyxjQUFjLEdBQUcsU0FBakJBLGNBQWNBLENBQUlDLE9BQWUsRUFBSztFQUNqRCxVQUFBQyxNQUFBLENBQVVILGtFQUFXLHFDQUFBRyxNQUFBLENBQWtDRCxPQUFPO0FBQ2hFLENBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hbnQtZGVzaWduLXByby8uL3NyYy91dGlscy9maWxlLnRzP2FlNDQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQVBJX1VSTF9ERVYgfSBmcm9tICdAL2NvbW1vbi9jb250YW5zdCc7XHJcblxyXG5leHBvcnQgY29uc3QgZ2VuRG93bmxvYWRVcmwgPSAoZmlsZVVybDogc3RyaW5nKSA9PiB7XHJcbiAgcmV0dXJuIGAke0FQSV9VUkxfREVWfS9hcGkvdjIvZmlsZS9kb3dubG9hZD9maWxlX3VybD0ke2ZpbGVVcmx9YDtcclxufTtcclxuIl0sIm5hbWVzIjpbIkFQSV9VUkxfREVWIiwiZ2VuRG93bmxvYWRVcmwiLCJmaWxlVXJsIiwiY29uY2F0Il0sInNvdXJjZVJvb3QiOiIifQ==
//# sourceURL=webpack-internal:///80320
`)}}]);
