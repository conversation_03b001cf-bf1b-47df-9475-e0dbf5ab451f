"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[9707],{75508:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(97857);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(5574);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _common_contanst_constanst__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(86604);
/* harmony import */ var _services_fileUpload__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(96876);
/* harmony import */ var _services_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(467);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(26859);
/* harmony import */ var _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(27068);
/* harmony import */ var _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(34994);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(7837);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(98138);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(45360);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(78367);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(14726);
/* harmony import */ var nanoid__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(53416);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(67294);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(85893);
















var getBase64 = function getBase64(file) {
  return new Promise(function (resolve, reject) {
    var reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = function () {
      return resolve(reader.result);
    };
    reader.onerror = function (error) {
      return reject(error);
    };
  });
};
var getFileNameFromUrl = function getFileNameFromUrl(url) {
  // Split the URL by '/' and get the last part
  if (typeof url !== 'string') {
    var _url$toString;
    return url === null || url === void 0 || (_url$toString = url.toString) === null || _url$toString === void 0 ? void 0 : _url$toString.call(url);
  }
  var parts = url.split('/');
  var fileName = parts[parts.length - 1];

  // If there's a query string, remove it
  fileName = fileName.split('?')[0];

  // If there's a fragment, remove it
  fileName = fileName.split('#')[0];
  return fileName.split('.')[0];
};
var FormUploadFiles = function FormUploadFiles(_ref) {
  var formItemName = _ref.formItemName,
    fileLimit = _ref.fileLimit,
    label = _ref.label,
    initialImages = _ref.initialImages,
    docType = _ref.docType,
    isReadonly = _ref.isReadonly,
    onValueChange = _ref.onValueChange,
    maxSize = _ref.maxSize,
    _ref$showUploadButton = _ref.showUploadButton,
    showUploadButton = _ref$showUploadButton === void 0 ? true : _ref$showUploadButton;
  // const [previewOpen, setPreviewOpen] = useState(false);
  // const [previewImage, setPreviewImage] = useState('');
  // const [previewTitle, setPreviewTitle] = useState('');

  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(initialImages),
    _useState2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_3___default()(_useState, 2),
    imageList = _useState2[0],
    setImageList = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)([]),
    _useState4 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_3___default()(_useState3, 2),
    fileList = _useState4[0],
    setFileList = _useState4[1];
  var form = antd__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .Z.useFormInstance();
  (0,_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_11__/* .useDeepCompareEffect */ .KW)(function () {
    var listImg = (0,_services_utils__WEBPACK_IMPORTED_MODULE_6__/* .getListFileUrlFromStringV2 */ .JJ)({
      arrUrlString: initialImages
    }).map(function (url, index) {
      return {
        // name: \`File \${(index + 1).toString()}\`,
        name: getFileNameFromUrl(url),
        url: url || '',
        uid: (-index).toString(),
        status: url ? 'done' : 'error'
      };
    });
    setFileList(listImg);
  }, [initialImages]);
  // const handlePreview = async (file: UploadFile) => {
  //   if (!file.url && !file.preview) {
  //     file.preview = await getBase64(file.originFileObj as RcFile);
  //   }

  //   setPreviewImage(file.url || (file.preview as string));
  //   setPreviewOpen(true);
  //   setPreviewTitle(file.name || file.url!.substring(file.url!.lastIndexOf('/') + 1));
  // };

  var handleChange = /*#__PURE__*/function () {
    var _ref3 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee2(_ref2) {
      var newFileList, uploadListRes, newFileListRes, arrFileUrl, fileUrls;
      return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee2$(_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            newFileList = _ref2.fileList;
            _context2.next = 3;
            return Promise.allSettled(newFileList.map( /*#__PURE__*/function () {
              var _ref4 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee(item) {
                var _item$lastModified, res;
                return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee$(_context) {
                  while (1) switch (_context.prev = _context.next) {
                    case 0:
                      if (!item.url) {
                        _context.next = 2;
                        break;
                      }
                      return _context.abrupt("return", {
                        url: item.url.split('file_url=').at(-1),
                        uid: item.uid,
                        status: 'done',
                        name: item.name
                      });
                    case 2:
                      _context.prev = 2;
                      _context.next = 5;
                      return (0,_services_fileUpload__WEBPACK_IMPORTED_MODULE_5__/* .uploadFile */ .cT)({
                        docType: docType || _common_contanst_constanst__WEBPACK_IMPORTED_MODULE_4__/* .DOCTYPE_ERP */ .lH.iotPlant,
                        docName: item.name + Math.random().toString(4) + ((_item$lastModified = item.lastModified) === null || _item$lastModified === void 0 ? void 0 : _item$lastModified.toString(4)),
                        file: item.originFileObj
                      });
                    case 5:
                      res = _context.sent;
                      return _context.abrupt("return", {
                        url: res.data.message.file_url,
                        name: getFileNameFromUrl(res.data.message.file_url),
                        uid: (0,nanoid__WEBPACK_IMPORTED_MODULE_12__/* .nanoid */ .x0)(),
                        status: 'done'
                      });
                    case 9:
                      _context.prev = 9;
                      _context.t0 = _context["catch"](2);
                      antd__WEBPACK_IMPORTED_MODULE_13__/* ["default"] */ .ZP.error({
                        content: "upload file kh\\xF4ng th\\xE0nh c\\xF4ng"
                      });
                      return _context.abrupt("return", D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({}, item), {}, {
                        status: 'error'
                      }));
                    case 13:
                    case "end":
                      return _context.stop();
                  }
                }, _callee, null, [[2, 9]]);
              }));
              return function (_x2) {
                return _ref4.apply(this, arguments);
              };
            }()));
          case 3:
            uploadListRes = _context2.sent;
            // for display
            newFileListRes = uploadListRes.map(function (item) {
              return item.status === 'fulfilled' ? item.value : null;
            }).filter(function (item) {
              return item !== null;
            }); // update img path
            arrFileUrl = newFileListRes.map(function (item) {
              return item.status === 'done' ? item.url : null;
            }).filter(function (item) {
              return item !== null;
            });
            fileUrls = arrFileUrl.join(',');
            console.log("fileUrls: ", fileUrls);

            // for value

            //
            _context2.next = 10;
            return Promise.all([onValueChange === null || onValueChange === void 0 ? void 0 : onValueChange(fileUrls)]);
          case 10:
            setFileList(function () {
              return newFileListRes.map(function (item) {
                var _getListFileUrlFromSt;
                return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({}, item), {}, {
                  url: ((_getListFileUrlFromSt = (0,_services_utils__WEBPACK_IMPORTED_MODULE_6__/* .getListFileUrlFromStringV2 */ .JJ)({
                    arrUrlString: item.url
                  })) === null || _getListFileUrlFromSt === void 0 ? void 0 : _getListFileUrlFromSt[0]) || ''
                });
              });
            });
            setImageList(fileUrls);
            form === null || form === void 0 || form.setFieldValue(formItemName, fileUrls);
          case 13:
          case "end":
            return _context2.stop();
        }
      }, _callee2);
    }));
    return function handleChange(_x) {
      return _ref3.apply(this, arguments);
    };
  }();
  var _useIntl = (0,_umijs_max__WEBPACK_IMPORTED_MODULE_7__.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.Fragment, {
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_14__/* .ProForm */ .A.Item, {
      name: formItemName,
      initialValue: imageList,
      style: {
        display: 'none'
      }
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_14__/* .ProForm */ .A.Item, {
      label: label,
      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(antd__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .Z
      // listType="text"
      , {
        fileList: fileList
        // onPreview={handlePreview}
        ,
        maxCount: fileLimit,
        onChange: handleChange,
        multiple: true,
        disabled: isReadonly,
        beforeUpload: function beforeUpload(file) {
          if (maxSize) {
            var isLt5M = file.size / 1024 / 1024 <= maxSize;
            if (!isLt5M) {
              antd__WEBPACK_IMPORTED_MODULE_13__/* ["default"] */ .ZP.error(formatMessage({
                id: 'common.upload-error-file-big'
              }) + " ".concat(maxSize, "MB"));
              // alert('Image must smaller than 5MB!');
              return antd__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .Z.LIST_IGNORE;
            }
          }
          return new Promise(function (resolve, reject) {
            // check the file size - you can specify the file size you'd like here:
            if (maxSize) {
              var _isLt5M = file.size / 1024 / 1024 <= maxSize;
              if (!_isLt5M) {
                antd__WEBPACK_IMPORTED_MODULE_13__/* ["default"] */ .ZP.error(formatMessage({
                  id: 'common.upload-error-file-big'
                }) + " ".concat(maxSize, "MB"));
                // alert('Image must smaller than 5MB!');
                reject(antd__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .Z.LIST_IGNORE);
                return;
              }
            }
            resolve(true);
          });
        },
        children: showUploadButton && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(antd__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .ZP, {
          disabled: isReadonly,
          icon: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__/* ["default"] */ .Z, {}),
          children: formatMessage({
            id: 'common.upload'
          })
        })
      })
    })]
  });
};
/* harmony default export */ __webpack_exports__.Z = (FormUploadFiles);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///75508
`)},19707:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`
// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  Z: function() { return /* binding */ Create; }
});

// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/regeneratorRuntime.js
var regeneratorRuntime = __webpack_require__(15009);
var regeneratorRuntime_default = /*#__PURE__*/__webpack_require__.n(regeneratorRuntime);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(99289);
var asyncToGenerator_default = /*#__PURE__*/__webpack_require__.n(asyncToGenerator);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/layouts/ProForm/index.js + 3 modules
var ProForm = __webpack_require__(34994);
// EXTERNAL MODULE: ./src/.umi-production/exports.ts + 34 modules
var _umi_production_exports = __webpack_require__(7837);
// EXTERNAL MODULE: ./node_modules/antd/es/message/index.js
var message = __webpack_require__(45360);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./src/services/diary-2/document.ts
var diary_2_document = __webpack_require__(10618);
// EXTERNAL MODULE: ./node_modules/antd/es/app/index.js + 1 modules
var app = __webpack_require__(31418);
;// CONCATENATED MODULE: ./src/pages/FarmingDiaryStatic/Certification/hooks/useCreate.ts



function useCreate() {
  var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},
    _onSuccess = _ref.onSuccess,
    _onError = _ref.onError;
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  var _App$useApp = app/* default */.Z.useApp(),
    message = _App$useApp.message;
  return (0,_umi_production_exports.useRequest)(diary_2_document/* createDocument */.OE, {
    manual: true,
    onSuccess: function onSuccess(data, params) {
      message.success(formatMessage({
        id: 'common.success'
      }));
      _onSuccess === null || _onSuccess === void 0 || _onSuccess();
    },
    onError: function onError(error) {
      //message.error(error.message);
      _onError === null || _onError === void 0 || _onError();
    }
  });
}
// EXTERNAL MODULE: ./src/components/UploadFIles/index.tsx
var UploadFIles = __webpack_require__(75508);
// EXTERNAL MODULE: ./node_modules/antd/es/card/index.js + 3 modules
var card = __webpack_require__(76216);
// EXTERNAL MODULE: ./node_modules/react/jsx-runtime.js
var jsx_runtime = __webpack_require__(85893);
;// CONCATENATED MODULE: ./src/pages/FarmingDiaryStatic/Certification/components/Create/Attachment.tsx




var Attachment = function Attachment(_ref) {
  var children = _ref.children;
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  return /*#__PURE__*/(0,jsx_runtime.jsx)(card/* default */.Z, {
    title: formatMessage({
      id: 'common.attachments'
    }),
    style: {
      boxShadow: 'none'
    },
    bordered: false,
    children: /*#__PURE__*/(0,jsx_runtime.jsx)(UploadFIles/* default */.Z, {
      fileLimit: 10,
      formItemName: "document_path"
    })
  });
};
/* harmony default export */ var Create_Attachment = (Attachment);
// EXTERNAL MODULE: ./src/common/contanst/constanst.ts
var constanst = __webpack_require__(86604);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/Text/index.js
var Text = __webpack_require__(5966);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/DatePicker/index.js + 5 modules
var DatePicker = __webpack_require__(50335);
// EXTERNAL MODULE: ./node_modules/antd/es/row/index.js
var row = __webpack_require__(71230);
// EXTERNAL MODULE: ./node_modules/antd/es/col/index.js
var col = __webpack_require__(15746);
// EXTERNAL MODULE: ./node_modules/dayjs/dayjs.min.js
var dayjs_min = __webpack_require__(27484);
var dayjs_min_default = /*#__PURE__*/__webpack_require__.n(dayjs_min);
;// CONCATENATED MODULE: ./src/pages/FarmingDiaryStatic/Certification/components/Create/DetailedInfo.tsx







var w = 'md';
var DetailedInfo = function DetailedInfo(_ref) {
  var children = _ref.children;
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  return /*#__PURE__*/(0,jsx_runtime.jsx)(card/* default */.Z, {
    title: formatMessage({
      id: 'task.detailed_info'
    }),
    bordered: false,
    style: {
      boxShadow: 'none'
    },
    children: /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
      gutter: 24,
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 24,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(Text/* default */.Z, {
          name: 'label',
          label: formatMessage({
            id: 'common.name'
          }),
          rules: [{
            required: true
          }]
        })
      }), /*#__PURE__*/(0,jsx_runtime.jsxs)(col/* default */.Z, {
        span: 12,
        children: [' ', /*#__PURE__*/(0,jsx_runtime.jsx)(DatePicker/* default */.Z, {
          style: {
            width: '100%'
          },
          width: 'xl',
          name: 'issue_date',
          label: formatMessage({
            id: 'common.certification_date'
          }),
          rules: [{
            required: true
          }],
          fieldProps: {
            format: function format(value) {
              return dayjs_min_default()(value).format(constanst/* DEFAULT_DATE_FORMAT_WITHOUT_TIME */.ug);
            }
          }
        })]
      }), /*#__PURE__*/(0,jsx_runtime.jsxs)(col/* default */.Z, {
        span: 12,
        children: [' ', /*#__PURE__*/(0,jsx_runtime.jsx)(DatePicker/* default */.Z, {
          style: {
            width: '100%'
          },
          width: 'xl',
          name: 'expiry_date',
          label: formatMessage({
            id: 'common.expiration_date'
          }),
          rules: [{
            required: true
          }],
          fieldProps: {
            format: function format(value) {
              return dayjs_min_default()(value).format(constanst/* DEFAULT_DATE_FORMAT_WITHOUT_TIME */.ug);
            }
          }
        })]
      })]
    })
  });
};
/* harmony default export */ var Create_DetailedInfo = (DetailedInfo);
;// CONCATENATED MODULE: ./src/pages/FarmingDiaryStatic/Certification/components/Create/index.tsx











var DocumentCreate = function DocumentCreate(_ref) {
  var children = _ref.children,
    _onSuccess = _ref.onSuccess,
    _ref$setIsFormDirty = _ref.setIsFormDirty,
    setIsFormDirty = _ref$setIsFormDirty === void 0 ? function () {} : _ref$setIsFormDirty;
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  var location = (0,_umi_production_exports.useLocation)();
  var state = location.state;
  var _useCreate = useCreate({
      onSuccess: function onSuccess() {
        if (state !== null && state !== void 0 && state.fromProcedureCreate) {
          _umi_production_exports.history.replace('/farming-diary-static/procedure/create');
        }
        if (state !== null && state !== void 0 && state.fromProcedureEdit) {
          _umi_production_exports.history.replace("/farming-diary-static/procedure/edit/".concat(state.id));
        }
        _onSuccess && _onSuccess();
      }
    }),
    run = _useCreate.run;
  (0,react.useEffect)(function () {
    setIsFormDirty(false);
  }, []);
  return /*#__PURE__*/(0,jsx_runtime.jsx)(ProForm/* ProForm */.A, {
    onValuesChange: function onValuesChange() {
      return setIsFormDirty(true);
    },
    submitter: {
      searchConfig: {
        // resetText: formatMessage({ id: 'common.reset' }),
        // submitText: formatMessage({ id: 'common.submit' }),
      },
      render: function render(_, dom) {
        return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
          style: {
            textAlign: 'right',
            margin: 24
          },
          children: dom.map(function (item, index) {
            return /*#__PURE__*/(0,jsx_runtime.jsx)("span", {
              style: {
                marginRight: index === 0 ? 8 : 0
              },
              children: item
            }, index);
          })
        });
      }
    },
    onFinish: ( /*#__PURE__*/function () {
      var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(values) {
        return regeneratorRuntime_default()().wrap(function _callee$(_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              if (!(values.issue_date > values.expiry_date)) {
                _context.next = 3;
                break;
              }
              message/* default */.ZP.error('Ng\xE0y h\u1EBFt h\u1EA1n ph\u1EA3i l\u1EDBn h\u01A1n ng\xE0y ch\u1EE9ng nh\u1EADn');
              return _context.abrupt("return");
            case 3:
              _context.next = 5;
              return run({
                // name: string;
                label: values.label,
                issue_date: values.issue_date,
                expiry_date: values.expiry_date,
                document_path: values.document_path
              });
            case 5:
              setIsFormDirty(false);
              _onSuccess === null || _onSuccess === void 0 || _onSuccess();
              return _context.abrupt("return", true);
            case 8:
            case "end":
              return _context.stop();
          }
        }, _callee);
      }));
      return function (_x) {
        return _ref2.apply(this, arguments);
      };
    }()),
    children: /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
      className: "mb-4 space-y-4",
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)(Create_DetailedInfo, {}), /*#__PURE__*/(0,jsx_runtime.jsx)(Create_Attachment, {})]
    })
  });
};
/* harmony default export */ var Create = (DocumentCreate);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///19707
`)},10618:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   OE: function() { return /* binding */ createDocument; },
/* harmony export */   _Q: function() { return /* binding */ getDocumentList; },
/* harmony export */   gU: function() { return /* binding */ updateDocument; },
/* harmony export */   iH: function() { return /* binding */ deleteDocument; }
/* harmony export */ });
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(7837);
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(467);




var getDocumentList = /*#__PURE__*/function () {
  var _ref = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/diary-v2/document'), {
            method: 'GET',
            params: (0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .getParamsReqList */ .vj)(params)
          });
        case 2:
          res = _context.sent;
          return _context.abrupt("return", res.result);
        case 4:
        case "end":
          return _context.stop();
      }
    }, _callee);
  }));
  return function getDocumentList(_x) {
    return _ref.apply(this, arguments);
  };
}();
var createDocument = /*#__PURE__*/function () {
  var _ref2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee2(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          _context2.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/diary-v2/document'), {
            method: 'POST',
            data: data
          });
        case 2:
          res = _context2.sent;
          return _context2.abrupt("return", res);
        case 4:
        case "end":
          return _context2.stop();
      }
    }, _callee2);
  }));
  return function createDocument(_x2) {
    return _ref2.apply(this, arguments);
  };
}();
var updateDocument = /*#__PURE__*/function () {
  var _ref3 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee3(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee3$(_context3) {
      while (1) switch (_context3.prev = _context3.next) {
        case 0:
          _context3.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/diary-v2/document'), {
            method: 'PUT',
            data: data
          });
        case 2:
          res = _context3.sent;
          return _context3.abrupt("return", res);
        case 4:
        case "end":
          return _context3.stop();
      }
    }, _callee3);
  }));
  return function updateDocument(_x3) {
    return _ref3.apply(this, arguments);
  };
}();
var deleteDocument = /*#__PURE__*/function () {
  var _ref4 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee4(id) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee4$(_context4) {
      while (1) switch (_context4.prev = _context4.next) {
        case 0:
          _context4.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/diary-v2/document'), {
            method: 'DELETE',
            params: {
              name: id
            }
          });
        case 2:
          res = _context4.sent;
          return _context4.abrupt("return", res);
        case 4:
        case "end":
          return _context4.stop();
      }
    }, _callee4);
  }));
  return function deleteDocument(_x4) {
    return _ref4.apply(this, arguments);
  };
}();//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiMTA2MTguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBcUM7QUFDd0I7QUFhdEQsSUFBTUcsZUFBZTtFQUFBLElBQUFDLElBQUEsR0FBQUMsK0tBQUEsZUFBQUMsaUxBQUEsR0FBQUMsSUFBQSxDQUFHLFNBQUFDLFFBQU9DLE1BQTBCO0lBQUEsSUFBQUMsR0FBQTtJQUFBLE9BQUFKLGlMQUFBLEdBQUFLLElBQUEsVUFBQUMsU0FBQUMsUUFBQTtNQUFBLGtCQUFBQSxRQUFBLENBQUFDLElBQUEsR0FBQUQsUUFBQSxDQUFBRSxJQUFBO1FBQUE7VUFBQUYsUUFBQSxDQUFBRSxJQUFBO1VBQUEsT0FDNUNmLG1EQUFPLENBQ3ZCQyxpRUFBZSxDQUFDLDBCQUEwQixDQUFDLEVBQzNDO1lBQ0VlLE1BQU0sRUFBRSxLQUFLO1lBQ2JQLE1BQU0sRUFBRVAsa0VBQWdCLENBQUNPLE1BQU07VUFDakMsQ0FDRixDQUFDO1FBQUE7VUFOS0MsR0FBRyxHQUFBRyxRQUFBLENBQUFJLElBQUE7VUFBQSxPQUFBSixRQUFBLENBQUFLLE1BQUEsV0FRRlIsR0FBRyxDQUFDUyxNQUFNO1FBQUE7UUFBQTtVQUFBLE9BQUFOLFFBQUEsQ0FBQU8sSUFBQTtNQUFBO0lBQUEsR0FBQVosT0FBQTtFQUFBLENBQ2xCO0VBQUEsZ0JBVllMLGVBQWVBLENBQUFrQixFQUFBO0lBQUEsT0FBQWpCLElBQUEsQ0FBQWtCLEtBQUEsT0FBQUMsU0FBQTtFQUFBO0FBQUEsR0FVM0I7QUFFTSxJQUFNQyxjQUFjO0VBQUEsSUFBQUMsS0FBQSxHQUFBcEIsK0tBQUEsZUFBQUMsaUxBQUEsR0FBQUMsSUFBQSxDQUFHLFNBQUFtQixTQUFPQyxJQUE0QjtJQUFBLElBQUFqQixHQUFBO0lBQUEsT0FBQUosaUxBQUEsR0FBQUssSUFBQSxVQUFBaUIsVUFBQUMsU0FBQTtNQUFBLGtCQUFBQSxTQUFBLENBQUFmLElBQUEsR0FBQWUsU0FBQSxDQUFBZCxJQUFBO1FBQUE7VUFBQWMsU0FBQSxDQUFBZCxJQUFBO1VBQUEsT0FDN0NmLG1EQUFPLENBQUNDLGlFQUFlLENBQUMsMEJBQTBCLENBQUMsRUFBRTtZQUNyRWUsTUFBTSxFQUFFLE1BQU07WUFDZFcsSUFBSSxFQUFKQTtVQUNGLENBQUMsQ0FBQztRQUFBO1VBSElqQixHQUFHLEdBQUFtQixTQUFBLENBQUFaLElBQUE7VUFBQSxPQUFBWSxTQUFBLENBQUFYLE1BQUEsV0FJRlIsR0FBRztRQUFBO1FBQUE7VUFBQSxPQUFBbUIsU0FBQSxDQUFBVCxJQUFBO01BQUE7SUFBQSxHQUFBTSxRQUFBO0VBQUEsQ0FDWDtFQUFBLGdCQU5ZRixjQUFjQSxDQUFBTSxHQUFBO0lBQUEsT0FBQUwsS0FBQSxDQUFBSCxLQUFBLE9BQUFDLFNBQUE7RUFBQTtBQUFBLEdBTTFCO0FBRU0sSUFBTVEsY0FBYztFQUFBLElBQUFDLEtBQUEsR0FBQTNCLCtLQUFBLGVBQUFDLGlMQUFBLEdBQUFDLElBQUEsQ0FBRyxTQUFBMEIsU0FBT04sSUFBNEI7SUFBQSxJQUFBakIsR0FBQTtJQUFBLE9BQUFKLGlMQUFBLEdBQUFLLElBQUEsVUFBQXVCLFVBQUFDLFNBQUE7TUFBQSxrQkFBQUEsU0FBQSxDQUFBckIsSUFBQSxHQUFBcUIsU0FBQSxDQUFBcEIsSUFBQTtRQUFBO1VBQUFvQixTQUFBLENBQUFwQixJQUFBO1VBQUEsT0FDN0NmLG1EQUFPLENBQUNDLGlFQUFlLENBQUMsMEJBQTBCLENBQUMsRUFBRTtZQUNyRWUsTUFBTSxFQUFFLEtBQUs7WUFDYlcsSUFBSSxFQUFKQTtVQUNGLENBQUMsQ0FBQztRQUFBO1VBSElqQixHQUFHLEdBQUF5QixTQUFBLENBQUFsQixJQUFBO1VBQUEsT0FBQWtCLFNBQUEsQ0FBQWpCLE1BQUEsV0FJRlIsR0FBRztRQUFBO1FBQUE7VUFBQSxPQUFBeUIsU0FBQSxDQUFBZixJQUFBO01BQUE7SUFBQSxHQUFBYSxRQUFBO0VBQUEsQ0FDWDtFQUFBLGdCQU5ZRixjQUFjQSxDQUFBSyxHQUFBO0lBQUEsT0FBQUosS0FBQSxDQUFBVixLQUFBLE9BQUFDLFNBQUE7RUFBQTtBQUFBLEdBTTFCO0FBRU0sSUFBTWMsY0FBYztFQUFBLElBQUFDLEtBQUEsR0FBQWpDLCtLQUFBLGVBQUFDLGlMQUFBLEdBQUFDLElBQUEsQ0FBRyxTQUFBZ0MsU0FBT0MsRUFBVTtJQUFBLElBQUE5QixHQUFBO0lBQUEsT0FBQUosaUxBQUEsR0FBQUssSUFBQSxVQUFBOEIsVUFBQUMsU0FBQTtNQUFBLGtCQUFBQSxTQUFBLENBQUE1QixJQUFBLEdBQUE0QixTQUFBLENBQUEzQixJQUFBO1FBQUE7VUFBQTJCLFNBQUEsQ0FBQTNCLElBQUE7VUFBQSxPQUMzQmYsbURBQU8sQ0FBQ0MsaUVBQWUsQ0FBQywwQkFBMEIsQ0FBQyxFQUFFO1lBQ3JFZSxNQUFNLEVBQUUsUUFBUTtZQUNoQlAsTUFBTSxFQUFFO2NBQ05rQyxJQUFJLEVBQUVIO1lBQ1I7VUFDRixDQUFDLENBQUM7UUFBQTtVQUxJOUIsR0FBRyxHQUFBZ0MsU0FBQSxDQUFBekIsSUFBQTtVQUFBLE9BQUF5QixTQUFBLENBQUF4QixNQUFBLFdBTUZSLEdBQUc7UUFBQTtRQUFBO1VBQUEsT0FBQWdDLFNBQUEsQ0FBQXRCLElBQUE7TUFBQTtJQUFBLEdBQUFtQixRQUFBO0VBQUEsQ0FDWDtFQUFBLGdCQVJZRixjQUFjQSxDQUFBTyxHQUFBO0lBQUEsT0FBQU4sS0FBQSxDQUFBaEIsS0FBQSxPQUFBQyxTQUFBO0VBQUE7QUFBQSxHQVExQiIsInNvdXJjZXMiOlsid2VicGFjazovL2FudC1kZXNpZ24tcHJvLy4vc3JjL3NlcnZpY2VzL2RpYXJ5LTIvZG9jdW1lbnQudHM/NTAwYyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyByZXF1ZXN0IH0gZnJvbSAnQHVtaWpzL21heCc7XHJcbmltcG9ydCB7IGdlbmVyYXRlQVBJUGF0aCwgZ2V0UGFyYW1zUmVxTGlzdCB9IGZyb20gJy4uL3V0aWxzJztcclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgRGlhcnlEb2N1bWVudCB7XHJcbiAgbmFtZTogc3RyaW5nO1xyXG4gIGxhYmVsOiBzdHJpbmc7XHJcbiAgZG9jdW1lbnRfcGF0aDogc3RyaW5nO1xyXG4gIGZpbGVfdHlwZTogc3RyaW5nO1xyXG4gIGlzc3VlX2RhdGU6IHN0cmluZztcclxuICBleHBpcnlfZGF0ZTogc3RyaW5nO1xyXG4gIGlzX2RlbGV0ZWQ6IG51bWJlcjtcclxuICBpb3RfY3VzdG9tZXI6IHN0cmluZztcclxufVxyXG5cclxuZXhwb3J0IGNvbnN0IGdldERvY3VtZW50TGlzdCA9IGFzeW5jIChwYXJhbXM/OiBBUEkuTGlzdFBhcmFtc1JlcSkgPT4ge1xyXG4gIGNvbnN0IHJlcyA9IGF3YWl0IHJlcXVlc3Q8QVBJLlBhZ2luYXRpb25SZXNwb25zZVJlc3VsdDxEaWFyeURvY3VtZW50W10+PihcclxuICAgIGdlbmVyYXRlQVBJUGF0aCgnYXBpL3YyL2RpYXJ5LXYyL2RvY3VtZW50JyksXHJcbiAgICB7XHJcbiAgICAgIG1ldGhvZDogJ0dFVCcsXHJcbiAgICAgIHBhcmFtczogZ2V0UGFyYW1zUmVxTGlzdChwYXJhbXMpLFxyXG4gICAgfSxcclxuICApO1xyXG5cclxuICByZXR1cm4gcmVzLnJlc3VsdDtcclxufTtcclxuXHJcbmV4cG9ydCBjb25zdCBjcmVhdGVEb2N1bWVudCA9IGFzeW5jIChkYXRhOiBQYXJ0aWFsPERpYXJ5RG9jdW1lbnQ+KSA9PiB7XHJcbiAgY29uc3QgcmVzID0gYXdhaXQgcmVxdWVzdChnZW5lcmF0ZUFQSVBhdGgoJ2FwaS92Mi9kaWFyeS12Mi9kb2N1bWVudCcpLCB7XHJcbiAgICBtZXRob2Q6ICdQT1NUJyxcclxuICAgIGRhdGEsXHJcbiAgfSk7XHJcbiAgcmV0dXJuIHJlcztcclxufTtcclxuXHJcbmV4cG9ydCBjb25zdCB1cGRhdGVEb2N1bWVudCA9IGFzeW5jIChkYXRhOiBQYXJ0aWFsPERpYXJ5RG9jdW1lbnQ+KSA9PiB7XHJcbiAgY29uc3QgcmVzID0gYXdhaXQgcmVxdWVzdChnZW5lcmF0ZUFQSVBhdGgoJ2FwaS92Mi9kaWFyeS12Mi9kb2N1bWVudCcpLCB7XHJcbiAgICBtZXRob2Q6ICdQVVQnLFxyXG4gICAgZGF0YSxcclxuICB9KTtcclxuICByZXR1cm4gcmVzO1xyXG59O1xyXG5cclxuZXhwb3J0IGNvbnN0IGRlbGV0ZURvY3VtZW50ID0gYXN5bmMgKGlkOiBzdHJpbmcpID0+IHtcclxuICBjb25zdCByZXMgPSBhd2FpdCByZXF1ZXN0KGdlbmVyYXRlQVBJUGF0aCgnYXBpL3YyL2RpYXJ5LXYyL2RvY3VtZW50JyksIHtcclxuICAgIG1ldGhvZDogJ0RFTEVURScsXHJcbiAgICBwYXJhbXM6IHtcclxuICAgICAgbmFtZTogaWQsXHJcbiAgICB9LFxyXG4gIH0pO1xyXG4gIHJldHVybiByZXM7XHJcbn07XHJcbiJdLCJuYW1lcyI6WyJyZXF1ZXN0IiwiZ2VuZXJhdGVBUElQYXRoIiwiZ2V0UGFyYW1zUmVxTGlzdCIsImdldERvY3VtZW50TGlzdCIsIl9yZWYiLCJfYXN5bmNUb0dlbmVyYXRvciIsIl9yZWdlbmVyYXRvclJ1bnRpbWUiLCJtYXJrIiwiX2NhbGxlZSIsInBhcmFtcyIsInJlcyIsIndyYXAiLCJfY2FsbGVlJCIsIl9jb250ZXh0IiwicHJldiIsIm5leHQiLCJtZXRob2QiLCJzZW50IiwiYWJydXB0IiwicmVzdWx0Iiwic3RvcCIsIl94IiwiYXBwbHkiLCJhcmd1bWVudHMiLCJjcmVhdGVEb2N1bWVudCIsIl9yZWYyIiwiX2NhbGxlZTIiLCJkYXRhIiwiX2NhbGxlZTIkIiwiX2NvbnRleHQyIiwiX3gyIiwidXBkYXRlRG9jdW1lbnQiLCJfcmVmMyIsIl9jYWxsZWUzIiwiX2NhbGxlZTMkIiwiX2NvbnRleHQzIiwiX3gzIiwiZGVsZXRlRG9jdW1lbnQiLCJfcmVmNCIsIl9jYWxsZWU0IiwiaWQiLCJfY2FsbGVlNCQiLCJfY29udGV4dDQiLCJuYW1lIiwiX3g0Il0sInNvdXJjZVJvb3QiOiIifQ==
//# sourceURL=webpack-internal:///10618
`)},96876:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   cT: function() { return /* binding */ uploadFile; },
/* harmony export */   uy: function() { return /* binding */ uploadFileHome; }
/* harmony export */ });
/* unused harmony export removeFileAttachment */
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(7837);
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(467);




var uploadFile = /*#__PURE__*/function () {
  var _ref = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee(data) {
    var formData, res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          formData = new FormData();
          formData.append('doctype', data.docType || '');
          formData.append('docname', data.docName || '');
          formData.append('folder', data.folder || 'Home/Attachments');
          formData.append('is_private', data.isPrivate || '1');
          formData.append('optimize', data.optimize || '0');
          formData.append('file', data.file);
          _context.next = 9;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/file/upload'), {
            method: 'POST',
            data: formData
          });
        case 9:
          res = _context.sent;
          return _context.abrupt("return", {
            data: res.result
          });
        case 11:
        case "end":
          return _context.stop();
      }
    }, _callee);
  }));
  return function uploadFile(_x) {
    return _ref.apply(this, arguments);
  };
}();
var uploadFileHome = /*#__PURE__*/function () {
  var _ref2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee2(data) {
    var formData, res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          formData = new FormData();
          formData.append('folder', data.folder || 'Home/Attachments');
          formData.append('is_private', data.isPrivate || '0');
          formData.append('is_home_folder', data.isPrivate || '1');
          formData.append('doctype', data.docType || '');
          formData.append('docname', data.docName || '');
          formData.append('optimize', data.optimize || '0');
          formData.append('file', data.file);
          _context2.next = 10;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/file/upload'), {
            method: 'POST',
            data: formData
          });
        case 10:
          res = _context2.sent;
          return _context2.abrupt("return", {
            data: res.result
          });
        case 12:
        case "end":
          return _context2.stop();
      }
    }, _callee2);
  }));
  return function uploadFileHome(_x2) {
    return _ref2.apply(this, arguments);
  };
}();
var removeFileAttachment = /*#__PURE__*/(/* unused pure expression or super */ null && (function () {
  var _ref3 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee3(data) {
    var res;
    return _regeneratorRuntime().wrap(function _callee3$(_context3) {
      while (1) switch (_context3.prev = _context3.next) {
        case 0:
          _context3.next = 2;
          return request(generateAPIPath('api/v2/file'), {
            method: 'DELETE',
            data: data
          });
        case 2:
          res = _context3.sent;
          return _context3.abrupt("return", {
            data: res.result
          });
        case 4:
        case "end":
          return _context3.stop();
      }
    }, _callee3);
  }));
  return function removeFileAttachment(_x3) {
    return _ref3.apply(this, arguments);
  };
}()));//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///96876
`)}}]);
