"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[8021],{58128:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`
// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  Z: function() { return /* binding */ es_ProCard; }
});

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectSpread2.js
var objectSpread2 = __webpack_require__(1413);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(4942);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/typeof.js
var esm_typeof = __webpack_require__(71002);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js + 1 modules
var slicedToArray = __webpack_require__(97685);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js + 1 modules
var objectWithoutProperties = __webpack_require__(91);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/extends.js
var esm_extends = __webpack_require__(87462);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons-svg/es/asn/RightOutlined.js
var asn_RightOutlined = __webpack_require__(50756);
// EXTERNAL MODULE: ./node_modules/classnames/index.js
var classnames = __webpack_require__(93967);
var classnames_default = /*#__PURE__*/__webpack_require__.n(classnames);
// EXTERNAL MODULE: ./node_modules/@ctrl/tinycolor/dist/module/conversion.js
var conversion = __webpack_require__(86500);
// EXTERNAL MODULE: ./node_modules/@ctrl/tinycolor/dist/module/format-input.js
var format_input = __webpack_require__(1350);
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-card/node_modules/@ant-design/colors/es/generate.js

var hueStep = 2; // \u8272\u76F8\u9636\u68AF
var saturationStep = 0.16; // \u9971\u548C\u5EA6\u9636\u68AF\uFF0C\u6D45\u8272\u90E8\u5206
var saturationStep2 = 0.05; // \u9971\u548C\u5EA6\u9636\u68AF\uFF0C\u6DF1\u8272\u90E8\u5206
var brightnessStep1 = 0.05; // \u4EAE\u5EA6\u9636\u68AF\uFF0C\u6D45\u8272\u90E8\u5206
var brightnessStep2 = 0.15; // \u4EAE\u5EA6\u9636\u68AF\uFF0C\u6DF1\u8272\u90E8\u5206
var lightColorCount = 5; // \u6D45\u8272\u6570\u91CF\uFF0C\u4E3B\u8272\u4E0A
var darkColorCount = 4; // \u6DF1\u8272\u6570\u91CF\uFF0C\u4E3B\u8272\u4E0B
// \u6697\u8272\u4E3B\u9898\u989C\u8272\u6620\u5C04\u5173\u7CFB\u8868
var darkColorMap = [{
  index: 7,
  opacity: 0.15
}, {
  index: 6,
  opacity: 0.25
}, {
  index: 5,
  opacity: 0.3
}, {
  index: 5,
  opacity: 0.45
}, {
  index: 5,
  opacity: 0.65
}, {
  index: 5,
  opacity: 0.85
}, {
  index: 4,
  opacity: 0.9
}, {
  index: 3,
  opacity: 0.95
}, {
  index: 2,
  opacity: 0.97
}, {
  index: 1,
  opacity: 0.98
}];
// Wrapper function ported from TinyColor.prototype.toHsv
// Keep it here because of \`hsv.h * 360\`
function toHsv(_ref) {
  var r = _ref.r,
    g = _ref.g,
    b = _ref.b;
  var hsv = (0,conversion/* rgbToHsv */.py)(r, g, b);
  return {
    h: hsv.h * 360,
    s: hsv.s,
    v: hsv.v
  };
}

// Wrapper function ported from TinyColor.prototype.toHexString
// Keep it here because of the prefix \`#\`
function toHex(_ref2) {
  var r = _ref2.r,
    g = _ref2.g,
    b = _ref2.b;
  return "#".concat((0,conversion/* rgbToHex */.vq)(r, g, b, false));
}

// Wrapper function ported from TinyColor.prototype.mix, not treeshakable.
// Amount in range [0, 1]
// Assume color1 & color2 has no alpha, since the following src code did so.
function mix(rgb1, rgb2, amount) {
  var p = amount / 100;
  var rgb = {
    r: (rgb2.r - rgb1.r) * p + rgb1.r,
    g: (rgb2.g - rgb1.g) * p + rgb1.g,
    b: (rgb2.b - rgb1.b) * p + rgb1.b
  };
  return rgb;
}
function getHue(hsv, i, light) {
  var hue;
  // \u6839\u636E\u8272\u76F8\u4E0D\u540C\uFF0C\u8272\u76F8\u8F6C\u5411\u4E0D\u540C
  if (Math.round(hsv.h) >= 60 && Math.round(hsv.h) <= 240) {
    hue = light ? Math.round(hsv.h) - hueStep * i : Math.round(hsv.h) + hueStep * i;
  } else {
    hue = light ? Math.round(hsv.h) + hueStep * i : Math.round(hsv.h) - hueStep * i;
  }
  if (hue < 0) {
    hue += 360;
  } else if (hue >= 360) {
    hue -= 360;
  }
  return hue;
}
function getSaturation(hsv, i, light) {
  // grey color don't change saturation
  if (hsv.h === 0 && hsv.s === 0) {
    return hsv.s;
  }
  var saturation;
  if (light) {
    saturation = hsv.s - saturationStep * i;
  } else if (i === darkColorCount) {
    saturation = hsv.s + saturationStep;
  } else {
    saturation = hsv.s + saturationStep2 * i;
  }
  // \u8FB9\u754C\u503C\u4FEE\u6B63
  if (saturation > 1) {
    saturation = 1;
  }
  // \u7B2C\u4E00\u683C\u7684 s \u9650\u5236\u5728 0.06-0.1 \u4E4B\u95F4
  if (light && i === lightColorCount && saturation > 0.1) {
    saturation = 0.1;
  }
  if (saturation < 0.06) {
    saturation = 0.06;
  }
  return Number(saturation.toFixed(2));
}
function getValue(hsv, i, light) {
  var value;
  if (light) {
    value = hsv.v + brightnessStep1 * i;
  } else {
    value = hsv.v - brightnessStep2 * i;
  }
  if (value > 1) {
    value = 1;
  }
  return Number(value.toFixed(2));
}
function generate(color) {
  var opts = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  var patterns = [];
  var pColor = (0,format_input/* inputToRGB */.uA)(color);
  for (var i = lightColorCount; i > 0; i -= 1) {
    var hsv = toHsv(pColor);
    var colorString = toHex((0,format_input/* inputToRGB */.uA)({
      h: getHue(hsv, i, true),
      s: getSaturation(hsv, i, true),
      v: getValue(hsv, i, true)
    }));
    patterns.push(colorString);
  }
  patterns.push(toHex(pColor));
  for (var _i = 1; _i <= darkColorCount; _i += 1) {
    var _hsv = toHsv(pColor);
    var _colorString = toHex((0,format_input/* inputToRGB */.uA)({
      h: getHue(_hsv, _i),
      s: getSaturation(_hsv, _i),
      v: getValue(_hsv, _i)
    }));
    patterns.push(_colorString);
  }

  // dark theme patterns
  if (opts.theme === 'dark') {
    return darkColorMap.map(function (_ref3) {
      var index = _ref3.index,
        opacity = _ref3.opacity;
      var darkColorString = toHex(mix((0,format_input/* inputToRGB */.uA)(opts.backgroundColor || '#141414'), (0,format_input/* inputToRGB */.uA)(patterns[index]), opacity * 100));
      return darkColorString;
    });
  }
  return patterns;
}
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-card/node_modules/@ant-design/colors/es/index.js

var presetPrimaryColors = {
  red: '#F5222D',
  volcano: '#FA541C',
  orange: '#FA8C16',
  gold: '#FAAD14',
  yellow: '#FADB14',
  lime: '#A0D911',
  green: '#52C41A',
  cyan: '#13C2C2',
  blue: '#1677FF',
  geekblue: '#2F54EB',
  purple: '#722ED1',
  magenta: '#EB2F96',
  grey: '#666666'
};
var presetPalettes = {};
var presetDarkPalettes = {};
Object.keys(presetPrimaryColors).forEach(function (key) {
  presetPalettes[key] = generate(presetPrimaryColors[key]);
  presetPalettes[key].primary = presetPalettes[key][5];

  // dark presetPalettes
  presetDarkPalettes[key] = generate(presetPrimaryColors[key], {
    theme: 'dark',
    backgroundColor: '#141414'
  });
  presetDarkPalettes[key].primary = presetDarkPalettes[key][5];
});
var red = presetPalettes.red;
var volcano = presetPalettes.volcano;
var gold = presetPalettes.gold;
var orange = presetPalettes.orange;
var yellow = presetPalettes.yellow;
var lime = presetPalettes.lime;
var green = presetPalettes.green;
var cyan = presetPalettes.cyan;
var blue = presetPalettes.blue;
var geekblue = presetPalettes.geekblue;
var purple = presetPalettes.purple;
var magenta = presetPalettes.magenta;
var grey = presetPalettes.grey;
var gray = presetPalettes.grey;

;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-card/node_modules/@ant-design/icons/es/components/Context.js

var IconContext = /*#__PURE__*/(0,react.createContext)({});
/* harmony default export */ var Context = (IconContext);
// EXTERNAL MODULE: ./node_modules/rc-util/es/Dom/dynamicCSS.js
var dynamicCSS = __webpack_require__(44958);
// EXTERNAL MODULE: ./node_modules/rc-util/es/Dom/shadow.js
var shadow = __webpack_require__(27571);
// EXTERNAL MODULE: ./node_modules/rc-util/es/warning.js
var warning = __webpack_require__(80334);
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-card/node_modules/@ant-design/icons/es/utils.js








function camelCase(input) {
  return input.replace(/-(.)/g, function (match, g) {
    return g.toUpperCase();
  });
}
function utils_warning(valid, message) {
  (0,warning/* default */.ZP)(valid, "[@ant-design/icons] ".concat(message));
}
function isIconDefinition(target) {
  return (0,esm_typeof/* default */.Z)(target) === 'object' && typeof target.name === 'string' && typeof target.theme === 'string' && ((0,esm_typeof/* default */.Z)(target.icon) === 'object' || typeof target.icon === 'function');
}
function normalizeAttrs() {
  var attrs = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
  return Object.keys(attrs).reduce(function (acc, key) {
    var val = attrs[key];
    switch (key) {
      case 'class':
        acc.className = val;
        delete acc.class;
        break;
      default:
        delete acc[key];
        acc[camelCase(key)] = val;
    }
    return acc;
  }, {});
}
function utils_generate(node, key, rootProps) {
  if (!rootProps) {
    return /*#__PURE__*/react.createElement(node.tag, (0,objectSpread2/* default */.Z)({
      key: key
    }, normalizeAttrs(node.attrs)), (node.children || []).map(function (child, index) {
      return utils_generate(child, "".concat(key, "-").concat(node.tag, "-").concat(index));
    }));
  }
  return /*#__PURE__*/react.createElement(node.tag, (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({
    key: key
  }, normalizeAttrs(node.attrs)), rootProps), (node.children || []).map(function (child, index) {
    return utils_generate(child, "".concat(key, "-").concat(node.tag, "-").concat(index));
  }));
}
function getSecondaryColor(primaryColor) {
  // choose the second color
  return generate(primaryColor)[0];
}
function normalizeTwoToneColors(twoToneColor) {
  if (!twoToneColor) {
    return [];
  }
  return Array.isArray(twoToneColor) ? twoToneColor : [twoToneColor];
}

// These props make sure that the SVG behaviours like general text.
// Reference: https://blog.prototypr.io/align-svg-icons-to-text-and-say-goodbye-to-font-icons-d44b3d7b26b4
var svgBaseProps = {
  width: '1em',
  height: '1em',
  fill: 'currentColor',
  'aria-hidden': 'true',
  focusable: 'false'
};
var iconStyles = "\\n.anticon {\\n  display: inline-block;\\n  color: inherit;\\n  font-style: normal;\\n  line-height: 0;\\n  text-align: center;\\n  text-transform: none;\\n  vertical-align: -0.125em;\\n  text-rendering: optimizeLegibility;\\n  -webkit-font-smoothing: antialiased;\\n  -moz-osx-font-smoothing: grayscale;\\n}\\n\\n.anticon > * {\\n  line-height: 1;\\n}\\n\\n.anticon svg {\\n  display: inline-block;\\n}\\n\\n.anticon::before {\\n  display: none;\\n}\\n\\n.anticon .anticon-icon {\\n  display: block;\\n}\\n\\n.anticon[tabindex] {\\n  cursor: pointer;\\n}\\n\\n.anticon-spin::before,\\n.anticon-spin {\\n  display: inline-block;\\n  -webkit-animation: loadingCircle 1s infinite linear;\\n  animation: loadingCircle 1s infinite linear;\\n}\\n\\n@-webkit-keyframes loadingCircle {\\n  100% {\\n    -webkit-transform: rotate(360deg);\\n    transform: rotate(360deg);\\n  }\\n}\\n\\n@keyframes loadingCircle {\\n  100% {\\n    -webkit-transform: rotate(360deg);\\n    transform: rotate(360deg);\\n  }\\n}\\n";
var useInsertStyles = function useInsertStyles(eleRef) {
  var _useContext = (0,react.useContext)(Context),
    csp = _useContext.csp,
    prefixCls = _useContext.prefixCls;
  var mergedStyleStr = iconStyles;
  if (prefixCls) {
    mergedStyleStr = mergedStyleStr.replace(/anticon/g, prefixCls);
  }
  (0,react.useEffect)(function () {
    var ele = eleRef.current;
    var shadowRoot = (0,shadow/* getShadowRoot */.A)(ele);
    (0,dynamicCSS/* updateCSS */.hq)(mergedStyleStr, '@ant-design-icons', {
      prepend: true,
      csp: csp,
      attachTo: shadowRoot
    });
  }, []);
};
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-card/node_modules/@ant-design/icons/es/components/IconBase.js


var _excluded = ["icon", "className", "onClick", "style", "primaryColor", "secondaryColor"];


var twoToneColorPalette = {
  primaryColor: '#333',
  secondaryColor: '#E6E6E6',
  calculated: false
};
function setTwoToneColors(_ref) {
  var primaryColor = _ref.primaryColor,
    secondaryColor = _ref.secondaryColor;
  twoToneColorPalette.primaryColor = primaryColor;
  twoToneColorPalette.secondaryColor = secondaryColor || getSecondaryColor(primaryColor);
  twoToneColorPalette.calculated = !!secondaryColor;
}
function getTwoToneColors() {
  return (0,objectSpread2/* default */.Z)({}, twoToneColorPalette);
}
var IconBase = function IconBase(props) {
  var icon = props.icon,
    className = props.className,
    onClick = props.onClick,
    style = props.style,
    primaryColor = props.primaryColor,
    secondaryColor = props.secondaryColor,
    restProps = (0,objectWithoutProperties/* default */.Z)(props, _excluded);
  var svgRef = react.useRef();
  var colors = twoToneColorPalette;
  if (primaryColor) {
    colors = {
      primaryColor: primaryColor,
      secondaryColor: secondaryColor || getSecondaryColor(primaryColor)
    };
  }
  useInsertStyles(svgRef);
  utils_warning(isIconDefinition(icon), "icon should be icon definiton, but got ".concat(icon));
  if (!isIconDefinition(icon)) {
    return null;
  }
  var target = icon;
  if (target && typeof target.icon === 'function') {
    target = (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, target), {}, {
      icon: target.icon(colors.primaryColor, colors.secondaryColor)
    });
  }
  return utils_generate(target.icon, "svg-".concat(target.name), (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({
    className: className,
    onClick: onClick,
    style: style,
    'data-icon': target.name,
    width: '1em',
    height: '1em',
    fill: 'currentColor',
    'aria-hidden': 'true'
  }, restProps), {}, {
    ref: svgRef
  }));
};
IconBase.displayName = 'IconReact';
IconBase.getTwoToneColors = getTwoToneColors;
IconBase.setTwoToneColors = setTwoToneColors;
/* harmony default export */ var components_IconBase = (IconBase);
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-card/node_modules/@ant-design/icons/es/components/twoTonePrimaryColor.js



function setTwoToneColor(twoToneColor) {
  var _normalizeTwoToneColo = normalizeTwoToneColors(twoToneColor),
    _normalizeTwoToneColo2 = (0,slicedToArray/* default */.Z)(_normalizeTwoToneColo, 2),
    primaryColor = _normalizeTwoToneColo2[0],
    secondaryColor = _normalizeTwoToneColo2[1];
  return components_IconBase.setTwoToneColors({
    primaryColor: primaryColor,
    secondaryColor: secondaryColor
  });
}
function getTwoToneColor() {
  var colors = components_IconBase.getTwoToneColors();
  if (!colors.calculated) {
    return colors.primaryColor;
  }
  return [colors.primaryColor, colors.secondaryColor];
}
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-card/node_modules/@ant-design/icons/es/components/AntdIcon.js
'use client';





var AntdIcon_excluded = ["className", "icon", "spin", "rotate", "tabIndex", "onClick", "twoToneColor"];







// Initial setting
// should move it to antd main repo?
setTwoToneColor(blue.primary);

// https://github.com/DefinitelyTyped/DefinitelyTyped/issues/34757#issuecomment-488848720

var Icon = /*#__PURE__*/react.forwardRef(function (props, ref) {
  var className = props.className,
    icon = props.icon,
    spin = props.spin,
    rotate = props.rotate,
    tabIndex = props.tabIndex,
    onClick = props.onClick,
    twoToneColor = props.twoToneColor,
    restProps = (0,objectWithoutProperties/* default */.Z)(props, AntdIcon_excluded);
  var _React$useContext = react.useContext(Context),
    _React$useContext$pre = _React$useContext.prefixCls,
    prefixCls = _React$useContext$pre === void 0 ? 'anticon' : _React$useContext$pre,
    rootClassName = _React$useContext.rootClassName;
  var classString = classnames_default()(rootClassName, prefixCls, (0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)({}, "".concat(prefixCls, "-").concat(icon.name), !!icon.name), "".concat(prefixCls, "-spin"), !!spin || icon.name === 'loading'), className);
  var iconTabIndex = tabIndex;
  if (iconTabIndex === undefined && onClick) {
    iconTabIndex = -1;
  }
  var svgStyle = rotate ? {
    msTransform: "rotate(".concat(rotate, "deg)"),
    transform: "rotate(".concat(rotate, "deg)")
  } : undefined;
  var _normalizeTwoToneColo = normalizeTwoToneColors(twoToneColor),
    _normalizeTwoToneColo2 = (0,slicedToArray/* default */.Z)(_normalizeTwoToneColo, 2),
    primaryColor = _normalizeTwoToneColo2[0],
    secondaryColor = _normalizeTwoToneColo2[1];
  return /*#__PURE__*/react.createElement("span", (0,esm_extends/* default */.Z)({
    role: "img",
    "aria-label": icon.name
  }, restProps, {
    ref: ref,
    tabIndex: iconTabIndex,
    onClick: onClick,
    className: classString
  }), /*#__PURE__*/react.createElement(components_IconBase, {
    icon: icon,
    primaryColor: primaryColor,
    secondaryColor: secondaryColor,
    style: svgStyle
  }));
});
Icon.displayName = 'AntdIcon';
Icon.getTwoToneColor = getTwoToneColor;
Icon.setTwoToneColor = setTwoToneColor;
/* harmony default export */ var AntdIcon = (Icon);
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-card/node_modules/@ant-design/icons/es/icons/RightOutlined.js

// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY




var RightOutlined = function RightOutlined(props, ref) {
  return /*#__PURE__*/react.createElement(AntdIcon, (0,esm_extends/* default */.Z)({}, props, {
    ref: ref,
    icon: asn_RightOutlined/* default */.Z
  }));
};
if (false) {}
/* harmony default export */ var icons_RightOutlined = (/*#__PURE__*/react.forwardRef(RightOutlined));
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-utils/es/components/LabelIconTip/index.js + 2 modules
var LabelIconTip = __webpack_require__(86333);
// EXTERNAL MODULE: ./node_modules/antd/es/config-provider/index.js + 7 modules
var config_provider = __webpack_require__(28459);
// EXTERNAL MODULE: ./node_modules/antd/es/grid/index.js
var grid = __webpack_require__(75302);
// EXTERNAL MODULE: ./node_modules/antd/es/tabs/index.js + 30 modules
var es_tabs = __webpack_require__(48096);
// EXTERNAL MODULE: ./node_modules/omit.js/es/index.js
var es = __webpack_require__(97435);
// EXTERNAL MODULE: ./node_modules/rc-util/es/hooks/useMergedState.js
var useMergedState = __webpack_require__(21770);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-card/es/components/Actions/index.js + 1 modules
var Actions = __webpack_require__(80171);
// EXTERNAL MODULE: ./node_modules/antd/es/row/index.js
var row = __webpack_require__(71230);
// EXTERNAL MODULE: ./node_modules/antd/es/col/index.js
var col = __webpack_require__(15746);
// EXTERNAL MODULE: ./node_modules/@ant-design/cssinjs/es/index.js + 35 modules
var cssinjs_es = __webpack_require__(36846);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-provider/es/useStyle/index.js
var useStyle = __webpack_require__(98082);
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-card/es/components/Loading/style.js




var cardLoading = new cssinjs_es/* Keyframes */.E4('card-loading', {
  '0%': {
    backgroundPosition: '0 50%'
  },
  '50%': {
    backgroundPosition: '100% 50%'
  },
  '100%': {
    backgroundPosition: '0 50%'
  }
});
var genProStyle = function genProStyle(token) {
  return (0,defineProperty/* default */.Z)({}, token.componentCls, (0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)({
    '&-loading': {
      overflow: 'hidden'
    },
    '&-loading &-body': {
      userSelect: 'none'
    }
  }, "".concat(token.componentCls, "-loading-content"), {
    width: '100%',
    p: {
      marginBlock: 0,
      marginInline: 0
    }
  }), "".concat(token.componentCls, "-loading-block"), {
    height: '14px',
    marginBlock: '4px',
    background: "linear-gradient(90deg, rgba(54, 61, 64, 0.2), rgba(54, 61, 64, 0.4), rgba(54, 61, 64, 0.2))",
    backgroundSize: '600% 600%',
    borderRadius: token.borderRadius,
    animationName: cardLoading,
    animationDuration: '1.4s',
    animationTimingFunction: 'ease',
    animationIterationCount: 'infinite'
  }));
};
function style_useStyle(prefixCls) {
  return (0,useStyle/* useStyle */.Xj)('ProCardLoading', function (token) {
    var proToken = (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, token), {}, {
      componentCls: ".".concat(prefixCls)
    });
    return [genProStyle(proToken)];
  });
}
// EXTERNAL MODULE: ./node_modules/react/jsx-runtime.js
var jsx_runtime = __webpack_require__(85893);
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-card/es/components/Loading/index.js





var Loading = function Loading(props) {
  var style = props.style,
    prefix = props.prefix;
  var _useStyle = style_useStyle(prefix || 'ant-pro-card'),
    wrapSSR = _useStyle.wrapSSR;
  return wrapSSR( /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
    className: "".concat(prefix, "-loading-content"),
    style: style,
    children: [/*#__PURE__*/(0,jsx_runtime.jsx)(row/* default */.Z, {
      gutter: 8,
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 22,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
          className: "".concat(prefix, "-loading-block")
        })
      })
    }), /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
      gutter: 8,
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 8,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
          className: "".concat(prefix, "-loading-block")
        })
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 15,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
          className: "".concat(prefix, "-loading-block")
        })
      })]
    }), /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
      gutter: 8,
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 6,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
          className: "".concat(prefix, "-loading-block")
        })
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 18,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
          className: "".concat(prefix, "-loading-block")
        })
      })]
    }), /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
      gutter: 8,
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 13,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
          className: "".concat(prefix, "-loading-block")
        })
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 9,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
          className: "".concat(prefix, "-loading-block")
        })
      })]
    }), /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
      gutter: 8,
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 4,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
          className: "".concat(prefix, "-loading-block")
        })
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 3,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
          className: "".concat(prefix, "-loading-block")
        })
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 16,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
          className: "".concat(prefix, "-loading-block")
        })
      })]
    })]
  }));
};
/* harmony default export */ var components_Loading = (Loading);
// EXTERNAL MODULE: ./node_modules/antd/es/version/index.js + 1 modules
var version = __webpack_require__(67159);
// EXTERNAL MODULE: ./node_modules/rc-util/es/Children/toArray.js
var toArray = __webpack_require__(50344);
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-card/es/components/TabPane/index.js
/* provided dependency */ var process = __webpack_require__(34155);


var TabPane_excluded = ["tab", "children"],
  _excluded2 = ["key", "tab", "tabKey", "disabled", "destroyInactiveTabPane", "children", "className", "style", "cardProps"];








function filter(items) {
  return items.filter(function (item) {
    return item;
  });
}
function useLegacyItems(items, children, tabs) {
  if (items) {
    return items.map(function (item) {
      return (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, item), {}, {
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(components_Card, (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, tabs === null || tabs === void 0 ? void 0 : tabs.cardProps), {}, {
          children: item.children
        }))
      });
    });
  }
  (0,warning/* noteOnce */.ET)(!tabs, 'Tabs.TabPane is deprecated. Please use \`items\` directly.');
  var childrenItems = (0,toArray/* default */.Z)(children).map(function (node) {
    if ( /*#__PURE__*/react.isValidElement(node)) {
      var key = node.key,
        props = node.props;
      var _ref = props || {},
        tab = _ref.tab,
        tempChild = _ref.children,
        restProps = (0,objectWithoutProperties/* default */.Z)(_ref, TabPane_excluded);
      var item = (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({
        key: String(key)
      }, restProps), {}, {
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(components_Card, (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, tabs === null || tabs === void 0 ? void 0 : tabs.cardProps), {}, {
          children: tempChild
        })),
        label: tab
      });
      return item;
    }
    return null;
  });
  return filter(childrenItems);
}
/**
 * @deprecated ProComponets 3.0
 */
var TabPane = function TabPane(props) {
  var _useContext = (0,react.useContext)(config_provider/* default.ConfigContext */.ZP.ConfigContext),
    getPrefixCls = _useContext.getPrefixCls;
  // \u5982\u679C\u662Fantd v5 \u5219\u8FD4\u56DE\u4E3A\u7A7A
  if (version/* default */.Z.startsWith('5')) {
    return /*#__PURE__*/(0,jsx_runtime.jsx)(jsx_runtime.Fragment, {});
  } else {
    var key = props.key,
      tab = props.tab,
      tabKey = props.tabKey,
      disabled = props.disabled,
      destroyInactiveTabPane = props.destroyInactiveTabPane,
      children = props.children,
      className = props.className,
      style = props.style,
      cardProps = props.cardProps,
      rest = (0,objectWithoutProperties/* default */.Z)(props, _excluded2);
    var prefixCls = getPrefixCls('pro-card-tabpane');
    var tabPaneClassName = classnames_default()(prefixCls, className);
    return /*#__PURE__*/(0,jsx_runtime.jsx)(es_tabs/* default */.Z.TabPane, (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({
      tabKey: tabKey,
      tab: tab,
      className: tabPaneClassName,
      style: style,
      disabled: disabled,
      destroyInactiveTabPane: destroyInactiveTabPane
    }, rest), {}, {
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(components_Card, (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, cardProps), {}, {
        children: children
      }))
    }), key);
  }
};
if (typeof process !== 'undefined' && "production" !== 'production') {}
/* harmony default export */ var components_TabPane = (TabPane);
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-card/es/components/Card/style.js



var genActiveStyle = function genActiveStyle(token) {
  return {
    backgroundColor: token.controlItemBgActive,
    borderColor: token.controlOutline
  };
};
var genProCardStyle = function genProCardStyle(token) {
  var componentCls = token.componentCls;
  return (0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)({}, componentCls, (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({
    position: 'relative',
    display: 'flex',
    flexDirection: 'column',
    boxSizing: 'border-box',
    width: '100%',
    marginBlock: 0,
    marginInline: 0,
    paddingBlock: 0,
    paddingInline: 0,
    backgroundColor: token.colorBgContainer,
    borderRadius: token.borderRadius
  }, useStyle/* resetComponent */.Wf === null || useStyle/* resetComponent */.Wf === void 0 ? void 0 : (0,useStyle/* resetComponent */.Wf)(token)), {}, (0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)({
    '&-box-shadow': {
      boxShadow: '0 1px 2px -2px #00000029, 0 3px 6px #0000001f, 0 5px 12px 4px #00000017',
      borderColor: 'transparent'
    },
    '&-col': {
      width: '100%'
    },
    '&-border': {
      border: "".concat(token.lineWidth, "px ").concat(token.lineType, " ").concat(token.colorSplit)
    },
    '&-hoverable': (0,defineProperty/* default */.Z)({
      cursor: 'pointer',
      transition: 'box-shadow 0.3s, border-color 0.3s',
      '&:hover': {
        borderColor: 'transparent',
        boxShadow: '0 1px 2px -2px #00000029, 0 3px 6px #0000001f, 0 5px 12px 4px #00000017'
      }
    }, "&".concat(componentCls, "-checked:hover"), {
      borderColor: token.controlOutline
    }),
    '&-checked': (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, genActiveStyle(token)), {}, {
      '&::after': {
        position: 'absolute',
        insetBlockStart: 2,
        insetInlineEnd: 2,
        width: 0,
        height: 0,
        border: "6px solid ".concat(token.colorPrimary),
        borderBlockEnd: '6px solid transparent',
        borderInlineStart: '6px solid transparent',
        borderStartEndRadius: 2,
        content: '""'
      }
    }),
    '&:focus': (0,objectSpread2/* default */.Z)({}, genActiveStyle(token)),
    '&&-ghost': (0,defineProperty/* default */.Z)({
      backgroundColor: 'transparent'
    }, "> ".concat(componentCls), {
      '&-header': {
        paddingInlineEnd: 0,
        paddingBlockEnd: token.padding,
        paddingInlineStart: 0
      },
      '&-body': {
        paddingBlock: 0,
        paddingInline: 0,
        backgroundColor: 'transparent'
      }
    }),
    '&&-split > &-body': {
      paddingBlock: 0,
      paddingInline: 0
    },
    '&&-contain-card > &-body': {
      display: 'flex'
    }
  }, "".concat(componentCls, "-body-direction-column"), {
    flexDirection: 'column'
  }), "".concat(componentCls, "-body-wrap"), {
    flexWrap: 'wrap'
  }), '&&-collapse', (0,defineProperty/* default */.Z)({}, "> ".concat(componentCls), {
    '&-header': {
      paddingBlockEnd: token.padding,
      borderBlockEnd: 0
    },
    '&-body': {
      display: 'none'
    }
  })), "".concat(componentCls, "-header"), {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingInline: token.paddingLG,
    paddingBlock: token.padding,
    paddingBlockEnd: 0,
    '&-border': {
      '&': {
        paddingBlockEnd: token.padding
      },
      borderBlockEnd: "".concat(token.lineWidth, "px ").concat(token.lineType, " ").concat(token.colorSplit)
    },
    '&-collapsible': {
      cursor: 'pointer'
    }
  }), "".concat(componentCls, "-title"), {
    color: token.colorText,
    fontWeight: 500,
    fontSize: token.fontSizeLG,
    lineHeight: token.lineHeight
  }), "".concat(componentCls, "-extra"), {
    color: token.colorText
  }), "".concat(componentCls, "-type-inner"), (0,defineProperty/* default */.Z)({}, "".concat(componentCls, "-header"), {
    backgroundColor: token.colorFillAlter
  })), "".concat(componentCls, "-collapsible-icon"), {
    marginInlineEnd: token.marginXS,
    color: token.colorIconHover,
    ':hover': {
      color: token.colorPrimaryHover
    },
    '& svg': {
      transition: "transform ".concat(token.motionDurationMid)
    }
  }), "".concat(componentCls, "-body"), {
    display: 'block',
    boxSizing: 'border-box',
    height: '100%',
    paddingInline: token.paddingLG,
    paddingBlock: token.padding,
    '&-center': {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    }
  }), '&&-size-small', (0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)({}, componentCls, {
    '&-header': {
      paddingInline: token.paddingSM,
      paddingBlock: token.paddingXS,
      paddingBlockEnd: 0,
      '&-border': {
        paddingBlockEnd: token.paddingXS
      }
    },
    '&-title': {
      fontSize: token.fontSize
    },
    '&-body': {
      paddingInline: token.paddingSM,
      paddingBlock: token.paddingSM
    }
  }), "".concat(componentCls, "-header").concat(componentCls, "-header-collapsible"), {
    paddingBlock: token.paddingXS
  })))), "".concat(componentCls, "-col"), (0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)({}, "&".concat(componentCls, "-split-vertical"), {
    borderInlineEnd: "".concat(token.lineWidth, "px ").concat(token.lineType, " ").concat(token.colorSplit)
  }), "&".concat(componentCls, "-split-horizontal"), {
    borderBlockEnd: "".concat(token.lineWidth, "px ").concat(token.lineType, " ").concat(token.colorSplit)
  })), "".concat(componentCls, "-tabs"), (0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)({}, "".concat(token.antCls, "-tabs-top > ").concat(token.antCls, "-tabs-nav"), (0,defineProperty/* default */.Z)({
    marginBlockEnd: 0
  }, "".concat(token.antCls, "-tabs-nav-list"), {
    marginBlockStart: token.marginXS,
    paddingInlineStart: token.padding
  })), "".concat(token.antCls, "-tabs-bottom > ").concat(token.antCls, "-tabs-nav"), (0,defineProperty/* default */.Z)({
    marginBlockEnd: 0
  }, "".concat(token.antCls, "-tabs-nav-list"), {
    paddingInlineStart: token.padding
  })), "".concat(token.antCls, "-tabs-left"), (0,defineProperty/* default */.Z)({}, "".concat(token.antCls, "-tabs-content-holder"), (0,defineProperty/* default */.Z)({}, "".concat(token.antCls, "-tabs-content"), (0,defineProperty/* default */.Z)({}, "".concat(token.antCls, "-tabs-tabpane"), {
    paddingInlineStart: 0
  })))), "".concat(token.antCls, "-tabs-left > ").concat(token.antCls, "-tabs-nav"), (0,defineProperty/* default */.Z)({
    marginInlineEnd: 0
  }, "".concat(token.antCls, "-tabs-nav-list"), {
    paddingBlockStart: token.padding
  })), "".concat(token.antCls, "-tabs-right"), (0,defineProperty/* default */.Z)({}, "".concat(token.antCls, "-tabs-content-holder"), (0,defineProperty/* default */.Z)({}, "".concat(token.antCls, "-tabs-content"), (0,defineProperty/* default */.Z)({}, "".concat(token.antCls, "-tabs-tabpane"), {
    paddingInlineStart: 0
  })))), "".concat(token.antCls, "-tabs-right > ").concat(token.antCls, "-tabs-nav"), (0,defineProperty/* default */.Z)({}, "".concat(token.antCls, "-tabs-nav-list"), {
    paddingBlockStart: token.padding
  })));
};
var GRID_COLUMNS = 24;
var genColStyle = function genColStyle(index, token) {
  var componentCls = token.componentCls;
  if (index === 0) {
    return (0,defineProperty/* default */.Z)({}, "".concat(componentCls, "-col-0"), {
      display: 'none'
    });
  }
  return (0,defineProperty/* default */.Z)({}, "".concat(componentCls, "-col-").concat(index), {
    flexShrink: 0,
    width: "".concat(index / GRID_COLUMNS * 100, "%")
  });
};
var genGridStyle = function genGridStyle(token) {
  return Array(GRID_COLUMNS + 1).fill(1).map(function (_, index) {
    return genColStyle(index, token);
  });
};
function Card_style_useStyle(prefixCls) {
  return (0,useStyle/* useStyle */.Xj)('ProCard', function (token) {
    var proCardToken = (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, token), {}, {
      componentCls: ".".concat(prefixCls)
    });
    return [genProCardStyle(proCardToken), genGridStyle(proCardToken)];
  });
}
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-card/es/components/Card/index.js





var Card_excluded = ["className", "style", "bodyStyle", "headStyle", "title", "subTitle", "extra", "tip", "wrap", "layout", "loading", "gutter", "tooltip", "split", "headerBordered", "bordered", "boxShadow", "children", "size", "actions", "ghost", "hoverable", "direction", "collapsed", "collapsible", "collapsibleIconRender", "defaultCollapsed", "onCollapse", "checked", "onChecked", "tabs", "type"];













var Card = /*#__PURE__*/react.forwardRef(function (props, ref) {
  var _classNames2;
  var className = props.className,
    style = props.style,
    bodyStyle = props.bodyStyle,
    headStyle = props.headStyle,
    title = props.title,
    subTitle = props.subTitle,
    extra = props.extra,
    tip = props.tip,
    _props$wrap = props.wrap,
    wrap = _props$wrap === void 0 ? false : _props$wrap,
    layout = props.layout,
    loading = props.loading,
    _props$gutter = props.gutter,
    gutter = _props$gutter === void 0 ? 0 : _props$gutter,
    tooltip = props.tooltip,
    split = props.split,
    _props$headerBordered = props.headerBordered,
    headerBordered = _props$headerBordered === void 0 ? false : _props$headerBordered,
    _props$bordered = props.bordered,
    bordered = _props$bordered === void 0 ? false : _props$bordered,
    _props$boxShadow = props.boxShadow,
    boxShadow = _props$boxShadow === void 0 ? false : _props$boxShadow,
    children = props.children,
    size = props.size,
    actions = props.actions,
    _props$ghost = props.ghost,
    ghost = _props$ghost === void 0 ? false : _props$ghost,
    _props$hoverable = props.hoverable,
    hoverable = _props$hoverable === void 0 ? false : _props$hoverable,
    direction = props.direction,
    controlCollapsed = props.collapsed,
    _props$collapsible = props.collapsible,
    collapsible = _props$collapsible === void 0 ? false : _props$collapsible,
    collapsibleIconRender = props.collapsibleIconRender,
    _props$defaultCollaps = props.defaultCollapsed,
    defaultCollapsed = _props$defaultCollaps === void 0 ? false : _props$defaultCollaps,
    onCollapse = props.onCollapse,
    checked = props.checked,
    onChecked = props.onChecked,
    tabs = props.tabs,
    type = props.type,
    rest = (0,objectWithoutProperties/* default */.Z)(props, Card_excluded);
  var _useContext = (0,react.useContext)(config_provider/* default.ConfigContext */.ZP.ConfigContext),
    getPrefixCls = _useContext.getPrefixCls;
  var screens = grid/* default */.ZP.useBreakpoint() || {
    lg: true,
    md: true,
    sm: true,
    xl: false,
    xs: false,
    xxl: false
  };
  var _useMergedState = (0,useMergedState/* default */.Z)(defaultCollapsed, {
      value: controlCollapsed,
      onChange: onCollapse
    }),
    _useMergedState2 = (0,slicedToArray/* default */.Z)(_useMergedState, 2),
    collapsed = _useMergedState2[0],
    setCollapsed = _useMergedState2[1];

  // \u987A\u5E8F\u51B3\u5B9A\u5982\u4F55\u8FDB\u884C\u54CD\u5E94\u5F0F\u53D6\u503C\uFF0C\u6309\u6700\u5927\u54CD\u5E94\u503C\u4F9D\u6B21\u53D6\u503C\uFF0C\u8BF7\u52FF\u4FEE\u6539\u3002
  var responsiveArray = ['xxl', 'xl', 'lg', 'md', 'sm', 'xs'];
  // \u4FEE\u6539\u7EC4\u5408\u4F20\u7ED9antd tabs\u7684\u53C2\u6570
  // @ts-ignore
  var ModifyTabItemsContent = useLegacyItems(tabs === null || tabs === void 0 ? void 0 : tabs.items, children, tabs);

  /**
   * \u6839\u636E\u54CD\u5E94\u5F0F\u83B7\u53D6 gutter, \u53C2\u8003 antd \u5B9E\u73B0
   *
   * @param gutter Gutter
   */
  var getNormalizedGutter = function getNormalizedGutter(gut) {
    var results = [0, 0];
    var normalizedGutter = Array.isArray(gut) ? gut : [gut, 0];
    normalizedGutter.forEach(function (g, index) {
      if ((0,esm_typeof/* default */.Z)(g) === 'object') {
        for (var i = 0; i < responsiveArray.length; i += 1) {
          var breakpoint = responsiveArray[i];
          if (screens[breakpoint] && g[breakpoint] !== undefined) {
            results[index] = g[breakpoint];
            break;
          }
        }
      } else {
        results[index] = g || 0;
      }
    });
    return results;
  };

  /**
   * \u6839\u636E\u6761\u4EF6\u8FD4\u56DE style\uFF0C\u8D1F\u8D23\u8FD4\u56DE\u7A7A\u5BF9\u8C61
   *
   * @param withStyle \u662F\u5426\u7B26\u5408\u6761\u4EF6
   * @param appendStyle \u5982\u679C\u7B26\u5408\u6761\u4EF6\u8981\u8FD4\u56DE\u7684 style \u5C5E\u6027
   */
  var getStyle = function getStyle(withStyle, appendStyle) {
    return withStyle ? appendStyle : {};
  };
  var getColSpanStyle = function getColSpanStyle(colSpan) {
    var span = colSpan;

    // colSpan \u54CD\u5E94\u5F0F
    if ((0,esm_typeof/* default */.Z)(colSpan) === 'object') {
      for (var i = 0; i < responsiveArray.length; i += 1) {
        var breakpoint = responsiveArray[i];
        if (screens !== null && screens !== void 0 && screens[breakpoint] && (colSpan === null || colSpan === void 0 ? void 0 : colSpan[breakpoint]) !== undefined) {
          span = colSpan[breakpoint];
          break;
        }
      }
    }

    // \u5F53 colSpan \u4E3A 30% \u6216 300px \u65F6
    var colSpanStyle = getStyle(typeof span === 'string' && /\\d%|\\dpx/i.test(span), {
      width: span,
      flexShrink: 0
    });
    return {
      span: span,
      colSpanStyle: colSpanStyle
    };
  };
  var prefixCls = getPrefixCls('pro-card');
  var _useStyle = Card_style_useStyle(prefixCls),
    wrapSSR = _useStyle.wrapSSR,
    hashId = _useStyle.hashId;
  var _getNormalizedGutter = getNormalizedGutter(gutter),
    _getNormalizedGutter2 = (0,slicedToArray/* default */.Z)(_getNormalizedGutter, 2),
    horizontalGutter = _getNormalizedGutter2[0],
    verticalGutter = _getNormalizedGutter2[1];

  // \u5224\u65AD\u662F\u5426\u5957\u4E86\u5361\u7247\uFF0C\u5982\u679C\u5957\u4E86\u7684\u8BDD\u5C06\u81EA\u8EAB\u5361\u7247\u5185\u90E8\u5185\u5BB9\u7684 padding \u8BBE\u7F6E\u4E3A0
  var containProCard = false;
  var childrenArray = react.Children.toArray(children);
  var childrenModified = childrenArray.map(function (element, index) {
    var _element$type;
    if (element !== null && element !== void 0 && (_element$type = element.type) !== null && _element$type !== void 0 && _element$type.isProCard) {
      containProCard = true;

      // \u5BBD\u5EA6
      var colSpan = element.props.colSpan;
      var _getColSpanStyle = getColSpanStyle(colSpan),
        span = _getColSpanStyle.span,
        colSpanStyle = _getColSpanStyle.colSpanStyle;
      var columnClassName = classnames_default()(["".concat(prefixCls, "-col")], hashId, (0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)({}, "".concat(prefixCls, "-split-vertical"), split === 'vertical' && index !== childrenArray.length - 1), "".concat(prefixCls, "-split-horizontal"), split === 'horizontal' && index !== childrenArray.length - 1), "".concat(prefixCls, "-col-").concat(span), typeof span === 'number' && span >= 0 && span <= 24));
      var wrappedElement = wrapSSR( /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        style: (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, colSpanStyle), getStyle(horizontalGutter > 0, {
          paddingInlineEnd: horizontalGutter / 2,
          paddingInlineStart: horizontalGutter / 2
        })), getStyle(verticalGutter > 0, {
          paddingBlockStart: verticalGutter / 2,
          paddingBlockEnd: verticalGutter / 2
        })),
        className: columnClassName,
        children: /*#__PURE__*/react.cloneElement(element)
      }));
      return /*#__PURE__*/react.cloneElement(wrappedElement, {
        key: "pro-card-col-".concat((element === null || element === void 0 ? void 0 : element.key) || index)
      });
    }
    return element;
  });
  var cardCls = classnames_default()("".concat(prefixCls), className, hashId, (_classNames2 = {}, (0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)(_classNames2, "".concat(prefixCls, "-border"), bordered), "".concat(prefixCls, "-box-shadow"), boxShadow), "".concat(prefixCls, "-contain-card"), containProCard), "".concat(prefixCls, "-loading"), loading), "".concat(prefixCls, "-split"), split === 'vertical' || split === 'horizontal'), "".concat(prefixCls, "-ghost"), ghost), "".concat(prefixCls, "-hoverable"), hoverable), "".concat(prefixCls, "-size-").concat(size), size), "".concat(prefixCls, "-type-").concat(type), type), "".concat(prefixCls, "-collapse"), collapsed), (0,defineProperty/* default */.Z)(_classNames2, "".concat(prefixCls, "-checked"), checked)));
  var bodyCls = classnames_default()("".concat(prefixCls, "-body"), hashId, (0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)({}, "".concat(prefixCls, "-body-center"), layout === 'center'), "".concat(prefixCls, "-body-direction-column"), split === 'horizontal' || direction === 'column'), "".concat(prefixCls, "-body-wrap"), wrap && containProCard));
  var cardBodyStyle = bodyStyle;
  var loadingDOM = /*#__PURE__*/react.isValidElement(loading) ? loading : /*#__PURE__*/(0,jsx_runtime.jsx)(components_Loading, {
    prefix: prefixCls,
    style: (bodyStyle === null || bodyStyle === void 0 ? void 0 : bodyStyle.padding) === 0 || (bodyStyle === null || bodyStyle === void 0 ? void 0 : bodyStyle.padding) === '0px' ? {
      padding: 24
    } : undefined
  });
  // \u975E\u53D7\u63A7\u60C5\u51B5\u4E0B\u5C55\u793A
  var collapsibleButton = collapsible && controlCollapsed === undefined && (collapsibleIconRender ? collapsibleIconRender({
    collapsed: collapsed
  }) : /*#__PURE__*/(0,jsx_runtime.jsx)(icons_RightOutlined, {
    rotate: !collapsed ? 90 : undefined,
    className: "".concat(prefixCls, "-collapsible-icon ").concat(hashId).trim()
  }));
  return wrapSSR( /*#__PURE__*/(0,jsx_runtime.jsxs)("div", (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({
    className: cardCls,
    style: style,
    ref: ref,
    onClick: function onClick(e) {
      var _rest$onClick;
      onChecked === null || onChecked === void 0 || onChecked(e);
      rest === null || rest === void 0 || (_rest$onClick = rest.onClick) === null || _rest$onClick === void 0 || _rest$onClick.call(rest, e);
    }
  }, (0,es/* default */.Z)(rest, ['prefixCls', 'colSpan'])), {}, {
    children: [(title || extra || collapsibleButton) && /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
      className: classnames_default()("".concat(prefixCls, "-header"), hashId, (0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)({}, "".concat(prefixCls, "-header-border"), headerBordered || type === 'inner'), "".concat(prefixCls, "-header-collapsible"), collapsibleButton)),
      style: headStyle,
      onClick: function onClick() {
        if (collapsibleButton) setCollapsed(!collapsed);
      },
      children: [/*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
        className: "".concat(prefixCls, "-title ").concat(hashId).trim(),
        children: [collapsibleButton, /*#__PURE__*/(0,jsx_runtime.jsx)(LabelIconTip/* LabelIconTip */.G, {
          label: title,
          tooltip: tooltip || tip,
          subTitle: subTitle
        })]
      }), extra && /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        className: "".concat(prefixCls, "-extra ").concat(hashId).trim(),
        onClick: function onClick(e) {
          return e.stopPropagation();
        },
        children: extra
      })]
    }), tabs ? /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
      className: "".concat(prefixCls, "-tabs ").concat(hashId).trim(),
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_tabs/* default */.Z, (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({
        onChange: tabs.onChange
      }, tabs), {}, {
        // @ts-ignore
        items: ModifyTabItemsContent,
        children: loading ? loadingDOM : children
      }))
    }) : /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
      className: bodyCls,
      style: cardBodyStyle,
      children: loading ? loadingDOM : childrenModified
    }), actions ? /*#__PURE__*/(0,jsx_runtime.jsx)(Actions/* default */.Z, {
      actions: actions,
      prefixCls: prefixCls
    }) : null]
  })));
});
/* harmony default export */ var components_Card = (Card);
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-card/es/components/Divider/style.js



var genDividerStyle = function genDividerStyle(token) {
  var componentCls = token.componentCls;
  return (0,defineProperty/* default */.Z)({}, componentCls, {
    '&-divider': {
      flex: 'none',
      width: token.lineWidth,
      marginInline: token.marginXS,
      marginBlock: token.marginLG,
      backgroundColor: token.colorSplit,
      '&-horizontal': {
        width: 'initial',
        height: token.lineWidth,
        marginInline: token.marginLG,
        marginBlock: token.marginXS
      }
    },
    '&&-size-small &-divider': {
      marginBlock: token.marginLG,
      marginInline: token.marginXS,
      '&-horizontal': {
        marginBlock: token.marginXS,
        marginInline: token.marginLG
      }
    }
  });
};
function Divider_style_useStyle(prefixCls) {
  return (0,useStyle/* useStyle */.Xj)('ProCardDivider', function (token) {
    var proCardDividerToken = (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, token), {}, {
      componentCls: ".".concat(prefixCls)
    });
    return [genDividerStyle(proCardDividerToken)];
  });
}
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-card/es/components/Divider/index.js






var ProCardDivider = function ProCardDivider(props) {
  var _useContext = (0,react.useContext)(config_provider/* default.ConfigContext */.ZP.ConfigContext),
    getPrefixCls = _useContext.getPrefixCls;
  var proCardPrefixCls = getPrefixCls('pro-card');
  var prefixCls = "".concat(proCardPrefixCls, "-divider");
  var _useStyle = Divider_style_useStyle(proCardPrefixCls),
    wrapSSR = _useStyle.wrapSSR,
    hashId = _useStyle.hashId;
  var className = props.className,
    _props$style = props.style,
    style = _props$style === void 0 ? {} : _props$style,
    type = props.type;
  var classString = classnames_default()(prefixCls, className, hashId, (0,defineProperty/* default */.Z)({}, "".concat(prefixCls, "-").concat(type), type));
  return wrapSSR( /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
    className: classString,
    style: style
  }));
};
/* harmony default export */ var Divider = (ProCardDivider);
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-card/es/ProCard.js





var Group = function Group(props) {
  return /*#__PURE__*/(0,jsx_runtime.jsx)(components_Card, (0,objectSpread2/* default */.Z)({
    bodyStyle: {
      padding: 0
    }
  }, props));
};

// \u5F53\u524D\u4E0D\u5BF9\u5E95\u5C42 Card \u505A\u5C01\u88C5\uFF0C\u4EC5\u6302\u8F7D\u5B50\u7EC4\u4EF6\uFF0C\u76F4\u63A5\u5BFC\u51FA
// @ts-ignore
var ProCard = components_Card;
ProCard.isProCard = true;
ProCard.Divider = Divider;
ProCard.TabPane = components_TabPane;
ProCard.Group = Group;
/* harmony default export */ var es_ProCard = (ProCard);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///58128
`)},80171:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`
// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  Z: function() { return /* binding */ Actions; }
});

// EXTERNAL MODULE: ./node_modules/classnames/index.js
var classnames = __webpack_require__(93967);
var classnames_default = /*#__PURE__*/__webpack_require__.n(classnames);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectSpread2.js
var objectSpread2 = __webpack_require__(1413);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(4942);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-provider/es/useStyle/index.js
var useStyle = __webpack_require__(98082);
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-card/es/components/Actions/style.js



var genActionsStyle = function genActionsStyle(token) {
  var componentCls = token.componentCls,
    antCls = token.antCls;
  return (0,defineProperty/* default */.Z)({}, "".concat(componentCls, "-actions"), (0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)({
    marginBlock: 0,
    marginInline: 0,
    paddingBlock: 0,
    paddingInline: 0,
    listStyle: 'none',
    display: 'flex',
    gap: token.marginXS,
    background: token.colorBgContainer,
    borderBlockStart: "".concat(token.lineWidth, "px ").concat(token.lineType, " ").concat(token.colorSplit),
    minHeight: 42
  }, "& > *", {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    display: 'flex',
    cursor: 'pointer',
    color: token.colorTextSecondary,
    transition: 'color 0.3s',
    '&:hover': {
      color: token.colorPrimaryHover
    }
  }), "& > li > div", {
    flex: 1,
    width: '100%',
    marginBlock: token.marginSM,
    marginInline: 0,
    color: token.colorTextSecondary,
    textAlign: 'center',
    a: {
      color: token.colorTextSecondary,
      transition: 'color 0.3s',
      '&:hover': {
        color: token.colorPrimaryHover
      }
    },
    div: (0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)({
      position: 'relative',
      display: 'block',
      minWidth: 32,
      fontSize: token.fontSize,
      lineHeight: token.lineHeight,
      cursor: 'pointer',
      '&:hover': {
        color: token.colorPrimaryHover,
        transition: 'color 0.3s'
      }
    }, "a:not(".concat(antCls, "-btn),\\n            > .anticon"), {
      display: 'inline-block',
      width: '100%',
      color: token.colorTextSecondary,
      lineHeight: '22px',
      transition: 'color 0.3s',
      '&:hover': {
        color: token.colorPrimaryHover
      }
    }), '.anticon', {
      fontSize: token.cardActionIconSize,
      lineHeight: '22px'
    }),
    '&:not(:last-child)': {
      borderInlineEnd: "".concat(token.lineWidth, "px ").concat(token.lineType, " ").concat(token.colorSplit)
    }
  }));
};
function style_useStyle(prefixCls) {
  return (0,useStyle/* useStyle */.Xj)('ProCardActions', function (token) {
    var proCardActionsToken = (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, token), {}, {
      componentCls: ".".concat(prefixCls),
      cardActionIconSize: 16
    });
    return [genActionsStyle(proCardActionsToken)];
  });
}
// EXTERNAL MODULE: ./node_modules/react/jsx-runtime.js
var jsx_runtime = __webpack_require__(85893);
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-card/es/components/Actions/index.js




var ProCardActions = function ProCardActions(props) {
  var actions = props.actions,
    prefixCls = props.prefixCls;
  var _useStyle = style_useStyle(prefixCls),
    wrapSSR = _useStyle.wrapSSR,
    hashId = _useStyle.hashId;
  if (Array.isArray(actions) && actions !== null && actions !== void 0 && actions.length) {
    return wrapSSR( /*#__PURE__*/(0,jsx_runtime.jsx)("ul", {
      className: classnames_default()("".concat(prefixCls, "-actions"), hashId),
      children: actions.map(function (action, index) {
        return (
          /*#__PURE__*/
          // eslint-disable-next-line react/no-array-index-key
          (0,jsx_runtime.jsx)("li", {
            style: {
              width: "".concat(100 / actions.length, "%"),
              padding: 0,
              margin: 0
            },
            className: classnames_default()("".concat(prefixCls, "-actions-item"), hashId),
            children: action
          }, "action-".concat(index))
        );
      })
    }));
  }
  return wrapSSR( /*#__PURE__*/(0,jsx_runtime.jsx)("ul", {
    className: classnames_default()("".concat(prefixCls, "-actions"), hashId),
    children: actions
  }));
};
/* harmony default export */ var Actions = (ProCardActions);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///80171
`)},24739:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   UW: function() { return /* binding */ ProFormGroup; }
/* harmony export */ });
/* harmony import */ var _ProForm__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(34994);









var ProFormGroup = _ProForm__WEBPACK_IMPORTED_MODULE_0__/* .ProForm */ .A.Group;//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiMjQ3MzkuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFvQztBQUNNO0FBQ0U7QUFDSjtBQUNRO0FBQ1I7QUFDSTtBQUNKO0FBQ3JCO0FBQ1osbUJBQW1CLHNEQUFPIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvQGFudC1kZXNpZ24vcHJvLWZvcm0vZXMvbGF5b3V0cy9pbmRleC5qcz8wZGFkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByb0Zvcm0gfSBmcm9tIFwiLi9Qcm9Gb3JtXCI7XG5leHBvcnQgeyBEcmF3ZXJGb3JtIH0gZnJvbSBcIi4vRHJhd2VyRm9ybVwiO1xuZXhwb3J0IHsgTGlnaHRGaWx0ZXIgfSBmcm9tIFwiLi9MaWdodEZpbHRlclwiO1xuZXhwb3J0IHsgTG9naW5Gb3JtIH0gZnJvbSBcIi4vTG9naW5Gb3JtXCI7XG5leHBvcnQgeyBMb2dpbkZvcm1QYWdlIH0gZnJvbSBcIi4vTG9naW5Gb3JtUGFnZVwiO1xuZXhwb3J0IHsgTW9kYWxGb3JtIH0gZnJvbSBcIi4vTW9kYWxGb3JtXCI7XG5leHBvcnQgeyBRdWVyeUZpbHRlciB9IGZyb20gXCIuL1F1ZXJ5RmlsdGVyXCI7XG5leHBvcnQgeyBTdGVwc0Zvcm0gfSBmcm9tIFwiLi9TdGVwc0Zvcm1cIjtcbmV4cG9ydCB7IFByb0Zvcm0gfTtcbmV4cG9ydCB2YXIgUHJvRm9ybUdyb3VwID0gUHJvRm9ybS5Hcm91cDsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=
//# sourceURL=webpack-internal:///24739
`)}}]);
