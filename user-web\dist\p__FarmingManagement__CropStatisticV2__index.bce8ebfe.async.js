"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[1360],{64029:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(1413);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(67294);
/* harmony import */ var _ant_design_icons_svg_es_asn_UpOutlined__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(92287);
/* harmony import */ var _components_AntdIcon__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(89099);

// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY



var UpOutlined = function UpOutlined(props, ref) {
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(_components_AntdIcon__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Z, (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z)((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z)({}, props), {}, {
    ref: ref,
    icon: _ant_design_icons_svg_es_asn_UpOutlined__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z
  }));
};
UpOutlined.displayName = 'UpOutlined';
/* harmony default export */ __webpack_exports__.Z = (/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(UpOutlined));//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNjQwMjkuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFxRTtBQUNyRTtBQUNBO0FBQytCO0FBQ3FDO0FBQ3RCO0FBQzlDO0FBQ0Esc0JBQXNCLGdEQUFtQixDQUFDLHFFQUFRLEVBQUUsNkZBQWEsQ0FBQyw2RkFBYSxHQUFHLFlBQVk7QUFDOUY7QUFDQSxVQUFVLHdGQUFhO0FBQ3ZCLEdBQUc7QUFDSDtBQUNBO0FBQ0EsbUVBQTRCLDZDQUFnQixZQUFZIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvQGFudC1kZXNpZ24vaWNvbnMvZXMvaWNvbnMvVXBPdXRsaW5lZC5qcz9hY2FlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBfb2JqZWN0U3ByZWFkIGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9vYmplY3RTcHJlYWQyXCI7XG4vLyBHRU5FUkFURSBCWSAuL3NjcmlwdHMvZ2VuZXJhdGUudHNcbi8vIERPTiBOT1QgRURJVCBJVCBNQU5VQUxMWVxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IFVwT3V0bGluZWRTdmcgZnJvbSBcIkBhbnQtZGVzaWduL2ljb25zLXN2Zy9lcy9hc24vVXBPdXRsaW5lZFwiO1xuaW1wb3J0IEFudGRJY29uIGZyb20gJy4uL2NvbXBvbmVudHMvQW50ZEljb24nO1xudmFyIFVwT3V0bGluZWQgPSBmdW5jdGlvbiBVcE91dGxpbmVkKHByb3BzLCByZWYpIHtcbiAgcmV0dXJuIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KEFudGRJY29uLCBfb2JqZWN0U3ByZWFkKF9vYmplY3RTcHJlYWQoe30sIHByb3BzKSwge30sIHtcbiAgICByZWY6IHJlZixcbiAgICBpY29uOiBVcE91dGxpbmVkU3ZnXG4gIH0pKTtcbn07XG5VcE91dGxpbmVkLmRpc3BsYXlOYW1lID0gJ1VwT3V0bGluZWQnO1xuZXhwb3J0IGRlZmF1bHQgLyojX19QVVJFX18qL1JlYWN0LmZvcndhcmRSZWYoVXBPdXRsaW5lZCk7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9
//# sourceURL=webpack-internal:///64029
`)},65402:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return /* binding */ CropStatisticV2; }
});

// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/objectSpread2.js
var objectSpread2 = __webpack_require__(97857);
var objectSpread2_default = /*#__PURE__*/__webpack_require__.n(objectSpread2);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-layout/es/components/PageContainer/index.js + 18 modules
var PageContainer = __webpack_require__(6110);
// EXTERNAL MODULE: ./src/.umi-production/exports.ts + 34 modules
var _umi_production_exports = __webpack_require__(7837);
// EXTERNAL MODULE: ./node_modules/antd/es/row/index.js
var row = __webpack_require__(71230);
// EXTERNAL MODULE: ./node_modules/antd/es/col/index.js
var col = __webpack_require__(15746);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./node_modules/zustand/esm/index.mjs + 1 modules
var esm = __webpack_require__(64529);
;// CONCATENATED MODULE: ./src/stores/CropDashboardV2/CropDashboardV2Store.tsx

var useCropDashboardV2Store = (0,esm/* create */.Ue)(function (set) {
  return {
    dataSource: {},
    setDataSource: function setDataSource(state) {
      console.log('Setting useCropDashboardV2Store dataSource:', state); // Log the new state
      set({
        dataSource: state
      });
    }
  };
});
// EXTERNAL MODULE: ./node_modules/antd/es/card/index.js + 3 modules
var card = __webpack_require__(76216);
// EXTERNAL MODULE: ./node_modules/antd/es/spin/index.js + 2 modules
var spin = __webpack_require__(75081);
// EXTERNAL MODULE: ./node_modules/numeral/numeral.js
var numeral = __webpack_require__(92077);
var numeral_default = /*#__PURE__*/__webpack_require__.n(numeral);
// EXTERNAL MODULE: ./node_modules/chart.js/dist/chart.js + 2 modules
var chart = __webpack_require__(65750);
// EXTERNAL MODULE: ./node_modules/react-chartjs-2/dist/index.js
var dist = __webpack_require__(26495);
// EXTERNAL MODULE: ./node_modules/react/jsx-runtime.js
var jsx_runtime = __webpack_require__(85893);
;// CONCATENATED MODULE: ./src/pages/FarmingManagement/CropStatisticV2/Components/CategoryStatistic/CategoryStatisticChart.tsx
// File path: src/ColumnChart.js






chart/* Chart */.kL.register(chart/* CategoryScale */.uw, chart/* LinearScale */.f$, chart/* BarElement */.ZL, chart/* Tooltip */.u, chart/* Legend */.De);
var ColumnChart = function ColumnChart() {
  var _useCropDashboardV2St = useCropDashboardV2Store(),
    dataSource = _useCropDashboardV2St.dataSource,
    setDataSource = _useCropDashboardV2St.setDataSource;
  var categories = dataSource.categories;

  // Prepare data for the chart
  var labels = categories.map(function (category) {
    return category.category_label;
  });
  var expectedValues = categories.map(function (category) {
    return category.total_exp_value;
  });
  var actualValues = categories.map(function (category) {
    return category.total_value;
  });
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  var data = {
    labels: labels,
    datasets: [{
      type: 'bar',
      label: formatMessage({
        id: 'common.exp_value'
      }),
      data: expectedValues,
      backgroundColor: '#48bb78',
      // xanh l\xE1
      yAxisID: 'y'
    }, {
      type: 'bar',
      label: formatMessage({
        id: 'common.real_value'
      }),
      data: actualValues,
      backgroundColor: '#ecc94b',
      // v\xE0ng
      yAxisID: 'y'
    }]
  };
  var options = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top'
      },
      tooltip: {
        mode: 'index',
        intersect: false
      }
    },
    scales: {
      y: {
        beginAtZero: true
      }
    }
  };
  return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
    style: {
      height: '500px',
      width: '100%'
    },
    children: /*#__PURE__*/(0,jsx_runtime.jsx)(dist/* Bar */.$Q, {
      data: data,
      options: options
    })
  });
};
/* harmony default export */ var CategoryStatisticChart = (ColumnChart);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/regeneratorRuntime.js
var regeneratorRuntime = __webpack_require__(15009);
var regeneratorRuntime_default = /*#__PURE__*/__webpack_require__.n(regeneratorRuntime);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(99289);
var asyncToGenerator_default = /*#__PURE__*/__webpack_require__.n(asyncToGenerator);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/slicedToArray.js
var slicedToArray = __webpack_require__(5574);
var slicedToArray_default = /*#__PURE__*/__webpack_require__.n(slicedToArray);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-table/es/Table.js + 76 modules
var Table = __webpack_require__(4894);
// EXTERNAL MODULE: ./node_modules/antd/es/app/index.js + 1 modules
var app = __webpack_require__(31418);
;// CONCATENATED MODULE: ./src/pages/FarmingManagement/CropStatisticV2/style.css
// extracted by mini-css-extract-plugin

;// CONCATENATED MODULE: ./src/pages/FarmingManagement/CropStatisticV2/Components/CategoryStatistic/CategoryStatisticTable.tsx












var CategoryStatisticTable = function CategoryStatisticTable() {
  var _App$useApp = app/* default */.Z.useApp(),
    message = _App$useApp.message;
  var actionRef = (0,react.useRef)();
  var _useCropDashboardV2St = useCropDashboardV2Store(),
    dataSource = _useCropDashboardV2St.dataSource;
  var categories = dataSource.categories;
  var _useState = (0,react.useState)(1),
    _useState2 = slicedToArray_default()(_useState, 2),
    current = _useState2[0],
    setCurrent = _useState2[1];
  var _useState3 = (0,react.useState)(10),
    _useState4 = slicedToArray_default()(_useState3, 2),
    pageSize = _useState4[0],
    setPageSize = _useState4[1];
  var _useState5 = (0,react.useState)([]),
    _useState6 = slicedToArray_default()(_useState5, 2),
    categoriesWithRowSpan = _useState6[0],
    setCategoriesWithRowSpan = _useState6[1];
  (0,react.useEffect)(function () {
    if (categories && categories.length > 0) {
      var updatedCategories = categories.map(function (item, index) {
        return objectSpread2_default()(objectSpread2_default()({}, item), {}, {
          rowSpan: 1
        });
      });
      var prevLabel = '';
      var prevRowSpan = 0;
      for (var i = 0; i < updatedCategories.length; i++) {
        var item = updatedCategories[i];
        if (item.group_label === prevLabel) {
          updatedCategories[i - prevRowSpan].rowSpan += 1;
          item.rowSpan = 0;
          prevRowSpan += 1;
        } else {
          prevLabel = item.group_label;
          prevRowSpan = 1;
        }
      }
      setCategoriesWithRowSpan(updatedCategories);
    }
  }, [categories]);
  if (!categories || categories.length === 0) {
    return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
      children: "No data"
    });
  }
  var intl = (0,_umi_production_exports.useIntl)();
  var columns = [{
    title: intl.formatMessage({
      id: 'common.category_group'
    }),
    dataIndex: 'group_label',
    width: 80
    // render: (text, record, index) => {
    //   const realIndex = (current - 1) * pageSize + index;
    //   const category = categoriesWithRowSpan[realIndex];
    //   return {
    //     children: <>{text}</>,
    //     props: {
    //       rowSpan: category.rowSpan,
    //     },
    //   };
    // },
  }, {
    title: intl.formatMessage({
      id: 'common.supplies'
    }),
    dataIndex: 'category_label',
    width: 80,
    align: 'left'
  }, {
    title: intl.formatMessage({
      id: 'common.exp_value'
    }),
    dataIndex: 'total_exp_value',
    width: 100,
    align: 'right',
    render: function render(dom, entity) {
      return numeral_default()(entity.total_exp_value).format('0,0');
    }
  }, {
    title: intl.formatMessage({
      id: 'common.real_value'
    }),
    dataIndex: 'total_value',
    width: 100,
    align: 'right',
    render: function render(dom, entity) {
      return numeral_default()(entity.total_value).format('0,0');
    }
  }, {
    title: intl.formatMessage({
      id: 'common.diff'
    }),
    dataIndex: 'difference',
    width: 100,
    align: 'right',
    render: function render(_, entity) {
      var difference = entity.total_value - entity.total_exp_value;
      var className = difference >= 0 ? 'text-green-500' : 'text-red-500';
      var sign = difference >= 0 ? '+' : '-';
      return /*#__PURE__*/(0,jsx_runtime.jsx)("span", {
        className: className,
        children: "".concat(sign).concat(numeral_default()(Math.abs(difference)).format('0,0'))
      });
    }
  }];
  return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
    className: "no-padding-right",
    children: /*#__PURE__*/(0,jsx_runtime.jsx)(Table/* default */.Z, {
      style: {
        paddingInline: 0
      },
      actionRef: actionRef,
      options: false,
      bordered: true,
      columns: columns,
      search: false,
      pagination: {
        pageSizeOptions: [10, 20, 50, 100],
        showSizeChanger: true,
        defaultPageSize: 10,
        onChange: function onChange(page, size) {
          setCurrent(page);
          setPageSize(size);
        }
      },
      scroll: {
        x: 'max-content'
      },
      request: ( /*#__PURE__*/function () {
        var _ref = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(params, sorter, filter) {
          var _current, _pageSize, startIndex, data;
          return regeneratorRuntime_default()().wrap(function _callee$(_context) {
            while (1) switch (_context.prev = _context.next) {
              case 0:
                _context.prev = 0;
                console.log('params', params);
                _current = params.current, _pageSize = params.pageSize;
                setCurrent(_current);
                setPageSize(_pageSize);
                startIndex = (_current - 1) * _pageSize;
                data = categoriesWithRowSpan.slice(startIndex, startIndex + _pageSize);
                return _context.abrupt("return", {
                  data: data,
                  success: true,
                  total: categories.length
                });
              case 10:
                _context.prev = 10;
                _context.t0 = _context["catch"](0);
                message.error("Error when getting Crop Items Statistic: ".concat(_context.t0.message));
                return _context.abrupt("return", Promise.resolve({
                  success: false
                }));
              case 14:
              case "end":
                return _context.stop();
            }
          }, _callee, null, [[0, 10]]);
        }));
        return function (_x, _x2, _x3) {
          return _ref.apply(this, arguments);
        };
      }()),
      rowKey: 'category_id'
    })
  });
};
/* harmony default export */ var CategoryStatistic_CategoryStatisticTable = (CategoryStatisticTable);
;// CONCATENATED MODULE: ./src/pages/FarmingManagement/CropStatisticV2/Components/CategoryStatistic/index.tsx








var CategoryStatisticCard = function CategoryStatisticCard(props) {
  var _useCropDashboardV2St = useCropDashboardV2Store(),
    dataSource = _useCropDashboardV2St.dataSource,
    setDataSource = _useCropDashboardV2St.setDataSource;
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  return /*#__PURE__*/(0,jsx_runtime.jsx)(card/* default */.Z, {
    title: /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
      className: "flex justify-between items-center",
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        className: "flex-1 text-left",
        children: formatMessage({
          id: 'common.supplies-cost-statistic'
        }).toUpperCase()
      }), /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
        className: "flex-1 text-right",
        children: [/*#__PURE__*/(0,jsx_runtime.jsxs)("span", {
          className: "text-base font-bold",
          children: [formatMessage({
            id: 'common.used-cost'
          }), ":", ' ']
        }), /*#__PURE__*/(0,jsx_runtime.jsxs)("span", {
          className: "text-primary-green text-base font-bold",
          children: [numeral_default()(dataSource.total_category_value).format('0,0'), " VN\\u0110"]
        })]
      })]
    }),
    children: dataSource.details ? /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
      gutter: [16, 16],
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 14,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(CategoryStatisticChart, {})
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 10,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(CategoryStatistic_CategoryStatisticTable, {})
      })]
    }) : /*#__PURE__*/(0,jsx_runtime.jsx)(spin/* default */.Z, {
      size: "large"
    })
  });
};
/* harmony default export */ var CategoryStatistic = (CategoryStatisticCard);
// EXTERNAL MODULE: ./src/services/crop.ts
var crop = __webpack_require__(52662);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/createClass.js
var createClass = __webpack_require__(72004);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/classCallCheck.js
var classCallCheck = __webpack_require__(12444);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/defineProperty.js
var defineProperty = __webpack_require__(9783);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/objectWithoutProperties.js
var objectWithoutProperties = __webpack_require__(13769);
var objectWithoutProperties_default = /*#__PURE__*/__webpack_require__.n(objectWithoutProperties);
// EXTERNAL MODULE: ./src/services/utils.ts
var utils = __webpack_require__(467);
;// CONCATENATED MODULE: ./src/services/crop-statistic-v2/cropStatistic-v2.ts







var _excluded = ["crop_status", "crop_id", "crop_list", "start_date", "end_date", "type", "work_type", "plant_id"],
  _excluded2 = ["start_date", "end_date", "crop_id_1", "crop_id_2"];


var handleError = function handleError(error) {
  console.log("Error in services: \\n".concat(error));
  throw error;
};
function getCropStatisticV2(_x) {
  return _getCropStatisticV.apply(this, arguments);
}
function _getCropStatisticV() {
  _getCropStatisticV = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(_ref) {
    var crop_status, crop_id, crop_list, start_date, end_date, type, work_type, plant_id, params, res;
    return regeneratorRuntime_default()().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          crop_status = _ref.crop_status, crop_id = _ref.crop_id, crop_list = _ref.crop_list, start_date = _ref.start_date, end_date = _ref.end_date, type = _ref.type, work_type = _ref.work_type, plant_id = _ref.plant_id, params = objectWithoutProperties_default()(_ref, _excluded);
          _context.prev = 1;
          _context.next = 4;
          return (0,_umi_production_exports.request)((0,utils/* generateAPIPath */.rH)("api/v2/cropStatistic-v2/all"), {
            method: 'GET',
            params: objectSpread2_default()({
              page: 1,
              size: 100,
              start_date: start_date,
              end_date: end_date,
              type: type,
              crop_list: JSON.stringify(crop_list),
              crop_id: crop_id,
              crop_status: crop_status,
              plant_id: plant_id,
              work_type: work_type
            }, (0,utils/* getParamsReqList */.vj)(params))
          });
        case 4:
          res = _context.sent;
          return _context.abrupt("return", res);
        case 8:
          _context.prev = 8;
          _context.t0 = _context["catch"](1);
          handleError(_context.t0);
          return _context.abrupt("return", {
            data: []
          });
        case 12:
        case "end":
          return _context.stop();
      }
    }, _callee, null, [[1, 8]]);
  }));
  return _getCropStatisticV.apply(this, arguments);
}
var ICropStatisticCompareParams = /*#__PURE__*/(/* unused pure expression or super */ null && (_createClass(function ICropStatisticCompareParams() {
  _classCallCheck(this, ICropStatisticCompareParams);
  _defineProperty(this, "start_date", void 0);
  _defineProperty(this, "end_date", void 0);
  _defineProperty(this, "crop_id_1", void 0);
  _defineProperty(this, "crop_id_2", void 0);
})));
function getCropCompareV2(_x2) {
  return _getCropCompareV.apply(this, arguments);
}
function _getCropCompareV() {
  _getCropCompareV = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2(_ref2) {
    var start_date, end_date, crop_id_1, crop_id_2, params, res;
    return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          start_date = _ref2.start_date, end_date = _ref2.end_date, crop_id_1 = _ref2.crop_id_1, crop_id_2 = _ref2.crop_id_2, params = objectWithoutProperties_default()(_ref2, _excluded2);
          _context2.prev = 1;
          _context2.next = 4;
          return (0,_umi_production_exports.request)((0,utils/* generateAPIPath */.rH)("api/v2/cropStatistic-v2/compare"), {
            method: 'GET',
            params: objectSpread2_default()({
              page: 1,
              size: 100,
              start_date: start_date,
              end_date: end_date,
              crop_id_1: crop_id_1,
              crop_id_2: crop_id_2
            }, (0,utils/* getParamsReqList */.vj)(params))
          });
        case 4:
          res = _context2.sent;
          return _context2.abrupt("return", res);
        case 8:
          _context2.prev = 8;
          _context2.t0 = _context2["catch"](1);
          handleError(_context2.t0);
          return _context2.abrupt("return", {
            data: []
          });
        case 12:
        case "end":
          return _context2.stop();
      }
    }, _callee2, null, [[1, 8]]);
  }));
  return _getCropCompareV.apply(this, arguments);
}
// EXTERNAL MODULE: ./src/services/plants.ts
var plants = __webpack_require__(18275);
;// CONCATENATED MODULE: ./src/stores/CropDashboardV2/CropDashboardV2CompareStore.tsx

var useCropCompareV2Store = (0,esm/* create */.Ue)(function (set) {
  return {
    dataSource: {},
    setDataSource: function setDataSource(state) {
      console.log('Setting useCropCompareV2Store dataSource:', state); // Log the new state
      set({
        dataSource: state
      });
    },
    resetDataSource: function resetDataSource() {
      set({
        dataSource: {}
      });
    }
  };
});
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/DownOutlined.js
var DownOutlined = __webpack_require__(34804);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/UpOutlined.js
var UpOutlined = __webpack_require__(64029);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/layouts/ProForm/index.js + 3 modules
var ProForm = __webpack_require__(34994);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/Select/index.js
var Select = __webpack_require__(64317);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/DateRangePicker/index.js
var DateRangePicker = __webpack_require__(34540);
// EXTERNAL MODULE: ./node_modules/antd/es/button/index.js + 9 modules
var es_button = __webpack_require__(14726);
// EXTERNAL MODULE: ./node_modules/dayjs/dayjs.min.js
var dayjs_min = __webpack_require__(27484);
var dayjs_min_default = /*#__PURE__*/__webpack_require__.n(dayjs_min);
// EXTERNAL MODULE: ./node_modules/antd/es/modal/index.js + 1 modules
var modal = __webpack_require__(85576);
;// CONCATENATED MODULE: ./src/pages/FarmingManagement/CropStatisticV2/Components/CompareModal/CategoryCompareTable.tsx








var CategoryCompareTable = function CategoryCompareTable() {
  var _App$useApp = app/* default */.Z.useApp(),
    message = _App$useApp.message;
  var _useCropCompareV2Stor = useCropCompareV2Store(),
    dataSource = _useCropCompareV2Stor.dataSource,
    setDataSource = _useCropCompareV2Stor.setDataSource;
  if (!dataSource.categories) {
    return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {});
  }
  var categories = dataSource.categories;
  var crop1Label = dataSource.crop_1_label;
  var crop2Label = dataSource.crop_2_label;
  var intl = (0,_umi_production_exports.useIntl)();
  var columns = [{
    title: intl.formatMessage({
      id: 'common.category_group'
    }),
    dataIndex: 'group_label',
    width: 80,
    render: function render(text, record, index) {
      if (index > 0 && categories[index - 1].group_label === text) {
        return {
          children: null,
          props: {
            rowSpan: 0
          }
        };
      }
      var rowSpan = 1;
      while (index + rowSpan < categories.length && categories[index + rowSpan].group_label === text) {
        rowSpan++;
      }
      return {
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(jsx_runtime.Fragment, {
          children: text
        }),
        props: {
          rowSpan: rowSpan
        }
      };
    },
    align: 'left'
  }, {
    title: intl.formatMessage({
      id: 'common.supplies'
    }),
    dataIndex: 'category_label',
    width: 80,
    align: 'left'
  }, {
    title: crop1Label,
    children: [{
      title: intl.formatMessage({
        id: 'common.qty'
      }),
      dataIndex: 'crop1_quantity',
      width: 100,
      render: function render(dom, entity, index, action, schema) {
        return /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
          children: [numeral_default()(entity.crop1_quantity).format('0,0'), " ", entity.uom_name]
        });
      },
      align: 'right'
    }, {
      title: intl.formatMessage({
        id: 'common.real_value'
      }),
      dataIndex: 'crop1_value',
      width: 100,
      render: function render(dom, entity, index, action, schema) {
        return numeral_default()(entity.crop1_value).format('0,0');
      },
      align: 'right'
    }]
  }, {
    title: crop2Label,
    children: [{
      title: intl.formatMessage({
        id: 'common.qty'
      }),
      dataIndex: 'crop2_quantity',
      width: 100,
      render: function render(dom, entity, index, action, schema) {
        return /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
          children: [numeral_default()(entity.crop2_quantity).format('0,0'), " ", entity.uom_name]
        });
      },
      align: 'right'
    }, {
      title: intl.formatMessage({
        id: 'common.real_value'
      }),
      dataIndex: 'crop2_value',
      width: 100,
      render: function render(dom, entity, index, action, schema) {
        return numeral_default()(entity.crop2_value).format('0,0');
      },
      align: 'right'
    }]
  },
  //c\u1ED9t ch\xEAnh l\u1EC7ch
  {
    title: intl.formatMessage({
      id: 'common.diff'
    }),
    children: [{
      title: intl.formatMessage({
        id: 'common.qty'
      }),
      dataIndex: 'quantity_diff',
      width: 100,
      // render(dom, entity, index, action, schema) {
      //   return <>{numeral(entity.quantity_diff).format('0,0')}</>;
      // },
      render: function render(dom, entity, index, action, schema) {
        var difference = entity.quantity_diff;
        var className = difference >= 0 ? 'text-green-500' : 'text-red-500';
        var sign = difference >= 0 ? '+' : '-';
        return /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
          children: [' ', /*#__PURE__*/(0,jsx_runtime.jsxs)("span", {
            className: className,
            children: ["".concat(sign).concat(numeral_default()(Math.abs(difference)).format('0,0')), " ", entity.uom_name]
          }), ' ']
        });
      },
      align: 'right'
    }, {
      title: intl.formatMessage({
        id: 'common.real_value'
      }),
      dataIndex: 'value_diff',
      width: 100,
      // render(dom, entity, index, action, schema) {
      //   return numeral(entity.value_diff).format('0,0');
      // },
      render: function render(dom, entity, index, action, schema) {
        var difference = entity.value_diff;
        var className = difference >= 0 ? 'text-green-500' : 'text-red-500';
        var sign = difference >= 0 ? '+' : '-';
        return /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
          children: [' ', /*#__PURE__*/(0,jsx_runtime.jsx)("span", {
            className: className,
            children: "".concat(sign).concat(numeral_default()(Math.abs(difference)).format('0,0'))
          }), ' ']
        });
      },
      align: 'right'
    }]
  }];
  return /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
    children: [/*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
      className: "flex justify-between w-full ml-6 mb-3 pr-12",
      children: [/*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
        className: "font-semibold text-base",
        children: [' ', intl.formatMessage({
          id: 'common.supplies'
        }).toUpperCase()]
      }), /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
        className: "font-semibold text-base",
        children: [intl.formatMessage({
          id: 'common.total_value_diff'
        }).toUpperCase(), ":", ' ', /*#__PURE__*/(0,jsx_runtime.jsxs)("span", {
          className: "text-teal-500",
          children: [numeral_default()(dataSource.totalCategoriesValueDiff).format('0,0'), " VN\\u0110"]
        })]
      })]
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(Table/* default */.Z, {
      options: false,
      bordered: true
      // headerTitle={
      //   <div className="flex justify-between w-full gap-6">
      //     <div> {intl.formatMessage({ id: 'common.supplies' }).toUpperCase()}</div>
      //     <div>{intl.formatMessage({ id: 'common.supplies' }).toUpperCase()}</div>
      //   </div>
      // }
      ,
      pagination: {
        defaultPageSize: 100
      },
      columns: columns,
      search: false,
      dataSource: categories,
      rowKey: 'category_id'
    })]
  });
};
/* harmony default export */ var CompareModal_CategoryCompareTable = (CategoryCompareTable);
// EXTERNAL MODULE: ./node_modules/antd/es/typography/index.js + 18 modules
var typography = __webpack_require__(25514);
// EXTERNAL MODULE: ./node_modules/antd/es/select/index.js + 30 modules
var es_select = __webpack_require__(83863);
;// CONCATENATED MODULE: ./src/pages/FarmingManagement/CropStatisticV2/Components/CompareModal/CompareSelectBar.tsx













var Text = typography/* default */.Z.Text;
var CompareSelectBar = function CompareSelectBar(_ref) {
  var firstCropOption = _ref.firstCropOption;
  var _useState = (0,react.useState)(false),
    _useState2 = slicedToArray_default()(_useState, 2),
    isModalOpen = _useState2[0],
    setIsModalOpen = _useState2[1];
  var _useCropCompareV2Stor = useCropCompareV2Store(),
    setDataSource = _useCropCompareV2Stor.setDataSource;
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  var _useState3 = (0,react.useState)([]),
    _useState4 = slicedToArray_default()(_useState3, 2),
    cropOptions = _useState4[0],
    setCropOptions = _useState4[1];
  var _ProForm$useForm = ProForm/* ProForm */.A.useForm(),
    _ProForm$useForm2 = slicedToArray_default()(_ProForm$useForm, 1),
    form = _ProForm$useForm2[0];
  var handleSearch = /*#__PURE__*/function () {
    var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(values) {
      var params, res;
      return regeneratorRuntime_default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _context.prev = 0;
            params = {
              crop_id_1: values.crop_id_1,
              crop_id_2: values.crop_id_2,
              start_date: values.date_range ? values.date_range[0] : '2021-01-01',
              end_date: values.date_range ? values.date_range[1] : '2026-01-01'
            };
            _context.next = 4;
            return getCropCompareV2(params);
          case 4:
            res = _context.sent;
            setDataSource(res.result);
            _context.next = 11;
            break;
          case 8:
            _context.prev = 8;
            _context.t0 = _context["catch"](0);
            console.error(_context.t0);
          case 11:
          case "end":
            return _context.stop();
        }
      }, _callee, null, [[0, 8]]);
    }));
    return function handleSearch(_x) {
      return _ref2.apply(this, arguments);
    };
  }();
  var handleCompare = /*#__PURE__*/function () {
    var _ref3 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2() {
      return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            showModal();
          case 1:
          case "end":
            return _context2.stop();
        }
      }, _callee2);
    }));
    return function handleCompare() {
      return _ref3.apply(this, arguments);
    };
  }();
  var showModal = function showModal() {
    setIsModalOpen(true);
  };
  (0,react.useEffect)(function () {
    form.setFieldsValue({
      crop_id_1: firstCropOption
    });
    var fetchData = /*#__PURE__*/function () {
      var _ref4 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee3() {
        var cropRes;
        return regeneratorRuntime_default()().wrap(function _callee3$(_context3) {
          while (1) switch (_context3.prev = _context3.next) {
            case 0:
              _context3.prev = 0;
              _context3.next = 3;
              return (0,crop/* getCropManagementInfoList */.Gz)({
                is_template: 0,
                page: 1,
                size: 1000
              });
            case 3:
              cropRes = _context3.sent;
              setCropOptions(cropRes.data.data.map(function (item) {
                return {
                  label: item.label,
                  value: item.name
                };
              }));
              _context3.next = 10;
              break;
            case 7:
              _context3.prev = 7;
              _context3.t0 = _context3["catch"](0);
              console.error(_context3.t0);
            case 10:
            case "end":
              return _context3.stop();
          }
        }, _callee3, null, [[0, 7]]);
      }));
      return function fetchData() {
        return _ref4.apply(this, arguments);
      };
    }();
    fetchData();
  }, [firstCropOption]);
  return /*#__PURE__*/(0,jsx_runtime.jsx)(jsx_runtime.Fragment, {
    children: /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
      className: "flex items-center justify-center border border-gray-200 p-3 mr-6 ml-6 mt-4 mb-4",
      children: /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        className: "w-full",
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(ProForm/* ProForm */.A, {
          layout: "vertical",
          onFinish: handleSearch,
          form: form,
          submitter: false,
          children: /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
            className: "flex flex-row items-center justify-items-start w-full gap-4 p-2"
            // style={{ borderWidth: '0.5px' }}
            ,
            children: [/*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
              name: 'crop_id_1',
              hidden: true
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
              name: 'crop_id_2',
              hidden: true
            }), ' ', /*#__PURE__*/(0,jsx_runtime.jsx)(Text, {
              strong: true,
              className: "basis-1/6 text-center",
              children: formatMessage({
                id: 'common.compare'
              }).toUpperCase()
            }), ' ', /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
              className: "basis-2/6",
              children: /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
                className: "flex flex-row gap-4 items-center justify-center",
                children: [/*#__PURE__*/(0,jsx_runtime.jsx)(Text, {
                  className: "basis-1/4 text-right",
                  children: formatMessage({
                    id: 'common.crop'
                  })
                }), /*#__PURE__*/(0,jsx_runtime.jsx)(es_select/* default */.Z, {
                  defaultValue: firstCropOption,
                  className: "basis-3/4 w-full"
                  // label={formatMessage({ id: 'common.crop' })}
                  ,
                  placeholder: formatMessage({
                    id: 'common.crop'
                  }),
                  optionFilterProp: "label",
                  options: cropOptions,
                  onChange: function onChange(value) {
                    return form.setFieldsValue({
                      crop_id_1: value
                    });
                  },
                  showSearch: true
                })]
              })
            }), /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
              className: "basis-2/6",
              children: /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
                className: "flex flex-row gap-4 items-center justify-center",
                children: [/*#__PURE__*/(0,jsx_runtime.jsx)(Text, {
                  className: "basis-1/4 text-right",
                  children: formatMessage({
                    id: 'common.with'
                  })
                }), /*#__PURE__*/(0,jsx_runtime.jsx)(es_select/* default */.Z, {
                  className: "basis-3/4 w-full"
                  // label={formatMessage({ id: 'common.crop' })}
                  ,
                  placeholder: formatMessage({
                    id: 'common.crop'
                  }),
                  optionFilterProp: "label",
                  options: cropOptions,
                  onChange: function onChange(value) {
                    return form.setFieldsValue({
                      crop_id_2: value
                    });
                  },
                  showSearch: true
                })]
              })
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
              className: "basis-1/6 text-center",
              type: "primary",
              htmlType: "submit",
              children: formatMessage({
                id: 'common.compare'
              })
            })]
          })
        })
      })
    })
  });
};
/* harmony default export */ var CompareModal_CompareSelectBar = (CompareSelectBar);
;// CONCATENATED MODULE: ./src/pages/FarmingManagement/CropStatisticV2/Components/CompareModal/ProductCompareTable.tsx








var ProductCompareTable = function ProductCompareTable() {
  var _App$useApp = app/* default */.Z.useApp(),
    message = _App$useApp.message;
  var _useCropCompareV2Stor = useCropCompareV2Store(),
    dataSource = _useCropCompareV2Stor.dataSource,
    setDataSource = _useCropCompareV2Stor.setDataSource;
  if (!dataSource.products) {
    return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {});
  }
  var products = dataSource.products;
  var crop1Label = dataSource.crop_1_label;
  var crop2Label = dataSource.crop_2_label;
  var intl = (0,_umi_production_exports.useIntl)();
  var columns = [{
    title: intl.formatMessage({
      id: 'common.production_group'
    }),
    dataIndex: 'group_label',
    width: 80,
    render: function render(text, record, index) {
      if (index > 0 && products[index - 1].group_label === text) {
        return {
          children: null,
          props: {
            rowSpan: 0
          }
        };
      }
      var rowSpan = 1;
      while (index + rowSpan < products.length && products[index + rowSpan].group_label === text) {
        rowSpan++;
      }
      return {
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(jsx_runtime.Fragment, {
          children: text
        }),
        props: {
          rowSpan: rowSpan
        }
      };
    },
    align: 'left'
  }, {
    title: intl.formatMessage({
      id: 'common.agri_product'
    }),
    dataIndex: 'product_label',
    width: 80,
    align: 'left'
  }, {
    title: crop1Label,
    children: [{
      title: intl.formatMessage({
        id: 'common.qty'
      }),
      dataIndex: 'crop1_quantity',
      width: 100,
      render: function render(dom, entity, index, action, schema) {
        return /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
          children: [numeral_default()(entity.crop1_quantity).format('0,0'), " ", entity.uom_name]
        });
      },
      align: 'right'
    }, {
      title: intl.formatMessage({
        id: 'common.real_value'
      }),
      dataIndex: 'crop1_value',
      width: 100,
      render: function render(dom, entity, index, action, schema) {
        return numeral_default()(entity.crop1_value).format('0,0');
      },
      align: 'right'
    }]
  }, {
    title: crop2Label,
    children: [{
      title: intl.formatMessage({
        id: 'common.qty'
      }),
      dataIndex: 'crop2_quantity',
      width: 100,
      render: function render(dom, entity, index, action, schema) {
        return /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
          children: [numeral_default()(entity.crop2_quantity).format('0,0'), " ", entity.uom_name]
        });
      },
      align: 'right'
    }, {
      title: intl.formatMessage({
        id: 'common.real_value'
      }),
      dataIndex: 'crop2_value',
      width: 100,
      render: function render(dom, entity, index, action, schema) {
        return numeral_default()(entity.crop2_value).format('0,0');
      },
      align: 'right'
    }]
  },
  //c\u1ED9t ch\xEAnh l\u1EC7ch
  {
    title: intl.formatMessage({
      id: 'common.diff'
    }),
    children: [{
      title: intl.formatMessage({
        id: 'common.qty'
      }),
      dataIndex: 'quantity_diff',
      width: 100,
      render: function render(dom, entity, index, action, schema) {
        var difference = entity.quantity_diff;
        var className = difference >= 0 ? 'text-green-500' : 'text-red-500';
        var sign = difference >= 0 ? '+' : '-';
        return /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
          children: [' ', /*#__PURE__*/(0,jsx_runtime.jsxs)("span", {
            className: className,
            children: ["".concat(sign).concat(numeral_default()(Math.abs(difference)).format('0,0')), " ", entity.uom_name]
          }), ' ']
        });
      },
      align: 'right'
    }, {
      title: intl.formatMessage({
        id: 'common.real_value'
      }),
      dataIndex: 'value_diff',
      width: 100,
      render: function render(dom, entity, index, action, schema) {
        var difference = entity.value_diff;
        var className = difference >= 0 ? 'text-green-500' : 'text-red-500';
        var sign = difference >= 0 ? '+' : '-';
        return /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
          children: [' ', /*#__PURE__*/(0,jsx_runtime.jsx)("span", {
            className: className,
            children: "".concat(sign).concat(numeral_default()(Math.abs(difference)).format('0,0'))
          }), ' ']
        });
      },
      align: 'right'
    }]
  }];
  return /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
    children: [/*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
      className: "flex justify-between w-full ml-6 mb-3 pr-12",
      children: [/*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
        className: "font-semibold text-base",
        children: [' ', intl.formatMessage({
          id: 'common.agri_product'
        }).toUpperCase()]
      }), /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
        className: "font-semibold text-base",
        children: [intl.formatMessage({
          id: 'common.total_value_diff'
        }).toUpperCase(), ":", ' ', /*#__PURE__*/(0,jsx_runtime.jsxs)("span", {
          className: "text-teal-500",
          children: [numeral_default()(dataSource.totalProductsValueDiff).format('0,0'), " VN\\u0110"]
        })]
      })]
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(Table/* default */.Z, {
      options: false,
      bordered: true
      // headerTitle={intl.formatMessage({ id: 'common.agri_product' }).toUpperCase()}
      ,
      columns: columns,
      search: false,
      pagination: {
        // pageSizeOptions: [10, 20, 50, 100],
        // showSizeChanger: true,
        defaultPageSize: 100
      },
      dataSource: products
      // request={(params, sorter, filter) => {
      //   try {
      //     console.log('dataSource.products', dataSource.products);
      //     return {
      //       dataSource: dataSource.products,
      //       success: true,
      //     };
      //   } catch (error: any) {
      //     message.error(\`Error when getting Crop Items Statistic: \${error.message}\`);
      //     return {
      //       success: false,
      //     };
      //   }
      // }}
      ,
      rowKey: 'category_id'
    })]
  });
};
/* harmony default export */ var CompareModal_ProductCompareTable = (ProductCompareTable);
;// CONCATENATED MODULE: ./src/pages/FarmingManagement/CropStatisticV2/Components/CompareModal/index.tsx







var CompareModal = function CompareModal(_ref) {
  var isModalOpen = _ref.isModalOpen,
    showModal = _ref.showModal,
    handleOk = _ref.handleOk,
    handleCancel = _ref.handleCancel,
    firstCropOption = _ref.firstCropOption;
  console.log('firstCropOption', firstCropOption);
  return /*#__PURE__*/(0,jsx_runtime.jsx)(jsx_runtime.Fragment, {
    children: /*#__PURE__*/(0,jsx_runtime.jsx)(modal/* default */.Z, {
      open: isModalOpen,
      width: 1200,
      onOk: handleOk,
      onCancel: handleCancel,
      footer: false,
      children: /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
        className: "flex flex-col gap-8",
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)(CompareModal_CompareSelectBar, {
          firstCropOption: firstCropOption
        }), /*#__PURE__*/(0,jsx_runtime.jsx)(CompareModal_CategoryCompareTable, {}), /*#__PURE__*/(0,jsx_runtime.jsx)(CompareModal_ProductCompareTable, {})]
      })
    })
  });
};
/* harmony default export */ var Components_CompareModal = (CompareModal);
;// CONCATENATED MODULE: ./src/pages/FarmingManagement/CropStatisticV2/Components/Filters.tsx



















var FilterBar = function FilterBar() {
  var _form$getFieldValue;
  var _useState = (0,react.useState)(false),
    _useState2 = slicedToArray_default()(_useState, 2),
    isModalOpen = _useState2[0],
    setIsModalOpen = _useState2[1];
  var _useState3 = (0,react.useState)(true),
    _useState4 = slicedToArray_default()(_useState3, 2),
    isCollapsed = _useState4[0],
    setIsCollapsed = _useState4[1];
  var _useCropDashboardV2St = useCropDashboardV2Store(),
    setDataSource = _useCropDashboardV2St.setDataSource;
  var _useCropCompareV2Stor = useCropCompareV2Store(),
    resetDataSource = _useCropCompareV2Stor.resetDataSource;
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  var _useState5 = (0,react.useState)([]),
    _useState6 = slicedToArray_default()(_useState5, 2),
    cropOptions = _useState6[0],
    setCropOptions = _useState6[1];
  var _useState7 = (0,react.useState)([]),
    _useState8 = slicedToArray_default()(_useState7, 2),
    plantOptions = _useState8[0],
    setPlantOptions = _useState8[1];
  var _ProForm$useForm = ProForm/* ProForm */.A.useForm(),
    _ProForm$useForm2 = slicedToArray_default()(_ProForm$useForm, 1),
    form = _ProForm$useForm2[0];
  var defaultValues = {
    crop_list: [],
    group_by: 'WEEK',
    date_range: ['2020-01-01', dayjs_min_default()().format('YYYY-MM-DD')]
  };
  var handleSearch = /*#__PURE__*/function () {
    var _ref = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(values) {
      var params, res;
      return regeneratorRuntime_default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            console.log('values', values);
            _context.prev = 1;
            params = {
              crop_list: values.crop_list || [],
              start_date: values.date_range ? values.date_range[0] : '',
              end_date: values.date_range ? values.date_range[1] : '',
              group_by: values.group_by || '',
              crop_status: values.crop_status || '',
              plant_id: values.plant_id || ''
            };
            _context.next = 5;
            return getCropStatisticV2(params);
          case 5:
            res = _context.sent;
            setDataSource(res.result);
            _context.next = 12;
            break;
          case 9:
            _context.prev = 9;
            _context.t0 = _context["catch"](1);
            console.error(_context.t0);
          case 12:
          case "end":
            return _context.stop();
        }
      }, _callee, null, [[1, 9]]);
    }));
    return function handleSearch(_x) {
      return _ref.apply(this, arguments);
    };
  }();
  var handleCompare = /*#__PURE__*/function () {
    var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2() {
      return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            showModal();
          case 1:
          case "end":
            return _context2.stop();
        }
      }, _callee2);
    }));
    return function handleCompare() {
      return _ref2.apply(this, arguments);
    };
  }();
  var showModal = function showModal() {
    setIsModalOpen(true);
  };
  var handleOk = function handleOk() {
    setIsModalOpen(false);
  };
  var handleCancel = function handleCancel() {
    setIsModalOpen(false);
    resetDataSource();
  };
  var toggleCollapse = function toggleCollapse() {
    setIsCollapsed(!isCollapsed);
  };
  (0,react.useEffect)(function () {
    var fetchData = /*#__PURE__*/function () {
      var _ref3 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee3() {
        var cropRes, initialCropList, plantRes;
        return regeneratorRuntime_default()().wrap(function _callee3$(_context3) {
          while (1) switch (_context3.prev = _context3.next) {
            case 0:
              _context3.prev = 0;
              _context3.next = 3;
              return (0,crop/* getCropManagementInfoList */.Gz)({
                is_template: 0,
                page: 1,
                size: 1000
              });
            case 3:
              cropRes = _context3.sent;
              console.log('cropRes', cropRes);
              initialCropList = cropRes.data.data.length > 0 ? [cropRes.data.data[0].name] : [];
              console.log('initialCropList', initialCropList);
              setCropOptions(cropRes.data.data.map(function (item) {
                return {
                  label: item.label,
                  value: item.name
                };
              }));
              defaultValues.crop_list = initialCropList;
              _context3.next = 11;
              return (0,plants/* getPlantList */.k4)({
                page: 1,
                size: 1000
              });
            case 11:
              plantRes = _context3.sent;
              console.log('plantRes', plantRes);
              setPlantOptions(plantRes.data.map(function (item) {
                return {
                  label: item.label,
                  value: item.name
                };
              }));
              form.setFieldsValue(defaultValues);
              handleSearch(defaultValues);
              _context3.next = 21;
              break;
            case 18:
              _context3.prev = 18;
              _context3.t0 = _context3["catch"](0);
              console.error(_context3.t0);
            case 21:
            case "end":
              return _context3.stop();
          }
        }, _callee3, null, [[0, 18]]);
      }));
      return function fetchData() {
        return _ref3.apply(this, arguments);
      };
    }();
    fetchData();
  }, []);
  return /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
    children: [/*#__PURE__*/(0,jsx_runtime.jsx)(card/* default */.Z, {
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(ProForm/* ProForm */.A, {
        layout: "vertical",
        onFinish: handleSearch,
        form: form,
        submitter: false,
        className: "no-margin-bottom",
        children: /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
          gutter: [16, 16],
          children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
            span: 6,
            className: "no-margin-bottom",
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
              className: "w-full",
              name: "crop_list",
              mode: "multiple",
              placeholder: formatMessage({
                id: 'common.crop'
              }),
              options: cropOptions,
              showSearch: true,
              rules: [{
                required: true
              }]
            })
          }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
            span: 3,
            className: "no-margin-bottom",
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
              className: "w-full",
              onClick: function onClick() {
                return handleCompare();
              },
              type: "primary",
              children: formatMessage({
                id: 'common.compare'
              })
            })
          }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
            span: 3,
            className: "no-margin-bottom",
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
              className: "w-full",
              name: "group_by",
              placeholder: formatMessage({
                id: 'common.time'
              }),
              options: [{
                label: 'Tu\u1EA7n',
                value: 'WEEK'
              }, {
                label: 'Th\xE1ng',
                value: 'MONTH'
              }, {
                label: 'N\u0103m',
                value: 'YEAR'
              }],
              showSearch: true,
              rules: [{
                required: true
              }]
            })
          }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
            span: 3,
            className: "no-margin-bottom",
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
              className: "w-full",
              name: "crop_status",
              placeholder: formatMessage({
                id: 'common.status'
              }),
              options: [{
                label: '\u0110ang di\u1EC5n ra',
                value: 'In progress'
              }, {
                label: 'Ho\xE0n th\xE0nh',
                value: 'Done'
              }],
              showSearch: true
            })
          }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
            span: 3,
            className: "no-margin-bottom",
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
              name: "plant_id",
              className: "w-full",
              placeholder: formatMessage({
                id: 'common.plant'
              }),
              options: plantOptions,
              showSearch: true
            })
          }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
            span: 3,
            className: "flex justify-items-end no-margin-bottom",
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
              type: "primary",
              htmlType: "submit",
              className: "w-full",
              children: formatMessage({
                id: 'common.search'
              })
            })
          }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
            span: 3,
            className: "no-margin-bottom",
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
              type: "link",
              onClick: toggleCollapse,
              style: {
                paddingLeft: 0,
                color: '#44c4a1'
              },
              children: isCollapsed ? /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
                children: [formatMessage({
                  id: 'common.expand'
                }), " ", /*#__PURE__*/(0,jsx_runtime.jsx)(DownOutlined/* default */.Z, {})]
              }) : /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
                children: [formatMessage({
                  id: 'common.collapse'
                }), " ", /*#__PURE__*/(0,jsx_runtime.jsx)(UpOutlined/* default */.Z, {})]
              })
            })
          }), !isCollapsed && /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
            span: 6,
            className: "pl-2 pr-2 no-margin-bottom",
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(DateRangePicker/* default */.Z, {
              name: "date_range",
              fieldProps: {
                format: 'YYYY-MM-DD',
                style: {
                  width: '100%'
                }
              },
              initialValue: defaultValues.date_range
            })
          })]
        })
      })
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(Components_CompareModal, {
      firstCropOption: (_form$getFieldValue = form.getFieldValue('crop_list')) === null || _form$getFieldValue === void 0 ? void 0 : _form$getFieldValue[0],
      isModalOpen: isModalOpen,
      showModal: showModal,
      handleOk: handleOk,
      handleCancel: handleCancel
    })]
  });
};
/* harmony default export */ var Filters = (FilterBar);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/toConsumableArray.js
var toConsumableArray = __webpack_require__(19632);
var toConsumableArray_default = /*#__PURE__*/__webpack_require__.n(toConsumableArray);
;// CONCATENATED MODULE: ./src/pages/FarmingManagement/CropStatisticV2/Components/OverviewChart/CostAndRevenueChart.tsx








chart/* Chart */.kL.register(chart/* CategoryScale */.uw, chart/* LinearScale */.f$, chart/* PointElement */.od, chart/* LineElement */.jn, chart/* Title */.Dx, chart/* Tooltip */.u, chart/* Legend */.De);
var LineChart = function LineChart() {
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  var _useCropDashboardV2St = useCropDashboardV2Store(),
    dataSource = _useCropDashboardV2St.dataSource,
    setDataSource = _useCropDashboardV2St.setDataSource;
  var cropData = dataSource;
  if (!cropData.details) {
    return /*#__PURE__*/(0,jsx_runtime.jsx)(spin/* default */.Z, {
      size: "large"
    });
  }
  // Process data
  var categoryDetails = cropData.details.category_details;
  var productDetails = cropData.details.product_details;
  var groupedDetails = categoryDetails.map(function (detail) {
    return {
      start_date: detail.interval_start,
      category_value: detail.value,
      category_exp_value: detail.exp_value,
      product_exp_value: 0,
      product_value: 0
    };
  }).concat(productDetails.map(function (detail) {
    return {
      start_date: detail.interval_start,
      category_value: 0,
      category_exp_value: 0,
      product_exp_value: detail.exp_value,
      product_value: detail.value
    };
  }));
  var groupBy = cropData.group_by; // Assuming groupBy is one of 'WEEK', 'MONTH', or 'YEAR'

  var data = {
    labels: toConsumableArray_default()(new Set(groupedDetails.map(function (detail) {
      return dayjs_min_default()(detail.start_date).format(groupBy === 'WEEK' ? "T".concat(dayjs_min_default()(detail.start_date).week(), "-YYYY") : groupBy === 'MONTH' ? 'MM-YYYY' : 'YYYY');
    }))),
    datasets: [{
      label: formatMessage({
        id: 'common.supplies'
      }),
      data: groupedDetails.filter(function (detail) {
        return detail.category_value > 0;
      }).map(function (detail) {
        return detail.category_value;
      }),
      borderColor: 'rgba(75, 192, 192, 1)',
      backgroundColor: 'rgba(75, 192, 192, 0.2)',
      fill: false
    }, {
      label: formatMessage({
        id: 'common.agri_product'
      }),
      data: groupedDetails.filter(function (detail) {
        return detail.product_value > 0;
      }).map(function (detail) {
        return detail.product_value;
      }),
      borderColor: 'rgba(54, 162, 235, 1)',
      backgroundColor: 'rgba(54, 162, 235, 0.2)',
      fill: false
    }]
  };
  var options = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top'
      },
      title: {
        display: true,
        text: formatMessage({
          id: 'common.cost-and-revenue-statistic'
        })
      }
    },
    scales: {
      y: {
        beginAtZero: true
        // ticks: {
        //   callback: function (value: any) {
        //     return value.toLocaleString('vi-VN');
        //   },
        // },
      },
      x: {
        beginAtZero: true
      }
    }
  };
  return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
    style: {
      height: '500px',
      width: '100%'
    },
    children: /*#__PURE__*/(0,jsx_runtime.jsx)(dist/* Line */.x1, {
      data: data,
      options: options
    })
  });
};
/* harmony default export */ var CostAndRevenueChart = (LineChart);
;// CONCATENATED MODULE: ./src/pages/FarmingManagement/CropStatisticV2/Components/OverviewChart/styles.css
// extracted by mini-css-extract-plugin

;// CONCATENATED MODULE: ./src/pages/FarmingManagement/CropStatisticV2/Components/OverviewChart/InforCards.tsx








var InfoCards = function InfoCards() {
  var _useCropDashboardV2St = useCropDashboardV2Store(),
    dataSource = _useCropDashboardV2St.dataSource;
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  var getColorClass = function getColorClass(value) {
    return value >= 0 ? 'text-green-500' : 'text-red-500';
  };
  return dataSource ? /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
    className: "w-full p-0",
    children: [/*#__PURE__*/(0,jsx_runtime.jsx)(card/* default */.Z, {
      bordered: false,
      className: "mt-4 no-shadow bg-gradient-to-b from-teal-100 to-white",
      size: "small",
      style: {
        boxShadow: 'none'
      },
      children: /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
        className: "flex flex-col w-full gap-0",
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)("div", {
          className: "w-full flex-1 text-left font-bold",
          children: formatMessage({
            id: 'common.total_crop'
          })
        }), /*#__PURE__*/(0,jsx_runtime.jsx)("span", {
          className: "flex flex-1 justify-between text-primary-green font-bold",
          children: dataSource.total_crop
        })]
      })
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(card/* default */.Z, {
      bordered: false,
      className: "mt-4 no-shadow border-2 border-green-300",
      size: "small",
      children: /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
        className: "flex flex-col w-full gap-0",
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)("div", {
          className: "text-left font-bold",
          children: formatMessage({
            id: 'common.total_used_item_value'
          })
        }), /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
          className: "flex justify-between",
          children: [/*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
            children: [numeral_default()(dataSource.total_category_value).format('0,0'), " VN\\u0110"]
          }), /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
            className: getColorClass((dataSource.total_category_value - dataSource.total_category_exp_value) * 100 / dataSource.total_category_exp_value),
            children: [numeral_default()((dataSource.total_category_value - dataSource.total_category_exp_value) * 100 / dataSource.total_category_exp_value).format('0,0.00'), ' ', "%"]
          })]
        })]
      })
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(card/* default */.Z, {
      bordered: false,
      className: "mt-4 no-shadow border-2 border-orange-300",
      size: "small",
      children: /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
        className: "flex flex-col w-full gap-0",
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)("div", {
          className: "text-left font-bold",
          children: formatMessage({
            id: 'common.total_used_product_value'
          })
        }), /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
          className: "flex justify-between",
          children: [/*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
            children: [numeral_default()(dataSource.total_product_value).format('0,0'), " VN\\u0110"]
          }), /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
            className: getColorClass((dataSource.total_product_value - dataSource.total_product_exp_value) * 100 / dataSource.total_product_exp_value),
            children: [numeral_default()((dataSource.total_product_value - dataSource.total_product_exp_value) * 100 / dataSource.total_product_exp_value).format('0,0.00'), ' ', "%"]
          })]
        })]
      })
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(card/* default */.Z, {
      bordered: false,
      style: {
        padding: 0,
        boxShadow: 'none'
      },
      size: "small",
      children: /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
        className: "flex flex-row w-full items-center",
        children: [/*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
          className: "flex-1 text-right font-bold text-base",
          children: [formatMessage({
            id: 'common.profit'
          }), " :"]
        }), /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
          className: "flex-2 text-right text-primary-green text-base font-bold ml-2",
          children: [numeral_default()(dataSource.total_product_value - dataSource.total_category_value).format('0,0'), ' ', "VN\\u0110"]
        })]
      })
    })]
  }) : /*#__PURE__*/(0,jsx_runtime.jsx)(jsx_runtime.Fragment, {});
};
/* harmony default export */ var InforCards = (InfoCards);
;// CONCATENATED MODULE: ./src/pages/FarmingManagement/CropStatisticV2/Components/OverviewChart/index.tsx
// /src/OverviewCard.js







var OverviewCard = function OverviewCard() {
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  return /*#__PURE__*/(0,jsx_runtime.jsx)(card/* default */.Z
  // className="min-h-screen text-left"
  , {
    title: formatMessage({
      id: 'common.overview'
    }).toUpperCase(),
    style: {
      alignItems: 'start',
      textAlign: 'left'
    },
    children: /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
      gutter: [16, 16],
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 16,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
          className: "w-full h-96 flex justify-center items-center",
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(CostAndRevenueChart, {})
        })
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 8,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(InforCards, {})
      })]
    })
  });
};
/* harmony default export */ var OverviewChart = (OverviewCard);
;// CONCATENATED MODULE: ./src/pages/FarmingManagement/CropStatisticV2/Components/ProductStatistic/ProductStatisticChart.tsx
// File path: src/MixedChart.js






chart/* Chart */.kL.register(chart/* CategoryScale */.uw, chart/* LinearScale */.f$, chart/* BarElement */.ZL, chart/* PointElement */.od, chart/* LineElement */.jn, chart/* Tooltip */.u, chart/* Legend */.De);
var MixedChart = function MixedChart() {
  var _useCropDashboardV2St = useCropDashboardV2Store(),
    dataSource = _useCropDashboardV2St.dataSource,
    setDataSource = _useCropDashboardV2St.setDataSource;
  var products = dataSource.products;
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  // Prepare data for the chart
  var labels = products.map(function (product) {
    return product.product_label;
  });
  var expectedValues = products.map(function (product) {
    return product.total_exp_value;
  });
  var actualValues = products.map(function (product) {
    return product.total_value;
  });
  var data = {
    labels: labels,
    datasets: [{
      type: 'bar',
      label: formatMessage({
        id: 'common.exp_value'
      }),
      data: expectedValues,
      backgroundColor: '#304463',
      // xanh l\xE1
      borderColor: '#304463',
      yAxisID: 'y',
      order: 2
    }, {
      type: 'line',
      label: formatMessage({
        id: 'common.real_value'
      }),
      data: actualValues,
      backgroundColor: '#ecc94b',
      // v\xE0ng
      borderColor: '#ecc94b',
      yAxisID: 'y',
      fill: false,
      order: 1
    }]
  };
  var options = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top'
      },
      tooltip: {
        mode: 'index',
        intersect: false
      }
    },
    scales: {
      y: {
        beginAtZero: true
      }
    }
  };
  return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
    style: {
      height: '500px',
      width: '100%'
    },
    children: /*#__PURE__*/(0,jsx_runtime.jsx)(dist/* Bar */.$Q, {
      data: data,
      options: options
    })
  });
};
/* harmony default export */ var ProductStatisticChart = (MixedChart);
;// CONCATENATED MODULE: ./src/pages/FarmingManagement/CropStatisticV2/Components/ProductStatistic/ProductStatisticTable.tsx










var ProductStatisticTable = function ProductStatisticTable() {
  var _App$useApp = app/* default */.Z.useApp(),
    message = _App$useApp.message;
  var _useCropDashboardV2St = useCropDashboardV2Store(),
    dataSource = _useCropDashboardV2St.dataSource,
    setDataSource = _useCropDashboardV2St.setDataSource;
  var products = dataSource.products;
  if (!products || products.length === 0) {
    //return no data message with center align horizontally and vertically
    return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
      children: "No data"
    });
  }
  var intl = (0,_umi_production_exports.useIntl)();
  var columns = [{
    title: intl.formatMessage({
      id: 'common.production_group'
    }),
    dataIndex: 'group_label',
    width: 80
    // render: (text, record, index) => {
    //   if (index > 0 && products[index - 1].group_label === text) {
    //     return {
    //       children: null,
    //       props: {
    //         rowSpan: 0,
    //       },
    //     };
    //   }

    //   let rowSpan = 1;
    //   while (
    //     index + rowSpan < products.length &&
    //     products[index + rowSpan].group_label === text
    //   ) {
    //     rowSpan++;
    //   }

    //   return {
    //     children: <>{text}</>,
    //     props: {
    //       rowSpan,
    //     },
    //   };
    // },
  }, {
    title: intl.formatMessage({
      id: 'common.agri_product'
    }),
    dataIndex: 'product_label',
    width: 80
  }, {
    // title: intl.formatMessage({ id: 'seasonalTab.totalExpected' }),
    title: intl.formatMessage({
      id: 'common.exp_value'
    }),
    dataIndex: 'total_exp_value',
    width: 100,
    render: function render(dom, entity, index, action, schema) {
      return numeral_default()(entity.total_exp_value).format('0,0');
    }
  }, {
    // title: intl.formatMessage({ id: 'seasonalTab.totalUsed' }),
    title: intl.formatMessage({
      id: 'common.real_value'
    }),
    dataIndex: 'total_value',
    width: 100,
    render: function render(dom, entity, index, action, schema) {
      return numeral_default()(entity.total_value).format('0,0');
    }
  }, {
    title: intl.formatMessage({
      id: 'common.diff'
    }),
    dataIndex: 'difference',
    width: 100,
    align: 'right',
    render: function render(_, entity) {
      var difference = entity.total_value - entity.total_exp_value;
      var className = difference >= 0 ? 'text-green-500' : 'text-red-500';
      var sign = difference >= 0 ? '+' : '-';
      return /*#__PURE__*/(0,jsx_runtime.jsx)("span", {
        className: className,
        children: "".concat(sign).concat(numeral_default()(Math.abs(difference)).format('0,0'))
      });
    }
  }

  //valuation_rate
  // {
  //   title: intl.formatMessage({ id: 'common.purchase-price' }),
  //   dataIndex: 'valuation_rate',
  //   width: 80,
  //   render(dom, entity, index, action, schema) {
  //     const valuation_rate = entity.valuation_rate ? entity.valuation_rate : 0;
  //     return numeral(entity.valuation_rate).format('0,000');
  //   },
  // },

  // {
  //   title: intl.formatMessage({ id: 'seasonalTab.remaining' }),
  //   dataIndex: 'remain_quantity',
  // },
  ];
  return /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
    className: "no-padding-left",
    children: [' ', /*#__PURE__*/(0,jsx_runtime.jsx)(Table/* default */.Z, {
      options: false,
      bordered: true
      // headerTitle={intl.formatMessage({ id: 'seasonalTab.suppliesStatistics' })}
      ,
      columns: columns,
      search: false,
      scroll: {
        x: 'max-content'
      },
      pagination: {
        pageSizeOptions: [10, 20, 50, 100],
        showSizeChanger: true,
        defaultPageSize: 10
      }
      // dataSource={products}
      ,
      request: ( /*#__PURE__*/function () {
        var _ref = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(params, sorter, filter) {
          var current, pageSize, startIndex, data;
          return regeneratorRuntime_default()().wrap(function _callee$(_context) {
            while (1) switch (_context.prev = _context.next) {
              case 0:
                _context.prev = 0;
                console.log('params', params);
                current = params.current, pageSize = params.pageSize;
                startIndex = (current - 1) * pageSize;
                data = products.slice(startIndex, startIndex + pageSize);
                return _context.abrupt("return", {
                  data: data,
                  success: true,
                  total: products.length
                });
              case 8:
                _context.prev = 8;
                _context.t0 = _context["catch"](0);
                message.error("Error when getting Crop products Statistic: ".concat(_context.t0.message));
                return _context.abrupt("return", Promise.resolve({
                  success: false
                }));
              case 12:
              case "end":
                return _context.stop();
            }
          }, _callee, null, [[0, 8]]);
        }));
        return function (_x, _x2, _x3) {
          return _ref.apply(this, arguments);
        };
      }()),
      rowKey: 'product_id'
    })]
  });
};
/* harmony default export */ var ProductStatistic_ProductStatisticTable = (ProductStatisticTable);
;// CONCATENATED MODULE: ./src/pages/FarmingManagement/CropStatisticV2/Components/ProductStatistic/index.tsx








var ProductStatisticCard = function ProductStatisticCard(props) {
  var _useCropDashboardV2St = useCropDashboardV2Store(),
    dataSource = _useCropDashboardV2St.dataSource,
    setDataSource = _useCropDashboardV2St.setDataSource;
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  return /*#__PURE__*/(0,jsx_runtime.jsx)(card/* default */.Z, {
    title: /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
      className: "flex justify-between items-center",
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        className: "flex-1 text-left",
        children: formatMessage({
          id: 'common.agri_product_profit_statistic'
        }).toUpperCase()
      }), /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
        className: "flex-1 text-right",
        children: [/*#__PURE__*/(0,jsx_runtime.jsxs)("span", {
          className: "text-base font-bold",
          children: [formatMessage({
            id: 'common.basic_amount'
          }), ":", ' ']
        }), /*#__PURE__*/(0,jsx_runtime.jsxs)("span", {
          className: "text-primary-green text-base font-bold",
          children: [numeral_default()(dataSource.total_product_value).format('0,0'), " VN\\u0110"]
        })]
      })]
    }),
    children: dataSource.details ? /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
      gutter: [16, 16],
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 10,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(ProductStatistic_ProductStatisticTable, {})
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 14,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(ProductStatisticChart, {})
      })]
    }) : /*#__PURE__*/(0,jsx_runtime.jsx)(spin/* default */.Z, {
      size: "large"
    })
  });
};
/* harmony default export */ var ProductStatistic = (ProductStatisticCard);
;// CONCATENATED MODULE: ./src/pages/FarmingManagement/CropStatisticV2/index.tsx











var Index = function Index(_ref) {
  var children = _ref.children;
  var _useModel = (0,_umi_production_exports.useModel)('@@initialState'),
    setInitialState = _useModel.setInitialState,
    initialState = _useModel.initialState;
  (0,react.useEffect)(function () {
    setInitialState(objectSpread2_default()(objectSpread2_default()({}, initialState), {}, {
      collapsed: true
    }));
  }, []);
  return /*#__PURE__*/(0,jsx_runtime.jsx)(PageContainer/* PageContainer */._z, {
    affixProps: {
      offsetTop: 20
    },
    fixedHeader: true,
    content: /*#__PURE__*/(0,jsx_runtime.jsx)(Filters, {}),
    children: /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
      gutter: [16, 16],
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 24,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(OverviewChart, {})
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 24,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(CategoryStatistic, {})
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 24,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(ProductStatistic, {})
      })]
    })
  });
};
/* harmony default export */ var CropStatisticV2 = (Index);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///65402
`)},52662:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   EH: function() { return /* binding */ getCrop; },
/* harmony export */   Gz: function() { return /* binding */ getCropManagementInfoList; },
/* harmony export */   Kw: function() { return /* binding */ getCropParticipantsTaskList; },
/* harmony export */   NQ: function() { return /* binding */ getCropWorksheetStatistic; },
/* harmony export */   _R: function() { return /* binding */ getCropItemStatistic; },
/* harmony export */   dK: function() { return /* binding */ getCropNote; },
/* harmony export */   e4: function() { return /* binding */ getCropByTask; },
/* harmony export */   hD: function() { return /* binding */ getCropParticipantsStatistic; },
/* harmony export */   qQ: function() { return /* binding */ getCropProductionStatisticDetailTask; },
/* harmony export */   su: function() { return /* binding */ getCropProductionQuantityStatistic; },
/* harmony export */   vx: function() { return /* binding */ getCropItemStatisticDetailTask; },
/* harmony export */   ym: function() { return /* binding */ getCropPest; }
/* harmony export */ });
/* unused harmony export cropList */
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(97857);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(7837);
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(467);





var CRUD_PATH = {
  CREATE: 'crop',
  READ: 'crop',
  UPDATE: 'crop',
  DELETE: 'crop'
};
var getCropManagementInfoList = /*#__PURE__*/function () {
  var _ref = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/cropManage/crop-management-info'), {
            method: 'GET',
            params: params
          });
        case 2:
          res = _context.sent;
          return _context.abrupt("return", {
            data: res.result
          });
        case 4:
        case "end":
          return _context.stop();
      }
    }, _callee);
  }));
  return function getCropManagementInfoList(_x) {
    return _ref.apply(this, arguments);
  };
}();
var getCropByTask = /*#__PURE__*/function () {
  var _ref2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee2(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          _context2.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/cropManage/crop-by-task'), {
            method: 'GET',
            params: params
          });
        case 2:
          res = _context2.sent;
          return _context2.abrupt("return", {
            data: res.result
          });
        case 4:
        case "end":
          return _context2.stop();
      }
    }, _callee2);
  }));
  return function getCropByTask(_x2) {
    return _ref2.apply(this, arguments);
  };
}();
function cropList(_x3) {
  return _cropList.apply(this, arguments);
}
function _cropList() {
  _cropList = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee13(_ref3) {
    var _ref3$page, page, _ref3$size, size, _ref3$fields, fields, _ref3$filters, filters, _ref3$or_filters, or_filters, _ref3$order_by, order_by, _ref3$group_by, group_by, params, result;
    return _regeneratorRuntime().wrap(function _callee13$(_context13) {
      while (1) switch (_context13.prev = _context13.next) {
        case 0:
          _ref3$page = _ref3.page, page = _ref3$page === void 0 ? 0 : _ref3$page, _ref3$size = _ref3.size, size = _ref3$size === void 0 ? 20 : _ref3$size, _ref3$fields = _ref3.fields, fields = _ref3$fields === void 0 ? ['*'] : _ref3$fields, _ref3$filters = _ref3.filters, filters = _ref3$filters === void 0 ? [] : _ref3$filters, _ref3$or_filters = _ref3.or_filters, or_filters = _ref3$or_filters === void 0 ? [] : _ref3$or_filters, _ref3$order_by = _ref3.order_by, order_by = _ref3$order_by === void 0 ? '' : _ref3$order_by, _ref3$group_by = _ref3.group_by, group_by = _ref3$group_by === void 0 ? '' : _ref3$group_by;
          _context13.prev = 1;
          params = {
            page: page,
            size: size,
            fields: JSON.stringify(fields),
            filters: JSON.stringify(filters),
            or_filters: JSON.stringify(or_filters)
            // order_by,
            // group_by
          };
          _context13.next = 5;
          return request(generateAPIPath("api/v2/cropManage/".concat(CRUD_PATH.READ)), {
            method: 'GET',
            params: params,
            queryParams: params
          });
        case 5:
          result = _context13.sent;
          return _context13.abrupt("return", result.result);
        case 9:
          _context13.prev = 9;
          _context13.t0 = _context13["catch"](1);
          console.log(_context13.t0);
          throw _context13.t0;
        case 13:
        case "end":
          return _context13.stop();
      }
    }, _callee13, null, [[1, 9]]);
  }));
  return _cropList.apply(this, arguments);
}
var getCropNote = /*#__PURE__*/function () {
  var _ref4 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee3(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee3$(_context3) {
      while (1) switch (_context3.prev = _context3.next) {
        case 0:
          _context3.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)("api/v2/cropManage/note"), {
            method: 'GET',
            params: params
          });
        case 2:
          res = _context3.sent;
          return _context3.abrupt("return", {
            data: res.result
          });
        case 4:
        case "end":
          return _context3.stop();
      }
    }, _callee3);
  }));
  return function getCropNote(_x4) {
    return _ref4.apply(this, arguments);
  };
}();
var getCropPest = /*#__PURE__*/function () {
  var _ref5 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee4(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee4$(_context4) {
      while (1) switch (_context4.prev = _context4.next) {
        case 0:
          _context4.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)("api/v2/cropManage/pest"), {
            method: 'GET',
            params: params
          });
        case 2:
          res = _context4.sent;
          console.log(' res.result', res.result);
          return _context4.abrupt("return", {
            data: res.result
          });
        case 5:
        case "end":
          return _context4.stop();
      }
    }, _callee4);
  }));
  return function getCropPest(_x5) {
    return _ref5.apply(this, arguments);
  };
}();
var getCrop = /*#__PURE__*/function () {
  var _ref6 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee5(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee5$(_context5) {
      while (1) switch (_context5.prev = _context5.next) {
        case 0:
          _context5.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/cropManage/crop'), {
            method: 'GET',
            params: params
          });
        case 2:
          res = _context5.sent;
          return _context5.abrupt("return", {
            data: res.result
          });
        case 4:
        case "end":
          return _context5.stop();
      }
    }, _callee5);
  }));
  return function getCrop(_x6) {
    return _ref6.apply(this, arguments);
  };
}();
var getCropItemStatistic = /*#__PURE__*/function () {
  var _ref7 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee6(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee6$(_context6) {
      while (1) switch (_context6.prev = _context6.next) {
        case 0:
          _context6.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/cropManage/statisticItem'), {
            method: 'GET',
            params: params
          });
        case 2:
          res = _context6.sent;
          return _context6.abrupt("return", {
            data: res.result
          });
        case 4:
        case "end":
          return _context6.stop();
      }
    }, _callee6);
  }));
  return function getCropItemStatistic(_x7) {
    return _ref7.apply(this, arguments);
  };
}();
var getCropItemStatisticDetailTask = /*#__PURE__*/function () {
  var _ref8 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee7(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee7$(_context7) {
      while (1) switch (_context7.prev = _context7.next) {
        case 0:
          _context7.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/cropManage/statisticItem/detail-in-crop'), {
            method: 'GET',
            params: params
          });
        case 2:
          res = _context7.sent;
          return _context7.abrupt("return", {
            data: res.result.data,
            pagination: res.result.pagination
          });
        case 4:
        case "end":
          return _context7.stop();
      }
    }, _callee7);
  }));
  return function getCropItemStatisticDetailTask(_x8) {
    return _ref8.apply(this, arguments);
  };
}();
var getCropProductionStatisticDetailTask = /*#__PURE__*/function () {
  var _ref9 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee8(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee8$(_context8) {
      while (1) switch (_context8.prev = _context8.next) {
        case 0:
          _context8.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/cropManage/statisticProductQuantity/detail-in-crop'), {
            method: 'GET',
            params: params
          });
        case 2:
          res = _context8.sent;
          return _context8.abrupt("return", {
            data: res.result.data,
            pagination: res.result.pagination
          });
        case 4:
        case "end":
          return _context8.stop();
      }
    }, _callee8);
  }));
  return function getCropProductionStatisticDetailTask(_x9) {
    return _ref9.apply(this, arguments);
  };
}();
var getCropParticipantsStatistic = /*#__PURE__*/function () {
  var _ref10 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee9(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee9$(_context9) {
      while (1) switch (_context9.prev = _context9.next) {
        case 0:
          _context9.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/cropManage/statisticParticipant'), {
            method: 'GET',
            params: params
          });
        case 2:
          res = _context9.sent;
          return _context9.abrupt("return", {
            data: res.result.data,
            pagination: res.result.pagination
          });
        case 4:
        case "end":
          return _context9.stop();
      }
    }, _callee9);
  }));
  return function getCropParticipantsStatistic(_x10) {
    return _ref10.apply(this, arguments);
  };
}();
var getCropParticipantsTaskList = /*#__PURE__*/function () {
  var _ref11 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee10(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee10$(_context10) {
      while (1) switch (_context10.prev = _context10.next) {
        case 0:
          _context10.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/cropManage/statisticParticipant/detail-task-list'), {
            method: 'GET',
            params: params
          });
        case 2:
          res = _context10.sent;
          return _context10.abrupt("return", {
            data: res.result.data,
            pagination: res.result.pagination
          });
        case 4:
        case "end":
          return _context10.stop();
      }
    }, _callee10);
  }));
  return function getCropParticipantsTaskList(_x11) {
    return _ref11.apply(this, arguments);
  };
}();
var getCropProductionQuantityStatistic = /*#__PURE__*/function () {
  var _ref12 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee11(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee11$(_context11) {
      while (1) switch (_context11.prev = _context11.next) {
        case 0:
          _context11.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/cropManage/statisticProductQuantity'), {
            method: 'GET',
            params: params
          });
        case 2:
          res = _context11.sent;
          return _context11.abrupt("return", {
            data: res.result
          });
        case 4:
        case "end":
          return _context11.stop();
      }
    }, _callee11);
  }));
  return function getCropProductionQuantityStatistic(_x12) {
    return _ref12.apply(this, arguments);
  };
}();
var getCropWorksheetStatistic = /*#__PURE__*/function () {
  var _ref13 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee12(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee12$(_context12) {
      while (1) switch (_context12.prev = _context12.next) {
        case 0:
          _context12.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/cropManage/statisticWorksheet'), {
            method: 'GET',
            params: params
          });
        case 2:
          res = _context12.sent;
          return _context12.abrupt("return", {
            data: res.result.map(function (stat) {
              return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default()({}, stat), {}, {
                type: stat.type.toLowerCase() === 'hour' ? 'Gi\u1EDD' : 'C\xF4ng'
              });
            })
          });
        case 4:
        case "end":
          return _context12.stop();
      }
    }, _callee12);
  }));
  return function getCropWorksheetStatistic(_x13) {
    return _ref13.apply(this, arguments);
  };
}();//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///52662
`)}}]);
