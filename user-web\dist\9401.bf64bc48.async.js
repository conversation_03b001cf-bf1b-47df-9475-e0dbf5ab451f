"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[9401],{29401:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return /* binding */ Inventory_StockEntryHistory; }
});

// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/regeneratorRuntime.js
var regeneratorRuntime = __webpack_require__(15009);
var regeneratorRuntime_default = /*#__PURE__*/__webpack_require__.n(regeneratorRuntime);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/objectSpread2.js
var objectSpread2 = __webpack_require__(97857);
var objectSpread2_default = /*#__PURE__*/__webpack_require__.n(objectSpread2);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(99289);
var asyncToGenerator_default = /*#__PURE__*/__webpack_require__.n(asyncToGenerator);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/slicedToArray.js
var slicedToArray = __webpack_require__(5574);
var slicedToArray_default = /*#__PURE__*/__webpack_require__.n(slicedToArray);
// EXTERNAL MODULE: ./src/common/contanst/constanst.ts
var constanst = __webpack_require__(86604);
// EXTERNAL MODULE: ./src/services/stock/stockEntry.ts
var stock_stockEntry = __webpack_require__(1631);
// EXTERNAL MODULE: ./src/utils/array.ts
var array = __webpack_require__(19073);
// EXTERNAL MODULE: ./src/utils/date.ts
var date = __webpack_require__(28382);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/EyeOutlined.js
var EyeOutlined = __webpack_require__(55287);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/Select/index.js
var Select = __webpack_require__(64317);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-table/es/Table.js + 76 modules
var Table = __webpack_require__(4894);
// EXTERNAL MODULE: ./src/.umi-production/exports.ts + 34 modules
var _umi_production_exports = __webpack_require__(7837);
// EXTERNAL MODULE: ./node_modules/antd/es/app/index.js + 1 modules
var app = __webpack_require__(31418);
// EXTERNAL MODULE: ./node_modules/antd/es/button/index.js + 9 modules
var es_button = __webpack_require__(14726);
// EXTERNAL MODULE: ./node_modules/antd/es/tag/index.js + 5 modules
var tag = __webpack_require__(66309);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./src/pages/WarehouseManagementV3/hooks/useDateRangeStore.ts
var useDateRangeStore = __webpack_require__(61791);
// EXTERNAL MODULE: ./src/pages/WarehouseManagementV3/hooks/useWarehouseStore.ts
var useWarehouseStore = __webpack_require__(22504);
// EXTERNAL MODULE: ./src/pages/WarehouseManagementV3/_utils.ts
var _utils = __webpack_require__(25770);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/defineProperty.js
var defineProperty = __webpack_require__(9783);
var defineProperty_default = /*#__PURE__*/__webpack_require__.n(defineProperty);
// EXTERNAL MODULE: ./src/components/ConfirmMdal/index.tsx
var ConfirmMdal = __webpack_require__(60461);
// EXTERNAL MODULE: ./node_modules/react/jsx-runtime.js
var jsx_runtime = __webpack_require__(85893);
;// CONCATENATED MODULE: ./src/components/DocStatusTag/index.tsx




var DocStatusTag = function DocStatusTag(_ref) {
  var status = _ref.status;
  var tagColor = constanst/* DOC_STATUS_COLOR */.IZ[status];
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  var docStatusLabel = formatMessage({
    id: "common.doc_status.".concat(status)
  });
  return /*#__PURE__*/(0,jsx_runtime.jsx)(tag/* default */.Z, {
    color: tagColor,
    children: docStatusLabel
  });
};
/* harmony default export */ var components_DocStatusTag = (DocStatusTag);
// EXTERNAL MODULE: ./src/services/utils.ts
var utils = __webpack_require__(467);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/PrinterOutlined.js + 1 modules
var PrinterOutlined = __webpack_require__(30019);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/layouts/ModalForm/index.js
var ModalForm = __webpack_require__(37476);
// EXTERNAL MODULE: ./node_modules/antd/es/form/index.js + 18 modules
var es_form = __webpack_require__(98138);
// EXTERNAL MODULE: ./node_modules/antd/es/spin/index.js + 2 modules
var spin = __webpack_require__(75081);
// EXTERNAL MODULE: ./src/pages/WarehouseManagementV3/Inventory/components/StockActionButton/index.tsx
var StockActionButton = __webpack_require__(858);
// EXTERNAL MODULE: ./src/pages/WarehouseManagementV3/Inventory/components/StockActionButton/voucherActions.ts
var voucherActions = __webpack_require__(24234);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/DateTimePicker/index.js
var DateTimePicker = __webpack_require__(22452);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/TextArea/index.js
var TextArea = __webpack_require__(90672);
// EXTERNAL MODULE: ./node_modules/antd/es/row/index.js
var row = __webpack_require__(71230);
// EXTERNAL MODULE: ./node_modules/antd/es/col/index.js
var col = __webpack_require__(15746);
// EXTERNAL MODULE: ./node_modules/dayjs/dayjs.min.js
var dayjs_min = __webpack_require__(27484);
var dayjs_min_default = /*#__PURE__*/__webpack_require__.n(dayjs_min);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/createForOfIteratorHelper.js
var createForOfIteratorHelper = __webpack_require__(64599);
var createForOfIteratorHelper_default = /*#__PURE__*/__webpack_require__.n(createForOfIteratorHelper);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/toConsumableArray.js
var toConsumableArray = __webpack_require__(19632);
var toConsumableArray_default = /*#__PURE__*/__webpack_require__.n(toConsumableArray);
// EXTERNAL MODULE: ./node_modules/antd/es/message/index.js
var message = __webpack_require__(45360);
// EXTERNAL MODULE: ./src/services/InventoryManagementV3/inventory.ts
var InventoryManagementV3_inventory = __webpack_require__(17322);
// EXTERNAL MODULE: ./src/services/customerUser.ts
var customerUser = __webpack_require__(40063);
// EXTERNAL MODULE: ./src/services/stock/warehouse.ts
var stock_warehouse = __webpack_require__(18327);
// EXTERNAL MODULE: ./src/services/InventoryManagementV3/supplier.ts
var supplier = __webpack_require__(26222);
;// CONCATENATED MODULE: ./src/pages/WarehouseManagementV3/Inventory/StockEntryHistory/components/StockEntryDetailEnhanced/utils/helpers.ts



// utils/helpers.ts






var fetchWarehouseList = /*#__PURE__*/function () {
  var _ref = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
    var warehouse;
    return regeneratorRuntime_default()().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.next = 2;
          return (0,stock_warehouse/* getWarehouseList */.Aq)();
        case 2:
          warehouse = _context.sent;
          return _context.abrupt("return", warehouse.data.map(function (storage) {
            return {
              label: storage.label,
              value: storage.name
            };
          }));
        case 4:
        case "end":
          return _context.stop();
      }
    }, _callee);
  }));
  return function fetchWarehouseList() {
    return _ref.apply(this, arguments);
  };
}();
var fetchCustomerUserList = /*#__PURE__*/function () {
  var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2() {
    var listUser;
    return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          _context2.next = 2;
          return (0,customerUser/* getCustomerUserList */.J9)();
        case 2:
          listUser = _context2.sent;
          return _context2.abrupt("return", listUser.data.map(function (item) {
            return {
              label: "".concat(item.first_name, " ").concat(item.last_name),
              value: item.name
            };
          }));
        case 4:
        case "end":
          return _context2.stop();
      }
    }, _callee2);
  }));
  return function fetchCustomerUserList() {
    return _ref2.apply(this, arguments);
  };
}();
var fetchSupplierV3 = /*#__PURE__*/(/* unused pure expression or super */ null && (function () {
  var _ref3 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee3() {
    var supplier;
    return _regeneratorRuntime().wrap(function _callee3$(_context3) {
      while (1) switch (_context3.prev = _context3.next) {
        case 0:
          _context3.next = 2;
          return getSupplierV3();
        case 2:
          supplier = _context3.sent;
          return _context3.abrupt("return", supplier);
        case 4:
        case "end":
          return _context3.stop();
      }
    }, _callee3);
  }));
  return function fetchSupplierV3() {
    return _ref3.apply(this, arguments);
  };
}()));

/**\r
 * Gets posting date and time from the form date value\r
 */
var getPostingDateTime = function getPostingDateTime(formDate) {
  var date = moment(formDate, DEFAULT_DATE_AND_HH_MM_FORMAT);
  var posting_date = date.format('YYYY-MM-DD');

  // Get hours and minutes from the date
  var hoursAndMinutes = date.format('HH:mm');

  // Get current seconds
  var currentSeconds = moment().format('ss');

  // Combine hours, minutes and seconds for posting_time
  var posting_time = "".concat(hoursAndMinutes, ":").concat(currentSeconds);
  return {
    posting_date: posting_date,
    posting_time: posting_time
  };
};

/**\r
 * Creates initial export voucher object\r
 */
var createImportVoucher = function createImportVoucher(values, selectedItems, posting_date, posting_time) {
  return {
    __isLocal: 1,
    __unsaved: 1,
    posting_date: posting_date,
    posting_time: posting_time,
    set_posting_time: 1,
    company: 'VIIS',
    description: values.description,
    iot_customer_user: values.employee,
    file_path: values.file_path,
    doctype: 'Delivery Note',
    customer: values.customer,
    set_warehouse: values.warehouse,
    items: selectedItems.map(function (item) {
      return {
        doctype: 'Delivery Note Item',
        name: 'new-delivery-note-item' + generateRandomString(),
        warehouse: values.warehouse,
        conversion_factor: item.conversion_factor,
        item_code: item.item_code,
        qty: item.qty,
        rate: item.rate,
        uom: item.uom
      };
    }),
    add_taxes: values.add_taxes,
    other_charges: values.other_charges,
    paid_amount: values.paid_amount,
    discount: values.discount,
    have_transaction: true
  };
};

/**\r
 * Creates submitting export voucher object from saved response\r
 */
var createSubmittingImportVoucher = function createSubmittingImportVoucher(saveRes) {
  return {
    set_posting_time: 1,
    name: saveRes.name,
    owner: saveRes.owner,
    creation: saveRes.creation,
    modified: saveRes.modified,
    modified_by: saveRes.modified_by,
    naming_series: saveRes.naming_series,
    posting_date: saveRes.posting_date,
    posting_time: saveRes.posting_time,
    status: 'Draft',
    company: saveRes.company,
    description: saveRes.description,
    file_path: saveRes.file_path,
    have_transaction: saveRes.have_transaction,
    add_taxes: saveRes.add_taxes,
    other_charges: saveRes.other_charges,
    paid_amount: saveRes.paid_amount,
    discount: saveRes.discount,
    doctype: 'Delivery Note',
    customer: saveRes.customer,
    customer_name: saveRes.customer_name,
    selling_price_list: saveRes.selling_price_list,
    price_list_currency: saveRes.price_list_currency,
    plc_conversion_rate: saveRes.plc_conversion_rate,
    set_warehouse: saveRes.set_warehouse,
    items: saveRes.items.map(function (item) {
      return {
        name: item.name,
        item_code: item.item_code,
        qty: item.qty,
        conversion_factor: item.conversion_factor,
        rate: item.rate,
        warehouse: item.warehouse,
        s_warehouse: item.s_warehouse,
        doctype: item.doctype,
        uom: item.uom
      };
    })
  };
};

// Helper function to generate random string
var generateRandomString = function generateRandomString() {
  return Math.random().toString(36).substring(7);
};
function groupByItemGroupWithItemGroupData(stockItems, stockItemGroups) {
  var groupedItems = {};
  stockItems.forEach(function (item) {
    var itemGroup = item.item_group;
    if (!groupedItems[itemGroup]) {
      groupedItems[itemGroup] = {
        items: [],
        itemGroup: {}
      };
    }
    groupedItems[itemGroup].items.push(item);
  });

  // Now add item group data
  var _iterator = createForOfIteratorHelper_default()(stockItemGroups),
    _step;
  try {
    for (_iterator.s(); !(_step = _iterator.n()).done;) {
      var itemGroup = _step.value;
      var itemGroupName = itemGroup.item_group_name;
      if (groupedItems[itemGroupName]) {
        groupedItems[itemGroupName].itemGroup = itemGroup;
      }
    }
  } catch (err) {
    _iterator.e(err);
  } finally {
    _iterator.f();
  }
  return groupedItems;
}
// EXTERNAL MODULE: ./src/services/stock/item.ts
var item = __webpack_require__(89436);
// EXTERNAL MODULE: ./node_modules/zustand/esm/index.mjs + 1 modules
var esm = __webpack_require__(64529);
;// CONCATENATED MODULE: ./src/pages/WarehouseManagementV3/Inventory/StockEntryHistory/components/StockEntryDetailEnhanced/stores/useStockEntryStore.tsx









var initialState = {
  // Form state
  form: null,
  submitting: false,
  // Items state
  selectedItems: [],
  treeData: [],
  items: [],
  // Selection state
  selectedSourceWarehouse: '',
  selectedTargetWarehouse: '',
  // Data fetching state
  isItemTreeDataLoaded: false,
  isInitializing: false,
  isSaved: false,
  savedVoucherData: null,
  docstatus: 0 // Kh\u1EDFi t\u1EA1o m\u1EB7c \u0111\u1ECBnh l\xE0 Draft
};
var useStockEntryStore = (0,esm/* create */.Ue)(function (set, get) {
  return objectSpread2_default()(objectSpread2_default()({}, initialState), {}, {
    // Basic Actions
    setForm: function setForm(form) {
      console.log('setForm', form);
      return set({
        form: form
      });
    },
    setSubmitting: function setSubmitting(submitting) {
      return set({
        submitting: submitting
      });
    },
    setSelectedItems: function setSelectedItems(items) {
      console.log('setSelectedItems', items);
      return set({
        selectedItems: items
      });
    },
    setTreeData: function setTreeData(data) {
      return set({
        treeData: data
      });
    },
    setItems: function setItems(items) {
      return set({
        items: items
      });
    },
    setSelectedSourceWarehouse: function setSelectedSourceWarehouse(warehouse) {
      return set({
        selectedSourceWarehouse: warehouse,
        selectedItems: warehouse !== get().selectedSourceWarehouse ? [] : get().selectedItems,
        // Reset items n\u1EBFu warehouse thay \u0111\u1ED5i
        isItemTreeDataLoaded: false // Chu\u1EA9n b\u1ECB \u0111\u1EC3 fetch l\u1EA1i khi c\u1EA7n
      });
    },
    setSelectedTargetWarehouse: function setSelectedTargetWarehouse(warehouse) {
      return set({
        selectedTargetWarehouse: warehouse
      });
    },
    setIsItemTreeDataLoaded: function setIsItemTreeDataLoaded(loaded) {
      return set({
        isItemTreeDataLoaded: loaded
      });
    },
    setSaved: function setSaved(isSaved) {
      return set({
        isSaved: isSaved
      });
    },
    setSavedVoucherData: function setSavedVoucherData(data) {
      return set({
        savedVoucherData: data
      });
    },
    setDocstatus: function setDocstatus(status) {
      return set({
        docstatus: status
      });
    },
    // Item Management Actions
    addItem: function addItem(item) {
      var state = get();
      var existingItem = state.selectedItems.find(function (i) {
        return i.item_code === item.item_code;
      });
      if (!existingItem) {
        set({
          selectedItems: [].concat(toConsumableArray_default()(state.selectedItems), [item])
        });
      } else {
        message/* default */.ZP.warning('Item already exists in the list');
      }
    },
    removeItem: function removeItem(itemCode) {
      var state = get();
      set({
        selectedItems: state.selectedItems.filter(function (item) {
          return item.item_code !== itemCode;
        })
      });
    },
    updateItemQuantity: function updateItemQuantity(itemCode, quantity) {
      var state = get();
      var updatedItems = state.selectedItems.map(function (item) {
        if (item.item_code === itemCode) {
          var total_price = item.rate ? quantity * item.rate : 0;
          return objectSpread2_default()(objectSpread2_default()({}, item), {}, {
            qty: quantity,
            total_price: total_price
          });
        }
        return item;
      });
      set({
        selectedItems: updatedItems
      });
    },
    updateItemPrice: function updateItemPrice(itemCode, price) {
      var state = get();
      var updatedItems = state.selectedItems.map(function (item) {
        if (item.item_code === itemCode) {
          var total_price = item.qty * price;
          return objectSpread2_default()(objectSpread2_default()({}, item), {}, {
            rate: price,
            total_price: total_price
          });
        }
        return item;
      });
      set({
        selectedItems: updatedItems
      });
    },
    // Fetch Item Tree Data
    fetchItemTreeData: function () {
      var _fetchItemTreeData = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(warehouseId, manualTrigger) {
        var state, _yield$Promise$all, _yield$Promise$all2, itemsResponse, groupsResponse, dataMap, generatedData;
        return regeneratorRuntime_default()().wrap(function _callee$(_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              state = get();
              if (!((state.isInitializing || state.isItemTreeDataLoaded) && !manualTrigger)) {
                _context.next = 3;
                break;
              }
              return _context.abrupt("return");
            case 3:
              set({
                isInitializing: true
              });
              _context.prev = 4;
              _context.next = 7;
              return Promise.all([(0,item/* getItemList */.m)({
                warehouse: warehouseId
              }), (0,item/* getItemGroupList */.A)({})]);
            case 7:
              _yield$Promise$all = _context.sent;
              _yield$Promise$all2 = slicedToArray_default()(_yield$Promise$all, 2);
              itemsResponse = _yield$Promise$all2[0];
              groupsResponse = _yield$Promise$all2[1];
              dataMap = groupByItemGroupWithItemGroupData(itemsResponse.data, groupsResponse.data);
              if (dataMap) {
                generatedData = Object.entries(dataMap).map(function (_ref) {
                  var _groupData$itemGroup;
                  var _ref2 = slicedToArray_default()(_ref, 2),
                    itemGroup = _ref2[0],
                    groupData = _ref2[1];
                  return {
                    title: ((_groupData$itemGroup = groupData.itemGroup) === null || _groupData$itemGroup === void 0 ? void 0 : _groupData$itemGroup.label) || '',
                    value: itemGroup,
                    key: itemGroup,
                    children: groupData.items.map(function (item) {
                      return {
                        title: item.label || '',
                        value: item.name || '',
                        key: item.name || ''
                      };
                    })
                  };
                });
                set({
                  treeData: generatedData,
                  items: itemsResponse.data,
                  isItemTreeDataLoaded: true,
                  selectedSourceWarehouse: warehouseId
                });
              }
              _context.next = 19;
              break;
            case 15:
              _context.prev = 15;
              _context.t0 = _context["catch"](4);
              message/* default */.ZP.error('Failed to fetch item data');
              set({
                isItemTreeDataLoaded: false,
                selectedSourceWarehouse: '',
                treeData: [],
                items: []
              });
            case 19:
              _context.prev = 19;
              set({
                isInitializing: false
              });
              return _context.finish(19);
            case 22:
            case "end":
              return _context.stop();
          }
        }, _callee, null, [[4, 15, 19, 22]]);
      }));
      function fetchItemTreeData(_x, _x2) {
        return _fetchItemTreeData.apply(this, arguments);
      }
      return fetchItemTreeData;
    }(),
    // Calculation Actions
    calculateTotalValue: function calculateTotalValue() {
      var state = get();
      return state.selectedItems.reduce(function (acc, item) {
        return acc + (item.total_price || 0);
      }, 0);
    },
    // Reset Action
    reset: function reset() {
      return set(objectSpread2_default()(objectSpread2_default()({}, initialState), {}, {
        isItemTreeDataLoaded: false,
        isInitializing: false,
        isSaved: false,
        savedVoucherData: null,
        docstatus: 0 // Reset v\u1EC1 Draft
      }));
    }
  });
});
;// CONCATENATED MODULE: ./src/pages/WarehouseManagementV3/Inventory/StockEntryHistory/components/StockEntryDetailEnhanced/hooks/useStockEntryLogic.ts













var useStockEntryLogic = function useStockEntryLogic(onSuccess) {
  var store = useStockEntryStore();

  // Handle source warehouse selection
  var handleSelectSourceWarehouse = (0,react.useCallback)(function (warehouseId) {
    if (warehouseId !== store.selectedSourceWarehouse) {
      store.setSelectedSourceWarehouse(warehouseId);
      store.setSelectedItems([]);
      store.setIsItemTreeDataLoaded(false);
    }
  }, []);

  // Handle target warehouse selection (cho Material Transfer)
  var handleSelectTargetWarehouse = (0,react.useCallback)(function (warehouseId) {
    store.setSelectedTargetWarehouse(warehouseId);
  }, []);

  // Handle adding items
  var handleAddItems = (0,react.useCallback)( /*#__PURE__*/asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
    var itemIds, filteredItems, inventory, formattedItems;
    return regeneratorRuntime_default()().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          if (store.form) {
            _context.next = 2;
            break;
          }
          return _context.abrupt("return");
        case 2:
          itemIds = store.form.getFieldValue('items');
          if (itemIds) {
            _context.next = 6;
            break;
          }
          message/* default */.ZP.error('Vui l\xF2ng ch\u1ECDn h\xE0ng h\xF3a');
          return _context.abrupt("return");
        case 6:
          filteredItems = store.items.filter(function (item) {
            return itemIds.includes(item.name) && !store.selectedItems.some(function (selected) {
              return selected.item_code === item.name;
            });
          });
          if (!(filteredItems.length === 0)) {
            _context.next = 10;
            break;
          }
          message/* default */.ZP.error('H\xE0ng h\xF3a \u0111\xE3 t\u1ED3n t\u1EA1i trong danh s\xE1ch');
          return _context.abrupt("return");
        case 10:
          _context.next = 12;
          return (0,InventoryManagementV3_inventory/* getInventoryV3 */.R)({
            warehouse: store.selectedSourceWarehouse,
            page: '1',
            size: '1000'
          });
        case 12:
          inventory = _context.sent;
          formattedItems = filteredItems.map(function (item) {
            var inventoryItem = inventory.data.find(function (inv) {
              return inv.item_code === item.item_code;
            });
            return {
              conversion_factor: 1,
              item_code: item.name,
              item_name: item.item_name,
              item_label: item.label,
              qty: 0,
              actual_qty: (inventoryItem === null || inventoryItem === void 0 ? void 0 : inventoryItem.actual_qty) || 0,
              rate: item.valuation_rate,
              total_price: 0,
              warehouse: store.selectedSourceWarehouse,
              uom: item.stock_uom || 'Unit'
            };
          });
          store.setSelectedItems([].concat(toConsumableArray_default()(formattedItems), toConsumableArray_default()(store.selectedItems)));
          store.form.setFieldValue('items', []);
        case 16:
        case "end":
          return _context.stop();
      }
    }, _callee);
  })), [store.form, store.items, store.selectedItems, store.selectedSourceWarehouse]);

  // Handle Excel import
  var handleExcelImport = (0,react.useCallback)( /*#__PURE__*/function () {
    var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2(excelData) {
      var inventory, formattedItems;
      return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            _context2.prev = 0;
            _context2.next = 3;
            return (0,InventoryManagementV3_inventory/* getInventoryV3 */.R)({
              warehouse: store.selectedSourceWarehouse,
              page: '1',
              size: '1000'
            });
          case 3:
            inventory = _context2.sent;
            formattedItems = excelData.map(function (row) {
              var item = store.items.find(function (i) {
                return i.item_name === row.item_name;
              });
              if (!item) return null;
              var existingItem = store.selectedItems.find(function (selected) {
                return selected.item_code === item.name;
              });
              if (existingItem) return null;
              var inventoryItem = inventory.data.find(function (inv) {
                return inv.item_code === item.item_code;
              });
              return {
                conversion_factor: 1,
                item_code: item.name,
                item_name: item.item_name,
                item_label: item.label,
                qty: row.quantity || 0,
                actual_qty: (inventoryItem === null || inventoryItem === void 0 ? void 0 : inventoryItem.actual_qty) || 0,
                rate: row.valuation_rate || item.valuation_rate,
                total_price: (row.quantity || 0) * (row.valuation_rate || item.valuation_rate),
                warehouse: store.selectedSourceWarehouse,
                uom: item.stock_uom || 'Unit'
              };
            }).filter(Boolean);
            store.setSelectedItems([].concat(toConsumableArray_default()(store.selectedItems), toConsumableArray_default()(formattedItems)));
            _context2.next = 11;
            break;
          case 8:
            _context2.prev = 8;
            _context2.t0 = _context2["catch"](0);
            message/* default */.ZP.error('Failed to import Excel data');
          case 11:
          case "end":
            return _context2.stop();
        }
      }, _callee2, null, [[0, 8]]);
    }));
    return function (_x) {
      return _ref2.apply(this, arguments);
    };
  }(), [store.selectedSourceWarehouse, store.items, store.selectedItems]);

  // Validate items before submission
  var validateItems = (0,react.useCallback)(function (items, stockEntryType) {
    var _iterator = createForOfIteratorHelper_default()(items),
      _step;
    try {
      for (_iterator.s(); !(_step = _iterator.n()).done;) {
        var item = _step.value;
        if (item.qty === null || item.qty <= 0) {
          message/* default */.ZP.error('S\u1ED1 l\u01B0\u1EE3ng h\xE0ng h\xF3a ph\u1EA3i l\u1EDBn h\u01A1n 0');
          return false;
        }
        if (stockEntryType === 'Material Issue' && item.qty > item.actual_qty) {
          message/* default */.ZP.error("S\\u1ED1 l\\u01B0\\u1EE3ng xu\\u1EA5t c\\u1EE7a ".concat(item.item_name, " v\\u01B0\\u1EE3t qu\\xE1 s\\u1ED1 l\\u01B0\\u1EE3ng t\\u1ED3n kho"));
          return false;
        }
      }
    } catch (err) {
      _iterator.e(err);
    } finally {
      _iterator.f();
    }
    return true;
  }, []);

  // Handle save (draft)
  var handleSave = (0,react.useCallback)( /*#__PURE__*/function () {
    var _ref3 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee3(values) {
      var _store$savedVoucherDa, stockEntryType, date, posting_date, posting_time, stockEntry, saveRes;
      return regeneratorRuntime_default()().wrap(function _callee3$(_context3) {
        while (1) switch (_context3.prev = _context3.next) {
          case 0:
            store.setSubmitting(true);
            _context3.prev = 1;
            stockEntryType = values.purpose || 'Material Issue';
            if (validateItems(store.selectedItems, stockEntryType)) {
              _context3.next = 5;
              break;
            }
            return _context3.abrupt("return", false);
          case 5:
            if (!(store.selectedItems.length > 20)) {
              _context3.next = 8;
              break;
            }
            message/* default */.ZP.error('S\u1ED1 l\u01B0\u1EE3ng h\xE0ng h\xF3a kh\xF4ng \u0111\u01B0\u1EE3c v\u01B0\u1EE3t qu\xE1 20.');
            return _context3.abrupt("return", false);
          case 8:
            date = values.posting_date ? dayjs_min_default()(values.posting_date, constanst/* DEFAULT_DATE_AND_HH_MM_FORMAT */.dD) : dayjs_min_default()();
            if (!date.isValid()) {
              console.error('Invalid posting_date from form:', values.posting_date);
              date = dayjs_min_default()();
            }
            posting_date = date.format('YYYY-MM-DD');
            posting_time = "".concat(date.format('HH:mm'), ":").concat(dayjs_min_default()().format('ss'));
            stockEntry = objectSpread2_default()(objectSpread2_default()({}, store.savedVoucherData), {}, {
              __isLocal: (_store$savedVoucherDa = store.savedVoucherData) !== null && _store$savedVoucherDa !== void 0 && _store$savedVoucherDa.name ? 0 : 1,
              __unsaved: 1,
              doctype: 'Stock Entry',
              stock_entry_type: stockEntryType,
              posting_date: posting_date,
              posting_time: posting_time,
              set_posting_time: 1,
              company: 'VIIS',
              description: values.description,
              employee: values.employee,
              file_path: values.file_path,
              source_warehouse: values.source_warehouse || store.selectedSourceWarehouse,
              target_warehouse: values.target_warehouse || store.selectedTargetWarehouse || undefined,
              items: store.selectedItems.map(function (item) {
                var _store$savedVoucherDa2;
                return {
                  doctype: 'Stock Entry Detail',
                  docstatus: 0,
                  __isLocal: 1,
                  __unsaved: 1,
                  parent: (_store$savedVoucherDa2 = store.savedVoucherData) === null || _store$savedVoucherDa2 === void 0 ? void 0 : _store$savedVoucherDa2.name,
                  parentfield: 'items',
                  parenttype: 'Stock Entry',
                  name: item.name || 'new-stock-entry-detail-' + generateRandomString(),
                  item_code: item.item_code,
                  qty: item.qty,
                  uom: item.uom,
                  conversion_factor: item.conversion_factor,
                  s_warehouse: stockEntryType !== 'Material Receipt' ? values.source_warehouse || store.selectedSourceWarehouse : undefined,
                  t_warehouse: stockEntryType === 'Material Transfer' || stockEntryType === 'Material Receipt' ? values.target_warehouse || store.selectedSourceWarehouse : undefined,
                  rate: item.rate
                };
              })
            });
            _context3.next = 15;
            return (0,stock_stockEntry/* saveStockEntry */.IP)(stockEntry);
          case 15:
            saveRes = _context3.sent;
            store.setSavedVoucherData(saveRes);
            store.setSaved(true);
            store.setDocstatus(0); // Draft
            message/* default */.ZP.success('L\u01B0u th\xE0nh c\xF4ng');
            return _context3.abrupt("return", true);
          case 23:
            _context3.prev = 23;
            _context3.t0 = _context3["catch"](1);
            console.error('Error in handleSave:', _context3.t0);
            message/* default */.ZP.error('L\u01B0u th\u1EA5t b\u1EA1i');
            return _context3.abrupt("return", false);
          case 28:
            _context3.prev = 28;
            store.setSubmitting(false);
            return _context3.finish(28);
          case 31:
          case "end":
            return _context3.stop();
        }
      }, _callee3, null, [[1, 23, 28, 31]]);
    }));
    return function (_x2) {
      return _ref3.apply(this, arguments);
    };
  }(), [store.selectedItems, store.savedVoucherData, store.selectedSourceWarehouse, store.selectedTargetWarehouse]);

  // Handle submit (from saved draft)
  var handleSubmit = (0,react.useCallback)( /*#__PURE__*/function () {
    var _ref4 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee4(formValues) {
      var hasChanges, saveSuccess;
      return regeneratorRuntime_default()().wrap(function _callee4$(_context4) {
        while (1) switch (_context4.prev = _context4.next) {
          case 0:
            // Th\xEAm tham s\u1ED1 formValues \u0111\u1EC3 nh\u1EADn gi\xE1 tr\u1ECB t\u1EEB form n\u1EBFu c\u1EA7n
            store.setSubmitting(true);
            _context4.prev = 1;
            // N\u1EBFu ch\u01B0a c\xF3 savedVoucherData ho\u1EB7c selectedItems \u0111\xE3 thay \u0111\u1ED5i, g\u1ECDi handleSave tr\u01B0\u1EDBc
            hasChanges = !store.savedVoucherData || JSON.stringify(store.savedVoucherData.items) !== JSON.stringify(store.selectedItems.map(function (item) {
              return {
                item_code: item.item_code,
                qty: item.qty,
                rate: item.rate,
                uom: item.uom,
                conversion_factor: item.conversion_factor
              };
            }));
            if (!(hasChanges && formValues)) {
              _context4.next = 9;
              break;
            }
            _context4.next = 6;
            return handleSave(formValues);
          case 6:
            saveSuccess = _context4.sent;
            if (saveSuccess) {
              _context4.next = 9;
              break;
            }
            return _context4.abrupt("return", false);
          case 9:
            if (store.savedVoucherData) {
              _context4.next = 12;
              break;
            }
            message/* default */.ZP.error('Vui l\xF2ng l\u01B0u phi\u1EBFu tr\u01B0\u1EDBc khi ho\xE0n th\xE0nh');
            return _context4.abrupt("return", false);
          case 12:
            _context4.next = 14;
            return (0,stock_stockEntry/* submitStockEntry */.yt)(objectSpread2_default()(objectSpread2_default()({}, store.savedVoucherData), {}, {
              doctype: 'Stock Entry',
              status: 'Draft'
            }));
          case 14:
            message/* default */.ZP.success('Ho\xE0n th\xE0nh phi\u1EBFu th\xE0nh c\xF4ng');
            store.setDocstatus(1); // Submitted
            onSuccess === null || onSuccess === void 0 || onSuccess();
            return _context4.abrupt("return", true);
          case 20:
            _context4.prev = 20;
            _context4.t0 = _context4["catch"](1);
            message/* default */.ZP.error('Ho\xE0n th\xE0nh phi\u1EBFu th\u1EA5t b\u1EA1i');
            return _context4.abrupt("return", false);
          case 24:
            _context4.prev = 24;
            store.setSubmitting(false);
            return _context4.finish(24);
          case 27:
          case "end":
            return _context4.stop();
        }
      }, _callee4, null, [[1, 20, 24, 27]]);
    }));
    return function (_x3) {
      return _ref4.apply(this, arguments);
    };
  }(), [store.savedVoucherData, store.selectedItems, handleSave]);

  // Handle delete voucher
  var handleDelete = (0,react.useCallback)( /*#__PURE__*/asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee5() {
    var _store$savedVoucherDa3;
    return regeneratorRuntime_default()().wrap(function _callee5$(_context5) {
      while (1) switch (_context5.prev = _context5.next) {
        case 0:
          store.setSubmitting(true);
          _context5.prev = 1;
          if (!(store.docstatus !== 0)) {
            _context5.next = 5;
            break;
          }
          message/* default */.ZP.error('Ch\u1EC9 c\xF3 th\u1EC3 x\xF3a phi\u1EBFu \u1EDF tr\u1EA1ng th\xE1i nh\xE1p');
          return _context5.abrupt("return", false);
        case 5:
          if ((_store$savedVoucherDa3 = store.savedVoucherData) !== null && _store$savedVoucherDa3 !== void 0 && _store$savedVoucherDa3.name) {
            _context5.next = 8;
            break;
          }
          message/* default */.ZP.error('Kh\xF4ng t\xECm th\u1EA5y phi\u1EBFu \u0111\u1EC3 x\xF3a');
          return _context5.abrupt("return", false);
        case 8:
          _context5.next = 10;
          return (0,stock_stockEntry/* deleteStockEntry */.EC)(store.savedVoucherData.name);
        case 10:
          message/* default */.ZP.success('X\xF3a phi\u1EBFu th\xE0nh c\xF4ng');
          onSuccess === null || onSuccess === void 0 || onSuccess();
          return _context5.abrupt("return", true);
        case 15:
          _context5.prev = 15;
          _context5.t0 = _context5["catch"](1);
          message/* default */.ZP.error('X\xF3a phi\u1EBFu th\u1EA5t b\u1EA1i');
          return _context5.abrupt("return", false);
        case 19:
          _context5.prev = 19;
          store.setSubmitting(false);
          return _context5.finish(19);
        case 22:
        case "end":
          return _context5.stop();
      }
    }, _callee5, null, [[1, 15, 19, 22]]);
  })), [store.savedVoucherData, store.docstatus]);

  // Handle cancel voucher
  var handleCancel = (0,react.useCallback)( /*#__PURE__*/asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee6() {
    var _store$savedVoucherDa4, _store$savedVoucherDa5, purpose, cancelBody;
    return regeneratorRuntime_default()().wrap(function _callee6$(_context6) {
      while (1) switch (_context6.prev = _context6.next) {
        case 0:
          store.setSubmitting(true);
          _context6.prev = 1;
          if (!(store.docstatus !== 1)) {
            _context6.next = 5;
            break;
          }
          message/* default */.ZP.error('Ch\u1EC9 c\xF3 th\u1EC3 h\u1EE7y phi\u1EBFu \u1EDF tr\u1EA1ng th\xE1i \u0111\xE3 ho\xE0n th\xE0nh');
          return _context6.abrupt("return", false);
        case 5:
          if ((_store$savedVoucherDa4 = store.savedVoucherData) !== null && _store$savedVoucherDa4 !== void 0 && _store$savedVoucherDa4.name) {
            _context6.next = 8;
            break;
          }
          message/* default */.ZP.error('Kh\xF4ng t\xECm th\u1EA5y phi\u1EBFu \u0111\u1EC3 h\u1EE7y');
          return _context6.abrupt("return", false);
        case 8:
          purpose = ((_store$savedVoucherDa5 = store.savedVoucherData) === null || _store$savedVoucherDa5 === void 0 ? void 0 : _store$savedVoucherDa5.purpose) || 'Material Issue';
          cancelBody = {
            item_codes: store.savedVoucherData.items.map(function (item) {
              return item.item_code;
            }),
            warehouses: store.savedVoucherData.items.map(function (item) {
              if (purpose === 'Material Receipt') {
                return item.t_warehouse;
              } else if (purpose === 'Material Transfer') {
                return item.s_warehouse || item.t_warehouse;
              } else {
                return item.s_warehouse;
              }
            }),
            name: store.savedVoucherData.name
          };
          _context6.next = 12;
          return (0,stock_stockEntry/* cancelStockEntry */.YZ)(cancelBody);
        case 12:
          message/* default */.ZP.success('H\u1EE7y phi\u1EBFu th\xE0nh c\xF4ng');
          store.setDocstatus(2); // Cancelled
          onSuccess === null || onSuccess === void 0 || onSuccess();
          return _context6.abrupt("return", true);
        case 18:
          _context6.prev = 18;
          _context6.t0 = _context6["catch"](1);
          message/* default */.ZP.error('H\u1EE7y phi\u1EBFu th\u1EA5t b\u1EA1i');
          return _context6.abrupt("return", false);
        case 22:
          _context6.prev = 22;
          store.setSubmitting(false);
          return _context6.finish(22);
        case 25:
        case "end":
          return _context6.stop();
      }
    }, _callee6, null, [[1, 18, 22, 25]]);
  })), [store.savedVoucherData, store.docstatus]);

  // Handle warehouse change
  var handleSourceWarehouseChange = (0,react.useCallback)(function (warehouseId) {
    store.setSelectedSourceWarehouse(warehouseId);
    store.setSelectedItems([]);
    store.setIsItemTreeDataLoaded(false);
  }, []);
  var handleTargetWarehouseChange = (0,react.useCallback)(function (warehouseId) {
    store.setSelectedTargetWarehouse(warehouseId);
  }, []);
  return {
    handleSelectSourceWarehouse: handleSelectSourceWarehouse,
    handleSelectTargetWarehouse: handleSelectTargetWarehouse,
    handleAddItems: handleAddItems,
    handleExcelImport: handleExcelImport,
    handleSave: handleSave,
    handleSubmit: handleSubmit,
    handleDelete: handleDelete,
    handleCancel: handleCancel,
    validateItems: validateItems,
    handleSourceWarehouseChange: handleSourceWarehouseChange,
    handleTargetWarehouseChange: handleTargetWarehouseChange
  };
};
;// CONCATENATED MODULE: ./src/pages/WarehouseManagementV3/Inventory/StockEntryHistory/components/StockEntryDetailEnhanced/components/BasicInfoSection.tsx
// components/BasicInfoSection.tsx





 // Thay moment b\u1EB1ng dayjs






var BasicInfoSection = function BasicInfoSection() {
  var _useStockEntryStore = useStockEntryStore(),
    setSelectedSourceWarehouse = _useStockEntryStore.setSelectedSourceWarehouse,
    setSelectedTargetWarehouse = _useStockEntryStore.setSelectedTargetWarehouse,
    savedVoucherData = _useStockEntryStore.savedVoucherData;
  var _useSelectedWarehouse = (0,useWarehouseStore/* useSelectedWarehousedStore */.O)(),
    globalWarehouse = _useSelectedWarehouse.selectedWarehouse;
  var _useStockEntryLogic = useStockEntryLogic(),
    handleSourceWarehouseChange = _useStockEntryLogic.handleSourceWarehouseChange,
    handleTargetWarehouseChange = _useStockEntryLogic.handleTargetWarehouseChange;
  var _useModel = (0,_umi_production_exports.useModel)('@@initialState'),
    initialState = _useModel.initialState;
  var currentUser = initialState === null || initialState === void 0 ? void 0 : initialState.currentUser;
  (0,react.useEffect)(function () {
    var purpose = (savedVoucherData === null || savedVoucherData === void 0 ? void 0 : savedVoucherData.purpose) || 'Material Issue';
    if (purpose === 'Material Issue' || purpose === 'Material Transfer') {
      setSelectedSourceWarehouse(globalWarehouse);
    } else if (purpose === 'Material Receipt') {
      setSelectedTargetWarehouse(globalWarehouse);
    }
  }, [globalWarehouse, setSelectedSourceWarehouse, setSelectedTargetWarehouse, savedVoucherData]);
  var purpose = (savedVoucherData === null || savedVoucherData === void 0 ? void 0 : savedVoucherData.purpose) || 'Material Issue';

  // X\u1EED l\xFD posting_date t\u1EEB server ho\u1EB7c m\u1EB7c \u0111\u1ECBnh ng\xE0y hi\u1EC7n t\u1EA1i
  var postingDateValue = savedVoucherData !== null && savedVoucherData !== void 0 && savedVoucherData.posting_date ? dayjs_min_default()("".concat(savedVoucherData.posting_date, " ").concat(savedVoucherData.posting_time || '00:00:00'), 'YYYY-MM-DD HH:mm:ss') : dayjs_min_default()();
  return /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
    gutter: [16, 16],
    children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
      span: 8,
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(DateTimePicker/* default */.Z, {
        name: "posting_date",
        required: true,
        rules: [{
          required: true,
          message: 'Vui l\xF2ng ch\u1ECDn ng\xE0y ho\u1EA1ch to\xE1n'
        }],
        label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
          id: "common.transaction_date"
        }),
        width: "md",
        fieldProps: {
          format: constanst/* DEFAULT_DATE_AND_HH_MM_FORMAT */.dD,
          // \u0110\u1ECBnh d\u1EA1ng hi\u1EC3n th\u1ECB: DD-MM-YYYY HH:mm
          // disabled: true, // Kh\xF3a ch\u1EC9nh s\u1EEDa
          style: {
            width: '100%'
          }
          // value: postingDateValue, // Dayjs object tr\u1EF1c ti\u1EBFp
        },
        initialValue: postingDateValue
      })
    }), (purpose === 'Material Issue' || purpose === 'Material Transfer') && /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
      span: 8,
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
        showSearch: true,
        name: "source_warehouse",
        rules: [{
          required: true,
          message: 'Vui l\xF2ng ch\u1ECDn kho ngu\u1ED3n'
        }],
        required: true,
        label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
          id: "warehouse-management.from-warehouse-name"
        }),
        onChange: function onChange(value) {
          return handleSourceWarehouseChange(value);
        },
        request: fetchWarehouseList,
        width: "md",
        initialValue: globalWarehouse,
        fieldProps: {
          style: {
            width: '100%'
          }
        }
      })
    }), (purpose === 'Material Receipt' || purpose === 'Material Transfer') && /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
      span: 8,
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
        showSearch: true,
        name: "target_warehouse",
        rules: [{
          required: true,
          message: 'Vui l\xF2ng ch\u1ECDn kho \u0111\xEDch'
        }],
        required: true,
        label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
          id: "warehouse-management.to-warehouse-name"
        }),
        onChange: function onChange(value) {
          return handleTargetWarehouseChange(value);
        },
        request: fetchWarehouseList,
        width: "md",
        initialValue: globalWarehouse,
        fieldProps: {
          style: {
            width: '100%'
          }
        }
      })
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
      span: 8,
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
        showSearch: true,
        required: true,
        label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
          id: "common.employee"
        }),
        request: fetchCustomerUserList,
        initialValue: currentUser === null || currentUser === void 0 ? void 0 : currentUser.user_id,
        name: "employee",
        disabled: true,
        width: "md",
        fieldProps: {
          style: {
            width: '100%'
          }
        }
      })
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
      span: 8,
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
        name: "purpose",
        label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
          id: "warehouse-management.stock-entry-history.purpose"
        }),
        width: "md",
        options: [{
          label: 'Material Issue',
          value: 'Material Issue'
        }, {
          label: 'Material Receipt',
          value: 'Material Receipt'
        }, {
          label: 'Material Transfer',
          value: 'Material Transfer'
        }],
        rules: [{
          required: true,
          message: 'Vui l\xF2ng ch\u1ECDn lo\u1EA1i phi\u1EBFu kho'
        }],
        initialValue: purpose,
        fieldProps: {
          style: {
            width: '100%'
          },
          disabled: true
        }
      })
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
      span: 24,
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(TextArea/* default */.Z, {
        name: "description",
        label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
          id: "common.form.description"
        }),
        width: "xl",
        fieldProps: {
          style: {
            width: '100%'
          }
        }
      })
    })]
  });
};
// EXTERNAL MODULE: ./src/components/DownloadFileButton/index.tsx
var DownloadFileButton = __webpack_require__(62735);
// EXTERNAL MODULE: ./src/components/Form/FormTreeSelectSearch/index.tsx
var FormTreeSelectSearch = __webpack_require__(30653);
// EXTERNAL MODULE: ./src/components/UploadExcelFile/index.tsx
var UploadExcelFile = __webpack_require__(41155);
// EXTERNAL MODULE: ./src/pages/InventoryManagementV3/ProductManagement/Products/CreateProduct-v2/index.tsx + 3 modules
var CreateProduct_v2 = __webpack_require__(53328);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/PlusOutlined.js
var PlusOutlined = __webpack_require__(51042);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/layouts/ProForm/index.js + 3 modules
var ProForm = __webpack_require__(34994);
// EXTERNAL MODULE: ./node_modules/antd/es/space/index.js + 2 modules
var space = __webpack_require__(42075);
// EXTERNAL MODULE: ./node_modules/antd/es/divider/index.js + 1 modules
var divider = __webpack_require__(96074);
// EXTERNAL MODULE: ./src/services/InventoryManagementV3/product-item.ts
var product_item = __webpack_require__(58642);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/DeleteOutlined.js
var DeleteOutlined = __webpack_require__(82061);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-table/es/components/EditableTable/index.js + 1 modules
var EditableTable = __webpack_require__(88280);
// EXTERNAL MODULE: ./node_modules/antd/es/input-number/index.js + 16 modules
var input_number = __webpack_require__(9735);
// EXTERNAL MODULE: ./node_modules/numeral/numeral.js
var numeral = __webpack_require__(92077);
var numeral_default = /*#__PURE__*/__webpack_require__.n(numeral);
;// CONCATENATED MODULE: ./src/pages/WarehouseManagementV3/Inventory/StockEntryHistory/components/StockEntryDetailEnhanced/components/StockEntryItemTable.tsx













var StockEntryItemTable = function StockEntryItemTable(_ref) {
  var data = _ref.data,
    setData = _ref.setData;
  var actionRef = (0,react.useRef)();
  var _useStockEntryStore = useStockEntryStore(),
    savedVoucherData = _useStockEntryStore.savedVoucherData,
    updateItemQuantity = _useStockEntryStore.updateItemQuantity,
    updateItemPrice = _useStockEntryStore.updateItemPrice;

  // X\xF3a item kh\u1ECFi danh s\xE1ch
  var handleDeleteItem = function handleDeleteItem(key) {
    var newData = data.filter(function (item) {
      return item.item_code !== key;
    });
    setData(newData);
  };

  // C\u1EADp nh\u1EADt \u0111\u01A1n v\u1ECB (unit) v\xE0 c\xE1c gi\xE1 tr\u1ECB li\xEAn quan
  var handleSelectUnit = function handleSelectUnit(itemCode, conversionFactor, uom) {
    var _actionRef$current;
    var newData = structuredClone(data);
    var recordIndex = newData.findIndex(function (item) {
      return item.item_code === itemCode;
    });
    if (recordIndex !== -1) {
      newData[recordIndex].actual_qty = newData[recordIndex].actual_qty * newData[recordIndex].conversion_factor / conversionFactor;
      newData[recordIndex].conversion_factor = conversionFactor;
      newData[recordIndex].uom = uom;
      newData[recordIndex].total_price = newData[recordIndex].qty * (newData[recordIndex].rate || 0);
      setData(newData);
    }
    (_actionRef$current = actionRef.current) === null || _actionRef$current === void 0 || _actionRef$current.reload();
  };
  var columns = [{
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.index"
    }),
    dataIndex: 'index',
    editable: false,
    render: function render(_, __, index) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        children: index + 1
      });
    },
    width: 40
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.item_name"
    }),
    dataIndex: 'item_name',
    editable: false,
    width: 120
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "warehouse-management.export-voucher.item_name",
      defaultMessage: "unknown"
    }),
    dataIndex: 'item_label',
    editable: false,
    width: 80
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "warehouse-management.export-voucher.actual_qty"
    }),
    dataIndex: 'actual_qty',
    editable: false,
    render: function render(_, record) {
      return numeral_default()(record.actual_qty).format('0,0.00');
    },
    width: 80
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "warehouse-management.export-voucher.quantity"
    }),
    dataIndex: 'qty',
    renderFormItem: function renderFormItem(_, _ref2) {
      var record = _ref2.record;
      return /*#__PURE__*/(0,jsx_runtime.jsx)(input_number/* default */.Z, {
        style: {
          width: '100%'
        },
        formatter: function formatter(value) {
          return "".concat(value).replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');
        },
        parser: function parser(value) {
          return value.replace(/\\$\\s?|(,*)/g, '');
        },
        step: "0.01",
        defaultValue: record === null || record === void 0 ? void 0 : record.qty,
        onChange: function onChange(value) {
          var qty = Number(value) || 0;
          updateItemQuantity(record.item_code, qty); // C\u1EADp nh\u1EADt qty trong store
          setData(function (prev) {
            return prev.map(function (item) {
              return item.item_code === record.item_code ? objectSpread2_default()(objectSpread2_default()({}, item), {}, {
                qty: qty,
                total_price: qty * (item.rate || 0)
              }) : item;
            });
          });
        }
      });
    },
    width: 80
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.unit"
    }),
    dataIndex: 'uom',
    editable: false,
    render: function render(_, entity) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
        formItemProps: {
          style: {
            marginBottom: 0
          }
        },
        fieldProps: {
          value: entity.conversion_factor
        },
        placeholder: "\\u0110\\u01A1n v\\u1ECB",
        request: /*#__PURE__*/asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
          var _res$data$uoms;
          var res, uoms, stockUomOption;
          return regeneratorRuntime_default()().wrap(function _callee$(_context) {
            while (1) switch (_context.prev = _context.next) {
              case 0:
                _context.next = 2;
                return (0,product_item/* getDetailsProductItemV3 */.eX)({
                  name: entity.item_code || ''
                });
              case 2:
                res = _context.sent;
                uoms = ((_res$data$uoms = res.data.uoms) === null || _res$data$uoms === void 0 ? void 0 : _res$data$uoms.map(function (item) {
                  return {
                    label: item.uom_name,
                    value: item.conversion_factor,
                    uom_id: item.uom
                  };
                })) || [];
                stockUomOption = {
                  label: res.data.uom_label,
                  value: 1,
                  uom_id: res.data.stock_uom
                };
                return _context.abrupt("return", [stockUomOption].concat(toConsumableArray_default()(uoms)));
              case 6:
              case "end":
                return _context.stop();
            }
          }, _callee);
        })),
        onChange: function onChange(value, option) {
          return handleSelectUnit(entity.item_code, +value, option.uom_id);
        }
      });
    },
    width: 100
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.rate"
    }),
    dataIndex: 'rate',
    renderFormItem: function renderFormItem(_, _ref4) {
      var record = _ref4.record;
      return /*#__PURE__*/(0,jsx_runtime.jsx)(input_number/* default */.Z, {
        style: {
          width: '100%'
        },
        formatter: function formatter(value) {
          return "".concat(value).replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');
        },
        parser: function parser(value) {
          return value.replace(/\\$\\s?|(,*)/g, '');
        },
        step: "0.01",
        defaultValue: record === null || record === void 0 ? void 0 : record.rate,
        onChange: function onChange(value) {
          var rate = Number(value) || 0;
          updateItemPrice(record.item_code, rate); // C\u1EADp nh\u1EADt rate trong store
          setData(function (prev) {
            return prev.map(function (item) {
              return item.item_code === record.item_code ? objectSpread2_default()(objectSpread2_default()({}, item), {}, {
                rate: rate,
                total_price: (item.qty || 0) * rate
              }) : item;
            });
          });
        }
      });
    },
    width: 80
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "warehouse-management.export-voucher.price"
    }),
    dataIndex: 'total_price',
    editable: false,
    render: function render(_, record) {
      return numeral_default()(record.total_price).format('0,0');
    },
    width: 100
  }, {
    title: 'Action',
    dataIndex: 'item_code',
    editable: false,
    render: function render(_, record) {
      return [/*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
        icon: /*#__PURE__*/(0,jsx_runtime.jsx)(DeleteOutlined/* default */.Z, {}),
        onClick: function onClick() {
          return handleDeleteItem(record.item_code);
        }
      }, "delete")];
    },
    width: 80
  }];
  (0,react.useEffect)(function () {
    var _actionRef$current2;
    (_actionRef$current2 = actionRef.current) === null || _actionRef$current2 === void 0 || _actionRef$current2.reload();
  }, [data]);
  return /*#__PURE__*/(0,jsx_runtime.jsx)(EditableTable/* default */.Z, {
    style: {
      minWidth: '100%'
    },
    columns: columns,
    actionRef: actionRef,
    cardBordered: true,
    request: /*#__PURE__*/asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2() {
      return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            return _context2.abrupt("return", {
              data: data,
              success: true,
              total: data.length
            });
          case 1:
          case "end":
            return _context2.stop();
        }
      }, _callee2);
    })),
    editable: {
      type: 'multiple',
      editableKeys: data.map(function (item) {
        return item.item_code;
      }),
      onChange: function onChange(keys) {}
    },
    recordCreatorProps: false,
    rowKey: "item_code",
    search: false,
    options: {
      setting: {
        listsHeight: 400
      }
    },
    pagination: {
      defaultPageSize: 20,
      showSizeChanger: true,
      pageSizeOptions: ['20', '50', '100']
    },
    toolbar: {
      multipleLine: false
    },
    toolBarRender: false,
    size: "small",
    dateFormatter: "string"
  });
};
/* harmony default export */ var components_StockEntryItemTable = (StockEntryItemTable);
;// CONCATENATED MODULE: ./src/pages/WarehouseManagementV3/Inventory/StockEntryHistory/components/StockEntryDetailEnhanced/components/ItemSection.tsx








 // \u0110\xFAng t\xEAn hook

// Gi\u1EA3 \u0111\u1ECBnh \u0111\xE3 \u0111\u1ED5i t\xEAn t\u1EEB IImportVoucherItem




var ItemSection = function ItemSection() {
  var _useStockEntryStore = useStockEntryStore(),
    selectedSourceWarehouse = _useStockEntryStore.selectedSourceWarehouse,
    treeData = _useStockEntryStore.treeData,
    selectedItems = _useStockEntryStore.selectedItems,
    setSelectedItems = _useStockEntryStore.setSelectedItems,
    fetchItemTreeData = _useStockEntryStore.fetchItemTreeData,
    savedVoucherData = _useStockEntryStore.savedVoucherData;
  console.log('re-render ItemSection', selectedItems);
  var _useStockEntryLogic = useStockEntryLogic(),
    handleAddItems = _useStockEntryLogic.handleAddItems,
    handleExcelImport = _useStockEntryLogic.handleExcelImport;

  // S\u1EEDa h\xE0m wrapper \u0111\u1EC3 x\u1EED l\xFD setState \u0111\xFAng c\xE1ch
  var handleSetData = function handleSetData(value) {
    if (typeof value === 'function') {
      setSelectedItems(value(selectedItems)); // G\u1ECDi h\xE0m v\u1EDBi state hi\u1EC7n t\u1EA1i
    } else {
      setSelectedItems(value); // Set tr\u1EF1c ti\u1EBFp n\u1EBFu l\xE0 array
    }
  };

  // X\xE1c \u0111\u1ECBnh lo\u1EA1i Stock Entry t\u1EEB savedVoucherData
  var purpose = (savedVoucherData === null || savedVoucherData === void 0 ? void 0 : savedVoucherData.purpose) || 'Material Issue';
  return /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
    children: [/*#__PURE__*/(0,jsx_runtime.jsxs)(ProForm/* ProForm */.A.Group, {
      title: /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        style: {
          marginBottom: '-20px'
        },
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
          id: "common.item_list"
        })
      }),
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)(FormTreeSelectSearch/* default */.Z, {
        name: "items",
        fieldProps: {
          treeData: treeData
        },
        label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
          id: "common.item_name"
        }),
        dropdownBottom: /*#__PURE__*/(0,jsx_runtime.jsx)(CreateProduct_v2/* default */.Z, {
          buttonType: "link",
          onSuccess: function onSuccess() {
            fetchItemTreeData(selectedSourceWarehouse, true); // S\u1EED d\u1EE5ng selectedSourceWarehouse
          }
        }),
        colProps: {
          span: 8
        }
      }), /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        style: {
          marginLeft: 5
        },
        children: /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
          align: "middle",
          justify: "start",
          children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(UploadExcelFile/* default */.Z, {
              isReadonly: true,
              formItemName: "item_list",
              label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
                id: "action.import-from-excel"
              }),
              onExcelDataLoaded: handleExcelImport
            })
          }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
            style: {
              marginLeft: 5,
              paddingTop: 5
            },
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(DownloadFileButton/* default */.Z, {
              disabled: true,
              filePath: "/private/files/stock_voucher.xlsx",
              buttonName: "common.form.excel_template"
            })
          })]
        })
      })]
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(ProForm/* ProForm */.A.Group, {
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(row/* default */.Z, {
        style: {
          width: '100%'
        },
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
          span: 18,
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(space/* default */.Z, {
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
              type: "primary",
              icon: /*#__PURE__*/(0,jsx_runtime.jsx)(PlusOutlined/* default */.Z, {}),
              onClick: handleAddItems,
              disabled: !selectedSourceWarehouse && purpose !== 'Material Receipt' // Ch\u1EC9 c\u1EA7n kho ngu\u1ED3n cho Issue/Transfer
              ,
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
                id: "common.add_item"
              })
            })
          })
        })
      })
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(divider/* default */.Z, {}), /*#__PURE__*/(0,jsx_runtime.jsx)(components_StockEntryItemTable, {
      data: selectedItems,
      setData: handleSetData
    })]
  });
};
;// CONCATENATED MODULE: ./src/pages/WarehouseManagementV3/Inventory/StockEntryHistory/components/StockEntryDetailEnhanced/components/StockEntryForm.tsx
// components/StockEntryForm.tsx






var StockEntryForm = function StockEntryForm() {
  return /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
    children: [/*#__PURE__*/(0,jsx_runtime.jsx)(BasicInfoSection, {}), /*#__PURE__*/(0,jsx_runtime.jsx)(ItemSection, {})]
  });
};
;// CONCATENATED MODULE: ./src/pages/WarehouseManagementV3/Inventory/StockEntryHistory/components/StockEntryDetailEnhanced/types/index.ts
// types/index.ts

var ModalAction = /*#__PURE__*/function (ModalAction) {
  ModalAction["SUBMIT"] = "submit";
  ModalAction["CANCEL"] = "cancel";
  ModalAction["DELETE"] = "delete";
  return ModalAction;
}({});
;// CONCATENATED MODULE: ./src/pages/WarehouseManagementV3/Inventory/StockEntryHistory/components/StockEntryDetailEnhanced/hooks/useMainModalState.ts





var useModalState = function useModalState() {
  var _useState = (0,react.useState)(defineProperty_default()(defineProperty_default()(defineProperty_default()({}, ModalAction.SUBMIT, false), ModalAction.CANCEL, false), ModalAction.DELETE, false)),
    _useState2 = slicedToArray_default()(_useState, 2),
    modalStates = _useState2[0],
    setModalStates = _useState2[1];

  //console.log('[useModalState] Initial modalStates:', modalStates); // Debug ngay sau kh\u1EDFi t\u1EA1o

  var showModal = (0,react.useCallback)(function (action) {
    console.log("[useModalState] showModal called with action: ".concat(action));
    setModalStates(function (prev) {
      console.log('[useModalState] Previous modal states:', prev);
      var newState = objectSpread2_default()(objectSpread2_default()({}, prev), {}, defineProperty_default()({}, action, true));
      console.log('[useModalState] New modal states:', newState);
      return newState;
    });
  }, []);
  var hideModal = (0,react.useCallback)(function (action) {
    console.log("[useModalState] hideModal called with action: ".concat(action));
    setModalStates(function (prev) {
      console.log('[useModalState] Previous modal states:', prev);
      var newState = objectSpread2_default()(objectSpread2_default()({}, prev), {}, defineProperty_default()({}, action, false));
      console.log('[useModalState] New modal states:', newState);
      return newState;
    });
  }, []);
  return {
    modalStates: modalStates,
    showModal: showModal,
    hideModal: hideModal
  };
};
;// CONCATENATED MODULE: ./src/pages/WarehouseManagementV3/Inventory/StockEntryHistory/components/StockEntryDetailEnhanced/index.tsx
























var StockEntryDetailEnhanced = function StockEntryDetailEnhanced(_ref) {
  var name = _ref.name,
    _onSuccess = _ref.onSuccess,
    isModalOpen = _ref.isModalOpen,
    setIsModalOpen = _ref.setIsModalOpen;
  var _useStockEntryLogic = useStockEntryLogic(_onSuccess),
    handleSave = _useStockEntryLogic.handleSave,
    handleSubmit = _useStockEntryLogic.handleSubmit,
    handleCancel = _useStockEntryLogic.handleCancel,
    handleDelete = _useStockEntryLogic.handleDelete;
  var store = useStockEntryStore();
  var _Form$useForm = es_form/* default */.Z.useForm(),
    _Form$useForm2 = slicedToArray_default()(_Form$useForm, 1),
    form = _Form$useForm2[0];
  var _useModalState = useModalState(),
    modalStates = _useModalState.modalStates,
    showModal = _useModalState.showModal,
    hideModal = _useModalState.hideModal;
  var _useState = (0,react.useState)(null),
    _useState2 = slicedToArray_default()(_useState, 2),
    selectedActionComponent = _useState2[0],
    setSelectedActionComponent = _useState2[1];

  // Handler for action button selection
  var handleActionSelect = function handleActionSelect(Component, initialData) {
    setIsModalOpen(false); // Close current modal
    setSelectedActionComponent( /*#__PURE__*/(0,jsx_runtime.jsx)(Component, {
      onSuccess: function onSuccess() {
        _onSuccess === null || _onSuccess === void 0 || _onSuccess();
        setSelectedActionComponent(null); // Clear after success
      },
      onClose: function onClose() {
        return setSelectedActionComponent(null);
      },
      initialData: initialData,
      autoOpen: true
    }));
  };
  var _useRequest = (0,_umi_production_exports.useRequest)(stock_stockEntry/* getStockEntryNoteDetail */.T2, {
      manual: true,
      onError: function onError(error) {
        console.error('Error fetching stock entry:', error.message);
      },
      onSuccess: function onSuccess(data) {
        return asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
          var _data$items, _data$items2, _data$items3, _data$items4;
          var transformedItems;
          return regeneratorRuntime_default()().wrap(function _callee$(_context) {
            while (1) switch (_context.prev = _context.next) {
              case 0:
                console.log('fetched data is', data);
                form.setFieldsValue({
                  name: data.name,
                  posting_date: data.posting_date,
                  purpose: data.purpose,
                  employee: "".concat(data.user_first_name, " ").concat(data.user_last_name),
                  description: data.description,
                  source_warehouse: (_data$items = data.items) === null || _data$items === void 0 || (_data$items = _data$items[0]) === null || _data$items === void 0 ? void 0 : _data$items.s_warehouse,
                  target_warehouse: (_data$items2 = data.items) === null || _data$items2 === void 0 || (_data$items2 = _data$items2[0]) === null || _data$items2 === void 0 ? void 0 : _data$items2.t_warehouse,
                  file_path: data.file_path
                });
                store.setSavedVoucherData(data);
                store.setDocstatus(data.docstatus);
                store.setSelectedSourceWarehouse(((_data$items3 = data.items) === null || _data$items3 === void 0 || (_data$items3 = _data$items3[0]) === null || _data$items3 === void 0 ? void 0 : _data$items3.s_warehouse) || '');
                store.setSelectedTargetWarehouse(((_data$items4 = data.items) === null || _data$items4 === void 0 || (_data$items4 = _data$items4[0]) === null || _data$items4 === void 0 ? void 0 : _data$items4.t_warehouse) || '');
                if (data.items) {
                  transformedItems = data.items.map(function (item) {
                    return {
                      item_code: item.item_code,
                      item_name: item.item_name,
                      item_label: item.item_label,
                      actual_qty: item.actual_qty,
                      qty: item.qty,
                      rate: item.valuation_rate,
                      total_price: item.amount,
                      warehouse: item.s_warehouse || item.t_warehouse,
                      uom: item.uom || '',
                      uom_label: item.uom_name || 'N/A',
                      name: item.name,
                      conversion_factor: item.conversion_factor || 1
                    };
                  });
                  console.log('transformedItems', transformedItems);
                  store.setSelectedItems(transformedItems);
                }
              case 7:
              case "end":
                return _context.stop();
            }
          }, _callee);
        }))();
      }
    }),
    data = _useRequest.data,
    run = _useRequest.run;
  (0,react.useEffect)(function () {
    store.setForm(form);
  }, [store.form]);
  (0,react.useEffect)(function () {
    if (isModalOpen) {
      run({
        name: name
      });
    }
  }, [name, isModalOpen]);
  (0,react.useEffect)(function () {
    if (isModalOpen && store.selectedSourceWarehouse && !store.isItemTreeDataLoaded) {
      store.fetchItemTreeData(store.selectedSourceWarehouse);
    }
  }, [isModalOpen, store.selectedSourceWarehouse, store.isItemTreeDataLoaded]);
  var handleActionSuccess = /*#__PURE__*/function () {
    var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2(action) {
      return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            _context2.prev = 0;
            _context2.next = 3;
            return action();
          case 3:
            setIsModalOpen(false);
            _onSuccess === null || _onSuccess === void 0 || _onSuccess();
            _context2.next = 10;
            break;
          case 7:
            _context2.prev = 7;
            _context2.t0 = _context2["catch"](0);
            console.error('Action failed:', _context2.t0);
          case 10:
          case "end":
            return _context2.stop();
        }
      }, _callee2, null, [[0, 7]]);
    }));
    return function handleActionSuccess(_x) {
      return _ref2.apply(this, arguments);
    };
  }();
  var modalConfig = (0,react.useMemo)(function () {
    return defineProperty_default()(defineProperty_default()(defineProperty_default()({}, ModalAction.SUBMIT, {
      title: 'Ho\xE0n th\xE0nh phi\u1EBFu',
      message: 'B\u1EA1n c\xF3 ch\u1EAFc ch\u1EAFn mu\u1ED1n ho\xE0n th\xE0nh phi\u1EBFu?',
      description: 'Sau khi ho\xE0n th\xE0nh, phi\u1EBFu s\u1EBD kh\xF4ng th\u1EC3 ch\u1EC9nh s\u1EEDa.',
      action: function action() {
        return handleActionSuccess(handleSubmit);
      }
    }), ModalAction.CANCEL, {
      title: 'H\u1EE7y phi\u1EBFu',
      message: 'B\u1EA1n c\xF3 ch\u1EAFc ch\u1EAFn mu\u1ED1n h\u1EE7y phi\u1EBFu?',
      description: 'Sau khi h\u1EE7y, phi\u1EBFu s\u1EBD kh\xF4ng th\u1EC3 kh\xF4i ph\u1EE5c.',
      type: 'danger',
      action: function action() {
        return handleActionSuccess(handleCancel);
      }
    }), ModalAction.DELETE, {
      title: 'X\xF3a phi\u1EBFu',
      message: 'B\u1EA1n c\xF3 ch\u1EAFc ch\u1EAFn mu\u1ED1n x\xF3a phi\u1EBFu?',
      description: 'Phi\u1EBFu s\u1EBD b\u1ECB x\xF3a v\u0129nh vi\u1EC5n v\xE0 kh\xF4ng th\u1EC3 kh\xF4i ph\u1EE5c.',
      type: 'danger',
      action: function action() {
        return handleActionSuccess(handleDelete);
      }
    });
  }, [handleSubmit, handleCancel, handleDelete]);
  var renderActionButtons = function renderActionButtons() {
    var _store$savedVoucherDa;
    var isEditable = store.docstatus === 0; // Draft
    var isSubmitted = store.docstatus === 1; // Submitted
    var stockEntryType = ((_store$savedVoucherDa = store.savedVoucherData) === null || _store$savedVoucherDa === void 0 ? void 0 : _store$savedVoucherDa.purpose) || 'Material Issue';
    var receiptType = '';
    var voucherActionType = '';
    switch (stockEntryType) {
      case 'Material Issue':
        receiptType = 'materialIssue';
        voucherActionType = 'Material Issue';
        break;
      case 'Material Receipt':
        receiptType = 'materialReceipt';
        voucherActionType = 'Material Receipt';
        break;
      case 'Material Transfer':
        receiptType = 'materialTransfer';
        voucherActionType = 'Material Transfer';
        break;
      default:
        receiptType = 'notHandledType';
        voucherActionType = 'Material Issue';
    }
    return /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
      style: {
        paddingLeft: '25.5rem',
        paddingRight: '22rem',
        display: 'flex',
        justifyContent: 'space-between',
        width: '100%',
        alignItems: 'center'
      },
      children: [/*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
        style: {
          display: 'flex',
          gap: '8px'
        },
        children: [isEditable && /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
          children: [/*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
            type: "primary",
            onClick: function onClick() {
              return handleActionSuccess( /*#__PURE__*/asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee3() {
                var values;
                return regeneratorRuntime_default()().wrap(function _callee3$(_context3) {
                  while (1) switch (_context3.prev = _context3.next) {
                    case 0:
                      _context3.next = 2;
                      return form.validateFields();
                    case 2:
                      values = _context3.sent;
                      _context3.next = 5;
                      return handleSave(values);
                    case 5:
                    case "end":
                      return _context3.stop();
                  }
                }, _callee3);
              })));
            },
            loading: store.submitting,
            style: {
              width: '150px'
            },
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
              id: "common.save"
            })
          }, "save"), /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
            type: "primary",
            onClick: function onClick() {
              return showModal(ModalAction.SUBMIT);
            },
            loading: store.submitting,
            style: {
              width: '150px'
            },
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
              id: "common.submit"
            })
          }, "submit"), /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
            danger: true,
            onClick: function onClick() {
              return showModal(ModalAction.DELETE);
            },
            loading: store.submitting,
            style: {
              width: '150px'
            },
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
              id: "common.delete"
            })
          }, "delete")]
        }), isSubmitted && /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
          danger: true,
          onClick: function onClick() {
            console.log('Cancel button clicked');
            showModal(ModalAction.CANCEL);
          },
          disabled: store.submitting,
          style: {
            width: '150px'
          },
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
            id: "common.cancel"
          })
        }, "cancel"), /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
          icon: /*#__PURE__*/(0,jsx_runtime.jsx)(PrinterOutlined/* default */.Z, {}),
          onClick: function onClick() {
            (0,utils/* openInNewTab */.YQ)("/warehouse-management-v3/to-pdf?type=".concat(receiptType, "&id=").concat(data === null || data === void 0 ? void 0 : data.name));
          },
          disabled: false // R\xF5 r\xE0ng kh\xF4ng bao gi\u1EDD disabled
          ,
          style: {
            width: '150px'
          },
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
            id: "common.print_receipt"
          })
        }, "print")]
      }), isSubmitted && voucherActions/* voucherActionConfigs */.A[voucherActionType] && /*#__PURE__*/(0,jsx_runtime.jsx)(StockActionButton/* default */.Z, {
        voucherData: store.savedVoucherData,
        actions: voucherActions/* voucherActionConfigs */.A[voucherActionType].map(function (action) {
          return objectSpread2_default()(objectSpread2_default()({}, action), {}, {
            onSelect: function onSelect() {
              return handleActionSelect(action.createComponent, action.mapData(store.savedVoucherData));
            }
          });
        }),
        onActionSuccess: _onSuccess,
        closeCurrentModal: function closeCurrentModal() {
          console.log('Closing current modal in StockEntryDetailEnhanced');
          setIsModalOpen(false);
        }
      })]
    });
  };
  var getTitle = function getTitle() {
    var _store$savedVoucherDa2, _store$savedVoucherDa3;
    var purpose = ((_store$savedVoucherDa2 = store.savedVoucherData) === null || _store$savedVoucherDa2 === void 0 ? void 0 : _store$savedVoucherDa2.purpose) || 'Material Issue';
    var titleId = 'warehouse-management.material-issue';
    if (purpose === 'Material Receipt') {
      titleId = 'warehouse-management.material-receipt';
    } else if (purpose === 'Material Transfer') {
      titleId = 'warehouse-management.material-transfer';
    }
    return /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
        id: titleId
      }), " ", ((_store$savedVoucherDa3 = store.savedVoucherData) === null || _store$savedVoucherDa3 === void 0 ? void 0 : _store$savedVoucherDa3.name) || '', " ", ' - ', ' ', /*#__PURE__*/(0,jsx_runtime.jsx)(components_DocStatusTag, {
        status: store.docstatus
      })]
    });
  };
  return /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
    children: [/*#__PURE__*/(0,jsx_runtime.jsxs)(ModalForm/* ModalForm */.Y, {
      disabled: store.docstatus !== 0 // V\xF4 hi\u1EC7u h\xF3a form nh\u01B0ng kh\xF4ng \u1EA3nh h\u01B0\u1EDFng \u0111\u1EBFn n\xFAt Print
      ,
      open: isModalOpen,
      title: getTitle(),
      modalProps: {
        onCancel: function onCancel() {
          store.reset();
          setIsModalOpen(false);
        },
        destroyOnClose: true
      },
      width: 1600,
      form: form,
      layout: "vertical",
      rowProps: {
        gutter: [16, 0]
      },
      submitter: {
        render: renderActionButtons
      },
      autoFocusFirstInput: true,
      grid: true,
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)(spin/* default */.Z, {
        spinning: store.submitting,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(StockEntryForm, {})
      }), Object.entries(modalConfig).map(function (_ref5) {
        var _ref6 = slicedToArray_default()(_ref5, 2),
          action = _ref6[0],
          config = _ref6[1];
        return /*#__PURE__*/(0,jsx_runtime.jsx)(ConfirmMdal/* default */.Z, objectSpread2_default()(objectSpread2_default()({
          open: modalStates[action]
        }, config), {}, {
          onConfirm: /*#__PURE__*/asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee4() {
            return regeneratorRuntime_default()().wrap(function _callee4$(_context4) {
              while (1) switch (_context4.prev = _context4.next) {
                case 0:
                  hideModal(action);
                  _context4.next = 3;
                  return config.action();
                case 3:
                case "end":
                  return _context4.stop();
              }
            }, _callee4);
          })),
          onCancel: function onCancel() {
            return hideModal(action);
          },
          confirmLoading: store.submitting,
          maskClosable: false
        }), action);
      })]
    }), selectedActionComponent]
  });
};
/* harmony default export */ var components_StockEntryDetailEnhanced = (StockEntryDetailEnhanced);
;// CONCATENATED MODULE: ./src/pages/WarehouseManagementV3/Inventory/StockEntryHistory/index.tsx




















var StockEntryHistory = function StockEntryHistory(_ref) {
  var type = _ref.type,
    refreshIndicator = _ref.refreshIndicator;
  var actionRef = (0,react.useRef)();
  var _useState = (0,react.useState)(false),
    _useState2 = slicedToArray_default()(_useState, 2),
    isModalOpen = _useState2[0],
    setIsModalOpen = _useState2[1];
  var _useState3 = (0,react.useState)(),
    _useState4 = slicedToArray_default()(_useState3, 2),
    currentItem = _useState4[0],
    setCurrentItem = _useState4[1];
  var _useSelectedWarehouse = (0,useWarehouseStore/* useSelectedWarehousedStore */.O)(),
    selectedWarehouse = _useSelectedWarehouse.selectedWarehouse;
  var _useDateRangeStore = (0,useDateRangeStore/* useDateRangeStore */.f)(),
    dateRange = _useDateRangeStore.dateRange;
  var intl = (0,_umi_production_exports.useIntl)();
  var _App$useApp = app/* default */.Z.useApp(),
    message = _App$useApp.message;
  var handlePopupDetail = function handlePopupDetail(record) {
    setCurrentItem(record);
    setIsModalOpen(true);
  };
  (0,react.useEffect)(function () {
    var _actionRef$current;
    (_actionRef$current = actionRef.current) === null || _actionRef$current === void 0 || _actionRef$current.reload();
  }, [selectedWarehouse, refreshIndicator, dateRange]);
  var columns = [{
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.id"
    }),
    dataIndex: 'name',
    width: 100,
    render: function render(_, record) {
      return /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
          icon: /*#__PURE__*/(0,jsx_runtime.jsx)(EyeOutlined/* default */.Z, {}),
          style: {
            marginRight: '8px'
          },
          onClick: function onClick() {
            return handlePopupDetail(record);
          }
        }), record === null || record === void 0 ? void 0 : record.name]
      });
    }
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "warehouse-management.stock-entry-history.date"
    }),
    dataIndex: 'formatted_posting_date',
    width: 100,
    valueType: 'date',
    hideInSearch: true,
    render: function render(_, record) {
      return (0,date/* formatOnlyDate */.Yw)(record.formatted_posting_date);
    },
    fieldProps: {
      format: constanst/* DEFAULT_DATE_FORMAT_WITHOUT_TIME */.ug
    }
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.description"
    }),
    dataIndex: 'description',
    search: false,
    width: 200
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "warehouse-management.stock-entry-history.purpose"
    }),
    dataIndex: 'purpose',
    width: 150,
    render: function render(_, entity) {
      switch (entity.purpose) {
        case 'Material Issue':
          return /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
            id: "warehouse-management.material-issue"
          });
        case 'Material Receipt':
          return /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
            id: "warehouse-management.material-receipt"
          });
        case 'Material Transfer':
          return /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
            id: "common.material-transfer2"
          });
        default:
          return entity.purpose || 'N/A';
      }
    },
    renderFormItem: function renderFormItem() {
      return /*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
        options: [{
          label: intl.formatMessage({
            id: 'warehouse-management.material-issue'
          }),
          value: 'Material Issue'
        }, {
          label: intl.formatMessage({
            id: 'warehouse-management.material-receipt'
          }),
          value: 'Material Receipt'
        }, {
          label: intl.formatMessage({
            id: 'common.material-transfer'
          }),
          value: 'Material Transfer'
        }],
        name: "purpose",
        width: "md"
      });
    }
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.status"
    }),
    dataIndex: 'docstatus',
    width: 100,
    render: function render(_, entity) {
      switch (entity.docstatus) {
        case 0:
          return /*#__PURE__*/(0,jsx_runtime.jsx)(tag/* default */.Z, {
            color: constanst/* STATUS_TAG_COLOR */.iO['Draft'],
            children: intl.formatMessage({
              id: 'common.draft'
            })
          });
        case 1:
          return /*#__PURE__*/(0,jsx_runtime.jsx)(tag/* default */.Z, {
            color: constanst/* STATUS_TAG_COLOR */.iO['To Bill'],
            children: intl.formatMessage({
              id: 'common.submitted'
            })
          });
        case 2:
          return /*#__PURE__*/(0,jsx_runtime.jsx)(tag/* default */.Z, {
            color: constanst/* STATUS_TAG_COLOR */.iO['Cancelled'],
            children: intl.formatMessage({
              id: 'common.cancel'
            })
          });
        default:
          return null;
      }
    },
    renderFormItem: function renderFormItem() {
      return /*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
        options: [{
          label: intl.formatMessage({
            id: 'common.draft'
          }),
          value: 0
        }, {
          label: intl.formatMessage({
            id: 'common.submitted'
          }),
          value: 1
        }, {
          label: intl.formatMessage({
            id: 'common.cancel'
          }),
          value: 2
        }],
        name: "docstatus",
        width: "md"
      });
    }
  }];
  return /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
    children: [currentItem && /*#__PURE__*/(0,jsx_runtime.jsx)(components_StockEntryDetailEnhanced, {
      name: currentItem.name,
      isModalOpen: isModalOpen,
      setIsModalOpen: setIsModalOpen,
      onSuccess: function onSuccess() {
        var _actionRef$current2;
        return (_actionRef$current2 = actionRef.current) === null || _actionRef$current2 === void 0 ? void 0 : _actionRef$current2.reload();
      } // Added refresh callback
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(Table/* default */.Z, {
      actionRef: actionRef,
      columns: (0,_utils/* addDefaultConfigColumns */.m)(columns),
      cardBordered: true,
      size: "small",
      pagination: {
        defaultPageSize: 20,
        showSizeChanger: true,
        pageSizeOptions: ['20', '50', '100']
      },
      request: ( /*#__PURE__*/function () {
        var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(params, sort) {
          var filters, dateFilter, res;
          return regeneratorRuntime_default()().wrap(function _callee$(_context) {
            while (1) switch (_context.prev = _context.next) {
              case 0:
                _context.prev = 0;
                filters = [];
                dateFilter = {};
                dateFilter['start_date'] = dateRange === null || dateRange === void 0 ? void 0 : dateRange.at(0);
                dateFilter['end_date'] = dateRange === null || dateRange === void 0 ? void 0 : dateRange.at(1);
                if (params.name) {
                  filters.push(['Stock Entry', 'name', 'like', params.name]);
                }
                if (params.purpose) {
                  filters.push(['Stock Entry', 'purpose', '=', params.purpose]);
                }
                if (typeof params.docstatus === 'number') {
                  filters.push(['Stock Entry', 'docstatus', '=', params.docstatus]);
                }
                if (type) {
                  filters.push(['Stock Entry', 'purpose', '=', type]); // Respect the type prop if provided
                }
                _context.next = 11;
                return (0,stock_stockEntry/* getStockEntryNote */.bp)(objectSpread2_default()({
                  page: params.current,
                  size: params.pageSize,
                  filters: filters,
                  set_warehouse: selectedWarehouse === 'all' ? undefined : selectedWarehouse
                }, dateFilter));
              case 11:
                res = _context.sent;
                return _context.abrupt("return", {
                  data: (0,array/* sortArrayByObjectKey */.G3)({
                    arr: res.data,
                    sort: sort
                  }),
                  success: true,
                  total: res.pagination.totalElements
                });
              case 15:
                _context.prev = 15;
                _context.t0 = _context["catch"](0);
                message.error("Error when getting Stock Entry: ".concat(_context.t0));
                return _context.abrupt("return", {
                  success: false
                });
              case 19:
              case "end":
                return _context.stop();
            }
          }, _callee, null, [[0, 15]]);
        }));
        return function (_x, _x2) {
          return _ref2.apply(this, arguments);
        };
      }()),
      rowKey: "name",
      search: {
        labelWidth: 'auto'
      },
      toolbar: {
        multipleLine: false,
        actions: []
      }
    })]
  });
};
/* harmony default export */ var Inventory_StockEntryHistory = (StockEntryHistory);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///29401
`)},25770:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   m: function() { return /* binding */ addDefaultConfigColumns; }
/* harmony export */ });
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(97857);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0__);

var addDefaultConfigColumns = function addDefaultConfigColumns(columns) {
  return columns
  // add sort multiple columns
  .map(function (item, index) {
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default()({}, item), {}, {
      sorter: {
        multiple: index
      }
    });
  });
};//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiMjU3NzAuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBRU8sSUFBTUEsdUJBQXVCLEdBQUcsU0FBMUJBLHVCQUF1QkEsQ0FBSUMsT0FBMEIsRUFBSztFQUNyRSxPQUNFQTtFQUNFO0VBQUEsQ0FDQ0MsR0FBRyxDQUNGLFVBQUNDLElBQUksRUFBRUMsS0FBSztJQUFBLE9BQUFDLDRLQUFBLENBQUFBLDRLQUFBLEtBRUxGLElBQUk7TUFDUEcsTUFBTSxFQUFFO1FBQ05DLFFBQVEsRUFBRUg7TUFDWjtJQUFDO0VBQUEsQ0FFUCxDQUFDO0FBRVAsQ0FBQyIsInNvdXJjZXMiOlsid2VicGFjazovL2FudC1kZXNpZ24tcHJvLy4vc3JjL3BhZ2VzL1dhcmVob3VzZU1hbmFnZW1lbnRWMy9fdXRpbHMudHM/ZGFiMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcm9Db2x1bW5zIH0gZnJvbSAnQGFudC1kZXNpZ24vcHJvLWNvbXBvbmVudHMnO1xyXG5cclxuZXhwb3J0IGNvbnN0IGFkZERlZmF1bHRDb25maWdDb2x1bW5zID0gKGNvbHVtbnM6IFByb0NvbHVtbnM8YW55PltdKSA9PiB7XHJcbiAgcmV0dXJuIChcclxuICAgIGNvbHVtbnNcclxuICAgICAgLy8gYWRkIHNvcnQgbXVsdGlwbGUgY29sdW1uc1xyXG4gICAgICAubWFwPFByb0NvbHVtbnM8YW55Pj4oXHJcbiAgICAgICAgKGl0ZW0sIGluZGV4KSA9PlxyXG4gICAgICAgICAgKHtcclxuICAgICAgICAgICAgLi4uaXRlbSxcclxuICAgICAgICAgICAgc29ydGVyOiB7XHJcbiAgICAgICAgICAgICAgbXVsdGlwbGU6IGluZGV4LFxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgfSBhcyBQcm9Db2x1bW5zPGFueT4gYXMgYW55KSxcclxuICAgICAgKVxyXG4gICk7XHJcbn07XHJcbiJdLCJuYW1lcyI6WyJhZGREZWZhdWx0Q29uZmlnQ29sdW1ucyIsImNvbHVtbnMiLCJtYXAiLCJpdGVtIiwiaW5kZXgiLCJfb2JqZWN0U3ByZWFkIiwic29ydGVyIiwibXVsdGlwbGUiXSwic291cmNlUm9vdCI6IiJ9
//# sourceURL=webpack-internal:///25770
`)},19073:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   G3: function() { return /* binding */ sortArrayByObjectKey; },
/* harmony export */   Pr: function() { return /* binding */ createEmptyArray; }
/* harmony export */ });
/* unused harmony export getDuplicateInArrayObj */
/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(96486);
/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_0__);

var createEmptyArray = function createEmptyArray(length) {
  return Array.from(new Array(length)).map(function (__, index) {
    return index;
  });
};
var getDuplicateInArrayObj = function getDuplicateInArrayObj(params) {
  var duplicates = _(params.arr).groupBy(params.groupBy).filter(function (group) {
    return group.length > 1;
  }).flatten().value();
  return duplicates;
};
var sortArrayByObjectKey = function sortArrayByObjectKey(params) {
  // const sorted = _.orderBy(
  //   params.arr,
  //   Object.keys(params.sort),
  //   Object.values(params.sort).map((key) => (key === 'ascend' ? 'asc' : 'desc')),
  // );
  // return sorted;
  // sorter with localCompare
  return params.arr.sort(function (a, b) {
    for (var _key in params.sort) {
      if (Object.prototype.hasOwnProperty.call(params.sort, _key)) {
        var order = params.sort[_key];
        var aValue = a[_key] ? lodash__WEBPACK_IMPORTED_MODULE_0___default().toString(a[_key]) : '';
        var bValue = b[_key] ? lodash__WEBPACK_IMPORTED_MODULE_0___default().toString(b[_key]) : '';
        var localCompare = aValue.localeCompare(bValue);
        if (localCompare < 0) {
          return order === 'ascend' ? -1 : 1;
        }
        if (localCompare > 0) {
          return order === 'ascend' ? 1 : -1;
        }
      }
    }
    return 0;
  });
};//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///19073
`)}}]);
