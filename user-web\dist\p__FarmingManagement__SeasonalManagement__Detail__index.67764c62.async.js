"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[4727],{27076:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(5574);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(97857);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _utils_string__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(7369);
/* harmony import */ var _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(6110);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(7837);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(67294);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(85893);







var PageContainerTabsWithSearch = function PageContainerTabsWithSearch(_ref) {
  var tabsItems = _ref.tabsItems,
    _ref$searchParamsUrlK = _ref.searchParamsUrlKey,
    searchParamsUrlKey = _ref$searchParamsUrlK === void 0 ? 'tab' : _ref$searchParamsUrlK,
    _onTabChange = _ref.onTabChange,
    pageTitle = _ref.pageTitle;
  var tabsItemsFormat = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(function () {
    return tabsItems === null || tabsItems === void 0 ? void 0 : tabsItems.map(function (item) {
      return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({}, item), {}, {
        tabKey: item.tabKey || (0,_utils_string__WEBPACK_IMPORTED_MODULE_5__/* .nonAccentVietnamese */ .w)(item.label)
      });
    });
  }, [tabsItems]);
  var _useSearchParams = (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)(),
    _useSearchParams2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_0___default()(_useSearchParams, 2),
    searchParams = _useSearchParams2[0],
    setSearchParams = _useSearchParams2[1];
  var tabActive = searchParamsUrlKey ? searchParams.get(searchParamsUrlKey) : undefined;
  console.log('tabActive', tabActive);
  var setTabActive = searchParamsUrlKey ? function (tabActiveVal) {
    searchParams.set(searchParamsUrlKey, tabActiveVal.toString());
    setSearchParams(searchParams);
  } : undefined;
  var tabList = tabsItemsFormat === null || tabsItemsFormat === void 0 ? void 0 : tabsItemsFormat.map(function (item) {
    return {
      tab: item.label,
      key: item.tabKey,
      tabKey: item.tabKey
    };
  });
  var tabPageActive = searchParamsUrlKey ? (tabsItemsFormat === null || tabsItemsFormat === void 0 ? void 0 : tabsItemsFormat.find(function (item) {
    return item.tabKey === tabActive;
  })) || (tabsItemsFormat === null || tabsItemsFormat === void 0 ? void 0 : tabsItemsFormat[0]) : undefined;
  var ComponentActive = tabPageActive === null || tabPageActive === void 0 ? void 0 : tabPageActive.component;
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_6__/* .PageContainer */ ._z, {
    fixedHeader: true,
    extra: tabPageActive === null || tabPageActive === void 0 ? void 0 : tabPageActive.extraPage,
    tabActiveKey: tabPageActive === null || tabPageActive === void 0 ? void 0 : tabPageActive.tabKey,
    tabList: tabList,
    onTabChange: function onTabChange(tabActiveVal) {
      _onTabChange === null || _onTabChange === void 0 || _onTabChange(tabActiveVal);
      if (searchParamsUrlKey) setTabActive === null || setTabActive === void 0 || setTabActive(tabActiveVal);
    },
    title: pageTitle,
    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(react__WEBPACK_IMPORTED_MODULE_3__.Suspense, {
      fallback: (tabPageActive === null || tabPageActive === void 0 ? void 0 : tabPageActive.fallback) || null,
      children: ComponentActive && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(ComponentActive, {})
    })
  });
};
/* harmony default export */ __webpack_exports__.Z = (PageContainerTabsWithSearch);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///27076
`)},17429:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(5574);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(19632);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _common_contanst_constanst__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(86604);
/* harmony import */ var _services_cropManager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(77890);
/* harmony import */ var _services_fileUpload__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(96876);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(51042);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(9890);
/* harmony import */ var _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(34994);
/* harmony import */ var _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(37476);
/* harmony import */ var _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(5966);
/* harmony import */ var _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(64317);
/* harmony import */ var _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(90672);
/* harmony import */ var _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(77636);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(7837);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(31418);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(14726);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(67294);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(85893);















var CreateNoteModal = function CreateNoteModal(_ref) {
  var cropId = _ref.cropId,
    onSuccess = _ref.onSuccess,
    trigger = _ref.trigger;
  var _App$useApp = antd__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .Z.useApp(),
    message = _App$useApp.message;
  var onFinish = /*#__PURE__*/function () {
    var _ref2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee2(values) {
      var dataCreate, uploadListRes, checkUploadFailed, arrFileUrl;
      return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee2$(_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            _context2.prev = 0;
            _context2.next = 3;
            return (0,_services_cropManager__WEBPACK_IMPORTED_MODULE_5__/* .createCropNote */ .rC)({
              label: values.label,
              crop: values.crop,
              note: values.note
            });
          case 3:
            dataCreate = _context2.sent;
            _context2.prev = 4;
            if (!(values.img && (values.img || []).length > 0)) {
              _context2.next = 15;
              break;
            }
            _context2.next = 8;
            return Promise.allSettled(values.img.map( /*#__PURE__*/function () {
              var _ref3 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee(item) {
                return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee$(_context) {
                  while (1) switch (_context.prev = _context.next) {
                    case 0:
                      _context.next = 2;
                      return (0,_services_fileUpload__WEBPACK_IMPORTED_MODULE_6__/* .uploadFile */ .cT)({
                        docType: _common_contanst_constanst__WEBPACK_IMPORTED_MODULE_4__/* .DOCTYPE_ERP */ .lH.iotPest,
                        docName: dataCreate.data.name,
                        file: item.originFileObj
                      });
                    case 2:
                      return _context.abrupt("return", _context.sent);
                    case 3:
                    case "end":
                      return _context.stop();
                  }
                }, _callee);
              }));
              return function (_x2) {
                return _ref3.apply(this, arguments);
              };
            }()));
          case 8:
            uploadListRes = _context2.sent;
            // check if() 1 v\xE0i upload failed
            checkUploadFailed = uploadListRes.find(function (item) {
              return item.status === 'rejected';
            });
            if (checkUploadFailed) {
              message.error({
                content: 'Some file upload failed'
              });
            }

            // update img path
            arrFileUrl = uploadListRes.reduce(function (prev, item) {
              var _item$value;
              return item.status === 'fulfilled' ? [].concat(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_1___default()(prev), [item === null || item === void 0 || (_item$value = item.value) === null || _item$value === void 0 || (_item$value = _item$value.data) === null || _item$value === void 0 || (_item$value = _item$value.message) === null || _item$value === void 0 ? void 0 : _item$value.file_url]) : prev;
            }, []).filter(function (item) {
              return typeof item === 'string';
            });
            if (!(arrFileUrl.length > 0)) {
              _context2.next = 15;
              break;
            }
            _context2.next = 15;
            return (0,_services_cropManager__WEBPACK_IMPORTED_MODULE_5__/* .updateCropNote */ .bx)({
              name: dataCreate.data.name,
              crop: values.crop,
              image: arrFileUrl.join(',')
            });
          case 15:
            _context2.next = 19;
            break;
          case 17:
            _context2.prev = 17;
            _context2.t0 = _context2["catch"](4);
          case 19:
            message.success({
              content: 'Created successfully'
            });
            onSuccess === null || onSuccess === void 0 || onSuccess();
            return _context2.abrupt("return", true);
          case 24:
            _context2.prev = 24;
            _context2.t1 = _context2["catch"](0);
            message.error({
              content: 'Error, please try again'
            });
            return _context2.abrupt("return", false);
          case 28:
          case "end":
            return _context2.stop();
        }
      }, _callee2, null, [[0, 24], [4, 17]]);
    }));
    return function onFinish(_x) {
      return _ref2.apply(this, arguments);
    };
  }();
  var _ProForm$useForm = _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_11__/* .ProForm */ .A.useForm(),
    _ProForm$useForm2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_0___default()(_ProForm$useForm, 1),
    form = _ProForm$useForm2[0];
  (0,react__WEBPACK_IMPORTED_MODULE_8__.useEffect)(function () {
    // default select crop
    if (cropId) {
      form.setFieldsValue({
        crop: cropId
      });
    }
  }, [cropId]);
  var access = (0,_umijs_max__WEBPACK_IMPORTED_MODULE_7__.useAccess)();
  var canCreateCrop = access.canCreateInSeasonalManagement();
  if (canCreateCrop) {
    return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxs)(_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_12__/* .ModalForm */ .Y, {
      modalProps: {
        destroyOnClose: true
      },
      name: "crop:create-note",
      title: "T\\u1EA1o ghi ch\\xFA",
      trigger: trigger || /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(antd__WEBPACK_IMPORTED_MODULE_13__/* ["default"] */ .ZP, {
        type: "primary",
        icon: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .Z, {}),
        children: "Th\\xEAm ghi ch\\xFA"
      }),
      width: 500,
      form: form,
      onFinish: onFinish,
      initialValues: {
        crop: cropId
      },
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .Z, {
        label: "Ti\\xEAu \\u0111\\u1EC1",
        name: "label",
        rules: [{
          required: true
        }]
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .Z, {
        label: 'Ch\u1ECDn v\u1EE5 m\xF9a',
        name: "crop",
        hidden: true,
        rules: [{
          required: true
        }],
        request: /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee3() {
          var res;
          return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee3$(_context3) {
            while (1) switch (_context3.prev = _context3.next) {
              case 0:
                _context3.next = 2;
                return (0,_services_cropManager__WEBPACK_IMPORTED_MODULE_5__/* .getCropManagementInfoList */ .Gz)({
                  page: 1,
                  size: _common_contanst_constanst__WEBPACK_IMPORTED_MODULE_4__/* .DEFAULT_PAGE_SIZE_ALL */ .YY
                });
              case 2:
                res = _context3.sent;
                return _context3.abrupt("return", res.data.map(function (item) {
                  return {
                    label: item.label,
                    value: item.name
                  };
                }));
              case 4:
              case "end":
                return _context3.stop();
            }
          }, _callee3);
        }))
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_17__/* ["default"] */ .Z, {
        label: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_7__.FormattedMessage, {
          id: 'common.form.description'
        }),
        name: 'note'
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_18__/* ["default"] */ .Z, {
        label: "Upload",
        listType: "picture-card",
        name: "img",
        icon: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__/* ["default"] */ .Z, {}),
        title: "",
        accept: "image/*",
        fieldProps: {
          multiple: true
        }
        // rules={[
        //   {
        //     required: true,
        //   },
        // ]}
      })]
    });
  } else return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.Fragment, {});
};
/* harmony default export */ __webpack_exports__.Z = (CreateNoteModal);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///17429
`)},78189:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(5574);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(19632);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _common_contanst_constanst__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(86604);
/* harmony import */ var _services_cropManager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(77890);
/* harmony import */ var _services_farming_plan__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(74459);
/* harmony import */ var _services_fileUpload__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(96876);
/* harmony import */ var _services_pandemic__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(61986);
/* harmony import */ var _services_sscript__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(39750);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(51042);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(9890);
/* harmony import */ var _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(34994);
/* harmony import */ var _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(37476);
/* harmony import */ var _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(5966);
/* harmony import */ var _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(64317);
/* harmony import */ var _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(90672);
/* harmony import */ var _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(77636);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(7837);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(31418);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(14726);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(67294);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(85893);


















var CreatePandemicModal = function CreatePandemicModal(_ref) {
  var cropId = _ref.cropId,
    onSuccess = _ref.onSuccess,
    trigger = _ref.trigger;
  var _App$useApp = antd__WEBPACK_IMPORTED_MODULE_13__/* ["default"] */ .Z.useApp(),
    message = _App$useApp.message;
  var userdata = JSON.parse(localStorage.getItem('token') || '{}');
  var customerId = userdata === null || userdata === void 0 ? void 0 : userdata.user.customer_id;
  console.log('userdata', userdata);
  var onFinish = /*#__PURE__*/function () {
    var _ref2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee2(values) {
      var dataCreate, pestId, uploadListRes, checkUploadFailed, arrFileUrl, dataRelevant;
      return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee2$(_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            _context2.prev = 0;
            console.log('values', values);
            // create
            _context2.next = 4;
            return (0,_services_pandemic__WEBPACK_IMPORTED_MODULE_8__/* .createPest */ ._l)({
              label: values.label,
              iot_crop: values.iot_crop,
              description: values.description
            });
          case 4:
            dataCreate = _context2.sent;
            pestId = dataCreate.data.name;
            _context2.prev = 6;
            if (!(values.img && values.img.length > 5)) {
              _context2.next = 10;
              break;
            }
            message.error('Ch\u1EC9 \u0111\u01B0\u1EE3c upload t\u1ED1i \u0111a 5 \u1EA3nh');
            return _context2.abrupt("return");
          case 10:
            if (!(values.img && (values.img || []).length > 0)) {
              _context2.next = 20;
              break;
            }
            _context2.next = 13;
            return Promise.allSettled(values.img.map( /*#__PURE__*/function () {
              var _ref3 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee(item) {
                return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee$(_context) {
                  while (1) switch (_context.prev = _context.next) {
                    case 0:
                      _context.next = 2;
                      return (0,_services_fileUpload__WEBPACK_IMPORTED_MODULE_7__/* .uploadFile */ .cT)({
                        docType: _common_contanst_constanst__WEBPACK_IMPORTED_MODULE_4__/* .DOCTYPE_ERP */ .lH.iotPest,
                        docName: pestId,
                        file: item.originFileObj
                      });
                    case 2:
                      return _context.abrupt("return", _context.sent);
                    case 3:
                    case "end":
                      return _context.stop();
                  }
                }, _callee);
              }));
              return function (_x2) {
                return _ref3.apply(this, arguments);
              };
            }()));
          case 13:
            uploadListRes = _context2.sent;
            // check if() 1 v\xE0i upload failed
            checkUploadFailed = uploadListRes.find(function (item) {
              return item.status === 'rejected';
            });
            if (checkUploadFailed) {
              message.error({
                content: 'Some file upload failed'
              });
            }

            // update img path
            arrFileUrl = uploadListRes.reduce(function (prev, item) {
              var _item$value;
              return item.status === 'fulfilled' ? [].concat(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_1___default()(prev), [item === null || item === void 0 || (_item$value = item.value) === null || _item$value === void 0 || (_item$value = _item$value.data) === null || _item$value === void 0 || (_item$value = _item$value.message) === null || _item$value === void 0 ? void 0 : _item$value.file_url]) : prev;
            }, []).filter(function (item) {
              return typeof item === 'string';
            });
            if (!(arrFileUrl.length > 0)) {
              _context2.next = 20;
              break;
            }
            _context2.next = 20;
            return (0,_services_pandemic__WEBPACK_IMPORTED_MODULE_8__/* .updatePest */ .EL)({
              name: pestId,
              iot_crop: values.iot_crop,
              image: arrFileUrl.join(',')
            });
          case 20:
            _context2.next = 24;
            break;
          case 22:
            _context2.prev = 22;
            _context2.t0 = _context2["catch"](6);
          case 24:
            //create multiple relevant
            dataRelevant = {
              name: pestId,
              category_list: values.category_list ? values.category_list : [],
              state_list: values.state_list ? values.state_list : [],
              involved_in_users: values.involved_in_users ? values.involved_in_users : []
            };
            _context2.next = 27;
            return (0,_services_pandemic__WEBPACK_IMPORTED_MODULE_8__/* .createPestMultipleRelevant */ .Xk)(dataRelevant);
          case 27:
            message.success({
              content: 'Created successfully'
            });
            onSuccess === null || onSuccess === void 0 || onSuccess();
            return _context2.abrupt("return", true);
          case 32:
            _context2.prev = 32;
            _context2.t1 = _context2["catch"](0);
            message.error({
              content: 'Error, please try again'
            });
            return _context2.abrupt("return", false);
          case 36:
          case "end":
            return _context2.stop();
        }
      }, _callee2, null, [[0, 32], [6, 22]]);
    }));
    return function onFinish(_x) {
      return _ref2.apply(this, arguments);
    };
  }();
  var _ProForm$useForm = _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_14__/* .ProForm */ .A.useForm(),
    _ProForm$useForm2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_0___default()(_ProForm$useForm, 1),
    form = _ProForm$useForm2[0];
  (0,react__WEBPACK_IMPORTED_MODULE_11__.useEffect)(function () {
    // default select crop
    if (cropId) {
      form.setFieldsValue({
        iot_crop: cropId
      });
    }
  }, [cropId]);
  var access = (0,_umijs_max__WEBPACK_IMPORTED_MODULE_10__.useAccess)();
  var canCreateCrop = access.canCreateInSeasonalManagement();
  var intl = (0,_umijs_max__WEBPACK_IMPORTED_MODULE_10__.useIntl)();
  if (canCreateCrop) {
    return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__.jsxs)(_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_15__/* .ModalForm */ .Y, {
      modalProps: {
        destroyOnClose: true
      },
      name: "create-pandemic",
      title: intl.formatMessage({
        id: 'common.create-pest'
      }),
      trigger: trigger || /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__.jsx)(antd__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .ZP, {
        type: "primary",
        icon: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__/* ["default"] */ .Z, {}),
        children: intl.formatMessage({
          id: 'common.add-health-info'
        })
      }),
      width: 500,
      form: form,
      onFinish: onFinish,
      initialValues: {
        iot_crop: cropId
      },
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__.jsx)(_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_18__/* ["default"] */ .Z, {
        label: intl.formatMessage({
          id: 'common.pest_name'
        }),
        name: "label",
        rules: [{
          required: true
        }]
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__.jsx)(_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_19__/* ["default"] */ .Z, {
        label: 'Ch\u1ECDn v\u1EE5 m\xF9a',
        name: "iot_crop",
        hidden: true,
        rules: [{
          required: true
        }],
        request: /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee3() {
          var res;
          return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee3$(_context3) {
            while (1) switch (_context3.prev = _context3.next) {
              case 0:
                _context3.next = 2;
                return (0,_services_cropManager__WEBPACK_IMPORTED_MODULE_5__/* .getCropManagementInfoList */ .Gz)({
                  page: 1,
                  size: _common_contanst_constanst__WEBPACK_IMPORTED_MODULE_4__/* .DEFAULT_PAGE_SIZE_ALL */ .YY
                });
              case 2:
                res = _context3.sent;
                return _context3.abrupt("return", res.data.map(function (item) {
                  return {
                    label: item.label,
                    value: item.name
                  };
                }));
              case 4:
              case "end":
                return _context3.stop();
            }
          }, _callee3);
        }))
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__.jsx)(_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_19__/* ["default"] */ .Z, {
        label: intl.formatMessage({
          id: 'common.select-category'
        }),
        name: "category_list",
        mode: "multiple",
        showSearch: true
        // rules={[
        //   {
        //     required: true,
        //   },
        // ]}
        ,
        request: /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee4() {
          var res;
          return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee4$(_context4) {
            while (1) switch (_context4.prev = _context4.next) {
              case 0:
                _context4.next = 2;
                return (0,_services_sscript__WEBPACK_IMPORTED_MODULE_9__/* .sscriptGeneralList */ .RB)({
                  doc_name: 'iot_category',
                  page: 1,
                  size: _common_contanst_constanst__WEBPACK_IMPORTED_MODULE_4__/* .DEFAULT_PAGE_SIZE_ALL */ .YY,
                  filters: [['iot_category', 'customer_id', 'like', customerId]]
                });
              case 2:
                res = _context4.sent;
                return _context4.abrupt("return", res.data.map(function (item) {
                  return {
                    label: item.label,
                    value: item.name
                  };
                }));
              case 4:
              case "end":
                return _context4.stop();
            }
          }, _callee4);
        }))
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__.jsx)(_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_19__/* ["default"] */ .Z, {
        label: intl.formatMessage({
          id: 'common.select_state'
        }),
        name: "state_list",
        mode: "multiple",
        showSearch: true
        // rules={[
        //   {
        //     required: true,
        //   },
        // ]}
        ,
        request: /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee5() {
          var res;
          return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee5$(_context5) {
            while (1) switch (_context5.prev = _context5.next) {
              case 0:
                _context5.next = 2;
                return (0,_services_farming_plan__WEBPACK_IMPORTED_MODULE_6__/* .getFarmingPlanState */ .jY)({
                  page: 1,
                  size: _common_contanst_constanst__WEBPACK_IMPORTED_MODULE_4__/* .DEFAULT_PAGE_SIZE_ALL */ .YY,
                  filters: [['iot_farming_plan_state', 'crop', 'like', cropId]]
                });
              case 2:
                res = _context5.sent;
                return _context5.abrupt("return", res.data.map(function (item) {
                  return {
                    label: item.label,
                    value: item.name
                  };
                }));
              case 4:
              case "end":
                return _context5.stop();
            }
          }, _callee5);
        }))
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__.jsx)(_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_19__/* ["default"] */ .Z, {
        label: intl.formatMessage({
          id: 'common.people_involved'
        }),
        name: "involved_in_users",
        showSearch: true,
        mode: "multiple"
        // rules={[
        //   {
        //     required: true,
        //   },
        // ]}
        ,
        request: /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee6() {
          var res;
          return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee6$(_context6) {
            while (1) switch (_context6.prev = _context6.next) {
              case 0:
                _context6.next = 2;
                return (0,_services_sscript__WEBPACK_IMPORTED_MODULE_9__/* .sscriptGeneralList */ .RB)({
                  doc_name: 'iot_customer_user',
                  page: 1,
                  size: _common_contanst_constanst__WEBPACK_IMPORTED_MODULE_4__/* .DEFAULT_PAGE_SIZE_ALL */ .YY,
                  filters: [['iot_customer_user', 'customer_id', 'like', customerId]]
                });
              case 2:
                res = _context6.sent;
                return _context6.abrupt("return", res.data.map(function (item) {
                  return {
                    label: "".concat(item.last_name, " ").concat(item.first_name),
                    value: item.name
                  };
                }));
              case 4:
              case "end":
                return _context6.stop();
            }
          }, _callee6);
        }))
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__.jsx)(_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_20__/* ["default"] */ .Z, {
        label: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_10__.FormattedMessage, {
          id: 'common.form.description'
        }),
        name: 'description'
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__.jsx)(_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_21__/* ["default"] */ .Z, {
        label: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_10__.FormattedMessage, {
          id: 'common.form.image-limit'
        }),
        listType: "picture-card",
        name: "img",
        icon: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__/* ["default"] */ .Z, {}),
        title: "",
        accept: "image/*",
        fieldProps: {
          multiple: true,
          maxCount: 5
        }
        // rules={[
        //   {
        //     required: true,
        //   },
        // ]}
      })]
    });
  } else return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__.Fragment, {});
};
/* harmony default export */ __webpack_exports__.Z = (CreatePandemicModal);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///78189
`)},72648:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`__webpack_require__.r(__webpack_exports__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(5574);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _services_ticket__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(1743);
/* harmony import */ var _stores_TicketStore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(33217);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(7837);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(96365);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(14726);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(2487);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(7134);
/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(27484);
/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(moment__WEBPACK_IMPORTED_MODULE_6__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(67294);
/* harmony import */ var react_icons_io5__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(72370);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(85893);












var TextArea = antd__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Z.TextArea;
var TicketMessages = function TicketMessages(_ref) {
  var ticket_id = _ref.ticket_id,
    crop_id = _ref.crop_id,
    messages = _ref.messages,
    onBack = _ref.onBack;
  console.log('ticket_id', ticket_id);
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(''),
    _useState2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2___default()(_useState, 2),
    messageContent = _useState2[0],
    setMessageContent = _useState2[1]; // Add this line
  var _useTicketListStore = (0,_stores_TicketStore__WEBPACK_IMPORTED_MODULE_4__/* .useTicketListStore */ .F)(),
    fetchTickets = _useTicketListStore.fetchTickets,
    getTicketMessages = _useTicketListStore.getTicketMessages;
  var messagesFromStore = getTicketMessages(ticket_id);
  var handleSendMessage = /*#__PURE__*/function () {
    var _ref2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee() {
      var createMessage;
      return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            // Call your API to send the message here
            console.log('Sending message:', messageContent);
            _context.next = 3;
            return (0,_services_ticket__WEBPACK_IMPORTED_MODULE_3__/* .createTicketMessage */ .Tw)({
              ticket_id: ticket_id,
              content: messageContent
            });
          case 3:
            createMessage = _context.sent;
            // Add this line
            console.log('createMessage', createMessage);
            setMessageContent(''); // Clear the message input
            fetchTickets(crop_id);
          case 7:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }));
    return function handleSendMessage() {
      return _ref2.apply(this, arguments);
    };
  }();
  var handleKeyDown = function handleKeyDown(event) {
    if (event.key === 'Enter' && event.ctrlKey) {
      // If the user pressed Ctrl + Enter, add a new line
      event.stopPropagation(); // Prevent the event from triggering onPressEnter
      setMessageContent(function (prevContent) {
        return prevContent + '\\n';
      });
    }
  };
  var handlePressEnter = function handlePressEnter(event) {
    if (!event.ctrlKey) {
      // If the user pressed Enter without pressing Ctrl, send the message
      event.preventDefault(); // Prevent the default action (new line)
      handleSendMessage();
    }
  };
  var intl = (0,_umijs_max__WEBPACK_IMPORTED_MODULE_5__.useIntl)();
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxs)("div", {
    className: "flex flex-col h-full",
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsx)(antd__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .ZP, {
      onClick: onBack,
      children: intl.formatMessage({
        id: 'common.back_to_tickets'
      })
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsx)(antd__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .Z, {
      className: "overflow-auto",
      itemLayout: "horizontal",
      dataSource: messagesFromStore,
      renderItem: function renderItem(message) {
        return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsx)(antd__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .Z.Item, {
          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsx)(antd__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .Z.Item.Meta, {
            avatar: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsx)(antd__WEBPACK_IMPORTED_MODULE_12__/* ["default"] */ .C, {
              src: ""
            }),
            title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxs)("div", {
              children: ["".concat(message.sender_first_name, " ").concat(message.sender_last_name), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsx)("small", {
                className: "text-gray-400 px-3",
                children: moment__WEBPACK_IMPORTED_MODULE_6___default()(message.creation).format('HH:mm DD/MM/YYYY')
              })]
            }),
            description: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxs)("div", {
              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsx)("p", {
                className: "text-gray-600 px-3",
                style: {
                  whiteSpace: 'pre-wrap'
                },
                children: message.content
              }), ' ']
            })
          })
        });
      }
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxs)("div", {
      className: "flex items-center p-0",
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsx)(TextArea, {
        rows: 4,
        className: "flex-1",
        placeholder: intl.formatMessage({
          id: 'common.send_message'
        }),
        value: messageContent // Set the value of the input
        ,
        onChange: function onChange(e) {
          return setMessageContent(e.target.value);
        } // Update the state when the input changes
        ,
        onKeyDown: handleKeyDown,
        onPressEnter: handlePressEnter
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsx)(antd__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .ZP, {
        className: "ml-3 text-primary-green",
        onClick: handleSendMessage,
        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsx)(react_icons_io5__WEBPACK_IMPORTED_MODULE_13__/* .IoSend */ .yhK, {})
      })]
    })]
  });
};
/* harmony default export */ __webpack_exports__["default"] = (TicketMessages);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///72648
`)},97738:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return /* binding */ SeasonalManagement_Detail; }
});

// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/slicedToArray.js
var slicedToArray = __webpack_require__(5574);
var slicedToArray_default = /*#__PURE__*/__webpack_require__.n(slicedToArray);
// EXTERNAL MODULE: ./src/utils/lazy.tsx
var lazy = __webpack_require__(48576);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-skeleton/es/components/Descriptions/index.js
var Descriptions = __webpack_require__(44688);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-skeleton/es/components/List/index.js
var List = __webpack_require__(56517);
// EXTERNAL MODULE: ./node_modules/antd/es/card/index.js + 3 modules
var card = __webpack_require__(76216);
// EXTERNAL MODULE: ./node_modules/antd/es/tabs/index.js + 30 modules
var tabs = __webpack_require__(48096);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./src/components/FallbackContent/index.tsx
var FallbackContent = __webpack_require__(65573);
// EXTERNAL MODULE: ./src/components/PageContainerTabsWithSearch/index.tsx
var PageContainerTabsWithSearch = __webpack_require__(27076);
// EXTERNAL MODULE: ./src/components/TodayTasks/index.tsx
var TodayTasks = __webpack_require__(7976);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/defineProperty.js
var defineProperty = __webpack_require__(9783);
var defineProperty_default = /*#__PURE__*/__webpack_require__.n(defineProperty);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/regeneratorRuntime.js
var regeneratorRuntime = __webpack_require__(15009);
var regeneratorRuntime_default = /*#__PURE__*/__webpack_require__.n(regeneratorRuntime);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(99289);
var asyncToGenerator_default = /*#__PURE__*/__webpack_require__.n(asyncToGenerator);
// EXTERNAL MODULE: ./src/assets/img/icons/tree-green.svg
var tree_green = __webpack_require__(43032);
// EXTERNAL MODULE: ./src/common/contanst/constanst.ts
var constanst = __webpack_require__(86604);
// EXTERNAL MODULE: ./src/services/farming-plan.ts
var farming_plan = __webpack_require__(74459);
// EXTERNAL MODULE: ./src/services/sscript.ts
var sscript = __webpack_require__(39750);
// EXTERNAL MODULE: ./src/services/utils.ts
var utils = __webpack_require__(467);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/layouts/ProForm/index.js + 3 modules
var ProForm = __webpack_require__(34994);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-list/es/index.js + 20 modules
var es = __webpack_require__(64176);
// EXTERNAL MODULE: ./src/.umi-production/exports.ts + 34 modules
var _umi_production_exports = __webpack_require__(7837);
// EXTERNAL MODULE: ./node_modules/antd/es/tag/index.js + 5 modules
var tag = __webpack_require__(66309);
// EXTERNAL MODULE: ./node_modules/antd/es/space/index.js + 2 modules
var space = __webpack_require__(42075);
// EXTERNAL MODULE: ./node_modules/antd/es/avatar/index.js + 4 modules
var avatar = __webpack_require__(7134);
// EXTERNAL MODULE: ./node_modules/antd/es/row/index.js
var row = __webpack_require__(71230);
// EXTERNAL MODULE: ./node_modules/antd/es/col/index.js
var col = __webpack_require__(15746);
// EXTERNAL MODULE: ./node_modules/antd/es/typography/index.js + 18 modules
var typography = __webpack_require__(25514);
// EXTERNAL MODULE: ./node_modules/antd/es/select/index.js + 30 modules
var es_select = __webpack_require__(83863);
// EXTERNAL MODULE: ./node_modules/antd/es/message/index.js
var message = __webpack_require__(45360);
// EXTERNAL MODULE: ./node_modules/antd-use-styles/dist/antd-use-styles.js
var antd_use_styles = __webpack_require__(38513);
// EXTERNAL MODULE: ./node_modules/dayjs/dayjs.min.js
var dayjs_min = __webpack_require__(27484);
var dayjs_min_default = /*#__PURE__*/__webpack_require__.n(dayjs_min);
// EXTERNAL MODULE: ./src/components/Task/TaskTodo/TaskTodoTableEditer.tsx + 3 modules
var TaskTodoTableEditer = __webpack_require__(10051);
// EXTERNAL MODULE: ./node_modules/react/jsx-runtime.js
var jsx_runtime = __webpack_require__(85893);
;// CONCATENATED MODULE: ./src/components/TodayTemplateTasks/index.tsx



















var useStyles = (0,antd_use_styles/* createStyles */.k)(function () {
  return {
    table: {
      '& .ant-pro-table-list-toolbar-left': {
        flex: 'none'
      }
    }
  };
});
var dateRangeFilterKey = 'dateRange';
var getStatus = function getStatus(key) {
  switch (key) {
    case 'Plan':
      return /*#__PURE__*/(0,jsx_runtime.jsx)(tag/* default */.Z, {
        color: "success",
        children: "K\\u1EBF ho\\u1EA1ch"
      });
    case 'Done':
      return /*#__PURE__*/(0,jsx_runtime.jsx)(tag/* default */.Z, {
        color: "success",
        children: "\\u0110\\xE3 ho\\xE0n th\\xE0nh"
      });
    case 'In progress':
      return /*#__PURE__*/(0,jsx_runtime.jsx)(tag/* default */.Z, {
        color: "blue",
        children: "\\u0110ang th\\u1EF1c hi\\u1EC7n"
      });
    case 'Pending':
      return /*#__PURE__*/(0,jsx_runtime.jsx)(tag/* default */.Z, {
        color: "warning",
        children: "Tr\\xEC ho\\xE3n"
      });
    default:
      return /*#__PURE__*/(0,jsx_runtime.jsx)(tag/* default */.Z, {
        children: key
      });
  }
};
var TodayTemplateTask = function TodayTemplateTask(_ref) {
  var cropId = _ref.cropId;
  var styles = useStyles();
  var intl = (0,_umi_production_exports.useIntl)();
  // const [searchParams, setSearchParams] = useSearchParams();
  // const farmingPlanState = searchParams.get('pl_state_name');
  // const dateRangeFilter = searchParams.get(dateRangeFilterKey);
  var _ProForm$useForm = ProForm/* ProForm */.A.useForm(),
    _ProForm$useForm2 = slicedToArray_default()(_ProForm$useForm, 1),
    formFilter = _ProForm$useForm2[0];
  var _useState = (0,react.useState)([]),
    _useState2 = slicedToArray_default()(_useState, 2),
    dataSource = _useState2[0],
    setDataSource = _useState2[1];
  var _useState3 = (0,react.useState)(false),
    _useState4 = slicedToArray_default()(_useState3, 2),
    loading = _useState4[0],
    setLoading = _useState4[1];
  var _useState5 = (0,react.useState)([]),
    _useState6 = slicedToArray_default()(_useState5, 2),
    expandedRowKeys = _useState6[0],
    setExpandedRowKeys = _useState6[1];
  var _useState7 = (0,react.useState)([]),
    _useState8 = slicedToArray_default()(_useState7, 2),
    selectedRowKeys = _useState8[0],
    setSelectedRowKeys = _useState8[1];
  var rowSelection = {
    selectedRowKeys: selectedRowKeys,
    onChange: function onChange(keys) {
      return setSelectedRowKeys(keys);
    }
  };
  var actionRef = (0,react.useRef)();
  // reload when form filter change
  // useEffect(() => {
  //   if (actionRef.current) {
  //     actionRef.current.reload();
  //   }
  // }, [dateRangeFilter]);

  return /*#__PURE__*/(0,jsx_runtime.jsx)(jsx_runtime.Fragment, {
    children: /*#__PURE__*/(0,jsx_runtime.jsx)(es/* ProList */.Rs, {
      actionRef: actionRef,
      request: ( /*#__PURE__*/function () {
        var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(params) {
          var today, filters, res;
          return regeneratorRuntime_default()().wrap(function _callee$(_context) {
            while (1) switch (_context.prev = _context.next) {
              case 0:
                // const today = moment().toISOString();
                // const startTime  = dayjs().startOf('day').toISOString();
                // const endTime = dayjs().endOf('day').toISOString();
                today = dayjs_min_default()().format('YYYY-MM-DD'); // const startDate = moment().startOf('date').toISOString();
                // const endDate = moment().endOf('date').toISOString();
                filters = [[constanst/* DOCTYPE_ERP */.lH.iotFarmingPlanTask, 'end_date', '>=', today], [constanst/* DOCTYPE_ERP */.lH.iotFarmingPlanTask, 'start_date', '<=', today]];
                if (cropId) {
                  filters.push([constanst/* DOCTYPE_ERP */.lH.iotCrop, 'name', 'like', cropId]);
                }
                _context.next = 5;
                return (0,farming_plan/* getTemplateTaskManagerList */.dQ)({
                  page: params.current,
                  size: params.pageSize,
                  filters: filters,
                  or_filters: []
                });
              case 5:
                res = _context.sent;
                return _context.abrupt("return", {
                  data: res.data,
                  total: res.pagination.totalElements
                });
              case 7:
              case "end":
                return _context.stop();
            }
          }, _callee);
        }));
        return function (_x) {
          return _ref2.apply(this, arguments);
        };
      }()),
      metas: {
        title: defineProperty_default()({
          dataIndex: 'label',
          search: false,
          render: function render(dom, entity) {
            return /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.Link, {
              target: "_blank",
              to: '/farming-management/workflow-management/detail/' + entity.name,
              children: dom
            });
          }
        }, "search", false),
        subTitle: {
          dataIndex: 'status',
          render: function render(dom, entity) {
            return /*#__PURE__*/(0,jsx_runtime.jsxs)(space/* default */.Z, {
              size: 0,
              children: [getStatus(dom), /*#__PURE__*/(0,jsx_runtime.jsxs)(tag/* default */.Z, {
                color: "blue",
                children: [' ', intl.formatMessage({
                  id: 'workflowTab.executionTime'
                }), ' ', dayjs_min_default()(entity.start_date).format('HH:mm DD/MM/YYYY'), ' ', intl.formatMessage({
                  id: 'common.to'
                }), ' ', dayjs_min_default()(entity.end_date).format('HH:mm DD/MM/YYYY')]
              })]
            });
          },
          search: false
        },
        type: {},
        description: {
          render: function render(value, record) {
            var _record$assigned_to_i;
            var info = (_record$assigned_to_i = record.assigned_to_info) === null || _record$assigned_to_i === void 0 ? void 0 : _record$assigned_to_i[0];
            if (!info) {
              return null;
            }
            var involveInArr = record.involve_in_users || [];
            var userNames = (involveInArr === null || involveInArr === void 0 ? void 0 : involveInArr.map(function (data) {
              return "".concat(data.last_name, " ").concat(data.first_name, " ");
            })) || [];
            return /*#__PURE__*/(0,jsx_runtime.jsxs)(space/* default */.Z, {
              children: [info.user_avatar && /*#__PURE__*/(0,jsx_runtime.jsx)(avatar/* default */.C, {
                size: 'small',
                src: (0,utils/* getFileUrlV2 */.mT)({
                  src: info.user_avatar
                })
              }), /*#__PURE__*/(0,jsx_runtime.jsx)(tag/* default */.Z, {
                children: /*#__PURE__*/(0,jsx_runtime.jsxs)("span", {
                  children: ["Ng\\u01B0\\u1EDDi th\\u1EF1c hi\\u1EC7n: ", info.last_name || '', " ", "".concat(info.first_name || '', " ")]
                })
              }), /*#__PURE__*/(0,jsx_runtime.jsx)(tag/* default */.Z, {
                children: /*#__PURE__*/(0,jsx_runtime.jsxs)("span", {
                  children: ["Ng\\u01B0\\u1EDDi li\\xEAn quan: ", userNames.join(', ')]
                })
              })]
            });
          }
        },
        avatar: {
          render: function render() {
            return /*#__PURE__*/(0,jsx_runtime.jsx)(jsx_runtime.Fragment, {
              children: /*#__PURE__*/(0,jsx_runtime.jsx)("img", {
                src: tree_green/* default */.Z
              })
            });
          }
        },
        content: {
          render: function render(dom, entity) {
            return /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
              gutter: [20, 20],
              children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
                md: 18,
                children: /*#__PURE__*/(0,jsx_runtime.jsx)(TaskTodoTableEditer/* default */.Z, {
                  task_id: entity.name,
                  showToolbar: false
                })
              }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
                md: 6,
                children: /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
                  gutter: [10, 10],
                  children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
                    md: 24,
                    children: /*#__PURE__*/(0,jsx_runtime.jsxs)(typography/* default */.Z.Text, {
                      children: ["M\\xF9a v\\u1EE5: ", entity.crop_name]
                    })
                  }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
                    md: 24,
                    children: /*#__PURE__*/(0,jsx_runtime.jsxs)(typography/* default */.Z.Text, {
                      children: ["Giai \\u0111o\\u1EA1n: ", entity.state_name]
                    })
                  }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
                    md: 24,
                    children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_select/* default */.Z, {
                      style: {
                        width: '100%'
                      },
                      options: [{
                        label: 'L\xEAn k\u1EBF ho\u1EA1ch',
                        value: 'Plan'
                      }, {
                        label: '\u0110ang th\u1EF1c hi\u1EC7n',
                        value: 'In progress'
                      }, {
                        label: 'Ho\xE0n t\u1EA5t',
                        value: 'Done'
                      }, {
                        label: 'Tr\xEC ho\xE3n',
                        value: 'Pending'
                      }],
                      onChange: ( /*#__PURE__*/function () {
                        var _ref3 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2(v) {
                          var _actionRef$current, _error$toString;
                          return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
                            while (1) switch (_context2.prev = _context2.next) {
                              case 0:
                                _context2.prev = 0;
                                _context2.next = 3;
                                return (0,sscript/* generalUpdate */.I6)('iot_farming_plan_task', entity.name, {
                                  data: {
                                    name: entity.name,
                                    status: v
                                  }
                                });
                              case 3:
                                (_actionRef$current = actionRef.current) === null || _actionRef$current === void 0 || _actionRef$current.reload();
                                _context2.next = 9;
                                break;
                              case 6:
                                _context2.prev = 6;
                                _context2.t0 = _context2["catch"](0);
                                message/* default */.ZP.error(_context2.t0 === null || _context2.t0 === void 0 || (_error$toString = _context2.t0.toString) === null || _error$toString === void 0 ? void 0 : _error$toString.call(_context2.t0));
                              case 9:
                              case "end":
                                return _context2.stop();
                            }
                          }, _callee2, null, [[0, 6]]);
                        }));
                        return function (_x2) {
                          return _ref3.apply(this, arguments);
                        };
                      }()),
                      value: entity.status
                    })
                  }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
                    md: 24,
                    children: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.Link, {
                      target: "_blank",
                      to: '/farming-management/workflow-management/detail/' + entity.name,
                      children: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
                        id: "workflowTab.detail"
                      })
                    })
                  })]
                })
              })]
            });
          }
        }
      },
      rowKey: "name",
      headerTitle: intl.formatMessage({
        id: 'workflowTab.workList'
      }),
      itemLayout: "vertical",
      pagination: {}
    })
  });
};
/* harmony default export */ var TodayTemplateTasks = (TodayTemplateTask);
// EXTERNAL MODULE: ./src/services/crop.ts
var crop = __webpack_require__(52662);
// EXTERNAL MODULE: ./node_modules/nanoid/index.browser.js
var index_browser = __webpack_require__(53416);
// EXTERNAL MODULE: ./src/pages/FarmingManagement/CropPlan/components/TableCropPlanDetails.tsx
var TableCropPlanDetails = __webpack_require__(96409);
// EXTERNAL MODULE: ./src/pages/FarmingManagement/CropPlan/components/TaskTemplateTable.tsx
var TaskTemplateTable = __webpack_require__(59629);
// EXTERNAL MODULE: ./src/pages/FarmingManagement/CropPlan/Detail/index.tsx + 3 modules
var Detail = __webpack_require__(39917);
// EXTERNAL MODULE: ./node_modules/antd/es/divider/index.js + 1 modules
var divider = __webpack_require__(96074);
// EXTERNAL MODULE: ./node_modules/react-router/index.js
var react_router = __webpack_require__(96974);
// EXTERNAL MODULE: ./src/pages/FarmingManagement/CropPlan/CreateState/index.tsx
var CreateState = __webpack_require__(24695);
// EXTERNAL MODULE: ./src/common/contanst/img.ts
var img = __webpack_require__(13490);
// EXTERNAL MODULE: ./src/utils/date.ts
var date = __webpack_require__(28382);
// EXTERNAL MODULE: ./src/utils/file.ts
var file = __webpack_require__(80320);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/DeleteOutlined.js
var DeleteOutlined = __webpack_require__(82061);
// EXTERNAL MODULE: ./node_modules/antd/es/app/index.js + 1 modules
var app = __webpack_require__(31418);
// EXTERNAL MODULE: ./node_modules/antd/es/skeleton/index.js + 12 modules
var skeleton = __webpack_require__(99559);
// EXTERNAL MODULE: ./node_modules/antd/es/button/index.js + 9 modules
var es_button = __webpack_require__(14726);
// EXTERNAL MODULE: ./node_modules/antd/es/descriptions/index.js + 8 modules
var descriptions = __webpack_require__(26412);
// EXTERNAL MODULE: ./node_modules/antd/es/card/Meta.js
var Meta = __webpack_require__(46256);
// EXTERNAL MODULE: ./src/pages/FarmingManagement/CropPlan/Edit/index.tsx
var Edit = __webpack_require__(60962);
;// CONCATENATED MODULE: ./src/pages/FarmingManagement/CropPlan/TemplateDetail/components/DetailCard.tsx
















var DetailCard = function DetailCard(_ref) {
  var planId = _ref.planId;
  var _useState = (0,react.useState)(false),
    _useState2 = slicedToArray_default()(_useState, 2),
    reRender = _useState2[0],
    setReRender = _useState2[1];
  var _useRequest = (0,_umi_production_exports.useRequest)( /*#__PURE__*/asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
      var response;
      return regeneratorRuntime_default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _context.next = 2;
            return (0,farming_plan/* getFarmingPlan */.j1)(planId);
          case 2:
            response = _context.sent;
            return _context.abrupt("return", response);
          case 4:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }))),
    run = _useRequest.run,
    data = _useRequest.data,
    loading = _useRequest.loading,
    refresh = _useRequest.refresh;
  (0,react.useEffect)(function () {
    var fetchData = function fetchData() {
      if (planId) run();
    };
    fetchData();
  }, [planId]);
  var intl = (0,_umi_production_exports.useIntl)();
  var _App$useApp = app/* default */.Z.useApp(),
    modal = _App$useApp.modal,
    message = _App$useApp.message;
  var onDeletePlan = function onDeletePlan() {
    modal.confirm({
      content: 'Are you sure you want to delete this plan?',
      onOk: function () {
        var _onOk = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2() {
          return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
            while (1) switch (_context2.prev = _context2.next) {
              case 0:
                _context2.prev = 0;
                _context2.next = 3;
                return (0,farming_plan/* updateFarmingPlan */.al)({
                  name: planId,
                  is_deleted: 1
                });
              case 3:
                message.success({
                  content: 'Delete  successfully'
                });
                _umi_production_exports.history.push("/farming-management/crop-management-plan");

                // await refresh();
                return _context2.abrupt("return", true);
              case 8:
                _context2.prev = 8;
                _context2.t0 = _context2["catch"](0);
                message.error({
                  content: 'Delete error, please try again'
                });
                return _context2.abrupt("return", false);
              case 12:
              case "end":
                return _context2.stop();
            }
          }, _callee2, null, [[0, 8]]);
        }));
        function onOk() {
          return _onOk.apply(this, arguments);
        }
        return onOk;
      }(),
      okButtonProps: {
        danger: true
      }
    });
  };
  var access = (0,_umi_production_exports.useAccess)();
  var canDeletePlan = access.canDeleteInPlanManagement();
  var canUpdatePlan = access.canUpdateInPlanManagement();
  return /*#__PURE__*/(0,jsx_runtime.jsx)(skeleton/* default */.Z, {
    loading: loading,
    children: /*#__PURE__*/(0,jsx_runtime.jsx)(card/* default */.Z, {
      title: data === null || data === void 0 ? void 0 : data.label,
      extra: /*#__PURE__*/(0,jsx_runtime.jsxs)(space/* default */.Z, {
        children: [canUpdatePlan && /*#__PURE__*/(0,jsx_runtime.jsx)(Edit/* default */.Z, {
          planId: planId,
          onSuccess: refresh
        }, 'edit'), canDeletePlan && /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
          size: "middle",
          icon: /*#__PURE__*/(0,jsx_runtime.jsx)(DeleteOutlined/* default */.Z, {}),
          danger: true,
          onClick: onDeletePlan,
          children: intl.formatMessage({
            id: 'common.delete'
          })
        }, 'delete')]
      }),
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(Meta/* default */.Z, {
        avatar: /*#__PURE__*/(0,jsx_runtime.jsx)(avatar/* default */.C, {
          shape: "square",
          size: 64,
          src: data !== null && data !== void 0 && data.image ? (0,file/* genDownloadUrl */.h)(data === null || data === void 0 ? void 0 : data.image) : img/* DEFAULT_FALLBACK_IMG */.W
        }),
        description: /*#__PURE__*/(0,jsx_runtime.jsxs)(descriptions/* default */.Z, {
          column: 1,
          children: [/*#__PURE__*/(0,jsx_runtime.jsx)(descriptions/* default */.Z.Item, {
            label: intl.formatMessage({
              id: 'common.crop'
            }),
            children: "".concat(data === null || data === void 0 ? void 0 : data.crop_name)
          }), /*#__PURE__*/(0,jsx_runtime.jsx)(descriptions/* default */.Z.Item, {
            label: intl.formatMessage({
              id: 'common.time'
            }),
            children: "".concat((0,date/* formatDateDefault */.L6)(data === null || data === void 0 ? void 0 : data.start_date), " - ").concat((0,date/* formatDateDefault */.L6)(data === null || data === void 0 ? void 0 : data.end_date))
          })]
        })
      })
    })
  });
};
/* harmony default export */ var components_DetailCard = (DetailCard);
// EXTERNAL MODULE: ./node_modules/antd/es/date-picker/index.js + 78 modules
var date_picker = __webpack_require__(47676);
// EXTERNAL MODULE: ./node_modules/antd/es/theme/index.js + 6 modules
var theme = __webpack_require__(9361);
// EXTERNAL MODULE: ./node_modules/antd/es/collapse/index.js + 8 modules
var collapse = __webpack_require__(47221);
// EXTERNAL MODULE: ./src/pages/FarmingManagement/CropPlan/EditState/index.tsx
var EditState = __webpack_require__(95924);
// EXTERNAL MODULE: ./src/assets/img/icons/sunflower.svg
var sunflower = __webpack_require__(40796);
// EXTERNAL MODULE: ./src/pages/FarmingManagement/WorkflowManagement/Create/index.tsx + 1 modules
var Create = __webpack_require__(22864);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/PlusOutlined.js
var PlusOutlined = __webpack_require__(51042);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-table/es/Table.js + 76 modules
var Table = __webpack_require__(4894);
// EXTERNAL MODULE: ./node_modules/antd/es/table/index.js + 42 modules
var table = __webpack_require__(67839);
;// CONCATENATED MODULE: ./src/pages/FarmingManagement/CropPlan/TemplateDetail/components/StateTaskTable.tsx



















var StateTaskTable_useStyles = (0,antd_use_styles/* createStyles */.k)(function () {
  return {
    table: {
      '& .ant-pro-table-list-toolbar-left': {
        flex: 'none'
      }
    }
  };
});
var StateTaskTable = function StateTaskTable(_ref) {
  var stateId = _ref.stateId;
  var intl = (0,_umi_production_exports.useIntl)();
  var access = (0,_umi_production_exports.useAccess)();
  var canCreateTask = access.canCreateInWorkFlowManagement();
  var canReadTask = access.canAccessPageWorkFlowManagement();
  var styles = StateTaskTable_useStyles();
  var columns = [{
    title: intl.formatMessage({
      id: 'common.index'
    }),
    renderText: function renderText(text, record, index, action) {
      return index + 1;
    },
    search: false
  }, {
    title: intl.formatMessage({
      id: 'workflowTab.workName'
    }),
    dataIndex: 'label',
    renderText: function renderText(_text, record, index, action) {
      if (canReadTask) {
        return /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.Link, {
          to: "/farming-management/workflow-management/detail/".concat(record.name),
          children: /*#__PURE__*/(0,jsx_runtime.jsxs)(space/* default */.Z, {
            children: [/*#__PURE__*/(0,jsx_runtime.jsx)("img", {
              src: tree_green/* default */.Z
            }), " ", _text]
          })
        });
      } else return /*#__PURE__*/(0,jsx_runtime.jsxs)(space/* default */.Z, {
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)("img", {
          src: tree_green/* default */.Z
        }), " ", _text]
      });
    }
  }, {
    title: intl.formatMessage({
      id: 'common.status'
    }),
    dataIndex: 'status',
    render: function render(dom, entity, index) {
      switch (dom) {
        case 'Plan':
          return /*#__PURE__*/(0,jsx_runtime.jsx)(tag/* default */.Z, {
            color: "success",
            children: "K\\u1EBF ho\\u1EA1ch"
          });
        case 'Done':
          return /*#__PURE__*/(0,jsx_runtime.jsx)(tag/* default */.Z, {
            color: "success",
            children: "\\u0110\\xE3 ho\\xE0n th\\xE0nh"
          });
        case 'In progress':
          return /*#__PURE__*/(0,jsx_runtime.jsx)(tag/* default */.Z, {
            color: "warning",
            children: "\\u0110ang th\\u1EF1c hi\\u1EC7n"
          });
        case 'Pending':
          /*#__PURE__*/(0,jsx_runtime.jsx)(tag/* default */.Z, {
            color: "danger",
            children: "Tr\\xEC ho\\xE3n"
          });
        default:
          return /*#__PURE__*/(0,jsx_runtime.jsx)(tag/* default */.Z, {});
      }
    }
  }, {
    title: intl.formatMessage({
      id: 'workflowTab.executor'
    }),
    dataIndex: 'assigned_to',
    render: function render(value, record) {
      var _record$assigned_to_i;
      var info = (_record$assigned_to_i = record.assigned_to_info) === null || _record$assigned_to_i === void 0 ? void 0 : _record$assigned_to_i[0];
      if (!info) {
        return null;
      }
      return /*#__PURE__*/(0,jsx_runtime.jsxs)(space/* default */.Z, {
        children: [info.user_avatar && /*#__PURE__*/(0,jsx_runtime.jsx)(avatar/* default */.C, {
          size: 'small',
          src: (0,utils/* getFileUrlV2 */.mT)({
            src: info.user_avatar
          })
        }), /*#__PURE__*/(0,jsx_runtime.jsx)("span", {
          children: "".concat(info.first_name || '', " ").concat(info.last_name || '', " ")
        })]
      });
    },
    search: false
  }, {
    title: 'Th\xE0nh vi\xEAn li\xEAn quan',
    dataIndex: 'involve_in_users',
    hideInTable: true,
    render: function render(value, record) {
      try {
        var involveInArr = record.involve_in_users;
        var userNames = involveInArr.map(function (data) {
          return "".concat(data.first_name, " ").concat(data.last_name);
        });
        return userNames.join(', ');
      } catch (error) {
        return null;
      }
    },
    search: false
  }, {
    title: 'D\u1EF1 \xE1n',
    dataIndex: 'project_name',
    hideInTable: true,
    render: function render(text, record, index, action) {
      return /*#__PURE__*/(0,jsx_runtime.jsxs)(space/* default */.Z, {
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)("img", {
          src: sunflower/* default */.Z
        }), /*#__PURE__*/(0,jsx_runtime.jsx)("span", {
          children: text
        })]
      });
    }
  }, {
    title: 'Khu v\u1EF1c ',
    hideInTable: true,
    dataIndex: 'zone_name'
    // renderText() {
    //   return 'V\u01B0\u1EDDn \u0110\xE0 L\u1EA1t';
    // },
  }, {
    title: 'V\u1EE5 m\xF9a',
    hideInTable: true,
    dataIndex: 'crop_name'
    // renderText() {
    //   return 'V\u1EE5 m\xF9a d\xE2u t\xE2y';
    // },
  }, {
    title: 'K\u1EBF ho\u1EA1ch',
    hideInTable: true,
    dataIndex: 'plan_name',
    render: function render(value, record) {
      try {
        return record.plan_name;
      } catch (error) {
        return null;
      }
    }
  }, {
    title: 'Giai \u0111o\u1EA1n',
    dataIndex: 'state_name',
    hideInTable: true,
    render: function render(value, record) {
      try {
        return record.state_name;
      } catch (error) {
        return null;
      }
    }
  }, {
    search: false,
    title: intl.formatMessage({
      id: 'common.start_date'
    }),
    dataIndex: 'start_date',
    render: function render(dom, entity) {
      return dayjs_min_default()(entity.start_date).format('YYYY-MM-DD HH:mm'); // Format date to 'YYYY-MM-DD HH:mm'
    },
    sortDirections: ['ascend', 'descend', 'ascend'],
    sorter: true,
    defaultSortOrder: 'descend'
  }, {
    search: false,
    title: intl.formatMessage({
      id: 'common.end_date'
    }),
    dataIndex: 'end_date',
    render: function render(dom, entity) {
      return dayjs_min_default()(entity.end_date).format('YYYY-MM-DD HH:mm'); // Format date to 'YYYY-MM-DD HH:mm'
    },
    sortDirections: ['ascend', 'descend', 'ascend'],
    sorter: true
  }, {
    title: 'C\xF4ng vi\u1EC7c',
    hideInTable: true,
    dataIndex: 'worksheet_list',
    render: function render(value, record) {
      try {
        var worksheet_list = record.worksheet_list;
        var dataName = worksheet_list.map(function (data) {
          return "".concat(data.work_type.label);
        });
        return dataName.join(', ');
      } catch (error) {
        return null;
      }
    },
    search: false
  },
  // {
  //         title: <FormattedMessage id="category.material-management.category_name" defaultMessage="unknown" />,
  //   dataIndex: 'item_list',
  //   render(value, record) {
  //     try {
  //       let item_list = record.item_list;
  //       const dataName = item_list.map((data: any) => \`\${data.category.label}\`);
  //       return dataName.join(', ');
  //     } catch (error) {
  //       return null;
  //     }
  //   },
  //   search: false,
  // },
  {
    title: intl.formatMessage({
      id: 'common.completion_level'
    }),
    dataIndex: 'todo_done',
    search: false,
    render: function render(value, record) {
      return "".concat(record.todo_done || 0, "/").concat(record.todo_total || 0);
    }
  }];
  var actionRef = (0,react.useRef)();
  // create new Task modal
  // modal create new task
  var reloadTable = function reloadTable() {
    var _actionRef$current, _actionRef$current$re;
    (_actionRef$current = actionRef.current) === null || _actionRef$current === void 0 || (_actionRef$current$re = _actionRef$current.reload) === null || _actionRef$current$re === void 0 || _actionRef$current$re.call(_actionRef$current);
  };
  var _useState = (0,react.useState)(false),
    _useState2 = slicedToArray_default()(_useState, 2),
    openModalCreateNewTask = _useState2[0],
    setOpenModalCreateNewTask = _useState2[1];
  var toolbarButtons = [];
  if (canCreateTask) {
    toolbarButtons.push( /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
      icon: /*#__PURE__*/(0,jsx_runtime.jsx)(PlusOutlined/* default */.Z, {}),
      onClick: function onClick() {
        setOpenModalCreateNewTask(true);
        return;
        // v\xEC ch\u1ED7 n\xE0y \u0111\xE3 memo component
        // n\xEAn farmingPlanState ko dc c\u1EADp nh\u1EADt
        var urlParams = new URLSearchParams(window.location.search);
        _umi_production_exports.history.push("/farming-management/workflow-management/create".concat(stateId ? "?farming_plan_state=".concat(stateId) : ''));
      },
      type: "primary",
      children: intl.formatMessage({
        id: 'workflowTab.createWork'
      })
    }, 'create'));
  }
  return /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
    children: [/*#__PURE__*/(0,jsx_runtime.jsx)(Table/* default */.Z, {
      style: {
        maxWidth: '75vw'
      },
      actionRef: actionRef,
      className: styles.table
      //   form={{
      //     syncToUrl: true,
      //     defaultCollapsed: false,
      //     initialValues: {
      //       pl_state_name: farmingPlanState,
      //     },
      //   }}
      ,
      search: false,
      tableAlertOptionRender: function tableAlertOptionRender() {
        return null;
      },
      toolbar: {
        actions: toolbarButtons
        // onSearch(keyWords) {
        //   console.log('keyWords: ', keyWords);
        // },
        // filter: (
        //   <ProForm
        //     form={formFilter}
        //     name="crop-detail:table-filter"
        //     onValuesChange={(changeValue) => {
        //       if (changeValue.dateRange) {
        //         searchParams.set(dateRangeFilterKey, JSON.stringify(changeValue.dateRange));
        //       } else {
        //         searchParams.delete(dateRangeFilterKey);
        //       }
        //       setSearchParams(searchParams);
        //     }}
        //     layout="inline"
        //     submitter={false}
        //   >
        //     <ProFormDateRangePicker
        //       name="dateRange"
        //       label="Th\u1EDDi gian th\u1EF1c hi\u1EC7n"
        //       style={{
        //         width: 150,
        //       }}
        //     />
        //     <Space size={'large'}>
        //       <Button
        //         onClick={() => {
        //           formFilter.setFieldsValue({
        //             dateRange: [new Date(), new Date()],
        //           });
        //           //
        //           searchParams.set(
        //             dateRangeFilterKey,
        //             JSON.stringify(
        //               [new Date(), new Date()].map((item) => dayjs(item).format('YYYY-MM-DD')),
        //             ),
        //           );
        //           setSearchParams(searchParams);
        //         }}
        //       >
        //         H\xF4m nay
        //       </Button>
        //       <Button
        //         icon={<PlusOutlined />}
        //         onClick={() => {
        //           // v\xEC ch\u1ED7 n\xE0y  \u0111\xE3 memo component
        //           // n\xEAn farmingPlanState ko dc c\u1EADp nh\u1EADt
        //           const urlParams = new URLSearchParams(window.location.search);
        //           const currentFarmingPlanState = urlParams.get('pl_state_name');
        //           history.push(
        //             \`/farming-management/workflow-management/create\${
        //               currentFarmingPlanState
        //                 ? \`?farming_plan_state=\${currentFarmingPlanState}\`
        //                 : ''
        //             }\`,
        //           );
        //         }}
        //       >
        //         T\u1EA1o c\xF4ng vi\u1EC7c m\u1EDBi
        //       </Button>
        //     </Space>
        //   </ProForm>
        // ),
        // actions: [
        //   <Button key="primary" type="primary">
        //     \u6DFB\u52A0
        //   </Button>,
        // ],
      },
      rowSelection: {
        selections: [table/* default */.Z.SELECTION_ALL, table/* default */.Z.SELECTION_INVERT]
      },
      pagination: {
        showSizeChanger: true,
        pageSizeOptions: [10, 20, 50, 100]
      },
      request: ( /*#__PURE__*/function () {
        var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(params, sort, filter) {
          var _res$pagination;
          var paramsReq, res;
          return regeneratorRuntime_default()().wrap(function _callee$(_context) {
            while (1) switch (_context.prev = _context.next) {
              case 0:
                paramsReq = (0,utils/* getParamsReqTable */.wh)({
                  doc_name: constanst/* DOCTYPE_ERP */.lH.iotFarmingPlanTask,
                  tableReqParams: {
                    params: params,
                    sort: sort,
                    filter: filter
                  },
                  concatFilter: [['iot_farming_plan_task', 'farming_plan_state', 'like', stateId]]
                });
                _context.next = 3;
                return (0,farming_plan/* getTemplateTaskManagerList */.dQ)(paramsReq);
              case 3:
                res = _context.sent;
                return _context.abrupt("return", {
                  data: res.data,
                  success: true,
                  total: res === null || res === void 0 || (_res$pagination = res.pagination) === null || _res$pagination === void 0 ? void 0 : _res$pagination.totalElements
                });
              case 5:
              case "end":
                return _context.stop();
            }
          }, _callee);
        }));
        return function (_x, _x2, _x3) {
          return _ref2.apply(this, arguments);
        };
      }()),
      headerTitle: "Danh s\\xE1ch c\\xF4ng vi\\u1EC7c",
      columns: columns,
      rowKey: 'name',
      scroll: {
        x: 'max-content'
      }
    }), openModalCreateNewTask && /*#__PURE__*/(0,jsx_runtime.jsx)(Create["default"], {
      mode: "modal",
      open: openModalCreateNewTask,
      onOpenChange: setOpenModalCreateNewTask,
      onCreateSuccess: reloadTable,
      farmingPlanStateId: stateId
    })]
  });
};
/* harmony default export */ var components_StateTaskTable = (StateTaskTable);
;// CONCATENATED MODULE: ./src/pages/FarmingManagement/CropPlan/TemplateDetail/components/StateCollapsableList.tsx













var RangePicker = date_picker["default"].RangePicker;
var StateCollapsableList = function StateCollapsableList(_ref) {
  var planId = _ref.planId,
    onSuccess = _ref.onSuccess,
    stateListRequest = _ref.stateListRequest;
  var _theme$useToken = theme["default"].useToken(),
    token = _theme$useToken.token;
  var _App$useApp = app/* default */.Z.useApp(),
    modal = _App$useApp.modal,
    message = _App$useApp.message;
  var run = stateListRequest.run,
    data = stateListRequest.data,
    loading = stateListRequest.loading,
    refresh = stateListRequest.refresh;
  var intl = (0,_umi_production_exports.useIntl)();
  // const { run, data, loading, refresh } = useRequest(() =>
  //   getFarmingPlanStates({
  //     filters: \`[["\${DOCTYPE_ERP.iotFarmingPlanState}", "farming_plan", "like", "\${planId}"]]\`,
  //   }),
  // );
  var onDeletePlanState = function onDeletePlanState(_ref2) {
    var stateId = _ref2.stateId;
    modal.confirm({
      content: 'Are you sure you want to delete this state?',
      onOk: function () {
        var _onOk = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
          return regeneratorRuntime_default()().wrap(function _callee$(_context) {
            while (1) switch (_context.prev = _context.next) {
              case 0:
                _context.prev = 0;
                _context.next = 3;
                return (0,farming_plan/* deletePlanState */.EZ)({
                  name: stateId
                });
              case 3:
                message.success({
                  content: 'Delete successfully'
                });
                refresh();
                return _context.abrupt("return", true);
              case 8:
                _context.prev = 8;
                _context.t0 = _context["catch"](0);
                message.error({
                  content: 'Delete error, please try again'
                });
                return _context.abrupt("return", false);
              case 12:
              case "end":
                return _context.stop();
            }
          }, _callee, null, [[0, 8]]);
        }));
        function onOk() {
          return _onOk.apply(this, arguments);
        }
        return onOk;
      }(),
      okButtonProps: {
        danger: true
      }
    });
  };
  var panelStyle = {
    marginBottom: 24,
    background: token.colorBgContainer,
    borderRadius: token.borderRadiusLG
    // border: 'none',
  };
  (0,react.useEffect)(function () {
    var fetchData = function fetchData() {
      if (planId) run();
      console.log('data state', data);
    };
    fetchData();
  }, [planId]);
  var access = (0,_umi_production_exports.useAccess)();
  var canDeleteState = access.canDeleteInStateManagement();
  var canUpdateState = access.canUpdateInStateManagement();
  var dateFormat = 'YYYY/MM/DD';
  return /*#__PURE__*/(0,jsx_runtime.jsx)(collapse/* default */.Z, {
    accordion: true // One active at a time
    ,
    bordered: false
    // defaultActiveKey={[0]}
    ,
    style: {
      background: 'none'
    },
    collapsible: "header",
    items: (data === null || data === void 0 ? void 0 : data.map(function (state, index) {
      return {
        label: state.label,
        key: index,
        style: panelStyle,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(components_StateTaskTable, {
          stateId: state.name
        }),
        extra: /*#__PURE__*/(0,jsx_runtime.jsx)(row/* default */.Z, {
          justify: "center",
          children: /*#__PURE__*/(0,jsx_runtime.jsxs)(space/* default */.Z, {
            align: "center",
            children: [/*#__PURE__*/(0,jsx_runtime.jsx)(tag/* default */.Z, {
              children: "".concat((0,date/* dayjsUtil */.PF)(state.end_date).diff(state.start_date, 'd') || 0, " ").concat(intl.formatMessage({
                id: 'common.date'
              }))
            }), data[index] && data[index].start_date && data[index].end_date ? /*#__PURE__*/(0,jsx_runtime.jsx)(RangePicker, {
              size: 'middle',
              value: [dayjs_min_default()(data[index].start_date, dateFormat), dayjs_min_default()(data[index].end_date, dateFormat)],
              disabled: true,
              format: dateFormat
            }) : null, canUpdateState && /*#__PURE__*/(0,jsx_runtime.jsx)(EditState/* default */.Z, {
              id: state.name,
              onSuccess: function onSuccess() {
                refresh();
              }
            }, "edit"), canDeleteState && /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
              size: "middle",
              icon: /*#__PURE__*/(0,jsx_runtime.jsx)(DeleteOutlined/* default */.Z, {}),
              danger: true,
              onClick: function onClick(e) {
                e.stopPropagation();
                onDeletePlanState({
                  stateId: state.name
                });
              },
              children: intl.formatMessage({
                id: 'common.delete'
              })
            }, 'delete')]
          })
        })
      };
    })) || []
  });
};
/* harmony default export */ var components_StateCollapsableList = (StateCollapsableList);
;// CONCATENATED MODULE: ./src/pages/FarmingManagement/CropPlan/TemplateDetail/index.tsx
















var getPlanId = /*#__PURE__*/function () {
  var _ref = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(cropId) {
    var plan;
    return regeneratorRuntime_default()().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.next = 2;
          return (0,sscript/* sscriptGeneralList */.RB)({
            doc_name: 'iot_farming_plan',
            fields: ['name'],
            filters: [['iot_farming_plan', 'crop', 'like', cropId]]
          });
        case 2:
          plan = _context.sent;
          return _context.abrupt("return", {
            data: plan.data[0].name
          });
        case 4:
        case "end":
          return _context.stop();
      }
    }, _callee);
  }));
  return function getPlanId(_x) {
    return _ref.apply(this, arguments);
  };
}();
var CropPlanTemplateDetail = function CropPlanTemplateDetail(_ref2) {
  var children = _ref2.children;
  var paramsUrl = (0,react_router/* useParams */.UO)();
  var cropId = paramsUrl.id;
  var _useState = (0,react.useState)(null),
    _useState2 = slicedToArray_default()(_useState, 2),
    planId = _useState2[0],
    setPlanId = _useState2[1];
  var planIdRequest = (0,_umi_production_exports.useRequest)(function () {
    return getPlanId(cropId);
  }, {
    manual: true,
    onSuccess: function onSuccess(data) {
      setPlanId(data);
    }
  });
  var stateListRequest = (0,_umi_production_exports.useRequest)(function () {
    if (planId) {
      return (0,farming_plan/* getFarmingPlanState */.jY)({
        filters: "[[\\"".concat(constanst/* DOCTYPE_ERP */.lH.iotFarmingPlanState, "\\", \\"farming_plan\\", \\"like\\", \\"").concat(planId, "\\"]]"),
        order_by: "start_date ASC"
      });
    }
  }, {
    manual: true
  });
  (0,react.useEffect)(function () {
    planIdRequest.run();
  }, []);
  (0,react.useEffect)(function () {
    if (planId) {
      stateListRequest.run();
    }
  }, [planId]);
  var access = (0,_umi_production_exports.useAccess)();
  var canCreateState = access.canCreateInStateManagement();
  var canReadState = access.canAccessPageStateManagement();
  return /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
    children: [/*#__PURE__*/(0,jsx_runtime.jsx)(components_DetailCard, {
      planId: planId
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(divider/* default */.Z, {}), /*#__PURE__*/(0,jsx_runtime.jsxs)(space/* default */.Z, {
      direction: "vertical",
      style: {
        display: 'flex'
      },
      size: 32,
      children: [canCreateState && /*#__PURE__*/(0,jsx_runtime.jsx)(CreateState/* default */.Z, {
        planId: planId,
        onSuccess: function onSuccess() {
          stateListRequest.refresh();
        }
      }, "create-state"), canReadState && /*#__PURE__*/(0,jsx_runtime.jsx)(components_StateCollapsableList, {
        planId: paramsUrl.id,
        stateListRequest: stateListRequest
      })]
    })]
  });
};
/* harmony default export */ var TemplateDetail = (CropPlanTemplateDetail);
;// CONCATENATED MODULE: ./src/pages/FarmingManagement/SeasonalManagement/Detail/hooks/useParamsUrl.ts


var tabActiveKey = 'tab';
var tabSecondActiveKey = 'tab2';
function useParamsUrl() {
  var _useSearchParams = (0,_umi_production_exports.useSearchParams)(),
    _useSearchParams2 = slicedToArray_default()(_useSearchParams, 2),
    searchParams = _useSearchParams2[0],
    setSearchParams = _useSearchParams2[1];
  var _useParams = (0,_umi_production_exports.useParams)(),
    detailId = _useParams.id;
  var tabActive = searchParams.get(tabActiveKey);
  var setTabActive = function setTabActive(tabActive) {
    searchParams.set(tabActiveKey, tabActive === null || tabActive === void 0 ? void 0 : tabActive.toString());
    setSearchParams(searchParams);
  };
  var tabSecondActive = searchParams.get(tabSecondActiveKey);
  var setTabSecondActive = function setTabSecondActive(tabActive) {
    searchParams.set(tabSecondActiveKey, tabActive === null || tabActive === void 0 ? void 0 : tabActive.toString());
    setSearchParams(searchParams);
  };
  return {
    tabActive: tabActive,
    setTabActive: setTabActive,
    tabSecondActive: tabSecondActive,
    setTabSecondActive: setTabSecondActive,
    detailId: detailId
  };
}
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/objectSpread2.js
var objectSpread2 = __webpack_require__(97857);
var objectSpread2_default = /*#__PURE__*/__webpack_require__.n(objectSpread2);
// EXTERNAL MODULE: ./src/services/plantRefAndUserOwner.ts
var plantRefAndUserOwner = __webpack_require__(44045);
// EXTERNAL MODULE: ./src/utils/string.ts
var string = __webpack_require__(7369);
// EXTERNAL MODULE: ./node_modules/antd/es/spin/index.js + 2 modules
var spin = __webpack_require__(75081);
;// CONCATENATED MODULE: ./src/pages/FarmingManagement/SeasonalManagement/Detail/Instruct/index.tsx













var GeneralInfo = (0,lazy/* myLazy */.Q)(function () {
  return __webpack_require__.e(/* import() */ 9797).then(__webpack_require__.bind(__webpack_require__, 69797));
});
var CareInstructions = (0,lazy/* myLazy */.Q)(function () {
  return __webpack_require__.e(/* import() */ 5441).then(__webpack_require__.bind(__webpack_require__, 55441));
});
var Instruct = function Instruct(_ref) {
  var children = _ref.children,
    plantId = _ref.plantId;
  var _useParamsUrl = useParamsUrl(),
    tabSecondActive = _useParamsUrl.tabSecondActive,
    setTabSecondActive = _useParamsUrl.setTabSecondActive;
  var _useState = (0,react.useState)(false),
    _useState2 = slicedToArray_default()(_useState, 2),
    isLoading = _useState2[0],
    setIsLoading = _useState2[1];
  var _useState3 = (0,react.useState)(),
    _useState4 = slicedToArray_default()(_useState3, 2),
    plantAllResource = _useState4[0],
    setPlantAllResource = _useState4[1];
  (0,react.useEffect)(function () {
    var fetchData = /*#__PURE__*/function () {
      var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
        var _res$data, res;
        return regeneratorRuntime_default()().wrap(function _callee$(_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              setIsLoading(true);
              _context.prev = 1;
              _context.next = 4;
              return (0,plantRefAndUserOwner/* getPlantUserOwnerAllResources */.A)({
                plant_id: plantId
              });
            case 4:
              res = _context.sent;
              setPlantAllResource((_res$data = res.data) === null || _res$data === void 0 ? void 0 : _res$data[0]);
              _context.next = 11;
              break;
            case 8:
              _context.prev = 8;
              _context.t0 = _context["catch"](1);
              console.log(_context.t0);
            case 11:
              _context.prev = 11;
              setIsLoading(false);
              return _context.finish(11);
            case 14:
            case "end":
              return _context.stop();
          }
        }, _callee, null, [[1, 8, 11, 14]]);
      }));
      return function fetchData() {
        return _ref2.apply(this, arguments);
      };
    }();
    fetchData();
  }, [plantId]);
  console.log("plantAllResource", plantAllResource === null || plantAllResource === void 0 ? void 0 : plantAllResource.infor_tab_list);
  var mappingTab = (0,react.useMemo)(function () {
    return [{
      label: 'Th\xF4ng tin chung',
      children: function children() {
        var _plantAllResource$inf;
        return /*#__PURE__*/(0,jsx_runtime.jsx)(GeneralInfo, {
          data: plantAllResource === null || plantAllResource === void 0 || (_plantAllResource$inf = plantAllResource.infor_tab_list) === null || _plantAllResource$inf === void 0 ? void 0 : _plantAllResource$inf.map(function (item) {
            return {
              id: item.name,
              title: item.label,
              icon: item.icon,
              content: item.description,
              image: item.image
            };
          })
        });
      },
      fallback: /*#__PURE__*/(0,jsx_runtime.jsx)(List/* ListSkeleton */.cg, {
        size: 10
      })
    }, {
      label: 'H\u01B0\u1EDBng d\u1EABn ch\u0103m s\xF3c',
      children: function children() {
        var _plantAllResource$gui;
        return /*#__PURE__*/(0,jsx_runtime.jsx)(CareInstructions, {
          data: plantAllResource === null || plantAllResource === void 0 || (_plantAllResource$gui = plantAllResource.guide_list) === null || _plantAllResource$gui === void 0 ? void 0 : _plantAllResource$gui.map(function (item) {
            return {
              id: item.name,
              title: item.label,
              icon: item.icon,
              content: item.description,
              image: item.image
            };
          })
        });
      },
      fallback: /*#__PURE__*/(0,jsx_runtime.jsx)(List/* ListSkeleton */.cg, {
        size: 10
      })
    }].map(function (item) {
      return objectSpread2_default()(objectSpread2_default()({}, item), {}, {
        key: (0,string/* nonAccentVietnamese */.w)(item.label)
      });
    });
  }, [plantAllResource]);
  var tabItems = mappingTab.map(function (item) {
    return {
      label: item.label,
      key: item.key
    };
  });
  var tabActive = mappingTab.find(function (item) {
    return item.key === tabSecondActive;
  }) || mappingTab[0];
  var Component = tabActive.children;
  return /*#__PURE__*/(0,jsx_runtime.jsxs)(spin/* default */.Z, {
    spinning: isLoading,
    children: [/*#__PURE__*/(0,jsx_runtime.jsx)(tabs/* default */.Z, {
      style: {
        marginBlockStart: -20
      },
      activeKey: tabActive.key,
      items: tabItems,
      onChange: setTabSecondActive
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(react.Suspense, {
      fallback: tabActive.fallback || null,
      children: Component && /*#__PURE__*/(0,jsx_runtime.jsx)(Component, {})
    })]
  });
};
/* harmony default export */ var Detail_Instruct = (Instruct);
// EXTERNAL MODULE: ./src/pages/FarmingManagement/SeasonalManagement/Detail/Note/Create/index.tsx
var Note_Create = __webpack_require__(17429);
// EXTERNAL MODULE: ./src/pages/FarmingManagement/SeasonalManagement/Detail/Pandemic/Create/index.tsx
var Pandemic_Create = __webpack_require__(78189);
// EXTERNAL MODULE: ./src/services/ticket.ts
var services_ticket = __webpack_require__(1743);
// EXTERNAL MODULE: ./src/stores/TicketStore.tsx
var TicketStore = __webpack_require__(33217);
// EXTERNAL MODULE: ./node_modules/antd/es/list/index.js + 3 modules
var list = __webpack_require__(2487);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/toConsumableArray.js
var toConsumableArray = __webpack_require__(19632);
var toConsumableArray_default = /*#__PURE__*/__webpack_require__.n(toConsumableArray);
// EXTERNAL MODULE: ./src/services/fileUpload.ts
var services_fileUpload = __webpack_require__(96876);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/CameraFilled.js + 1 modules
var CameraFilled = __webpack_require__(9890);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/Text/index.js
var Text = __webpack_require__(5966);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/TextArea/index.js
var TextArea = __webpack_require__(90672);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/UploadButton/index.js + 1 modules
var UploadButton = __webpack_require__(77636);
// EXTERNAL MODULE: ./node_modules/antd/es/form/index.js + 18 modules
var es_form = __webpack_require__(98138);
// EXTERNAL MODULE: ./node_modules/antd/es/modal/index.js + 1 modules
var modal = __webpack_require__(85576);
;// CONCATENATED MODULE: ./src/pages/FarmingManagement/SeasonalManagement/Detail/Ticket/components/CreateTicket.tsx







 // assuming you have a createTicket service









var CreateTicketButton = function CreateTicketButton(_ref) {
  var crop_id = _ref.crop_id;
  var _useState = (0,react.useState)(false),
    _useState2 = slicedToArray_default()(_useState, 2),
    isModalVisible = _useState2[0],
    setIsModalVisible = _useState2[1];
  var _useTicketListStore = (0,TicketStore/* useTicketListStore */.F)(),
    setTickets = _useTicketListStore.setTickets,
    fetchTickets = _useTicketListStore.fetchTickets;
  console.log('setTickets function:', setTickets); // Check if setTickets is defined

  var _Form$useForm = es_form/* default */.Z.useForm(),
    _Form$useForm2 = slicedToArray_default()(_Form$useForm, 1),
    form = _Form$useForm2[0];
  var intl = (0,_umi_production_exports.useIntl)();
  var showModal = function showModal() {
    setIsModalVisible(true);
  };
  var handleCancel = function handleCancel() {
    setIsModalVisible(false);
  };
  var handleCreate = /*#__PURE__*/function () {
    var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2(values) {
      var createBody, res, ticketId, uploadListRes, checkUploadFailed, arrFileUrl;
      return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            _context2.prev = 0;
            // Handle ticket creation here
            values.crop_id = crop_id;
            createBody = objectSpread2_default()(objectSpread2_default()({}, values), {}, {
              crop_id: crop_id
            });
            delete createBody.img;
            _context2.next = 6;
            return (0,services_ticket/* createTicket */.ax)(createBody);
          case 6:
            res = _context2.sent;
            ticketId = res.data.name;
            fetchTickets(crop_id);
            if (values.img) {
              _context2.next = 13;
              break;
            }
            message/* default */.ZP.success('T\u1EA1o phi\u1EBFu th\xE0nh c\xF4ng');
            setIsModalVisible(false);
            return _context2.abrupt("return");
          case 13:
            _context2.next = 15;
            return Promise.allSettled(values.img.map( /*#__PURE__*/function () {
              var _ref3 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(item) {
                return regeneratorRuntime_default()().wrap(function _callee$(_context) {
                  while (1) switch (_context.prev = _context.next) {
                    case 0:
                      _context.next = 2;
                      return (0,services_fileUpload/* uploadFile */.cT)({
                        docType: constanst/* DOCTYPE_ERP */.lH.iotTicket,
                        docName: ticketId,
                        file: item.originFileObj
                      });
                    case 2:
                      return _context.abrupt("return", _context.sent);
                    case 3:
                    case "end":
                      return _context.stop();
                  }
                }, _callee);
              }));
              return function (_x2) {
                return _ref3.apply(this, arguments);
              };
            }()));
          case 15:
            uploadListRes = _context2.sent;
            // check if() 1 v\xE0i upload failed
            checkUploadFailed = uploadListRes.find(function (item) {
              return item.status === 'rejected';
            });
            if (checkUploadFailed) {
              message/* default */.ZP.error({
                content: 'Some file upload failed'
              });
            }

            // update img path
            arrFileUrl = uploadListRes.reduce(function (prev, item) {
              var _item$value;
              return item.status === 'fulfilled' ? [].concat(toConsumableArray_default()(prev), [item === null || item === void 0 || (_item$value = item.value) === null || _item$value === void 0 || (_item$value = _item$value.data) === null || _item$value === void 0 || (_item$value = _item$value.message) === null || _item$value === void 0 ? void 0 : _item$value.file_url]) : prev;
            }, []).filter(function (item) {
              return typeof item === 'string';
            });
            console.log('arrFileUrl', arrFileUrl);
            if (!(arrFileUrl.length > 0)) {
              _context2.next = 23;
              break;
            }
            _context2.next = 23;
            return (0,services_ticket/* updateTicket */.O)(objectSpread2_default()(objectSpread2_default()({}, res.data), {}, {
              image: arrFileUrl.join(','),
              name: ticketId
            }));
          case 23:
            message/* default */.ZP.success('T\u1EA1o phi\u1EBFu th\xE0nh c\xF4ng');
            setIsModalVisible(false);
            _context2.next = 30;
            break;
          case 27:
            _context2.prev = 27;
            _context2.t0 = _context2["catch"](0);
            console.log('error', _context2.t0);
          case 30:
          case "end":
            return _context2.stop();
        }
      }, _callee2, null, [[0, 27]]);
    }));
    return function handleCreate(_x) {
      return _ref2.apply(this, arguments);
    };
  }();
  return /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
    children: [/*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
      type: "default",
      icon: /*#__PURE__*/(0,jsx_runtime.jsx)(PlusOutlined/* default */.Z, {}),
      onClick: showModal,
      style: {
        width: '100%'
      },
      children: intl.formatMessage({
        id: 'common.create'
      })
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(modal/* default */.Z, {
      title: intl.formatMessage({
        id: 'common.create'
      }),
      open: isModalVisible,
      onCancel: handleCancel,
      footer: null,
      children: /*#__PURE__*/(0,jsx_runtime.jsxs)(ProForm/* ProForm */.A, {
        form: form,
        onFinish: handleCreate,
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)(Text/* default */.Z, {
          name: "label",
          label: intl.formatMessage({
            id: 'common.name'
          })
        }), /*#__PURE__*/(0,jsx_runtime.jsx)(TextArea/* default */.Z, {
          name: "description",
          label: intl.formatMessage({
            id: 'common.description'
          })
        }), /*#__PURE__*/(0,jsx_runtime.jsx)(UploadButton/* default */.Z, {
          name: "img",
          label: intl.formatMessage({
            id: 'common.images'
          }),
          listType: "picture-card",
          icon: /*#__PURE__*/(0,jsx_runtime.jsx)(CameraFilled/* default */.Z, {}),
          title: "",
          accept: "image/*",
          fieldProps: {
            multiple: true,
            maxCount: 5,
            beforeUpload: function beforeUpload() {
              // Return false to stop the automatic upload
              return false;
            }
          },
          action: ''
        })]
      })
    })]
  });
};
/* harmony default export */ var CreateTicket = (CreateTicketButton);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/UserOutlined.js + 1 modules
var UserOutlined = __webpack_require__(87547);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectSpread2.js
var esm_objectSpread2 = __webpack_require__(1413);
;// CONCATENATED MODULE: ./node_modules/@ant-design/icons-svg/es/asn/MessageOutlined.js
// This icon file is generated automatically.
var MessageOutlined = { "icon": { "tag": "svg", "attrs": { "viewBox": "64 64 896 896", "focusable": "false" }, "children": [{ "tag": "path", "attrs": { "d": "M464 512a48 48 0 1096 0 48 48 0 10-96 0zm200 0a48 48 0 1096 0 48 48 0 10-96 0zm-400 0a48 48 0 1096 0 48 48 0 10-96 0zm661.2-173.6c-22.6-53.7-55-101.9-96.3-143.3a444.35 444.35 0 00-143.3-96.3C630.6 75.7 572.2 64 512 64h-2c-60.6.3-119.3 12.3-174.5 35.9a445.35 445.35 0 00-142 96.5c-40.9 41.3-73 89.3-95.2 142.8-23 55.4-34.6 114.3-34.3 174.9A449.4 449.4 0 00112 714v152a46 46 0 0046 46h152.1A449.4 449.4 0 00510 960h2.1c59.9 0 118-11.6 172.7-34.3a444.48 444.48 0 00142.8-95.2c41.3-40.9 73.8-88.7 96.5-142 23.6-55.2 35.6-113.9 35.9-174.5.3-60.9-11.5-120-34.8-175.6zm-151.1 438C704 845.8 611 884 512 884h-1.7c-60.3-.3-120.2-15.3-173.1-43.5l-8.4-4.5H188V695.2l-4.5-8.4C155.3 633.9 140.3 574 140 513.7c-.4-99.7 37.7-193.3 107.6-263.8 69.8-70.5 163.1-109.5 262.8-109.9h1.7c50 0 98.5 9.7 144.2 28.9 44.6 18.7 84.6 45.6 119 80 34.3 34.3 61.3 74.4 80 119 19.4 46.2 29.1 95.2 28.9 145.8-.6 99.6-39.7 192.9-110.1 262.7z" } }] }, "name": "message", "theme": "outlined" };
/* harmony default export */ var asn_MessageOutlined = (MessageOutlined);

// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/components/AntdIcon.js + 5 modules
var AntdIcon = __webpack_require__(89099);
;// CONCATENATED MODULE: ./node_modules/@ant-design/icons/es/icons/MessageOutlined.js

// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY



var MessageOutlined_MessageOutlined = function MessageOutlined(props, ref) {
  return /*#__PURE__*/react.createElement(AntdIcon/* default */.Z, (0,esm_objectSpread2/* default */.Z)((0,esm_objectSpread2/* default */.Z)({}, props), {}, {
    ref: ref,
    icon: asn_MessageOutlined
  }));
};
MessageOutlined_MessageOutlined.displayName = 'MessageOutlined';
/* harmony default export */ var icons_MessageOutlined = (/*#__PURE__*/react.forwardRef(MessageOutlined_MessageOutlined));
// EXTERNAL MODULE: ./node_modules/antd/es/badge/index.js + 5 modules
var badge = __webpack_require__(40411);
// EXTERNAL MODULE: ./node_modules/antd/es/config-provider/index.js + 7 modules
var config_provider = __webpack_require__(28459);
// EXTERNAL MODULE: ./node_modules/antd/es/image/index.js + 37 modules
var es_image = __webpack_require__(11499);
;// CONCATENATED MODULE: ./src/pages/FarmingManagement/SeasonalManagement/Detail/Ticket/ticket.css
// extracted by mini-css-extract-plugin

;// CONCATENATED MODULE: ./src/pages/FarmingManagement/SeasonalManagement/Detail/Ticket/components/UpdateTicket.tsx
















var UpdateTicketModal = function UpdateTicketModal(_ref) {
  var _ticket$image;
  var ticket = _ref.ticket,
    visible = _ref.visible,
    onCancel = _ref.onCancel,
    onUpdated = _ref.onUpdated;
  var _Form$useForm = es_form/* default */.Z.useForm(),
    _Form$useForm2 = slicedToArray_default()(_Form$useForm, 1),
    form = _Form$useForm2[0];
  form.setFieldsValue({
    label: ticket.label,
    description: ticket.description,
    img: (_ticket$image = ticket.image) === null || _ticket$image === void 0 ? void 0 : _ticket$image.split(',').map(function (url) {
      return {
        uid: url,
        name: url,
        status: 'done',
        url: (0,utils/* generateAPIPath */.rH)('api/v2/file/download?file_url=' + url)
      };
    })
  });
  var intl = (0,_umi_production_exports.useIntl)();
  var _useSelectedTicketSto = (0,TicketStore/* useSelectedTicketStore */.A)(),
    selectedTicket = _useSelectedTicketSto.selectedTicket,
    setSelectedTicket = _useSelectedTicketSto.setSelectedTicket,
    setViewingDetails = _useSelectedTicketSto.setViewingDetails;
  var handleEdit = /*#__PURE__*/function () {
    var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2() {
      var values, arrFileUrl, filesUploaded, filesNotUpload, filesUploadedUrls, uploadListRes, checkUploadFailed, _updatedTicket, response;
      return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            _context2.next = 2;
            return form.validateFields();
          case 2:
            values = _context2.sent;
            _context2.prev = 3;
            arrFileUrl = undefined;
            if (!(values.img && (values.img || []).length > 0)) {
              _context2.next = 15;
              break;
            }
            // ki\u1EC3m tra c\xE1c file \u0111\xE3 upload
            filesUploaded = values.img.filter(function (item) {
              return !item.originFileObj;
            });
            filesNotUpload = values.img.filter(function (item) {
              return item.originFileObj;
            });
            filesUploadedUrls = filesUploaded.map(function (item) {
              var url = new URL(item.url || '');
              return url.searchParams.get('file_url');
            }).filter(function (item) {
              return item !== null;
            }); // upload b\u1EA5t k\u1EC3 th\xE0nh c\xF4ng hay ko
            _context2.next = 11;
            return Promise.allSettled(filesNotUpload.map( /*#__PURE__*/function () {
              var _ref3 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(item) {
                return regeneratorRuntime_default()().wrap(function _callee$(_context) {
                  while (1) switch (_context.prev = _context.next) {
                    case 0:
                      _context.next = 2;
                      return (0,services_fileUpload/* uploadFile */.cT)({
                        docType: constanst/* DOCTYPE_ERP */.lH.iotTicket,
                        docName: ticket.name,
                        file: item.originFileObj
                      });
                    case 2:
                      return _context.abrupt("return", _context.sent);
                    case 3:
                    case "end":
                      return _context.stop();
                  }
                }, _callee);
              }));
              return function (_x) {
                return _ref3.apply(this, arguments);
              };
            }()));
          case 11:
            uploadListRes = _context2.sent;
            // check if() 1 v\xE0i upload failed
            checkUploadFailed = uploadListRes.find(function (item) {
              return item.status === 'rejected';
            });
            if (checkUploadFailed) {
              message/* default */.ZP.error({
                content: 'Some file upload failed'
              });
            }

            // update img path
            arrFileUrl = uploadListRes.reduce(function (prev, item) {
              var _item$value;
              return item.status === 'fulfilled' ? [].concat(toConsumableArray_default()(prev), [item === null || item === void 0 || (_item$value = item.value) === null || _item$value === void 0 || (_item$value = _item$value.data) === null || _item$value === void 0 || (_item$value = _item$value.message) === null || _item$value === void 0 ? void 0 : _item$value.file_url]) : prev;
            }, []).filter(function (item) {
              return typeof item === 'string';
            })
            // th\xEAm file \u0111\xE3 upload
            .concat(filesUploadedUrls);
          case 15:
            _updatedTicket = {
              name: ticket.name,
              label: values.label,
              description: values.description,
              image: arrFileUrl ? arrFileUrl.join(',') : null,
              ticket_involve_user: values.ticket_involve_user,
              messages: []
            };
            _context2.next = 18;
            return (0,services_ticket/* updateTicket */.O)(_updatedTicket);
          case 18:
            response = _context2.sent;
            setSelectedTicket(objectSpread2_default()(objectSpread2_default()({}, ticket), response.data));
            onCancel(); // Close the modal
            message/* default */.ZP.success('Ticket updated successfully');
            _context2.next = 27;
            break;
          case 24:
            _context2.prev = 24;
            _context2.t0 = _context2["catch"](3);
            console.log('error', _context2.t0);
          case 27:
          case "end":
            return _context2.stop();
        }
      }, _callee2, null, [[3, 24]]);
    }));
    return function handleEdit() {
      return _ref2.apply(this, arguments);
    };
  }();
  return /*#__PURE__*/(0,jsx_runtime.jsx)(modal/* default */.Z, {
    title: intl.formatMessage({
      id: 'common.edit'
    }),
    open: visible,
    onCancel: onCancel,
    footer: null,
    children: /*#__PURE__*/(0,jsx_runtime.jsxs)(ProForm/* ProForm */.A, {
      form: form,
      initialValues: ticket,
      onFinish: handleEdit,
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)(Text/* default */.Z, {
        name: "label",
        label: intl.formatMessage({
          id: 'common.name'
        })
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(TextArea/* default */.Z, {
        name: "description",
        label: intl.formatMessage({
          id: 'common.description'
        })
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(UploadButton/* default */.Z, {
        name: "img",
        label: intl.formatMessage({
          id: 'common.images'
        }),
        listType: "picture-card",
        icon: /*#__PURE__*/(0,jsx_runtime.jsx)(CameraFilled/* default */.Z, {}),
        title: "",
        accept: "image/*",
        fieldProps: {
          multiple: true,
          maxCount: 5,
          beforeUpload: function beforeUpload() {
            // Return false to stop the automatic upload
            return false;
          }
        },
        action: ''
      })]
    })
  });
};
/* harmony default export */ var UpdateTicket = (UpdateTicketModal);
;// CONCATENATED MODULE: ./src/pages/FarmingManagement/SeasonalManagement/Detail/Ticket/components/Ticket.tsx

















var getColorFromStatus = function getColorFromStatus(status) {
  switch (status) {
    case 'Open':
      return '#C8E4B2';
    case 'Closed':
      return '#DC8686';
    case 'In Progress':
      return '#6DA9E4';
    default:
      return '#FBF8DD';
  }
};
var SupportTicketCard = function SupportTicketCard(_ref) {
  var _initTicket$image;
  var initTicket = _ref.ticket,
    onClick = _ref.onClick;
  // const [ticket, setTicket] = useState<IIotTicket>(initTicket);
  // const { dataSource, setDataSource } = useTicketDataStore();
  var _useSelectedTicketSto = (0,TicketStore/* useSelectedTicketStore */.A)(),
    selectedTicket = _useSelectedTicketSto.selectedTicket,
    setSelectedTicket = _useSelectedTicketSto.setSelectedTicket,
    setViewingDetails = _useSelectedTicketSto.setViewingDetails;
  var _useState = (0,react.useState)(getColorFromStatus(initTicket.status)),
    _useState2 = slicedToArray_default()(_useState, 2),
    selectorBg = _useState2[0],
    setSelectorBg = _useState2[1];
  var handleUpdated = function handleUpdated(updatedTicket) {
    setSelectedTicket(objectSpread2_default()(objectSpread2_default()({}, initTicket), updatedTicket));
    message/* default */.ZP.success('Ticket updated successfully');
    setIsModalVisible(false);
  };
  var handleStatusChange = /*#__PURE__*/function () {
    var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(value) {
      var update;
      return regeneratorRuntime_default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _context.prev = 0;
            _context.t0 = value;
            _context.next = _context.t0 === 'Open' ? 4 : _context.t0 === 'Closed' ? 6 : _context.t0 === 'In Progress' ? 8 : 10;
            break;
          case 4:
            setSelectorBg('#C8E4B2');
            return _context.abrupt("break", 11);
          case 6:
            setSelectorBg('#DC8686');
            return _context.abrupt("break", 11);
          case 8:
            setSelectorBg('#6DA9E4');
            return _context.abrupt("break", 11);
          case 10:
            setSelectorBg('#FBF8DD');
          case 11:
            _context.next = 13;
            return (0,services_ticket/* updateTicket */.O)({
              name: initTicket.name,
              status: value
            });
          case 13:
            update = _context.sent;
            message/* default */.ZP.success('C\u1EADp nh\u1EADt phi\u1EBFu th\xE0nh c\xF4ng');
            _context.next = 20;
            break;
          case 17:
            _context.prev = 17;
            _context.t1 = _context["catch"](0);
            console.log('error', _context.t1);
          case 20:
          case "end":
            return _context.stop();
        }
      }, _callee, null, [[0, 17]]);
    }));
    return function handleStatusChange(_x) {
      return _ref2.apply(this, arguments);
    };
  }();
  var handleTitleClick = function handleTitleClick() {
    onClick(initTicket);
    setViewingDetails(true);
  };
  var _Form$useForm = es_form/* default */.Z.useForm(),
    _Form$useForm2 = slicedToArray_default()(_Form$useForm, 1),
    form = _Form$useForm2[0];
  form.setFieldsValue({
    label: initTicket.label,
    description: initTicket.description,
    img: (_initTicket$image = initTicket.image) === null || _initTicket$image === void 0 ? void 0 : _initTicket$image.split(',').map(function (url) {
      return {
        uid: url,
        name: url,
        status: 'done',
        url: (0,utils/* generateAPIPath */.rH)('api/v2/file/download?file_url=' + url)
      };
    })
  });
  var _useState3 = (0,react.useState)(false),
    _useState4 = slicedToArray_default()(_useState3, 2),
    isModalVisible = _useState4[0],
    setIsModalVisible = _useState4[1];
  var showModal = function showModal() {
    setIsModalVisible(true);
  };
  var handleCancel = function handleCancel() {
    setIsModalVisible(false);
  };
  var intl = (0,_umi_production_exports.useIntl)();
  return /*#__PURE__*/(0,jsx_runtime.jsxs)(card/* default */.Z, {
    className: "border-0 flex-1 shadow-sm",
    title: /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
      className: "font-bold text-xl mb-2 cursor-pointer hover:text-primary-green",
      onClick: handleTitleClick,
      style: {
        fontSize: '16px'
      } // Add this line
      ,
      children: initTicket.label
    }),
    extra: /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
      onClick: showModal,
      children: intl.formatMessage({
        id: 'common.edit'
      })
    }),
    children: [/*#__PURE__*/(0,jsx_runtime.jsx)(UpdateTicket, {
      ticket: initTicket,
      visible: isModalVisible,
      onCancel: function onCancel() {
        return setIsModalVisible(false);
      },
      onUpdated: handleUpdated
    }), /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
      gutter: 18,
      children: [/*#__PURE__*/(0,jsx_runtime.jsxs)(col/* default */.Z, {
        span: 6,
        children: [/*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
          className: "px-6 py-4",
          children: [/*#__PURE__*/(0,jsx_runtime.jsxs)("p", {
            className: "text-gray-700 text-base",
            children: [/*#__PURE__*/(0,jsx_runtime.jsx)(avatar/* default */.C, {
              icon: /*#__PURE__*/(0,jsx_runtime.jsx)(UserOutlined/* default */.Z, {})
            }), ' ', " ".concat(initTicket.first_name, " ").concat(initTicket.last_name)]
          }), initTicket.image && initTicket.image.split(',').map(function (url, index) {
            return /*#__PURE__*/(0,jsx_runtime.jsx)(es_image/* default */.Z, {
              width: 50,
              src: (0,utils/* generateAPIPath */.rH)('api/v2/file/download?file_url=' + url)
            }, index);
          })]
        }), /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
          className: "px-6 pt-4 pb-2"
        })]
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 12,
        children: /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
          className: "px-6 pt-4 pb-2",
          children: [/*#__PURE__*/(0,jsx_runtime.jsxs)("p", {
            className: "text-gray-600",
            children: ["Last updated: ", dayjs_min_default().utc(initTicket.modified).format('HH:mm DD/MM/YYYY')]
          }), /*#__PURE__*/(0,jsx_runtime.jsx)("p", {
            className: "text-gray-600"
            // style={{
            //   overflowWrap: 'break-word',
            //   wordWrap: 'break-word',
            //   textOverflow: 'ellipsis',
            //   maxHeight: '90px', // Adjust this value according to your needs
            //   overflow: 'hidden',
            // }}
            ,
            children: initTicket.description
          })]
        })
      }), /*#__PURE__*/(0,jsx_runtime.jsxs)(col/* default */.Z, {
        span: 6,
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)("div", {
          className: "px-6 pt-4 pb-2",
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(badge/* default */.Z, {
            count: initTicket.messages ? initTicket.messages.length : 0,
            showZero: true,
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(icons_MessageOutlined, {
              style: {
                fontSize: '24px'
              }
            })
          })
        }), /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
          className: "px-6 pt-4 pb-2",
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(config_provider/* default */.ZP, {
            theme: {
              components: {
                Select: {
                  selectorBg: selectorBg,
                  colorBorder: selectorBg
                }
              }
            },
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_select/* default */.Z, {
              className: "noFocusHighlight",
              variant: "filled",
              defaultValue: initTicket.status,
              style: {
                width: 120,
                backgroundColor: selectorBg,
                borderColor: selectorBg,
                // Set border color to match background
                outline: 'none',
                // Remove outline
                boxShadow: 'none' // Remove box shadow
              },
              onChange: handleStatusChange,
              options: [{
                value: 'Open',
                label: 'Open'
              }, {
                value: 'Closed',
                label: 'Closed'
              }, {
                value: 'In Progress',
                label: 'In Progress'
              }]
            })
          })
        })]
      })]
    })]
  });
};
/* harmony default export */ var Ticket = (SupportTicketCard);
// EXTERNAL MODULE: ./src/pages/FarmingManagement/SeasonalManagement/Detail/Ticket/components/TicketMessages.tsx
var TicketMessages = __webpack_require__(72648);
;// CONCATENATED MODULE: ./src/pages/FarmingManagement/SeasonalManagement/Detail/Ticket/index.tsx













var TicketList = function TicketList(props) {
  // const [selectedTicket, setSelectedTicket] = useState<IIotTicket | null>(null);
  var _useTicketListStore = (0,TicketStore/* useTicketListStore */.F)(),
    tickets = _useTicketListStore.tickets,
    setTickets = _useTicketListStore.setTickets;
  var _useSelectedTicketSto = (0,TicketStore/* useSelectedTicketStore */.A)(),
    selectedTicket = _useSelectedTicketSto.selectedTicket,
    setSelectedTicket = _useSelectedTicketSto.setSelectedTicket,
    viewingDetails = _useSelectedTicketSto.viewingDetails,
    setViewingDetails = _useSelectedTicketSto.setViewingDetails;
  //tickets state
  // const [tickets, setTickets] = useState<IIotTicket[]>([]);
  var handleTicketClick = function handleTicketClick(ticket) {
    setSelectedTicket(ticket);
  };
  var handleBackClick = function handleBackClick() {
    setSelectedTicket({});
    setViewingDetails(false);
  };

  //get data
  (0,react.useEffect)(function () {
    var fetchData = /*#__PURE__*/function () {
      var _ref = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
        var res;
        return regeneratorRuntime_default()().wrap(function _callee$(_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              _context.next = 2;
              return (0,services_ticket/* getTicketList */.tZ)({
                page: 1,
                size: 10000,
                filters: [['iot_ticket', 'crop_id', 'like', props.cropId]]
              });
            case 2:
              res = _context.sent;
              setTickets(res.data);
            case 4:
            case "end":
              return _context.stop();
          }
        }, _callee);
      }));
      return function fetchData() {
        return _ref.apply(this, arguments);
      };
    }();
    fetchData();
  }, [selectedTicket]);
  if (viewingDetails) {
    return /*#__PURE__*/(0,jsx_runtime.jsx)(TicketMessages["default"], {
      crop_id: props.cropId,
      ticket_id: selectedTicket.name,
      messages: selectedTicket.messages ? selectedTicket.messages : [],
      onBack: handleBackClick
    });
  }
  return /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
    children: [/*#__PURE__*/(0,jsx_runtime.jsx)(list/* default */.Z, {
      grid: {
        gutter: 16,
        column: 1
      },
      dataSource: tickets,
      renderItem: function renderItem(ticket) {
        return /*#__PURE__*/(0,jsx_runtime.jsx)(list/* default */.Z.Item, {
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(Ticket, {
            ticket: ticket,
            onClick: handleTicketClick
          }, ticket.name)
        });
      }
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(CreateTicket, {
      crop_id: props.cropId
    })]
  });
};
/* harmony default export */ var Detail_Ticket = (TicketList);
;// CONCATENATED MODULE: ./src/pages/FarmingManagement/SeasonalManagement/Detail/index.tsx






















var Pandemic = (0,lazy/* myLazy */.Q)(function () {
  return __webpack_require__.e(/* import() */ 7857).then(__webpack_require__.bind(__webpack_require__, 68943));
});
var Detail_GeneralInfo = (0,lazy/* myLazy */.Q)(function () {
  return Promise.all(/* import() */[__webpack_require__.e(397), __webpack_require__.e(4730), __webpack_require__.e(5757), __webpack_require__.e(3328), __webpack_require__.e(8183), __webpack_require__.e(2404), __webpack_require__.e(6799), __webpack_require__.e(2123), __webpack_require__.e(9392)]).then(__webpack_require__.bind(__webpack_require__, 62557));
});
var SeasonalNote = (0,lazy/* myLazy */.Q)(function () {
  return __webpack_require__.e(/* import() */ 348).then(__webpack_require__.bind(__webpack_require__, 60348));
});
var Participants = (0,lazy/* myLazy */.Q)(function () {
  return __webpack_require__.e(/* import() */ 2488).then(__webpack_require__.bind(__webpack_require__, 42488));
});
var DetailWorkflow = function DetailWorkflow() {
  var _useParamsUrl = useParamsUrl(),
    detailId = _useParamsUrl.detailId,
    tabActive = _useParamsUrl.tabActive,
    setTabActive = _useParamsUrl.setTabActive;
  var _useModel = (0,_umi_production_exports.useModel)('MyCrop'),
    selectedCrop = _useModel.selectedCrop,
    setSelectedCrop = _useModel.setSelectedCrop;
  var intl = (0,_umi_production_exports.useIntl)();
  //  h\u01B0\u1EDBng d\u1EABn c\u1EA7n th\xF4ng tin plant_id n\xEAn get \u1EDF \u0111\xE2y lu\xF4n
  var _useRequest = (0,_umi_production_exports.useRequest)(function () {
      return (0,crop/* getCrop */.EH)({
        page: 1,
        size: 1,
        filters: JSON.stringify([['iot_crop', 'name', 'like', detailId]]),
        fields: JSON.stringify(['name', 'plant_id', 'is_template'])
      });
    }, {
      refreshDeps: [detailId],
      formatResult: function formatResult(res) {
        setSelectedCrop(res.data.data[0]);
        return res.data.data[0];
      }
    }),
    detailCrop = _useRequest.data,
    loading = _useRequest.loading;
  // refresh the Pandemic
  var _useState = (0,react.useState)((0,index_browser/* nanoid */.x0)()),
    _useState2 = slicedToArray_default()(_useState, 2),
    pandemicCacheKey = _useState2[0],
    setPandemicCacheKey = _useState2[1];
  var _useState3 = (0,react.useState)((0,index_browser/* nanoid */.x0)()),
    _useState4 = slicedToArray_default()(_useState3, 2),
    cropNoteCacheKey = _useState4[0],
    setCropNoteCacheKey = _useState4[1];
  var access = (0,_umi_production_exports.useAccess)();
  var canReadTask = access.canAccessPageWorkFlowManagement();
  return /*#__PURE__*/(0,jsx_runtime.jsx)(PageContainerTabsWithSearch/* default */.Z, {
    tabsItems: [{
      label: intl.formatMessage({
        id: 'seasonalTab.overview'
      }),
      component: function component() {
        return /*#__PURE__*/(0,jsx_runtime.jsx)(Detail_GeneralInfo, {
          cropId: detailId
        });
      },
      // extraPage: [<Button key={'cancel'}>H\u1EE7y</Button>, <Button key={'save'}>L\u01B0u</Button>],
      fallback: /*#__PURE__*/(0,jsx_runtime.jsx)(Descriptions/* DescriptionsSkeleton */.Yk, {
        active: true
      })
    }, {
      label: intl.formatMessage({
        id: 'seasonalTab.care'
      }),
      component: function component() {
        return /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.Access, {
          accessible: canReadTask,
          fallback: /*#__PURE__*/(0,jsx_runtime.jsx)(FallbackContent/* default */.Z, {}),
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(card/* default */.Z, {
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(tabs/* default */.Z, {
              defaultActiveKey: "1",
              items: [{
                label: intl.formatMessage({
                  id: 'common.all'
                }),
                children: !selectedCrop.is_template ? /*#__PURE__*/(0,jsx_runtime.jsx)(TableCropPlanDetails/* default */.Z, {
                  cropId: detailId,
                  createNewTaskInModal: true
                }) : /*#__PURE__*/(0,jsx_runtime.jsx)(TaskTemplateTable/* default */.Z, {
                  cropId: detailId,
                  createNewTaskInModal: true
                }),
                key: '1'
              }, {
                label: intl.formatMessage({
                  id: 'common.today'
                }),
                children: !selectedCrop.is_template ? /*#__PURE__*/(0,jsx_runtime.jsx)(TodayTasks/* default */.Z, {
                  cropId: detailId
                }) : /*#__PURE__*/(0,jsx_runtime.jsx)(TodayTemplateTasks, {
                  cropId: detailId
                }),
                key: '2'
              }, {
                label: intl.formatMessage({
                  id: 'common.plan'
                }),
                children: !selectedCrop.is_template ? /*#__PURE__*/(0,jsx_runtime.jsx)(Detail["default"], {}) : /*#__PURE__*/(0,jsx_runtime.jsx)(TemplateDetail, {}),
                key: '3'
              }]
            })
          })
        });
      }
    }, {
      label: intl.formatMessage({
        id: 'seasonalTab.health'
      }),
      component: function component() {
        return /*#__PURE__*/(0,jsx_runtime.jsx)(Pandemic, {
          cropId: detailId,
          cacheKey: pandemicCacheKey
        });
      },
      extraPage: [
      /*#__PURE__*/
      // <Button key={'delete-ss'}>X\xF3a v\u1EE5 m\xF9a</Button>,
      // <Button key={'create-ss'}>T\u1EA1o v\u1EE5 m\xF9a m\u1EDBi</Button>,
      (0,jsx_runtime.jsx)(Pandemic_Create/* default */.Z, {
        cropId: detailId,
        onSuccess: function onSuccess() {
          setPandemicCacheKey((0,index_browser/* nanoid */.x0)());
        }
      }, "create-pandemic")],
      fallback: /*#__PURE__*/(0,jsx_runtime.jsx)(List/* ListSkeleton */.cg, {
        size: 10
      })
    }, {
      label: intl.formatMessage({
        id: 'seasonalTab.note'
      }),
      component: function component() {
        return /*#__PURE__*/(0,jsx_runtime.jsx)(SeasonalNote, {
          cropId: detailId,
          cacheKey: cropNoteCacheKey
        });
      },
      extraPage: [
      /*#__PURE__*/
      // <Button key={'delete-ss'}>X\xF3a v\u1EE5 m\xF9a</Button>,
      // <Button key={'create-ss'}>T\u1EA1o v\u1EE5 m\xF9a m\u1EDBi</Button>,
      (0,jsx_runtime.jsx)(Note_Create/* default */.Z, {
        cropId: detailId,
        onSuccess: function onSuccess() {
          setCropNoteCacheKey((0,index_browser/* nanoid */.x0)());
        }
      }, "create-note")],
      fallback: /*#__PURE__*/(0,jsx_runtime.jsx)(List/* ListSkeleton */.cg, {
        size: 10
      })
    }, {
      label: intl.formatMessage({
        id: 'seasonalTab.plantingInformation'
      }),
      component: function component() {
        return /*#__PURE__*/(0,jsx_runtime.jsx)(Detail_Instruct, {
          plantId: detailCrop === null || detailCrop === void 0 ? void 0 : detailCrop.plant_id
        });
      }
    },
    // {
    //   label: intl.formatMessage({ id: 'seasonalTab.cultivationDiary' }),
    //   component: () => <LogOverview />,
    // },
    {
      label: intl.formatMessage({
        id: 'seasonalTab.participants'
      }),
      component: function component() {
        return /*#__PURE__*/(0,jsx_runtime.jsx)(Participants, {
          cropId: detailId
        });
      },
      fallback: /*#__PURE__*/(0,jsx_runtime.jsx)(Descriptions/* TableSkeleton */.hM, {
        active: true,
        size: 10
      })
    }, {
      label: intl.formatMessage({
        id: 'common.ticket'
      }),
      component: function component() {
        return /*#__PURE__*/(0,jsx_runtime.jsx)(Detail_Ticket, {
          cropId: detailId
        });
      },
      fallback: /*#__PURE__*/(0,jsx_runtime.jsx)(Descriptions/* TableSkeleton */.hM, {
        active: true,
        size: 10
      })
    }],
    autoFormatTabKey: true
  });
};
/* harmony default export */ var SeasonalManagement_Detail = (DetailWorkflow);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///97738
`)},52662:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   EH: function() { return /* binding */ getCrop; },
/* harmony export */   Gz: function() { return /* binding */ getCropManagementInfoList; },
/* harmony export */   Kw: function() { return /* binding */ getCropParticipantsTaskList; },
/* harmony export */   NQ: function() { return /* binding */ getCropWorksheetStatistic; },
/* harmony export */   _R: function() { return /* binding */ getCropItemStatistic; },
/* harmony export */   dK: function() { return /* binding */ getCropNote; },
/* harmony export */   e4: function() { return /* binding */ getCropByTask; },
/* harmony export */   hD: function() { return /* binding */ getCropParticipantsStatistic; },
/* harmony export */   qQ: function() { return /* binding */ getCropProductionStatisticDetailTask; },
/* harmony export */   su: function() { return /* binding */ getCropProductionQuantityStatistic; },
/* harmony export */   vx: function() { return /* binding */ getCropItemStatisticDetailTask; },
/* harmony export */   ym: function() { return /* binding */ getCropPest; }
/* harmony export */ });
/* unused harmony export cropList */
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(97857);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(7837);
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(467);





var CRUD_PATH = {
  CREATE: 'crop',
  READ: 'crop',
  UPDATE: 'crop',
  DELETE: 'crop'
};
var getCropManagementInfoList = /*#__PURE__*/function () {
  var _ref = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/cropManage/crop-management-info'), {
            method: 'GET',
            params: params
          });
        case 2:
          res = _context.sent;
          return _context.abrupt("return", {
            data: res.result
          });
        case 4:
        case "end":
          return _context.stop();
      }
    }, _callee);
  }));
  return function getCropManagementInfoList(_x) {
    return _ref.apply(this, arguments);
  };
}();
var getCropByTask = /*#__PURE__*/function () {
  var _ref2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee2(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          _context2.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/cropManage/crop-by-task'), {
            method: 'GET',
            params: params
          });
        case 2:
          res = _context2.sent;
          return _context2.abrupt("return", {
            data: res.result
          });
        case 4:
        case "end":
          return _context2.stop();
      }
    }, _callee2);
  }));
  return function getCropByTask(_x2) {
    return _ref2.apply(this, arguments);
  };
}();
function cropList(_x3) {
  return _cropList.apply(this, arguments);
}
function _cropList() {
  _cropList = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee13(_ref3) {
    var _ref3$page, page, _ref3$size, size, _ref3$fields, fields, _ref3$filters, filters, _ref3$or_filters, or_filters, _ref3$order_by, order_by, _ref3$group_by, group_by, params, result;
    return _regeneratorRuntime().wrap(function _callee13$(_context13) {
      while (1) switch (_context13.prev = _context13.next) {
        case 0:
          _ref3$page = _ref3.page, page = _ref3$page === void 0 ? 0 : _ref3$page, _ref3$size = _ref3.size, size = _ref3$size === void 0 ? 20 : _ref3$size, _ref3$fields = _ref3.fields, fields = _ref3$fields === void 0 ? ['*'] : _ref3$fields, _ref3$filters = _ref3.filters, filters = _ref3$filters === void 0 ? [] : _ref3$filters, _ref3$or_filters = _ref3.or_filters, or_filters = _ref3$or_filters === void 0 ? [] : _ref3$or_filters, _ref3$order_by = _ref3.order_by, order_by = _ref3$order_by === void 0 ? '' : _ref3$order_by, _ref3$group_by = _ref3.group_by, group_by = _ref3$group_by === void 0 ? '' : _ref3$group_by;
          _context13.prev = 1;
          params = {
            page: page,
            size: size,
            fields: JSON.stringify(fields),
            filters: JSON.stringify(filters),
            or_filters: JSON.stringify(or_filters)
            // order_by,
            // group_by
          };
          _context13.next = 5;
          return request(generateAPIPath("api/v2/cropManage/".concat(CRUD_PATH.READ)), {
            method: 'GET',
            params: params,
            queryParams: params
          });
        case 5:
          result = _context13.sent;
          return _context13.abrupt("return", result.result);
        case 9:
          _context13.prev = 9;
          _context13.t0 = _context13["catch"](1);
          console.log(_context13.t0);
          throw _context13.t0;
        case 13:
        case "end":
          return _context13.stop();
      }
    }, _callee13, null, [[1, 9]]);
  }));
  return _cropList.apply(this, arguments);
}
var getCropNote = /*#__PURE__*/function () {
  var _ref4 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee3(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee3$(_context3) {
      while (1) switch (_context3.prev = _context3.next) {
        case 0:
          _context3.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)("api/v2/cropManage/note"), {
            method: 'GET',
            params: params
          });
        case 2:
          res = _context3.sent;
          return _context3.abrupt("return", {
            data: res.result
          });
        case 4:
        case "end":
          return _context3.stop();
      }
    }, _callee3);
  }));
  return function getCropNote(_x4) {
    return _ref4.apply(this, arguments);
  };
}();
var getCropPest = /*#__PURE__*/function () {
  var _ref5 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee4(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee4$(_context4) {
      while (1) switch (_context4.prev = _context4.next) {
        case 0:
          _context4.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)("api/v2/cropManage/pest"), {
            method: 'GET',
            params: params
          });
        case 2:
          res = _context4.sent;
          console.log(' res.result', res.result);
          return _context4.abrupt("return", {
            data: res.result
          });
        case 5:
        case "end":
          return _context4.stop();
      }
    }, _callee4);
  }));
  return function getCropPest(_x5) {
    return _ref5.apply(this, arguments);
  };
}();
var getCrop = /*#__PURE__*/function () {
  var _ref6 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee5(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee5$(_context5) {
      while (1) switch (_context5.prev = _context5.next) {
        case 0:
          _context5.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/cropManage/crop'), {
            method: 'GET',
            params: params
          });
        case 2:
          res = _context5.sent;
          return _context5.abrupt("return", {
            data: res.result
          });
        case 4:
        case "end":
          return _context5.stop();
      }
    }, _callee5);
  }));
  return function getCrop(_x6) {
    return _ref6.apply(this, arguments);
  };
}();
var getCropItemStatistic = /*#__PURE__*/function () {
  var _ref7 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee6(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee6$(_context6) {
      while (1) switch (_context6.prev = _context6.next) {
        case 0:
          _context6.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/cropManage/statisticItem'), {
            method: 'GET',
            params: params
          });
        case 2:
          res = _context6.sent;
          return _context6.abrupt("return", {
            data: res.result
          });
        case 4:
        case "end":
          return _context6.stop();
      }
    }, _callee6);
  }));
  return function getCropItemStatistic(_x7) {
    return _ref7.apply(this, arguments);
  };
}();
var getCropItemStatisticDetailTask = /*#__PURE__*/function () {
  var _ref8 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee7(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee7$(_context7) {
      while (1) switch (_context7.prev = _context7.next) {
        case 0:
          _context7.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/cropManage/statisticItem/detail-in-crop'), {
            method: 'GET',
            params: params
          });
        case 2:
          res = _context7.sent;
          return _context7.abrupt("return", {
            data: res.result.data,
            pagination: res.result.pagination
          });
        case 4:
        case "end":
          return _context7.stop();
      }
    }, _callee7);
  }));
  return function getCropItemStatisticDetailTask(_x8) {
    return _ref8.apply(this, arguments);
  };
}();
var getCropProductionStatisticDetailTask = /*#__PURE__*/function () {
  var _ref9 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee8(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee8$(_context8) {
      while (1) switch (_context8.prev = _context8.next) {
        case 0:
          _context8.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/cropManage/statisticProductQuantity/detail-in-crop'), {
            method: 'GET',
            params: params
          });
        case 2:
          res = _context8.sent;
          return _context8.abrupt("return", {
            data: res.result.data,
            pagination: res.result.pagination
          });
        case 4:
        case "end":
          return _context8.stop();
      }
    }, _callee8);
  }));
  return function getCropProductionStatisticDetailTask(_x9) {
    return _ref9.apply(this, arguments);
  };
}();
var getCropParticipantsStatistic = /*#__PURE__*/function () {
  var _ref10 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee9(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee9$(_context9) {
      while (1) switch (_context9.prev = _context9.next) {
        case 0:
          _context9.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/cropManage/statisticParticipant'), {
            method: 'GET',
            params: params
          });
        case 2:
          res = _context9.sent;
          return _context9.abrupt("return", {
            data: res.result.data,
            pagination: res.result.pagination
          });
        case 4:
        case "end":
          return _context9.stop();
      }
    }, _callee9);
  }));
  return function getCropParticipantsStatistic(_x10) {
    return _ref10.apply(this, arguments);
  };
}();
var getCropParticipantsTaskList = /*#__PURE__*/function () {
  var _ref11 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee10(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee10$(_context10) {
      while (1) switch (_context10.prev = _context10.next) {
        case 0:
          _context10.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/cropManage/statisticParticipant/detail-task-list'), {
            method: 'GET',
            params: params
          });
        case 2:
          res = _context10.sent;
          return _context10.abrupt("return", {
            data: res.result.data,
            pagination: res.result.pagination
          });
        case 4:
        case "end":
          return _context10.stop();
      }
    }, _callee10);
  }));
  return function getCropParticipantsTaskList(_x11) {
    return _ref11.apply(this, arguments);
  };
}();
var getCropProductionQuantityStatistic = /*#__PURE__*/function () {
  var _ref12 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee11(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee11$(_context11) {
      while (1) switch (_context11.prev = _context11.next) {
        case 0:
          _context11.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/cropManage/statisticProductQuantity'), {
            method: 'GET',
            params: params
          });
        case 2:
          res = _context11.sent;
          return _context11.abrupt("return", {
            data: res.result
          });
        case 4:
        case "end":
          return _context11.stop();
      }
    }, _callee11);
  }));
  return function getCropProductionQuantityStatistic(_x12) {
    return _ref12.apply(this, arguments);
  };
}();
var getCropWorksheetStatistic = /*#__PURE__*/function () {
  var _ref13 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee12(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee12$(_context12) {
      while (1) switch (_context12.prev = _context12.next) {
        case 0:
          _context12.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/cropManage/statisticWorksheet'), {
            method: 'GET',
            params: params
          });
        case 2:
          res = _context12.sent;
          return _context12.abrupt("return", {
            data: res.result.map(function (stat) {
              return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default()({}, stat), {}, {
                type: stat.type.toLowerCase() === 'hour' ? 'Gi\u1EDD' : 'C\xF4ng'
              });
            })
          });
        case 4:
        case "end":
          return _context12.stop();
      }
    }, _callee12);
  }));
  return function getCropWorksheetStatistic(_x13) {
    return _ref13.apply(this, arguments);
  };
}();//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///52662
`)},61986:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   EL: function() { return /* binding */ updatePest; },
/* harmony export */   Kx: function() { return /* binding */ updatePestMultipleRelevant; },
/* harmony export */   Xk: function() { return /* binding */ createPestMultipleRelevant; },
/* harmony export */   _l: function() { return /* binding */ createPest; },
/* harmony export */   cO: function() { return /* binding */ getPestList; },
/* harmony export */   et: function() { return /* binding */ deletePest; }
/* harmony export */ });
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(7837);
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(467);




var getPestList = /*#__PURE__*/function () {
  var _ref = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/pest'), {
            method: 'GET',
            params: (0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .getParamsReqList */ .vj)(params)
          });
        case 2:
          res = _context.sent;
          return _context.abrupt("return", res.result);
        case 4:
        case "end":
          return _context.stop();
      }
    }, _callee);
  }));
  return function getPestList(_x) {
    return _ref.apply(this, arguments);
  };
}();
var createPest = /*#__PURE__*/function () {
  var _ref2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee2(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          _context2.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/pest'), {
            method: 'POST',
            data: data
          });
        case 2:
          res = _context2.sent;
          return _context2.abrupt("return", res.result);
        case 4:
        case "end":
          return _context2.stop();
      }
    }, _callee2);
  }));
  return function createPest(_x2) {
    return _ref2.apply(this, arguments);
  };
}();
var createPestMultipleRelevant = /*#__PURE__*/function () {
  var _ref3 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee3(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee3$(_context3) {
      while (1) switch (_context3.prev = _context3.next) {
        case 0:
          _context3.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/pest/multiple-relevant'), {
            method: 'POST',
            data: data
          });
        case 2:
          res = _context3.sent;
          return _context3.abrupt("return", res.result);
        case 4:
        case "end":
          return _context3.stop();
      }
    }, _callee3);
  }));
  return function createPestMultipleRelevant(_x3) {
    return _ref3.apply(this, arguments);
  };
}();
var updatePest = /*#__PURE__*/function () {
  var _ref4 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee4(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee4$(_context4) {
      while (1) switch (_context4.prev = _context4.next) {
        case 0:
          _context4.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/pest'), {
            method: 'PUT',
            data: data
          });
        case 2:
          res = _context4.sent;
          return _context4.abrupt("return", res.result);
        case 4:
        case "end":
          return _context4.stop();
      }
    }, _callee4);
  }));
  return function updatePest(_x4) {
    return _ref4.apply(this, arguments);
  };
}();
var deletePest = /*#__PURE__*/function () {
  var _ref6 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee5(_ref5) {
    var name, res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee5$(_context5) {
      while (1) switch (_context5.prev = _context5.next) {
        case 0:
          name = _ref5.name;
          _context5.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)("api/v2/cropManage/pest?name=".concat(name)), {
            method: 'DELETE'
          });
        case 3:
          res = _context5.sent;
          return _context5.abrupt("return", res.result);
        case 5:
        case "end":
          return _context5.stop();
      }
    }, _callee5);
  }));
  return function deletePest(_x5) {
    return _ref6.apply(this, arguments);
  };
}();
var updatePestMultipleRelevant = /*#__PURE__*/function () {
  var _ref7 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee6(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee6$(_context6) {
      while (1) switch (_context6.prev = _context6.next) {
        case 0:
          _context6.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/pest/multiple-relevant'), {
            method: 'PUT',
            data: data
          });
        case 2:
          res = _context6.sent;
          return _context6.abrupt("return", res.result);
        case 4:
        case "end":
          return _context6.stop();
      }
    }, _callee6);
  }));
  return function updatePestMultipleRelevant(_x6) {
    return _ref7.apply(this, arguments);
  };
}();//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNjE5ODYuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7OztBQUFxQztBQUN1QjtBQWtFckQsSUFBTUcsV0FBVztFQUFBLElBQUFDLElBQUEsR0FBQUMsK0tBQUEsZUFBQUMsaUxBQUEsR0FBQUMsSUFBQSxDQUFHLFNBQUFDLFFBQU9DLE1BQTBCO0lBQUEsSUFBQUMsR0FBQTtJQUFBLE9BQUFKLGlMQUFBLEdBQUFLLElBQUEsVUFBQUMsU0FBQUMsUUFBQTtNQUFBLGtCQUFBQSxRQUFBLENBQUFDLElBQUEsR0FBQUQsUUFBQSxDQUFBRSxJQUFBO1FBQUE7VUFBQUYsUUFBQSxDQUFBRSxJQUFBO1VBQUEsT0FDeENmLG1EQUFPLENBSXZCQyxpRUFBZSxDQUFDLHdCQUF3QixDQUFDLEVBQUU7WUFDM0NlLE1BQU0sRUFBRSxLQUFLO1lBQ2JQLE1BQU0sRUFBRVAsa0VBQWdCLENBQUNPLE1BQU07VUFDakMsQ0FBQyxDQUFDO1FBQUE7VUFQSUMsR0FBRyxHQUFBRyxRQUFBLENBQUFJLElBQUE7VUFBQSxPQUFBSixRQUFBLENBQUFLLE1BQUEsV0FRRlIsR0FBRyxDQUFDUyxNQUFNO1FBQUE7UUFBQTtVQUFBLE9BQUFOLFFBQUEsQ0FBQU8sSUFBQTtNQUFBO0lBQUEsR0FBQVosT0FBQTtFQUFBLENBQ2xCO0VBQUEsZ0JBVllMLFdBQVdBLENBQUFrQixFQUFBO0lBQUEsT0FBQWpCLElBQUEsQ0FBQWtCLEtBQUEsT0FBQUMsU0FBQTtFQUFBO0FBQUEsR0FVdkI7QUFFTSxJQUFNQyxVQUFVO0VBQUEsSUFBQUMsS0FBQSxHQUFBcEIsK0tBQUEsZUFBQUMsaUxBQUEsR0FBQUMsSUFBQSxDQUFHLFNBQUFtQixTQUFPQyxJQUFvQjtJQUFBLElBQUFqQixHQUFBO0lBQUEsT0FBQUosaUxBQUEsR0FBQUssSUFBQSxVQUFBaUIsVUFBQUMsU0FBQTtNQUFBLGtCQUFBQSxTQUFBLENBQUFmLElBQUEsR0FBQWUsU0FBQSxDQUFBZCxJQUFBO1FBQUE7VUFBQWMsU0FBQSxDQUFBZCxJQUFBO1VBQUEsT0FDakNmLG1EQUFPLENBSXZCQyxpRUFBZSxDQUFDLHdCQUF3QixDQUFDLEVBQUU7WUFDM0NlLE1BQU0sRUFBRSxNQUFNO1lBQ2RXLElBQUksRUFBSkE7VUFDRixDQUFDLENBQUM7UUFBQTtVQVBJakIsR0FBRyxHQUFBbUIsU0FBQSxDQUFBWixJQUFBO1VBQUEsT0FBQVksU0FBQSxDQUFBWCxNQUFBLFdBUUZSLEdBQUcsQ0FBQ1MsTUFBTTtRQUFBO1FBQUE7VUFBQSxPQUFBVSxTQUFBLENBQUFULElBQUE7TUFBQTtJQUFBLEdBQUFNLFFBQUE7RUFBQSxDQUNsQjtFQUFBLGdCQVZZRixVQUFVQSxDQUFBTSxHQUFBO0lBQUEsT0FBQUwsS0FBQSxDQUFBSCxLQUFBLE9BQUFDLFNBQUE7RUFBQTtBQUFBLEdBVXRCO0FBRU0sSUFBTVEsMEJBQTBCO0VBQUEsSUFBQUMsS0FBQSxHQUFBM0IsK0tBQUEsZUFBQUMsaUxBQUEsR0FBQUMsSUFBQSxDQUFHLFNBQUEwQixTQUFPTixJQUFzQjtJQUFBLElBQUFqQixHQUFBO0lBQUEsT0FBQUosaUxBQUEsR0FBQUssSUFBQSxVQUFBdUIsVUFBQUMsU0FBQTtNQUFBLGtCQUFBQSxTQUFBLENBQUFyQixJQUFBLEdBQUFxQixTQUFBLENBQUFwQixJQUFBO1FBQUE7VUFBQW9CLFNBQUEsQ0FBQXBCLElBQUE7VUFBQSxPQUNuRGYsbURBQU8sQ0FJdkJDLGlFQUFlLENBQUMsMENBQTBDLENBQUMsRUFBRTtZQUM3RGUsTUFBTSxFQUFFLE1BQU07WUFDZFcsSUFBSSxFQUFKQTtVQUNGLENBQUMsQ0FBQztRQUFBO1VBUElqQixHQUFHLEdBQUF5QixTQUFBLENBQUFsQixJQUFBO1VBQUEsT0FBQWtCLFNBQUEsQ0FBQWpCLE1BQUEsV0FRRlIsR0FBRyxDQUFDUyxNQUFNO1FBQUE7UUFBQTtVQUFBLE9BQUFnQixTQUFBLENBQUFmLElBQUE7TUFBQTtJQUFBLEdBQUFhLFFBQUE7RUFBQSxDQUNsQjtFQUFBLGdCQVZZRiwwQkFBMEJBLENBQUFLLEdBQUE7SUFBQSxPQUFBSixLQUFBLENBQUFWLEtBQUEsT0FBQUMsU0FBQTtFQUFBO0FBQUEsR0FVdEM7QUFFTSxJQUFNYyxVQUFVO0VBQUEsSUFBQUMsS0FBQSxHQUFBakMsK0tBQUEsZUFBQUMsaUxBQUEsR0FBQUMsSUFBQSxDQUFHLFNBQUFnQyxTQUFPWixJQUFvQjtJQUFBLElBQUFqQixHQUFBO0lBQUEsT0FBQUosaUxBQUEsR0FBQUssSUFBQSxVQUFBNkIsVUFBQUMsU0FBQTtNQUFBLGtCQUFBQSxTQUFBLENBQUEzQixJQUFBLEdBQUEyQixTQUFBLENBQUExQixJQUFBO1FBQUE7VUFBQTBCLFNBQUEsQ0FBQTFCLElBQUE7VUFBQSxPQUNqQ2YsbURBQU8sQ0FJdkJDLGlFQUFlLENBQUMsd0JBQXdCLENBQUMsRUFBRTtZQUMzQ2UsTUFBTSxFQUFFLEtBQUs7WUFDYlcsSUFBSSxFQUFKQTtVQUNGLENBQUMsQ0FBQztRQUFBO1VBUElqQixHQUFHLEdBQUErQixTQUFBLENBQUF4QixJQUFBO1VBQUEsT0FBQXdCLFNBQUEsQ0FBQXZCLE1BQUEsV0FRRlIsR0FBRyxDQUFDUyxNQUFNO1FBQUE7UUFBQTtVQUFBLE9BQUFzQixTQUFBLENBQUFyQixJQUFBO01BQUE7SUFBQSxHQUFBbUIsUUFBQTtFQUFBLENBQ2xCO0VBQUEsZ0JBVllGLFVBQVVBLENBQUFLLEdBQUE7SUFBQSxPQUFBSixLQUFBLENBQUFoQixLQUFBLE9BQUFDLFNBQUE7RUFBQTtBQUFBLEdBVXRCO0FBRU0sSUFBTW9CLFVBQVU7RUFBQSxJQUFBQyxLQUFBLEdBQUF2QywrS0FBQSxlQUFBQyxpTEFBQSxHQUFBQyxJQUFBLENBQUcsU0FBQXNDLFNBQUFDLEtBQUE7SUFBQSxJQUFBQyxJQUFBLEVBQUFyQyxHQUFBO0lBQUEsT0FBQUosaUxBQUEsR0FBQUssSUFBQSxVQUFBcUMsVUFBQUMsU0FBQTtNQUFBLGtCQUFBQSxTQUFBLENBQUFuQyxJQUFBLEdBQUFtQyxTQUFBLENBQUFsQyxJQUFBO1FBQUE7VUFBU2dDLElBQUksR0FBQUQsS0FBQSxDQUFKQyxJQUFJO1VBQUFFLFNBQUEsQ0FBQWxDLElBQUE7VUFBQSxPQUNuQmYsbURBQU8sQ0FLdkJDLGlFQUFlLGdDQUFBaUQsTUFBQSxDQUFnQ0gsSUFBSSxDQUFFLENBQUMsRUFBRTtZQUN4RC9CLE1BQU0sRUFBRTtVQUNWLENBQUMsQ0FBQztRQUFBO1VBUElOLEdBQUcsR0FBQXVDLFNBQUEsQ0FBQWhDLElBQUE7VUFBQSxPQUFBZ0MsU0FBQSxDQUFBL0IsTUFBQSxXQVFGUixHQUFHLENBQUNTLE1BQU07UUFBQTtRQUFBO1VBQUEsT0FBQThCLFNBQUEsQ0FBQTdCLElBQUE7TUFBQTtJQUFBLEdBQUF5QixRQUFBO0VBQUEsQ0FDbEI7RUFBQSxnQkFWWUYsVUFBVUEsQ0FBQVEsR0FBQTtJQUFBLE9BQUFQLEtBQUEsQ0FBQXRCLEtBQUEsT0FBQUMsU0FBQTtFQUFBO0FBQUEsR0FVdEI7QUFDTSxJQUFNNkIsMEJBQTBCO0VBQUEsSUFBQUMsS0FBQSxHQUFBaEQsK0tBQUEsZUFBQUMsaUxBQUEsR0FBQUMsSUFBQSxDQUFHLFNBQUErQyxTQUFPM0IsSUFBc0I7SUFBQSxJQUFBakIsR0FBQTtJQUFBLE9BQUFKLGlMQUFBLEdBQUFLLElBQUEsVUFBQTRDLFVBQUFDLFNBQUE7TUFBQSxrQkFBQUEsU0FBQSxDQUFBMUMsSUFBQSxHQUFBMEMsU0FBQSxDQUFBekMsSUFBQTtRQUFBO1VBQUF5QyxTQUFBLENBQUF6QyxJQUFBO1VBQUEsT0FDbkRmLG1EQUFPLENBSXZCQyxpRUFBZSxDQUFDLDBDQUEwQyxDQUFDLEVBQUU7WUFDN0RlLE1BQU0sRUFBRSxLQUFLO1lBQ2JXLElBQUksRUFBSkE7VUFDRixDQUFDLENBQUM7UUFBQTtVQVBJakIsR0FBRyxHQUFBOEMsU0FBQSxDQUFBdkMsSUFBQTtVQUFBLE9BQUF1QyxTQUFBLENBQUF0QyxNQUFBLFdBUUZSLEdBQUcsQ0FBQ1MsTUFBTTtRQUFBO1FBQUE7VUFBQSxPQUFBcUMsU0FBQSxDQUFBcEMsSUFBQTtNQUFBO0lBQUEsR0FBQWtDLFFBQUE7RUFBQSxDQUNsQjtFQUFBLGdCQVZZRiwwQkFBMEJBLENBQUFLLEdBQUE7SUFBQSxPQUFBSixLQUFBLENBQUEvQixLQUFBLE9BQUFDLFNBQUE7RUFBQTtBQUFBLEdBVXRDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9zcmMvc2VydmljZXMvcGFuZGVtaWMudHM/ZDYzMCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyByZXF1ZXN0IH0gZnJvbSAnQHVtaWpzL21heCc7XHJcbmltcG9ydCB7IGdlbmVyYXRlQVBJUGF0aCwgZ2V0UGFyYW1zUmVxTGlzdCB9IGZyb20gJy4vdXRpbHMnO1xyXG5cclxuZXhwb3J0IGludGVyZmFjZSBJUGVzdFJlcyB7XHJcbiAgbmFtZTogc3RyaW5nO1xyXG4gIGNyZWF0aW9uOiBzdHJpbmc7XHJcbiAgbW9kaWZpZWQ6IHN0cmluZztcclxuICBtb2RpZmllZF9ieTogc3RyaW5nO1xyXG4gIG93bmVyOiBzdHJpbmc7XHJcbiAgZG9jc3RhdHVzOiBudW1iZXI7XHJcbiAgaWR4OiBudW1iZXI7XHJcbiAgZmFybWluZ19wbGFuX3N0YXRlOiBhbnk7XHJcbiAgbGFiZWw6IHN0cmluZztcclxuICBkZXNjcmlwdGlvbjogc3RyaW5nO1xyXG4gIF91c2VyX3RhZ3M6IGFueTtcclxuICBfY29tbWVudHM6IGFueTtcclxuICBfYXNzaWduOiBhbnk7XHJcbiAgX2xpa2VkX2J5OiBhbnk7XHJcbiAgY3JvcDogYW55O1xyXG4gIGltYWdlOiBzdHJpbmc7XHJcbiAgY3JvcF9uYW1lOiBzdHJpbmc7XHJcbiAgaW90X2Nyb3A6IHN0cmluZztcclxuICBzdGF0ZV9saXN0OiBzdHJpbmdbXTtcclxuICBjYXRlZ29yeV9saXN0OiBzdHJpbmdbXTtcclxuICBpbnZvbHZlZF9pbl91c2Vyczogc3RyaW5nW107XHJcbn1cclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgUGVzdFJlbGV2YW50Qm9keSB7XHJcbiAgbGFiZWw/OiBzdHJpbmc7XHJcbiAgbmFtZTogc3RyaW5nO1xyXG4gIGlvdF9jcm9wPzogc3RyaW5nO1xyXG4gIGNhdGVnb3J5X2xpc3Q6IHN0cmluZ1tdO1xyXG4gIHN0YXRlX2xpc3Q6IHN0cmluZ1tdO1xyXG4gIGludm9sdmVkX2luX3VzZXJzOiBzdHJpbmdbXTtcclxufVxyXG5cclxuZXhwb3J0IHR5cGUgSUNyZWF0ZVBlc3RSZXEgPSB7XHJcbiAgaW90X2Nyb3A6IHN0cmluZztcclxuICBsYWJlbDogc3RyaW5nO1xyXG4gIGRlc2NyaXB0aW9uPzogc3RyaW5nO1xyXG4gIGltYWdlPzogc3RyaW5nO1xyXG59O1xyXG5leHBvcnQgaW50ZXJmYWNlIElDcmVhdGVQZXN0UmVzIHtcclxuICBuYW1lOiBzdHJpbmc7XHJcbiAgb3duZXI6IHN0cmluZztcclxuICBjcmVhdGlvbjogc3RyaW5nO1xyXG4gIG1vZGlmaWVkOiBzdHJpbmc7XHJcbiAgbW9kaWZpZWRfYnk6IHN0cmluZztcclxuICBkb2NzdGF0dXM6IG51bWJlcjtcclxuICBpZHg6IG51bWJlcjtcclxuICBpb3RfY3JvcDogc3RyaW5nO1xyXG4gIGNyb3BfbmFtZTogc3RyaW5nO1xyXG4gIGxhYmVsOiBzdHJpbmc7XHJcbiAgZGVzY3JpcHRpb246IHN0cmluZztcclxuICBpbWFnZTogc3RyaW5nO1xyXG4gIGRvY3R5cGU6IHN0cmluZztcclxufVxyXG5cclxuZXhwb3J0IGludGVyZmFjZSBJVXBkYXRlUGVzdFJlcSB7XHJcbiAgbmFtZTogc3RyaW5nO1xyXG4gIGxhYmVsPzogc3RyaW5nO1xyXG4gIGRlc2NyaXB0aW9uPzogc3RyaW5nO1xyXG4gIGltYWdlPzogc3RyaW5nO1xyXG4gIGNyb3BfbmFtZT86IHN0cmluZztcclxuICBpb3RfY3JvcDogc3RyaW5nO1xyXG59XHJcblxyXG5leHBvcnQgY29uc3QgZ2V0UGVzdExpc3QgPSBhc3luYyAocGFyYW1zPzogQVBJLkxpc3RQYXJhbXNSZXEpID0+IHtcclxuICBjb25zdCByZXMgPSBhd2FpdCByZXF1ZXN0PFxyXG4gICAgQVBJLlJlc3BvbnNlUmVzdWx0PHtcclxuICAgICAgZGF0YTogSVBlc3RSZXNbXTtcclxuICAgIH0+XHJcbiAgPihnZW5lcmF0ZUFQSVBhdGgoJ2FwaS92Mi9jcm9wTWFuYWdlL3Blc3QnKSwge1xyXG4gICAgbWV0aG9kOiAnR0VUJyxcclxuICAgIHBhcmFtczogZ2V0UGFyYW1zUmVxTGlzdChwYXJhbXMpLFxyXG4gIH0pO1xyXG4gIHJldHVybiByZXMucmVzdWx0O1xyXG59O1xyXG5cclxuZXhwb3J0IGNvbnN0IGNyZWF0ZVBlc3QgPSBhc3luYyAoZGF0YTogSUNyZWF0ZVBlc3RSZXEpID0+IHtcclxuICBjb25zdCByZXMgPSBhd2FpdCByZXF1ZXN0PFxyXG4gICAgQVBJLlJlc3BvbnNlUmVzdWx0PHtcclxuICAgICAgZGF0YTogSUNyZWF0ZVBlc3RSZXM7XHJcbiAgICB9PlxyXG4gID4oZ2VuZXJhdGVBUElQYXRoKCdhcGkvdjIvY3JvcE1hbmFnZS9wZXN0JyksIHtcclxuICAgIG1ldGhvZDogJ1BPU1QnLFxyXG4gICAgZGF0YSxcclxuICB9KTtcclxuICByZXR1cm4gcmVzLnJlc3VsdDtcclxufTtcclxuXHJcbmV4cG9ydCBjb25zdCBjcmVhdGVQZXN0TXVsdGlwbGVSZWxldmFudCA9IGFzeW5jIChkYXRhOiBQZXN0UmVsZXZhbnRCb2R5KSA9PiB7XHJcbiAgY29uc3QgcmVzID0gYXdhaXQgcmVxdWVzdDxcclxuICAgIEFQSS5SZXNwb25zZVJlc3VsdDx7XHJcbiAgICAgIGRhdGE6IElDcmVhdGVQZXN0UmVzO1xyXG4gICAgfT5cclxuICA+KGdlbmVyYXRlQVBJUGF0aCgnYXBpL3YyL2Nyb3BNYW5hZ2UvcGVzdC9tdWx0aXBsZS1yZWxldmFudCcpLCB7XHJcbiAgICBtZXRob2Q6ICdQT1NUJyxcclxuICAgIGRhdGEsXHJcbiAgfSk7XHJcbiAgcmV0dXJuIHJlcy5yZXN1bHQ7XHJcbn07XHJcblxyXG5leHBvcnQgY29uc3QgdXBkYXRlUGVzdCA9IGFzeW5jIChkYXRhOiBJVXBkYXRlUGVzdFJlcSkgPT4ge1xyXG4gIGNvbnN0IHJlcyA9IGF3YWl0IHJlcXVlc3Q8XHJcbiAgICBBUEkuUmVzcG9uc2VSZXN1bHQ8e1xyXG4gICAgICBkYXRhOiBJUGVzdFJlcztcclxuICAgIH0+XHJcbiAgPihnZW5lcmF0ZUFQSVBhdGgoJ2FwaS92Mi9jcm9wTWFuYWdlL3Blc3QnKSwge1xyXG4gICAgbWV0aG9kOiAnUFVUJyxcclxuICAgIGRhdGEsXHJcbiAgfSk7XHJcbiAgcmV0dXJuIHJlcy5yZXN1bHQ7XHJcbn07XHJcblxyXG5leHBvcnQgY29uc3QgZGVsZXRlUGVzdCA9IGFzeW5jICh7IG5hbWUgfTogeyBuYW1lOiBzdHJpbmcgfSkgPT4ge1xyXG4gIGNvbnN0IHJlcyA9IGF3YWl0IHJlcXVlc3Q8XHJcbiAgICBBUEkuUmVzcG9uc2VSZXN1bHQ8e1xyXG4gICAgICBleGNfdHlwZTogJ0RvZXNOb3RFeGlzdEVycm9yJztcclxuICAgICAgbWVzc2FnZTogJ29rJztcclxuICAgIH0+XHJcbiAgPihnZW5lcmF0ZUFQSVBhdGgoYGFwaS92Mi9jcm9wTWFuYWdlL3Blc3Q/bmFtZT0ke25hbWV9YCksIHtcclxuICAgIG1ldGhvZDogJ0RFTEVURScsXHJcbiAgfSk7XHJcbiAgcmV0dXJuIHJlcy5yZXN1bHQ7XHJcbn07XHJcbmV4cG9ydCBjb25zdCB1cGRhdGVQZXN0TXVsdGlwbGVSZWxldmFudCA9IGFzeW5jIChkYXRhOiBQZXN0UmVsZXZhbnRCb2R5KSA9PiB7XHJcbiAgY29uc3QgcmVzID0gYXdhaXQgcmVxdWVzdDxcclxuICAgIEFQSS5SZXNwb25zZVJlc3VsdDx7XHJcbiAgICAgIGRhdGE6IElDcmVhdGVQZXN0UmVzO1xyXG4gICAgfT5cclxuICA+KGdlbmVyYXRlQVBJUGF0aCgnYXBpL3YyL2Nyb3BNYW5hZ2UvcGVzdC9tdWx0aXBsZS1yZWxldmFudCcpLCB7XHJcbiAgICBtZXRob2Q6ICdQVVQnLFxyXG4gICAgZGF0YSxcclxuICB9KTtcclxuICByZXR1cm4gcmVzLnJlc3VsdDtcclxufTtcclxuIl0sIm5hbWVzIjpbInJlcXVlc3QiLCJnZW5lcmF0ZUFQSVBhdGgiLCJnZXRQYXJhbXNSZXFMaXN0IiwiZ2V0UGVzdExpc3QiLCJfcmVmIiwiX2FzeW5jVG9HZW5lcmF0b3IiLCJfcmVnZW5lcmF0b3JSdW50aW1lIiwibWFyayIsIl9jYWxsZWUiLCJwYXJhbXMiLCJyZXMiLCJ3cmFwIiwiX2NhbGxlZSQiLCJfY29udGV4dCIsInByZXYiLCJuZXh0IiwibWV0aG9kIiwic2VudCIsImFicnVwdCIsInJlc3VsdCIsInN0b3AiLCJfeCIsImFwcGx5IiwiYXJndW1lbnRzIiwiY3JlYXRlUGVzdCIsIl9yZWYyIiwiX2NhbGxlZTIiLCJkYXRhIiwiX2NhbGxlZTIkIiwiX2NvbnRleHQyIiwiX3gyIiwiY3JlYXRlUGVzdE11bHRpcGxlUmVsZXZhbnQiLCJfcmVmMyIsIl9jYWxsZWUzIiwiX2NhbGxlZTMkIiwiX2NvbnRleHQzIiwiX3gzIiwidXBkYXRlUGVzdCIsIl9yZWY0IiwiX2NhbGxlZTQiLCJfY2FsbGVlNCQiLCJfY29udGV4dDQiLCJfeDQiLCJkZWxldGVQZXN0IiwiX3JlZjYiLCJfY2FsbGVlNSIsIl9yZWY1IiwibmFtZSIsIl9jYWxsZWU1JCIsIl9jb250ZXh0NSIsImNvbmNhdCIsIl94NSIsInVwZGF0ZVBlc3RNdWx0aXBsZVJlbGV2YW50IiwiX3JlZjciLCJfY2FsbGVlNiIsIl9jYWxsZWU2JCIsIl9jb250ZXh0NiIsIl94NiJdLCJzb3VyY2VSb290IjoiIn0=
//# sourceURL=webpack-internal:///61986
`)},44045:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: function() { return /* binding */ getPlantUserOwnerAllResources; }
/* harmony export */ });
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(97857);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectWithoutProperties_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(13769);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectWithoutProperties_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectWithoutProperties_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(7837);
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(467);




var _excluded = ["plant_id"];


var getPlantUserOwnerAllResources = /*#__PURE__*/function () {
  var _ref2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee(_ref) {
    var plant_id, params, res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          plant_id = _ref.plant_id, params = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectWithoutProperties_js__WEBPACK_IMPORTED_MODULE_2___default()(_ref, _excluded);
          _context.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_4__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_5__/* .generateAPIPath */ .rH)("api/v2/plantManage/plant/allResources"), {
            method: 'GET',
            params: D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({
              plant_id: plant_id
            }, (0,_utils__WEBPACK_IMPORTED_MODULE_5__/* .getParamsReqList */ .vj)(params))
          });
        case 3:
          res = _context.sent;
          return _context.abrupt("return", {
            data: res.result.data,
            pagination: res.result.pagination
          });
        case 5:
        case "end":
          return _context.stop();
      }
    }, _callee);
  }));
  return function getPlantUserOwnerAllResources(_x) {
    return _ref2.apply(this, arguments);
  };
}();//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///44045
`)},1743:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   O: function() { return /* binding */ updateTicket; },
/* harmony export */   Tw: function() { return /* binding */ createTicketMessage; },
/* harmony export */   ax: function() { return /* binding */ createTicket; },
/* harmony export */   tZ: function() { return /* binding */ getTicketList; }
/* harmony export */ });
/* unused harmony export deleteTicket */
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(7837);
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(467);




var getTicketList = /*#__PURE__*/function () {
  var _ref = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/ticket'), {
            method: 'GET',
            params: (0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .getParamsReqList */ .vj)(params)
          });
        case 2:
          res = _context.sent;
          return _context.abrupt("return", res.result);
        case 4:
        case "end":
          return _context.stop();
      }
    }, _callee);
  }));
  return function getTicketList(_x) {
    return _ref.apply(this, arguments);
  };
}();
var createTicket = /*#__PURE__*/function () {
  var _ref2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee2(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          _context2.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/ticket'), {
            method: 'POST',
            data: data
          });
        case 2:
          res = _context2.sent;
          return _context2.abrupt("return", res.result);
        case 4:
        case "end":
          return _context2.stop();
      }
    }, _callee2);
  }));
  return function createTicket(_x2) {
    return _ref2.apply(this, arguments);
  };
}();
var updateTicket = /*#__PURE__*/function () {
  var _ref3 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee3(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee3$(_context3) {
      while (1) switch (_context3.prev = _context3.next) {
        case 0:
          _context3.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/ticket'), {
            method: 'PUT',
            data: data
          });
        case 2:
          res = _context3.sent;
          return _context3.abrupt("return", res.result);
        case 4:
        case "end":
          return _context3.stop();
      }
    }, _callee3);
  }));
  return function updateTicket(_x3) {
    return _ref3.apply(this, arguments);
  };
}();
var deleteTicket = /*#__PURE__*/(/* unused pure expression or super */ null && (function () {
  var _ref5 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee4(_ref4) {
    var name, res;
    return _regeneratorRuntime().wrap(function _callee4$(_context4) {
      while (1) switch (_context4.prev = _context4.next) {
        case 0:
          name = _ref4.name;
          _context4.next = 3;
          return request(generateAPIPath("api/v2/cropManage/pest?name=".concat(name)), {
            method: 'DELETE'
          });
        case 3:
          res = _context4.sent;
          return _context4.abrupt("return", res.result);
        case 5:
        case "end":
          return _context4.stop();
      }
    }, _callee4);
  }));
  return function deleteTicket(_x4) {
    return _ref5.apply(this, arguments);
  };
}()));
var createTicketMessage = /*#__PURE__*/function () {
  var _ref6 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee5(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee5$(_context5) {
      while (1) switch (_context5.prev = _context5.next) {
        case 0:
          _context5.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/ticket/message'), {
            method: 'POST',
            data: data
          });
        case 2:
          res = _context5.sent;
          return _context5.abrupt("return", res.result);
        case 4:
        case "end":
          return _context5.stop();
      }
    }, _callee5);
  }));
  return function createTicketMessage(_x5) {
    return _ref6.apply(this, arguments);
  };
}();//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///1743
`)},33217:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: function() { return /* binding */ useSelectedTicketStore; },
/* harmony export */   F: function() { return /* binding */ useTicketListStore; }
/* harmony export */ });
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _services_ticket__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(1743);
/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(64529);




// Define a store for the ticket list
var useTicketListStore = (0,zustand__WEBPACK_IMPORTED_MODULE_3__/* .create */ .Ue)(function (set, get) {
  return {
    tickets: [],
    setTickets: function setTickets(tickets) {
      console.log('Setting tickets:', tickets); // Log the new state
      set({
        tickets: tickets
      });
    },
    fetchTickets: function () {
      var _fetchTickets = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee(cropId) {
        var res;
        return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee$(_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              _context.next = 2;
              return (0,_services_ticket__WEBPACK_IMPORTED_MODULE_2__/* .getTicketList */ .tZ)({
                page: 1,
                size: 10000,
                filters: [['iot_ticket', 'crop_id', 'like', cropId]]
              });
            case 2:
              res = _context.sent;
              console.log('fetchTickets tickets:', res.data); // Log the new state
              set({
                tickets: res.data
              });
            case 5:
            case "end":
              return _context.stop();
          }
        }, _callee);
      }));
      function fetchTickets(_x) {
        return _fetchTickets.apply(this, arguments);
      }
      return fetchTickets;
    }(),
    getTicketMessages: function getTicketMessages(ticketId) {
      var ticket = get().tickets.find(function (ticket) {
        return ticket.name === ticketId;
      });
      var returnMessages = ticket !== null && ticket !== void 0 && ticket.messages ? ticket.messages : [];
      return returnMessages;
    }
  };
});

// Define a store for the selected ticket
var useSelectedTicketStore = (0,zustand__WEBPACK_IMPORTED_MODULE_3__/* .create */ .Ue)(function (set) {
  return {
    selectedTicket: {},
    viewingDetails: false,
    setSelectedTicket: function setSelectedTicket(ticket) {
      console.log('Setting selectedTicket:', ticket); // Log the new state
      set({
        selectedTicket: ticket
      });
    },
    setViewingDetails: function setViewingDetails(viewing) {
      console.log('Setting viewingDetails:', viewing); // Log the new state
      set({
        viewingDetails: viewing
      });
    }
  };
});//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiMzMyMTcuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQWtEO0FBRWpCO0FBZ0JqQztBQUNPLElBQU1FLGtCQUFrQixHQUFHRCx5REFBTSxDQUFhLFVBQUNFLEdBQUcsRUFBRUMsR0FBRztFQUFBLE9BQU07SUFDbEVDLE9BQU8sRUFBRSxFQUFFO0lBQ1hDLFVBQVUsRUFBRSxTQUFBQSxXQUFDRCxPQUFxQixFQUFLO01BQ3JDRSxPQUFPLENBQUNDLEdBQUcsQ0FBQyxrQkFBa0IsRUFBRUgsT0FBTyxDQUFDLENBQUMsQ0FBQztNQUMxQ0YsR0FBRyxDQUFDO1FBQUVFLE9BQU8sRUFBUEE7TUFBUSxDQUFDLENBQUM7SUFDbEIsQ0FBQztJQUNESSxZQUFZO01BQUEsSUFBQUMsYUFBQSxHQUFBQywrS0FBQSxlQUFBQyxpTEFBQSxHQUFBQyxJQUFBLENBQUUsU0FBQUMsUUFBT0MsTUFBYztRQUFBLElBQUFDLEdBQUE7UUFBQSxPQUFBSixpTEFBQSxHQUFBSyxJQUFBLFVBQUFDLFNBQUFDLFFBQUE7VUFBQSxrQkFBQUEsUUFBQSxDQUFBQyxJQUFBLEdBQUFELFFBQUEsQ0FBQUUsSUFBQTtZQUFBO2NBQUFGLFFBQUEsQ0FBQUUsSUFBQTtjQUFBLE9BQ2ZyQix5RUFBYSxDQUFDO2dCQUM5QnNCLElBQUksRUFBRSxDQUFDO2dCQUNQQyxJQUFJLEVBQUUsS0FBSztnQkFDWEMsT0FBTyxFQUFFLENBQUMsQ0FBQyxZQUFZLEVBQUUsU0FBUyxFQUFFLE1BQU0sRUFBRVQsTUFBTSxDQUFDO2NBQ3JELENBQUMsQ0FBQztZQUFBO2NBSklDLEdBQUcsR0FBQUcsUUFBQSxDQUFBTSxJQUFBO2NBS1RsQixPQUFPLENBQUNDLEdBQUcsQ0FBQyx1QkFBdUIsRUFBRVEsR0FBRyxDQUFDVSxJQUFJLENBQUMsQ0FBQyxDQUFDO2NBQ2hEdkIsR0FBRyxDQUFDO2dCQUFFRSxPQUFPLEVBQUVXLEdBQUcsQ0FBQ1U7Y0FBSyxDQUFDLENBQUM7WUFBQztZQUFBO2NBQUEsT0FBQVAsUUFBQSxDQUFBUSxJQUFBO1VBQUE7UUFBQSxHQUFBYixPQUFBO01BQUEsQ0FDNUI7TUFBQSxTQUFBTCxhQUFBbUIsRUFBQTtRQUFBLE9BQUFsQixhQUFBLENBQUFtQixLQUFBLE9BQUFDLFNBQUE7TUFBQTtNQUFBLE9BQUFyQixZQUFBO0lBQUE7SUFDRHNCLGlCQUFpQixFQUFFLFNBQUFBLGtCQUFDQyxRQUFnQixFQUFLO01BQ3ZDLElBQU1DLE1BQU0sR0FBRzdCLEdBQUcsQ0FBQyxDQUFDLENBQUNDLE9BQU8sQ0FBQzZCLElBQUksQ0FBQyxVQUFDRCxNQUFNO1FBQUEsT0FBS0EsTUFBTSxDQUFDRSxJQUFJLEtBQUtILFFBQVE7TUFBQSxFQUFDO01BQ3ZFLElBQU1JLGNBQWMsR0FBR0gsTUFBTSxhQUFOQSxNQUFNLGVBQU5BLE1BQU0sQ0FBRUksUUFBUSxHQUFHSixNQUFNLENBQUNJLFFBQVEsR0FBRyxFQUFFO01BQzlELE9BQU9ELGNBQWM7SUFDdkI7RUFDRixDQUFDO0FBQUEsQ0FBQyxDQUFDOztBQUVIO0FBQ08sSUFBTUUsc0JBQXNCLEdBQUdyQyx5REFBTSxDQUFpQixVQUFDRSxHQUFHO0VBQUEsT0FBTTtJQUNyRW9DLGNBQWMsRUFBRSxDQUFDLENBQWU7SUFDaENDLGNBQWMsRUFBRSxLQUFLO0lBQ3JCQyxpQkFBaUIsRUFBRSxTQUFBQSxrQkFBQ1IsTUFBa0IsRUFBSztNQUN6QzFCLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLHlCQUF5QixFQUFFeUIsTUFBTSxDQUFDLENBQUMsQ0FBQztNQUNoRDlCLEdBQUcsQ0FBQztRQUFFb0MsY0FBYyxFQUFFTjtNQUFPLENBQUMsQ0FBQztJQUNqQyxDQUFDO0lBQ0RTLGlCQUFpQixFQUFFLFNBQUFBLGtCQUFDQyxPQUFnQixFQUFLO01BQ3ZDcEMsT0FBTyxDQUFDQyxHQUFHLENBQUMseUJBQXlCLEVBQUVtQyxPQUFPLENBQUMsQ0FBQyxDQUFDO01BQ2pEeEMsR0FBRyxDQUFDO1FBQUVxQyxjQUFjLEVBQUVHO01BQVEsQ0FBQyxDQUFDO0lBQ2xDO0VBQ0YsQ0FBQztBQUFBLENBQUMsQ0FBQyIsInNvdXJjZXMiOlsid2VicGFjazovL2FudC1kZXNpZ24tcHJvLy4vc3JjL3N0b3Jlcy9UaWNrZXRTdG9yZS50c3g/M2YwOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBnZXRUaWNrZXRMaXN0IH0gZnJvbSAnQC9zZXJ2aWNlcy90aWNrZXQnO1xyXG5pbXBvcnQgeyBJSW90VGlja2V0IH0gZnJvbSAnQC90eXBlcy9JSW90VGlja2V0JztcclxuaW1wb3J0IHsgY3JlYXRlIH0gZnJvbSAnenVzdGFuZCc7XHJcblxyXG50eXBlIFRpY2tldExpc3QgPSB7XHJcbiAgdGlja2V0czogSUlvdFRpY2tldFtdO1xyXG4gIHNldFRpY2tldHM6IChzdGF0ZTogSUlvdFRpY2tldFtdKSA9PiB2b2lkO1xyXG4gIGZldGNoVGlja2V0czogKGNyb3BJZDogc3RyaW5nKSA9PiB2b2lkO1xyXG4gIGdldFRpY2tldE1lc3NhZ2VzOiAodGlja2V0SWQ6IHN0cmluZykgPT4gYW55W107XHJcbn07XHJcblxyXG50eXBlIFNlbGVjdGVkVGlja2V0ID0ge1xyXG4gIHNlbGVjdGVkVGlja2V0OiBJSW90VGlja2V0O1xyXG4gIHNldFNlbGVjdGVkVGlja2V0OiAoc3RhdGU6IElJb3RUaWNrZXQpID0+IHZvaWQ7XHJcbiAgdmlld2luZ0RldGFpbHM6IGJvb2xlYW47XHJcbiAgc2V0Vmlld2luZ0RldGFpbHM6IChzdGF0ZTogYm9vbGVhbikgPT4gdm9pZDtcclxufTtcclxuXHJcbi8vIERlZmluZSBhIHN0b3JlIGZvciB0aGUgdGlja2V0IGxpc3RcclxuZXhwb3J0IGNvbnN0IHVzZVRpY2tldExpc3RTdG9yZSA9IGNyZWF0ZTxUaWNrZXRMaXN0Pigoc2V0LCBnZXQpID0+ICh7XHJcbiAgdGlja2V0czogW10sXHJcbiAgc2V0VGlja2V0czogKHRpY2tldHM6IElJb3RUaWNrZXRbXSkgPT4ge1xyXG4gICAgY29uc29sZS5sb2coJ1NldHRpbmcgdGlja2V0czonLCB0aWNrZXRzKTsgLy8gTG9nIHRoZSBuZXcgc3RhdGVcclxuICAgIHNldCh7IHRpY2tldHMgfSk7XHJcbiAgfSxcclxuICBmZXRjaFRpY2tldHM6IGFzeW5jIChjcm9wSWQ6IHN0cmluZykgPT4ge1xyXG4gICAgY29uc3QgcmVzID0gYXdhaXQgZ2V0VGlja2V0TGlzdCh7XHJcbiAgICAgIHBhZ2U6IDEsXHJcbiAgICAgIHNpemU6IDEwMDAwLFxyXG4gICAgICBmaWx0ZXJzOiBbWydpb3RfdGlja2V0JywgJ2Nyb3BfaWQnLCAnbGlrZScsIGNyb3BJZF1dLFxyXG4gICAgfSk7XHJcbiAgICBjb25zb2xlLmxvZygnZmV0Y2hUaWNrZXRzIHRpY2tldHM6JywgcmVzLmRhdGEpOyAvLyBMb2cgdGhlIG5ldyBzdGF0ZVxyXG4gICAgc2V0KHsgdGlja2V0czogcmVzLmRhdGEgfSk7XHJcbiAgfSxcclxuICBnZXRUaWNrZXRNZXNzYWdlczogKHRpY2tldElkOiBzdHJpbmcpID0+IHtcclxuICAgIGNvbnN0IHRpY2tldCA9IGdldCgpLnRpY2tldHMuZmluZCgodGlja2V0KSA9PiB0aWNrZXQubmFtZSA9PT0gdGlja2V0SWQpO1xyXG4gICAgY29uc3QgcmV0dXJuTWVzc2FnZXMgPSB0aWNrZXQ/Lm1lc3NhZ2VzID8gdGlja2V0Lm1lc3NhZ2VzIDogW107XHJcbiAgICByZXR1cm4gcmV0dXJuTWVzc2FnZXM7XHJcbiAgfSxcclxufSkpO1xyXG5cclxuLy8gRGVmaW5lIGEgc3RvcmUgZm9yIHRoZSBzZWxlY3RlZCB0aWNrZXRcclxuZXhwb3J0IGNvbnN0IHVzZVNlbGVjdGVkVGlja2V0U3RvcmUgPSBjcmVhdGU8U2VsZWN0ZWRUaWNrZXQ+KChzZXQpID0+ICh7XHJcbiAgc2VsZWN0ZWRUaWNrZXQ6IHt9IGFzIElJb3RUaWNrZXQsXHJcbiAgdmlld2luZ0RldGFpbHM6IGZhbHNlLFxyXG4gIHNldFNlbGVjdGVkVGlja2V0OiAodGlja2V0OiBJSW90VGlja2V0KSA9PiB7XHJcbiAgICBjb25zb2xlLmxvZygnU2V0dGluZyBzZWxlY3RlZFRpY2tldDonLCB0aWNrZXQpOyAvLyBMb2cgdGhlIG5ldyBzdGF0ZVxyXG4gICAgc2V0KHsgc2VsZWN0ZWRUaWNrZXQ6IHRpY2tldCB9KTtcclxuICB9LFxyXG4gIHNldFZpZXdpbmdEZXRhaWxzOiAodmlld2luZzogYm9vbGVhbikgPT4ge1xyXG4gICAgY29uc29sZS5sb2coJ1NldHRpbmcgdmlld2luZ0RldGFpbHM6Jywgdmlld2luZyk7IC8vIExvZyB0aGUgbmV3IHN0YXRlXHJcbiAgICBzZXQoeyB2aWV3aW5nRGV0YWlsczogdmlld2luZyB9KTtcclxuICB9LFxyXG59KSk7XHJcbiJdLCJuYW1lcyI6WyJnZXRUaWNrZXRMaXN0IiwiY3JlYXRlIiwidXNlVGlja2V0TGlzdFN0b3JlIiwic2V0IiwiZ2V0IiwidGlja2V0cyIsInNldFRpY2tldHMiLCJjb25zb2xlIiwibG9nIiwiZmV0Y2hUaWNrZXRzIiwiX2ZldGNoVGlja2V0cyIsIl9hc3luY1RvR2VuZXJhdG9yIiwiX3JlZ2VuZXJhdG9yUnVudGltZSIsIm1hcmsiLCJfY2FsbGVlIiwiY3JvcElkIiwicmVzIiwid3JhcCIsIl9jYWxsZWUkIiwiX2NvbnRleHQiLCJwcmV2IiwibmV4dCIsInBhZ2UiLCJzaXplIiwiZmlsdGVycyIsInNlbnQiLCJkYXRhIiwic3RvcCIsIl94IiwiYXBwbHkiLCJhcmd1bWVudHMiLCJnZXRUaWNrZXRNZXNzYWdlcyIsInRpY2tldElkIiwidGlja2V0IiwiZmluZCIsIm5hbWUiLCJyZXR1cm5NZXNzYWdlcyIsIm1lc3NhZ2VzIiwidXNlU2VsZWN0ZWRUaWNrZXRTdG9yZSIsInNlbGVjdGVkVGlja2V0Iiwidmlld2luZ0RldGFpbHMiLCJzZXRTZWxlY3RlZFRpY2tldCIsInNldFZpZXdpbmdEZXRhaWxzIiwidmlld2luZyJdLCJzb3VyY2VSb290IjoiIn0=
//# sourceURL=webpack-internal:///33217
`)},7369:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   W: function() { return /* binding */ toFirstUpperCase; },
/* harmony export */   w: function() { return /* binding */ nonAccentVietnamese; }
/* harmony export */ });
function nonAccentVietnamese(str) {
  var _str$toString;
  var _str = str === null || str === void 0 || (_str$toString = str.toString) === null || _str$toString === void 0 ? void 0 : _str$toString.call(str);
  if (typeof _str !== 'string') return str;
  _str = _str.toLowerCase();
  //     We can also use this instead of from line 11 to line 17
  //     str = str.replace(/\\u00E0|\\u00E1|\\u1EA1|\\u1EA3|\\u00E3|\\u00E2|\\u1EA7|\\u1EA5|\\u1EAD|\\u1EA9|\\u1EAB|\\u0103|\\u1EB1|\\u1EAF|\\u1EB7|\\u1EB3|\\u1EB5/g, "a");
  //     str = str.replace(/\\u00E8|\\u00E9|\\u1EB9|\\u1EBB|\\u1EBD|\\u00EA|\\u1EC1|\\u1EBF|\\u1EC7|\\u1EC3|\\u1EC5/g, "e");
  //     str = str.replace(/\\u00EC|\\u00ED|\\u1ECB|\\u1EC9|\\u0129/g, "i");
  //     str = str.replace(/\\u00F2|\\u00F3|\\u1ECD|\\u1ECF|\\u00F5|\\u00F4|\\u1ED3|\\u1ED1|\\u1ED9|\\u1ED5|\\u1ED7|\\u01A1|\\u1EDD|\\u1EDB|\\u1EE3|\\u1EDF|\\u1EE1/g, "o");
  //     str = str.replace(/\\u00F9|\\u00FA|\\u1EE5|\\u1EE7|\\u0169|\\u01B0|\\u1EEB|\\u1EE9|\\u1EF1|\\u1EED|\\u1EEF/g, "u");
  //     str = str.replace(/\\u1EF3|\\u00FD|\\u1EF5|\\u1EF7|\\u1EF9/g, "y");
  //     str = str.replace(/\\u0111/g, "d");
  _str = _str.replace(/\xE0|\xE1|\u1EA1|\u1EA3|\xE3|\xE2|\u1EA7|\u1EA5|\u1EAD|\u1EA9|\u1EAB|\u0103|\u1EB1|\u1EAF|\u1EB7|\u1EB3|\u1EB5/g, 'a');
  _str = _str.replace(/\xE8|\xE9|\u1EB9|\u1EBB|\u1EBD|\xEA|\u1EC1|\u1EBF|\u1EC7|\u1EC3|\u1EC5/g, 'e');
  _str = _str.replace(/\xEC|\xED|\u1ECB|\u1EC9|\u0129/g, 'i');
  _str = _str.replace(/\xF2|\xF3|\u1ECD|\u1ECF|\xF5|\xF4|\u1ED3|\u1ED1|\u1ED9|\u1ED5|\u1ED7|\u01A1|\u1EDD|\u1EDB|\u1EE3|\u1EDF|\u1EE1/g, 'o');
  _str = _str.replace(/\xF9|\xFA|\u1EE5|\u1EE7|\u0169|\u01B0|\u1EEB|\u1EE9|\u1EF1|\u1EED|\u1EEF/g, 'u');
  _str = _str.replace(/\u1EF3|\xFD|\u1EF5|\u1EF7|\u1EF9/g, 'y');
  _str = _str.replace(/\u0111/g, 'd');
  // Some system encode vietnamese combining accent as individual utf-8 characters
  _str = _str.replace(/\\u0300|\\u0301|\\u0303|\\u0309|\\u0323/g, ''); // Huy\u1EC1n s\u1EAFc h\u1ECFi ng\xE3 n\u1EB7ng
  _str = _str.replace(/\\u02C6|\\u0306|\\u031B/g, ''); // \xC2, \xCA, \u0102, \u01A0, \u01AF
  return _str.split(',').join('');
}
var toFirstUpperCase = function toFirstUpperCase(str) {
  if (!str) return '';
  return str.charAt(0).toUpperCase() + str.slice(1);
};//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///7369
`)}}]);
