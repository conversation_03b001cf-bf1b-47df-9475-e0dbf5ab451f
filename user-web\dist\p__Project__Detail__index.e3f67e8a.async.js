"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[3069],{47046:function(__unused_webpack_module,__webpack_exports__){eval(`// This icon file is generated automatically.
var DeleteOutlined = { "icon": { "tag": "svg", "attrs": { "viewBox": "64 64 896 896", "focusable": "false" }, "children": [{ "tag": "path", "attrs": { "d": "M360 184h-8c4.4 0 8-3.6 8-8v8h304v-8c0 4.4 3.6 8 8 8h-8v72h72v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80h72v-72zm504 72H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM731.3 840H292.7l-24.2-512h487l-24.2 512z" } }] }, "name": "delete", "theme": "outlined" };
/* harmony default export */ __webpack_exports__.Z = (DeleteOutlined);
//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNDcwNDYuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQSx1QkFBdUIsVUFBVSx5QkFBeUIsa0RBQWtELGlCQUFpQiwwQkFBMEIsOFZBQThWLEdBQUc7QUFDeGYsc0RBQWUsY0FBYyxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvQGFudC1kZXNpZ24vaWNvbnMtc3ZnL2VzL2Fzbi9EZWxldGVPdXRsaW5lZC5qcz85ODlhIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIFRoaXMgaWNvbiBmaWxlIGlzIGdlbmVyYXRlZCBhdXRvbWF0aWNhbGx5LlxudmFyIERlbGV0ZU91dGxpbmVkID0geyBcImljb25cIjogeyBcInRhZ1wiOiBcInN2Z1wiLCBcImF0dHJzXCI6IHsgXCJ2aWV3Qm94XCI6IFwiNjQgNjQgODk2IDg5NlwiLCBcImZvY3VzYWJsZVwiOiBcImZhbHNlXCIgfSwgXCJjaGlsZHJlblwiOiBbeyBcInRhZ1wiOiBcInBhdGhcIiwgXCJhdHRyc1wiOiB7IFwiZFwiOiBcIk0zNjAgMTg0aC04YzQuNCAwIDgtMy42IDgtOHY4aDMwNHYtOGMwIDQuNCAzLjYgOCA4IDhoLTh2NzJoNzJ2LTgwYzAtMzUuMy0yOC43LTY0LTY0LTY0SDM1MmMtMzUuMyAwLTY0IDI4LjctNjQgNjR2ODBoNzJ2LTcyem01MDQgNzJIMTYwYy0xNy43IDAtMzIgMTQuMy0zMiAzMnYzMmMwIDQuNCAzLjYgOCA4IDhoNjAuNGwyNC43IDUyM2MxLjYgMzQuMSAyOS44IDYxIDYzLjkgNjFoNDU0YzM0LjIgMCA2Mi4zLTI2LjggNjMuOS02MWwyNC43LTUyM0g4ODhjNC40IDAgOC0zLjYgOC04di0zMmMwLTE3LjctMTQuMy0zMi0zMi0zMnpNNzMxLjMgODQwSDI5Mi43bC0yNC4yLTUxMmg0ODdsLTI0LjIgNTEyelwiIH0gfV0gfSwgXCJuYW1lXCI6IFwiZGVsZXRlXCIsIFwidGhlbWVcIjogXCJvdXRsaW5lZFwiIH07XG5leHBvcnQgZGVmYXVsdCBEZWxldGVPdXRsaW5lZDtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==
//# sourceURL=webpack-internal:///47046
`)},82061:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(1413);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(67294);
/* harmony import */ var _ant_design_icons_svg_es_asn_DeleteOutlined__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(47046);
/* harmony import */ var _components_AntdIcon__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(89099);

// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY



var DeleteOutlined = function DeleteOutlined(props, ref) {
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(_components_AntdIcon__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Z, (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z)((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z)({}, props), {}, {
    ref: ref,
    icon: _ant_design_icons_svg_es_asn_DeleteOutlined__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z
  }));
};
DeleteOutlined.displayName = 'DeleteOutlined';
/* harmony default export */ __webpack_exports__.Z = (/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(DeleteOutlined));//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiODIwNjEuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFxRTtBQUNyRTtBQUNBO0FBQytCO0FBQzZDO0FBQzlCO0FBQzlDO0FBQ0Esc0JBQXNCLGdEQUFtQixDQUFDLHFFQUFRLEVBQUUsNkZBQWEsQ0FBQyw2RkFBYSxHQUFHLFlBQVk7QUFDOUY7QUFDQSxVQUFVLDRGQUFpQjtBQUMzQixHQUFHO0FBQ0g7QUFDQTtBQUNBLG1FQUE0Qiw2Q0FBZ0IsZ0JBQWdCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvQGFudC1kZXNpZ24vaWNvbnMvZXMvaWNvbnMvRGVsZXRlT3V0bGluZWQuanM/NGRkNyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgX29iamVjdFNwcmVhZCBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vb2JqZWN0U3ByZWFkMlwiO1xuLy8gR0VORVJBVEUgQlkgLi9zY3JpcHRzL2dlbmVyYXRlLnRzXG4vLyBET04gTk9UIEVESVQgSVQgTUFOVUFMTFlcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCBEZWxldGVPdXRsaW5lZFN2ZyBmcm9tIFwiQGFudC1kZXNpZ24vaWNvbnMtc3ZnL2VzL2Fzbi9EZWxldGVPdXRsaW5lZFwiO1xuaW1wb3J0IEFudGRJY29uIGZyb20gJy4uL2NvbXBvbmVudHMvQW50ZEljb24nO1xudmFyIERlbGV0ZU91dGxpbmVkID0gZnVuY3Rpb24gRGVsZXRlT3V0bGluZWQocHJvcHMsIHJlZikge1xuICByZXR1cm4gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoQW50ZEljb24sIF9vYmplY3RTcHJlYWQoX29iamVjdFNwcmVhZCh7fSwgcHJvcHMpLCB7fSwge1xuICAgIHJlZjogcmVmLFxuICAgIGljb246IERlbGV0ZU91dGxpbmVkU3ZnXG4gIH0pKTtcbn07XG5EZWxldGVPdXRsaW5lZC5kaXNwbGF5TmFtZSA9ICdEZWxldGVPdXRsaW5lZCc7XG5leHBvcnQgZGVmYXVsdCAvKiNfX1BVUkVfXyovUmVhY3QuZm9yd2FyZFJlZihEZWxldGVPdXRsaW5lZCk7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9
//# sourceURL=webpack-internal:///82061
`)},60219:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`
// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  Z: function() { return /* binding */ icons_SaveOutlined; }
});

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectSpread2.js
var objectSpread2 = __webpack_require__(1413);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
;// CONCATENATED MODULE: ./node_modules/@ant-design/icons-svg/es/asn/SaveOutlined.js
// This icon file is generated automatically.
var SaveOutlined = { "icon": { "tag": "svg", "attrs": { "viewBox": "64 64 896 896", "focusable": "false" }, "children": [{ "tag": "path", "attrs": { "d": "M893.3 293.3L730.7 130.7c-7.5-7.5-16.7-13-26.7-16V112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V338.5c0-17-6.7-33.2-18.7-45.2zM384 184h256v104H384V184zm456 656H184V184h136v136c0 17.7 14.3 32 32 32h320c17.7 0 32-14.3 32-32V205.8l136 136V840zM512 442c-79.5 0-144 64.5-144 144s64.5 144 144 144 144-64.5 144-144-64.5-144-144-144zm0 224c-44.2 0-80-35.8-80-80s35.8-80 80-80 80 35.8 80 80-35.8 80-80 80z" } }] }, "name": "save", "theme": "outlined" };
/* harmony default export */ var asn_SaveOutlined = (SaveOutlined);

// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/components/AntdIcon.js + 5 modules
var AntdIcon = __webpack_require__(89099);
;// CONCATENATED MODULE: ./node_modules/@ant-design/icons/es/icons/SaveOutlined.js

// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY



var SaveOutlined_SaveOutlined = function SaveOutlined(props, ref) {
  return /*#__PURE__*/react.createElement(AntdIcon/* default */.Z, (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, props), {}, {
    ref: ref,
    icon: asn_SaveOutlined
  }));
};
SaveOutlined_SaveOutlined.displayName = 'SaveOutlined';
/* harmony default export */ var icons_SaveOutlined = (/*#__PURE__*/react.forwardRef(SaveOutlined_SaveOutlined));//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///60219
`)},44688:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Yk: function() { return /* binding */ DescriptionsSkeleton; },
/* harmony export */   hM: function() { return /* binding */ TableSkeleton; }
/* harmony export */ });
/* unused harmony export TableItemSkeleton */
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(99559);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(75302);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(76216);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(67294);
/* harmony import */ var _List__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(56517);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(85893);






var MediaQueryKeyEnum = {
  xs: 1,
  sm: 2,
  md: 3,
  lg: 3,
  xl: 3,
  xxl: 4
};
var DescriptionsLargeItemSkeleton = function DescriptionsLargeItemSkeleton(_ref) {
  var active = _ref.active;
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)("div", {
    style: {
      marginBlockStart: 32
    },
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z.Button, {
      active: active,
      size: "small",
      style: {
        width: 100,
        marginBlockEnd: 16
      }
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)("div", {
      style: {
        width: '100%',
        justifyContent: 'space-between',
        display: 'flex'
      },
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)("div", {
        style: {
          flex: 1,
          marginInlineEnd: 24,
          maxWidth: 300
        },
        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z, {
          active: active,
          paragraph: false,
          title: {
            style: {
              marginBlockStart: 0
            }
          }
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z, {
          active: active,
          paragraph: false,
          title: {
            style: {
              marginBlockStart: 8
            }
          }
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z, {
          active: active,
          paragraph: false,
          title: {
            style: {
              marginBlockStart: 8
            }
          }
        })]
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)("div", {
        style: {
          flex: 1,
          alignItems: 'center',
          justifyContent: 'center'
        },
        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)("div", {
          style: {
            maxWidth: 300,
            margin: 'auto'
          },
          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z, {
            active: active,
            paragraph: false,
            title: {
              style: {
                marginBlockStart: 0
              }
            }
          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z, {
            active: active,
            paragraph: false,
            title: {
              style: {
                marginBlockStart: 8
              }
            }
          })]
        })
      })]
    })]
  });
};
var DescriptionsItemSkeleton = function DescriptionsItemSkeleton(_ref2) {
  var size = _ref2.size,
    active = _ref2.active;
  var defaultCol = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function () {
    return {
      lg: true,
      md: true,
      sm: false,
      xl: false,
      xs: false,
      xxl: false
    };
  }, []);
  var col = antd__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .ZP.useBreakpoint() || defaultCol;
  var colSize = Object.keys(col).filter(function (key) {
    return col[key] === true;
  })[0] || 'md';
  var arraySize = size === undefined ? MediaQueryKeyEnum[colSize] || 3 : size;
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)("div", {
    style: {
      width: '100%',
      justifyContent: 'space-between',
      display: 'flex'
    },
    children: new Array(arraySize).fill(null).map(function (_, index) {
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)("div", {
        style: {
          flex: 1,
          paddingInlineStart: index === 0 ? 0 : 24,
          paddingInlineEnd: index === arraySize - 1 ? 0 : 24
        },
        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z, {
          active: active,
          paragraph: false,
          title: {
            style: {
              marginBlockStart: 0
            }
          }
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z, {
          active: active,
          paragraph: false,
          title: {
            style: {
              marginBlockStart: 8
            }
          }
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z, {
          active: active,
          paragraph: false,
          title: {
            style: {
              marginBlockStart: 8
            }
          }
        })]
      }, index);
    })
  });
};

/**
 * Table \u7684\u5B50\u9879\u76EE\u9AA8\u67B6\u5C4F
 *
 * @param param0
 */
var TableItemSkeleton = function TableItemSkeleton(_ref3) {
  var active = _ref3.active,
    _ref3$header = _ref3.header,
    header = _ref3$header === void 0 ? false : _ref3$header;
  var defaultCol = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function () {
    return {
      lg: true,
      md: true,
      sm: false,
      xl: false,
      xs: false,
      xxl: false
    };
  }, []);
  var col = antd__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .ZP.useBreakpoint() || defaultCol;
  var colSize = Object.keys(col).filter(function (key) {
    return col[key] === true;
  })[0] || 'md';
  var arraySize = MediaQueryKeyEnum[colSize] || 3;
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)("div", {
      style: {
        display: 'flex',
        background: header ? 'rgba(0,0,0,0.02)' : 'none',
        padding: '24px 8px'
      },
      children: [new Array(arraySize).fill(null).map(function (_, index) {
        return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)("div", {
          style: {
            flex: 1,
            paddingInlineStart: header && index === 0 ? 0 : 20,
            paddingInlineEnd: 32
          },
          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z, {
            active: active,
            paragraph: false,
            title: {
              style: {
                margin: 0,
                height: 24,
                width: header ? '75px' : '100%'
              }
            }
          })
        }, index);
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)("div", {
        style: {
          flex: 3,
          paddingInlineStart: 32
        },
        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z, {
          active: active,
          paragraph: false,
          title: {
            style: {
              margin: 0,
              height: 24,
              width: header ? '75px' : '100%'
            }
          }
        })
      })]
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_List__WEBPACK_IMPORTED_MODULE_4__/* .Line */ .x1, {
      padding: "0px 0px"
    })]
  });
};

/**
 * Table \u9AA8\u67B6\u5C4F
 *
 * @param param0
 */
var TableSkeleton = function TableSkeleton(_ref4) {
  var active = _ref4.active,
    _ref4$size = _ref4.size,
    size = _ref4$size === void 0 ? 4 : _ref4$size;
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z, {
    bordered: false,
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z.Button, {
      active: active,
      size: "small",
      style: {
        width: 100,
        marginBlockEnd: 16
      }
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TableItemSkeleton, {
      header: true,
      active: active
    }), new Array(size).fill(null).map(function (_, index) {
      return (
        /*#__PURE__*/
        // eslint-disable-next-line react/no-array-index-key
        (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TableItemSkeleton, {
          active: active
        }, index)
      );
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)("div", {
      style: {
        display: 'flex',
        justifyContent: 'flex-end',
        paddingBlockStart: 16
      },
      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z, {
        active: active,
        paragraph: false,
        title: {
          style: {
            margin: 0,
            height: 32,
            float: 'right',
            maxWidth: '630px'
          }
        }
      })
    })]
  });
};
var DescriptionsSkeleton = function DescriptionsSkeleton(_ref5) {
  var active = _ref5.active;
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z, {
    bordered: false,
    style: {
      borderStartEndRadius: 0,
      borderTopLeftRadius: 0
    },
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z.Button, {
      active: active,
      size: "small",
      style: {
        width: 100,
        marginBlockEnd: 16
      }
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DescriptionsItemSkeleton, {
      active: active
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DescriptionsLargeItemSkeleton, {
      active: active
    })]
  });
};
var DescriptionsPageSkeleton = function DescriptionsPageSkeleton(_ref6) {
  var _ref6$active = _ref6.active,
    active = _ref6$active === void 0 ? true : _ref6$active,
    pageHeader = _ref6.pageHeader,
    list = _ref6.list;
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)("div", {
    style: {
      width: '100%'
    },
    children: [pageHeader !== false && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_List__WEBPACK_IMPORTED_MODULE_4__/* .PageHeaderSkeleton */ .SM, {
      active: active
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DescriptionsSkeleton, {
      active: active
    }), list !== false && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_List__WEBPACK_IMPORTED_MODULE_4__/* .Line */ .x1, {}), list !== false && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TableSkeleton, {
      active: active,
      size: list
    })]
  });
};
/* harmony default export */ __webpack_exports__.ZP = (DescriptionsPageSkeleton);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///44688
`)},56517:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SM: function() { return /* binding */ PageHeaderSkeleton; },
/* harmony export */   cg: function() { return /* binding */ ListSkeleton; },
/* harmony export */   x1: function() { return /* binding */ Line; }
/* harmony export */ });
/* unused harmony exports MediaQueryKeyEnum, ListSkeletonItem, ListToolbarSkeleton */
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(96074);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(75302);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(76216);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(99559);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(42075);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(67294);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(85893);



/** \u4E00\u6761\u5206\u5272\u7EBF */



var Line = function Line(_ref) {
  var padding = _ref.padding;
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)("div", {
    style: {
      padding: padding || '0 24px'
    },
    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z, {
      style: {
        margin: 0
      }
    })
  });
};
var MediaQueryKeyEnum = {
  xs: 2,
  sm: 2,
  md: 4,
  lg: 4,
  xl: 6,
  xxl: 6
};
var StatisticSkeleton = function StatisticSkeleton(_ref2) {
  var size = _ref2.size,
    active = _ref2.active;
  var defaultCol = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function () {
    return {
      lg: true,
      md: true,
      sm: false,
      xl: false,
      xs: false,
      xxl: false
    };
  }, []);
  var col = antd__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .ZP.useBreakpoint() || defaultCol;
  var colSize = Object.keys(col).filter(function (key) {
    return col[key] === true;
  })[0] || 'md';
  var arraySize = size === undefined ? MediaQueryKeyEnum[colSize] || 6 : size;
  var firstWidth = function firstWidth(index) {
    if (index === 0) {
      return 0;
    }
    if (arraySize > 2) {
      return 42;
    }
    return 16;
  };
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z, {
    bordered: false,
    style: {
      marginBlockEnd: 16
    },
    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)("div", {
      style: {
        width: '100%',
        justifyContent: 'space-between',
        display: 'flex'
      },
      children: new Array(arraySize).fill(null).map(function (_, index) {
        return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)("div", {
          style: {
            borderInlineStart: arraySize > 2 && index === 1 ? '1px solid rgba(0,0,0,0.06)' : undefined,
            paddingInlineStart: firstWidth(index),
            flex: 1,
            marginInlineEnd: index === 0 ? 16 : 0
          },
          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z, {
            active: active,
            paragraph: false,
            title: {
              width: 100,
              style: {
                marginBlockStart: 0
              }
            }
          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z.Button, {
            active: active,
            style: {
              height: 48
            }
          })]
        }, index);
      })
    })
  });
};

/** \u5217\u8868\u5B50\u9879\u76EE\u9AA8\u67B6\u5C4F */
var ListSkeletonItem = function ListSkeletonItem(_ref3) {
  var active = _ref3.active;
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z, {
      bordered: false
      // eslint-disable-next-line react/no-array-index-key
      ,
      style: {
        borderRadius: 0
      },
      bodyStyle: {
        padding: 24
      },
      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)("div", {
        style: {
          width: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        },
        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)("div", {
          style: {
            maxWidth: '100%',
            flex: 1
          },
          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z, {
            active: active,
            title: {
              width: 100,
              style: {
                marginBlockStart: 0
              }
            },
            paragraph: {
              rows: 1,
              style: {
                margin: 0
              }
            }
          })
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z.Button, {
          active: active,
          size: "small",
          style: {
            width: 165,
            marginBlockStart: 12
          }
        })]
      })
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Line, {})]
  });
};

/** \u5217\u8868\u9AA8\u67B6\u5C4F */
var ListSkeleton = function ListSkeleton(_ref4) {
  var size = _ref4.size,
    _ref4$active = _ref4.active,
    active = _ref4$active === void 0 ? true : _ref4$active,
    actionButton = _ref4.actionButton;
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z, {
    bordered: false,
    bodyStyle: {
      padding: 0
    },
    children: [new Array(size).fill(null).map(function (_, index) {
      return (
        /*#__PURE__*/
        // eslint-disable-next-line react/no-array-index-key
        (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ListSkeletonItem, {
          active: !!active
        }, index)
      );
    }), actionButton !== false && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z, {
      bordered: false,
      style: {
        borderStartEndRadius: 0,
        borderTopLeftRadius: 0
      },
      bodyStyle: {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      },
      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z.Button, {
        style: {
          width: 102
        },
        active: active,
        size: "small"
      })
    })]
  });
};

/**
 * \u9762\u5305\u5C51\u7684 \u9AA8\u67B6\u5C4F
 *
 * @param param0
 */
var PageHeaderSkeleton = function PageHeaderSkeleton(_ref5) {
  var active = _ref5.active;
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)("div", {
    style: {
      marginBlockEnd: 16
    },
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z, {
      paragraph: false,
      title: {
        width: 185
      }
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z.Button, {
      active: active,
      size: "small"
    })]
  });
};
/**
 * \u5217\u8868\u64CD\u4F5C\u680F\u7684\u9AA8\u67B6\u5C4F
 *
 * @param param0
 */
var ListToolbarSkeleton = function ListToolbarSkeleton(_ref6) {
  var active = _ref6.active;
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z, {
    bordered: false,
    style: {
      borderBottomRightRadius: 0,
      borderBottomLeftRadius: 0
    },
    bodyStyle: {
      paddingBlockEnd: 8
    },
    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Z, {
      style: {
        width: '100%',
        justifyContent: 'space-between'
      },
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z.Button, {
        active: active,
        style: {
          width: 200
        },
        size: "small"
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Z, {
        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z.Button, {
          active: active,
          size: "small",
          style: {
            width: 120
          }
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z.Button, {
          active: active,
          size: "small",
          style: {
            width: 80
          }
        })]
      })]
    })
  });
};
var ListPageSkeleton = function ListPageSkeleton(_ref7) {
  var _ref7$active = _ref7.active,
    active = _ref7$active === void 0 ? true : _ref7$active,
    statistic = _ref7.statistic,
    actionButton = _ref7.actionButton,
    toolbar = _ref7.toolbar,
    pageHeader = _ref7.pageHeader,
    _ref7$list = _ref7.list,
    list = _ref7$list === void 0 ? 5 : _ref7$list;
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)("div", {
    style: {
      width: '100%'
    },
    children: [pageHeader !== false && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PageHeaderSkeleton, {
      active: active
    }), statistic !== false && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(StatisticSkeleton, {
      size: statistic,
      active: active
    }), (toolbar !== false || list !== false) && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z, {
      bordered: false,
      bodyStyle: {
        padding: 0
      },
      children: [toolbar !== false && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ListToolbarSkeleton, {
        active: active
      }), list !== false && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ListSkeleton, {
        size: list,
        active: active,
        actionButton: actionButton
      })]
    })]
  });
};
/* harmony default export */ __webpack_exports__.ZP = (ListPageSkeleton);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///56517
`)},27076:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(5574);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(97857);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _utils_string__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(7369);
/* harmony import */ var _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(6110);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(7837);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(67294);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(85893);







var PageContainerTabsWithSearch = function PageContainerTabsWithSearch(_ref) {
  var tabsItems = _ref.tabsItems,
    _ref$searchParamsUrlK = _ref.searchParamsUrlKey,
    searchParamsUrlKey = _ref$searchParamsUrlK === void 0 ? 'tab' : _ref$searchParamsUrlK,
    _onTabChange = _ref.onTabChange,
    pageTitle = _ref.pageTitle;
  var tabsItemsFormat = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(function () {
    return tabsItems === null || tabsItems === void 0 ? void 0 : tabsItems.map(function (item) {
      return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({}, item), {}, {
        tabKey: item.tabKey || (0,_utils_string__WEBPACK_IMPORTED_MODULE_5__/* .nonAccentVietnamese */ .w)(item.label)
      });
    });
  }, [tabsItems]);
  var _useSearchParams = (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)(),
    _useSearchParams2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_0___default()(_useSearchParams, 2),
    searchParams = _useSearchParams2[0],
    setSearchParams = _useSearchParams2[1];
  var tabActive = searchParamsUrlKey ? searchParams.get(searchParamsUrlKey) : undefined;
  console.log('tabActive', tabActive);
  var setTabActive = searchParamsUrlKey ? function (tabActiveVal) {
    searchParams.set(searchParamsUrlKey, tabActiveVal.toString());
    setSearchParams(searchParams);
  } : undefined;
  var tabList = tabsItemsFormat === null || tabsItemsFormat === void 0 ? void 0 : tabsItemsFormat.map(function (item) {
    return {
      tab: item.label,
      key: item.tabKey,
      tabKey: item.tabKey
    };
  });
  var tabPageActive = searchParamsUrlKey ? (tabsItemsFormat === null || tabsItemsFormat === void 0 ? void 0 : tabsItemsFormat.find(function (item) {
    return item.tabKey === tabActive;
  })) || (tabsItemsFormat === null || tabsItemsFormat === void 0 ? void 0 : tabsItemsFormat[0]) : undefined;
  var ComponentActive = tabPageActive === null || tabPageActive === void 0 ? void 0 : tabPageActive.component;
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_6__/* .PageContainer */ ._z, {
    fixedHeader: true,
    extra: tabPageActive === null || tabPageActive === void 0 ? void 0 : tabPageActive.extraPage,
    tabActiveKey: tabPageActive === null || tabPageActive === void 0 ? void 0 : tabPageActive.tabKey,
    tabList: tabList,
    onTabChange: function onTabChange(tabActiveVal) {
      _onTabChange === null || _onTabChange === void 0 || _onTabChange(tabActiveVal);
      if (searchParamsUrlKey) setTabActive === null || setTabActive === void 0 || setTabActive(tabActiveVal);
    },
    title: pageTitle,
    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(react__WEBPACK_IMPORTED_MODULE_3__.Suspense, {
      fallback: (tabPageActive === null || tabPageActive === void 0 ? void 0 : tabPageActive.fallback) || null,
      children: ComponentActive && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(ComponentActive, {})
    })
  });
};
/* harmony default export */ __webpack_exports__.Z = (PageContainerTabsWithSearch);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///27076
`)},94713:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return /* binding */ Detail; }
});

// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/regeneratorRuntime.js
var regeneratorRuntime = __webpack_require__(15009);
var regeneratorRuntime_default = /*#__PURE__*/__webpack_require__.n(regeneratorRuntime);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(99289);
var asyncToGenerator_default = /*#__PURE__*/__webpack_require__.n(asyncToGenerator);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/slicedToArray.js
var slicedToArray = __webpack_require__(5574);
var slicedToArray_default = /*#__PURE__*/__webpack_require__.n(slicedToArray);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-skeleton/es/components/Descriptions/index.js
var Descriptions = __webpack_require__(44688);
// EXTERNAL MODULE: ./src/.umi-production/exports.ts + 34 modules
var _umi_production_exports = __webpack_require__(7837);
// EXTERNAL MODULE: ./node_modules/antd/es/button/index.js + 9 modules
var es_button = __webpack_require__(14726);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./src/common/contanst/constanst.ts
var constanst = __webpack_require__(86604);
// EXTERNAL MODULE: ./src/components/PageContainerTabsWithSearch/index.tsx
var PageContainerTabsWithSearch = __webpack_require__(27076);
// EXTERNAL MODULE: ./src/services/projects.ts
var projects = __webpack_require__(78263);
// EXTERNAL MODULE: ./src/utils/lazy.tsx
var lazy = __webpack_require__(48576);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/SaveOutlined.js + 1 modules
var SaveOutlined = __webpack_require__(60219);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/DeleteOutlined.js
var DeleteOutlined = __webpack_require__(82061);
// EXTERNAL MODULE: ./node_modules/antd/es/app/index.js + 1 modules
var app = __webpack_require__(31418);
// EXTERNAL MODULE: ./src/pages/Project/hooks/useDeleleteProject.tsx
var useDeleleteProject = __webpack_require__(57250);
// EXTERNAL MODULE: ./node_modules/react/jsx-runtime.js
var jsx_runtime = __webpack_require__(85893);
;// CONCATENATED MODULE: ./src/pages/Project/Detail/DeleteProject.tsx






var DeleteProject = function DeleteProject(_ref) {
  var children = _ref.children,
    name = _ref.name;
  var _App$useApp = app/* default */.Z.useApp(),
    modal = _App$useApp.modal;
  var _useDeleteProject = (0,useDeleleteProject/* default */.Z)(),
    run = _useDeleteProject.run;
  return /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
    type: "primary",
    danger: true,
    onClick: function onClick(e) {
      modal.confirm({
        title: "B\\u1EA1n c\\xF3 ch\\u1EAFc ch\\u1EAFc mu\\u1ED1n x\\xF3a d\\u1EF1 \\xE1n ".concat(name),
        content: 'H\xE0nh \u0111\u1ED9ng n\xE0y kh\xF4ng th\u1EC3 ho\xE0n t\xE1c!',
        okButtonProps: {
          danger: true
        },
        onOk: function () {
          var _onOk = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
            return regeneratorRuntime_default()().wrap(function _callee$(_context) {
              while (1) switch (_context.prev = _context.next) {
                case 0:
                  _context.prev = 0;
                  _context.next = 3;
                  return run(name);
                case 3:
                  return _context.abrupt("return", true);
                case 6:
                  _context.prev = 6;
                  _context.t0 = _context["catch"](0);
                  return _context.abrupt("return", false);
                case 9:
                case "end":
                  return _context.stop();
              }
            }, _callee, null, [[0, 6]]);
          }));
          function onOk() {
            return _onOk.apply(this, arguments);
          }
          return onOk;
        }()
      });
    },
    icon: /*#__PURE__*/(0,jsx_runtime.jsx)(DeleteOutlined/* default */.Z, {}),
    children: "X\\xF3a d\\u1EF1 \\xE1n"
  });
};
/* harmony default export */ var Detail_DeleteProject = (DeleteProject);
;// CONCATENATED MODULE: ./src/pages/Project/Detail/index.tsx














var UpdateProjectForm = (0,lazy/* myLazy */.Q)(function () {
  return Promise.all(/* import() */[__webpack_require__.e(6), __webpack_require__.e(1499), __webpack_require__.e(5779), __webpack_require__.e(2788), __webpack_require__.e(3633), __webpack_require__.e(5277), __webpack_require__.e(5243), __webpack_require__.e(265), __webpack_require__.e(4908), __webpack_require__.e(4994), __webpack_require__.e(2783), __webpack_require__.e(5514), __webpack_require__.e(8021), __webpack_require__.e(5419), __webpack_require__.e(2466), __webpack_require__.e(5034), __webpack_require__.e(9228)]).then(__webpack_require__.bind(__webpack_require__, 39228));
});
var ZoneInProject = (0,lazy/* myLazy */.Q)(function () {
  return Promise.all(/* import() */[__webpack_require__.e(6), __webpack_require__.e(1499), __webpack_require__.e(5779), __webpack_require__.e(2788), __webpack_require__.e(3633), __webpack_require__.e(5277), __webpack_require__.e(5243), __webpack_require__.e(265), __webpack_require__.e(4908), __webpack_require__.e(4994), __webpack_require__.e(2783), __webpack_require__.e(5514), __webpack_require__.e(7839), __webpack_require__.e(8021), __webpack_require__.e(1403), __webpack_require__.e(4560), __webpack_require__.e(691), __webpack_require__.e(5419), __webpack_require__.e(8047), __webpack_require__.e(6175), __webpack_require__.e(2454), __webpack_require__.e(397), __webpack_require__.e(4730), __webpack_require__.e(5034), __webpack_require__.e(5806), __webpack_require__.e(9635), __webpack_require__.e(7001)]).then(__webpack_require__.bind(__webpack_require__, 9132));
});
var ProjectInfor = function ProjectInfor() {
  var params = (0,_umi_production_exports.useParams)();
  var _useState = (0,react.useState)({}),
    _useState2 = slicedToArray_default()(_useState, 2),
    projectData = _useState2[0],
    setProjectData = _useState2[1];
  var getProjectList = /*#__PURE__*/function () {
    var _ref = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
      var res;
      return regeneratorRuntime_default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _context.next = 2;
            return (0,projects/* projectList */.d9)({
              page: 1,
              size: constanst/* DEFAULT_PAGE_SIZE_ALL */.YY,
              filters: [['iot_project', 'id', 'like', params.id]]
            });
          case 2:
            res = _context.sent;
            setProjectData(res.data[0]);
          case 4:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }));
    return function getProjectList() {
      return _ref.apply(this, arguments);
    };
  }();
  (0,react.useEffect)(function () {
    getProjectList();
  }, []);
  var _useState3 = (0,react.useState)(false),
    _useState4 = slicedToArray_default()(_useState3, 2),
    isUpdatingProject = _useState4[0],
    setIsUpdatingProject = _useState4[1];
  var updateFormRef = (0,react.useRef)(null);
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  var access = (0,_umi_production_exports.useAccess)();
  var canUpdateProject = access.canUpdateInProjectManagement();
  var canDeleteProject = access.canDeleteInProjectManagement();
  var extraPage = [];
  if (canUpdateProject) {
    extraPage.push( /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
      onClick: function onClick() {
        var _updateFormRef$curren, _updateFormRef$curren2;
        (_updateFormRef$curren = updateFormRef.current) === null || _updateFormRef$curren === void 0 || (_updateFormRef$curren2 = _updateFormRef$curren.submit) === null || _updateFormRef$curren2 === void 0 || _updateFormRef$curren2.call(_updateFormRef$curren);
      },
      type: "primary",
      icon: /*#__PURE__*/(0,jsx_runtime.jsx)(SaveOutlined/* default */.Z, {}),
      loading: isUpdatingProject,
      children: formatMessage({
        id: 'common.save'
      })
    }, 'save'));
  }
  if (canDeleteProject) {
    /*#__PURE__*/(0,jsx_runtime.jsx)(Detail_DeleteProject, {
      name: params.id
    }, 'delete');
  }
  return /*#__PURE__*/(0,jsx_runtime.jsx)(PageContainerTabsWithSearch/* default */.Z, {
    pageTitle: "Chi ti\\u1EBFt d\\u1EF1 \\xE1n".concat(projectData !== null && projectData !== void 0 && projectData.label ? ": ".concat(projectData.label) : ''),
    tabsItems: [{
      label: formatMessage({
        id: 'common.detail'
      }),
      component: function component() {
        return /*#__PURE__*/(0,jsx_runtime.jsx)(UpdateProjectForm, {
          ref: updateFormRef,
          projectId: params.id,
          onSubmittingChange: setIsUpdatingProject
        });
      },
      extraPage: [extraPage],
      fallback: /*#__PURE__*/(0,jsx_runtime.jsx)(Descriptions/* DescriptionsSkeleton */.Yk, {
        active: true
      })
    }, {
      label: formatMessage({
        id: 'common.zone'
      }),
      component: function component() {
        return /*#__PURE__*/(0,jsx_runtime.jsx)(ZoneInProject, {
          projectId: params.id
        });
      }
    }],
    autoFormatTabKey: true
  });
};
/* harmony default export */ var Detail = (ProjectInfor);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///94713
`)},57250:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Z: function() { return /* binding */ useDeleteProject; }
/* harmony export */ });
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _services_projects__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(78263);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(7837);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(31418);





function useDeleteProject(
  //   {}: // onSuccess,
  // {
  //   // onSuccess?: (res: ProjectRes) => void;
  // }
) {
  var _App$useApp = antd__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z.useApp(),
    message = _App$useApp.message;
  return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.useRequest)( /*#__PURE__*/function () {
    var _ref = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee(projectId) {
      var res;
      return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            if (projectId) {
              _context.next = 2;
              break;
            }
            throw new Error('Project not found');
          case 2:
            _context.next = 4;
            return (0,_services_projects__WEBPACK_IMPORTED_MODULE_2__/* .projectDelete */ .g6)(projectId);
          case 4:
            res = _context.sent;
            return _context.abrupt("return", {
              data: res
            });
          case 6:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }));
    return function (_x) {
      return _ref.apply(this, arguments);
    };
  }(), {
    manual: true,
    onSuccess: function onSuccess(res) {
      message.success('X\xF3a th\xE0nh c\xF4ng');
      _umijs_max__WEBPACK_IMPORTED_MODULE_3__.history.push('/project-management');
    },
    onError: function onError(err) {
      console.log('err: ', err);
      message.error('\u0110\xE3 c\xF3 l\u1ED7i x\u1EA3y ra, vui l\xF2ng th\u1EED l\u1EA1i');
    }
  });
}//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///57250
`)},7369:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   W: function() { return /* binding */ toFirstUpperCase; },
/* harmony export */   w: function() { return /* binding */ nonAccentVietnamese; }
/* harmony export */ });
function nonAccentVietnamese(str) {
  var _str$toString;
  var _str = str === null || str === void 0 || (_str$toString = str.toString) === null || _str$toString === void 0 ? void 0 : _str$toString.call(str);
  if (typeof _str !== 'string') return str;
  _str = _str.toLowerCase();
  //     We can also use this instead of from line 11 to line 17
  //     str = str.replace(/\\u00E0|\\u00E1|\\u1EA1|\\u1EA3|\\u00E3|\\u00E2|\\u1EA7|\\u1EA5|\\u1EAD|\\u1EA9|\\u1EAB|\\u0103|\\u1EB1|\\u1EAF|\\u1EB7|\\u1EB3|\\u1EB5/g, "a");
  //     str = str.replace(/\\u00E8|\\u00E9|\\u1EB9|\\u1EBB|\\u1EBD|\\u00EA|\\u1EC1|\\u1EBF|\\u1EC7|\\u1EC3|\\u1EC5/g, "e");
  //     str = str.replace(/\\u00EC|\\u00ED|\\u1ECB|\\u1EC9|\\u0129/g, "i");
  //     str = str.replace(/\\u00F2|\\u00F3|\\u1ECD|\\u1ECF|\\u00F5|\\u00F4|\\u1ED3|\\u1ED1|\\u1ED9|\\u1ED5|\\u1ED7|\\u01A1|\\u1EDD|\\u1EDB|\\u1EE3|\\u1EDF|\\u1EE1/g, "o");
  //     str = str.replace(/\\u00F9|\\u00FA|\\u1EE5|\\u1EE7|\\u0169|\\u01B0|\\u1EEB|\\u1EE9|\\u1EF1|\\u1EED|\\u1EEF/g, "u");
  //     str = str.replace(/\\u1EF3|\\u00FD|\\u1EF5|\\u1EF7|\\u1EF9/g, "y");
  //     str = str.replace(/\\u0111/g, "d");
  _str = _str.replace(/\xE0|\xE1|\u1EA1|\u1EA3|\xE3|\xE2|\u1EA7|\u1EA5|\u1EAD|\u1EA9|\u1EAB|\u0103|\u1EB1|\u1EAF|\u1EB7|\u1EB3|\u1EB5/g, 'a');
  _str = _str.replace(/\xE8|\xE9|\u1EB9|\u1EBB|\u1EBD|\xEA|\u1EC1|\u1EBF|\u1EC7|\u1EC3|\u1EC5/g, 'e');
  _str = _str.replace(/\xEC|\xED|\u1ECB|\u1EC9|\u0129/g, 'i');
  _str = _str.replace(/\xF2|\xF3|\u1ECD|\u1ECF|\xF5|\xF4|\u1ED3|\u1ED1|\u1ED9|\u1ED5|\u1ED7|\u01A1|\u1EDD|\u1EDB|\u1EE3|\u1EDF|\u1EE1/g, 'o');
  _str = _str.replace(/\xF9|\xFA|\u1EE5|\u1EE7|\u0169|\u01B0|\u1EEB|\u1EE9|\u1EF1|\u1EED|\u1EEF/g, 'u');
  _str = _str.replace(/\u1EF3|\xFD|\u1EF5|\u1EF7|\u1EF9/g, 'y');
  _str = _str.replace(/\u0111/g, 'd');
  // Some system encode vietnamese combining accent as individual utf-8 characters
  _str = _str.replace(/\\u0300|\\u0301|\\u0303|\\u0309|\\u0323/g, ''); // Huy\u1EC1n s\u1EAFc h\u1ECFi ng\xE3 n\u1EB7ng
  _str = _str.replace(/\\u02C6|\\u0306|\\u031B/g, ''); // \xC2, \xCA, \u0102, \u01A0, \u01AF
  return _str.split(',').join('');
}
var toFirstUpperCase = function toFirstUpperCase(str) {
  if (!str) return '';
  return str.charAt(0).toUpperCase() + str.slice(1);
};//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///7369
`)},75302:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var _hooks_useBreakpoint__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(25378);
"use client";




// Do not export params
function useBreakpoint() {
  return (0,_hooks_useBreakpoint__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .Z)();
}

/* harmony default export */ __webpack_exports__.ZP = ({
  useBreakpoint
});//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNzUzMDIuanMiLCJtYXBwaW5ncyI6IjtBQUFBOztBQUV3QjtBQUNrQztBQUNsQztBQUN4QjtBQUNBO0FBQ0EsU0FBUyx5RUFBcUI7QUFDOUI7QUFDb0I7QUFDcEIsdURBQWU7QUFDZjtBQUNBLENBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hbnQtZGVzaWduLXByby8uL25vZGVfbW9kdWxlcy9hbnRkL2VzL2dyaWQvaW5kZXguanM/YTQxNyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcblxuaW1wb3J0IENvbCBmcm9tICcuL2NvbCc7XG5pbXBvcnQgdXNlSW50ZXJuYWxCcmVha3BvaW50IGZyb20gJy4vaG9va3MvdXNlQnJlYWtwb2ludCc7XG5pbXBvcnQgUm93IGZyb20gJy4vcm93Jztcbi8vIERvIG5vdCBleHBvcnQgcGFyYW1zXG5mdW5jdGlvbiB1c2VCcmVha3BvaW50KCkge1xuICByZXR1cm4gdXNlSW50ZXJuYWxCcmVha3BvaW50KCk7XG59XG5leHBvcnQgeyBDb2wsIFJvdyB9O1xuZXhwb3J0IGRlZmF1bHQge1xuICB1c2VCcmVha3BvaW50XG59OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==
//# sourceURL=webpack-internal:///75302
`)}}]);
