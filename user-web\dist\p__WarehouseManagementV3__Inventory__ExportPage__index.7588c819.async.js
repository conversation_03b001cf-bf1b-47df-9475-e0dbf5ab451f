"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[8035],{5717:function(__unused_webpack_module,__webpack_exports__){eval(`// This icon file is generated automatically.
var EyeOutlined = { "icon": { "tag": "svg", "attrs": { "viewBox": "64 64 896 896", "focusable": "false" }, "children": [{ "tag": "path", "attrs": { "d": "M942.2 486.2C847.4 286.5 704.1 186 512 186c-192.2 0-335.4 100.5-430.2 300.3a60.3 60.3 0 000 51.5C176.6 737.5 319.9 838 512 838c192.2 0 335.4-100.5 430.2-300.3 7.7-16.2 7.7-35 0-51.5zM512 766c-161.3 0-279.4-81.8-362.7-254C232.6 339.8 350.7 258 512 258c161.3 0 279.4 81.8 362.7 254C791.5 684.2 673.4 766 512 766zm-4-430c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm0 288c-61.9 0-112-50.1-112-112s50.1-112 112-112 112 50.1 112 112-50.1 112-112 112z" } }] }, "name": "eye", "theme": "outlined" };
/* harmony default export */ __webpack_exports__.Z = (EyeOutlined);
//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNTcxNy5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBLG9CQUFvQixVQUFVLHlCQUF5QixrREFBa0QsaUJBQWlCLDBCQUEwQix3ZUFBd2UsR0FBRztBQUMvbkIsc0RBQWUsV0FBVyxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvQGFudC1kZXNpZ24vaWNvbnMtc3ZnL2VzL2Fzbi9FeWVPdXRsaW5lZC5qcz80MTU1Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIFRoaXMgaWNvbiBmaWxlIGlzIGdlbmVyYXRlZCBhdXRvbWF0aWNhbGx5LlxudmFyIEV5ZU91dGxpbmVkID0geyBcImljb25cIjogeyBcInRhZ1wiOiBcInN2Z1wiLCBcImF0dHJzXCI6IHsgXCJ2aWV3Qm94XCI6IFwiNjQgNjQgODk2IDg5NlwiLCBcImZvY3VzYWJsZVwiOiBcImZhbHNlXCIgfSwgXCJjaGlsZHJlblwiOiBbeyBcInRhZ1wiOiBcInBhdGhcIiwgXCJhdHRyc1wiOiB7IFwiZFwiOiBcIk05NDIuMiA0ODYuMkM4NDcuNCAyODYuNSA3MDQuMSAxODYgNTEyIDE4NmMtMTkyLjIgMC0zMzUuNCAxMDAuNS00MzAuMiAzMDAuM2E2MC4zIDYwLjMgMCAwMDAgNTEuNUMxNzYuNiA3MzcuNSAzMTkuOSA4MzggNTEyIDgzOGMxOTIuMiAwIDMzNS40LTEwMC41IDQzMC4yLTMwMC4zIDcuNy0xNi4yIDcuNy0zNSAwLTUxLjV6TTUxMiA3NjZjLTE2MS4zIDAtMjc5LjQtODEuOC0zNjIuNy0yNTRDMjMyLjYgMzM5LjggMzUwLjcgMjU4IDUxMiAyNThjMTYxLjMgMCAyNzkuNCA4MS44IDM2Mi43IDI1NEM3OTEuNSA2ODQuMiA2NzMuNCA3NjYgNTEyIDc2NnptLTQtNDMwYy05Ny4yIDAtMTc2IDc4LjgtMTc2IDE3NnM3OC44IDE3NiAxNzYgMTc2IDE3Ni03OC44IDE3Ni0xNzYtNzguOC0xNzYtMTc2LTE3NnptMCAyODhjLTYxLjkgMC0xMTItNTAuMS0xMTItMTEyczUwLjEtMTEyIDExMi0xMTIgMTEyIDUwLjEgMTEyIDExMi01MC4xIDExMi0xMTIgMTEyelwiIH0gfV0gfSwgXCJuYW1lXCI6IFwiZXllXCIsIFwidGhlbWVcIjogXCJvdXRsaW5lZFwiIH07XG5leHBvcnQgZGVmYXVsdCBFeWVPdXRsaW5lZDtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==
//# sourceURL=webpack-internal:///5717
`)},61592:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(5574);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _services_customerUser__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(40063);
/* harmony import */ var _services_stock_deliveryNote__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(14329);
/* harmony import */ var _services_stock_warehouse__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(18327);
/* harmony import */ var _services_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(467);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(7837);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(71230);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(15746);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(42075);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(96074);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(67839);
/* harmony import */ var antd_use_styles__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(38513);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(67294);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(85893);











// import './index.scss';



var useStyles = (0,antd_use_styles__WEBPACK_IMPORTED_MODULE_8__/* .createStyles */ .k)(function (_ref) {
  var token = _ref.token;
  return {
    slideFooter: {
      color: 'white'
    },
    textLarge: {
      fontSize: token.fontSizeHeading1 + 10,
      fontWeight: 700
    },
    textMedium: {
      fontSize: token.fontSizeHeading3,
      fontWeight: token.fontWeightStrong
    },
    text: {
      fontSize: token.fontSizeHeading4,
      fontWeight: token.fontWeightStrong,
      lineHeight: 1.5
    },
    textUppercase: {
      textTransform: 'uppercase'
    }
  };
});
var GeneralExportPrint = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.memo(function (_ref2) {
  var warehouse = _ref2.warehouse,
    start_date = _ref2.start_date,
    end_date = _ref2.end_date,
    openPrint = _ref2.openPrint,
    onDataLoaded = _ref2.onDataLoaded;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(''),
    _useState2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2___default()(_useState, 2),
    warehouseLabel = _useState2[0],
    setWarehouseLabel = _useState2[1];
  //current user
  var _useModel = (0,_umijs_max__WEBPACK_IMPORTED_MODULE_7__.useModel)("@@initialState"),
    initialState = _useModel.initialState;
  var curentUser = initialState === null || initialState === void 0 ? void 0 : initialState.currentUser;
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(),
    _useState4 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2___default()(_useState3, 2),
    user = _useState4[0],
    setUser = _useState4[1];
  var _useRequest = (0,_umijs_max__WEBPACK_IMPORTED_MODULE_7__.useRequest)(_services_stock_deliveryNote__WEBPACK_IMPORTED_MODULE_4__/* .getDeliveryNote */ .M_, {
      manual: true,
      onError: function onError(error) {
        console.log('error', error.message);
      },
      onSuccess: function onSuccess(data, params) {
        if (openPrint) {
          setTimeout(function () {
            window.print();
          }, 1000);
        }
        if (onDataLoaded) {
          onDataLoaded(data);
        }
      }
    }),
    data = _useRequest.data,
    run = _useRequest.run;
  var _useRequest2 = (0,_umijs_max__WEBPACK_IMPORTED_MODULE_7__.useRequest)(_services_stock_warehouse__WEBPACK_IMPORTED_MODULE_5__/* .getWarehouseList */ .Aq, {
      manual: true,
      onSuccess: function onSuccess(data, params) {
        var _data$at;
        setWarehouseLabel(((_data$at = data.at(0)) === null || _data$at === void 0 ? void 0 : _data$at.label) || '');
      }
    }),
    runWarehouse = _useRequest2.run;
  (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(function () {
    console.log({
      curentUser: curentUser
    });
    var fetchData = /*#__PURE__*/function () {
      var _ref3 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee() {
        var fetchUser;
        return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee$(_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              _context.next = 2;
              return (0,_services_customerUser__WEBPACK_IMPORTED_MODULE_3__/* .getCustomerUserList */ .J9)({
                filters: [['iot_customer_user', 'name', 'like', curentUser === null || curentUser === void 0 ? void 0 : curentUser.user_id]]
              });
            case 2:
              fetchUser = _context.sent;
              setUser(fetchUser.data.at(0));
            case 4:
            case "end":
              return _context.stop();
          }
        }, _callee);
      }));
      return function fetchData() {
        return _ref3.apply(this, arguments);
      };
    }();
    fetchData();
    var filters = [];
    if (warehouse && warehouse !== 'all') {
      filters.push(['set_warehouse', 'like', warehouse]);
    }
    run({
      filters: filters,
      start_date: start_date,
      end_date: end_date
    });
    if (warehouse !== 'all') {
      runWarehouse({
        filters: [['Warehouse', 'name', '=', warehouse]]
      });
    } else {
      setWarehouseLabel('T\u1EA5t c\u1EA3');
    }
  }, [warehouse, start_date, end_date, curentUser]);
  var columns = [{
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_7__.FormattedMessage, {
      id: "common.index"
    }),
    dataIndex: 'index',
    render: function render(dom, entity, index) {
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)("div", {
        children: index + 1
      });
    },
    width: 10
  }, {
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_7__.FormattedMessage, {
      id: "common.voucher_code"
    }),
    width: 15,
    render: function render(value, record, index) {
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)("div", {
        style: {
          whiteSpace: 'pre-wrap'
        },
        children: record.name
      });
    }
  }, {
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_7__.FormattedMessage, {
      id: "common.creation"
    }),
    width: 15,
    render: function render(value, record, index) {
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)("div", {
        children: (0,_services_utils__WEBPACK_IMPORTED_MODULE_6__/* .formatDate */ .p6)(record.creation)
      });
    }
  }, {
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_7__.FormattedMessage, {
      id: "common.supplier_name"
    }),
    width: 15,
    render: function render(value, record, index) {
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)("div", {
        style: {
          whiteSpace: 'pre-wrap'
        },
        children: record.customer_label
      });
    }
  }, {
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_7__.FormattedMessage, {
      id: "common.grand_total"
    }),
    width: 15,
    render: function render(value, record, index) {
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)("div", {
        children: (0,_services_utils__WEBPACK_IMPORTED_MODULE_6__/* .formatNumeralNoDecimal */ .iX)(record.rounded_total)
      });
    }
  }, {
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_7__.FormattedMessage, {
      id: "common.creator"
    }),
    width: 15,
    render: function render(value, record, index) {
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)("div", {
        children: "".concat(record.user_first_name, " ").concat(record.user_last_name)
      });
    }
  }];
  var renderHeader = function renderHeader() {
    return 'CH\u1EE8NG T\u1EEA XU\u1EA4T H\xC0NG';
  };
  var renderWarehouse = function renderWarehouse() {
    return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .Z, {
      children: ["Kho: ", warehouseLabel]
    });
  };
  var getPageMargins = function getPageMargins() {
    return "@page { margin: 0 !important; } ".concat(openPrint ? '.ant-table table { font-size: 10px; }' : '');
  };
  (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(function () {}, [curentUser]);
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxs)("div", {
    style: {
      width: '100%'
    },
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)("style", {
      children: getPageMargins()
    }), data && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.Fragment, {
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(antd__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .Z, {
        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(antd__WEBPACK_IMPORTED_MODULE_12__/* ["default"] */ .Z, {
          span: 24,
          style: {
            display: 'flex',
            justifyContent: 'center'
          },
          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(antd__WEBPACK_IMPORTED_MODULE_13__/* ["default"] */ .Z, {
            align: "center",
            direction: "vertical",
            children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(antd__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .Z, {
              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)("b", {
                children: renderHeader()
              })
            })
          })
        })
      }), ' ', /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .Z, {
        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(antd__WEBPACK_IMPORTED_MODULE_12__/* ["default"] */ .Z, {
          span: 8,
          offset: 4,
          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(antd__WEBPACK_IMPORTED_MODULE_13__/* ["default"] */ .Z, {
            align: "center",
            direction: "vertical",
            children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(antd__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .Z, {
              children: "".concat(user === null || user === void 0 ? void 0 : user.first_name, " ").concat(user === null || user === void 0 ? void 0 : user.last_name)
            })
          })
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_12__/* ["default"] */ .Z, {
          span: 8,
          offset: 4,
          children: [renderWarehouse(), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .Z, {
            children: ["T\\u1EEB ", (0,_services_utils__WEBPACK_IMPORTED_MODULE_6__/* .formatDate */ .p6)(start_date), " \\u0111\\u1EBFn ", (0,_services_utils__WEBPACK_IMPORTED_MODULE_6__/* .formatDate */ .p6)(end_date)]
          })]
        })]
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(antd__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .Z, {}), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)("br", {}), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(antd__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .Z, {
        columns: columns,
        size: "small",
        dataSource: data || [],
        rowKey: 'index',
        pagination: false
      })]
    })]
  });
});
/* harmony default export */ __webpack_exports__.Z = (GeneralExportPrint);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///61592
`)},99927:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(5574);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _services_customerUser__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(40063);
/* harmony import */ var _services_stock_purchaseReceipt__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(33326);
/* harmony import */ var _services_stock_warehouse__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(18327);
/* harmony import */ var _services_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(467);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(7837);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(71230);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(15746);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(42075);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(96074);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(67839);
/* harmony import */ var antd_use_styles__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(38513);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(67294);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(85893);











// import './index.scss';



var useStyles = (0,antd_use_styles__WEBPACK_IMPORTED_MODULE_8__/* .createStyles */ .k)(function (_ref) {
  var token = _ref.token;
  return {
    slideFooter: {
      color: 'white'
    },
    textLarge: {
      fontSize: token.fontSizeHeading1 + 10,
      fontWeight: 700
    },
    textMedium: {
      fontSize: token.fontSizeHeading3,
      fontWeight: token.fontWeightStrong
    },
    text: {
      fontSize: token.fontSizeHeading4,
      fontWeight: token.fontWeightStrong,
      lineHeight: 1.5
    },
    textUppercase: {
      textTransform: 'uppercase'
    }
  };
});
var GeneralImportPrint = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.memo(function (_ref2) {
  var warehouse = _ref2.warehouse,
    start_date = _ref2.start_date,
    end_date = _ref2.end_date,
    openPrint = _ref2.openPrint,
    onDataLoaded = _ref2.onDataLoaded;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(''),
    _useState2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2___default()(_useState, 2),
    warehouseLabel = _useState2[0],
    setWarehouseLabel = _useState2[1];
  //current user
  var _useModel = (0,_umijs_max__WEBPACK_IMPORTED_MODULE_7__.useModel)("@@initialState"),
    initialState = _useModel.initialState;
  var curentUser = initialState === null || initialState === void 0 ? void 0 : initialState.currentUser;
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(),
    _useState4 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2___default()(_useState3, 2),
    user = _useState4[0],
    setUser = _useState4[1];
  var _useRequest = (0,_umijs_max__WEBPACK_IMPORTED_MODULE_7__.useRequest)(_services_stock_purchaseReceipt__WEBPACK_IMPORTED_MODULE_4__/* .getImportReceipts */ .xA, {
      manual: true,
      onError: function onError(error) {
        console.log('error', error.message);
      },
      onSuccess: function onSuccess(data, params) {
        if (openPrint) {
          setTimeout(function () {
            window.print();
          }, 1000);
        }
        if (onDataLoaded) {
          onDataLoaded(data);
        }
      }
    }),
    data = _useRequest.data,
    run = _useRequest.run;
  var _useRequest2 = (0,_umijs_max__WEBPACK_IMPORTED_MODULE_7__.useRequest)(_services_stock_warehouse__WEBPACK_IMPORTED_MODULE_5__/* .getWarehouseList */ .Aq, {
      manual: true,
      onSuccess: function onSuccess(data, params) {
        var _data$at;
        setWarehouseLabel(((_data$at = data.at(0)) === null || _data$at === void 0 ? void 0 : _data$at.label) || '');
      }
    }),
    runWarehouse = _useRequest2.run;
  (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(function () {
    console.log({
      curentUser: curentUser
    });
    var fetchData = /*#__PURE__*/function () {
      var _ref3 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee() {
        var fetchUser;
        return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee$(_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              _context.next = 2;
              return (0,_services_customerUser__WEBPACK_IMPORTED_MODULE_3__/* .getCustomerUserList */ .J9)({
                filters: [['iot_customer_user', 'name', 'like', curentUser === null || curentUser === void 0 ? void 0 : curentUser.user_id]]
              });
            case 2:
              fetchUser = _context.sent;
              setUser(fetchUser.data.at(0));
            case 4:
            case "end":
              return _context.stop();
          }
        }, _callee);
      }));
      return function fetchData() {
        return _ref3.apply(this, arguments);
      };
    }();
    fetchData();
    var filters = [];
    if (warehouse && warehouse !== 'all') {
      filters.push(['set_warehouse', 'like', warehouse]);
    }
    run({
      filters: filters,
      start_date: start_date,
      end_date: end_date
    });
    if (warehouse !== 'all') {
      runWarehouse({
        filters: [['Warehouse', 'name', '=', warehouse]]
      });
    } else {
      setWarehouseLabel('T\u1EA5t c\u1EA3');
    }
  }, [warehouse, start_date, end_date, curentUser]);
  var columns = [{
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_7__.FormattedMessage, {
      id: "common.index"
    }),
    dataIndex: 'index',
    render: function render(dom, entity, index) {
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)("div", {
        children: index + 1
      });
    },
    width: 10
  }, {
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_7__.FormattedMessage, {
      id: "common.voucher_code"
    }),
    width: 15,
    render: function render(value, record, index) {
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)("div", {
        style: {
          whiteSpace: 'pre-wrap'
        },
        children: record.name
      });
    }
  }, {
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_7__.FormattedMessage, {
      id: "common.creation"
    }),
    width: 15,
    render: function render(value, record, index) {
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)("div", {
        children: (0,_services_utils__WEBPACK_IMPORTED_MODULE_6__/* .formatDate */ .p6)(record.creation)
      });
    }
  }, {
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_7__.FormattedMessage, {
      id: "common.supplier_name"
    }),
    width: 15,
    render: function render(value, record, index) {
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)("div", {
        style: {
          whiteSpace: 'pre-wrap'
        },
        children: record.supplier_label
      });
    }
  }, {
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_7__.FormattedMessage, {
      id: "common.grand_total"
    }),
    width: 15,
    render: function render(value, record, index) {
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)("div", {
        children: (0,_services_utils__WEBPACK_IMPORTED_MODULE_6__/* .formatNumeralNoDecimal */ .iX)(record.rounded_total)
      });
    }
  }, {
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_7__.FormattedMessage, {
      id: "common.creator"
    }),
    width: 15,
    render: function render(value, record, index) {
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)("div", {
        children: "".concat(record.user_first_name, " ").concat(record.user_last_name)
      });
    }
  }];
  var renderHeader = function renderHeader() {
    return 'CH\u1EE8NG T\u1EEA NH\u1EACP H\xC0NG';
  };
  var renderWarehouse = function renderWarehouse() {
    return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .Z, {
      children: ["Kho: ", warehouseLabel]
    });
  };
  var getPageMargins = function getPageMargins() {
    return "@page { margin: 0 !important; } ".concat(openPrint ? '.ant-table table { font-size: 10px; }' : '');
  };
  (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(function () {}, [curentUser]);
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxs)("div", {
    style: {
      width: '100%'
    },
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)("style", {
      children: getPageMargins()
    }), data && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.Fragment, {
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(antd__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .Z, {
        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(antd__WEBPACK_IMPORTED_MODULE_12__/* ["default"] */ .Z, {
          span: 24,
          style: {
            display: 'flex',
            justifyContent: 'center'
          },
          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(antd__WEBPACK_IMPORTED_MODULE_13__/* ["default"] */ .Z, {
            align: "center",
            direction: "vertical",
            children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(antd__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .Z, {
              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)("b", {
                children: renderHeader()
              })
            })
          })
        })
      }), ' ', /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .Z, {
        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(antd__WEBPACK_IMPORTED_MODULE_12__/* ["default"] */ .Z, {
          span: 8,
          offset: 4,
          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(antd__WEBPACK_IMPORTED_MODULE_13__/* ["default"] */ .Z, {
            align: "center",
            direction: "vertical",
            children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(antd__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .Z, {
              children: "".concat(user === null || user === void 0 ? void 0 : user.first_name, " ").concat(user === null || user === void 0 ? void 0 : user.last_name)
            })
          })
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_12__/* ["default"] */ .Z, {
          span: 8,
          offset: 4,
          children: [renderWarehouse(), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .Z, {
            children: ["T\\u1EEB ", (0,_services_utils__WEBPACK_IMPORTED_MODULE_6__/* .formatDate */ .p6)(start_date), " \\u0111\\u1EBFn ", (0,_services_utils__WEBPACK_IMPORTED_MODULE_6__/* .formatDate */ .p6)(end_date)]
          })]
        })]
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(antd__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .Z, {}), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)("br", {}), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(antd__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .Z, {
        columns: columns,
        size: "small",
        dataSource: data || [],
        rowKey: 'index',
        pagination: false
      })]
    })]
  });
});
/* harmony default export */ __webpack_exports__.Z = (GeneralImportPrint);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///99927
`)},90864:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(5574);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _services_customerUser__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(40063);
/* harmony import */ var _services_stock_dashboard__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(98041);
/* harmony import */ var _services_stock_warehouse__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(18327);
/* harmony import */ var _services_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(467);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(7837);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(71230);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(15746);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(42075);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(96074);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(67839);
/* harmony import */ var antd_use_styles__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(38513);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(67294);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(85893);














var useStyles = (0,antd_use_styles__WEBPACK_IMPORTED_MODULE_8__/* .createStyles */ .k)(function (_ref) {
  var token = _ref.token;
  return {
    slideFooter: {
      color: 'white'
    },
    textLarge: {
      fontSize: token.fontSizeHeading1 + 10,
      fontWeight: 700
    },
    textMedium: {
      fontSize: token.fontSizeHeading3,
      fontWeight: token.fontWeightStrong
    },
    text: {
      fontSize: token.fontSizeHeading4,
      fontWeight: token.fontWeightStrong,
      lineHeight: 1.5
    },
    textUppercase: {
      textTransform: 'uppercase'
    }
  };
});
var ImportExportPrint = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.memo(function (_ref2) {
  var warehouse = _ref2.warehouse,
    start_date = _ref2.start_date,
    end_date = _ref2.end_date,
    openPrint = _ref2.openPrint,
    item_code_list = _ref2.item_code_list,
    onDataLoaded = _ref2.onDataLoaded;
  console.log('memo');
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(''),
    _useState2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2___default()(_useState, 2),
    warehouseLabel = _useState2[0],
    setWarehouseLabel = _useState2[1];
  //current user
  var _useModel = (0,_umijs_max__WEBPACK_IMPORTED_MODULE_7__.useModel)("@@initialState"),
    initialState = _useModel.initialState;
  var curentUser = initialState === null || initialState === void 0 ? void 0 : initialState.currentUser;
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(),
    _useState4 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2___default()(_useState3, 2),
    user = _useState4[0],
    setUser = _useState4[1];
  var _useRequest = (0,_umijs_max__WEBPACK_IMPORTED_MODULE_7__.useRequest)(_services_stock_dashboard__WEBPACK_IMPORTED_MODULE_4__/* .getTotalItemQtyInRange */ .yI, {
      manual: true,
      onError: function onError(error) {
        console.log('error', error.message);
      },
      onSuccess: function onSuccess(data, params) {
        if (openPrint) {
          setTimeout(function () {
            window.print();
          }, 1000);
        }
        if (onDataLoaded) {
          onDataLoaded(data); // Pass data to parent component
        }
      }
    }),
    data = _useRequest.data,
    run = _useRequest.run;
  var _useRequest2 = (0,_umijs_max__WEBPACK_IMPORTED_MODULE_7__.useRequest)(_services_stock_warehouse__WEBPACK_IMPORTED_MODULE_5__/* .getWarehouseList */ .Aq, {
      manual: true,
      onSuccess: function onSuccess(data, params) {
        var _data$at;
        setWarehouseLabel(((_data$at = data.at(0)) === null || _data$at === void 0 ? void 0 : _data$at.label) || '');
      }
    }),
    runWarehouse = _useRequest2.run;
  (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(function () {
    console.log({
      item_code_list: item_code_list
    });
    console.log({
      curentUser: curentUser
    });
    var fetchData = /*#__PURE__*/function () {
      var _ref3 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee() {
        var fetchUser;
        return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee$(_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              _context.next = 2;
              return (0,_services_customerUser__WEBPACK_IMPORTED_MODULE_3__/* .getCustomerUserList */ .J9)({
                filters: [['iot_customer_user', 'name', 'like', curentUser === null || curentUser === void 0 ? void 0 : curentUser.user_id]]
              });
            case 2:
              fetchUser = _context.sent;
              setUser(fetchUser.data.at(0));
            case 4:
            case "end":
              return _context.stop();
          }
        }, _callee);
      }));
      return function fetchData() {
        return _ref3.apply(this, arguments);
      };
    }();
    fetchData();
    run({
      warehouse: warehouse === 'all' ? undefined : warehouse,
      start_date: start_date,
      end_date: end_date,
      item_code_list: item_code_list
    });
    if (warehouse !== 'all') {
      runWarehouse({
        filters: [['Warehouse', 'name', '=', warehouse]]
      });
    } else {
      setWarehouseLabel('T\u1EA5t c\u1EA3');
    }
  }, [warehouse, start_date, end_date, item_code_list, curentUser]);
  var columns = [{
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_7__.FormattedMessage, {
      id: "common.index"
    }),
    dataIndex: 'index',
    render: function render(dom, entity, index) {
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)("div", {
        children: index + 1
      });
    },
    width: 10
  }, {
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_7__.FormattedMessage, {
      id: "warehouse-management.stock-entry-history.item_id"
    }),
    dataIndex: 'item_code',
    width: 15
  }, {
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_7__.FormattedMessage, {
      id: "warehouse-management.stock-entry-history.item_label"
    }),
    dataIndex: 'item_label',
    width: 15,
    render: function render(value, record, index) {
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)("div", {
        style: {
          whiteSpace: 'pre-wrap'
        },
        children: record.item_label
      });
    }
  }, {
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_7__.FormattedMessage, {
      id: "common.item_group"
    }),
    dataIndex: 'item_group_label',
    width: 10,
    render: function render(text, record, index) {
      if (index > 0 && data[index - 1].item_group_label === text) {
        return {
          children: null,
          props: {
            rowSpan: 0
          }
        };
      }
      var rowSpan = 1;
      while (index + rowSpan < data.length && data[index + rowSpan].item_group_label === text) {
        rowSpan++;
      }
      return {
        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)("div", {
          style: {
            whiteSpace: 'pre-wrap',
            wordBreak: 'break-word'
          },
          children: text
        }),
        props: {
          rowSpan: rowSpan
        }
      };
    }
  }, {
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_7__.FormattedMessage, {
      id: "common.unit"
    }),
    dataIndex: 'uom_name',
    width: 20,
    render: function render(value, record, index) {
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)("div", {
        style: {
          whiteSpace: 'pre-wrap',
          wordBreak: 'break-word'
        },
        children: record.uom_name
      });
    }
  }, {
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_7__.FormattedMessage, {
      id: "common.begin_qty"
    }),
    dataIndex: 'begin_qty',
    width: 20,
    render: function render(dom, entity, index) {
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)("div", {
        children: (0,_services_utils__WEBPACK_IMPORTED_MODULE_6__/* .formatNumeral */ .GW)(entity.begin_qty)
      });
    }
  }, {
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_7__.FormattedMessage, {
      id: "common.import_qty"
    }),
    dataIndex: 'import_qty',
    width: 10,
    render: function render(dom, entity, index) {
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)("div", {
        children: (0,_services_utils__WEBPACK_IMPORTED_MODULE_6__/* .formatNumeral */ .GW)(entity.import_qty)
      });
    }
  }, {
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_7__.FormattedMessage, {
      id: "common.export_qty"
    }),
    dataIndex: 'export_qty',
    width: 10,
    render: function render(dom, entity, index) {
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)("div", {
        children: (0,_services_utils__WEBPACK_IMPORTED_MODULE_6__/* .formatNumeral */ .GW)(entity.export_qty)
      });
    }
  }, {
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_7__.FormattedMessage, {
      id: "common.end_qty"
    }),
    dataIndex: 'end_qty',
    width: 20,
    render: function render(dom, entity, index) {
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)("div", {
        children: (0,_services_utils__WEBPACK_IMPORTED_MODULE_6__/* .formatNumeral */ .GW)(entity.end_qty)
      });
    }
  }];
  var renderHeader = function renderHeader() {
    return 'T\u1ED2N KHO V\xC0 LU\xC2N CHUY\u1EC2N H\xC0NG';
  };
  var renderWarehouse = function renderWarehouse() {
    return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .Z, {
      children: ["Kho: ", warehouseLabel]
    });
  };
  var getPageMargins = function getPageMargins() {
    return "@page { margin: 0 !important; } ".concat(openPrint ? '.ant-table table { font-size: 10px; }' : '');
  };
  (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(function () {}, [curentUser]);
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxs)("div", {
    style: {
      width: '100%'
    },
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)("style", {
      children: getPageMargins()
    }), data && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.Fragment, {
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(antd__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .Z, {
        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(antd__WEBPACK_IMPORTED_MODULE_12__/* ["default"] */ .Z, {
          span: 24,
          style: {
            display: 'flex',
            justifyContent: 'center'
          },
          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(antd__WEBPACK_IMPORTED_MODULE_13__/* ["default"] */ .Z, {
            align: "center",
            direction: "vertical",
            children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(antd__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .Z, {
              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)("b", {
                children: renderHeader()
              })
            })
          })
        })
      }), ' ', /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .Z, {
        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(antd__WEBPACK_IMPORTED_MODULE_12__/* ["default"] */ .Z, {
          span: 8,
          offset: 4,
          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(antd__WEBPACK_IMPORTED_MODULE_13__/* ["default"] */ .Z, {
            align: "center",
            direction: "vertical",
            children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(antd__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .Z, {
              children: "".concat(user === null || user === void 0 ? void 0 : user.first_name, " ").concat(user === null || user === void 0 ? void 0 : user.last_name)
            })
          })
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_12__/* ["default"] */ .Z, {
          span: 8,
          offset: 4,
          children: [renderWarehouse(), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .Z, {
            children: ["T\\u1EEB ", (0,_services_utils__WEBPACK_IMPORTED_MODULE_6__/* .formatDate */ .p6)(start_date), " \\u0111\\u1EBFn ", (0,_services_utils__WEBPACK_IMPORTED_MODULE_6__/* .formatDate */ .p6)(end_date)]
          })]
        })]
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(antd__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .Z, {}), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)("br", {}), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(antd__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .Z, {
        columns: columns,
        size: "small",
        dataSource: data || [],
        rowKey: 'index',
        pagination: false
      })]
    })]
  });
});
/* harmony default export */ __webpack_exports__.Z = (ImportExportPrint);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///90864
`)},71507:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(5574);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _services_customerUser__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(40063);
/* harmony import */ var _services_stock_dashboard__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(98041);
/* harmony import */ var _services_stock_warehouse__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(18327);
/* harmony import */ var _services_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(467);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(7837);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(71230);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(15746);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(42075);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(96074);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(67839);
/* harmony import */ var antd_use_styles__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(38513);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(67294);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(85893);











// import './index.scss';



var useStyles = (0,antd_use_styles__WEBPACK_IMPORTED_MODULE_8__/* .createStyles */ .k)(function (_ref) {
  var token = _ref.token;
  return {
    slideFooter: {
      color: 'white'
    },
    textLarge: {
      fontSize: token.fontSizeHeading1 + 10,
      fontWeight: 700
    },
    textMedium: {
      fontSize: token.fontSizeHeading3,
      fontWeight: token.fontWeightStrong
    },
    text: {
      fontSize: token.fontSizeHeading4,
      fontWeight: token.fontWeightStrong,
      lineHeight: 1.5
    },
    textUppercase: {
      textTransform: 'uppercase'
    }
  };
});
var ImportExportValuePrint = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.memo(function (_ref2) {
  var warehouse = _ref2.warehouse,
    start_date = _ref2.start_date,
    end_date = _ref2.end_date,
    openPrint = _ref2.openPrint,
    item_code_list = _ref2.item_code_list,
    onDataLoaded = _ref2.onDataLoaded;
  console.log('render nelk');
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(''),
    _useState2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2___default()(_useState, 2),
    warehouseLabel = _useState2[0],
    setWarehouseLabel = _useState2[1];
  //current user
  var _useModel = (0,_umijs_max__WEBPACK_IMPORTED_MODULE_7__.useModel)("@@initialState"),
    initialState = _useModel.initialState;
  var curentUser = initialState === null || initialState === void 0 ? void 0 : initialState.currentUser;
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(),
    _useState4 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2___default()(_useState3, 2),
    user = _useState4[0],
    setUser = _useState4[1];
  var _useRequest = (0,_umijs_max__WEBPACK_IMPORTED_MODULE_7__.useRequest)(_services_stock_dashboard__WEBPACK_IMPORTED_MODULE_4__/* .getTotalItemQtyInRange */ .yI, {
      manual: true,
      onError: function onError(error) {
        console.log('error', error.message);
      },
      onSuccess: function onSuccess(data, params) {
        if (openPrint) {
          setTimeout(function () {
            window.print();
          }, 1000);
        }
        if (onDataLoaded) {
          onDataLoaded(data);
        }
      }
    }),
    data = _useRequest.data,
    run = _useRequest.run;
  var _useRequest2 = (0,_umijs_max__WEBPACK_IMPORTED_MODULE_7__.useRequest)(_services_stock_warehouse__WEBPACK_IMPORTED_MODULE_5__/* .getWarehouseList */ .Aq, {
      manual: true,
      onSuccess: function onSuccess(data, params) {
        var _data$at;
        setWarehouseLabel(((_data$at = data.at(0)) === null || _data$at === void 0 ? void 0 : _data$at.label) || '');
      }
    }),
    runWarehouse = _useRequest2.run;
  (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(function () {
    console.log({
      item_code_list: item_code_list
    });
    console.log({
      curentUser: curentUser
    });
    var fetchData = /*#__PURE__*/function () {
      var _ref3 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee() {
        var fetchUser;
        return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee$(_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              _context.next = 2;
              return (0,_services_customerUser__WEBPACK_IMPORTED_MODULE_3__/* .getCustomerUserList */ .J9)({
                filters: [['iot_customer_user', 'name', 'like', curentUser === null || curentUser === void 0 ? void 0 : curentUser.user_id]]
              });
            case 2:
              fetchUser = _context.sent;
              setUser(fetchUser.data.at(0));
            case 4:
            case "end":
              return _context.stop();
          }
        }, _callee);
      }));
      return function fetchData() {
        return _ref3.apply(this, arguments);
      };
    }();
    fetchData();
    run({
      warehouse: warehouse === 'all' ? undefined : warehouse,
      start_date: start_date,
      end_date: end_date,
      item_code_list: item_code_list
    });
    if (warehouse !== 'all') {
      runWarehouse({
        filters: [['Warehouse', 'name', '=', warehouse]]
      });
    } else {
      setWarehouseLabel('T\u1EA5t c\u1EA3');
    }
  }, [warehouse, start_date, end_date, item_code_list, curentUser]);
  var columns = [{
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_7__.FormattedMessage, {
      id: "common.index"
    }),
    dataIndex: 'index',
    render: function render(dom, entity, index) {
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)("div", {
        children: index + 1
      });
    },
    width: 10
  }, {
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_7__.FormattedMessage, {
      id: "warehouse-management.stock-entry-history.item_id"
    }),
    dataIndex: 'item_code',
    width: 15
  }, {
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_7__.FormattedMessage, {
      id: "warehouse-management.stock-entry-history.item_label"
    }),
    dataIndex: 'item_label',
    width: 15,
    render: function render(value, record, index) {
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)("div", {
        style: {
          whiteSpace: 'pre-wrap'
        },
        children: record.item_label
      });
    }
  }, {
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_7__.FormattedMessage, {
      id: "common.item_group"
    }),
    dataIndex: 'item_group_label',
    width: 10,
    render: function render(text, record, index) {
      if (index > 0 && data[index - 1].item_group_label === text) {
        return {
          children: null,
          props: {
            rowSpan: 0
          }
        };
      }
      var rowSpan = 1;
      while (index + rowSpan < data.length && data[index + rowSpan].item_group_label === text) {
        rowSpan++;
      }
      return {
        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)("div", {
          style: {
            whiteSpace: 'pre-wrap',
            wordBreak: 'break-word'
          },
          children: text
        }),
        props: {
          rowSpan: rowSpan
        }
      };
    }
  }, {
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_7__.FormattedMessage, {
      id: "common.unit"
    }),
    dataIndex: 'uom_name',
    width: 20,
    render: function render(value, record, index) {
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)("div", {
        style: {
          whiteSpace: 'pre-wrap',
          wordBreak: 'break-word'
        },
        children: record.uom_name
      });
    }
  }, {
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_7__.FormattedMessage, {
      id: "common.begin_price"
    }),
    dataIndex: 'begin_price',
    width: 20,
    render: function render(dom, entity, index) {
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)("div", {
        children: (0,_services_utils__WEBPACK_IMPORTED_MODULE_6__/* .formatNumeralNoDecimal */ .iX)(entity.begin_price)
      });
    }
  }, {
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_7__.FormattedMessage, {
      id: "common.import_price"
    }),
    dataIndex: 'import_qty',
    width: 10,
    render: function render(dom, entity, index) {
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)("div", {
        children: (0,_services_utils__WEBPACK_IMPORTED_MODULE_6__/* .formatNumeralNoDecimal */ .iX)(entity.import_price)
      });
    }
  }, {
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_7__.FormattedMessage, {
      id: "common.export_price"
    }),
    dataIndex: 'export_qty',
    width: 10,
    render: function render(dom, entity, index) {
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)("div", {
        children: (0,_services_utils__WEBPACK_IMPORTED_MODULE_6__/* .formatNumeralNoDecimal */ .iX)(entity.export_price)
      });
    }
  }, {
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_7__.FormattedMessage, {
      id: "common.end_price"
    }),
    dataIndex: 'end_qty',
    width: 20,
    render: function render(dom, entity, index) {
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)("div", {
        children: (0,_services_utils__WEBPACK_IMPORTED_MODULE_6__/* .formatNumeralNoDecimal */ .iX)(entity.end_price)
      });
    }
  }];
  var renderHeader = function renderHeader() {
    return 'T\u1ED2N KHO V\xC0 LU\xC2N CHUY\u1EC2N GI\xC1 TR\u1ECA';
  };
  var renderWarehouse = function renderWarehouse() {
    return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .Z, {
      children: ["Kho: ", warehouseLabel]
    });
  };
  var getPageMargins = function getPageMargins() {
    return "@page { margin: 0 !important; } ".concat(openPrint ? '.ant-table table { font-size: 10px; }' : '');
  };
  (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(function () {}, [curentUser]);
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxs)("div", {
    style: {
      width: '100%'
    },
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)("style", {
      children: getPageMargins()
    }), data && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.Fragment, {
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(antd__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .Z, {
        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(antd__WEBPACK_IMPORTED_MODULE_12__/* ["default"] */ .Z, {
          span: 24,
          style: {
            display: 'flex',
            justifyContent: 'center'
          },
          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(antd__WEBPACK_IMPORTED_MODULE_13__/* ["default"] */ .Z, {
            align: "center",
            direction: "vertical",
            children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(antd__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .Z, {
              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)("b", {
                children: renderHeader()
              })
            })
          })
        })
      }), ' ', /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .Z, {
        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(antd__WEBPACK_IMPORTED_MODULE_12__/* ["default"] */ .Z, {
          span: 8,
          offset: 4,
          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(antd__WEBPACK_IMPORTED_MODULE_13__/* ["default"] */ .Z, {
            align: "center",
            direction: "vertical",
            children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(antd__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .Z, {
              children: "".concat(user === null || user === void 0 ? void 0 : user.first_name, " ").concat(user === null || user === void 0 ? void 0 : user.last_name)
            })
          })
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_12__/* ["default"] */ .Z, {
          span: 8,
          offset: 4,
          children: [renderWarehouse(), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .Z, {
            children: ["T\\u1EEB ", (0,_services_utils__WEBPACK_IMPORTED_MODULE_6__/* .formatDate */ .p6)(start_date), " \\u0111\\u1EBFn ", (0,_services_utils__WEBPACK_IMPORTED_MODULE_6__/* .formatDate */ .p6)(end_date)]
          })]
        })]
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(antd__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .Z, {}), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)("br", {}), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(antd__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .Z, {
        columns: columns,
        size: "small",
        dataSource: data || [],
        rowKey: 'index',
        pagination: false
      })]
    })]
  });
});
/* harmony default export */ __webpack_exports__.Z = (ImportExportValuePrint);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///71507
`)},66221:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return /* binding */ Inventory_ExportPage; }
});

// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/slicedToArray.js
var slicedToArray = __webpack_require__(5574);
var slicedToArray_default = /*#__PURE__*/__webpack_require__.n(slicedToArray);
// EXTERNAL MODULE: ./src/.umi-production/exports.ts + 34 modules
var _umi_production_exports = __webpack_require__(7837);
// EXTERNAL MODULE: ./node_modules/@umijs/preset-umi/node_modules/react-router-dom/index.js
var react_router_dom = __webpack_require__(13854);
// EXTERNAL MODULE: ./src/components/ImagePreview/index.tsx
var ImagePreview = __webpack_require__(55396);
// EXTERNAL MODULE: ./src/services/accounts.ts
var accounts = __webpack_require__(63510);
// EXTERNAL MODULE: ./src/services/stock/deliveryNote.ts
var deliveryNote = __webpack_require__(14329);
// EXTERNAL MODULE: ./src/services/utils.ts
var utils = __webpack_require__(467);
// EXTERNAL MODULE: ./node_modules/antd/es/table/index.js + 42 modules
var table = __webpack_require__(67839);
// EXTERNAL MODULE: ./node_modules/antd/es/row/index.js
var row = __webpack_require__(71230);
// EXTERNAL MODULE: ./node_modules/antd/es/col/index.js
var col = __webpack_require__(15746);
// EXTERNAL MODULE: ./node_modules/antd/es/space/index.js + 2 modules
var space = __webpack_require__(42075);
// EXTERNAL MODULE: ./node_modules/antd/es/divider/index.js + 1 modules
var divider = __webpack_require__(96074);
// EXTERNAL MODULE: ./node_modules/antd-use-styles/dist/antd-use-styles.js
var antd_use_styles = __webpack_require__(38513);
// EXTERNAL MODULE: ./node_modules/react/jsx-runtime.js
var jsx_runtime = __webpack_require__(85893);
;// CONCATENATED MODULE: ./src/pages/WarehouseManagementV3/Inventory/ExportPage/components/ExportReceiptPrint.tsx










var useStyles = (0,antd_use_styles/* createStyles */.k)(function (_ref) {
  var token = _ref.token;
  return {
    pageContent: {
      fontFamily: 'Times New Roman, serif',
      '& *': {
        fontFamily: 'Times New Roman, serif !important'
      },
      '& .ant-table': {
        fontFamily: 'Times New Roman, serif !important'
      },
      '& .ant-table th': {
        fontFamily: 'Times New Roman, serif !important'
      },
      '& .ant-table td': {
        fontFamily: 'Times New Roman, serif !important'
      },
      '@media print': {
        '& img': {
          display: 'block',
          visibility: 'visible',
          printColorAdjust: 'exact',
          WebkitPrintColorAdjust: 'exact'
        },
        '& .ant-image': {
          display: 'block',
          visibility: 'visible'
        },
        '& .ant-image-img': {
          display: 'block',
          visibility: 'visible'
        }
      }
    },
    nameText: {
      textTransform: 'uppercase',
      fontWeight: 'bold'
    },
    slideFooter: {
      color: 'white'
    },
    textLarge: {
      fontSize: token.fontSizeHeading1 + 10,
      fontWeight: 700
    },
    textMedium: {
      fontSize: token.fontSizeHeading3,
      fontWeight: token.fontWeightStrong
    },
    text: {
      fontSize: token.fontSizeHeading4,
      fontWeight: token.fontWeightStrong,
      lineHeight: 1.5
    },
    textUppercase: {
      textTransform: 'uppercase'
    }
  };
});
var ExportReceiptPrint = function ExportReceiptPrint(_ref2) {
  var _deliveryData$items;
  var id = _ref2.id;
  var _useModel = (0,_umi_production_exports.useModel)('@@initialState'),
    initialState = _useModel.initialState;
  var styles = useStyles();
  var _useRequest = (0,_umi_production_exports.useRequest)(deliveryNote/* getDeliveryNoteDetail */.r7, {
      manual: false,
      defaultParams: [{
        name: id
      }],
      onSuccess: function onSuccess(data) {
        console.log('Delivery Note Detail:', data);
        setTimeout(function () {
          window.print();
        }, 1000);
      }
    }),
    deliveryData = _useRequest.data,
    deliveryLoading = _useRequest.loading;
  var _useRequest2 = (0,_umi_production_exports.useRequest)(accounts/* getAccountProfile */._v, {
      manual: false,
      onSuccess: function onSuccess(data) {
        console.log('Account Profile:', data);
      }
    }),
    profile = _useRequest2.data,
    profileLoading = _useRequest2.loading;
  var columns = [{
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.index"
    }),
    dataIndex: 'index',
    render: function render(value, record, index) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        children: index + 1
      });
    },
    width: 15
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "warehouse-management.import-history.item_id"
    }),
    dataIndex: 'item_name',
    width: 10
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "warehouse-management.import-history.item_label"
    }),
    dataIndex: 'item_label',
    width: 20
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.unit"
    }),
    dataIndex: 'uom_name',
    width: 20
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.qty"
    }),
    dataIndex: 'qty',
    width: 20,
    render: function render(value, record, index) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        children: (0,utils/* formatNumeral */.GW)(record.qty)
      });
    }
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "warehouse-management.import-history.rate"
    }),
    dataIndex: 'rate',
    width: 20,
    render: function render(value, record, index) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        children: (0,utils/* formatMoneyNumeral */.yp)(record.rate)
      });
    }
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "warehouse-management.import-history.amount"
    }),
    dataIndex: 'amount',
    width: 20,
    render: function render(value, record, index) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        children: (0,utils/* formatMoneyNumeral */.yp)(record.amount)
      });
    }
  }];

  // Show loading state if either request is loading
  if (deliveryLoading || profileLoading) {
    return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
      children: "Loading..."
    });
  }

  // Both requests completed but no data - check after loading is complete
  if (!deliveryData) {
    return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
      children: "No delivery note data available"
    });
  }

  // We'll render even if profile is missing, just show placeholder
  var profileData = profile || {
    full_name: 'N/A',
    address: 'N/A',
    phone_number: 'N/A'
  };
  // T\u1EA1o chu\u1ED7i \u0111\u1ECBa ch\u1EC9 t\u1EEB c\xE1c tr\u01B0\u1EDDng c\xF3 gi\xE1 tr\u1ECB
  var fullAddress = [profile === null || profile === void 0 ? void 0 : profile.customer_address, profile === null || profile === void 0 ? void 0 : profile.customer_ward, profile === null || profile === void 0 ? void 0 : profile.customer_district, profile === null || profile === void 0 ? void 0 : profile.customer_province].filter(function (val) {
    return val;
  }) // Ch\u1EC9 l\u1EA5y nh\u1EEFng gi\xE1 tr\u1ECB t\u1ED3n t\u1EA1i (kh\xE1c null/undefined)
  .join(', '); // N\u1ED1i b\u1EB1ng d\u1EA5u ph\u1EA9y v\xE0 kho\u1EA3ng tr\u1EAFng
  // Both data are available, render the content

  var renderSummary = function renderSummary() {
    return /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
      children: [/*#__PURE__*/(0,jsx_runtime.jsxs)(table/* default */.Z.Summary.Row, {
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)(table/* default */.Z.Summary.Cell, {
          index: 1,
          colSpan: 6,
          align: "right",
          children: /*#__PURE__*/(0,jsx_runtime.jsx)("b", {
            children: "C\\u1ED9ng ti\\u1EC1n h\\xE0ng:"
          })
        }), /*#__PURE__*/(0,jsx_runtime.jsx)(table/* default */.Z.Summary.Cell, {
          index: 2,
          colSpan: 6,
          align: "left",
          children: /*#__PURE__*/(0,jsx_runtime.jsx)("b", {
            children: (0,utils/* formatMoneyNumeral */.yp)(deliveryData.voucher_amount)
          })
        })]
      }), /*#__PURE__*/(0,jsx_runtime.jsxs)(table/* default */.Z.Summary.Row, {
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)(table/* default */.Z.Summary.Cell, {
          index: 1,
          colSpan: 6,
          align: "right",
          children: /*#__PURE__*/(0,jsx_runtime.jsx)("b", {
            children: "Chi\\u1EBFt kh\\u1EA5u:"
          })
        }), /*#__PURE__*/(0,jsx_runtime.jsx)(table/* default */.Z.Summary.Cell, {
          index: 2,
          colSpan: 6,
          align: "left",
          children: /*#__PURE__*/(0,jsx_runtime.jsx)("b", {
            children: (0,utils/* formatMoneyNumeral */.yp)(deliveryData.discount)
          })
        })]
      }), /*#__PURE__*/(0,jsx_runtime.jsxs)(table/* default */.Z.Summary.Row, {
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)(table/* default */.Z.Summary.Cell, {
          index: 1,
          colSpan: 6,
          align: "right",
          children: /*#__PURE__*/(0,jsx_runtime.jsx)("b", {
            children: "Thu\\u1EBF: "
          })
        }), /*#__PURE__*/(0,jsx_runtime.jsx)(table/* default */.Z.Summary.Cell, {
          index: 2,
          colSpan: 6,
          align: "left",
          children: /*#__PURE__*/(0,jsx_runtime.jsx)("b", {
            children: (0,utils/* formatMoneyNumeral */.yp)(deliveryData.add_taxes)
          })
        })]
      }), /*#__PURE__*/(0,jsx_runtime.jsxs)(table/* default */.Z.Summary.Row, {
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)(table/* default */.Z.Summary.Cell, {
          index: 1,
          colSpan: 6,
          align: "right",
          children: /*#__PURE__*/(0,jsx_runtime.jsx)("b", {
            children: "Chi ph\\xED kh\\xE1c: "
          })
        }), /*#__PURE__*/(0,jsx_runtime.jsx)(table/* default */.Z.Summary.Cell, {
          index: 2,
          colSpan: 6,
          align: "left",
          children: /*#__PURE__*/(0,jsx_runtime.jsxs)("b", {
            children: [" ", (0,utils/* formatMoneyNumeral */.yp)(deliveryData.other_charges)]
          })
        })]
      }), /*#__PURE__*/(0,jsx_runtime.jsxs)(table/* default */.Z.Summary.Row, {
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)(table/* default */.Z.Summary.Cell, {
          index: 1,
          colSpan: 6,
          align: "right",
          children: /*#__PURE__*/(0,jsx_runtime.jsx)("b", {
            children: "T\\u1ED5ng ti\\u1EC1n thanh to\\xE1n: "
          })
        }), /*#__PURE__*/(0,jsx_runtime.jsx)(table/* default */.Z.Summary.Cell, {
          index: 2,
          colSpan: 6,
          align: "left",
          children: /*#__PURE__*/(0,jsx_runtime.jsx)("b", {
            children: (0,utils/* formatMoneyNumeral */.yp)(deliveryData.total_amount)
          })
        })]
      }), /*#__PURE__*/(0,jsx_runtime.jsxs)(table/* default */.Z.Summary.Row, {
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)(table/* default */.Z.Summary.Cell, {
          index: 1,
          colSpan: 6,
          align: "right",
          children: /*#__PURE__*/(0,jsx_runtime.jsx)("b", {
            children: "\\u0110\\xE3 tr\\u1EA3: "
          })
        }), /*#__PURE__*/(0,jsx_runtime.jsx)(table/* default */.Z.Summary.Cell, {
          index: 2,
          colSpan: 6,
          align: "left",
          children: /*#__PURE__*/(0,jsx_runtime.jsx)("b", {
            children: (0,utils/* formatMoneyNumeral */.yp)(deliveryData.paid_amount)
          })
        })]
      }), /*#__PURE__*/(0,jsx_runtime.jsxs)(table/* default */.Z.Summary.Row, {
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)(table/* default */.Z.Summary.Cell, {
          index: 1,
          colSpan: 6,
          align: "right",
          children: /*#__PURE__*/(0,jsx_runtime.jsx)("b", {
            children: "C\\xF2n l\\u1EA1i: "
          })
        }), /*#__PURE__*/(0,jsx_runtime.jsx)(table/* default */.Z.Summary.Cell, {
          index: 2,
          colSpan: 6,
          align: "left",
          children: /*#__PURE__*/(0,jsx_runtime.jsx)("b", {
            children: (0,utils/* formatMoneyNumeral */.yp)(deliveryData.total_amount - deliveryData.paid_amount)
          })
        })]
      })]
    });
  };
  return /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
    className: styles.pageContent,
    children: [/*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
      children: [/*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
        justify: "space-between",
        style: {
          width: '100%'
        },
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
          flex: "auto",
          children: /*#__PURE__*/(0,jsx_runtime.jsxs)(space/* default */.Z, {
            align: "start",
            direction: "vertical",
            children: [/*#__PURE__*/(0,jsx_runtime.jsx)(row/* default */.Z, {
              children: /*#__PURE__*/(0,jsx_runtime.jsx)("span", {
                className: styles.nameText,
                children: profileData.customer_name
              })
            }), /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
              children: ["\\u0110\\u1ECAA CH\\u1EC8: ", fullAddress || 'N/A']
            }), /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
              children: ["S\\u0110T: ", profileData.customer_phone]
            })]
          })
        }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
          flex: "none",
          style: {
            marginLeft: 'auto',
            paddingRight: '20px'
          },
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(space/* default */.Z, {
            align: "center",
            direction: "vertical",
            size: 'small',
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(ImagePreview/* default */.Z, {
              imageUrls: [profileData.customer_logo]
            })
          })
        })]
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(row/* default */.Z, {
        justify: "center",
        style: {
          width: '100%',
          marginTop: '20px'
        },
        children: /*#__PURE__*/(0,jsx_runtime.jsxs)(space/* default */.Z, {
          align: "center",
          direction: "vertical",
          children: [/*#__PURE__*/(0,jsx_runtime.jsx)(row/* default */.Z, {
            children: /*#__PURE__*/(0,jsx_runtime.jsx)("b", {
              children: "PHI\\u1EBEU XU\\u1EA4T KHO KI\\xCAM PHI\\u1EBEU GIAO H\\xC0NG "
            })
          }), /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
            children: ["NG\\xC0Y: ", (0,utils/* formatDate */.p6)(deliveryData.posting_date)]
          }), /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
            children: ["M\\xC3 CH\\u1EE8NG T\\u1EEA: ", deliveryData.name]
          }), /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
            children: ["T\\xCAN KHO XU\\u1EA4T: ", deliveryData === null || deliveryData === void 0 || (_deliveryData$items = deliveryData.items) === null || _deliveryData$items === void 0 || (_deliveryData$items = _deliveryData$items.at(0)) === null || _deliveryData$items === void 0 ? void 0 : _deliveryData$items.warehouse_label]
          })]
        })
      })]
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(divider/* default */.Z, {}), /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
      justify: "space-between",
      align: "middle",
      children: [/*#__PURE__*/(0,jsx_runtime.jsxs)(col/* default */.Z, {
        span: 11,
        offset: 1,
        children: [deliveryData.customer_label && /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
          children: ["Kh\\xE1ch h\\xE0ng: ", deliveryData.customer_label]
        }), [deliveryData.customer_address, deliveryData.customer_ward, deliveryData.customer_district, deliveryData.customer_province].some(function (val) {
          return val;
        }) && /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
          children: ["\\u0110\\u1ECBa ch\\u1EC9:", ' ', [deliveryData.customer_address, deliveryData.customer_ward, deliveryData.customer_district, deliveryData.customer_province].filter(function (val) {
            return val;
          }).join(', ')]
        }), deliveryData.customer_phone && /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
          children: ["S\\u1ED1 \\u0111i\\u1EC7n tho\\u1EA1i: ", deliveryData.customer_phone]
        })]
      }), /*#__PURE__*/(0,jsx_runtime.jsxs)(col/* default */.Z, {
        span: 11,
        offset: 1,
        children: [((deliveryData === null || deliveryData === void 0 ? void 0 : deliveryData.user_first_name) || (deliveryData === null || deliveryData === void 0 ? void 0 : deliveryData.user_last_name)) && /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
          children: ["Ng\\u01B0\\u1EDDi t\\u1EA1o phi\\u1EBFu:", ' ', "".concat((deliveryData === null || deliveryData === void 0 ? void 0 : deliveryData.user_first_name) || '', " ").concat((deliveryData === null || deliveryData === void 0 ? void 0 : deliveryData.user_last_name) || '')]
        }), [deliveryData.user_address, deliveryData.user_ward, deliveryData.user_district, deliveryData.user_province].some(function (val) {
          return val;
        }) && /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
          children: ["\\u0110\\u1ECBa ch\\u1EC9:", ' ', [deliveryData.user_address, deliveryData.user_ward, deliveryData.user_district, deliveryData.user_province].filter(function (value) {
            return value;
          }).join(', ')]
        })]
      })]
    }), /*#__PURE__*/(0,jsx_runtime.jsx)("br", {}), /*#__PURE__*/(0,jsx_runtime.jsx)(table/* default */.Z, {
      columns: columns,
      size: "small",
      dataSource: (deliveryData === null || deliveryData === void 0 ? void 0 : deliveryData.items) || [],
      rowKey: 'name',
      pagination: false,
      style: {
        width: '100%'
      },
      bordered: true,
      summary: function summary() {
        return renderSummary();
      }
    }), /*#__PURE__*/(0,jsx_runtime.jsx)("br", {}), /*#__PURE__*/(0,jsx_runtime.jsx)(row/* default */.Z, {
      justify: "space-between",
      align: "middle",
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 11,
        offset: 1,
        children: /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
          children: ["M\\xF4 t\\u1EA3: ", deliveryData.description]
        })
      })
    }), /*#__PURE__*/(0,jsx_runtime.jsx)("br", {}), /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
      justify: "space-between",
      align: "middle",
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 11,
        offset: 1,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(row/* default */.Z, {
          children: "NG\\u01AF\\u1EDCI GIAO H\\xC0NG"
        })
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 11,
        offset: 1,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(row/* default */.Z, {
          children: "NG\\u01AF\\u1EDCI NH\\u1EACN H\\xC0NG"
        })
      })]
    })]
  });
};
/* harmony default export */ var components_ExportReceiptPrint = (ExportReceiptPrint);
;// CONCATENATED MODULE: ./src/pages/WarehouseManagementV3/Inventory/ExportPage/components/ExportReceiptPrintQtyOnly.tsx










var ExportReceiptPrintQtyOnly_useStyles = (0,antd_use_styles/* createStyles */.k)(function (_ref) {
  var token = _ref.token;
  return {
    pageContent: {
      fontFamily: 'Times New Roman, serif',
      '& *': {
        fontFamily: 'Times New Roman, serif !important'
      },
      '& .ant-table': {
        fontFamily: 'Times New Roman, serif !important'
      },
      '& .ant-table th': {
        fontFamily: 'Times New Roman, serif !important'
      },
      '& .ant-table td': {
        fontFamily: 'Times New Roman, serif !important'
      },
      '@media print': {
        '& img': {
          display: 'block',
          visibility: 'visible',
          printColorAdjust: 'exact',
          WebkitPrintColorAdjust: 'exact'
        },
        '& .ant-image': {
          display: 'block',
          visibility: 'visible'
        },
        '& .ant-image-img': {
          display: 'block',
          visibility: 'visible'
        }
      }
    },
    nameText: {
      textTransform: 'uppercase',
      fontWeight: 'bold'
    },
    slideFooter: {
      color: 'white'
    },
    textLarge: {
      fontSize: token.fontSizeHeading1 + 10,
      fontWeight: 700
    },
    textMedium: {
      fontSize: token.fontSizeHeading3,
      fontWeight: token.fontWeightStrong
    },
    text: {
      fontSize: token.fontSizeHeading4,
      fontWeight: token.fontWeightStrong,
      lineHeight: 1.5
    },
    textUppercase: {
      textTransform: 'uppercase'
    }
  };
});
var ExportReceiptPrintQtyOnly = function ExportReceiptPrintQtyOnly(_ref2) {
  var _deliveryData$items;
  var id = _ref2.id;
  var _useModel = (0,_umi_production_exports.useModel)('@@initialState'),
    initialState = _useModel.initialState;
  var styles = ExportReceiptPrintQtyOnly_useStyles();
  var _useRequest = (0,_umi_production_exports.useRequest)(deliveryNote/* getDeliveryNoteDetail */.r7, {
      manual: false,
      defaultParams: [{
        name: id
      }],
      onSuccess: function onSuccess(data) {
        console.log('Delivery Note Detail:', data);
        setTimeout(function () {
          window.print();
        }, 1000);
      }
    }),
    deliveryData = _useRequest.data,
    deliveryLoading = _useRequest.loading;
  var _useRequest2 = (0,_umi_production_exports.useRequest)(accounts/* getAccountProfile */._v, {
      manual: false,
      onSuccess: function onSuccess(data) {
        console.log('Account Profile:', data);
      }
    }),
    profile = _useRequest2.data,
    profileLoading = _useRequest2.loading;
  var columns = [{
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.index"
    }),
    dataIndex: 'index',
    render: function render(value, record, index) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        children: index + 1
      });
    },
    width: 15
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "warehouse-management.import-history.item_id"
    }),
    dataIndex: 'item_name',
    width: 10
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "warehouse-management.import-history.item_label"
    }),
    dataIndex: 'item_label',
    width: 20
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.unit"
    }),
    dataIndex: 'uom_name',
    width: 20
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.qty"
    }),
    dataIndex: 'qty',
    width: 20,
    render: function render(value, record, index) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        children: (0,utils/* formatNumeral */.GW)(record.qty)
      });
    }
  }
  // {
  //   title: <FormattedMessage id="warehouse-management.import-history.rate" />,
  //   dataIndex: 'rate',
  //   width: 20,
  //   render(value, record, index) {
  //     return <div>{formatMoneyNumeral(record.rate)}</div>;
  //   },
  // },
  // {
  //   title: <FormattedMessage id="warehouse-management.import-history.amount" />,
  //   dataIndex: 'amount',
  //   width: 20,
  //   render(value, record, index) {
  //     return <div>{formatMoneyNumeral(record.amount)}</div>;
  //   },
  // },
  ];

  // Show loading state if either request is loading
  if (deliveryLoading || profileLoading) {
    return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
      children: "Loading..."
    });
  }

  // Both requests completed but no data - check after loading is complete
  if (!deliveryData) {
    return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
      children: "No delivery note data available"
    });
  }

  // We'll render even if profile is missing, just show placeholder
  var profileData = profile || {
    full_name: 'N/A',
    address: 'N/A',
    phone_number: 'N/A'
  };
  // T\u1EA1o chu\u1ED7i \u0111\u1ECBa ch\u1EC9 t\u1EEB c\xE1c tr\u01B0\u1EDDng c\xF3 gi\xE1 tr\u1ECB
  var fullAddress = [profile === null || profile === void 0 ? void 0 : profile.customer_address, profile === null || profile === void 0 ? void 0 : profile.customer_ward, profile === null || profile === void 0 ? void 0 : profile.customer_district, profile === null || profile === void 0 ? void 0 : profile.customer_province].filter(function (val) {
    return val;
  }) // Ch\u1EC9 l\u1EA5y nh\u1EEFng gi\xE1 tr\u1ECB t\u1ED3n t\u1EA1i (kh\xE1c null/undefined)
  .join(', '); // N\u1ED1i b\u1EB1ng d\u1EA5u ph\u1EA9y v\xE0 kho\u1EA3ng tr\u1EAFng
  // Both data are available, render the content

  var renderSummary = function renderSummary() {
    return /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
      children: [/*#__PURE__*/(0,jsx_runtime.jsxs)(table/* default */.Z.Summary.Row, {
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)(table/* default */.Z.Summary.Cell, {
          index: 1,
          colSpan: 6,
          align: "right",
          children: /*#__PURE__*/(0,jsx_runtime.jsx)("b", {
            children: "Ti\\u1EC1n h\\xE0ng:"
          })
        }), /*#__PURE__*/(0,jsx_runtime.jsx)(table/* default */.Z.Summary.Cell, {
          index: 2,
          colSpan: 6,
          align: "left",
          children: /*#__PURE__*/(0,jsx_runtime.jsx)("b", {
            children: (0,utils/* formatMoneyNumeral */.yp)(deliveryData.voucher_amount)
          })
        })]
      }), /*#__PURE__*/(0,jsx_runtime.jsxs)(table/* default */.Z.Summary.Row, {
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)(table/* default */.Z.Summary.Cell, {
          index: 1,
          colSpan: 6,
          align: "right",
          children: /*#__PURE__*/(0,jsx_runtime.jsx)("b", {
            children: "Chi\\u1EBFt kh\\u1EA5u:"
          })
        }), /*#__PURE__*/(0,jsx_runtime.jsx)(table/* default */.Z.Summary.Cell, {
          index: 2,
          colSpan: 6,
          align: "left",
          children: /*#__PURE__*/(0,jsx_runtime.jsx)("b", {
            children: (0,utils/* formatMoneyNumeral */.yp)(deliveryData.discount)
          })
        })]
      }), /*#__PURE__*/(0,jsx_runtime.jsxs)(table/* default */.Z.Summary.Row, {
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)(table/* default */.Z.Summary.Cell, {
          index: 1,
          colSpan: 6,
          align: "right",
          children: /*#__PURE__*/(0,jsx_runtime.jsx)("b", {
            children: "Thu\\u1EBF: "
          })
        }), /*#__PURE__*/(0,jsx_runtime.jsx)(table/* default */.Z.Summary.Cell, {
          index: 2,
          colSpan: 6,
          align: "left",
          children: /*#__PURE__*/(0,jsx_runtime.jsx)("b", {
            children: (0,utils/* formatMoneyNumeral */.yp)(deliveryData.add_taxes)
          })
        })]
      }), /*#__PURE__*/(0,jsx_runtime.jsxs)(table/* default */.Z.Summary.Row, {
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)(table/* default */.Z.Summary.Cell, {
          index: 1,
          colSpan: 6,
          align: "right",
          children: /*#__PURE__*/(0,jsx_runtime.jsx)("b", {
            children: "Chi ph\\xED kh\\xE1c: "
          })
        }), /*#__PURE__*/(0,jsx_runtime.jsx)(table/* default */.Z.Summary.Cell, {
          index: 2,
          colSpan: 6,
          align: "left",
          children: /*#__PURE__*/(0,jsx_runtime.jsxs)("b", {
            children: [" ", (0,utils/* formatMoneyNumeral */.yp)(deliveryData.other_charges)]
          })
        })]
      }), /*#__PURE__*/(0,jsx_runtime.jsxs)(table/* default */.Z.Summary.Row, {
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)(table/* default */.Z.Summary.Cell, {
          index: 1,
          colSpan: 6,
          align: "right",
          children: /*#__PURE__*/(0,jsx_runtime.jsx)("b", {
            children: "T\\u1ED5ng ti\\u1EC1n h\\xE0ng: "
          })
        }), /*#__PURE__*/(0,jsx_runtime.jsx)(table/* default */.Z.Summary.Cell, {
          index: 2,
          colSpan: 6,
          align: "left",
          children: /*#__PURE__*/(0,jsx_runtime.jsx)("b", {
            children: (0,utils/* formatMoneyNumeral */.yp)(deliveryData.total_amount)
          })
        })]
      }), /*#__PURE__*/(0,jsx_runtime.jsxs)(table/* default */.Z.Summary.Row, {
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)(table/* default */.Z.Summary.Cell, {
          index: 1,
          colSpan: 6,
          align: "right",
          children: /*#__PURE__*/(0,jsx_runtime.jsx)("b", {
            children: "\\u0110\\xE3 tr\\u1EA3: "
          })
        }), /*#__PURE__*/(0,jsx_runtime.jsx)(table/* default */.Z.Summary.Cell, {
          index: 2,
          colSpan: 6,
          align: "left",
          children: /*#__PURE__*/(0,jsx_runtime.jsx)("b", {
            children: (0,utils/* formatMoneyNumeral */.yp)(deliveryData.paid_amount)
          })
        })]
      })]
    });
  };
  return /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
    className: styles.pageContent,
    children: [/*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
      children: [/*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
        justify: "space-between",
        style: {
          width: '100%'
        },
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
          flex: "auto",
          children: /*#__PURE__*/(0,jsx_runtime.jsxs)(space/* default */.Z, {
            align: "start",
            direction: "vertical",
            children: [/*#__PURE__*/(0,jsx_runtime.jsx)(row/* default */.Z, {
              children: /*#__PURE__*/(0,jsx_runtime.jsx)("span", {
                className: styles.nameText,
                children: profileData.customer_name
              })
            }), /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
              children: ["\\u0110\\u1ECAA CH\\u1EC8: ", fullAddress || 'N/A']
            }), /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
              children: ["S\\u0110T: ", profileData.customer_phone]
            })]
          })
        }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
          flex: "none",
          style: {
            marginLeft: 'auto',
            paddingRight: '20px'
          },
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(space/* default */.Z, {
            align: "center",
            direction: "vertical",
            size: 'small',
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(ImagePreview/* default */.Z, {
              imageUrls: [profileData.customer_logo]
            })
          })
        })]
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(row/* default */.Z, {
        justify: "center",
        style: {
          width: '100%',
          marginTop: '20px'
        },
        children: /*#__PURE__*/(0,jsx_runtime.jsxs)(space/* default */.Z, {
          align: "center",
          direction: "vertical",
          children: [/*#__PURE__*/(0,jsx_runtime.jsx)(row/* default */.Z, {
            children: /*#__PURE__*/(0,jsx_runtime.jsx)("b", {
              children: "PHI\\u1EBEU XU\\u1EA4T KHO KI\\xCAM PHI\\u1EBEU GIAO H\\xC0NG "
            })
          }), /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
            children: ["NG\\xC0Y: ", (0,utils/* formatDate */.p6)(deliveryData.posting_date)]
          }), /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
            children: ["M\\xC3 CH\\u1EE8NG T\\u1EEA: ", deliveryData.name]
          }), /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
            children: ["T\\xCAN KHO XU\\u1EA4T: ", deliveryData === null || deliveryData === void 0 || (_deliveryData$items = deliveryData.items) === null || _deliveryData$items === void 0 || (_deliveryData$items = _deliveryData$items.at(0)) === null || _deliveryData$items === void 0 ? void 0 : _deliveryData$items.warehouse_label]
          })]
        })
      })]
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(divider/* default */.Z, {}), /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
      justify: "space-between",
      align: "middle",
      children: [/*#__PURE__*/(0,jsx_runtime.jsxs)(col/* default */.Z, {
        span: 11,
        offset: 1,
        children: [deliveryData.customer_label && /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
          children: ["Kh\\xE1ch h\\xE0ng: ", deliveryData.customer_label]
        }), [deliveryData.customer_address, deliveryData.customer_ward, deliveryData.customer_district, deliveryData.customer_province].some(function (val) {
          return val;
        }) && /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
          children: ["\\u0110\\u1ECBa ch\\u1EC9:", ' ', [deliveryData.customer_address, deliveryData.customer_ward, deliveryData.customer_district, deliveryData.customer_province].filter(function (val) {
            return val;
          }).join(', ')]
        }), deliveryData.customer_phone && /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
          children: ["S\\u1ED1 \\u0111i\\u1EC7n tho\\u1EA1i: ", deliveryData.customer_phone]
        })]
      }), /*#__PURE__*/(0,jsx_runtime.jsxs)(col/* default */.Z, {
        span: 11,
        offset: 1,
        children: [((deliveryData === null || deliveryData === void 0 ? void 0 : deliveryData.user_first_name) || (deliveryData === null || deliveryData === void 0 ? void 0 : deliveryData.user_last_name)) && /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
          children: ["Ng\\u01B0\\u1EDDi t\\u1EA1o phi\\u1EBFu:", ' ', "".concat((deliveryData === null || deliveryData === void 0 ? void 0 : deliveryData.user_first_name) || '', " ").concat((deliveryData === null || deliveryData === void 0 ? void 0 : deliveryData.user_last_name) || '')]
        }), [deliveryData.user_address, deliveryData.user_ward, deliveryData.user_district, deliveryData.user_province].some(function (val) {
          return val;
        }) && /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
          children: ["\\u0110\\u1ECBa ch\\u1EC9:", ' ', [deliveryData.user_address, deliveryData.user_ward, deliveryData.user_district, deliveryData.user_province].filter(function (value) {
            return value;
          }).join(', ')]
        })]
      })]
    }), /*#__PURE__*/(0,jsx_runtime.jsx)("br", {}), /*#__PURE__*/(0,jsx_runtime.jsx)(table/* default */.Z, {
      columns: columns,
      size: "small",
      dataSource: (deliveryData === null || deliveryData === void 0 ? void 0 : deliveryData.items) || [],
      rowKey: 'name',
      pagination: false,
      style: {
        width: '100%'
      },
      bordered: true
      // summary={() => renderSummary()}
    }), /*#__PURE__*/(0,jsx_runtime.jsx)("br", {}), /*#__PURE__*/(0,jsx_runtime.jsx)(row/* default */.Z, {
      justify: "space-between",
      align: "middle",
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 11,
        offset: 1,
        children: /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
          children: ["M\\xF4 t\\u1EA3: ", deliveryData.description]
        })
      })
    }), /*#__PURE__*/(0,jsx_runtime.jsx)("br", {}), /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
      justify: "space-between",
      align: "middle",
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 11,
        offset: 1,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(row/* default */.Z, {
          children: "NG\\u01AF\\u1EDCI GIAO H\\xC0NG"
        })
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 11,
        offset: 1,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(row/* default */.Z, {
          children: "NG\\u01AF\\u1EDCI NH\\u1EACN H\\xC0NG"
        })
      })]
    })]
  });
};
/* harmony default export */ var components_ExportReceiptPrintQtyOnly = (ExportReceiptPrintQtyOnly);
// EXTERNAL MODULE: ./src/pages/WarehouseManagementV3/Inventory/ExportPage/components/GeneralExportPrint.tsx
var GeneralExportPrint = __webpack_require__(61592);
// EXTERNAL MODULE: ./src/pages/WarehouseManagementV3/Inventory/ExportPage/components/GeneralImportPrint.tsx
var GeneralImportPrint = __webpack_require__(99927);
// EXTERNAL MODULE: ./src/pages/WarehouseManagementV3/Inventory/ExportPage/components/ImportExportPrint.tsx
var ImportExportPrint = __webpack_require__(90864);
// EXTERNAL MODULE: ./src/pages/WarehouseManagementV3/Inventory/ExportPage/components/ImportExportValuePrint.tsx
var ImportExportValuePrint = __webpack_require__(71507);
// EXTERNAL MODULE: ./src/services/stock/purchaseReceipt.ts
var purchaseReceipt = __webpack_require__(33326);
;// CONCATENATED MODULE: ./src/pages/WarehouseManagementV3/Inventory/ExportPage/components/ImportReceiptPrint.tsx










var ImportReceiptPrint_useStyles = (0,antd_use_styles/* createStyles */.k)(function (_ref) {
  var token = _ref.token;
  return {
    pageContent: {
      fontFamily: 'Times New Roman, serif',
      '& *': {
        fontFamily: 'Times New Roman, serif !important'
      },
      '& .ant-table': {
        fontFamily: 'Times New Roman, serif !important'
      },
      '& .ant-table th': {
        fontFamily: 'Times New Roman, serif !important'
      },
      '& .ant-table td': {
        fontFamily: 'Times New Roman, serif !important'
      },
      '@media print': {
        '& img': {
          display: 'block',
          visibility: 'visible',
          printColorAdjust: 'exact',
          WebkitPrintColorAdjust: 'exact'
        },
        '& .ant-image': {
          display: 'block',
          visibility: 'visible'
        },
        '& .ant-image-img': {
          display: 'block',
          visibility: 'visible'
        }
      }
    },
    nameText: {
      textTransform: 'uppercase',
      fontWeight: 'bold'
    },
    slideFooter: {
      color: 'white'
    },
    textLarge: {
      fontSize: token.fontSizeHeading1 + 10,
      fontWeight: 700
    },
    textMedium: {
      fontSize: token.fontSizeHeading3,
      fontWeight: token.fontWeightStrong
    },
    text: {
      fontSize: token.fontSizeHeading4,
      fontWeight: token.fontWeightStrong,
      lineHeight: 1.5
    },
    textUppercase: {
      textTransform: 'uppercase'
    }
  };
});
var ImportReceiptPrint = function ImportReceiptPrint(_ref2) {
  var _importData$items;
  var id = _ref2.id;
  var _useModel = (0,_umi_production_exports.useModel)('@@initialState'),
    initialState = _useModel.initialState;
  var styles = ImportReceiptPrint_useStyles();
  var _useRequest = (0,_umi_production_exports.useRequest)(purchaseReceipt/* getImportReceiptDetail */.af, {
      manual: false,
      defaultParams: [{
        name: id
      }],
      onSuccess: function onSuccess(data) {
        console.log('Import Receipt Detail:', data);
        setTimeout(function () {
          window.print();
        }, 1000);
      }
    }),
    importData = _useRequest.data,
    importLoading = _useRequest.loading;
  var _useRequest2 = (0,_umi_production_exports.useRequest)(accounts/* getAccountProfile */._v, {
      manual: false,
      onSuccess: function onSuccess(data) {
        console.log('Account Profile:', data);
      }
    }),
    profile = _useRequest2.data,
    profileLoading = _useRequest2.loading;
  var columns = [{
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.index"
    }),
    dataIndex: 'index',
    render: function render(value, record, index) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        children: index + 1
      });
    },
    width: 15
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "warehouse-management.import-history.item_id"
    }),
    dataIndex: 'item_name',
    width: 10
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "warehouse-management.import-history.item_label"
    }),
    dataIndex: 'item_label',
    width: 20
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.unit"
    }),
    dataIndex: 'uom_name',
    width: 20
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.qty"
    }),
    dataIndex: 'received_qty',
    width: 20,
    render: function render(value, record, index) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        children: (0,utils/* formatNumeral */.GW)(record.received_qty)
      });
    }
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "warehouse-management.import-history.rate"
    }),
    dataIndex: 'rate',
    width: 20,
    render: function render(value, record, index) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        children: (0,utils/* formatMoneyNumeral */.yp)(record.rate)
      });
    }
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "warehouse-management.import-history.amount"
    }),
    dataIndex: 'amount',
    width: 20,
    render: function render(value, record, index) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        children: (0,utils/* formatMoneyNumeral */.yp)(record.amount)
      });
    }
  }];
  if (importLoading || profileLoading) {
    return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
      children: "Loading..."
    });
  }
  if (!importData) {
    return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
      children: "No import note data available"
    });
  }
  var profileData = profile || {
    full_name: 'N/A',
    address: 'N/A',
    phone_number: 'N/A'
  };
  var fullAddress = [profile === null || profile === void 0 ? void 0 : profile.customer_address, profile === null || profile === void 0 ? void 0 : profile.customer_ward, profile === null || profile === void 0 ? void 0 : profile.customer_district, profile === null || profile === void 0 ? void 0 : profile.customer_province].filter(function (val) {
    return val;
  }).join(', ');
  var renderSummary = function renderSummary() {
    return /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
      children: [/*#__PURE__*/(0,jsx_runtime.jsxs)(table/* default */.Z.Summary.Row, {
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)(table/* default */.Z.Summary.Cell, {
          index: 1,
          colSpan: 6,
          align: "right",
          children: /*#__PURE__*/(0,jsx_runtime.jsx)("b", {
            children: "Ti\\u1EC1n h\\xE0ng:"
          })
        }), /*#__PURE__*/(0,jsx_runtime.jsx)(table/* default */.Z.Summary.Cell, {
          index: 2,
          colSpan: 6,
          align: "left",
          children: /*#__PURE__*/(0,jsx_runtime.jsx)("b", {
            children: (0,utils/* formatMoneyNumeral */.yp)(importData.voucher_amount)
          })
        })]
      }), /*#__PURE__*/(0,jsx_runtime.jsxs)(table/* default */.Z.Summary.Row, {
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)(table/* default */.Z.Summary.Cell, {
          index: 1,
          colSpan: 6,
          align: "right",
          children: /*#__PURE__*/(0,jsx_runtime.jsx)("b", {
            children: "Chi\\u1EBFt kh\\u1EA5u:"
          })
        }), /*#__PURE__*/(0,jsx_runtime.jsx)(table/* default */.Z.Summary.Cell, {
          index: 2,
          colSpan: 6,
          align: "left",
          children: /*#__PURE__*/(0,jsx_runtime.jsx)("b", {
            children: (0,utils/* formatMoneyNumeral */.yp)(importData.discount)
          })
        })]
      }), /*#__PURE__*/(0,jsx_runtime.jsxs)(table/* default */.Z.Summary.Row, {
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)(table/* default */.Z.Summary.Cell, {
          index: 1,
          colSpan: 6,
          align: "right",
          children: /*#__PURE__*/(0,jsx_runtime.jsx)("b", {
            children: "Thu\\u1EBF: "
          })
        }), /*#__PURE__*/(0,jsx_runtime.jsx)(table/* default */.Z.Summary.Cell, {
          index: 2,
          colSpan: 6,
          align: "left",
          children: /*#__PURE__*/(0,jsx_runtime.jsx)("b", {
            children: (0,utils/* formatMoneyNumeral */.yp)(importData.add_taxes)
          })
        })]
      }), /*#__PURE__*/(0,jsx_runtime.jsxs)(table/* default */.Z.Summary.Row, {
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)(table/* default */.Z.Summary.Cell, {
          index: 1,
          colSpan: 6,
          align: "right",
          children: /*#__PURE__*/(0,jsx_runtime.jsx)("b", {
            children: "Chi ph\\xED kh\\xE1c: "
          })
        }), /*#__PURE__*/(0,jsx_runtime.jsx)(table/* default */.Z.Summary.Cell, {
          index: 2,
          colSpan: 6,
          align: "left",
          children: /*#__PURE__*/(0,jsx_runtime.jsxs)("b", {
            children: [" ", (0,utils/* formatMoneyNumeral */.yp)(importData.other_charges)]
          })
        })]
      }), /*#__PURE__*/(0,jsx_runtime.jsxs)(table/* default */.Z.Summary.Row, {
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)(table/* default */.Z.Summary.Cell, {
          index: 1,
          colSpan: 6,
          align: "right",
          children: /*#__PURE__*/(0,jsx_runtime.jsx)("b", {
            children: "T\\u1ED5ng ti\\u1EC1n h\\xE0ng: "
          })
        }), /*#__PURE__*/(0,jsx_runtime.jsx)(table/* default */.Z.Summary.Cell, {
          index: 2,
          colSpan: 6,
          align: "left",
          children: /*#__PURE__*/(0,jsx_runtime.jsx)("b", {
            children: (0,utils/* formatMoneyNumeral */.yp)(importData.total_amount)
          })
        })]
      }), /*#__PURE__*/(0,jsx_runtime.jsxs)(table/* default */.Z.Summary.Row, {
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)(table/* default */.Z.Summary.Cell, {
          index: 1,
          colSpan: 6,
          align: "right",
          children: /*#__PURE__*/(0,jsx_runtime.jsx)("b", {
            children: "\\u0110\\xE3 tr\\u1EA3: "
          })
        }), /*#__PURE__*/(0,jsx_runtime.jsx)(table/* default */.Z.Summary.Cell, {
          index: 2,
          colSpan: 6,
          align: "left",
          children: /*#__PURE__*/(0,jsx_runtime.jsx)("b", {
            children: (0,utils/* formatMoneyNumeral */.yp)(importData.paid_amount)
          })
        })]
      })]
    });
  };
  return /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
    className: styles.pageContent,
    children: [/*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
      children: [/*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
        justify: "space-between",
        style: {
          width: '100%'
        },
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
          flex: "auto",
          children: /*#__PURE__*/(0,jsx_runtime.jsxs)(space/* default */.Z, {
            align: "start",
            direction: "vertical",
            children: [/*#__PURE__*/(0,jsx_runtime.jsx)(row/* default */.Z, {
              children: /*#__PURE__*/(0,jsx_runtime.jsx)("span", {
                className: styles.nameText,
                children: profileData.customer_name
              })
            }), /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
              children: ["\\u0110\\u1ECAA CH\\u1EC8: ", fullAddress || 'N/A']
            }), /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
              children: ["S\\u0110T: ", profileData.customer_phone]
            })]
          })
        }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
          flex: "none",
          style: {
            marginLeft: 'auto',
            paddingRight: '20px'
          },
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(space/* default */.Z, {
            align: "center",
            direction: "vertical",
            size: 'small',
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(ImagePreview/* default */.Z, {
              imageUrls: [profileData.customer_logo]
            })
          })
        })]
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(row/* default */.Z, {
        justify: "center",
        style: {
          width: '100%',
          marginTop: '20px'
        },
        children: /*#__PURE__*/(0,jsx_runtime.jsxs)(space/* default */.Z, {
          align: "center",
          direction: "vertical",
          children: [/*#__PURE__*/(0,jsx_runtime.jsx)(row/* default */.Z, {
            children: /*#__PURE__*/(0,jsx_runtime.jsx)("b", {
              children: "PHI\\u1EBEU NH\\u1EACP KHO"
            })
          }), /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
            children: ["NG\\xC0Y: ", (0,utils/* formatDate */.p6)(importData.posting_date)]
          }), /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
            children: ["M\\xC3 CH\\u1EE8NG T\\u1EEA: ", importData.name]
          }), /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
            children: ["T\\xCAN KHO NH\\u1EACP: ", importData === null || importData === void 0 || (_importData$items = importData.items) === null || _importData$items === void 0 || (_importData$items = _importData$items.at(0)) === null || _importData$items === void 0 ? void 0 : _importData$items.warehouse_label]
          })]
        })
      })]
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(divider/* default */.Z, {}), /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
      justify: "space-between",
      align: "middle",
      children: [/*#__PURE__*/(0,jsx_runtime.jsxs)(col/* default */.Z, {
        span: 11,
        offset: 1,
        children: [importData.supplier_label && /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
          children: ["Nh\\xE0 cung c\\u1EA5p: ", importData.supplier_label]
        }), [importData.supplier_address, importData.supplier_ward, importData.supplier_district, importData.supplier_province].some(function (val) {
          return val;
        }) && /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
          children: ["\\u0110\\u1ECBa ch\\u1EC9:", ' ', [importData.supplier_address, importData.supplier_ward, importData.supplier_district, importData.supplier_province].filter(function (val) {
            return val;
          }).join(', ')]
        }), importData.supplier_phone && /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
          children: ["S\\u1ED1 \\u0111i\\u1EC7n tho\\u1EA1i: ", importData.supplier_phone]
        })]
      }), /*#__PURE__*/(0,jsx_runtime.jsxs)(col/* default */.Z, {
        span: 11,
        offset: 1,
        children: [((importData === null || importData === void 0 ? void 0 : importData.user_first_name) || (importData === null || importData === void 0 ? void 0 : importData.user_last_name)) && /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
          children: ["Ng\\u01B0\\u1EDDi t\\u1EA1o phi\\u1EBFu:", ' ', "".concat((importData === null || importData === void 0 ? void 0 : importData.user_first_name) || '', " ").concat((importData === null || importData === void 0 ? void 0 : importData.user_last_name) || '')]
        }), [importData.user_address, importData.user_ward, importData.user_district, importData.user_province].some(function (val) {
          return val;
        }) && /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
          children: ["\\u0110\\u1ECBa ch\\u1EC9:", ' ', [importData.user_address, importData.user_ward, importData.user_district, importData.user_province].filter(function (value) {
            return value;
          }).join(', ')]
        })]
      })]
    }), /*#__PURE__*/(0,jsx_runtime.jsx)("br", {}), /*#__PURE__*/(0,jsx_runtime.jsx)(table/* default */.Z, {
      columns: columns,
      size: "small",
      dataSource: (importData === null || importData === void 0 ? void 0 : importData.items) || [],
      rowKey: 'name',
      pagination: false,
      style: {
        width: '100%'
      },
      bordered: true,
      summary: function summary() {
        return renderSummary();
      }
    }), /*#__PURE__*/(0,jsx_runtime.jsx)("br", {}), /*#__PURE__*/(0,jsx_runtime.jsx)(row/* default */.Z, {
      justify: "space-between",
      align: "middle",
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 11,
        offset: 1,
        children: /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
          children: ["M\\xF4 t\\u1EA3: ", importData.description]
        })
      })
    }), /*#__PURE__*/(0,jsx_runtime.jsx)("br", {}), /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
      justify: "space-between",
      align: "middle",
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 11,
        offset: 1,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(row/* default */.Z, {
          children: "NG\\u01AF\\u1EDCI GIAO H\\xC0NG"
        })
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 11,
        offset: 1,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(row/* default */.Z, {
          children: "NG\\u01AF\\u1EDCI NH\\u1EACN H\\xC0NG"
        })
      })]
    })]
  });
};
/* harmony default export */ var components_ImportReceiptPrint = (ImportReceiptPrint);
// EXTERNAL MODULE: ./src/services/stock/stockEntry.ts
var stockEntry = __webpack_require__(1631);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
;// CONCATENATED MODULE: ./src/pages/WarehouseManagementV3/Inventory/ExportPage/components/MaterialIssuePrint.tsx









var MaterialIssuePrint_useStyles = (0,antd_use_styles/* createStyles */.k)(function (_ref) {
  var token = _ref.token;
  return {
    slideFooter: {
      color: 'white'
    },
    textLarge: {
      fontSize: token.fontSizeHeading1 + 10,
      fontWeight: 700
    },
    textMedium: {
      fontSize: token.fontSizeHeading3,
      fontWeight: token.fontWeightStrong
    },
    text: {
      fontSize: token.fontSizeHeading4,
      fontWeight: token.fontWeightStrong,
      lineHeight: 1.5
    },
    textUppercase: {
      textTransform: 'uppercase'
    }
  };
});
var StockEntryPrint = function StockEntryPrint(_ref2) {
  var id = _ref2.id,
    type = _ref2.type;
  var _useRequest = (0,_umi_production_exports.useRequest)(stockEntry/* getStockEntryNoteDetail */.T2, {
      manual: true,
      onError: function onError(error) {
        console.log('error', error.message);
      },
      onSuccess: function onSuccess(data, params) {
        setTimeout(function () {
          window.print();
        }, 1000);
      }
    }),
    data = _useRequest.data,
    loading = _useRequest.loading,
    refresh = _useRequest.refresh,
    run = _useRequest.run;
  (0,react.useEffect)(function () {
    run({
      name: id
    });
  }, [id, type]);
  var columns = [{
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.index"
    }),
    dataIndex: 'index',
    render: function render(dom, entity, index) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        children: index + 1
      });
    },
    width: 15
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "warehouse-management.stock-entry-history.item_id"
    }),
    dataIndex: 'item_name',
    width: 20
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "warehouse-management.stock-entry-history.item_label"
    }),
    dataIndex: 'item_label',
    width: 20
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.unit"
    }),
    dataIndex: 'uom_name',
    width: 20
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.qty"
    }),
    dataIndex: 'qty',
    width: 20,
    render: function render(dom, entity, index) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        children: (0,utils/* formatNumeral */.GW)(entity.qty)
      });
    }
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "warehouse-management.stock-entry-history.rate"
    }),
    dataIndex: 'rate',
    width: 20,
    render: function render(dom, entity, index) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        children: (0,utils/* formatNumeral */.GW)(entity.valuation_rate)
      });
    }
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "warehouse-management.stock-entry-history.amount"
    }),
    dataIndex: 'amount',
    width: 20,
    render: function render(dom, entity, index) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        children: (0,utils/* formatNumeral */.GW)(entity.amount)
      });
    }
  }];
  var renderHeader = function renderHeader() {
    switch (type) {
      case 'materialIssue':
        return 'PHI\u1EBEU XU\u1EA4T H\u1EE6Y';
      case 'materialReceipt':
        return 'PHI\u1EBEU NH\u1EACN H\xC0NG TR\u1EF0C TI\u1EBEP';
      case 'materialTransfer':
        return 'PHI\u1EBEU CHUY\u1EC2N KHO';
      default:
        return 'PHI\u1EBEU';
    }
  };
  var renderWarehouse = function renderWarehouse() {
    var _data$items, _data$items2;
    switch (type) {
      case 'materialIssue':
        return /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
          children: ["T\\xCAN KHO XU\\u1EA4T: ", data === null || data === void 0 || (_data$items = data.items) === null || _data$items === void 0 || (_data$items = _data$items.at(0)) === null || _data$items === void 0 ? void 0 : _data$items.s_warehouse_label]
        });
      case 'materialReceipt':
        return /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
          children: ["T\\xCAN KHO NH\\u1EACN: ", data === null || data === void 0 || (_data$items2 = data.items) === null || _data$items2 === void 0 || (_data$items2 = _data$items2.at(0)) === null || _data$items2 === void 0 ? void 0 : _data$items2.t_warehouse_label]
        });
      case 'materialTransfer':
        return /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
          justify: "space-between",
          align: "middle",
          children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
            span: 11,
            offset: 1,
            children: /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
              children: ["T\\xCAN KHO CHUY\\u1EC2N: ", data === null || data === void 0 ? void 0 : data.items[0].s_warehouse_label]
            })
          }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
            span: 11,
            offset: 1,
            children: /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
              children: ["T\\xCAN KHO NH\\u1EACN: ", data === null || data === void 0 ? void 0 : data.items[0].t_warehouse_label]
            })
          })]
        });
      default:
        return '';
    }
  };
  return /*#__PURE__*/(0,jsx_runtime.jsx)(jsx_runtime.Fragment, {
    children: data && /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)(row/* default */.Z, {
        justify: "space-between",
        align: "middle",
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
          span: 24,
          style: {
            display: 'flex',
            justifyContent: 'center'
          },
          children: /*#__PURE__*/(0,jsx_runtime.jsxs)(space/* default */.Z, {
            align: "center",
            direction: "vertical",
            children: [/*#__PURE__*/(0,jsx_runtime.jsx)(row/* default */.Z, {
              children: /*#__PURE__*/(0,jsx_runtime.jsx)("b", {
                children: renderHeader()
              })
            }), /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
              children: ["NG\\xC0Y: ", (0,utils/* formatDate */.p6)(data.posting_date)]
            }), /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
              children: ["M\\xC3 CH\\u1EE8NG T\\u1EEA: ", data.name]
            }), renderWarehouse()]
          })
        })
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(divider/* default */.Z, {}), /*#__PURE__*/(0,jsx_runtime.jsx)(row/* default */.Z, {
        justify: "space-between",
        align: "middle",
        children: /*#__PURE__*/(0,jsx_runtime.jsxs)(col/* default */.Z, {
          span: 11,
          offset: 1,
          children: [/*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
            children: ["Ng\\u01B0\\u1EDDi t\\u1EA1o phi\\u1EBFu: ", "".concat(data === null || data === void 0 ? void 0 : data.user_first_name, " ").concat(data === null || data === void 0 ? void 0 : data.user_last_name)]
          }), /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
            children: ["\\u0110\\u1ECBa ch\\u1EC9:", ' ', "".concat(data.user_address || '', ", ").concat(data.user_ward || '', ", ").concat(data.user_district || '', ", ").concat(data.user_province || '')]
          }), /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
            children: ["S\\u1ED1 \\u0111i\\u1EC7n tho\\u1EA1i: ", data.user_phone_number]
          })]
        })
      }), /*#__PURE__*/(0,jsx_runtime.jsx)("br", {}), /*#__PURE__*/(0,jsx_runtime.jsx)(table/* default */.Z, {
        columns: columns,
        size: "small",
        dataSource: (data === null || data === void 0 ? void 0 : data.items) || [],
        rowKey: 'name',
        pagination: false
      }), /*#__PURE__*/(0,jsx_runtime.jsx)("br", {}), /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
        justify: "space-between",
        align: "middle",
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
          span: 11,
          offset: 1,
          children: /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
            children: ["M\\xF4 t\\u1EA3: ", data.description]
          })
        }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
          span: 11,
          offset: 1,
          children: /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
            children: ["T\\u1ED5ng c\\u1ED9ng: ", (0,utils/* formatNumeral */.GW)(data.value_difference)]
          })
        })]
      }), /*#__PURE__*/(0,jsx_runtime.jsx)("br", {}), /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
        justify: "space-between",
        align: "middle",
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
          span: 11,
          offset: 1,
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(row/* default */.Z, {
            children: "TH\\u1EF0C HI\\u1EC6N"
          })
        }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
          span: 11,
          offset: 1,
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(row/* default */.Z, {
            children: "QU\\u1EA2N L\\xDD KHO"
          })
        })]
      })]
    })
  });
};
/* harmony default export */ var MaterialIssuePrint = (StockEntryPrint);
// EXTERNAL MODULE: ./src/services/stock/stockReconciliation.ts
var stockReconciliation = __webpack_require__(47161);
;// CONCATENATED MODULE: ./src/pages/WarehouseManagementV3/Inventory/ExportPage/components/ReconciliationReceiptPrint.tsx









var ReconciliationReceiptPrint_useStyles = (0,antd_use_styles/* createStyles */.k)(function (_ref) {
  var token = _ref.token;
  return {
    slideFooter: {
      color: 'white'
    },
    textLarge: {
      fontSize: token.fontSizeHeading1 + 10,
      fontWeight: 700
    },
    textMedium: {
      fontSize: token.fontSizeHeading3,
      fontWeight: token.fontWeightStrong
    },
    text: {
      fontSize: token.fontSizeHeading4,
      fontWeight: token.fontWeightStrong,
      lineHeight: 1.5
    },
    textUppercase: {
      textTransform: 'uppercase'
    }
  };
});
var ReconciliationReceiptPrint = function ReconciliationReceiptPrint(_ref2) {
  var _data$items;
  var id = _ref2.id;
  var _useRequest = (0,_umi_production_exports.useRequest)(stockReconciliation/* getStockReconciliationDetail */.qF, {
      manual: true,
      onError: function onError(error) {
        console.log('error', error.message);
      },
      onSuccess: function onSuccess(data, params) {
        setTimeout(function () {
          window.print();
        }, 1000);
      }
    }),
    data = _useRequest.data,
    loading = _useRequest.loading,
    refresh = _useRequest.refresh,
    run = _useRequest.run;
  (0,react.useEffect)(function () {
    run({
      name: id
    });
  }, [id]);
  var columns = [{
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.index"
    }),
    dataIndex: 'index',
    //   render(dom, entity, index, action, schema) {
    //     return <div>{index + 1}</div>;
    //   },
    render: function render(value, record, index) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        children: index + 1
      });
    },
    width: 15
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "warehouse-management.import-history.item_id"
    }),
    dataIndex: 'item_name',
    width: 10
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "warehouse-management.import-history.item_label"
    }),
    dataIndex: 'item_label',
    width: 20
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.old_quantity"
    }),
    dataIndex: 'current_qty',
    width: 20,
    render: function render(dom, entity, index) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        children: (0,utils/* formatNumeral */.GW)(entity.current_qty)
      });
    }
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.reconciled_quantity"
    }),
    dataIndex: 'qty',
    width: 20,
    render: function render(dom, entity, index) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        children: (0,utils/* formatNumeral */.GW)(entity.qty)
      });
    }
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.unit"
    }),
    dataIndex: 'uom_name',
    width: 20
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.old_rate"
    }),
    dataIndex: 'current_valuation_rate',
    width: 20,
    render: function render(dom, entity, index) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        children: (0,utils/* formatNumeral */.GW)(entity.current_valuation_rate)
      });
    }
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.reconciled_rate"
    }),
    dataIndex: 'valuation_rate',
    width: 20,
    render: function render(dom, entity, index) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        children: (0,utils/* formatNumeral */.GW)(entity.valuation_rate)
      });
    }
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.reconciled_amount"
    }),
    dataIndex: 'amount',
    width: 20,
    render: function render(value, record, index) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        children: (0,utils/* formatMoneyNumeral */.yp)(record.amount)
      });
    }
  }];
  return /*#__PURE__*/(0,jsx_runtime.jsx)(jsx_runtime.Fragment, {
    children: data && /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)(row/* default */.Z, {
        justify: "space-between",
        align: "middle",
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
          span: 24,
          style: {
            display: 'flex',
            justifyContent: 'center'
          },
          children: /*#__PURE__*/(0,jsx_runtime.jsxs)(space/* default */.Z, {
            align: "center",
            direction: "vertical",
            children: [/*#__PURE__*/(0,jsx_runtime.jsx)(row/* default */.Z, {
              children: /*#__PURE__*/(0,jsx_runtime.jsx)("b", {
                children: "PHI\\u1EBEU KI\\u1EC2M KHO "
              })
            }), /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
              children: ["NG\\xC0Y: ", (0,utils/* formatDate */.p6)(data.posting_date)]
            }), /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
              children: ["M\\xC3 CH\\u1EE8NG T\\u1EEA: ", data.name]
            }), /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
              children: ["T\\xCAN KHO: ", data === null || data === void 0 || (_data$items = data.items) === null || _data$items === void 0 || (_data$items = _data$items.at(0)) === null || _data$items === void 0 ? void 0 : _data$items.warehouse_label]
            })]
          })
        })
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(divider/* default */.Z, {}), /*#__PURE__*/(0,jsx_runtime.jsx)(row/* default */.Z, {
        justify: "space-between",
        align: "middle",
        children: /*#__PURE__*/(0,jsx_runtime.jsxs)(col/* default */.Z, {
          span: 11,
          offset: 1,
          children: [/*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
            children: ["Ng\\u01B0\\u1EDDi t\\u1EA1o phi\\u1EBFu: ", "".concat(data === null || data === void 0 ? void 0 : data.user_first_name, " ").concat(data === null || data === void 0 ? void 0 : data.user_last_name)]
          }), /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
            children: ["\\u0110\\u1ECBa ch\\u1EC9:", ' ', "".concat(data.user_address || '', ", ").concat(data.user_ward || '', ", ").concat(data.user_district || '', ", ").concat(data.user_province || '')]
          }), /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
            children: ["S\\u1ED1 \\u0111i\\u1EC7n tho\\u1EA1i: ", data.user_phone_number]
          })]
        })
      }), /*#__PURE__*/(0,jsx_runtime.jsx)("br", {}), /*#__PURE__*/(0,jsx_runtime.jsx)(table/* default */.Z, {
        columns: columns,
        size: "small",
        dataSource: (data === null || data === void 0 ? void 0 : data.items) || [],
        rowKey: 'name',
        pagination: false
      }), /*#__PURE__*/(0,jsx_runtime.jsx)("br", {}), /*#__PURE__*/(0,jsx_runtime.jsx)(row/* default */.Z, {
        justify: "space-between",
        align: "middle",
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
          span: 11,
          offset: 1,
          children: /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
            children: ["M\\xF4 t\\u1EA3: ", data.description]
          })
        })
      }), /*#__PURE__*/(0,jsx_runtime.jsx)("br", {}), /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
        justify: "space-between",
        align: "middle",
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
          span: 11,
          offset: 1,
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(row/* default */.Z, {
            children: "NG\\u01AF\\u1EDCI KI\\u1EC2M"
          })
        }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
          span: 11,
          offset: 1,
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(row/* default */.Z, {
            children: "KI\\u1EC2M TRA"
          })
        })]
      })]
    })
  });
};
/* harmony default export */ var components_ReconciliationReceiptPrint = (ReconciliationReceiptPrint);
;// CONCATENATED MODULE: ./src/pages/WarehouseManagementV3/Inventory/ExportPage/index.tsx














var selectType = function selectType(searchParams) {
  var _searchParams$get, _searchParams$get2;
  var id = searchParams.get('id');
  var type = searchParams.get('type');
  switch (type) {
    case 'import':
      if (!id) {
        _umi_production_exports.history.push('404');
        return;
      }
      return /*#__PURE__*/(0,jsx_runtime.jsx)(components_ImportReceiptPrint, {
        id: id
      });
    case 'export':
      if (!id) {
        _umi_production_exports.history.push('404');
        return;
      }
      return /*#__PURE__*/(0,jsx_runtime.jsx)(components_ExportReceiptPrint, {
        id: id
      });
    case 'exportQtyOnly':
      if (!id) {
        _umi_production_exports.history.push('404');
        return;
      }
      return /*#__PURE__*/(0,jsx_runtime.jsx)(components_ExportReceiptPrintQtyOnly, {
        id: id
      });
    case 'reconciliation':
      if (!id) {
        _umi_production_exports.history.push('404');
        return;
      }
      return /*#__PURE__*/(0,jsx_runtime.jsx)(components_ReconciliationReceiptPrint, {
        id: id
      });
    case 'materialIssue':
    case 'materialReceipt':
    case 'materialTransfer':
      if (!id) {
        _umi_production_exports.history.push('404');
        return;
      }
      return /*#__PURE__*/(0,jsx_runtime.jsx)(MaterialIssuePrint, {
        type: type,
        id: id
      });
    case 'importExport':
      return /*#__PURE__*/(0,jsx_runtime.jsx)(ImportExportPrint/* default */.Z, {
        warehouse: searchParams.get('warehouse') || '',
        start_date: searchParams.get('start_date') || '',
        end_date: searchParams.get('end_date') || '',
        openPrint: true,
        item_code_list: JSON.parse((_searchParams$get = searchParams.get('item_code_list')) !== null && _searchParams$get !== void 0 ? _searchParams$get : '')
      });
    case 'importExportValue':
      return /*#__PURE__*/(0,jsx_runtime.jsx)(ImportExportValuePrint/* default */.Z, {
        warehouse: searchParams.get('warehouse') || '',
        start_date: searchParams.get('start_date') || '',
        end_date: searchParams.get('end_date') || '',
        openPrint: true,
        item_code_list: JSON.parse((_searchParams$get2 = searchParams.get('item_code_list')) !== null && _searchParams$get2 !== void 0 ? _searchParams$get2 : '')
      });
    case 'generalImport':
      return /*#__PURE__*/(0,jsx_runtime.jsx)(GeneralImportPrint/* default */.Z, {
        warehouse: searchParams.get('warehouse') || '',
        end_date: searchParams.get('end_date') || '',
        start_date: searchParams.get('start_date') || '',
        openPrint: true
      });
    case 'generalExport':
      return /*#__PURE__*/(0,jsx_runtime.jsx)(GeneralExportPrint/* default */.Z, {
        warehouse: searchParams.get('warehouse') || '',
        end_date: searchParams.get('end_date') || '',
        start_date: searchParams.get('start_date') || '',
        openPrint: true
      });
    default:
      _umi_production_exports.history.push('404');
      break;
  }
};
var ExportPage = function ExportPage() {
  var _useSearchParams = (0,react_router_dom/* useSearchParams */.lr)(),
    _useSearchParams2 = slicedToArray_default()(_useSearchParams, 1),
    searchParams = _useSearchParams2[0];
  return /*#__PURE__*/(0,jsx_runtime.jsx)(jsx_runtime.Fragment, {
    children: selectType(searchParams)
  });
};
/* harmony default export */ var Inventory_ExportPage = (ExportPage);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///66221
`)},98041:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Bg: function() { return /* binding */ getTotalWarehouseQty; },
/* harmony export */   Ej: function() { return /* binding */ getTotalImportExportWarehouseQty; },
/* harmony export */   w5: function() { return /* binding */ getImportExportWarehouse; },
/* harmony export */   yI: function() { return /* binding */ getTotalItemQtyInRange; },
/* harmony export */   zB: function() { return /* binding */ getTotalWarerhouseItemQty; }
/* harmony export */ });
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(97857);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(7837);
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(467);





var handleError = function handleError(error) {
  console.log("Error in services/stock/dashboard: \\n".concat(error));
};
var CRUD_PATH = {
  totalQtyValue: 'stockReport/dashboard/totalQtyValue',
  totalImportExportQtyValue: 'stockReport/dashboard/totalQtyValue/import-export',
  totalItemQty: 'stockReport/dashboard/totalItemQty',
  totalImportExport: 'stockReport/dashboard/import-export',
  totalItemQtyInRange: 'stockReport/dashboard/totalItemQtyInRange',
  SUBMIT: 'deliveryNote/submit'
};
var getTotalWarerhouseItemQty = /*#__PURE__*/function () {
  var _ref = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.prev = 0;
          _context.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)("api/v2/".concat(CRUD_PATH.totalItemQty)), {
            method: 'GET',
            params: params
          });
        case 3:
          res = _context.sent;
          return _context.abrupt("return", {
            result: res.result.data,
            stockStatusCounts: res.result.stockStatusCounts
          });
        case 7:
          _context.prev = 7;
          _context.t0 = _context["catch"](0);
          handleError(_context.t0);
          return _context.abrupt("return", {
            result: null
          });
        case 11:
        case "end":
          return _context.stop();
      }
    }, _callee, null, [[0, 7]]);
  }));
  return function getTotalWarerhouseItemQty(_x) {
    return _ref.apply(this, arguments);
  };
}();
var getTotalWarehouseQty = /*#__PURE__*/function () {
  var _ref2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee2(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          _context2.prev = 0;
          _context2.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)("api/v2/".concat(CRUD_PATH.totalQtyValue)), {
            method: 'GET',
            params: params
          });
        case 3:
          res = _context2.sent;
          return _context2.abrupt("return", {
            result: res.result
          });
        case 7:
          _context2.prev = 7;
          _context2.t0 = _context2["catch"](0);
          handleError(_context2.t0);
          return _context2.abrupt("return", {
            result: null
          });
        case 11:
        case "end":
          return _context2.stop();
      }
    }, _callee2, null, [[0, 7]]);
  }));
  return function getTotalWarehouseQty(_x2) {
    return _ref2.apply(this, arguments);
  };
}();
var getTotalImportExportWarehouseQty = /*#__PURE__*/function () {
  var _ref3 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee3(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee3$(_context3) {
      while (1) switch (_context3.prev = _context3.next) {
        case 0:
          _context3.prev = 0;
          _context3.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)("api/v2/".concat(CRUD_PATH.totalImportExportQtyValue)), {
            method: 'GET',
            params: params
          });
        case 3:
          res = _context3.sent;
          return _context3.abrupt("return", {
            result: res.result
          });
        case 7:
          _context3.prev = 7;
          _context3.t0 = _context3["catch"](0);
          handleError(_context3.t0);
          return _context3.abrupt("return", {
            result: null
          });
        case 11:
        case "end":
          return _context3.stop();
      }
    }, _callee3, null, [[0, 7]]);
  }));
  return function getTotalImportExportWarehouseQty(_x3) {
    return _ref3.apply(this, arguments);
  };
}();
var getImportExportWarehouse = /*#__PURE__*/function () {
  var _ref4 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee4(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee4$(_context4) {
      while (1) switch (_context4.prev = _context4.next) {
        case 0:
          _context4.prev = 0;
          _context4.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)("api/v2/".concat(CRUD_PATH.totalImportExport)), {
            method: 'GET',
            params: params
          });
        case 3:
          res = _context4.sent;
          return _context4.abrupt("return", {
            result: res.result
          });
        case 7:
          _context4.prev = 7;
          _context4.t0 = _context4["catch"](0);
          handleError(_context4.t0);
          return _context4.abrupt("return", {
            result: null
          });
        case 11:
        case "end":
          return _context4.stop();
      }
    }, _callee4, null, [[0, 7]]);
  }));
  return function getImportExportWarehouse(_x4) {
    return _ref4.apply(this, arguments);
  };
}();
var getTotalItemQtyInRange = /*#__PURE__*/function () {
  var _ref5 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee5(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee5$(_context5) {
      while (1) switch (_context5.prev = _context5.next) {
        case 0:
          _context5.prev = 0;
          _context5.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)("api/v2/".concat(CRUD_PATH.totalItemQtyInRange)), {
            method: 'GET',
            params: D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default()({}, params), {}, {
              item_code_list: JSON.stringify(params.item_code_list)
            })
          });
        case 3:
          res = _context5.sent;
          return _context5.abrupt("return", {
            data: res.result.data
          });
        case 7:
          _context5.prev = 7;
          _context5.t0 = _context5["catch"](0);
          handleError(_context5.t0);
          return _context5.abrupt("return", {
            data: null
          });
        case 11:
        case "end":
          return _context5.stop();
      }
    }, _callee5, null, [[0, 7]]);
  }));
  return function getTotalItemQtyInRange(_x5) {
    return _ref5.apply(this, arguments);
  };
}();//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///98041
`)},14329:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   M_: function() { return /* binding */ getDeliveryNote; },
/* harmony export */   U4: function() { return /* binding */ updateDeliveryNote; },
/* harmony export */   a1: function() { return /* binding */ submitExportVoucher; },
/* harmony export */   f4: function() { return /* binding */ deleteDeliveryNote; },
/* harmony export */   lm: function() { return /* binding */ saveExportVoucher; },
/* harmony export */   r7: function() { return /* binding */ getDeliveryNoteDetail; },
/* harmony export */   sF: function() { return /* binding */ cancelDeliveryNote; }
/* harmony export */ });
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(97857);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(7837);
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(467);





var handleError = function handleError(error) {
  console.log("Error in services/deliveryNote: \\n".concat(error));
};
var CRUD_PATH = {
  READ: 'deliveryNote',
  READ_DETAIL: 'deliveryNote/detail',
  SAVE: 'deliveryNote/save',
  SUBMIT: 'deliveryNote/submit',
  CANCEL: 'deliveryNote/cancel'
};
var getDeliveryNote = /*#__PURE__*/function () {
  var _ref = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.prev = 0;
          _context.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)("api/v2/".concat(CRUD_PATH.READ)), {
            method: 'GET',
            params: D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({
              page: 1,
              size: 100
            }, params), (0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .getParamsReqList */ .vj)(params)), {}, {
              customer_id: params.customer_id
            })
          });
        case 3:
          res = _context.sent;
          return _context.abrupt("return", {
            data: res.result.data || [],
            pagination: res.result.pagination
          });
        case 7:
          _context.prev = 7;
          _context.t0 = _context["catch"](0);
          handleError(_context.t0);
          return _context.abrupt("return", {
            data: []
          });
        case 11:
        case "end":
          return _context.stop();
      }
    }, _callee, null, [[0, 7]]);
  }));
  return function getDeliveryNote(_x) {
    return _ref.apply(this, arguments);
  };
}();
var getDeliveryNoteDetail = /*#__PURE__*/function () {
  var _ref3 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee2(_ref2) {
    var name, _res$result$docs, res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          name = _ref2.name;
          _context2.prev = 1;
          _context2.next = 4;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)("api/v2/".concat(CRUD_PATH.READ_DETAIL)), {
            method: 'GET',
            params: {
              name: name
            }
          });
        case 4:
          res = _context2.sent;
          return _context2.abrupt("return", {
            data: ((_res$result$docs = res.result.docs) === null || _res$result$docs === void 0 ? void 0 : _res$result$docs.at(0)) || null
          });
        case 8:
          _context2.prev = 8;
          _context2.t0 = _context2["catch"](1);
          handleError(_context2.t0);
          return _context2.abrupt("return", {
            data: {}
          });
        case 12:
        case "end":
          return _context2.stop();
      }
    }, _callee2, null, [[1, 8]]);
  }));
  return function getDeliveryNoteDetail(_x2) {
    return _ref3.apply(this, arguments);
  };
}();
var saveExportVoucher = /*#__PURE__*/function () {
  var _ref4 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee3(newExportReceipt) {
    var _res$result$docs2, res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee3$(_context3) {
      while (1) switch (_context3.prev = _context3.next) {
        case 0:
          _context3.prev = 0;
          _context3.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)("api/v2/".concat(CRUD_PATH.SAVE)), {
            method: 'POST',
            data: D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({}, newExportReceipt)
          });
        case 3:
          res = _context3.sent;
          return _context3.abrupt("return", (_res$result$docs2 = res.result.docs) === null || _res$result$docs2 === void 0 ? void 0 : _res$result$docs2.at(0));
        case 7:
          _context3.prev = 7;
          _context3.t0 = _context3["catch"](0);
          handleError(_context3.t0);
          throw _context3.t0;
        case 11:
        case "end":
          return _context3.stop();
      }
    }, _callee3, null, [[0, 7]]);
  }));
  return function saveExportVoucher(_x3) {
    return _ref4.apply(this, arguments);
  };
}();
var submitExportVoucher = /*#__PURE__*/function () {
  var _ref5 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee4(newExportReceipt) {
    var _res$result$docs3, res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee4$(_context4) {
      while (1) switch (_context4.prev = _context4.next) {
        case 0:
          _context4.prev = 0;
          _context4.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)("api/v2/".concat(CRUD_PATH.SUBMIT)), {
            method: 'POST',
            data: D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({}, newExportReceipt)
          });
        case 3:
          res = _context4.sent;
          return _context4.abrupt("return", (_res$result$docs3 = res.result.docs) === null || _res$result$docs3 === void 0 ? void 0 : _res$result$docs3.at(0));
        case 7:
          _context4.prev = 7;
          _context4.t0 = _context4["catch"](0);
          handleError(_context4.t0);
          throw _context4.t0;
        case 11:
        case "end":
          return _context4.stop();
      }
    }, _callee4, null, [[0, 7]]);
  }));
  return function submitExportVoucher(_x4) {
    return _ref5.apply(this, arguments);
  };
}();
var updateDeliveryNote = /*#__PURE__*/function () {
  var _ref6 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee5(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee5$(_context5) {
      while (1) switch (_context5.prev = _context5.next) {
        case 0:
          _context5.prev = 0;
          _context5.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)("api/v2/".concat(CRUD_PATH.READ)), {
            method: 'PUT',
            data: data
          });
        case 3:
          res = _context5.sent;
          return _context5.abrupt("return", res);
        case 7:
          _context5.prev = 7;
          _context5.t0 = _context5["catch"](0);
          handleError(_context5.t0);
          throw _context5.t0;
        case 11:
        case "end":
          return _context5.stop();
      }
    }, _callee5, null, [[0, 7]]);
  }));
  return function updateDeliveryNote(_x5) {
    return _ref6.apply(this, arguments);
  };
}();
var deleteDeliveryNote = /*#__PURE__*/function () {
  var _ref7 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee6(name) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee6$(_context6) {
      while (1) switch (_context6.prev = _context6.next) {
        case 0:
          _context6.prev = 0;
          _context6.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)("api/v2/".concat(CRUD_PATH.READ)), {
            method: 'DELETE',
            params: {
              name: name
            }
          });
        case 3:
          res = _context6.sent;
          return _context6.abrupt("return", res);
        case 7:
          _context6.prev = 7;
          _context6.t0 = _context6["catch"](0);
          handleError(_context6.t0);
          throw _context6.t0;
        case 11:
        case "end":
          return _context6.stop();
      }
    }, _callee6, null, [[0, 7]]);
  }));
  return function deleteDeliveryNote(_x6) {
    return _ref7.apply(this, arguments);
  };
}();
var cancelDeliveryNote = /*#__PURE__*/function () {
  var _ref8 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee7(name) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee7$(_context7) {
      while (1) switch (_context7.prev = _context7.next) {
        case 0:
          _context7.prev = 0;
          _context7.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)("api/v2/".concat(CRUD_PATH.CANCEL)), {
            method: 'PUT',
            params: {
              name: name
            }
          });
        case 3:
          res = _context7.sent;
          return _context7.abrupt("return", res);
        case 7:
          _context7.prev = 7;
          _context7.t0 = _context7["catch"](0);
          handleError(_context7.t0);
          throw _context7.t0;
        case 11:
        case "end":
          return _context7.stop();
      }
    }, _callee7, null, [[0, 7]]);
  }));
  return function cancelDeliveryNote(_x7) {
    return _ref8.apply(this, arguments);
  };
}();//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///14329
`)},33326:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   F7: function() { return /* binding */ deletePurchaseReceipt; },
/* harmony export */   af: function() { return /* binding */ getImportReceiptDetail; },
/* harmony export */   bw: function() { return /* binding */ cancelPurchaseReceipt; },
/* harmony export */   cV: function() { return /* binding */ updatePurchaseReceipt; },
/* harmony export */   ej: function() { return /* binding */ submitImportReceipt; },
/* harmony export */   t4: function() { return /* binding */ saveImportReceipt; },
/* harmony export */   xA: function() { return /* binding */ getImportReceipts; }
/* harmony export */ });
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(97857);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(7837);
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(467);





var handleError = function handleError(error) {
  console.log("Error in services/warehouse: \\n".concat(error));
};
var CRUD_PATH = {
  READ: 'purchaseRe',
  READ_DETAIL: 'purchaseRe/detail',
  SAVE: 'purchaseRe/save',
  SUBMIT: 'purchaseRe/submit',
  CANCEL: 'purchaseRe/cancel'
};
var getImportReceipts = /*#__PURE__*/function () {
  var _ref = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.prev = 0;
          _context.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)("api/v2/".concat(CRUD_PATH.READ)), {
            method: 'GET',
            params: D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({
              page: 1,
              size: 100
            }, params), (0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .getParamsReqList */ .vj)(params)), {}, {
              supplier_id: params.supplier_id
            })
          });
        case 3:
          res = _context.sent;
          return _context.abrupt("return", {
            data: res.result.data || [],
            pagination: res.result.pagination
          });
        case 7:
          _context.prev = 7;
          _context.t0 = _context["catch"](0);
          handleError(_context.t0);
          return _context.abrupt("return", {
            data: []
          });
        case 11:
        case "end":
          return _context.stop();
      }
    }, _callee, null, [[0, 7]]);
  }));
  return function getImportReceipts(_x) {
    return _ref.apply(this, arguments);
  };
}();
var getImportReceiptDetail = /*#__PURE__*/function () {
  var _ref3 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee2(_ref2) {
    var name, _res$result$docs, res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          name = _ref2.name;
          _context2.prev = 1;
          _context2.next = 4;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)("api/v2/".concat(CRUD_PATH.READ_DETAIL)), {
            method: 'GET',
            params: {
              name: name
            }
          });
        case 4:
          res = _context2.sent;
          return _context2.abrupt("return", {
            data: ((_res$result$docs = res.result.docs) === null || _res$result$docs === void 0 ? void 0 : _res$result$docs.at(0)) || null
          });
        case 8:
          _context2.prev = 8;
          _context2.t0 = _context2["catch"](1);
          handleError(_context2.t0);
          return _context2.abrupt("return", {
            data: {}
          });
        case 12:
        case "end":
          return _context2.stop();
      }
    }, _callee2, null, [[1, 8]]);
  }));
  return function getImportReceiptDetail(_x2) {
    return _ref3.apply(this, arguments);
  };
}();
var saveImportReceipt = /*#__PURE__*/function () {
  var _ref4 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee3(newImportReceipt) {
    var _res$result$docs2, res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee3$(_context3) {
      while (1) switch (_context3.prev = _context3.next) {
        case 0:
          _context3.prev = 0;
          _context3.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)("api/v2/".concat(CRUD_PATH.SAVE)), {
            method: 'POST',
            data: D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({}, newImportReceipt)
          });
        case 3:
          res = _context3.sent;
          return _context3.abrupt("return", (_res$result$docs2 = res.result.docs) === null || _res$result$docs2 === void 0 ? void 0 : _res$result$docs2.at(0));
        case 7:
          _context3.prev = 7;
          _context3.t0 = _context3["catch"](0);
          handleError(_context3.t0);
          throw _context3.t0;
        case 11:
        case "end":
          return _context3.stop();
      }
    }, _callee3, null, [[0, 7]]);
  }));
  return function saveImportReceipt(_x3) {
    return _ref4.apply(this, arguments);
  };
}();
var submitImportReceipt = /*#__PURE__*/function () {
  var _ref5 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee4(newImportReceipt) {
    var _res$result$docs3, res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee4$(_context4) {
      while (1) switch (_context4.prev = _context4.next) {
        case 0:
          _context4.prev = 0;
          _context4.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)("api/v2/".concat(CRUD_PATH.SUBMIT)), {
            method: 'POST',
            data: D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({}, newImportReceipt)
          });
        case 3:
          res = _context4.sent;
          return _context4.abrupt("return", (_res$result$docs3 = res.result.docs) === null || _res$result$docs3 === void 0 ? void 0 : _res$result$docs3.at(0));
        case 7:
          _context4.prev = 7;
          _context4.t0 = _context4["catch"](0);
          handleError(_context4.t0);
          throw _context4.t0;
        case 11:
        case "end":
          return _context4.stop();
      }
    }, _callee4, null, [[0, 7]]);
  }));
  return function submitImportReceipt(_x4) {
    return _ref5.apply(this, arguments);
  };
}();
var updatePurchaseReceipt = /*#__PURE__*/function () {
  var _ref6 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee5(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee5$(_context5) {
      while (1) switch (_context5.prev = _context5.next) {
        case 0:
          _context5.prev = 0;
          _context5.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)("api/v2/".concat(CRUD_PATH.READ)), {
            method: 'PUT',
            data: data
          });
        case 3:
          res = _context5.sent;
          return _context5.abrupt("return", res);
        case 7:
          _context5.prev = 7;
          _context5.t0 = _context5["catch"](0);
          handleError(_context5.t0);
          throw _context5.t0;
        case 11:
        case "end":
          return _context5.stop();
      }
    }, _callee5, null, [[0, 7]]);
  }));
  return function updatePurchaseReceipt(_x5) {
    return _ref6.apply(this, arguments);
  };
}();
var deletePurchaseReceipt = /*#__PURE__*/function () {
  var _ref7 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee6(name) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee6$(_context6) {
      while (1) switch (_context6.prev = _context6.next) {
        case 0:
          _context6.prev = 0;
          _context6.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)("api/v2/".concat(CRUD_PATH.READ)), {
            method: 'DELETE',
            params: {
              name: name
            }
          });
        case 3:
          res = _context6.sent;
          return _context6.abrupt("return", res);
        case 7:
          _context6.prev = 7;
          _context6.t0 = _context6["catch"](0);
          handleError(_context6.t0);
          throw _context6.t0;
        case 11:
        case "end":
          return _context6.stop();
      }
    }, _callee6, null, [[0, 7]]);
  }));
  return function deletePurchaseReceipt(_x6) {
    return _ref7.apply(this, arguments);
  };
}();
var cancelPurchaseReceipt = /*#__PURE__*/function () {
  var _ref8 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee7(name) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee7$(_context7) {
      while (1) switch (_context7.prev = _context7.next) {
        case 0:
          _context7.prev = 0;
          _context7.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)("api/v2/".concat(CRUD_PATH.CANCEL)), {
            method: 'PUT',
            params: {
              name: name
            }
          });
        case 3:
          res = _context7.sent;
          return _context7.abrupt("return", res);
        case 7:
          _context7.prev = 7;
          _context7.t0 = _context7["catch"](0);
          handleError(_context7.t0);
          throw _context7.t0;
        case 11:
        case "end":
          return _context7.stop();
      }
    }, _callee7, null, [[0, 7]]);
  }));
  return function cancelPurchaseReceipt(_x7) {
    return _ref8.apply(this, arguments);
  };
}();//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///33326
`)},47161:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   HG: function() { return /* binding */ submitStockReconciliation; },
/* harmony export */   Hv: function() { return /* binding */ saveStockReconciliation; },
/* harmony export */   qF: function() { return /* binding */ getStockReconciliationDetail; },
/* harmony export */   xn: function() { return /* binding */ getStockReconciliations; }
/* harmony export */ });
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(97857);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(7837);
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(467);





var handleError = function handleError(error) {
  console.log("Error in services/stock/stockReconciliation: \\n".concat(error));
};
var CRUD_PATH = {
  READ: 'stockReconciliation',
  READ_DETAIL: 'stockReconciliation/detail',
  SAVE: 'stockReconciliation/save',
  SUBMIT: 'stockReconciliation/submit'
};
var saveStockReconciliation = /*#__PURE__*/function () {
  var _ref = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee(newRecon) {
    var _res$result$docs, res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.prev = 0;
          _context.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)("api/v2/".concat(CRUD_PATH.SAVE)), {
            method: 'POST',
            data: D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({}, newRecon)
          });
        case 3:
          res = _context.sent;
          return _context.abrupt("return", (_res$result$docs = res.result.docs) === null || _res$result$docs === void 0 ? void 0 : _res$result$docs.at(0));
        case 7:
          _context.prev = 7;
          _context.t0 = _context["catch"](0);
          handleError(_context.t0);
          throw _context.t0;
        case 11:
        case "end":
          return _context.stop();
      }
    }, _callee, null, [[0, 7]]);
  }));
  return function saveStockReconciliation(_x) {
    return _ref.apply(this, arguments);
  };
}();
var submitStockReconciliation = /*#__PURE__*/function () {
  var _ref2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee2(newRecon) {
    var _res$result$docs2, res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          _context2.prev = 0;
          _context2.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)("api/v2/".concat(CRUD_PATH.SUBMIT)), {
            method: 'POST',
            data: D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({}, newRecon)
          });
        case 3:
          res = _context2.sent;
          return _context2.abrupt("return", (_res$result$docs2 = res.result.docs) === null || _res$result$docs2 === void 0 ? void 0 : _res$result$docs2.at(0));
        case 7:
          _context2.prev = 7;
          _context2.t0 = _context2["catch"](0);
          handleError(_context2.t0);
          throw _context2.t0;
        case 11:
        case "end":
          return _context2.stop();
      }
    }, _callee2, null, [[0, 7]]);
  }));
  return function submitStockReconciliation(_x2) {
    return _ref2.apply(this, arguments);
  };
}();
var getStockReconciliations = /*#__PURE__*/function () {
  var _ref3 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee3(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee3$(_context3) {
      while (1) switch (_context3.prev = _context3.next) {
        case 0:
          _context3.prev = 0;
          _context3.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)("api/v2/".concat(CRUD_PATH.READ)), {
            method: 'GET',
            params: D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({
              page: 1,
              size: 100
            }, params), (0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .getParamsReqList */ .vj)(params))
          });
        case 3:
          res = _context3.sent;
          return _context3.abrupt("return", {
            data: res.result.data || [],
            pagination: res.result.pagination
          });
        case 7:
          _context3.prev = 7;
          _context3.t0 = _context3["catch"](0);
          handleError(_context3.t0);
          return _context3.abrupt("return", {
            data: []
          });
        case 11:
        case "end":
          return _context3.stop();
      }
    }, _callee3, null, [[0, 7]]);
  }));
  return function getStockReconciliations(_x3) {
    return _ref3.apply(this, arguments);
  };
}();
var getStockReconciliationDetail = /*#__PURE__*/function () {
  var _ref5 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee4(_ref4) {
    var name, _res$result$docs3, res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee4$(_context4) {
      while (1) switch (_context4.prev = _context4.next) {
        case 0:
          name = _ref4.name;
          _context4.prev = 1;
          _context4.next = 4;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)("api/v2/".concat(CRUD_PATH.READ_DETAIL)), {
            method: 'GET',
            params: {
              name: name
            }
          });
        case 4:
          res = _context4.sent;
          return _context4.abrupt("return", {
            data: ((_res$result$docs3 = res.result.docs) === null || _res$result$docs3 === void 0 ? void 0 : _res$result$docs3.at(0)) || null
          });
        case 8:
          _context4.prev = 8;
          _context4.t0 = _context4["catch"](1);
          handleError(_context4.t0);
          return _context4.abrupt("return", {
            data: {}
          });
        case 12:
        case "end":
          return _context4.stop();
      }
    }, _callee4, null, [[1, 8]]);
  }));
  return function getStockReconciliationDetail(_x4) {
    return _ref5.apply(this, arguments);
  };
}();//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///47161
`)},15746:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var _grid__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(21584);
"use client";


/* harmony default export */ __webpack_exports__.Z = (_grid__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .Z);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiMTU3NDYuanMiLCJtYXBwaW5ncyI6IjtBQUFBOztBQUU4QjtBQUM5QixzREFBZSxzREFBRyIsInNvdXJjZXMiOlsid2VicGFjazovL2FudC1kZXNpZ24tcHJvLy4vbm9kZV9tb2R1bGVzL2FudGQvZXMvY29sL2luZGV4LmpzPzViMTciXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbmltcG9ydCB7IENvbCB9IGZyb20gJy4uL2dyaWQnO1xuZXhwb3J0IGRlZmF1bHQgQ29sOyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==
//# sourceURL=webpack-internal:///15746
`)},85576:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`
// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  Z: function() { return /* binding */ modal; }
});

// EXTERNAL MODULE: ./node_modules/antd/es/modal/confirm.js
var modal_confirm = __webpack_require__(56080);
// EXTERNAL MODULE: ./node_modules/antd/es/modal/destroyFns.js
var destroyFns = __webpack_require__(38657);
// EXTERNAL MODULE: ./node_modules/antd/es/modal/Modal.js + 1 modules
var Modal = __webpack_require__(56745);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./node_modules/classnames/index.js
var classnames = __webpack_require__(93967);
var classnames_default = /*#__PURE__*/__webpack_require__.n(classnames);
// EXTERNAL MODULE: ./node_modules/antd/node_modules/rc-dialog/es/index.js + 8 modules
var es = __webpack_require__(31058);
// EXTERNAL MODULE: ./node_modules/antd/es/_util/PurePanel.js
var PurePanel = __webpack_require__(8745);
// EXTERNAL MODULE: ./node_modules/antd/es/config-provider/context.js
var context = __webpack_require__(53124);
// EXTERNAL MODULE: ./node_modules/antd/es/modal/ConfirmDialog.js + 3 modules
var ConfirmDialog = __webpack_require__(32409);
// EXTERNAL MODULE: ./node_modules/antd/es/modal/shared.js + 2 modules
var shared = __webpack_require__(4941);
// EXTERNAL MODULE: ./node_modules/antd/es/modal/style/index.js
var style = __webpack_require__(71194);
// EXTERNAL MODULE: ./node_modules/antd/es/config-provider/hooks/useCSSVarCls.js
var useCSSVarCls = __webpack_require__(35792);
;// CONCATENATED MODULE: ./node_modules/antd/es/modal/PurePanel.js
"use client";

var __rest = undefined && undefined.__rest || function (s, e) {
  var t = {};
  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
  }
  return t;
};
/* eslint-disable react/jsx-no-useless-fragment */









const PurePanel_PurePanel = props => {
  const {
      prefixCls: customizePrefixCls,
      className,
      closeIcon,
      closable,
      type,
      title,
      children,
      footer
    } = props,
    restProps = __rest(props, ["prefixCls", "className", "closeIcon", "closable", "type", "title", "children", "footer"]);
  const {
    getPrefixCls
  } = react.useContext(context/* ConfigContext */.E_);
  const rootPrefixCls = getPrefixCls();
  const prefixCls = customizePrefixCls || getPrefixCls('modal');
  const rootCls = (0,useCSSVarCls/* default */.Z)(rootPrefixCls);
  const [wrapCSSVar, hashId, cssVarCls] = (0,style/* default */.ZP)(prefixCls, rootCls);
  const confirmPrefixCls = \`\${prefixCls}-confirm\`;
  // Choose target props by confirm mark
  let additionalProps = {};
  if (type) {
    additionalProps = {
      closable: closable !== null && closable !== void 0 ? closable : false,
      title: '',
      footer: '',
      children: ( /*#__PURE__*/react.createElement(ConfirmDialog/* ConfirmContent */.O, Object.assign({}, props, {
        prefixCls: prefixCls,
        confirmPrefixCls: confirmPrefixCls,
        rootPrefixCls: rootPrefixCls,
        content: children
      })))
    };
  } else {
    additionalProps = {
      closable: closable !== null && closable !== void 0 ? closable : true,
      title,
      footer: footer !== null && /*#__PURE__*/react.createElement(shared/* Footer */.$, Object.assign({}, props)),
      children
    };
  }
  return wrapCSSVar( /*#__PURE__*/react.createElement(es/* Panel */.s, Object.assign({
    prefixCls: prefixCls,
    className: classnames_default()(hashId, \`\${prefixCls}-pure-panel\`, type && confirmPrefixCls, type && \`\${confirmPrefixCls}-\${type}\`, className, cssVarCls, rootCls)
  }, restProps, {
    closeIcon: (0,shared/* renderCloseIcon */.b)(prefixCls, closeIcon),
    closable: closable
  }, additionalProps)));
};
/* harmony default export */ var modal_PurePanel = ((0,PurePanel/* withPureRenderTheme */.i)(PurePanel_PurePanel));
// EXTERNAL MODULE: ./node_modules/antd/es/modal/useModal/index.js + 2 modules
var useModal = __webpack_require__(94423);
;// CONCATENATED MODULE: ./node_modules/antd/es/modal/index.js
"use client";






function modalWarn(props) {
  return (0,modal_confirm/* default */.ZP)((0,modal_confirm/* withWarn */.uW)(props));
}
const modal_Modal = Modal/* default */.Z;
modal_Modal.useModal = useModal/* default */.Z;
modal_Modal.info = function infoFn(props) {
  return (0,modal_confirm/* default */.ZP)((0,modal_confirm/* withInfo */.cw)(props));
};
modal_Modal.success = function successFn(props) {
  return (0,modal_confirm/* default */.ZP)((0,modal_confirm/* withSuccess */.vq)(props));
};
modal_Modal.error = function errorFn(props) {
  return (0,modal_confirm/* default */.ZP)((0,modal_confirm/* withError */.AQ)(props));
};
modal_Modal.warning = modalWarn;
modal_Modal.warn = modalWarn;
modal_Modal.confirm = function confirmFn(props) {
  return (0,modal_confirm/* default */.ZP)((0,modal_confirm/* withConfirm */.Au)(props));
};
modal_Modal.destroyAll = function destroyAllFn() {
  while (destroyFns/* default */.Z.length) {
    const close = destroyFns/* default */.Z.pop();
    if (close) {
      close();
    }
  }
};
modal_Modal.config = modal_confirm/* modalGlobalConfig */.ai;
modal_Modal._InternalPanelDoNotUseOrYouWillBeFired = modal_PurePanel;
if (false) {}
/* harmony default export */ var modal = (modal_Modal);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///85576
`)},71230:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var _grid__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(92820);
"use client";


/* harmony default export */ __webpack_exports__.Z = (_grid__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .Z);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNzEyMzAuanMiLCJtYXBwaW5ncyI6IjtBQUFBOztBQUU4QjtBQUM5QixzREFBZSxzREFBRyIsInNvdXJjZXMiOlsid2VicGFjazovL2FudC1kZXNpZ24tcHJvLy4vbm9kZV9tb2R1bGVzL2FudGQvZXMvcm93L2luZGV4LmpzP2M4NzAiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbmltcG9ydCB7IFJvdyB9IGZyb20gJy4uL2dyaWQnO1xuZXhwb3J0IGRlZmF1bHQgUm93OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==
//# sourceURL=webpack-internal:///71230
`)},49867:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   N: function() { return /* binding */ operationUnit; }
/* harmony export */ });
// eslint-disable-next-line import/prefer-default-export
const operationUnit = token => ({
  // FIXME: This use link but is a operation unit. Seems should be a colorPrimary.
  // And Typography use this to generate link style which should not do this.
  color: token.colorLink,
  textDecoration: 'none',
  outline: 'none',
  cursor: 'pointer',
  transition: \`color \${token.motionDurationSlow}\`,
  '&:focus, &:hover': {
    color: token.colorLinkHover
  },
  '&:active': {
    color: token.colorLinkActive
  }
});//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNDk4NjcuanMiLCJtYXBwaW5ncyI6Ijs7O0FBQUE7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1Qix5QkFBeUI7QUFDaEQ7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQSxDQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvYW50ZC9lcy9zdHlsZS9vcGVyYXRpb25Vbml0LmpzPzNjODIiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIGltcG9ydC9wcmVmZXItZGVmYXVsdC1leHBvcnRcbmV4cG9ydCBjb25zdCBvcGVyYXRpb25Vbml0ID0gdG9rZW4gPT4gKHtcbiAgLy8gRklYTUU6IFRoaXMgdXNlIGxpbmsgYnV0IGlzIGEgb3BlcmF0aW9uIHVuaXQuIFNlZW1zIHNob3VsZCBiZSBhIGNvbG9yUHJpbWFyeS5cbiAgLy8gQW5kIFR5cG9ncmFwaHkgdXNlIHRoaXMgdG8gZ2VuZXJhdGUgbGluayBzdHlsZSB3aGljaCBzaG91bGQgbm90IGRvIHRoaXMuXG4gIGNvbG9yOiB0b2tlbi5jb2xvckxpbmssXG4gIHRleHREZWNvcmF0aW9uOiAnbm9uZScsXG4gIG91dGxpbmU6ICdub25lJyxcbiAgY3Vyc29yOiAncG9pbnRlcicsXG4gIHRyYW5zaXRpb246IGBjb2xvciAke3Rva2VuLm1vdGlvbkR1cmF0aW9uU2xvd31gLFxuICAnJjpmb2N1cywgJjpob3Zlcic6IHtcbiAgICBjb2xvcjogdG9rZW4uY29sb3JMaW5rSG92ZXJcbiAgfSxcbiAgJyY6YWN0aXZlJzoge1xuICAgIGNvbG9yOiB0b2tlbi5jb2xvckxpbmtBY3RpdmVcbiAgfVxufSk7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9
//# sourceURL=webpack-internal:///49867
`)},1208:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(87462);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(67294);
/* harmony import */ var _ant_design_icons_svg_es_asn_EyeOutlined__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(5717);
/* harmony import */ var _components_AntdIcon__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(93771);

// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY




var EyeOutlined = function EyeOutlined(props, ref) {
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(_components_AntdIcon__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Z, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z)({}, props, {
    ref: ref,
    icon: _ant_design_icons_svg_es_asn_EyeOutlined__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z
  }));
};
if (false) {}
/* harmony default export */ __webpack_exports__.Z = (/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(EyeOutlined));//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiMTIwOC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQTBEO0FBQzFEO0FBQ0E7O0FBRStCO0FBQ3VDO0FBQ3hCO0FBQzlDO0FBQ0Esc0JBQXNCLGdEQUFtQixDQUFDLHFFQUFRLEVBQUUsdUZBQVEsR0FBRztBQUMvRDtBQUNBLFVBQVUseUZBQWM7QUFDeEIsR0FBRztBQUNIO0FBQ0EsSUFBSSxLQUFxQyxFQUFFLEVBRTFDO0FBQ0QsbUVBQTRCLDZDQUFnQixhQUFhIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvYW50ZC9ub2RlX21vZHVsZXMvQGFudC1kZXNpZ24vaWNvbnMvZXMvaWNvbnMvRXllT3V0bGluZWQuanM/YWRhYyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgX2V4dGVuZHMgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL2V4dGVuZHNcIjtcbi8vIEdFTkVSQVRFIEJZIC4vc2NyaXB0cy9nZW5lcmF0ZS50c1xuLy8gRE9OIE5PVCBFRElUIElUIE1BTlVBTExZXG5cbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCBFeWVPdXRsaW5lZFN2ZyBmcm9tIFwiQGFudC1kZXNpZ24vaWNvbnMtc3ZnL2VzL2Fzbi9FeWVPdXRsaW5lZFwiO1xuaW1wb3J0IEFudGRJY29uIGZyb20gXCIuLi9jb21wb25lbnRzL0FudGRJY29uXCI7XG52YXIgRXllT3V0bGluZWQgPSBmdW5jdGlvbiBFeWVPdXRsaW5lZChwcm9wcywgcmVmKSB7XG4gIHJldHVybiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChBbnRkSWNvbiwgX2V4dGVuZHMoe30sIHByb3BzLCB7XG4gICAgcmVmOiByZWYsXG4gICAgaWNvbjogRXllT3V0bGluZWRTdmdcbiAgfSkpO1xufTtcbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIEV5ZU91dGxpbmVkLmRpc3BsYXlOYW1lID0gJ0V5ZU91dGxpbmVkJztcbn1cbmV4cG9ydCBkZWZhdWx0IC8qI19fUFVSRV9fKi9SZWFjdC5mb3J3YXJkUmVmKEV5ZU91dGxpbmVkKTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=
//# sourceURL=webpack-internal:///1208
`)},64019:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Z: function() { return /* binding */ addEventListenerWrap; }
/* harmony export */ });
/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(73935);

function addEventListenerWrap(target, eventType, cb, option) {
  /* eslint camelcase: 2 */
  var callback = react_dom__WEBPACK_IMPORTED_MODULE_0__.unstable_batchedUpdates ? function run(e) {
    react_dom__WEBPACK_IMPORTED_MODULE_0__.unstable_batchedUpdates(cb, e);
  } : cb;
  if (target !== null && target !== void 0 && target.addEventListener) {
    target.addEventListener(eventType, callback, option);
  }
  return {
    remove: function remove() {
      if (target !== null && target !== void 0 && target.removeEventListener) {
        target.removeEventListener(eventType, callback, option);
      }
    }
  };
}//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNjQwMTkuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFpQztBQUNsQjtBQUNmO0FBQ0EsaUJBQWlCLDhEQUFnQztBQUNqRCxJQUFJLDhEQUFnQztBQUNwQyxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2FudC1kZXNpZ24tcHJvLy4vbm9kZV9tb2R1bGVzL3JjLXV0aWwvZXMvRG9tL2FkZEV2ZW50TGlzdGVuZXIuanM/OTU5NyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3RET00gZnJvbSAncmVhY3QtZG9tJztcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGFkZEV2ZW50TGlzdGVuZXJXcmFwKHRhcmdldCwgZXZlbnRUeXBlLCBjYiwgb3B0aW9uKSB7XG4gIC8qIGVzbGludCBjYW1lbGNhc2U6IDIgKi9cbiAgdmFyIGNhbGxiYWNrID0gUmVhY3RET00udW5zdGFibGVfYmF0Y2hlZFVwZGF0ZXMgPyBmdW5jdGlvbiBydW4oZSkge1xuICAgIFJlYWN0RE9NLnVuc3RhYmxlX2JhdGNoZWRVcGRhdGVzKGNiLCBlKTtcbiAgfSA6IGNiO1xuICBpZiAodGFyZ2V0ICE9PSBudWxsICYmIHRhcmdldCAhPT0gdm9pZCAwICYmIHRhcmdldC5hZGRFdmVudExpc3RlbmVyKSB7XG4gICAgdGFyZ2V0LmFkZEV2ZW50TGlzdGVuZXIoZXZlbnRUeXBlLCBjYWxsYmFjaywgb3B0aW9uKTtcbiAgfVxuICByZXR1cm4ge1xuICAgIHJlbW92ZTogZnVuY3Rpb24gcmVtb3ZlKCkge1xuICAgICAgaWYgKHRhcmdldCAhPT0gbnVsbCAmJiB0YXJnZXQgIT09IHZvaWQgMCAmJiB0YXJnZXQucmVtb3ZlRXZlbnRMaXN0ZW5lcikge1xuICAgICAgICB0YXJnZXQucmVtb3ZlRXZlbnRMaXN0ZW5lcihldmVudFR5cGUsIGNhbGxiYWNrLCBvcHRpb24pO1xuICAgICAgfVxuICAgIH1cbiAgfTtcbn0iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=
//# sourceURL=webpack-internal:///64019
`)},27678:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   g1: function() { return /* binding */ getClientSize; },
/* harmony export */   os: function() { return /* binding */ getOffset; }
/* harmony export */ });
/* unused harmony exports get, set, getOuterWidth, getOuterHeight, getDocSize, getScroll */
/* eslint-disable no-nested-ternary */
var PIXEL_PATTERN = /margin|padding|width|height|max|min|offset/;
var removePixel = {
  left: true,
  top: true
};
var floatMap = {
  cssFloat: 1,
  styleFloat: 1,
  float: 1
};
function getComputedStyle(node) {
  return node.nodeType === 1 ? node.ownerDocument.defaultView.getComputedStyle(node, null) : {};
}
function getStyleValue(node, type, value) {
  type = type.toLowerCase();
  if (value === 'auto') {
    if (type === 'height') {
      return node.offsetHeight;
    }
    if (type === 'width') {
      return node.offsetWidth;
    }
  }
  if (!(type in removePixel)) {
    removePixel[type] = PIXEL_PATTERN.test(type);
  }
  return removePixel[type] ? parseFloat(value) || 0 : value;
}
function get(node, name) {
  var length = arguments.length;
  var style = getComputedStyle(node);
  name = floatMap[name] ? 'cssFloat' in node.style ? 'cssFloat' : 'styleFloat' : name;
  return length === 1 ? style : getStyleValue(node, name, style[name] || node.style[name]);
}
function set(node, name, value) {
  var length = arguments.length;
  name = floatMap[name] ? 'cssFloat' in node.style ? 'cssFloat' : 'styleFloat' : name;
  if (length === 3) {
    if (typeof value === 'number' && PIXEL_PATTERN.test(name)) {
      value = "".concat(value, "px");
    }
    node.style[name] = value; // Number
    return value;
  }
  for (var x in name) {
    if (name.hasOwnProperty(x)) {
      set(node, x, name[x]);
    }
  }
  return getComputedStyle(node);
}
function getOuterWidth(el) {
  if (el === document.body) {
    return document.documentElement.clientWidth;
  }
  return el.offsetWidth;
}
function getOuterHeight(el) {
  if (el === document.body) {
    return window.innerHeight || document.documentElement.clientHeight;
  }
  return el.offsetHeight;
}
function getDocSize() {
  var width = Math.max(document.documentElement.scrollWidth, document.body.scrollWidth);
  var height = Math.max(document.documentElement.scrollHeight, document.body.scrollHeight);
  return {
    width: width,
    height: height
  };
}
function getClientSize() {
  var width = document.documentElement.clientWidth;
  var height = window.innerHeight || document.documentElement.clientHeight;
  return {
    width: width,
    height: height
  };
}
function getScroll() {
  return {
    scrollLeft: Math.max(document.documentElement.scrollLeft, document.body.scrollLeft),
    scrollTop: Math.max(document.documentElement.scrollTop, document.body.scrollTop)
  };
}
function getOffset(node) {
  var box = node.getBoundingClientRect();
  var docElem = document.documentElement;

  // < ie8 \u4E0D\u652F\u6301 win.pageXOffset, \u5219\u4F7F\u7528 docElem.scrollLeft
  return {
    left: box.left + (window.pageXOffset || docElem.scrollLeft) - (docElem.clientLeft || document.body.clientLeft || 0),
    top: box.top + (window.pageYOffset || docElem.scrollTop) - (docElem.clientTop || document.body.clientTop || 0)
  };
}//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///27678
`)},79370:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   G: function() { return /* binding */ isStyleSupport; }
/* harmony export */ });
/* harmony import */ var _canUseDom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(98924);

var isStyleNameSupport = function isStyleNameSupport(styleName) {
  if ((0,_canUseDom__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .Z)() && window.document.documentElement) {
    var styleNameList = Array.isArray(styleName) ? styleName : [styleName];
    var documentElement = window.document.documentElement;
    return styleNameList.some(function (name) {
      return name in documentElement.style;
    });
  }
  return false;
};
var isStyleValueSupport = function isStyleValueSupport(styleName, value) {
  if (!isStyleNameSupport(styleName)) {
    return false;
  }
  var ele = document.createElement('div');
  var origin = ele.style[styleName];
  ele.style[styleName] = value;
  return ele.style[styleName] !== origin;
};
function isStyleSupport(styleName, styleValue) {
  if (!Array.isArray(styleName) && styleValue !== undefined) {
    return isStyleValueSupport(styleName, styleValue);
  }
  return isStyleNameSupport(styleName);
}//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNzkzNzAuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFvQztBQUNwQztBQUNBLE1BQU0sK0RBQVM7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hbnQtZGVzaWduLXByby8uL25vZGVfbW9kdWxlcy9yYy11dGlsL2VzL0RvbS9zdHlsZUNoZWNrZXIuanM/YWU2YyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY2FuVXNlRG9tIGZyb20gXCIuL2NhblVzZURvbVwiO1xudmFyIGlzU3R5bGVOYW1lU3VwcG9ydCA9IGZ1bmN0aW9uIGlzU3R5bGVOYW1lU3VwcG9ydChzdHlsZU5hbWUpIHtcbiAgaWYgKGNhblVzZURvbSgpICYmIHdpbmRvdy5kb2N1bWVudC5kb2N1bWVudEVsZW1lbnQpIHtcbiAgICB2YXIgc3R5bGVOYW1lTGlzdCA9IEFycmF5LmlzQXJyYXkoc3R5bGVOYW1lKSA/IHN0eWxlTmFtZSA6IFtzdHlsZU5hbWVdO1xuICAgIHZhciBkb2N1bWVudEVsZW1lbnQgPSB3aW5kb3cuZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50O1xuICAgIHJldHVybiBzdHlsZU5hbWVMaXN0LnNvbWUoZnVuY3Rpb24gKG5hbWUpIHtcbiAgICAgIHJldHVybiBuYW1lIGluIGRvY3VtZW50RWxlbWVudC5zdHlsZTtcbiAgICB9KTtcbiAgfVxuICByZXR1cm4gZmFsc2U7XG59O1xudmFyIGlzU3R5bGVWYWx1ZVN1cHBvcnQgPSBmdW5jdGlvbiBpc1N0eWxlVmFsdWVTdXBwb3J0KHN0eWxlTmFtZSwgdmFsdWUpIHtcbiAgaWYgKCFpc1N0eWxlTmFtZVN1cHBvcnQoc3R5bGVOYW1lKSkge1xuICAgIHJldHVybiBmYWxzZTtcbiAgfVxuICB2YXIgZWxlID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnZGl2Jyk7XG4gIHZhciBvcmlnaW4gPSBlbGUuc3R5bGVbc3R5bGVOYW1lXTtcbiAgZWxlLnN0eWxlW3N0eWxlTmFtZV0gPSB2YWx1ZTtcbiAgcmV0dXJuIGVsZS5zdHlsZVtzdHlsZU5hbWVdICE9PSBvcmlnaW47XG59O1xuZXhwb3J0IGZ1bmN0aW9uIGlzU3R5bGVTdXBwb3J0KHN0eWxlTmFtZSwgc3R5bGVWYWx1ZSkge1xuICBpZiAoIUFycmF5LmlzQXJyYXkoc3R5bGVOYW1lKSAmJiBzdHlsZVZhbHVlICE9PSB1bmRlZmluZWQpIHtcbiAgICByZXR1cm4gaXNTdHlsZVZhbHVlU3VwcG9ydChzdHlsZU5hbWUsIHN0eWxlVmFsdWUpO1xuICB9XG4gIHJldHVybiBpc1N0eWxlTmFtZVN1cHBvcnQoc3R5bGVOYW1lKTtcbn0iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=
//# sourceURL=webpack-internal:///79370
`)}}]);
